/* Modern Dashboard Styles */

.modern-dashboard {
  padding: var(--space-6);
  background: transparent;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

/* ===== LOADING STATE ===== */
.modern-dashboard.loading {
  justify-content: center;
  align-items: center;
}

.dashboard-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  width: 100%;
  max-width: 1200px;
}

.skeleton-card {
  height: 200px;
  background: linear-gradient(
    90deg,
    var(--neutral-200) 25%,
    var(--neutral-300) 50%,
    var(--neutral-200) 75%
  );
  background-size: 200% 100%;
  border-radius: var(--radius-2xl);
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== DASHBOARD HEADER ===== */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-8) var(--space-6);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-2xl);
  animation: slideInDown 0.8s ease-out;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0.05;
  z-index: -1;
}

.dashboard-welcome h1 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: var(--leading-tight);
}

.dashboard-welcome p {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
  line-height: var(--leading-relaxed);
}

.dashboard-actions {
  display: flex;
  gap: var(--space-3);
  flex-shrink: 0;
}

/* ===== DASHBOARD STATS ===== */
.dashboard-stats {
  animation: slideInUp 0.8s ease-out 0.1s both;
}

.dashboard-stats .stats-card {
  transition: all var(--transition-base);
}

.dashboard-stats .stats-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

/* ===== DASHBOARD METRICS ===== */
.dashboard-metrics {
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.metric-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--space-4);
  padding: var(--space-6);
}

.metric-card-content h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

/* ===== DASHBOARD CHARTS ===== */
.dashboard-charts {
  animation: slideInUp 0.8s ease-out 0.3s both;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--neutral-200);
}

.chart-header h3 {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.chart-controls {
  display: flex;
  gap: var(--space-2);
}

.time-range-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background: var(--neutral-0);
  color: var(--neutral-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.time-range-select:hover {
  border-color: var(--primary-400);
}

.time-range-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

/* ===== DASHBOARD TABS ===== */
.dashboard-tabs-card {
  animation: slideInUp 0.8s ease-out 0.4s both;
}

/* ===== RECENT BOOKINGS ===== */
.recent-bookings {
  padding: var(--space-6);
}

.bookings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.bookings-header h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.view-all-link {
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  transition: color var(--transition-fast);
}

.view-all-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.bookings-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.booking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.booking-item:hover {
  background: var(--primary-50);
  border-color: var(--primary-200);
  transform: translateX(-4px);
}

.booking-info {
  flex: 1;
}

.booking-customer {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
}

.booking-service {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  margin-bottom: var(--space-1);
}

.booking-date {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  font-family: var(--font-family-mono);
}

.booking-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-2);
}

.booking-amount {
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  color: var(--success-600);
  font-family: var(--font-family-mono);
}

/* ===== TOP DESTINATIONS ===== */
.top-destinations {
  padding: var(--space-6);
}

.destinations-header {
  margin-bottom: var(--space-6);
}

.destinations-header h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.destinations-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.destination-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.destination-item:hover {
  background: var(--primary-50);
  border-color: var(--primary-200);
  transform: scale(1.02);
}

.destination-rank {
  width: 32px;
  height: 32px;
  background: var(--primary-500);
  color: var(--neutral-0);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  flex-shrink: 0;
}

.destination-info {
  flex: 1;
}

.destination-name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
}

.destination-stats {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  font-family: var(--font-family-mono);
}

.destination-progress {
  width: 100px;
  height: 6px;
  background: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
}

.destination-progress .progress-bar {
  height: 100%;
  background: var(--primary-500);
  border-radius: inherit;
  transition: width var(--transition-base);
}

/* ===== ANALYTICS CONTENT ===== */
.analytics-content {
  padding: var(--space-6);
}

/* ===== QUICK ACTIONS ===== */
.quick-actions-card {
  animation: slideInUp 0.8s ease-out 0.5s both;
}

.quick-actions-header {
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
  text-align: center;
}

.quick-actions-header h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.quick-actions-header p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--neutral-600);
}

.quick-actions-grid {
  padding: var(--space-6);
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-6);
  background: var(--gradient-glass);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-2xl);
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.quick-action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-base);
  z-index: -1;
}

.quick-action-item:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-300);
}

.quick-action-item:hover::before {
  opacity: 0.1;
}

.quick-action-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-3);
  transition: transform var(--transition-base);
}

.quick-action-item:hover .quick-action-icon {
  transform: scale(1.2) rotate(5deg);
}

.quick-action-title {
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
}

.quick-action-desc {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
}

/* ===== DASHBOARD ALERTS ===== */
.dashboard-alerts {
  animation: slideInUp 0.8s ease-out 0.6s both;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .modern-dashboard {
    padding: var(--space-4);
    gap: var(--space-6);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
    text-align: center;
    padding: var(--space-6) var(--space-4);
  }
  
  .dashboard-welcome h1 {
    font-size: var(--text-3xl);
  }
  
  .dashboard-welcome p {
    font-size: var(--text-base);
  }
  
  .dashboard-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .dashboard-stats,
  .dashboard-metrics,
  .dashboard-charts,
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }
  
  .booking-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .booking-details {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .destination-item {
    flex-wrap: wrap;
    gap: var(--space-3);
  }
  
  .destination-progress {
    width: 100%;
  }
  
  .quick-action-item {
    padding: var(--space-4);
  }
  
  .quick-action-icon {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 480px) {
  .modern-dashboard {
    padding: var(--space-3);
    gap: var(--space-4);
  }
  
  .dashboard-header {
    padding: var(--space-4) var(--space-3);
  }
  
  .dashboard-welcome h1 {
    font-size: var(--text-2xl);
  }
  
  .dashboard-welcome p {
    font-size: var(--text-sm);
  }
  
  .dashboard-actions {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .recent-bookings,
  .top-destinations,
  .analytics-content,
  .quick-actions-grid {
    padding: var(--space-4);
  }
  
  .quick-actions-header {
    padding: var(--space-4);
  }
  
  .booking-item,
  .destination-item {
    padding: var(--space-3);
  }
  
  .quick-action-item {
    padding: var(--space-3);
  }
  
  .quick-action-icon {
    font-size: var(--text-2xl);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .dashboard-header {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .dashboard-welcome h1 {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .dashboard-welcome p {
    color: var(--neutral-400);
  }
  
  .chart-header h3,
  .bookings-header h4,
  .destinations-header h4,
  .quick-actions-header h3 {
    color: var(--neutral-200);
  }
  
  .chart-header {
    border-bottom-color: var(--neutral-600);
  }
  
  .time-range-select {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-300);
  }
  
  .booking-item,
  .destination-item {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .booking-item:hover,
  .destination-item:hover {
    background: var(--primary-800);
    border-color: var(--primary-600);
  }
  
  .booking-customer,
  .destination-name,
  .quick-action-title {
    color: var(--neutral-200);
  }
  
  .booking-service,
  .booking-date,
  .destination-stats,
  .quick-action-desc,
  .quick-actions-header p {
    color: var(--neutral-400);
  }
  
  .booking-amount {
    color: var(--success-400);
  }
  
  .quick-action-item {
    background: rgba(0, 0, 0, 0.3);
    border-color: var(--neutral-600);
  }
  
  .quick-actions-header {
    border-bottom-color: var(--neutral-600);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-dashboard {
    padding: 0;
    background: white !important;
  }
  
  .dashboard-header {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .dashboard-actions,
  .quick-actions-card,
  .dashboard-alerts {
    display: none !important;
  }
  
  .dashboard-welcome h1 {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }
  
  .dashboard-welcome p {
    color: #666 !important;
  }
  
  .booking-item,
  .destination-item {
    background: white !important;
    border: 1px solid #000 !important;
  }
  
  .chart-header h3,
  .bookings-header h4,
  .destinations-header h4 {
    color: black !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-dashboard,
  .dashboard-header,
  .dashboard-stats,
  .dashboard-metrics,
  .dashboard-charts,
  .dashboard-tabs-card,
  .quick-actions-card,
  .dashboard-alerts {
    animation: none !important;
  }
  
  .dashboard-stats .stats-card,
  .booking-item,
  .destination-item,
  .quick-action-item {
    transition: none !important;
  }
  
  .dashboard-stats .stats-card:hover,
  .booking-item:hover,
  .destination-item:hover,
  .quick-action-item:hover {
    transform: none !important;
  }
  
  .quick-action-item:hover .quick-action-icon {
    transform: none !important;
  }
  
  .skeleton-card {
    animation: none !important;
  }
}

/* Focus styles for keyboard navigation */
.quick-action-item:focus,
.view-all-link:focus,
.time-range-select:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dashboard-header,
  .booking-item,
  .destination-item,
  .quick-action-item {
    border: 2px solid currentColor !important;
  }
  
  .chart-header {
    border-bottom: 2px solid currentColor !important;
  }
  
  .destination-rank {
    border: 1px solid currentColor;
  }
}