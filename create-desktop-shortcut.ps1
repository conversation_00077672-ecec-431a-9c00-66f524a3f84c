# ===================================================================
# 🖥️ سكربت إنشاء أيقونة سطح المكتب لنظام شراء للسفر والسياحة
# Desktop Shortcut Creator for Sharau Travel System
# ===================================================================

# إعداد الألوان
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

Write-Host "
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║    🖥️  إنشاء أيقونة سطح المكتب  🖥️                            ║
║         Desktop Shortcut Creator                                ║
║                                                                  ║
║    ✈️ نظام شراء للسفر والسياحة المتطور ✈️                     ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
" -ForegroundColor Cyan

# متغيرات النظام
$PROJECT_PATH = "c:\Users\<USER>\Desktop\sharaubtravelsoft"
$SCRIPT_PATH = "$PROJECT_PATH\start-sharau-app.ps1"
$DESKTOP_PATH = [Environment]::GetFolderPath("Desktop")
$SHORTCUT_PATH = "$DESKTOP_PATH\نظام شراء للسفر والسياحة.lnk"
$ICON_PATH = "$PROJECT_PATH\app-icon.ico"

# دالة إنشاء أيقونة ICO
function Create-AppIcon {
    Write-Host "🎨 إنشاء أيقونة التطبيق..." -ForegroundColor Yellow
    
    # إنشاء ملف أيقونة بسيط (نص)
    # في الواقع، سنستخدم أيقونة PowerShell الافتراضية
    $powershellPath = (Get-Command powershell.exe).Source
    
    if (Test-Path $powershellPath) {
        Write-Host "✅ سيتم استخدام أيقونة PowerShell" -ForegroundColor Green
        return $powershellPath
    } else {
        Write-Host "⚠️ سيتم استخدام أيقونة افتراضية" -ForegroundColor Yellow
        return "shell32.dll,21"  # أيقونة تطبيق افتراضية
    }
}

# دالة إنشاء الاختصار
function Create-Shortcut {
    param(
        [string]$ShortcutPath,
        [string]$TargetPath,
        [string]$Arguments,
        [string]$WorkingDirectory,
        [string]$IconLocation,
        [string]$Description
    )
    
    try {
        $WshShell = New-Object -ComObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = $TargetPath
        $Shortcut.Arguments = $Arguments
        $Shortcut.WorkingDirectory = $WorkingDirectory
        $Shortcut.IconLocation = $IconLocation
        $Shortcut.Description = $Description
        $Shortcut.WindowStyle = 1  # Normal window
        $Shortcut.Save()
        
        Write-Host "✅ تم إنشاء الاختصار بنجاح" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ فشل في إنشاء الاختصار: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# دالة إنشاء ملف batch للتشغيل
function Create-BatchFile {
    $batchPath = "$PROJECT_PATH\start-app.bat"
    
    $batchContent = @"
@echo off
title نظام شراء للسفر والسياحة
cd /d "$PROJECT_PATH"
powershell.exe -ExecutionPolicy Bypass -File "$SCRIPT_PATH"
pause
"@
    
    try {
        Set-Content -Path $batchPath -Value $batchContent -Encoding UTF8
        Write-Host "✅ تم إنشاء ملف التشغيل: $batchPath" -ForegroundColor Green
        return $batchPath
    } catch {
        Write-Host "❌ فشل في إنشاء ملف التشغيل" -ForegroundColor Red
        return $null
    }
}

# دالة إنشاء سكربت VBS للتشغيل الصامت
function Create-VBSScript {
    $vbsPath = "$PROJECT_PATH\start-app-silent.vbs"
    
    $vbsContent = @"
Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "$PROJECT_PATH"
WshShell.Run "powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File ""$SCRIPT_PATH""", 0, False
"@
    
    try {
        Set-Content -Path $vbsPath -Value $vbsContent -Encoding UTF8
        Write-Host "✅ تم إنشاء سكربت التشغيل الصامت: $vbsPath" -ForegroundColor Green
        return $vbsPath
    } catch {
        Write-Host "❌ فشل في إنشاء سكربت التشغيل الصامت" -ForegroundColor Red
        return $null
    }
}

# دالة إنشاء ملف تكوين التطبيق
function Create-AppConfig {
    $configPath = "$PROJECT_PATH\app-config.json"
    
    $config = @{
        appName = "نظام شراء للسفر والسياحة"
        appNameEn = "Sharau Travel & Tourism System"
        version = "1.0.0"
        description = "نظام متطور لإدارة السفر والسياحة مع قوالب مذهلة"
        author = "Sharau Development Team"
        created = (Get-Date).ToString("yyyy-MM-dd")
        urls = @{
            frontend = "http://localhost:3001"
            backend = "http://localhost:5000"
            templates = "http://localhost:3001/templates"
            vouchers = "http://localhost:3001/vouchers"
            dashboard = "http://localhost:3001/dashboard"
        }
        features = @(
            "نظام القوالب المذهل",
            "إدارة السندات المتطورة", 
            "التقارير التحليلية",
            "إدارة الحجوزات الشاملة",
            "واجهة مستخدم عصرية",
            "دعم العملات المتعددة"
        )
    } | ConvertTo-Json -Depth 3
    
    try {
        Set-Content -Path $configPath -Value $config -Encoding UTF8
        Write-Host "✅ تم إنشاء ملف التكوين: $configPath" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ فشل في إنشاء ملف التكوين" -ForegroundColor Red
        return $false
    }
}

# دالة إنشاء دليل المستخدم
function Create-UserGuide {
    $guidePath = "$PROJECT_PATH\دليل_المستخدم.txt"
    
    $guide = @"
╔══════════════════════════════════════════════════════════════════╗
║                    دليل استخدام النظام                         ║
║              نظام شراء للسفر والسياحة المتطور                  ║
╚══════════════════════════════════════════════════════════════════╝

🚀 كيفية تشغيل النظام:
═══════════════════════════

1️⃣ انقر نقراً مزدوجاً على أيقونة "نظام شراء للسفر والسياحة" على سطح المكتب

2️⃣ انتظر حتى يتم تحميل النظام (قد يستغرق دقيقة أو دقيقتين في المرة الأولى)

3️⃣ سيفتح المتصفح تلقائياً على العنوان: http://localhost:3001

4️⃣ استمتع بالنظام المذهل!

🎯 الميزات الرئيسية:
═══════════════════════

📄 نظام القوالب المذهل:
   • قوالب السندات الفاخرة (قبض وصرف)
   • فواتير الطيران المتطورة
   • عقود الخدمات السياحية
   • رسائل البريد الإلكتروني التفاعلية
   • تقارير المبيعات التحليلية

💰 إدارة السندات المتطورة:
   • سندات قبض بتصميمات متعددة
   • سندات صرف احترافية
   • دعم العملات المتعددة
   • تحويل المبالغ إلى كلمات

🎨 منشئ القوالب المتطور:
   • تصميمات متنوعة (عصري، كلاسيكي، بسيط، فاخر)
   • أنظمة ألوان متعددة
   • تخصيص كامل للعناصر
   • معاينة مباشرة

📊 التقارير والإحصائيات:
   • لوحة معلومات شاملة
   • إحصائيات مفصلة
   • تقارير تحليلية
   • رسوم بيانية تفاعلية

🌐 الروابط المهمة:
═══════════════════

• الصفحة الرئيسية: http://localhost:3001
• القوالب المذهلة: http://localhost:3001/templates
• السندات المتطورة: http://localhost:3001/vouchers
• لوحة التحكم: http://localhost:3001/dashboard
• التقارير: http://localhost:3001/reports

⚙️ استكشاف الأخطاء:
═══════════════════════

❓ إذا لم يفتح المتصفح تلقائياً:
   افتح المتصفح يدوياً واذهب إلى: http://localhost:3001

❓ إذا ظهرت رسالة خطأ:
   • تأكد من أن Node.js مثبت على النظام
   • أعد تشغيل النظام
   • تحقق من ملف السجلات: system.log

❓ إذا كان النظام بطيئاً:
   • انتظر قليلاً حتى يتم التحميل الكامل
   • أغلق التطبيقات الأخرى لتوفير الذاكرة
   • أعد تشغيل الكمبيوتر إذا لزم الأمر

🛑 إيقاف النظام:
═══════════════════

• أغلق نافذة PowerShell (الشاشة السوداء)
• أو اضغط Ctrl+C في نافذة PowerShell

📞 الدعم الفني:
═══════════════

في حالة وجود أي مشاكل أو استفسارات:
• راجع ملف السجلات: system.log
• تحقق من ملف التكوين: app-config.json
• راجع الدليل الشامل: ULTIMATE_TEMPLATES_SYSTEM.md

═══════════════════════════════════════════════════════════════════

🎉 استمتع بتجربة النظام المذهل! 🎉

تم تطوير هذا النظام بعناية فائقة ليوفر لك أفضل تجربة
في إدارة السفر والسياحة مع قوالب مذهلة وميزات متطورة.

═══════════════════════════════════════════════════════════════════
"@
    
    try {
        Set-Content -Path $guidePath -Value $guide -Encoding UTF8
        Write-Host "✅ تم إنشاء دليل المستخدم: $guidePath" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ فشل في إنشاء دليل المستخدم" -ForegroundColor Red
        return $false
    }
}

# ===================================================================
# 🚀 تنفيذ إنشاء الأيقونة والملفات
# ===================================================================

Write-Host "🔄 بدء إنشاء أيقونة سطح المكتب..." -ForegroundColor Yellow
Write-Host ""

# التحقق من وجود المجلد
if (-not (Test-Path $PROJECT_PATH)) {
    Write-Host "❌ مجلد المشروع غير موجود: $PROJECT_PATH" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# التحقق من وجود السكربت الرئيسي
if (-not (Test-Path $SCRIPT_PATH)) {
    Write-Host "❌ سكربت التشغيل غير موجود: $SCRIPT_PATH" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إنشاء الأيقونة
$iconPath = Create-AppIcon

# إنشاء ملف batch
$batchPath = Create-BatchFile
if (-not $batchPath) {
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إنشاء سكربت VBS
$vbsPath = Create-VBSScript

# إنشاء ملف التكوين
Create-AppConfig | Out-Null

# إنشاء دليل المستخدم
Create-UserGuide | Out-Null

# حذف الاختصار القديم إذا كان موجوداً
if (Test-Path $SHORTCUT_PATH) {
    Remove-Item $SHORTCUT_PATH -Force
    Write-Host "🗑️ تم حذف الاختصار القديم" -ForegroundColor Yellow
}

# إنشاء الاختصار الجديد
Write-Host "🖥️ إنشاء اختصار سطح المكتب..." -ForegroundColor Yellow

$success = Create-Shortcut -ShortcutPath $SHORTCUT_PATH `
                          -TargetPath $batchPath `
                          -Arguments "" `
                          -WorkingDirectory $PROJECT_PATH `
                          -IconLocation $iconPath `
                          -Description "نظام شراء للسفر والسياحة المتطور - Sharau Travel System"

if ($success) {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
    Write-Host "║                    🎉 تم الإنشاء بنجاح! 🎉                     ║" -ForegroundColor Green
    Write-Host "╠══════════════════════════════════════════════════════════════════╣" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "║  ✅ تم إنشاء أيقونة سطح المكتب بنجاح                          ║" -ForegroundColor Green
    Write-Host "║  📁 الموقع: $DESKTOP_PATH" -ForegroundColor Green
    Write-Host "║  🏷️ الاسم: نظام شراء للسفر والسياحة                          ║" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "║  📋 الملفات المنشأة:                                           ║" -ForegroundColor Green
    Write-Host "║  • start-app.bat - ملف التشغيل                                ║" -ForegroundColor Green
    Write-Host "║  • start-app-silent.vbs - التشغيل الصامت                     ║" -ForegroundColor Green
    Write-Host "║  • app-config.json - ملف التكوين                              ║" -ForegroundColor Green
    Write-Host "║  • دليل_المستخدم.txt - دليل الاستخدام                        ║" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "╚══════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 كيفية الاستخدام:" -ForegroundColor Cyan
    Write-Host "   1. انقر نقراً مزدوجاً على الأيقونة الجديدة" -ForegroundColor White
    Write-Host "   2. انتظر تحميل النظام" -ForegroundColor White
    Write-Host "   3. سيفتح المتصفح تلقائياً" -ForegroundColor White
    Write-Host "   4. استمتع بالنظام المذهل!" -ForegroundColor White
    Write-Host ""
    Write-Host "📖 لمزيد من المعلومات، راجع دليل المستخدم في مجلد المشروع" -ForegroundColor Cyan
    Write-Host ""
} else {
    Write-Host ""
    Write-Host "❌ فشل في إنشاء اختصار سطح المكتب" -ForegroundColor Red
    Write-Host "💡 يمكنك تشغيل النظام يدوياً من خلال:" -ForegroundColor Yellow
    Write-Host "   $batchPath" -ForegroundColor White
    Write-Host ""
}

Write-Host "🎯 النظام جاهز للاستخدام!" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"