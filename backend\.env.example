# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/travel_accounting
DB_HOST=localhost
DB_PORT=3306
DB_NAME=travel_accounting
DB_USER=root
DB_PASSWORD=your_password

# Security
SECRET_KEY=your-super-secret-key-here-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379/0

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Application Settings
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
API_V1_STR=/api/v1

# File Upload Settings
MAX_FILE_SIZE=********  # 10MB
UPLOAD_DIR=uploads/

# Backup Settings
BACKUP_DIR=backups/
AUTO_BACKUP_ENABLED=True
BACKUP_RETENTION_DAYS=30