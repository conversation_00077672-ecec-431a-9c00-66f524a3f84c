import React from 'react';

const TestPage = () => {
  return (
    <div style={{
      padding: '50px',
      textAlign: 'center',
      fontFamily: 'Arial, sans-serif',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      color: 'white'
    }}>
      <h1 style={{ fontSize: '48px', marginBottom: '20px' }}>
        🎉 النظام يعمل بنجاح!
      </h1>
      <p style={{ fontSize: '24px', marginBottom: '30px' }}>
        تم إصلاح جميع الأخطاء وتشغيل النظام بنجاح
      </p>
      <div style={{
        background: 'rgba(255,255,255,0.2)',
        padding: '20px',
        borderRadius: '15px',
        margin: '20px auto',
        maxWidth: '600px'
      }}>
        <h2>🔗 الروابط المتاحة:</h2>
        <ul style={{ listStyle: 'none', padding: 0 }}>
          <li style={{ margin: '10px 0' }}>
            <a href="/bookings" style={{ color: 'white', textDecoration: 'none', fontSize: '18px' }}>
              📋 صفحة الحجوزات
            </a>
          </li>
          <li style={{ margin: '10px 0' }}>
            <a href="/simple-test" style={{ color: 'white', textDecoration: 'none', fontSize: '18px' }}>
              🧪 اختبار المكونات
            </a>
          </li>
          <li style={{ margin: '10px 0' }}>
            <a href="/error-diagnosis" style={{ color: 'white', textDecoration: 'none', fontSize: '18px' }}>
              🔍 تشخيص الأخطاء
            </a>
          </li>
          <li style={{ margin: '10px 0' }}>
            <a href="/dashboard" style={{ color: 'white', textDecoration: 'none', fontSize: '18px' }}>
              📊 لوحة التحكم
            </a>
          </li>
        </ul>
      </div>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        padding: '15px',
        borderRadius: '10px',
        margin: '20px auto',
        maxWidth: '500px'
      }}>
        <p style={{ margin: 0, fontSize: '16px' }}>
          ✅ الخادم يعمل على المنفذ 3000<br/>
          ✅ جميع المكونات محملة بنجاح<br/>
          ✅ النظام جاهز للاستخدام
        </p>
      </div>
    </div>
  );
};

export default TestPage;