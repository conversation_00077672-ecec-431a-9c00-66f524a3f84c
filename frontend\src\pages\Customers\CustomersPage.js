import React, { useState, useEffect } from 'react';
import agentsService from '../../services/AgentsService';
import customersService from '../../services/CustomersService';
import AgentSelector from '../../components/Customers/AgentSelector';
import './CustomersPage.css';

const CustomersPage = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('list');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterVisaCategory, setFilterVisaCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterAgent, setFilterAgent] = useState('');
  const [filterDeliveryLocation, setFilterDeliveryLocation] = useState('');
  const [filterTransactionStatus, setFilterTransactionStatus] = useState('all');
  const [filterDeliveryDate, setFilterDeliveryDate] = useState('');
  
  // ربط مع مخزون التأشيرات
  const [visaInventory, setVisaInventory] = useState([]);
  const [selectedVisa, setSelectedVisa] = useState(null);
  const [showVisaSelector, setShowVisaSelector] = useState(false);
  
  // ربط مع قائمة الوكلاء
  const [agentsList, setAgentsList] = useState([]);
  
  // إدارة عرض الأعمدة
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState({
    customerName: true,
    phone: true,
    passportNumber: true,
    profession: true,
    email: true,
    address: false,
    agentName: true,
    authorizationOffice: true,
    deliveryLocation: false,
    requestNumber: true,
    visaType: true,
    visaCategory: true,
    issuedNumber: true,
    registryNumber: false,
    companyName: true,
    deliveryDate: true,
    deportationDate: true,
    embassyArrivalDate: false,
    customerDeliveryDate: false,
    transactionFeesPaid: true,
    transactionFeesRemaining: false,
    visaFeesPaid: true,
    visaFeesRemaining: false,
    currency: true,
    transactionStatus: true,
    paymentStatusToOffice: true,
    customerDeliveryStatus: true,
    customerStatus: true,
    notes: false,
    registrationDate: false,
    actions: true
  });

  // تعريف الأعمدة
  const columnDefinitions = {
    customerName: { label: 'اسم العميل', width: '150px' },
    phone: { label: 'الجوال', width: '120px' },
    passportNumber: { label: 'رقم الجواز', width: '120px' },
    profession: { label: 'المهنة', width: '120px' },
    email: { label: 'الإيميل', width: '180px' },
    address: { label: 'العنوان', width: '200px' },
    agentName: { label: 'اسم الوكيل', width: '150px' },
    authorizationOffice: { label: 'مكتب التفويض', width: '150px' },
    deliveryLocation: { label: 'مكان التسليم', width: '150px' },
    requestNumber: { label: 'رقم الطلب', width: '120px' },
    visaType: { label: 'نوع الفيزا', width: '120px' },
    visaCategory: { label: 'نوع التأشيرة', width: '120px' },
    issuedNumber: { label: 'رقم الصادر', width: '120px' },
    registryNumber: { label: 'رقم السجل', width: '120px' },
    companyName: { label: 'اسم الشركة', width: '150px' },
    deliveryDate: { label: 'تاريخ التسليم', width: '120px' },
    deportationDate: { label: 'تاريخ الترحيل', width: '120px' },
    embassyArrivalDate: { label: 'تاريخ الوصول من السفارة', width: '160px' },
    customerDeliveryDate: { label: 'تاريخ التسليم للعميل', width: '160px' },
    transactionFeesPaid: { label: 'رسوم المعاملة المدفوعة', width: '160px' },
    transactionFeesRemaining: { label: 'رسوم المعاملة المتبقية', width: '160px' },
    visaFeesPaid: { label: 'رسوم الفيزا المدفوعة', width: '160px' },
    visaFeesRemaining: { label: 'رسوم الفيزا المتبقية', width: '160px' },
    currency: { label: 'العملة', width: '80px' },
    transactionStatus: { label: 'حالة المعاملة', width: '150px' },
    paymentStatusToOffice: { label: 'حالة السداد', width: '120px' },
    customerDeliveryStatus: { label: 'حالة التسليم', width: '120px' },
    customerStatus: { label: 'حالة العميل', width: '120px' },
    notes: { label: 'ملاحظات', width: '200px' },
    registrationDate: { label: 'تاريخ التسجيل', width: '120px' },
    actions: { label: 'الإجراءات', width: '120px' }
  };

  // دالة تبديل عرض العمود
  const toggleColumn = (columnKey) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey]
    }));
  };

  // دالة إظهار جميع الأعمدة
  const showAllColumns = () => {
    const allVisible = {};
    Object.keys(columnDefinitions).forEach(key => {
      allVisible[key] = true;
    });
    setVisibleColumns(allVisible);
  };

  // دالة إخفاء جميع الأعمدة (عدا الأساسية)
  const hideAllColumns = () => {
    setVisibleColumns({
      customerName: true,
      phone: true,
      passportNumber: true,
      visaType: true,
      transactionStatus: true,
      actions: true,
      ...Object.keys(columnDefinitions).reduce((acc, key) => {
        if (!['customerName', 'phone', 'passportNumber', 'visaType', 'transactionStatus', 'actions'].includes(key)) {
          acc[key] = false;
        }
        return acc;
      }, {})
    });
  };

  // دالة الحصول على الأعمدة المرئية
  const getVisibleColumns = () => {
    return Object.keys(columnDefinitions).filter(key => visibleColumns[key]);
  };

  // دالة عرض قيمة العمود
  const renderColumnValue = (customer, columnKey) => {
    switch (columnKey) {
      case 'customerName':
        return customer.customerName;
      case 'phone':
        return customer.phone;
      case 'passportNumber':
        return <span style={{ color: '#3498db', fontWeight: 'bold' }}>{customer.passportNumber}</span>;
      case 'profession':
        return customer.profession;
      case 'email':
        return customer.email;
      case 'address':
        return customer.address;
      case 'agentName':
        return customer.agentName;
      case 'authorizationOffice':
        return customer.authorizationOffice;
      case 'deliveryLocation':
        return customer.deliveryLocation;
      case 'requestNumber':
        return <span style={{ color: '#9b59b6', fontWeight: 'bold' }}>{customer.requestNumber}</span>;
      case 'visaType':
        return (
          <span style={{
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '10px',
            fontWeight: 'bold',
            background: customer.visaType === 'work' ? '#e8f5e8' :
                       customer.visaType === 'work_temporary' ? '#fff3cd' :
                       customer.visaType === 'family_visit' ? '#f0e6ff' :
                       customer.visaType === 'tourist' ? '#cce5ff' :
                       customer.visaType === 'business' ? '#fff3cd' : '#f8d7da',
            color: customer.visaType === 'work' ? '#27ae60' :
                  customer.visaType === 'work_temporary' ? '#f39c12' :
                  customer.visaType === 'family_visit' ? '#8e44ad' :
                  customer.visaType === 'tourist' ? '#3498db' :
                  customer.visaType === 'business' ? '#f39c12' : '#e74c3c'
          }}>
            {getVisaTypeLabel(customer.visaType)}
          </span>
        );
      case 'visaCategory':
        return (
          <span style={{
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '10px',
            fontWeight: 'bold',
            background: customer.visaCategory === 'individual' ? '#e8f5e8' : '#fff3cd',
            color: customer.visaCategory === 'individual' ? '#27ae60' : '#f39c12'
          }}>
            {customer.visaCategory === 'individual' ? 'فردي' : 
             customer.visaCategory === 'normal' ? 'عادي' : customer.visaCategory}
          </span>
        );
      case 'issuedNumber':
        return customer.issuedNumber;
      case 'registryNumber':
        return customer.registryNumber;
      case 'companyName':
        return customer.companyName;
      case 'deliveryDate':
        return customer.deliveryDate;
      case 'deportationDate':
        return customer.deportationDate;
      case 'embassyArrivalDate':
        return customer.embassyArrivalDate;
      case 'customerDeliveryDate':
        return customer.customerDeliveryDate;
      case 'transactionFeesPaid':
        return <span style={{ color: '#27ae60', fontWeight: 'bold' }}>{customer.transactionFeesPaid ? customer.transactionFeesPaid.toLocaleString() : '0'}</span>;
      case 'transactionFeesRemaining':
        return <span style={{ color: customer.transactionFeesRemaining > 0 ? '#e74c3c' : '#27ae60', fontWeight: 'bold' }}>{customer.transactionFeesRemaining ? customer.transactionFeesRemaining.toLocaleString() : '0'}</span>;
      case 'visaFeesPaid':
        return <span style={{ color: '#3498db', fontWeight: 'bold' }}>{customer.visaFeesPaid ? customer.visaFeesPaid.toLocaleString() : '0'}</span>;
      case 'visaFeesRemaining':
        return <span style={{ color: customer.visaFeesRemaining > 0 ? '#e74c3c' : '#3498db', fontWeight: 'bold' }}>{customer.visaFeesRemaining ? customer.visaFeesRemaining.toLocaleString() : '0'}</span>;
      case 'currency':
        return customer.currency === 'YER' ? 'ر.ي' : 
               customer.currency === 'SAR' ? 'ر.س' : 
               customer.currency === 'USD' ? '$' : customer.currency;
      case 'transactionStatus':
        return (
          <span style={{
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '10px',
            fontWeight: 'bold',
            background: customer.transactionStatus === 'delivered_stamped' ? '#e8f5e8' :
                       customer.transactionStatus === 'delivered_unstamped' ? '#fff3cd' :
                       customer.transactionStatus === 'embassy_stamped' ? '#d4edda' :
                       customer.transactionStatus === 'office_stamped' ? '#cce5ff' :
                       customer.transactionStatus === 'embassy_execution' ? '#fff3cd' :
                       customer.transactionStatus === 'office_preparation' ? '#f8d7da' :
                       customer.transactionStatus === 'returned' ? '#f5c6cb' : '#e2e3e5',
            color: customer.transactionStatus === 'delivered_stamped' ? '#27ae60' :
                  customer.transactionStatus === 'delivered_unstamped' ? '#f39c12' :
                  customer.transactionStatus === 'embassy_stamped' ? '#155724' :
                  customer.transactionStatus === 'office_stamped' ? '#004085' :
                  customer.transactionStatus === 'embassy_execution' ? '#856404' :
                  customer.transactionStatus === 'office_preparation' ? '#721c24' :
                  customer.transactionStatus === 'returned' ? '#721c24' : '#6c757d'
          }}>
            {customer.transactionStatus === 'office_preparation' ? 'قيد التجهيز بالمكتب' :
             customer.transactionStatus === 'embassy_execution' ? 'قيد التنفيذ بالسفارة' :
             customer.transactionStatus === 'embassy_stamped' ? 'مؤشر في السفارة' :
             customer.transactionStatus === 'office_stamped' ? 'مؤشر في المكتب' :
             customer.transactionStatus === 'delivered_stamped' ? 'مسلم للعميل مؤشر' :
             customer.transactionStatus === 'delivered_unstamped' ? 'مسلم للعميل غير مؤشر' :
             customer.transactionStatus === 'returned' ? 'مرجوع' : customer.transactionStatus}
          </span>
        );
      case 'paymentStatusToOffice':
        return (
          <span style={{
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '10px',
            fontWeight: 'bold',
            background: customer.paymentStatusToOffice === 'paid' ? '#e8f5e8' :
                       customer.paymentStatusToOffice === 'partial' ? '#fff3cd' :
                       customer.paymentStatusToOffice === 'unpaid' ? '#f8d7da' : '#e2e3e5',
            color: customer.paymentStatusToOffice === 'paid' ? '#27ae60' :
                  customer.paymentStatusToOffice === 'partial' ? '#f39c12' :
                  customer.paymentStatusToOffice === 'unpaid' ? '#e74c3c' : '#6c757d'
          }}>
            {customer.paymentStatusToOffice === 'paid' ? 'مدفوع' :
             customer.paymentStatusToOffice === 'partial' ? 'جزئي' :
             customer.paymentStatusToOffice === 'unpaid' ? 'غير مدفوع' :
             customer.paymentStatusToOffice === 'overdue' ? 'متأخر' : customer.paymentStatusToOffice}
          </span>
        );
      case 'customerDeliveryStatus':
        return (
          <span style={{
            padding: '4px 8px',
            borderRadius: '12px',
            fontSize: '10px',
            fontWeight: 'bold',
            background: customer.customerDeliveryStatus === 'delivered' ? '#e8f5e8' :
                       customer.customerDeliveryStatus === 'ready' ? '#fff3cd' :
                       customer.customerDeliveryStatus === 'pending' ? '#f8d7da' : '#e2e3e5',
            color: customer.customerDeliveryStatus === 'delivered' ? '#27ae60' :
                  customer.customerDeliveryStatus === 'ready' ? '#f39c12' :
                  customer.customerDeliveryStatus === 'pending' ? '#e74c3c' : '#6c757d'
          }}>
            {customer.customerDeliveryStatus === 'pending' ? 'انتظار' :
             customer.customerDeliveryStatus === 'ready' ? 'جاهز' :
             customer.customerDeliveryStatus === 'delivered' ? 'تم التسليم' :
             customer.customerDeliveryStatus === 'overdue' ? 'متأخر' : customer.customerDeliveryStatus}
          </span>
        );
      case 'customerStatus':
        return (
          <select
            value={customer.customerStatus}
            onChange={(e) => {
              const updatedCustomers = customers.map(c => 
                c.id === customer.id ? {...c, customerStatus: e.target.value} : c
              );
              setCustomers(updatedCustomers);
            }}
            style={{
              padding: '4px 8px',
              border: 'none',
              borderRadius: '12px',
              fontSize: '10px',
              fontWeight: 'bold',
              background: customer.customerStatus === 'active' ? '#e8f5e8' :
                         customer.customerStatus === 'vip' ? '#fff3cd' :
                         customer.customerStatus === 'inactive' ? '#f8d7da' : '#e2e3e5',
              color: customer.customerStatus === 'active' ? '#27ae60' :
                    customer.customerStatus === 'vip' ? '#f39c12' :
                    customer.customerStatus === 'inactive' ? '#e74c3c' : '#6c757d'
            }}
          >
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
            <option value="blocked">محظور</option>
            <option value="vip">مميز</option>
          </select>
        );
      case 'notes':
        return customer.notes;
      case 'registrationDate':
        return customer.registrationDate || new Date().toLocaleDateString('ar-SA');
      case 'actions':
        return (
          <div style={{ display: 'flex', gap: '5px' }}>
            <button
              onClick={() => {
                setNewCustomer(customer);
                setActiveTab('add');
              }}
              style={{
                padding: '4px 8px',
                background: '#3498db',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              تحرير
            </button>
            <button
              onClick={() => {
                if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                  setCustomers(customers.filter(c => c.id !== customer.id));
                }
              }}
              style={{
                padding: '4px 8px',
                background: '#e74c3c',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              حذف
            </button>
          </div>
        );
      default:
        return customer[columnKey] || '';
    }
  };

  const [newCustomer, setNewCustomer] = useState({
    // المعلومات الأساسية
    customerName: '',
    phone: '',
    passportNumber: '',
    profession: '',
    email: '',
    address: '',
    
    // معلومات التأشيرة والمعاملة
    agentName: '',
    authorizationOffice: '',
    deliveryLocation: '',
    requestNumber: '',
    visaType: '',
    visaCategory: 'normal', // individual, normal
    issuedNumber: '',
    registryNumber: '',
    companyName: '',
    
    // التواريخ المهمة
    deliveryDate: '',
    deportationDate: '',
    embassyArrivalDate: '',
    customerDeliveryDate: '',
    
    // المعلومات المالية
    transactionFeesPaid: 0,
    transactionFeesRemaining: 0,
    visaFeesPaid: 0,
    visaFeesRemaining: 0,
    currency: 'YER', // YER, SAR, USD
    
    // الحالة والملاحظات
    transactionStatus: 'office_preparation',
    paymentStatusToOffice: 'unpaid',
    customerDeliveryStatus: 'pending',
    customerStatus: 'active',
    notes: '',
    
    // مرفقات المعاملة
    attachments: []
  });

  const [customerStats, setCustomerStats] = useState({
    totalCustomers: 0,
    activeCustomers: 0,
    vipCustomers: 0,
    businessVisas: 0,
    newThisMonth: 0,
    totalTransactions: 0
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      const mockCustomers = [
        {
          id: 1,
          customerName: 'أحمد محمد العلي',
          phone: '+967771234567',
          passportNumber: 'A12345678',
          profession: 'مهندس',
          email: '<EMAIL>',
          address: 'صنعاء، شارع الزبيري، حي السبعين',
          
          agentName: 'محمد الحداد',
          authorizationOffice: 'مكتب الهجرة والجوازات',
          deliveryLocation: 'مكتب الوكالة الرئيسي',
          requestNumber: 'REQ-2024-001',
          visaType: 'work',
          visaCategory: 'individual',
          issuedNumber: 'V123456',
          registryNumber: 'R789012',
          companyName: 'شركة البناء المتقدمة',
          
          deliveryDate: '2024-01-15',
          deportationDate: '2024-02-01',
          embassyArrivalDate: '2024-01-25',
          customerDeliveryDate: '2024-01-30',
          
          transactionFeesPaid: 50000,
          transactionFeesRemaining: 10000,
          visaFeesPaid: 80000,
          visaFeesRemaining: 0,
          currency: 'YER',
          
          transactionStatus: 'delivered_stamped',
          paymentStatusToOffice: 'paid',
          customerDeliveryStatus: 'delivered',
          customerStatus: 'active',
          notes: 'عميل مميز - معاملة مكتملة بنجاح',
          
          registrationDate: '2023-01-15',
          lastBooking: '2024-01-20',
          totalBookings: 15,
          totalSpent: 45000,
          loyaltyPoints: 2250,
          attachments: []
        },
        {
          id: 2,
          customerName: 'سارة عبدالله الخالد',
          phone: '+967772345678',
          passportNumber: 'B87654321',
          profession: 'طبيبة',
          email: '<EMAIL>',
          address: 'عدن، كريتر، شارع الملكة أروى',
          
          agentName: 'فاطمة الشامي',
          authorizationOffice: 'مكتب الجوازات عدن',
          deliveryLocation: 'مكتب الوكالة فرع عدن',
          requestNumber: 'REQ-2024-002',
          visaType: 'family_visit',
          visaCategory: 'normal',
          issuedNumber: 'V234567',
          registryNumber: 'R890123',
          companyName: '',
          
          deliveryDate: '2024-01-10',
          deportationDate: '2024-01-28',
          embassyArrivalDate: '2024-01-22',
          customerDeliveryDate: '2024-01-26',
          
          transactionFeesPaid: 30000,
          transactionFeesRemaining: 5000,
          visaFeesPaid: 60000,
          visaFeesRemaining: 15000,
          currency: 'YER',
          
          transactionStatus: 'embassy_execution',
          paymentStatusToOffice: 'partial',
          customerDeliveryStatus: 'ready',
          customerStatus: 'active',
          notes: 'عميلة جديدة - تحتاج متابعة',
          
          registrationDate: '2023-05-10',
          lastBooking: '2024-01-18',
          totalBookings: 8,
          totalSpent: 24000,
          loyaltyPoints: 1200,
          attachments: []
        },
        {
          id: 3,
          customerName: 'خالد عبدالرحمن الزهراني',
          phone: '+967773456789',
          passportNumber: 'C11223344',
          profession: 'فني كهرباء',
          email: '<EMAIL>',
          address: 'تعز، شارع جمال عبدالناصر',

          agentName: 'محمد الحداد',
          authorizationOffice: 'مكتب الهجرة والجوازات',
          deliveryLocation: 'مكتب الوكالة الرئيسي',
          requestNumber: 'REQ-2024-003',
          visaType: 'work_temporary',
          visaCategory: 'individual',
          issuedNumber: 'V345678',
          registryNumber: 'R901234',
          companyName: 'شركة الكهرباء المحدودة',

          deliveryDate: '2024-01-20',
          deportationDate: '2024-07-20',
          embassyArrivalDate: '2024-01-28',
          customerDeliveryDate: '2024-02-02',

          transactionFeesPaid: 35000,
          transactionFeesRemaining: 0,
          visaFeesPaid: 45000,
          visaFeesRemaining: 5000,
          currency: 'YER',

          transactionStatus: 'delivered_stamped',
          paymentStatusToOffice: 'paid',
          customerDeliveryStatus: 'delivered',
          customerStatus: 'active',
          notes: 'فيزة عمل مؤقتة لمدة 6 أشهر',

          registrationDate: '2023-12-01',
          lastBooking: '2024-01-22',
          totalBookings: 3,
          totalSpent: 40000,
          loyaltyPoints: 400,
          attachments: []
        },
        {
          id: 4,
          customerName: 'نورا أحمد الشهري',
          phone: '+967774567890',
          passportNumber: 'D55667788',
          profession: 'معلمة',
          email: '<EMAIL>',
          address: 'الحديدة، شارع الكورنيش',

          agentName: 'فاطمة الشامي',
          authorizationOffice: 'مكتب الجوازات الحديدة',
          deliveryLocation: 'مكتب الوكالة فرع الحديدة',
          requestNumber: 'REQ-2024-004',
          visaType: 'tourist',
          visaCategory: 'normal',
          issuedNumber: 'V456789',
          registryNumber: 'R012345',
          companyName: '',

          deliveryDate: '2024-01-25',
          deportationDate: '2024-03-25',
          embassyArrivalDate: '2024-02-01',
          customerDeliveryDate: '2024-02-05',

          transactionFeesPaid: 25000,
          transactionFeesRemaining: 0,
          visaFeesPaid: 40000,
          visaFeesRemaining: 0,
          currency: 'YER',

          transactionStatus: 'embassy_execution',
          paymentStatusToOffice: 'paid',
          customerDeliveryStatus: 'pending',
          customerStatus: 'active',
          notes: 'فيزة سياحية لمدة شهرين',

          registrationDate: '2024-01-01',
          lastBooking: '2024-01-25',
          totalBookings: 1,
          totalSpent: 25000,
          loyaltyPoints: 250,
          attachments: []
        }
      ];

      // تحميل العملاء من الخدمة المشتركة
      const customersData = customersService.getAllCustomers();
      setCustomers(customersData);

      // حساب الإحصائيات من البيانات الفعلية
      const activeCustomers = customersData.filter(c => c.customerStatus === 'active').length;
      const vipCustomers = customersData.filter(c => c.customerStatus === 'vip').length;
      const corporateCustomers = customersData.filter(c => c.visaType === 'business').length;
      const newThisMonth = customersData.filter(c =>
        new Date(c.createdDate) >= new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      ).length;
      const totalBookings = customersData.length; // عدد الحجوزات = عدد العملاء

      setCustomerStats({
        totalCustomers: customersData.length,
        activeCustomers: activeCustomers,
        vipCustomers: vipCustomers,
        businessVisas: corporateCustomers,
        newThisMonth: newThisMonth,
        totalTransactions: totalBookings
      });

      // تحميل مخزون التأشيرات
      const mockVisaInventory = [
        {
          id: 1,
          visaNumber: 'UAE-2024-001',
          country: 'الإمارات العربية المتحدة',
          countryCode: 'AE',
          visaType: 'individual_yearly',
          totalCount: 100,
          usedCount: 25,
          remainingCount: 75,
          issuerNumber: 'ISS-2024-001',
          registrationNumber: 'REG-UAE-001',
          companyName: 'شركة الإمارات للسياحة',
          profession: 'سائح',
          issueDate: '2024-01-15',
          supplierName: 'وكالة الإمارات للسفر',
          agentName: 'أحمد محمد السالم',
          authorizationOffice: 'مكتب دبي للتفويض',
          cost: 800,
          sellingPrice: 1200,
          status: 'available',
          notes: 'تأشيرة فردية سنوية - متعددة الاستخدام'
        },
        {
          id: 2,
          visaNumber: 'TUR-2024-002',
          country: 'تركيا',
          countryCode: 'TR',
          visaType: 'regular_3months',
          totalCount: 50,
          usedCount: 30,
          remainingCount: 20,
          issuerNumber: 'ISS-2024-002',
          registrationNumber: 'REG-TUR-002',
          companyName: 'شركة الأناضول للسياحة',
          profession: 'رجل أعمال',
          issueDate: '2024-01-10',
          supplierName: 'القنصلية التركية',
          agentName: 'فاطمة أحمد الزهراني',
          authorizationOffice: 'مكتب إسطنبول للتفويض',
          cost: 600,
          sellingPrice: 900,
          status: 'available',
          notes: 'تأشيرة عادية 3 شهور - متاحة'
        },
        {
          id: 3,
          visaNumber: 'EGY-2024-003',
          country: 'مصر',
          countryCode: 'EG',
          visaType: 'regular_3months',
          totalCount: 200,
          usedCount: 150,
          remainingCount: 50,
          issuerNumber: 'ISS-2024-003',
          registrationNumber: 'REG-EGY-003',
          companyName: 'شركة النيل للسياحة',
          profession: 'سائح',
          issueDate: '2024-01-05',
          supplierName: 'القنصلية المصرية',
          agentName: 'محمد علي حسن',
          authorizationOffice: 'مكتب القاهرة للتفويض',
          cost: 300,
          sellingPrice: 500,
          status: 'available',
          notes: 'تأشيرة عادية 3 شهور - سياحية'
        }
      ];

      setVisaInventory(mockVisaInventory);

      // تحميل قائمة الوكلاء (نفس البيانات من صفحة الوكلاء)
      const mockAgents = [
        {
          id: 1,
          agentName: 'أحمد محمد السالم',
          agentCode: 'AGT001',
          phone: '+966501234567',
          email: '<EMAIL>',
          office: 'مكتب الرياض الرئيسي',
          specialty: 'تأشيرات الإمارات',
          status: 'active'
        },
        {
          id: 2,
          agentName: 'فاطمة أحمد الزهراني',
          agentCode: 'AGT002',
          phone: '+966502345678',
          email: '<EMAIL>',
          office: 'مكتب جدة',
          specialty: 'تأشيرات تركيا',
          status: 'active'
        },
        {
          id: 3,
          agentName: 'محمد علي حسن',
          agentCode: 'AGT003',
          phone: '+966503456789',
          email: '<EMAIL>',
          office: 'مكتب الدمام',
          specialty: 'تأشيرات مصر',
          status: 'active'
        },
        {
          id: 4,
          agentName: 'سارة محمد الأحمد',
          agentCode: 'AGT004',
          phone: '+966504567890',
          email: '<EMAIL>',
          office: 'مكتب مكة',
          specialty: 'تأشيرات الهند',
          status: 'active'
        },
        {
          id: 5,
          agentName: 'خالد عبدالله المطيري',
          agentCode: 'AGT005',
          phone: '+966505678901',
          email: '<EMAIL>',
          office: 'مكتب المدينة',
          specialty: 'تأشيرات أمريكا',
          status: 'inactive'
        }
      ];

      // تحميل قائمة الوكلاء من الخدمة المشتركة
      const agents = agentsService.getAllAgents();
      setAgentsList(agents);

      setLoading(false);
    }, 1000);

    // إضافة مستمع للتحديثات المباشرة للوكلاء
    const handleAgentsUpdate = (updatedAgents) => {
      setAgentsList(updatedAgents);
    };

    agentsService.addListener(handleAgentsUpdate);

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      agentsService.removeListener(handleAgentsUpdate);
    };
  }, []);

  // دالة لترجمة أنواع الفيز إلى العربية
  const getVisaTypeLabel = (visaType) => {
    const visaTypes = {
      'work': 'فيزة عمل',
      'work_temporary': 'فيزة عمل مؤقتة',
      'family_visit': 'فيزة زيارة عائلية',
      'tourist': 'فيزة سياحية',
      'business': 'فيزة تجارية',
      'study': 'فيزة دراسة',
      'transit': 'فيزة ترانزيت',
      'medical': 'فيزة علاج',
      'diplomatic': 'فيزة دبلوماسية',
      'official': 'فيزة رسمية',
      'other': 'أخرى'
    };
    return visaTypes[visaType] || visaType;
  };

  // دالة اختيار التأشيرة من المخزون
  const handleSelectVisa = (visa) => {
    setSelectedVisa(visa);
    setNewCustomer({
      ...newCustomer,
      issuedNumber: visa.issuerNumber,
      registryNumber: visa.registrationNumber,
      companyName: visa.companyName,
      agentName: visa.agentName,
      authorizationOffice: visa.authorizationOffice
    });
    setShowVisaSelector(false);
  };

  // دالة إلغاء اختيار التأشيرة
  const handleClearVisa = () => {
    setSelectedVisa(null);
    setNewCustomer({
      ...newCustomer,
      issuedNumber: '',
      registryNumber: '',
      companyName: '',
      agentName: '',
      authorizationOffice: ''
    });
  };

  const handleAddCustomer = (e) => {
    e.preventDefault();
    const customer = customersService.addCustomer(newCustomer);
    setCustomers(customersService.getAllCustomers());

    // تحديث الإحصائيات
    const customersData = customersService.getAllCustomers();
    const activeCustomers = customersData.filter(c => c.customerStatus === 'active').length;
    const vipCustomers = customersData.filter(c => c.customerStatus === 'vip').length;
    const corporateCustomers = customersData.filter(c => c.visaType === 'business').length;
    const newThisMonth = customersData.filter(c =>
      new Date(c.createdDate) >= new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    ).length;

    setCustomerStats({
      totalCustomers: customersData.length,
      activeCustomers: activeCustomers,
      vipCustomers: vipCustomers,
      businessVisas: corporateCustomers,
      newThisMonth: newThisMonth,
      totalTransactions: customersData.length
    });

    // تحديث مخزون التأشيرات إذا تم اختيار تأشيرة
    if (selectedVisa) {
      const updatedInventory = visaInventory.map(visa => {
        if (visa.id === selectedVisa.id) {
          return {
            ...visa,
            usedCount: visa.usedCount + 1,
            remainingCount: visa.remainingCount - 1,
            status: visa.remainingCount - 1 <= 0 ? 'sold' : visa.status
          };
        }
        return visa;
      });
      setVisaInventory(updatedInventory);
    }
    
    // إعادة تعيين النموذج
    setSelectedVisa(null);
    setNewCustomer({
      // المعلومات الأساسية
      customerName: '',
      phone: '',
      passportNumber: '',
      profession: '',
      email: '',
      address: '',
      
      // معلومات التأشيرة والمعاملة
      agentName: '',
      authorizationOffice: '',
      deliveryLocation: '',
      requestNumber: '',
      visaType: '',
      issuedNumber: '',
      registryNumber: '',
      companyName: '',
      
      // التواريخ المهمة
      deliveryDate: '',
      deportationDate: '',
      embassyArrivalDate: '',
      customerDeliveryDate: '',
      
      // المعلومات المالية
      transactionFeesPaid: 0,
      transactionFeesRemaining: 0,
      visaFeesPaid: 0,
      visaFeesRemaining: 0,
      currency: 'YER',
      
      // الحالة والملاحظات
      transactionStatus: 'office_preparation',
      paymentStatusToOffice: 'unpaid',
      customerDeliveryStatus: 'pending',
      customerStatus: 'active',
      notes: '',
      
      // مرفقات المعاملة
      attachments: []
    });
    
    setActiveTab('list');
  };

  const handleDeleteCustomer = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      setCustomers(customers.filter(customer => customer.id !== id));
    }
  };

  const handleFileUpload = (files) => {
    const fileArray = Array.from(files);
    const currentAttachments = newCustomer.attachments || [];
    setNewCustomer({...newCustomer, attachments: [...currentAttachments, ...fileArray]});
  };

  const removeFile = (index) => {
    const currentAttachments = newCustomer.attachments || [];
    const newFiles = currentAttachments.filter((_, i) => i !== index);
    setNewCustomer({...newCustomer, attachments: newFiles});
  };

  const handleStatusChange = (id, newStatus) => {
    setCustomers(customers.map(customer => 
      customer.id === id ? { ...customer, customerStatus: newStatus } : customer
    ));
  };

  // دوال الطباعة والتصدير والاستيراد
  const handlePrint = () => {
    const visibleCols = getVisibleColumns();
    const printWindow = window.open('', '_blank');
    const printContent = `
      <html>
        <head>
          <title>قائمة العملاء - شراء السفريات</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: center; font-size: 10px; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .header { text-align: center; margin-bottom: 20px; }
            .company-name { font-size: 24px; font-weight: bold; color: #2c3e50; }
            .report-title { font-size: 18px; color: #34495e; margin-top: 10px; }
            .print-date { font-size: 12px; color: #7f8c8d; margin-top: 10px; }
            .column-info { font-size: 12px; color: #7f8c8d; margin-top: 5px; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">شراء السفريات</div>
            <div class="report-title">تقرير قائمة العملاء</div>
            <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</div>
            <div class="column-info">الأعمدة المعروضة: ${visibleCols.length} من ${Object.keys(columnDefinitions).length}</div>
          </div>
          <table>
            <thead>
              <tr>
                ${visibleCols.map(col => `<th>${columnDefinitions[col].label}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
              ${filteredCustomers.map(customer => `
                <tr>
                  ${visibleCols.map(col => {
                    const value = renderColumnValue(customer, col);
                    // تحويل القيم المعقدة إلى نص للطباعة
                    if (typeof value === 'object' && value.props) {
                      if (col === 'visaType') {
                        return `<td>${getVisaTypeLabel(customer.visaType)}</td>`;
                      } else if (col === 'visaCategory') {
                        return `<td>${customer.visaCategory === 'individual' ? 'فردي' : 
                                     customer.visaCategory === 'normal' ? 'عادي' : customer.visaCategory}</td>`;
                      } else if (col === 'transactionStatus') {
                        return `<td>${customer.transactionStatus === 'office_preparation' ? 'قيد التجهيز بالمكتب' :
                                     customer.transactionStatus === 'embassy_execution' ? 'قيد التنفيذ بالسفارة' :
                                     customer.transactionStatus === 'embassy_stamped' ? 'مؤشر في السفارة' :
                                     customer.transactionStatus === 'office_stamped' ? 'مؤشر في المكتب' :
                                     customer.transactionStatus === 'delivered_stamped' ? 'مسلم للعميل مؤشر' :
                                     customer.transactionStatus === 'delivered_unstamped' ? 'مسلم للعميل غير مؤشر' :
                                     customer.transactionStatus === 'returned' ? 'مرجوع' : customer.transactionStatus}</td>`;
                      } else if (col === 'customerStatus') {
                        return `<td>${customer.customerStatus === 'active' ? 'نشط' :
                                     customer.customerStatus === 'inactive' ? 'غير نشط' :
                                     customer.customerStatus === 'blocked' ? 'محظور' :
                                     customer.customerStatus === 'vip' ? 'مميز' : customer.customerStatus}</td>`;
                      } else if (col === 'actions') {
                        return '<td>-</td>';
                      } else {
                        return `<td>${customer[col] || '-'}</td>`;
                      }
                    } else {
                      return `<td>${value || '-'}</td>`;
                    }
                  }).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
          <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #7f8c8d;">
            إجمالي العملاء: ${filteredCustomers.length} | تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}
          </div>
        </body>
      </html>
    `;
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const handleExportExcel = () => {
    const visibleCols = getVisibleColumns();
    const csvContent = [
      // Headers - الأعمدة المحددة فقط
      visibleCols.map(col => columnDefinitions[col].label),
      // Data - الأعمدة المحددة فقط
      ...filteredCustomers.map(customer => 
        visibleCols.map(col => {
          const value = renderColumnValue(customer, col);
          // تحويل القيم المعقدة إلى نص للتصدير
          if (typeof value === 'object' && value.props) {
            if (col === 'visaType') {
              return getVisaTypeLabel(customer.visaType);
            } else if (col === 'visaCategory') {
              return customer.visaCategory === 'individual' ? 'فردي' : 
                     customer.visaCategory === 'normal' ? 'عادي' : customer.visaCategory;
            } else if (col === 'transactionStatus') {
              return customer.transactionStatus === 'office_preparation' ? 'قيد التجهيز بالمكتب' :
                     customer.transactionStatus === 'embassy_execution' ? 'قيد التنفيذ بالسفارة' :
                     customer.transactionStatus === 'embassy_stamped' ? 'مؤشر في السفارة' :
                     customer.transactionStatus === 'office_stamped' ? 'مؤشر في المكتب' :
                     customer.transactionStatus === 'delivered_stamped' ? 'مسلم للعميل مؤشر' :
                     customer.transactionStatus === 'delivered_unstamped' ? 'مسلم للعميل غير مؤشر' :
                     customer.transactionStatus === 'returned' ? 'مرجوع' : customer.transactionStatus;
            } else if (col === 'paymentStatusToOffice') {
              return customer.paymentStatusToOffice === 'paid' ? 'مدفوع' :
                     customer.paymentStatusToOffice === 'partial' ? 'جزئي' :
                     customer.paymentStatusToOffice === 'unpaid' ? 'غير مدفوع' :
                     customer.paymentStatusToOffice === 'overdue' ? 'متأخر' : customer.paymentStatusToOffice;
            } else if (col === 'customerDeliveryStatus') {
              return customer.customerDeliveryStatus === 'delivered' ? 'تم التسليم' :
                     customer.customerDeliveryStatus === 'ready' ? 'جاهز' :
                     customer.customerDeliveryStatus === 'pending' ? 'انتظار' :
                     customer.customerDeliveryStatus === 'overdue' ? 'متأخر' : customer.customerDeliveryStatus;
            } else if (col === 'customerStatus') {
              return customer.customerStatus === 'active' ? 'نشط' :
                     customer.customerStatus === 'inactive' ? 'غير نشط' :
                     customer.customerStatus === 'blocked' ? 'محظور' :
                     customer.customerStatus === 'vip' ? 'مميز' : customer.customerStatus;
            } else if (col === 'currency') {
              return customer.currency === 'YER' ? 'ريال يمني' : 
                     customer.currency === 'SAR' ? 'ريال سعودي' : 
                     customer.currency === 'USD' ? 'دولار أمريكي' : customer.currency;
            } else if (col === 'actions') {
              return '';
            } else {
              return customer[col] || '';
            }
          } else {
            return value || '';
          }
        })
      )
    ];

    const csvString = csvContent.map(row => 
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `قائمة_العملاء_${visibleCols.length}أعمدة_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const csv = e.target.result;
          const lines = csv.split('\n');
          const headers = lines[0].split(',').map(h => h.replace(/"/g, ''));
          
          const importedCustomers = lines.slice(1).filter(line => line.trim()).map((line, index) => {
            const values = line.split(',').map(v => v.replace(/"/g, ''));
            return {
              id: customers.length + index + 1,
              customerName: values[0] || '',
              phone: values[1] || '',
              passportNumber: values[2] || '',
              profession: values[3] || '',
              email: values[4] || '',
              address: values[5] || '',
              agentName: values[6] || '',
              authorizationOffice: values[7] || '',
              deliveryLocation: values[8] || '',
              requestNumber: values[9] || '',
              visaType: values[10] === 'عمل' ? 'work' : 
                       values[10] === 'سياحية' ? 'tourist' :
                       values[10] === 'تجارية' ? 'business' :
                       values[10] === 'دراسة' ? 'study' :
                       values[10] === 'عائلية' ? 'family' :
                       values[10] === 'ترانزيت' ? 'transit' : 'tourist',
              issuedNumber: values[11] || '',
              registryNumber: values[12] || '',
              companyName: values[13] || '',
              deliveryDate: values[14] || '',
              deportationDate: values[15] || '',
              embassyArrivalDate: values[16] || '',
              customerDeliveryDate: values[17] || '',
              transactionFeesPaid: parseInt(values[18]) || 0,
              transactionFeesRemaining: parseInt(values[19]) || 0,
              visaFeesPaid: parseInt(values[20]) || 0,
              visaFeesRemaining: parseInt(values[21]) || 0,
              currency: values[22] === 'ريال يمني' ? 'YER' : 
                       values[22] === 'ريال سعودي' ? 'SAR' : 
                       values[22] === 'دولار أمريكي' ? 'USD' : 'YER',
              transactionStatus: values[23] === 'قيد التجهيز بالمكتب' ? 'office_preparation' :
                               values[23] === 'قيد التنفيذ بالسفارة' ? 'embassy_execution' :
                               values[23] === 'مؤشر في السفارة' ? 'embassy_stamped' :
                               values[23] === 'مؤشر في المكتب' ? 'office_stamped' :
                               values[23] === 'مسلم للعميل مؤشر' ? 'delivered_stamped' :
                               values[23] === 'مسلم للعميل غير مؤشر' ? 'delivered_unstamped' :
                               values[23] === 'مرجوع' ? 'returned' : 'office_preparation',
              paymentStatusToOffice: values[24] === 'مدفوع' ? 'paid' :
                                   values[24] === 'جزئي' ? 'partial' :
                                   values[24] === 'غير مدفوع' ? 'unpaid' :
                                   values[24] === 'متأخر' ? 'overdue' : 'unpaid',
              customerDeliveryStatus: values[25] === 'تم التسليم' ? 'delivered' :
                                    values[25] === 'جاهز' ? 'ready' :
                                    values[25] === 'انتظار' ? 'pending' :
                                    values[25] === 'متأخر' ? 'overdue' : 'pending',
              customerStatus: values[26] === 'نشط' ? 'active' :
                            values[26] === 'غير نشط' ? 'inactive' :
                            values[26] === 'محظور' ? 'blocked' :
                            values[26] === 'مميز' ? 'vip' : 'active',
              notes: values[27] || '',
              registrationDate: values[28] || new Date().toISOString().split('T')[0],
              attachments: [],
              totalBookings: 0,
              totalSpent: (parseInt(values[18]) || 0) + (parseInt(values[20]) || 0),
              loyaltyPoints: Math.floor(((parseInt(values[18]) || 0) + (parseInt(values[20]) || 0)) / 100)
            };
          });

          setCustomers([...customers, ...importedCustomers]);
          alert(`تم استيراد ${importedCustomers.length} عميل بنجاح!`);
        } catch (error) {
          alert('حدث خطأ في استيراد الملف. تأكد من تنسيق الملف.');
        }
      };
      reader.readAsText(file);
    }
    // Reset input
    event.target.value = '';
  };

  const getCustomerTypeLabel = (type) => {
    const types = {
      individual: 'فردي',
      corporate: 'شركة',
      vip: 'مميز'
    };
    return types[type] || type;
  };

  const getCustomerTypeColor = (type) => {
    const colors = {
      individual: '#3498db',
      corporate: '#9b59b6',
      vip: '#f39c12'
    };
    return colors[type] || '#95a5a6';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#27ae60';
      case 'inactive': return '#e74c3c';
      case 'blocked': return '#95a5a6';
      default: return '#95a5a6';
    }
  };

  const getStatusLabel = (status) => {
    const labels = {
      active: 'نشط',
      inactive: 'غير نشط',
      blocked: 'محظور'
    };
    return labels[status] || status;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.passportNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || customer.visaType === filterType;
    const matchesVisaCategory = filterVisaCategory === 'all' || customer.visaCategory === filterVisaCategory;
    const matchesStatus = filterStatus === 'all' || customer.customerStatus === filterStatus;
    const matchesAgent = filterAgent === '' || (customer.agentName && customer.agentName.toLowerCase().includes(filterAgent.toLowerCase()));
    const matchesDeliveryLocation = filterDeliveryLocation === '' || (customer.deliveryLocation && customer.deliveryLocation.toLowerCase().includes(filterDeliveryLocation.toLowerCase()));
    const matchesTransactionStatus = filterTransactionStatus === 'all' || customer.transactionStatus === filterTransactionStatus;
    const matchesDeliveryDate = filterDeliveryDate === '' || customer.deliveryDate === filterDeliveryDate;
    
    return matchesSearch && matchesType && matchesVisaCategory && matchesStatus && matchesAgent && matchesDeliveryLocation && matchesTransactionStatus && matchesDeliveryDate;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل بيانات العملاء...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h1 style={{ margin: 0, color: '#2c3e50', fontSize: '28px' }}>👥 إدارة العملاء</h1>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة شاملة لقاعدة بيانات العملاء</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي العملاء', value: customerStats.totalCustomers, color: '#3498db', icon: '👥' },
          { title: 'العملاء النشطين', value: customerStats.activeCustomers, color: '#27ae60', icon: '✅' },
          { title: 'العملاء المميزين', value: customerStats.vipCustomers, color: '#f39c12', icon: '⭐' },
          { title: 'فيزا تجارية', value: customerStats.businessVisas, color: '#9b59b6', icon: '💼' },
          { title: 'جدد هذا الشهر', value: customerStats.newThisMonth, color: '#1abc9c', icon: '🆕' },
          { title: 'إجمالي المعاملات', value: customerStats.totalTransactions, color: '#e74c3c', icon: '📋' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center',
            transition: 'transform 0.3s ease'
          }}
          onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
          onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {stat.value.toLocaleString()}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Tabs */}
      <div style={{
        display: 'flex',
        gap: '10px',
        marginBottom: '30px',
        borderBottom: '2px solid #e0e0e0'
      }}>
        {[
          { id: 'list', label: 'قائمة العملاء', icon: '📋' },
          { id: 'add', label: 'إضافة عميل جديد', icon: '➕' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              padding: '12px 20px',
              border: 'none',
              background: activeTab === tab.id ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
              color: activeTab === tab.id ? 'white' : '#7f8c8d',
              borderRadius: '8px 8px 0 0',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              transition: 'all 0.3s ease'
            }}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* Customer List Tab */}
      {activeTab === 'list' && (
        <div>
          {/* مؤشر الفلاتر النشطة */}
          {(searchTerm || filterType !== 'all' || filterVisaCategory !== 'all' || filterStatus !== 'all' || filterAgent || filterDeliveryLocation || filterTransactionStatus !== 'all' || filterDeliveryDate) && (
            <div style={{
              padding: '10px 15px',
              background: '#e8f4fd',
              border: '1px solid #3498db',
              borderRadius: '8px',
              marginBottom: '15px',
              fontSize: '14px',
              color: '#2c3e50'
            }}>
              🔍 الفلاتر النشطة: 
              {searchTerm && <span style={{ marginLeft: '10px', background: '#3498db', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>بحث: {searchTerm}</span>}
              {filterType !== 'all' && <span style={{ marginLeft: '10px', background: '#27ae60', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>نوع الفيزا</span>}
              {filterVisaCategory !== 'all' && <span style={{ marginLeft: '10px', background: '#8e44ad', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>نوع التأشيرة</span>}
              {filterStatus !== 'all' && <span style={{ marginLeft: '10px', background: '#f39c12', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>حالة العميل</span>}
              {filterAgent && <span style={{ marginLeft: '10px', background: '#9b59b6', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>الوكيل: {filterAgent}</span>}
              {filterDeliveryLocation && <span style={{ marginLeft: '10px', background: '#e67e22', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>مكان التسليم: {filterDeliveryLocation}</span>}
              {filterTransactionStatus !== 'all' && <span style={{ marginLeft: '10px', background: '#1abc9c', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>حالة المعاملة</span>}
              {filterDeliveryDate && <span style={{ marginLeft: '10px', background: '#e74c3c', color: 'white', padding: '2px 8px', borderRadius: '12px', fontSize: '12px' }}>تاريخ: {filterDeliveryDate}</span>}
            </div>
          )}

          {/* واجهة إدارة الأعمدة */}
          {showColumnSelector && (
            <div style={{
              background: 'white',
              padding: '20px',
              borderRadius: '15px',
              marginBottom: '25px',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              border: '2px solid #8e44ad'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '20px'
              }}>
                <h3 style={{ color: '#8e44ad', margin: 0 }}>📋 إدارة عرض الأعمدة</h3>
                <div style={{ display: 'flex', gap: '10px' }}>
                  <button
                    onClick={showAllColumns}
                    style={{
                      padding: '8px 16px',
                      background: '#27ae60',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    ✅ إظهار الكل
                  </button>
                  <button
                    onClick={hideAllColumns}
                    style={{
                      padding: '8px 16px',
                      background: '#e74c3c',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    ❌ إخفاء الكل
                  </button>
                  <button
                    onClick={() => setShowColumnSelector(false)}
                    style={{
                      padding: '8px 16px',
                      background: '#95a5a6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    ✖️ إغلاق
                  </button>
                </div>
              </div>
              
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '15px'
              }}>
                {Object.entries(columnDefinitions).map(([key, definition]) => (
                  <label
                    key={key}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '10px',
                      padding: '10px',
                      background: visibleColumns[key] ? '#e8f5e8' : '#f8f9fa',
                      border: `2px solid ${visibleColumns[key] ? '#27ae60' : '#e0e0e0'}`,
                      borderRadius: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={visibleColumns[key]}
                      onChange={() => toggleColumn(key)}
                      style={{
                        width: '18px',
                        height: '18px',
                        cursor: 'pointer'
                      }}
                    />
                    <span style={{
                      fontSize: '14px',
                      fontWeight: visibleColumns[key] ? 'bold' : 'normal',
                      color: visibleColumns[key] ? '#27ae60' : '#2c3e50'
                    }}>
                      {definition.label}
                    </span>
                  </label>
                ))}
              </div>
              
              <div style={{
                marginTop: '20px',
                padding: '15px',
                background: '#f8f9fa',
                borderRadius: '8px',
                fontSize: '14px',
                color: '#6c757d'
              }}>
                <strong>📊 إحصائيات الأعمدة:</strong>
                <div style={{ marginTop: '8px' }}>
                  • الأعمدة المعروضة: {Object.values(visibleColumns).filter(Boolean).length} من {Object.keys(columnDefinitions).length}
                </div>
                <div style={{ marginTop: '5px' }}>
                  • العرض التقديري للجدول: {Object.entries(visibleColumns).filter(([_, visible]) => visible).reduce((total, [key, _]) => total + parseInt(columnDefinitions[key].width), 0)}px
                </div>
              </div>
            </div>
          )}

          {/* Filters */}
          <div style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            marginBottom: '25px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            display: 'flex',
            gap: '20px',
            flexWrap: 'wrap',
            alignItems: 'center'
          }}>
            <div style={{ flex: 1, minWidth: '200px' }}>
              <input
                type="text"
                placeholder="🔍 البحث في العملاء..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '14px',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '150px'
              }}
            >
              <option value="all">جميع أنواع الفيزا</option>
              <option value="work">فيزة عمل</option>
              <option value="work_temporary">فيزة عمل مؤقتة</option>
              <option value="family_visit">فيزة زيارة عائلية</option>
              <option value="tourist">فيزة سياحية</option>
              <option value="business">فيزة تجارية</option>
              <option value="study">فيزة دراسة</option>
              <option value="transit">فيزة ترانزيت</option>
              <option value="medical">فيزة علاج</option>
              <option value="diplomatic">فيزة دبلوماسية</option>
              <option value="official">فيزة رسمية</option>
              <option value="other">أخرى</option>
            </select>
            <select
              value={filterVisaCategory}
              onChange={(e) => setFilterVisaCategory(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '150px'
              }}
            >
              <option value="all">جميع أنواع التأشيرة</option>
              <option value="individual">فردي</option>
              <option value="normal">عادي</option>
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '150px'
              }}
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
              <option value="blocked">محظور</option>
              <option value="vip">مميز</option>
            </select>

            {/* الفلاتر الإضافية */}
            <select
              value={filterAgent}
              onChange={(e) => setFilterAgent(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '150px'
              }}
            >
              <option value="">جميع الوكلاء</option>
              {agentsList.map(agent => (
                <option key={agent.id} value={agent.agentName}>
                  {agent.agentName} ({agent.agentCode})
                </option>
              ))}
            </select>
            
            <input
              type="text"
              placeholder="📍 مكان التسليم..."
              value={filterDeliveryLocation}
              onChange={(e) => setFilterDeliveryLocation(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '150px'
              }}
            />
            
            <select
              value={filterTransactionStatus}
              onChange={(e) => setFilterTransactionStatus(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '180px'
              }}
            >
              <option value="all">جميع حالات المعاملة</option>
              <option value="office_preparation">قيد التجهيز بالمكتب</option>
              <option value="embassy_execution">قيد التنفيذ بالسفارة</option>
              <option value="embassy_stamped">مؤشر في السفارة</option>
              <option value="office_stamped">مؤشر في المكتب</option>
              <option value="delivered_stamped">مسلم للعميل مؤشر</option>
              <option value="delivered_unstamped">مسلم للعميل غير مؤشر</option>
              <option value="returned">مرجوع</option>
            </select>
            
            <input
              type="date"
              placeholder="📅 تاريخ التسليم"
              value={filterDeliveryDate}
              onChange={(e) => setFilterDeliveryDate(e.target.value)}
              style={{
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                minWidth: '150px'
              }}
            />

            {/* زر مسح الفلاتر */}
            <button
              onClick={() => {
                setSearchTerm('');
                setFilterType('all');
                setFilterVisaCategory('all');
                setFilterStatus('all');
                setFilterAgent('');
                setFilterDeliveryLocation('');
                setFilterTransactionStatus('all');
                setFilterDeliveryDate('');
              }}
              style={{
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              🗑️ مسح الفلاتر
            </button>

            {/* زر إدارة الأعمدة */}
            <button
              onClick={() => setShowColumnSelector(!showColumnSelector)}
              style={{
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              📋 إدارة الأعمدة
            </button>

            {/* أزرار الطباعة والتصدير والاستيراد */}
            <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
              <button
                onClick={handlePrint}
                style={{
                  padding: '12px 20px',
                  background: 'linear-gradient(135deg, #27ae60 0%, #2ecc71 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                🖨️ طباعة
              </button>
              
              <button
                onClick={handleExportExcel}
                style={{
                  padding: '12px 20px',
                  background: 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                📊 تصدير Excel
              </button>
              
              <label style={{
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                📥 استيراد
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleImport}
                  style={{ display: 'none' }}
                />
              </label>
            </div>
          </div>

          {/* Customers Table */}
          <div style={{
            background: 'white',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            overflow: 'hidden'
          }}>
            <div style={{ overflowX: 'auto' }}>
            <table style={{
                width: '100%',
                minWidth: `${Object.entries(visibleColumns).filter(([_, visible]) => visible).reduce((total, [key, _]) => total + parseInt(columnDefinitions[key].width), 0)}px`,
                borderCollapse: 'collapse',
                fontSize: '12px',
                border: '1px solid #e0e0e0'
              }}>
                <thead>
                  <tr style={{ background: '#f8f9fa' }}>
                    {getVisibleColumns().map(columnKey => (
                      <th 
                        key={columnKey}
                        style={{ 
                          padding: '10px', 
                          textAlign: columnKey === 'customerName' ? 'right' : 'center', 
                          fontWeight: 'bold', 
                          fontSize: '12px',
                          minWidth: columnDefinitions[columnKey].width,
                          maxWidth: columnDefinitions[columnKey].width
                        }}
                      >
                        {columnDefinitions[columnKey].label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {filteredCustomers.map((customer) => (
                    <tr key={customer.id} style={{
                      borderBottom: '1px solid #dee2e6',
                      transition: 'background-color 0.3s ease'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                      {getVisibleColumns().map(columnKey => (
                        <td 
                          key={columnKey}
                          style={{ 
                            padding: '8px', 
                            fontSize: '11px', 
                            textAlign: columnKey === 'customerName' ? 'right' : 'center',
                            minWidth: columnDefinitions[columnKey].width,
                            maxWidth: columnDefinitions[columnKey].width
                          }}
                        >
                          {renderColumnValue(customer, columnKey)}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* إحصائيات الجدول */}
            <div style={{
              padding: '15px',
              background: '#f8f9fa',
              borderTop: '1px solid #e0e0e0',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              fontSize: '14px',
              color: '#2c3e50'
            }}>
              <div>
                <strong>إجمالي العملاء المعروضين: {filteredCustomers.length}</strong>
              </div>
              <div style={{ display: 'flex', gap: '20px' }}>
                <span>💰 إجمالي رسوم المعاملات: {filteredCustomers.reduce((sum, c) => sum + (c.transactionFeesPaid || 0), 0).toLocaleString()}</span>
                <span>🛂 إجمالي رسوم الفيزا: {filteredCustomers.reduce((sum, c) => sum + (c.visaFeesPaid || 0), 0).toLocaleString()}</span>
                <span>📊 المعاملات المكتملة: {filteredCustomers.filter(c => c.transactionStatus === 'delivered_stamped' || c.transactionStatus === 'delivered_unstamped').length}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Customer Tab */}
      {activeTab === 'add' && (
        <div style={{
          background: 'white',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          padding: '30px'
        }}>
          <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>➕ إضافة عميل جديد</h2>
          
          <form onSubmit={handleAddCustomer}>
            {/* Basic Information */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#3498db', marginBottom: '20px', borderBottom: '2px solid #3498db20', paddingBottom: '10px' }}>
                📝 المعلومات الأساسية
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                  <input
                    type="text"
                    value={newCustomer.customerName}
                    onChange={(e) => setNewCustomer({...newCustomer, customerName: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الجوال</label>
                  <input
                    type="tel"
                    value={newCustomer.phone}
                    onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الجواز</label>
                  <input
                    type="text"
                    value={newCustomer.passportNumber}
                    onChange={(e) => setNewCustomer({...newCustomer, passportNumber: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المهنة</label>
                  <input
                    type="text"
                    value={newCustomer.profession}
                    onChange={(e) => setNewCustomer({...newCustomer, profession: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                <input
                  type="email"
                  value={newCustomer.email}
                  onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العنوان</label>
                <textarea
                  value={newCustomer.address}
                  onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>
            </div>

            {/* Visa and Transaction Information */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#27ae60', marginBottom: '20px', borderBottom: '2px solid #27ae6020', paddingBottom: '10px' }}>
                🛂 معلومات التأشيرة والمعاملة
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم الوكيل</label>
                  <AgentSelector
                    value={newCustomer.agentName}
                    onChange={(e) => {
                      const selectedAgent = agentsService.getAgentByName(e.target.value);
                      setNewCustomer({
                        ...newCustomer,
                        agentName: e.target.value,
                        // تحديث مكتب التفويض تلقائياً حسب الوكيل المختار
                        authorizationOffice: selectedAgent ? selectedAgent.office : newCustomer.authorizationOffice
                      });
                    }}
                    disabled={selectedVisa}
                    showActiveOnly={true}
                    onAgentSelect={(agent) => {
                      // تحديث إضافي عند اختيار الوكيل
                      setNewCustomer(prev => ({
                        ...prev,
                        authorizationOffice: agent.office
                      }));
                    }}
                  />
                  {selectedVisa && (
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                      🔒 تم تحديد الوكيل تلقائياً من التأشيرة المختارة
                    </div>
                  )}
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مكتب التفويض</label>
                  <input
                    type="text"
                    value={newCustomer.authorizationOffice}
                    onChange={(e) => setNewCustomer({...newCustomer, authorizationOffice: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مكان تسليم المعاملة</label>
                  <input
                    type="text"
                    value={newCustomer.deliveryLocation}
                    onChange={(e) => setNewCustomer({...newCustomer, deliveryLocation: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الطلب</label>
                  <input
                    type="text"
                    value={newCustomer.requestNumber}
                    onChange={(e) => setNewCustomer({...newCustomer, requestNumber: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الفيزا</label>
                  <select
                    value={newCustomer.visaType}
                    onChange={(e) => setNewCustomer({...newCustomer, visaType: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="">اختر نوع الفيزا</option>
                    <option value="work">فيزة عمل</option>
                    <option value="work_temporary">فيزة عمل مؤقتة</option>
                    <option value="family_visit">فيزة زيارة عائلية</option>
                    <option value="tourist">فيزة سياحية</option>
                    <option value="business">فيزة تجارية</option>
                    <option value="study">فيزة دراسة</option>
                    <option value="transit">فيزة ترانزيت</option>
                    <option value="medical">فيزة علاج</option>
                    <option value="diplomatic">فيزة دبلوماسية</option>
                    <option value="official">فيزة رسمية</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع التأشيرة</label>
                  <select
                    value={newCustomer.visaCategory}
                    onChange={(e) => setNewCustomer({...newCustomer, visaCategory: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="normal">عادي</option>
                    <option value="individual">فردي</option>
                  </select>
                </div>
              </div>

              {/* ربط مع مخزون التأشيرات */}
              <div style={{ marginBottom: '20px', padding: '20px', background: '#f8f9fa', borderRadius: '10px', border: '2px solid #e9ecef' }}>
                <h4 style={{ color: '#495057', marginBottom: '15px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  📋 ربط مع مخزون التأشيرات
                </h4>
                
                {selectedVisa ? (
                  <div style={{ 
                    background: 'white', 
                    padding: '15px', 
                    borderRadius: '8px',
                    border: '2px solid #28a745',
                    marginBottom: '15px'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                      <h5 style={{ color: '#28a745', margin: 0 }}>✅ تم اختيار التأشيرة</h5>
                      <button
                        type="button"
                        onClick={handleClearVisa}
                        style={{
                          background: '#dc3545',
                          color: 'white',
                          border: 'none',
                          padding: '5px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        ❌ إلغاء
                      </button>
                    </div>
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px', fontSize: '14px' }}>
                      <div><strong>رقم التأشيرة:</strong> {selectedVisa.visaNumber}</div>
                      <div><strong>الدولة:</strong> {selectedVisa.country}</div>
                      <div><strong>رقم الصادر:</strong> {selectedVisa.issuerNumber}</div>
                      <div><strong>رقم السجل:</strong> {selectedVisa.registrationNumber}</div>
                      <div><strong>الشركة:</strong> {selectedVisa.companyName}</div>
                      <div><strong>الوكيل:</strong> {selectedVisa.agentName}</div>
                    </div>
                  </div>
                ) : (
                  <button
                    type="button"
                    onClick={() => setShowVisaSelector(true)}
                    style={{
                      background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                      color: 'white',
                      border: 'none',
                      padding: '12px 20px',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: 'bold',
                      width: '100%'
                    }}
                  >
                    🔍 اختيار تأشيرة من المخزون
                  </button>
                )}
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الصادر</label>
                  <input
                    type="text"
                    value={newCustomer.issuedNumber}
                    onChange={(e) => setNewCustomer({...newCustomer, issuedNumber: e.target.value})}
                    readOnly={selectedVisa}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      background: selectedVisa ? '#f8f9fa' : 'white',
                      color: selectedVisa ? '#6c757d' : 'black'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم السجل</label>
                  <input
                    type="text"
                    value={newCustomer.registryNumber}
                    onChange={(e) => setNewCustomer({...newCustomer, registryNumber: e.target.value})}
                    readOnly={selectedVisa}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      background: selectedVisa ? '#f8f9fa' : 'white',
                      color: selectedVisa ? '#6c757d' : 'black'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم الشركة</label>
                  <input
                    type="text"
                    value={newCustomer.companyName}
                    onChange={(e) => setNewCustomer({...newCustomer, companyName: e.target.value})}
                    readOnly={selectedVisa}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      background: selectedVisa ? '#f8f9fa' : 'white',
                      color: selectedVisa ? '#6c757d' : 'black'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Important Dates */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#e74c3c', marginBottom: '20px', borderBottom: '2px solid #e74c3c20', paddingBottom: '10px' }}>
                📅 التواريخ المهمة
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ التسليم</label>
                  <input
                    type="date"
                    value={newCustomer.deliveryDate}
                    onChange={(e) => setNewCustomer({...newCustomer, deliveryDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الترحيل</label>
                  <input
                    type="date"
                    value={newCustomer.deportationDate}
                    onChange={(e) => setNewCustomer({...newCustomer, deportationDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الوصول من السفارة</label>
                  <input
                    type="date"
                    value={newCustomer.embassyArrivalDate}
                    onChange={(e) => setNewCustomer({...newCustomer, embassyArrivalDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ التسليم للعميل</label>
                  <input
                    type="date"
                    value={newCustomer.customerDeliveryDate}
                    onChange={(e) => setNewCustomer({...newCustomer, customerDeliveryDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Financial Information */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#9b59b6', marginBottom: '20px', borderBottom: '2px solid #9b59b620', paddingBottom: '10px' }}>
                💰 المعلومات المالية
              </h3>
              
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع العملة</label>
                <select
                  value={newCustomer.currency}
                  onChange={(e) => setNewCustomer({...newCustomer, currency: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="YER">ريال يمني</option>
                  <option value="SAR">ريال سعودي</option>
                  <option value="USD">دولار أمريكي</option>
                </select>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>دفع رسوم المعاملة</label>
                  <input
                    type="number"
                    value={newCustomer.transactionFeesPaid}
                    onChange={(e) => setNewCustomer({...newCustomer, transactionFeesPaid: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.01"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المتبقي من رسوم المعاملة</label>
                  <input
                    type="number"
                    value={newCustomer.transactionFeesRemaining}
                    onChange={(e) => setNewCustomer({...newCustomer, transactionFeesRemaining: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.01"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>دفع رسوم الفيزا</label>
                  <input
                    type="number"
                    value={newCustomer.visaFeesPaid}
                    onChange={(e) => setNewCustomer({...newCustomer, visaFeesPaid: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.01"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المتبقي من رسوم الفيزا</label>
                  <input
                    type="number"
                    value={newCustomer.visaFeesRemaining}
                    onChange={(e) => setNewCustomer({...newCustomer, visaFeesRemaining: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.01"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Status and Notes */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#1abc9c', marginBottom: '20px', borderBottom: '2px solid #1abc9c20', paddingBottom: '10px' }}>
                📊 الحالة والملاحظات
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>حالة المعاملة</label>
                  <select
                    value={newCustomer.transactionStatus}
                    onChange={(e) => setNewCustomer({...newCustomer, transactionStatus: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="office_preparation">قيد التجهيز بالمكتب</option>
                    <option value="embassy_execution">قيد التنفيذ بالسفارة</option>
                    <option value="embassy_stamped">مؤشر في السفارة</option>
                    <option value="office_stamped">مؤشر في المكتب</option>
                    <option value="delivered_stamped">مسلم للعميل مؤشر</option>
                    <option value="delivered_unstamped">مسلم للعميل غير مؤشر</option>
                    <option value="returned">مرجوع</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>حالة السداد لمكتب الترحيل</label>
                  <select
                    value={newCustomer.paymentStatusToOffice}
                    onChange={(e) => setNewCustomer({...newCustomer, paymentStatusToOffice: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="unpaid">غير مدفوع</option>
                    <option value="partial">مدفوع جزئياً</option>
                    <option value="paid">مدفوع بالكامل</option>
                    <option value="overdue">متأخر</option>
                  </select>
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>حالة التسليم للعميل</label>
                  <select
                    value={newCustomer.customerDeliveryStatus}
                    onChange={(e) => setNewCustomer({...newCustomer, customerDeliveryStatus: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="pending">في الانتظار</option>
                    <option value="ready">جاهز للتسليم</option>
                    <option value="delivered">تم التسليم</option>
                    <option value="delayed">متأخر</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>حالة العميل</label>
                  <select
                    value={newCustomer.customerStatus}
                    onChange={(e) => setNewCustomer({...newCustomer, customerStatus: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="blocked">محظور</option>
                    <option value="vip">مميز</option>
                  </select>
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newCustomer.notes}
                  onChange={(e) => setNewCustomer({...newCustomer, notes: e.target.value})}
                  rows="4"
                  placeholder="أي ملاحظات إضافية عن العميل أو المعاملة..."
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>
            </div>

            {/* Transaction Attachments */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#f39c12', marginBottom: '20px', borderBottom: '2px solid #f39c1220', paddingBottom: '10px' }}>
                📎 مرفقات المعاملة
              </h3>
              
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إرفاق الملفات</label>
                <div style={{
                  border: '2px dashed #e0e0e0',
                  borderRadius: '8px',
                  padding: '30px',
                  textAlign: 'center',
                  background: '#f8f9fa',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = '#f39c12';
                  e.currentTarget.style.background = '#fff3cd';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#e0e0e0';
                  e.currentTarget.style.background = '#f8f9fa';
                }}>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      const currentAttachments = newCustomer.attachments || [];
                      setNewCustomer({...newCustomer, attachments: [...currentAttachments, ...files]});
                    }}
                    style={{ display: 'none' }}
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" style={{ cursor: 'pointer', display: 'block' }}>
                    <div style={{ fontSize: '48px', marginBottom: '15px', color: '#f39c12' }}>📁</div>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px', color: '#2c3e50' }}>
                      اضغط لاختيار الملفات أو اسحبها هنا
                    </div>
                    <div style={{ fontSize: '14px', color: '#7f8c8d' }}>
                      يمكنك رفع ملفات PDF, Word, أو صور (JPG, PNG)
                    </div>
                  </label>
                </div>
                
                {newCustomer.attachments && newCustomer.attachments.length > 0 && (
                  <div style={{ marginTop: '15px' }}>
                    <h4 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '14px' }}>الملفات المحددة:</h4>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
                      {Array.from(newCustomer.attachments).map((file, index) => (
                        <div key={index} style={{
                          background: '#e8f5e8',
                          padding: '8px 12px',
                          borderRadius: '20px',
                          fontSize: '12px',
                          color: '#27ae60',
                          border: '1px solid #27ae60',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '5px'
                        }}>
                          <span>📄</span>
                          <span>{file.name}</span>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            style={{
                              background: 'none',
                              border: 'none',
                              color: '#e74c3c',
                              cursor: 'pointer',
                              fontSize: '12px',
                              marginLeft: '5px'
                            }}
                          >
                            ✕
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
              <button
                type="button"
                onClick={() => setActiveTab('list')}
                style={{
                  padding: '12px 25px',
                  border: '2px solid #e0e0e0',
                  background: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إلغاء
              </button>
              <button
                type="submit"
                style={{
                  padding: '12px 25px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                إضافة العميل
              </button>
            </div>
          </form>
        </div>
      )}

      {/* نافذة اختيار التأشيرة */}
      {showVisaSelector && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '15px',
            padding: '30px',
            maxWidth: '900px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '25px' }}>
              <h2 style={{ color: '#2c3e50', margin: 0 }}>📋 اختيار تأشيرة من المخزون</h2>
              <button
                onClick={() => setShowVisaSelector(false)}
                style={{
                  background: '#e74c3c',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '35px',
                  height: '35px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <p style={{ color: '#7f8c8d', margin: 0 }}>
                اختر تأشيرة متاحة من المخزون لربطها بالعميل الجديد
              </p>
            </div>

            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ background: '#f8f9fa' }}>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>رقم التأشيرة</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الدولة</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>النوع</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>المتبقي</th>
                    <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الشركة</th>
                    <th style={{ padding: '12px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>اختيار</th>
                  </tr>
                </thead>
                <tbody>
                  {visaInventory.filter(visa => visa.status === 'available' && visa.remainingCount > 0).map((visa) => (
                    <tr key={visa.id} style={{ borderBottom: '1px solid #dee2e6' }}>
                      <td style={{ padding: '12px' }}>
                        <div style={{ fontWeight: 'bold', color: '#2c3e50' }}>{visa.visaNumber}</div>
                        <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                          {visa.issueDate}
                        </div>
                      </td>
                      <td style={{ padding: '12px' }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <span style={{ fontSize: '20px' }}>
                            {visa.countryCode === 'AE' ? '🇦🇪' : 
                             visa.countryCode === 'TR' ? '🇹🇷' : 
                             visa.countryCode === 'EG' ? '🇪🇬' : 
                             visa.countryCode === 'IN' ? '🇮🇳' : 
                             visa.countryCode === 'US' ? '🇺🇸' : '🌍'}
                          </span>
                          <span>{visa.country}</span>
                        </div>
                      </td>
                      <td style={{ padding: '12px' }}>
                        <div style={{ fontWeight: 'bold' }}>
                          {visa.visaType === 'individual_yearly' ? 'فردي سنة' : 'عادي 3 شهور'}
                        </div>
                        <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                          {visa.profession}
                        </div>
                      </td>
                      <td style={{ padding: '12px' }}>
                        <div style={{ color: '#27ae60', fontWeight: 'bold', fontSize: '16px' }}>
                          {visa.remainingCount}
                        </div>
                        <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                          من {visa.totalCount}
                        </div>
                      </td>
                      <td style={{ padding: '12px' }}>
                        <div style={{ fontWeight: 'bold' }}>{visa.companyName}</div>
                        <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                          {visa.agentName}
                        </div>
                      </td>
                      <td style={{ padding: '12px', textAlign: 'center' }}>
                        <button
                          onClick={() => handleSelectVisa(visa)}
                          style={{
                            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                            color: 'white',
                            border: 'none',
                            padding: '8px 15px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}
                        >
                          ✅ اختيار
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {visaInventory.filter(visa => visa.status === 'available' && visa.remainingCount > 0).length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '40px',
                color: '#7f8c8d'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '15px' }}>📭</div>
                <h3>لا توجد تأشيرات متاحة</h3>
                <p>جميع التأشيرات في المخزون مستخدمة أو غير متاحة</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomersPage;