/* 🔍 أنماط البحث السريع */

/* 🌫️ خلفية المودال */
.quick-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  animation: fadeIn 0.2s ease-out;
}

/* 📦 مودال البحث */
.quick-search-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 🔍 حاوية حقل البحث */
.quick-search-input-container {
  display: flex;
  align-items: center;
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

.quick-search-icon {
  font-size: 1.25rem;
  color: var(--neutral-500);
  margin-left: var(--space-3);
}

/* 📝 حقل البحث */
.quick-search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--neutral-800);
  outline: none;
  padding: var(--space-2) 0;
  font-family: inherit;
}

.quick-search-input::placeholder {
  color: var(--neutral-400);
  font-weight: 400;
}

/* ⌨️ اختصار الإغلاق */
.quick-search-shortcut {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.quick-search-shortcut kbd {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 var(--space-2);
  background: var(--neutral-200);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--neutral-600);
  text-transform: uppercase;
}

/* 📋 منطقة النتائج */
.quick-search-results {
  max-height: 50vh;
  overflow-y: auto;
  padding: var(--space-2);
}

/* 📄 قسم النتائج */
.quick-search-section {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--glass-border);
}

.quick-search-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 🎯 عنصر النتيجة */
.quick-search-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  margin-bottom: var(--space-1);
}

.quick-search-item:hover,
.quick-search-item.selected {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(-2px);
}

.quick-search-item.selected {
  background: rgba(59, 130, 246, 0.15);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 🎭 أيقونة العنصر */
.quick-search-item-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  flex-shrink: 0;
}

/* 📝 محتوى العنصر */
.quick-search-item-content {
  flex: 1;
  min-width: 0;
}

.quick-search-item-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
  line-height: 1.4;
}

.quick-search-item-description {
  font-size: 0.875rem;
  color: var(--neutral-500);
  line-height: 1.4;
  margin-bottom: var(--space-1);
}

.quick-search-item-category {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
  color: var(--neutral-400);
}

.category-icon {
  font-size: 0.875rem;
}

.category-name {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 🎬 إجراء العنصر */
.quick-search-item-action {
  flex-shrink: 0;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.quick-search-item:hover .quick-search-item-action,
.quick-search-item.selected .quick-search-item-action {
  opacity: 1;
}

.quick-search-item-action kbd {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.75rem;
  font-weight: 600;
}

/* 🎨 تمييز النص المطابق */
.search-highlight {
  background: rgba(245, 158, 11, 0.3);
  color: var(--warning-700);
  padding: 1px 2px;
  border-radius: var(--radius-sm);
  font-weight: 600;
}

/* 🚫 حالة فارغة */
.quick-search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.3;
}

.empty-message {
  font-size: 1.125rem;
  color: var(--neutral-500);
  font-weight: 500;
}

/* 🔗 تذييل المودال */
.quick-search-footer {
  padding: var(--space-3) var(--space-5);
  border-top: 1px solid var(--glass-border);
  background: rgba(255, 255, 255, 0.03);
}

.quick-search-tips {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.tip {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
  color: var(--neutral-500);
}

.tip kbd {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 var(--space-1);
  background: var(--neutral-200);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.625rem;
  font-weight: 600;
  color: var(--neutral-600);
  margin: 0 1px;
}

/* 🔘 زر البحث السريع */
.quick-search-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  color: var(--neutral-600);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.quick-search-button:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.quick-search-button-icon {
  font-size: 1rem;
}

.quick-search-button-text {
  flex: 1;
  white-space: nowrap;
}

.quick-search-button-shortcut {
  display: flex;
  align-items: center;
  gap: 2px;
  opacity: 0.7;
}

.quick-search-button-shortcut kbd {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-sm);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.5rem;
  font-weight: 600;
  color: currentColor;
}

/* 📊 إحصائيات البحث */
.search-stats {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-3);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.search-stats-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.search-stats-label {
  font-size: 0.75rem;
  color: var(--neutral-500);
  font-weight: 500;
}

.search-stats-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--neutral-800);
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .quick-search-input,
.dark-mode .quick-search-input {
  color: var(--neutral-200);
}

[data-theme="dark"] .quick-search-input::placeholder,
.dark-mode .quick-search-input::placeholder {
  color: var(--neutral-500);
}

[data-theme="dark"] .quick-search-item-title,
.dark-mode .quick-search-item-title {
  color: var(--neutral-200);
}

[data-theme="dark"] .quick-search-item-description,
.dark-mode .quick-search-item-description {
  color: var(--neutral-400);
}

[data-theme="dark"] .quick-search-shortcut kbd,
[data-theme="dark"] .tip kbd,
.dark-mode .quick-search-shortcut kbd,
.dark-mode .tip kbd {
  background: var(--neutral-700);
  border-color: var(--neutral-600);
  color: var(--neutral-200);
}

[data-theme="dark"] .search-stats-value,
.dark-mode .search-stats-value {
  color: var(--neutral-200);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .quick-search-overlay {
    padding-top: 5vh;
  }
  
  .quick-search-modal {
    width: 95%;
    max-height: 85vh;
  }
  
  .quick-search-input-container {
    padding: var(--space-3) var(--space-4);
  }
  
  .quick-search-input {
    font-size: 1rem;
  }
  
  .quick-search-item {
    padding: var(--space-3);
  }
  
  .quick-search-tips {
    gap: var(--space-2);
  }
  
  .quick-search-button-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .quick-search-overlay {
    padding-top: 2vh;
  }
  
  .quick-search-modal {
    width: 98%;
    max-height: 90vh;
  }
  
  .quick-search-item-category {
    display: none;
  }
  
  .quick-search-tips {
    flex-direction: column;
    gap: var(--space-1);
  }
}

/* 🎬 حركات خاصة */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .quick-search-overlay,
  .quick-search-modal,
  .quick-search-item,
  .quick-search-button {
    animation: none;
    transition: none;
  }
  
  .quick-search-item:hover,
  .quick-search-button:hover {
    transform: none;
  }
}

/* 🎯 تحسينات إمكانية الوصول */
.quick-search-modal:focus-within {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.quick-search-item:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
