import React, { useState, useRef, useEffect } from 'react';
import './ModernForm.css';

// Modern Input Component
export const ModernInput = ({
  type = 'text',
  label,
  placeholder,
  value,
  onChange,
  error,
  success,
  disabled = false,
  required = false,
  icon,
  iconPosition = 'right',
  size = 'md',
  variant = 'default',
  className = '',
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);

  useEffect(() => {
    setHasValue(!!value);
  }, [value]);

  const inputClasses = [
    'modern-input',
    `modern-input--${size}`,
    `modern-input--${variant}`,
    focused && 'modern-input--focused',
    hasValue && 'modern-input--has-value',
    error && 'modern-input--error',
    success && 'modern-input--success',
    disabled && 'modern-input--disabled',
    icon && 'modern-input--with-icon',
    icon && `modern-input--icon-${iconPosition}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="modern-input-wrapper">
      {label && (
        <label className="modern-input-label">
          {label}
          {required && <span className="modern-input-required">*</span>}
        </label>
      )}
      
      <div className="modern-input-container">
        {icon && iconPosition === 'right' && (
          <span className="modern-input-icon modern-input-icon--right">
            {icon}
          </span>
        )}
        
        <input
          type={type}
          className={inputClasses}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          disabled={disabled}
          required={required}
          {...props}
        />
        
        {icon && iconPosition === 'left' && (
          <span className="modern-input-icon modern-input-icon--left">
            {icon}
          </span>
        )}
      </div>
      
      {error && (
        <span className="modern-input-message modern-input-message--error">
          {error}
        </span>
      )}
      
      {success && (
        <span className="modern-input-message modern-input-message--success">
          {success}
        </span>
      )}
    </div>
  );
};

// Modern Textarea Component
export const ModernTextarea = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  success,
  disabled = false,
  required = false,
  rows = 4,
  resize = true,
  size = 'md',
  className = '',
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);

  useEffect(() => {
    setHasValue(!!value);
  }, [value]);

  const textareaClasses = [
    'modern-textarea',
    `modern-textarea--${size}`,
    focused && 'modern-textarea--focused',
    hasValue && 'modern-textarea--has-value',
    error && 'modern-textarea--error',
    success && 'modern-textarea--success',
    disabled && 'modern-textarea--disabled',
    !resize && 'modern-textarea--no-resize',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="modern-textarea-wrapper">
      {label && (
        <label className="modern-textarea-label">
          {label}
          {required && <span className="modern-textarea-required">*</span>}
        </label>
      )}
      
      <textarea
        className={textareaClasses}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        disabled={disabled}
        required={required}
        rows={rows}
        {...props}
      />
      
      {error && (
        <span className="modern-textarea-message modern-textarea-message--error">
          {error}
        </span>
      )}
      
      {success && (
        <span className="modern-textarea-message modern-textarea-message--success">
          {success}
        </span>
      )}
    </div>
  );
};

// Modern Select Component
export const ModernSelect = ({
  label,
  placeholder = 'اختر خياراً',
  value,
  onChange,
  options = [],
  error,
  success,
  disabled = false,
  required = false,
  searchable = false,
  multiple = false,
  size = 'md',
  className = '',
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focused, setFocused] = useState(false);
  const selectRef = useRef(null);

  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false);
        setFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selectClasses = [
    'modern-select',
    `modern-select--${size}`,
    isOpen && 'modern-select--open',
    focused && 'modern-select--focused',
    value && 'modern-select--has-value',
    error && 'modern-select--error',
    success && 'modern-select--success',
    disabled && 'modern-select--disabled',
    className
  ].filter(Boolean).join(' ');

  const handleOptionClick = (optionValue) => {
    onChange?.(optionValue);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className="modern-select-wrapper" ref={selectRef}>
      {label && (
        <label className="modern-select-label">
          {label}
          {required && <span className="modern-select-required">*</span>}
        </label>
      )}
      
      <div className={selectClasses}>
        <div
          className="modern-select-trigger"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          onFocus={() => setFocused(true)}
          tabIndex={0}
        >
          <span className="modern-select-value">
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <span className={`modern-select-arrow ${isOpen ? 'modern-select-arrow--up' : ''}`}>
            ▼
          </span>
        </div>
        
        {isOpen && (
          <div className="modern-select-dropdown">
            {searchable && (
              <div className="modern-select-search">
                <input
                  type="text"
                  placeholder="البحث..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="modern-select-search-input"
                />
              </div>
            )}
            
            <div className="modern-select-options">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`modern-select-option ${
                      option.value === value ? 'modern-select-option--selected' : ''
                    }`}
                    onClick={() => handleOptionClick(option.value)}
                  >
                    {option.label}
                  </div>
                ))
              ) : (
                <div className="modern-select-no-options">
                  لا توجد خيارات متاحة
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      
      {error && (
        <span className="modern-select-message modern-select-message--error">
          {error}
        </span>
      )}
      
      {success && (
        <span className="modern-select-message modern-select-message--success">
          {success}
        </span>
      )}
    </div>
  );
};

// Modern Checkbox Component
export const ModernCheckbox = ({
  label,
  checked,
  onChange,
  disabled = false,
  size = 'md',
  variant = 'default',
  className = '',
  ...props
}) => {
  const checkboxClasses = [
    'modern-checkbox',
    `modern-checkbox--${size}`,
    `modern-checkbox--${variant}`,
    checked && 'modern-checkbox--checked',
    disabled && 'modern-checkbox--disabled',
    className
  ].filter(Boolean).join(' ');

  return (
    <label className={checkboxClasses}>
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className="modern-checkbox-input"
        {...props}
      />
      <span className="modern-checkbox-checkmark">
        {checked && <span className="modern-checkbox-check">✓</span>}
      </span>
      {label && <span className="modern-checkbox-label">{label}</span>}
    </label>
  );
};

// Modern Radio Component
export const ModernRadio = ({
  label,
  name,
  value,
  checked,
  onChange,
  disabled = false,
  size = 'md',
  className = '',
  ...props
}) => {
  const radioClasses = [
    'modern-radio',
    `modern-radio--${size}`,
    checked && 'modern-radio--checked',
    disabled && 'modern-radio--disabled',
    className
  ].filter(Boolean).join(' ');

  return (
    <label className={radioClasses}>
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className="modern-radio-input"
        {...props}
      />
      <span className="modern-radio-circle">
        {checked && <span className="modern-radio-dot"></span>}
      </span>
      {label && <span className="modern-radio-label">{label}</span>}
    </label>
  );
};

// Modern Form Component
export const ModernForm = ({
  children,
  onSubmit,
  className = '',
  variant = 'default',
  size = 'md',
  ...props
}) => {
  const formClasses = [
    'modern-form',
    `modern-form--${variant}`,
    `modern-form--${size}`,
    className
  ].filter(Boolean).join(' ');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit?.(e);
  };

  return (
    <form className={formClasses} onSubmit={handleSubmit} {...props}>
      {children}
    </form>
  );
};

// Form Group Component
export const FormGroup = ({
  children,
  className = '',
  direction = 'column',
  gap = 'md',
  ...props
}) => {
  const groupClasses = [
    'form-group',
    `form-group--${direction}`,
    `form-group--gap-${gap}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={groupClasses} {...props}>
      {children}
    </div>
  );
};

// Form Row Component
export const FormRow = ({
  children,
  className = '',
  cols = 'auto',
  gap = 'md',
  ...props
}) => {
  const rowClasses = [
    'form-row',
    `form-row--cols-${cols}`,
    `form-row--gap-${gap}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={rowClasses} {...props}>
      {children}
    </div>
  );
};

export default ModernForm;