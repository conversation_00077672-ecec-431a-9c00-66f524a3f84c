# ===================================================================
# 🛑 سكربت إيقاف نظام شراء للسفر والسياحة
# System Stop Script for Sharau Travel System
# ===================================================================

# إعداد الألوان
$Host.UI.RawUI.BackgroundColor = "DarkRed"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

Write-Host "
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║    🛑  إيقاف نظام شراء للسفر والسياحة  🛑                      ║
║         Stop Sharau Travel System                               ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
" -ForegroundColor Red

# متغيرات النظام
$PROJECT_PATH = "c:\Users\<USER>\Desktop\sharaubtravelsoft"
$PID_FILE = "$PROJECT_PATH\system.pid"
$LOG_FILE = "$PROJECT_PATH\system.log"

# دالة كتابة السجلات
function Write-Log {
    param($Message, $Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if (Test-Path $PROJECT_PATH) {
        Add-Content -Path $LOG_FILE -Value $logEntry
    }
    
    switch ($Level) {
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "⚠️ $Message" -ForegroundColor Yellow }
        default { Write-Host "ℹ️ $Message" -ForegroundColor White }
    }
}

# دالة إيقاف العمليات من ملف PID
function Stop-ProcessesFromPID {
    Write-Log "إيقاف العمليات المسجلة..." "INFO"
    
    if (Test-Path $PID_FILE) {
        $pids = Get-Content $PID_FILE
        $stoppedCount = 0
        
        foreach ($pid in $pids) {
            if ($pid -and $pid -ne "") {
                try {
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process) {
                        $processName = $process.ProcessName
                        Stop-Process -Id $pid -Force
                        Write-Log "تم إيقاف العملية: $processName (PID: $pid)" "SUCCESS"
                        $stoppedCount++
                    }
                } catch {
                    Write-Log "لا يمكن إيقاف العملية: $pid" "WARNING"
                }
            }
        }
        
        if ($stoppedCount -gt 0) {
            Write-Log "تم إيقاف $stoppedCount عملية" "SUCCESS"
        } else {
            Write-Log "لم يتم العثور على عمليات نشطة" "INFO"
        }
        
        # حذف ملف PID
        Remove-Item $PID_FILE -Force
        Write-Log "تم حذف ملف PID" "SUCCESS"
    } else {
        Write-Log "ملف PID غير موجود" "WARNING"
    }
}

# دالة إيقاف عمليات Node.js على المنافذ المحددة
function Stop-NodeProcesses {
    Write-Log "إيقاف عمليات Node.js..." "INFO"
    
    $ports = @(3001, 5000)
    $stoppedCount = 0
    
    foreach ($port in $ports) {
        try {
            $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
            foreach ($connection in $connections) {
                $processId = $connection.OwningProcess
                $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                
                if ($process -and $process.ProcessName -eq "node") {
                    Stop-Process -Id $processId -Force
                    Write-Log "تم إيقاف Node.js على المنفذ $port (PID: $processId)" "SUCCESS"
                    $stoppedCount++
                }
            }
        } catch {
            # تجاهل الأخطاء
        }
    }
    
    if ($stoppedCount -gt 0) {
        Write-Log "تم إيقاف $stoppedCount عملية Node.js" "SUCCESS"
    } else {
        Write-Log "لم يتم العثور على عمليات Node.js نشطة" "INFO"
    }
}

# دالة إيقاف جميع عمليات Node.js (خيار قوي)
function Stop-AllNodeProcesses {
    Write-Log "إيقاف جميع عمليات Node.js..." "WARNING"
    
    try {
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        $stoppedCount = 0
        
        foreach ($process in $nodeProcesses) {
            try {
                Stop-Process -Id $process.Id -Force
                Write-Log "تم إيقاف عملية Node.js: $($process.Id)" "SUCCESS"
                $stoppedCount++
            } catch {
                Write-Log "فشل في إيقاف عملية Node.js: $($process.Id)" "ERROR"
            }
        }
        
        if ($stoppedCount -gt 0) {
            Write-Log "تم إيقاف $stoppedCount عملية Node.js" "SUCCESS"
        } else {
            Write-Log "لم يتم العثور على عمليات Node.js" "INFO"
        }
    } catch {
        Write-Log "خطأ في إيقاف عمليات Node.js: $($_.Exception.Message)" "ERROR"
    }
}

# دالة تنظيف الملفات المؤقتة
function Cleanup-TempFiles {
    Write-Log "تنظيف الملفات المؤقتة..." "INFO"
    
    $tempFiles = @(
        "$PROJECT_PATH\*.tmp",
        "$PROJECT_PATH\*.temp",
        "$PROJECT_PATH\frontend\node_modules\.cache",
        "$PROJECT_PATH\backend\node_modules\.cache"
    )
    
    foreach ($pattern in $tempFiles) {
        try {
            $files = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
            foreach ($file in $files) {
                Remove-Item $file.FullName -Recurse -Force -ErrorAction SilentlyContinue
            }
        } catch {
            # تجاهل الأخطاء
        }
    }
    
    Write-Log "تم تنظيف الملفات المؤقتة" "SUCCESS"
}

# دالة فحص حالة النظام
function Check-SystemStatus {
    Write-Log "فحص حالة النظام..." "INFO"
    
    # فحص المنافذ
    $frontendRunning = $false
    $backendRunning = $false
    
    try {
        $frontendConnection = Get-NetTCPConnection -LocalPort 3001 -ErrorAction SilentlyContinue
        if ($frontendConnection) {
            $frontendRunning = $true
        }
    } catch { }
    
    try {
        $backendConnection = Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue
        if ($backendConnection) {
            $backendRunning = $true
        }
    } catch { }
    
    # فحص عمليات Node.js
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    $nodeCount = if ($nodeProcesses) { $nodeProcesses.Count } else { 0 }
    
    Write-Host ""
    Write-Host "📊 حالة النظام:" -ForegroundColor Cyan
    Write-Host "   Frontend (3001): $(if ($frontendRunning) { '🟢 يعمل' } else { '🔴 متوقف' })" -ForegroundColor $(if ($frontendRunning) { 'Green' } else { 'Red' })
    Write-Host "   Backend (5000):  $(if ($backendRunning) { '🟢 يعمل' } else { '🔴 متوقف' })" -ForegroundColor $(if ($backendRunning) { 'Green' } else { 'Red' })
    Write-Host "   عمليات Node.js: $nodeCount" -ForegroundColor White
    Write-Host ""
    
    return @{
        Frontend = $frontendRunning
        Backend = $backendRunning
        NodeProcesses = $nodeCount
    }
}

# دالة عرض القائمة التفاعلية
function Show-Menu {
    Write-Host "🎯 خيارات الإيقاف:" -ForegroundColor Cyan
    Write-Host "   1. إيقاف عادي (موصى به)" -ForegroundColor Green
    Write-Host "   2. إيقاف قوي (جميع عمليات Node.js)" -ForegroundColor Yellow
    Write-Host "   3. فحص الحالة فقط" -ForegroundColor Blue
    Write-Host "   4. تنظيف وإيقاف" -ForegroundColor Magenta
    Write-Host "   5. إلغاء" -ForegroundColor Gray
    Write-Host ""
    
    $choice = Read-Host "اختر رقم الخيار (1-5)"
    return $choice
}

# ===================================================================
# 🚀 تنفيذ إيقاف النظام
# ===================================================================

try {
    Write-Log "بدء إيقاف نظام شراء للسفر والسياحة" "INFO"
    
    # فحص حالة النظام أولاً
    $status = Check-SystemStatus
    
    # إذا لم يكن هناك شيء يعمل
    if (-not $status.Frontend -and -not $status.Backend -and $status.NodeProcesses -eq 0) {
        Write-Host "✅ النظام متوقف مسبقاً" -ForegroundColor Green
        Write-Log "النظام متوقف مسبقاً" "INFO"
        Read-Host "اضغط Enter للخروج"
        exit 0
    }
    
    # عرض القائمة التفاعلية
    $choice = Show-Menu
    
    switch ($choice) {
        "1" {
            Write-Log "تم اختيار الإيقاف العادي" "INFO"
            Stop-ProcessesFromPID
            Stop-NodeProcesses
            Start-Sleep -Seconds 2
        }
        "2" {
            Write-Log "تم اختيار الإيقاف القوي" "WARNING"
            Stop-ProcessesFromPID
            Stop-AllNodeProcesses
            Start-Sleep -Seconds 2
        }
        "3" {
            Write-Log "فحص الحالة فقط" "INFO"
            Check-SystemStatus | Out-Null
            Read-Host "اضغط Enter للخروج"
            exit 0
        }
        "4" {
            Write-Log "تم اختيار التنظيف والإيقاف" "INFO"
            Stop-ProcessesFromPID
            Stop-AllNodeProcesses
            Cleanup-TempFiles
            Start-Sleep -Seconds 2
        }
        "5" {
            Write-Log "تم إلغاء العملية" "INFO"
            Write-Host "❌ تم إلغاء الإيقاف" -ForegroundColor Yellow
            Read-Host "اضغط Enter للخروج"
            exit 0
        }
        default {
            Write-Log "خيار غير صحيح، سيتم الإيقاف العادي" "WARNING"
            Stop-ProcessesFromPID
            Stop-NodeProcesses
            Start-Sleep -Seconds 2
        }
    }
    
    # فحص الحالة النهائية
    Write-Log "فحص الحالة النهائية..." "INFO"
    $finalStatus = Check-SystemStatus
    
    if (-not $finalStatus.Frontend -and -not $finalStatus.Backend -and $finalStatus.NodeProcesses -eq 0) {
        Write-Host ""
        Write-Host "╔══════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
        Write-Host "║                    ✅ تم إيقاف النظام بنجاح! ✅                 ║" -ForegroundColor Green
        Write-Host "╠══════════════════════════════════════════════════════════════════╣" -ForegroundColor Green
        Write-Host "║                                                                  ║" -ForegroundColor Green
        Write-Host "║  🔴 Frontend متوقف                                              ║" -ForegroundColor Green
        Write-Host "║  🔴 Backend متوقف                                               ║" -ForegroundColor Green
        Write-Host "║  🔴 جميع عمليات Node.js متوقفة                                 ║" -ForegroundColor Green
        Write-Host "║                                                                  ║" -ForegroundColor Green
        Write-Host "║  💡 لإعادة تشغيل النظام:                                        ║" -ForegroundColor Green
        Write-Host "║     انقر على أيقونة سطح المكتب                                ║" -ForegroundColor Green
        Write-Host "║     أو شغل: start-sharau-app.ps1                               ║" -ForegroundColor Green
        Write-Host "║                                                                  ║" -ForegroundColor Green
        Write-Host "╚══════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
        Write-Host ""
        Write-Log "تم إيقاف النظام بنجاح" "SUCCESS"
    } else {
        Write-Host ""
        Write-Host "⚠️ قد تكون هناك عمليات لا تزال تعمل" -ForegroundColor Yellow
        Write-Host "💡 جرب الإيقاف القوي إذا لزم الأمر" -ForegroundColor Cyan
        Write-Log "قد تكون هناك عمليات لا تزال تعمل" "WARNING"
    }
    
} catch {
    Write-Log "خطأ في إيقاف النظام: $($_.Exception.Message)" "ERROR"
    Write-Host "❌ حدث خطأ في إيقاف النظام" -ForegroundColor Red
    Write-Host "💡 جرب إعادة تشغيل الكمبيوتر إذا استمرت المشكلة" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 شكراً لاستخدام نظام شراء للسفر والسياحة!" -ForegroundColor Cyan
Read-Host "اضغط Enter للخروج"