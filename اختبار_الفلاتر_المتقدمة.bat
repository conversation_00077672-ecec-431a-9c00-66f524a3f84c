@echo off
chcp 65001 >nul
title اختبار الفلاتر المتقدمة

echo.
echo ========================================
echo    🔍 اختبار الفلاتر المتقدمة
echo      نظام البحث الشامل
echo ========================================
echo.

echo 🌐 فتح النظام...
start http://localhost:3000/customers

echo.
echo 📋 قائمة اختبار الفلاتر الجديدة:
echo.
echo ✅ 1. اختبار الفلاتر الأساسية:
echo    □ البحث العام (🔍)
echo    □ نوع الفيزا (🛂)
echo    □ حالة العميل (👤)
echo.
echo ✅ 2. اختبار الفلاتر الجديدة:
echo    □ اسم الوكيل (🧑‍💼)
echo    □ مكان التسليم (📍)
echo    □ حالة المعاملة (📊)
echo    □ تاريخ التسليم (📅)
echo.
echo ✅ 3. اختبار البحث العام:
echo    □ ابحث باسم عميل موجود
echo    □ ابحث برقم هاتف
echo    □ ابحث برقم جواز
echo    □ ابحث بإيميل
echo    □ تحقق من النتائج الفورية
echo.
echo ✅ 4. اختبار فلتر اسم الوكيل:
echo    □ اكتب اسم وكيل موجود
echo    □ جرب البحث الجزئي (مثل "أحم" لـ "أحمد")
echo    □ تحقق من النتائج المطابقة
echo    □ امسح الحقل وتحقق من العودة للكل
echo.
echo ✅ 5. اختبار فلتر مكان التسليم:
echo    □ اكتب مكان تسليم موجود
echo    □ جرب أماكن مختلفة
echo    □ تحقق من دقة النتائج
echo    □ جرب البحث الجزئي
echo.
echo ✅ 6. اختبار فلتر حالة المعاملة:
echo    □ اختر "قيد التجهيز بالمكتب"
echo    □ اختر "قيد التنفيذ بالسفارة"
echo    □ اختر "مؤشر في السفارة"
echo    □ اختر "مؤشر في المكتب"
echo    □ اختر "مسلم للعميل مؤشر"
echo    □ اختر "مسلم للعميل غير مؤشر"
echo    □ اختر "مرجوع"
echo    □ تحقق من الألوان المطابقة
echo.
echo ✅ 7. اختبار فلتر تاريخ التسليم:
echo    □ اختر تاريخ موجود في البيانات
echo    □ جرب تاريخ غير موجود
echo    □ تحقق من دقة المطابقة
echo    □ امسح التاريخ وتحقق من العودة للكل
echo.
echo ✅ 8. اختبار الفلاتر المتعددة:
echo    □ استخدم البحث + نوع الفيزا
echo    □ استخدم اسم الوكيل + حالة المعاملة
echo    □ استخدم 3 فلاتر معاً
echo    □ استخدم جميع الفلاتر الـ7
echo    □ تحقق من دقة النتائج المركبة
echo.
echo ✅ 9. اختبار مؤشر الفلاتر النشطة:
echo    □ طبق فلتر واحد وراقب ظهور المؤشر الأزرق
echo    □ طبق عدة فلاتر وراقب الشارات الملونة:
echo      • أزرق: البحث العام
echo      • أخضر: نوع الفيزا
echo      • أصفر: حالة العميل
echo      • بنفسجي: اسم الوكيل
echo      • برتقالي: مكان التسليم
echo      • تركوازي: حالة المعاملة
echo      • أحمر: تاريخ التسليم
echo    □ تحقق من النصوص في الشارات
echo.
echo ✅ 10. اختبار زر مسح الفلاتر:
echo    □ طبق عدة فلاتر
echo    □ اضغط زر "🗑️ مسح الفلاتر"
echo    □ تحقق من مسح جميع الفلاتر
echo    □ تحقق من اختفاء مؤشر الفلاتر النشطة
echo    □ تحقق من عودة جميع العملاء للظهور
echo.
echo ========================================
echo.
echo 🎯 سيناريوهات اختبار متقدمة:
echo.
echo 📊 سيناريو 1: البحث عن وكيل محدد
echo    1. اكتب في فلتر "اسم الوكيل": "أحمد"
echo    2. راقب ظهور العملاء المرتبطين بهذا الوكيل
echo    3. أضف فلتر "حالة المعاملة": "مؤشر في السفارة"
echo    4. تحقق من النتائج المركبة
echo.
echo 📊 سيناريو 2: تقرير يومي
echo    1. اختر تاريخ محدد في "تاريخ التسليم"
echo    2. أضف فلتر "حالة المعاملة": "مسلم للعميل مؤشر"
echo    3. راقب المعاملات المكتملة في ذلك اليوم
echo    4. اطبع التقرير
echo.
echo 📊 سيناريو 3: متابعة مكان تسليم
echo    1. اكتب مكان تسليم في الفلتر
echo    2. أضف فلتر نوع الفيزا
echo    3. راقب توزيع المعاملات
echo    4. صدر البيانات للتحليل
echo.
echo 📊 سيناريو 4: بحث شامل
echo    1. ابحث باسم عميل في البحث العام
echo    2. أضف فلتر الوكيل
echo    3. أضف فلتر حالة المعاملة
echo    4. أضف فلتر التاريخ
echo    5. تحقق من دقة النتيجة النهائية
echo.
echo ========================================
echo.
echo 🔍 نقاط التحقق المهمة:
echo.
echo ✅ الواجهة:
echo    □ جميع الفلاتر الـ7 تظهر بوضوح
echo    □ التخطيط منظم ومريح
echo    □ الألوان متناسقة
echo    □ النصوص واضحة ومقروءة
echo.
echo ✅ الوظائف:
echo    □ البحث فوري أثناء الكتابة
echo    □ الفلاتر تعمل بشكل مستقل
echo    □ الفلاتر المتعددة تعمل معاً
echo    □ زر المسح يعمل بشكل صحيح
echo.
echo ✅ المؤشرات:
echo    □ مؤشر الفلاتر النشطة يظهر/يختفي
echo    □ الشارات الملونة صحيحة
echo    □ النصوص في الشارات دقيقة
echo    □ التصميم جذاب ومفيد
echo.
echo ✅ الأداء:
echo    □ البحث سريع بدون تأخير
echo    □ التصفية فورية
echo    □ لا توجد أخطاء في الكونسول
echo    □ الذاكرة مستقرة
echo.
echo ========================================
echo.
echo 💡 نصائح للاختبار:
echo.
echo 🔍 للبحث الفعال:
echo    - جرب كلمات كاملة وجزئية
echo    - اختبر الحساسية للحالة (كبير/صغير)
echo    - جرب أرقام وحروف
echo.
echo 📊 للفلاتر المتعددة:
echo    - ابدأ بفلتر واحد ثم أضف تدريجياً
echo    - راقب تغير عدد النتائج
echo    - تحقق من منطقية النتائج
echo.
echo 🎨 للمؤشرات البصرية:
echo    - راقب ظهور/اختفاء المؤشر
echo    - تحقق من ألوان الشارات
echo    - اقرأ النصوص في الشارات
echo.
echo 🧹 لزر المسح:
echo    - طبق فلاتر متنوعة قبل المسح
echo    - تأكد من مسح جميع الحقول
echo    - تحقق من عودة جميع البيانات
echo.
echo ========================================
echo.
echo 🎯 معايير النجاح:
echo.
echo ✅ جميع الفلاتر الـ7 تعمل بشكل صحيح
echo ✅ البحث فوري ودقيق
echo ✅ الفلاتر المتعددة تعمل معاً
echo ✅ مؤشر الفلاتر النشطة يعمل
echo ✅ زر مسح الفلاتر يعمل
echo ✅ الواجهة جذابة ومنظمة
echo ✅ الأداء سريع ومستقر
echo ✅ لا توجد أخطاء في النظام
echo.
echo ========================================
echo.
echo 🎉 إذا نجحت جميع الاختبارات:
echo    نظام الفلاتر المتقدم يعمل بنجاح!
echo.
echo 🔧 إذا فشل أي اختبار:
echo    راجع دليل الفلاتر المتقدمة
echo    أو استخدم ملف الإصلاح السريع
echo.
echo 📊 للاستخدام العملي:
echo    ابدأ بالفلاتر البسيطة ثم تدرج للمعقدة
echo    استخدم مؤشر الفلاتر النشطة للمتابعة
echo    امسح الفلاتر عند الانتهاء
echo.
echo اضغط أي مفتاح للخروج...
pause >nul