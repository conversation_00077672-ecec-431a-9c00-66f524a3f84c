import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // إعدادات الشريط الجانبي
  sidebarOpen: true,
  sidebarCollapsed: false,
  
  // إعدادات المظهر
  theme: localStorage.getItem('theme') || 'light',
  language: localStorage.getItem('language') || 'ar',
  
  // إعدادات التنبيهات
  notifications: [],
  notificationCount: 0,
  
  // إعدادات التحميل
  loading: false,
  loadingMessage: '',
  
  // إعدادات النوافذ المنبثقة
  modals: {},
  
  // إعدادات الصفحة
  pageTitle: '',
  breadcrumbs: [],
  
  // إعدادات الجداول
  tableSettings: {
    pageSize: 10,
    sortBy: '',
    sortOrder: 'asc',
    filters: {},
  },
  
  // إعدادات الشاشة
  screenSize: 'desktop', // mobile, tablet, desktop
  isMobile: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // إدارة الشريط الجانبي
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.sidebarOpen = action.payload;
    },
    toggleSidebarCollapse: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // إدارة المظهر
    setTheme: (state, action) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', state.theme);
    },
    
    // إدارة اللغة
    setLanguage: (state, action) => {
      state.language = action.payload;
      localStorage.setItem('language', action.payload);
    },
    
    // إدارة التنبيهات
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        read: false,
        ...action.payload,
      };
      state.notifications.unshift(notification);
      state.notificationCount = state.notifications.filter(n => !n.read).length;
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
      state.notificationCount = state.notifications.filter(n => !n.read).length;
    },
    markNotificationAsRead: (state, action) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
        state.notificationCount = state.notifications.filter(n => !n.read).length;
      }
    },
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
      state.notificationCount = 0;
    },
    clearNotifications: (state) => {
      state.notifications = [];
      state.notificationCount = 0;
    },
    
    // إدارة التحميل
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setLoadingMessage: (state, action) => {
      state.loadingMessage = action.payload;
    },
    startLoading: (state, action) => {
      state.loading = true;
      state.loadingMessage = action.payload || '';
    },
    stopLoading: (state) => {
      state.loading = false;
      state.loadingMessage = '';
    },
    
    // إدارة النوافذ المنبثقة
    openModal: (state, action) => {
      const { modalId, props = {} } = action.payload;
      state.modals[modalId] = { open: true, props };
    },
    closeModal: (state, action) => {
      const modalId = action.payload;
      if (state.modals[modalId]) {
        state.modals[modalId].open = false;
      }
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalId => {
        state.modals[modalId].open = false;
      });
    },
    
    // إدارة الصفحة
    setPageTitle: (state, action) => {
      state.pageTitle = action.payload;
    },
    setBreadcrumbs: (state, action) => {
      state.breadcrumbs = action.payload;
    },
    addBreadcrumb: (state, action) => {
      state.breadcrumbs.push(action.payload);
    },
    
    // إدارة إعدادات الجداول
    setTableSettings: (state, action) => {
      state.tableSettings = { ...state.tableSettings, ...action.payload };
    },
    setTablePageSize: (state, action) => {
      state.tableSettings.pageSize = action.payload;
    },
    setTableSort: (state, action) => {
      const { sortBy, sortOrder } = action.payload;
      state.tableSettings.sortBy = sortBy;
      state.tableSettings.sortOrder = sortOrder;
    },
    setTableFilters: (state, action) => {
      state.tableSettings.filters = action.payload;
    },
    clearTableFilters: (state) => {
      state.tableSettings.filters = {};
    },
    
    // إدارة حجم الشاشة
    setScreenSize: (state, action) => {
      state.screenSize = action.payload;
      state.isMobile = action.payload === 'mobile';
    },
    
    // إعادة تعيين الحالة
    resetUI: (state) => {
      return { ...initialState, theme: state.theme, language: state.language };
    },
  },
});

export const {
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapse,
  setSidebarCollapsed,
  setTheme,
  toggleTheme,
  setLanguage,
  addNotification,
  removeNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
  setLoading,
  setLoadingMessage,
  startLoading,
  stopLoading,
  openModal,
  closeModal,
  closeAllModals,
  setPageTitle,
  setBreadcrumbs,
  addBreadcrumb,
  setTableSettings,
  setTablePageSize,
  setTableSort,
  setTableFilters,
  clearTableFilters,
  setScreenSize,
  resetUI,
} = uiSlice.actions;

export default uiSlice.reducer;