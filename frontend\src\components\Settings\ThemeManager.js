import React, { useState, useEffect } from 'react';
import './ThemeManager.css';

// مكون إدارة الثيمات والمظهر
export const ThemeManager = () => {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [customThemes, setCustomThemes] = useState([]);
  const [showCreateTheme, setShowCreateTheme] = useState(false);
  const [newTheme, setNewTheme] = useState({
    name: '',
    primaryColor: '#667eea',
    secondaryColor: '#764ba2',
    backgroundColor: '#ffffff',
    textColor: '#2c3e50',
    accentColor: '#28a745',
    warningColor: '#ffc107',
    errorColor: '#dc3545',
    borderColor: '#e9ecef',
    shadowColor: 'rgba(0, 0, 0, 0.1)'
  });

  const predefinedThemes = [
    {
      id: 'light',
      name: 'فاتح (افتراضي)',
      preview: {
        primaryColor: '#667eea',
        secondaryColor: '#764ba2',
        backgroundColor: '#ffffff',
        textColor: '#2c3e50'
      }
    },
    {
      id: 'dark',
      name: 'داكن',
      preview: {
        primaryColor: '#4c63d2',
        secondaryColor: '#5a67d8',
        backgroundColor: '#1a202c',
        textColor: '#e2e8f0'
      }
    },
    {
      id: 'blue',
      name: 'أزرق احترافي',
      preview: {
        primaryColor: '#3182ce',
        secondaryColor: '#2c5282',
        backgroundColor: '#f7fafc',
        textColor: '#2d3748'
      }
    },
    {
      id: 'green',
      name: 'أخضر طبيعي',
      preview: {
        primaryColor: '#38a169',
        secondaryColor: '#2f855a',
        backgroundColor: '#f0fff4',
        textColor: '#1a202c'
      }
    },
    {
      id: 'purple',
      name: 'بنفسجي ملكي',
      preview: {
        primaryColor: '#805ad5',
        secondaryColor: '#6b46c1',
        backgroundColor: '#faf5ff',
        textColor: '#322659'
      }
    },
    {
      id: 'orange',
      name: 'برتقالي دافئ',
      preview: {
        primaryColor: '#ed8936',
        secondaryColor: '#dd6b20',
        backgroundColor: '#fffaf0',
        textColor: '#7b341e'
      }
    }
  ];

  const [fontSettings, setFontSettings] = useState({
    fontFamily: 'Cairo',
    fontSize: '14px',
    lineHeight: '1.5',
    fontWeight: 'normal'
  });

  const [layoutSettings, setLayoutSettings] = useState({
    sidebarWidth: '280px',
    headerHeight: '70px',
    borderRadius: '12px',
    spacing: 'normal',
    compactMode: false
  });

  const fontFamilies = [
    { value: 'Cairo', label: 'Cairo (افتراضي)' },
    { value: 'Tajawal', label: 'Tajawal' },
    { value: 'Amiri', label: 'Amiri' },
    { value: 'Noto Sans Arabic', label: 'Noto Sans Arabic' },
    { value: 'IBM Plex Sans Arabic', label: 'IBM Plex Sans Arabic' },
    { value: 'Almarai', label: 'Almarai' }
  ];

  const fontSizes = [
    { value: '12px', label: 'صغير (12px)' },
    { value: '14px', label: 'متوسط (14px)' },
    { value: '16px', label: 'كبير (16px)' },
    { value: '18px', label: 'كبير جداً (18px)' }
  ];

  const spacingOptions = [
    { value: 'compact', label: 'مضغوط' },
    { value: 'normal', label: 'عادي' },
    { value: 'comfortable', label: 'مريح' }
  ];

  useEffect(() => {
    // تحميل الثيم المحفوظ
    const savedTheme = localStorage.getItem('selectedTheme');
    if (savedTheme) {
      setCurrentTheme(savedTheme);
      applyTheme(savedTheme);
    }

    // تحميل الثيمات المخصصة
    const savedCustomThemes = localStorage.getItem('customThemes');
    if (savedCustomThemes) {
      setCustomThemes(JSON.parse(savedCustomThemes));
    }

    // تحميل إعدادات الخط
    const savedFontSettings = localStorage.getItem('fontSettings');
    if (savedFontSettings) {
      setFontSettings(JSON.parse(savedFontSettings));
    }

    // تحميل إعدادات التخطيط
    const savedLayoutSettings = localStorage.getItem('layoutSettings');
    if (savedLayoutSettings) {
      setLayoutSettings(JSON.parse(savedLayoutSettings));
    }
  }, []);

  const applyTheme = (themeId) => {
    const theme = predefinedThemes.find(t => t.id === themeId) || 
                  customThemes.find(t => t.id === themeId);
    
    if (theme) {
      const root = document.documentElement;
      
      if (theme.preview) {
        // ثيم محدد مسبقاً
        Object.entries(theme.preview).forEach(([key, value]) => {
          root.style.setProperty(`--${key}`, value);
        });
      } else {
        // ثيم مخصص
        Object.entries(theme).forEach(([key, value]) => {
          if (key !== 'id' && key !== 'name') {
            root.style.setProperty(`--${key}`, value);
          }
        });
      }
    }
  };

  const selectTheme = (themeId) => {
    setCurrentTheme(themeId);
    applyTheme(themeId);
    localStorage.setItem('selectedTheme', themeId);
  };

  const createCustomTheme = () => {
    if (!newTheme.name) {
      alert('يرجى إدخال اسم للثيم');
      return;
    }

    const customTheme = {
      id: `custom-${Date.now()}`,
      name: newTheme.name,
      ...newTheme
    };

    const updatedCustomThemes = [...customThemes, customTheme];
    setCustomThemes(updatedCustomThemes);
    localStorage.setItem('customThemes', JSON.stringify(updatedCustomThemes));
    
    setNewTheme({
      name: '',
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      backgroundColor: '#ffffff',
      textColor: '#2c3e50',
      accentColor: '#28a745',
      warningColor: '#ffc107',
      errorColor: '#dc3545',
      borderColor: '#e9ecef',
      shadowColor: 'rgba(0, 0, 0, 0.1)'
    });
    setShowCreateTheme(false);
    
    alert('تم إنشاء الثيم المخصص بنجاح!');
  };

  const deleteCustomTheme = (themeId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الثيم؟')) {
      const updatedCustomThemes = customThemes.filter(theme => theme.id !== themeId);
      setCustomThemes(updatedCustomThemes);
      localStorage.setItem('customThemes', JSON.stringify(updatedCustomThemes));
      
      if (currentTheme === themeId) {
        selectTheme('light');
      }
    }
  };

  const updateFontSettings = (key, value) => {
    const newFontSettings = { ...fontSettings, [key]: value };
    setFontSettings(newFontSettings);
    localStorage.setItem('fontSettings', JSON.stringify(newFontSettings));
    
    // تطبيق إعدادات الخط
    const root = document.documentElement;
    root.style.setProperty(`--font-${key}`, value);
  };

  const updateLayoutSettings = (key, value) => {
    const newLayoutSettings = { ...layoutSettings, [key]: value };
    setLayoutSettings(newLayoutSettings);
    localStorage.setItem('layoutSettings', JSON.stringify(newLayoutSettings));
    
    // تطبيق إعدادات التخطيط
    const root = document.documentElement;
    root.style.setProperty(`--layout-${key}`, value);
  };

  const exportTheme = (theme) => {
    const themeData = JSON.stringify(theme, null, 2);
    const blob = new Blob([themeData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `theme-${theme.name.replace(/\s+/g, '-')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importTheme = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedTheme = JSON.parse(e.target.result);
          importedTheme.id = `imported-${Date.now()}`;
          
          const updatedCustomThemes = [...customThemes, importedTheme];
          setCustomThemes(updatedCustomThemes);
          localStorage.setItem('customThemes', JSON.stringify(updatedCustomThemes));
          
          alert('تم استيراد الثيم بنجاح!');
        } catch (error) {
          alert('فشل في استيراد الثيم - ملف غير صالح');
        }
      };
      reader.readAsText(file);
    }
  };

  const resetToDefault = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات المظهر إلى القيم الافتراضية؟')) {
      selectTheme('light');
      setFontSettings({
        fontFamily: 'Cairo',
        fontSize: '14px',
        lineHeight: '1.5',
        fontWeight: 'normal'
      });
      setLayoutSettings({
        sidebarWidth: '280px',
        headerHeight: '70px',
        borderRadius: '12px',
        spacing: 'normal',
        compactMode: false
      });
      
      localStorage.removeItem('fontSettings');
      localStorage.removeItem('layoutSettings');
      
      alert('تم إعادة تعيين إعدادات المظهر بنجاح!');
    }
  };

  return (
    <div className="theme-manager">
      <div className="theme-header">
        <h3>إدارة الثيمات والمظهر</h3>
        <div className="theme-actions">
          <label className="import-theme-btn">
            📥 استيراد ثيم
            <input 
              type="file" 
              accept=".json" 
              onChange={importTheme}
              style={{ display: 'none' }}
            />
          </label>
          <button 
            className="reset-theme-btn"
            onClick={resetToDefault}
          >
            🔄 إعادة تعيين
          </button>
        </div>
      </div>

      {/* الثيمات المحددة مسبقاً */}
      <div className="themes-section">
        <h4>الثيمات المحددة مسبقاً</h4>
        <div className="themes-grid">
          {predefinedThemes.map(theme => (
            <div 
              key={theme.id}
              className={`theme-card ${currentTheme === theme.id ? 'active' : ''}`}
              onClick={() => selectTheme(theme.id)}
            >
              <div className="theme-preview">
                <div 
                  className="preview-header"
                  style={{ backgroundColor: theme.preview.primaryColor }}
                ></div>
                <div 
                  className="preview-body"
                  style={{ 
                    backgroundColor: theme.preview.backgroundColor,
                    color: theme.preview.textColor
                  }}
                >
                  <div 
                    className="preview-accent"
                    style={{ backgroundColor: theme.preview.secondaryColor }}
                  ></div>
                </div>
              </div>
              <div className="theme-name">{theme.name}</div>
              {currentTheme === theme.id && (
                <div className="active-indicator">✓ نشط</div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* الثيمات المخصصة */}
      <div className="custom-themes-section">
        <div className="section-header">
          <h4>الثيمات المخصصة</h4>
          <button 
            className="create-theme-btn"
            onClick={() => setShowCreateTheme(!showCreateTheme)}
          >
            {showCreateTheme ? '❌ إلغاء' : '🎨 إنشاء ثيم مخصص'}
          </button>
        </div>

        {showCreateTheme && (
          <div className="create-theme-form">
            <div className="form-grid">
              <div className="form-group">
                <label>اسم الثيم</label>
                <input
                  type="text"
                  value={newTheme.name}
                  onChange={(e) => setNewTheme({...newTheme, name: e.target.value})}
                  placeholder="أدخل اسم الثيم"
                />
              </div>

              <div className="color-inputs">
                <div className="color-group">
                  <label>اللون الأساسي</label>
                  <input
                    type="color"
                    value={newTheme.primaryColor}
                    onChange={(e) => setNewTheme({...newTheme, primaryColor: e.target.value})}
                  />
                </div>

                <div className="color-group">
                  <label>اللون الثانوي</label>
                  <input
                    type="color"
                    value={newTheme.secondaryColor}
                    onChange={(e) => setNewTheme({...newTheme, secondaryColor: e.target.value})}
                  />
                </div>

                <div className="color-group">
                  <label>لون الخلفية</label>
                  <input
                    type="color"
                    value={newTheme.backgroundColor}
                    onChange={(e) => setNewTheme({...newTheme, backgroundColor: e.target.value})}
                  />
                </div>

                <div className="color-group">
                  <label>لون النص</label>
                  <input
                    type="color"
                    value={newTheme.textColor}
                    onChange={(e) => setNewTheme({...newTheme, textColor: e.target.value})}
                  />
                </div>

                <div className="color-group">
                  <label>لون التمييز</label>
                  <input
                    type="color"
                    value={newTheme.accentColor}
                    onChange={(e) => setNewTheme({...newTheme, accentColor: e.target.value})}
                  />
                </div>

                <div className="color-group">
                  <label>لون التحذير</label>
                  <input
                    type="color"
                    value={newTheme.warningColor}
                    onChange={(e) => setNewTheme({...newTheme, warningColor: e.target.value})}
                  />
                </div>
              </div>

              <div className="theme-preview-live">
                <h5>معاينة مباشرة</h5>
                <div className="live-preview">
                  <div 
                    className="preview-header"
                    style={{ backgroundColor: newTheme.primaryColor }}
                  >
                    <span style={{ color: 'white' }}>رأس الصفحة</span>
                  </div>
                  <div 
                    className="preview-content"
                    style={{ 
                      backgroundColor: newTheme.backgroundColor,
                      color: newTheme.textColor,
                      border: `1px solid ${newTheme.borderColor}`
                    }}
                  >
                    <div 
                      className="preview-button"
                      style={{ backgroundColor: newTheme.accentColor }}
                    >
                      زر
                    </div>
                    <div 
                      className="preview-text"
                      style={{ color: newTheme.textColor }}
                    >
                      نص تجريبي
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="form-actions">
              <button className="save-theme-btn" onClick={createCustomTheme}>
                💾 حفظ الثيم
              </button>
            </div>
          </div>
        )}

        {customThemes.length > 0 && (
          <div className="custom-themes-grid">
            {customThemes.map(theme => (
              <div 
                key={theme.id}
                className={`theme-card custom ${currentTheme === theme.id ? 'active' : ''}`}
              >
                <div className="theme-preview" onClick={() => selectTheme(theme.id)}>
                  <div 
                    className="preview-header"
                    style={{ backgroundColor: theme.primaryColor }}
                  ></div>
                  <div 
                    className="preview-body"
                    style={{ 
                      backgroundColor: theme.backgroundColor,
                      color: theme.textColor
                    }}
                  >
                    <div 
                      className="preview-accent"
                      style={{ backgroundColor: theme.secondaryColor }}
                    ></div>
                  </div>
                </div>
                <div className="theme-name">{theme.name}</div>
                <div className="theme-actions">
                  <button 
                    className="export-btn"
                    onClick={() => exportTheme(theme)}
                    title="تصدير الثيم"
                  >
                    📤
                  </button>
                  <button 
                    className="delete-btn"
                    onClick={() => deleteCustomTheme(theme.id)}
                    title="حذف الثيم"
                  >
                    🗑️
                  </button>
                </div>
                {currentTheme === theme.id && (
                  <div className="active-indicator">✓ نشط</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* إعدادات الخط */}
      <div className="font-settings-section">
        <h4>إعدادات الخط</h4>
        <div className="font-settings-grid">
          <div className="setting-item">
            <label>نوع الخط</label>
            <select
              value={fontSettings.fontFamily}
              onChange={(e) => updateFontSettings('fontFamily', e.target.value)}
            >
              {fontFamilies.map(font => (
                <option key={font.value} value={font.value}>
                  {font.label}
                </option>
              ))}
            </select>
          </div>

          <div className="setting-item">
            <label>حجم الخط</label>
            <select
              value={fontSettings.fontSize}
              onChange={(e) => updateFontSettings('fontSize', e.target.value)}
            >
              {fontSizes.map(size => (
                <option key={size.value} value={size.value}>
                  {size.label}
                </option>
              ))}
            </select>
          </div>

          <div className="setting-item">
            <label>ارتفاع السطر</label>
            <input
              type="range"
              min="1.2"
              max="2"
              step="0.1"
              value={fontSettings.lineHeight}
              onChange={(e) => updateFontSettings('lineHeight', e.target.value)}
            />
            <span className="range-value">{fontSettings.lineHeight}</span>
          </div>

          <div className="setting-item">
            <label>وزن الخط</label>
            <select
              value={fontSettings.fontWeight}
              onChange={(e) => updateFontSettings('fontWeight', e.target.value)}
            >
              <option value="normal">عادي</option>
              <option value="500">متوسط</option>
              <option value="600">سميك</option>
              <option value="700">سميك جداً</option>
            </select>
          </div>
        </div>
      </div>

      {/* إعدادات التخطيط */}
      <div className="layout-settings-section">
        <h4>إعدادات التخطيط</h4>
        <div className="layout-settings-grid">
          <div className="setting-item">
            <label>عرض الشريط الجانبي</label>
            <input
              type="range"
              min="200"
              max="400"
              step="10"
              value={parseInt(layoutSettings.sidebarWidth)}
              onChange={(e) => updateLayoutSettings('sidebarWidth', `${e.target.value}px`)}
            />
            <span className="range-value">{layoutSettings.sidebarWidth}</span>
          </div>

          <div className="setting-item">
            <label>ارتفاع الرأس</label>
            <input
              type="range"
              min="50"
              max="100"
              step="5"
              value={parseInt(layoutSettings.headerHeight)}
              onChange={(e) => updateLayoutSettings('headerHeight', `${e.target.value}px`)}
            />
            <span className="range-value">{layoutSettings.headerHeight}</span>
          </div>

          <div className="setting-item">
            <label>نصف قطر الحدود</label>
            <input
              type="range"
              min="0"
              max="20"
              step="2"
              value={parseInt(layoutSettings.borderRadius)}
              onChange={(e) => updateLayoutSettings('borderRadius', `${e.target.value}px`)}
            />
            <span className="range-value">{layoutSettings.borderRadius}</span>
          </div>

          <div className="setting-item">
            <label>المسافات</label>
            <select
              value={layoutSettings.spacing}
              onChange={(e) => updateLayoutSettings('spacing', e.target.value)}
            >
              {spacingOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={layoutSettings.compactMode}
                onChange={(e) => updateLayoutSettings('compactMode', e.target.checked)}
              />
              <span className="checkmark"></span>
              الوضع المضغوط
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeManager;