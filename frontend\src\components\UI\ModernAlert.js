import React, { useState, useEffect } from 'react';
import { IconButton } from './ModernButton';
import './ModernAlert.css';

// Modern Alert Component
export const ModernAlert = ({
  children,
  variant = 'info',
  size = 'md',
  dismissible = false,
  icon,
  title,
  actions,
  className = '',
  onDismiss,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(true);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  const getDefaultIcon = () => {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      primary: '🔵',
      secondary: '⚪'
    };
    return icons[variant] || icons.info;
  };

  const alertClasses = [
    'modern-alert',
    `modern-alert--${variant}`,
    `modern-alert--${size}`,
    dismissible && 'modern-alert--dismissible',
    className
  ].filter(Boolean).join(' ');

  if (!isVisible) return null;

  return (
    <div className={alertClasses} role="alert" {...props}>
      <div className="modern-alert-content">
        {(icon || !title) && (
          <div className="modern-alert-icon">
            {icon || getDefaultIcon()}
          </div>
        )}
        
        <div className="modern-alert-body">
          {title && (
            <div className="modern-alert-title">{title}</div>
          )}
          <div className="modern-alert-message">
            {children}
          </div>
          {actions && (
            <div className="modern-alert-actions">
              {actions}
            </div>
          )}
        </div>
      </div>
      
      {dismissible && (
        <IconButton
          icon="✕"
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="modern-alert-dismiss"
        />
      )}
    </div>
  );
};

// Toast Notification Component
export const Toast = ({
  children,
  variant = 'info',
  duration = 5000,
  position = 'top-right',
  icon,
  title,
  dismissible = true,
  className = '',
  onDismiss,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleDismiss = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, 300);
  };

  const getDefaultIcon = () => {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[variant] || icons.info;
  };

  const toastClasses = [
    'modern-toast',
    `modern-toast--${variant}`,
    `modern-toast--${position}`,
    isExiting && 'modern-toast--exiting',
    className
  ].filter(Boolean).join(' ');

  if (!isVisible) return null;

  return (
    <div className={toastClasses} {...props}>
      <div className="modern-toast-content">
        <div className="modern-toast-icon">
          {icon || getDefaultIcon()}
        </div>
        
        <div className="modern-toast-body">
          {title && (
            <div className="modern-toast-title">{title}</div>
          )}
          <div className="modern-toast-message">
            {children}
          </div>
        </div>
      </div>
      
      {dismissible && (
        <IconButton
          icon="✕"
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="modern-toast-dismiss"
        />
      )}
      
      {duration > 0 && (
        <div 
          className="modern-toast-progress"
          style={{ animationDuration: `${duration}ms` }}
        />
      )}
    </div>
  );
};

// Toast Container Component
export const ToastContainer = ({ 
  position = 'top-right',
  className = '',
  children 
}) => {
  const containerClasses = [
    'modern-toast-container',
    `modern-toast-container--${position}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

// Banner Component
export const Banner = ({
  children,
  variant = 'info',
  icon,
  title,
  actions,
  dismissible = false,
  sticky = false,
  className = '',
  onDismiss,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(true);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  const getDefaultIcon = () => {
    const icons = {
      success: '🎉',
      error: '🚨',
      warning: '📢',
      info: '📣'
    };
    return icons[variant] || icons.info;
  };

  const bannerClasses = [
    'modern-banner',
    `modern-banner--${variant}`,
    sticky && 'modern-banner--sticky',
    className
  ].filter(Boolean).join(' ');

  if (!isVisible) return null;

  return (
    <div className={bannerClasses} {...props}>
      <div className="modern-banner-content">
        <div className="modern-banner-icon">
          {icon || getDefaultIcon()}
        </div>
        
        <div className="modern-banner-body">
          {title && (
            <div className="modern-banner-title">{title}</div>
          )}
          <div className="modern-banner-message">
            {children}
          </div>
        </div>
        
        {actions && (
          <div className="modern-banner-actions">
            {actions}
          </div>
        )}
      </div>
      
      {dismissible && (
        <IconButton
          icon="✕"
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="modern-banner-dismiss"
        />
      )}
    </div>
  );
};

// Status Badge Component
export const StatusBadge = ({
  children,
  variant = 'default',
  size = 'md',
  icon,
  pulse = false,
  className = '',
  ...props
}) => {
  const badgeClasses = [
    'modern-status-badge',
    `modern-status-badge--${variant}`,
    `modern-status-badge--${size}`,
    pulse && 'modern-status-badge--pulse',
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={badgeClasses} {...props}>
      {icon && (
        <span className="modern-status-badge-icon">{icon}</span>
      )}
      <span className="modern-status-badge-text">{children}</span>
    </span>
  );
};

// Progress Alert Component
export const ProgressAlert = ({
  children,
  variant = 'info',
  progress = 0,
  showProgress = true,
  icon,
  title,
  className = '',
  ...props
}) => {
  const alertClasses = [
    'modern-progress-alert',
    `modern-progress-alert--${variant}`,
    className
  ].filter(Boolean).join(' ');

  const getDefaultIcon = () => {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[variant] || icons.info;
  };

  return (
    <div className={alertClasses} {...props}>
      <div className="modern-progress-alert-content">
        <div className="modern-progress-alert-icon">
          {icon || getDefaultIcon()}
        </div>
        
        <div className="modern-progress-alert-body">
          {title && (
            <div className="modern-progress-alert-title">{title}</div>
          )}
          <div className="modern-progress-alert-message">
            {children}
          </div>
          {showProgress && (
            <div className="modern-progress-alert-progress">
              <div className="modern-progress-alert-progress-bar">
                <div 
                  className="modern-progress-alert-progress-fill"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                />
              </div>
              <span className="modern-progress-alert-progress-text">
                {Math.round(progress)}%
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Callout Component
export const Callout = ({
  children,
  variant = 'info',
  icon,
  title,
  bordered = true,
  className = '',
  ...props
}) => {
  const calloutClasses = [
    'modern-callout',
    `modern-callout--${variant}`,
    bordered && 'modern-callout--bordered',
    className
  ].filter(Boolean).join(' ');

  const getDefaultIcon = () => {
    const icons = {
      success: '💡',
      error: '🚫',
      warning: '⚡',
      info: '💬',
      tip: '🔥',
      note: '📝'
    };
    return icons[variant] || icons.info;
  };

  return (
    <div className={calloutClasses} {...props}>
      {(icon !== false) && (
        <div className="modern-callout-icon">
          {icon || getDefaultIcon()}
        </div>
      )}
      
      <div className="modern-callout-content">
        {title && (
          <div className="modern-callout-title">{title}</div>
        )}
        <div className="modern-callout-body">
          {children}
        </div>
      </div>
    </div>
  );
};

export default ModernAlert;