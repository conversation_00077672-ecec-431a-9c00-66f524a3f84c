// Currency Manager - إدارة العملات المتعددة
export const CURRENCIES = {
  SAR: {
    code: 'SAR',
    name: 'الريال السعودي',
    nameEn: 'Saudi Riyal',
    symbol: 'ر.س',
    symbolEn: 'SAR',
    flag: '🇸🇦',
    rate: 1, // العملة الأساسية
    decimals: 2,
    format: 'right', // موضع الرمز
    rtl: true
  },
  YER: {
    code: 'YER',
    name: 'الريال اليمني',
    nameEn: 'Yemeni Rial',
    symbol: 'ر.ي',
    symbolEn: 'YER',
    flag: '🇾🇪',
    rate: 250.75, // سعر الصرف مقابل الريال السعودي
    decimals: 0,
    format: 'right',
    rtl: true
  },
  USD: {
    code: 'USD',
    name: 'الدولار الأمريكي',
    nameEn: 'US Dollar',
    symbol: '$',
    symbolEn: 'USD',
    flag: '🇺🇸',
    rate: 0.27, // سعر الصرف مقابل الريال السعودي
    decimals: 2,
    format: 'left',
    rtl: false
  },
  EUR: {
    code: 'EUR',
    name: 'اليورو',
    nameEn: 'Euro',
    symbol: '€',
    symbolEn: 'EUR',
    flag: '🇪🇺',
    rate: 0.25,
    decimals: 2,
    format: 'left',
    rtl: false
  },
  AED: {
    code: 'AED',
    name: 'الدرهم الإماراتي',
    nameEn: 'UAE Dirham',
    symbol: 'د.إ',
    symbolEn: 'AED',
    flag: '🇦🇪',
    rate: 1.00,
    decimals: 2,
    format: 'right',
    rtl: true
  }
};

export class CurrencyManager {
  static formatAmount(amount, currencyCode = 'SAR', showSymbol = true) {
    const currency = CURRENCIES[currencyCode];
    if (!currency) return amount.toString();

    const formattedAmount = new Intl.NumberFormat('ar-SA', {
      minimumFractionDigits: currency.decimals,
      maximumFractionDigits: currency.decimals
    }).format(amount);

    if (!showSymbol) return formattedAmount;

    return currency.format === 'right' 
      ? `${formattedAmount} ${currency.symbol}`
      : `${currency.symbol}${formattedAmount}`;
  }

  static convertCurrency(amount, fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) return amount;
    
    const from = CURRENCIES[fromCurrency];
    const to = CURRENCIES[toCurrency];
    
    if (!from || !to) return amount;

    // تحويل إلى الريال السعودي أولاً
    const sarAmount = amount / from.rate;
    // ثم تحويل إلى العملة المطلوبة
    return sarAmount * to.rate;
  }

  static getExchangeRate(fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) return 1;
    
    const from = CURRENCIES[fromCurrency];
    const to = CURRENCIES[toCurrency];
    
    if (!from || !to) return 1;

    return to.rate / from.rate;
  }

  static getCurrencyList() {
    return Object.values(CURRENCIES);
  }

  static getCurrency(code) {
    return CURRENCIES[code] || CURRENCIES.SAR;
  }

  static formatAmountInWords(amount, currencyCode = 'SAR') {
    const currency = CURRENCIES[currencyCode];
    const integerPart = Math.floor(amount);
    const decimalPart = Math.round((amount - integerPart) * 100);

    const ones = [
      '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
      'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر',
      'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ];

    const tens = [
      '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    ];

    const hundreds = [
      '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    ];

    function convertToWords(num) {
      if (num === 0) return 'صفر';
      if (num < 20) return ones[num];
      if (num < 100) {
        const ten = Math.floor(num / 10);
        const one = num % 10;
        return tens[ten] + (one > 0 ? ' و' + ones[one] : '');
      }
      if (num < 1000) {
        const hundred = Math.floor(num / 100);
        const remainder = num % 100;
        return hundreds[hundred] + (remainder > 0 ? ' و' + convertToWords(remainder) : '');
      }
      if (num < 1000000) {
        const thousand = Math.floor(num / 1000);
        const remainder = num % 1000;
        let result = '';
        if (thousand === 1) result = 'ألف';
        else if (thousand === 2) result = 'ألفان';
        else if (thousand < 11) result = convertToWords(thousand) + ' آلاف';
        else result = convertToWords(thousand) + ' ألف';
        
        return result + (remainder > 0 ? ' و' + convertToWords(remainder) : '');
      }
      return num.toString(); // للأرقام الكبيرة جداً
    }

    let result = convertToWords(integerPart);
    
    // إضافة اسم العملة
    if (currencyCode === 'SAR') {
      result += integerPart === 1 ? ' ريال سعودي' : ' ريال سعودي';
    } else if (currencyCode === 'YER') {
      result += integerPart === 1 ? ' ريال يمني' : ' ريال يمني';
    } else if (currencyCode === 'USD') {
      result += integerPart === 1 ? ' دولار أمريكي' : ' دولار أمريكي';
    } else if (currencyCode === 'EUR') {
      result += integerPart === 1 ? ' يورو' : ' يورو';
    } else if (currencyCode === 'AED') {
      result += integerPart === 1 ? ' درهم إماراتي' : ' درهم إماراتي';
    }

    // إضافة الكسور إذا وجدت
    if (decimalPart > 0 && currency.decimals > 0) {
      result += ' و' + convertToWords(decimalPart);
      if (currencyCode === 'SAR' || currencyCode === 'YER') {
        result += decimalPart === 1 ? ' هللة' : ' هللة';
      } else if (currencyCode === 'USD') {
        result += decimalPart === 1 ? ' سنت' : ' سنت';
      } else if (currencyCode === 'EUR') {
        result += decimalPart === 1 ? ' سنت' : ' سنت';
      } else if (currencyCode === 'AED') {
        result += decimalPart === 1 ? ' فلس' : ' فلس';
      }
    }

    return result + ' فقط لا غير';
  }

  static getLatestRates() {
    // في التطبيق الحقيقي، هذه ستكون API call لجلب أسعار الصرف الحالية
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          SAR: 1,
          YER: 250.75,
          USD: 0.27,
          EUR: 0.25,
          AED: 1.00,
          lastUpdated: new Date().toISOString()
        });
      }, 1000);
    });
  }
}

export default CurrencyManager;