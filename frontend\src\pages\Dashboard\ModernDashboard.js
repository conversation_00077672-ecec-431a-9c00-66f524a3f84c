import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  ModernGrid, 
  ModernFlex,
  ModernCard, 
  StatsCard, 
  MetricCard,
  ModernButton, 
  IconButton,
  ModernAlert, 
  StatusBadge,
  Tabs, 
  TabPanel,
  ProgressCircle, 
  BarChart, 
  LineChart, 
  DonutChart, 
  MetricDisplay,
  GaugeChart 
} from '../../components/UI';
import './ModernDashboard.css';

const ModernDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({});
  const [timeRange, setTimeRange] = useState('month');

  // Sample data
  useEffect(() => {
    setTimeout(() => {
      setDashboardData({
        stats: {
          totalRevenue: 2450000,
          totalBookings: 1234,
          activeCustomers: 856,
          pendingPayments: 45000,
          monthlyGrowth: 12.5,
          customerSatisfaction: 94
        },
        recentBookings: [
          {
            id: 1,
            customer: 'أحمد محمد العلي',
            service: 'رحلة الرياض - دبي',
            amount: 2500,
            status: 'confirmed',
            date: '2024-01-15'
          },
          {
            id: 2,
            customer: 'فاطمة سالم أحمد',
            service: 'عمرة - 7 أيام',
            amount: 1800,
            status: 'pending',
            date: '2024-01-14'
          },
          {
            id: 3,
            customer: 'محمد عبدالله الزهراني',
            service: 'تأشيرة سياحية - تركيا',
            amount: 450,
            status: 'processing',
            date: '2024-01-13'
          }
        ],
        salesChart: [
          { label: 'يناير', value: 180000 },
          { label: 'فبراير', value: 220000 },
          { label: 'مارس', value: 195000 },
          { label: 'أبريل', value: 280000 },
          { label: 'مايو', value: 310000 },
          { label: 'يونيو', value: 290000 }
        ],
        servicesChart: [
          { label: 'حجز طيران', value: 45, color: '#3B82F6' },
          { label: 'حجز فنادق', value: 30, color: '#10B981' },
          { label: 'تأشيرات', value: 15, color: '#F59E0B' },
          { label: 'باقات سياحية', value: 10, color: '#EF4444' }
        ],
        monthlyTrend: [65, 72, 68, 85, 92, 88, 95, 89, 94, 98, 85, 92],
        topDestinations: [
          { name: 'دبي', bookings: 156, revenue: 780000 },
          { name: 'القاهرة', bookings: 134, revenue: 670000 },
          { name: 'إسطنبول', bookings: 98, revenue: 490000 },
          { name: 'لندن', bookings: 87, revenue: 435000 },
          { name: 'باريس', bookings: 76, revenue: 380000 }
        ]
      });
      setLoading(false);
    }, 1000);
  }, [timeRange]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusLabel = (status) => {
    const labels = {
      confirmed: 'مؤكد',
      pending: 'قيد الانتظار',
      processing: 'قيد المعالجة',
      cancelled: 'ملغي'
    };
    return labels[status] || status;
  };

  const getStatusVariant = (status) => {
    const variants = {
      confirmed: 'success',
      pending: 'warning',
      processing: 'info',
      cancelled: 'error'
    };
    return variants[status] || 'default';
  };

  if (loading) {
    return (
      <div className="modern-dashboard loading">
        <div className="dashboard-skeleton">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="skeleton-card" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="modern-dashboard">
      {/* Dashboard Header */}
      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1>مرحباً بك في لوحة التحكم</h1>
          <p>نظرة شاملة على أداء شركتك للسفر والسياحة</p>
        </div>
        
        <div className="dashboard-actions">
          <ModernButton
            variant="outline"
            icon="📊"
            onClick={() => console.log('Export report')}
          >
            تصدير التقرير
          </ModernButton>
          <ModernButton
            variant="primary"
            icon="➕"
            onClick={() => console.log('New booking')}
          >
            حجز جديد
          </ModernButton>
        </div>
      </div>

      {/* Quick Stats */}
      <ModernGrid cols={4} gap="lg" responsive className="dashboard-stats">
        <StatsCard
          title="إجمالي الإيرادات"
          value={formatCurrency(dashboardData.stats.totalRevenue)}
          change="+12.5%"
          changeType="positive"
          icon="💰"
          color="success"
          trend={dashboardData.monthlyTrend}
        />
        
        <StatsCard
          title="إجمالي الحجوزات"
          value={dashboardData.stats.totalBookings.toLocaleString()}
          change="+8.2%"
          changeType="positive"
          icon="📋"
          color="primary"
        />
        
        <StatsCard
          title="العملاء النشطون"
          value={dashboardData.stats.activeCustomers.toLocaleString()}
          change="+15.3%"
          changeType="positive"
          icon="👥"
          color="info"
        />
        
        <StatsCard
          title="المدفوعات المعلقة"
          value={formatCurrency(dashboardData.stats.pendingPayments)}
          change="-5.1%"
          changeType="negative"
          icon="⏳"
          color="warning"
        />
      </ModernGrid>

      {/* Performance Metrics */}
      <ModernGrid cols={3} gap="lg" responsive className="dashboard-metrics">
        <ModernCard>
          <div className="metric-card-content">
            <h3>معدل النمو الشهري</h3>
            <ProgressCircle
              value={dashboardData.stats.monthlyGrowth}
              max={20}
              size={120}
              color="var(--success-500)"
              label="نمو شهري"
            />
          </div>
        </ModernCard>
        
        <ModernCard>
          <div className="metric-card-content">
            <h3>رضا العملاء</h3>
            <GaugeChart
              value={dashboardData.stats.customerSatisfaction}
              size={140}
              color="var(--primary-500)"
              label="من 100"
            />
          </div>
        </ModernCard>
        
        <ModernCard>
          <div className="metric-card-content">
            <h3>توزيع الخدمات</h3>
            <DonutChart
              data={dashboardData.servicesChart}
              size={120}
              showLabels={false}
              showValues={false}
            />
          </div>
        </ModernCard>
      </ModernGrid>

      {/* Charts Section */}
      <ModernGrid cols={2} gap="lg" responsive className="dashboard-charts">
        <ModernCard>
          <div className="chart-header">
            <h3>اتجاه المبيعات</h3>
            <div className="chart-controls">
              <select 
                value={timeRange} 
                onChange={(e) => setTimeRange(e.target.value)}
                className="time-range-select"
              >
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="quarter">هذا الربع</option>
                <option value="year">هذا العام</option>
              </select>
            </div>
          </div>
          <LineChart
            data={dashboardData.salesChart}
            width={400}
            height={250}
            color="var(--primary-500)"
            showGrid={true}
            showPoints={true}
          />
        </ModernCard>
        
        <ModernCard>
          <div className="chart-header">
            <h3>أداء الخدمات</h3>
          </div>
          <BarChart
            data={dashboardData.servicesChart}
            width={400}
            height={250}
            showValues={true}
            showGrid={true}
          />
        </ModernCard>
      </ModernGrid>

      {/* Content Tabs */}
      <ModernCard className="dashboard-tabs-card">
        <Tabs defaultTab={0} variant="pills">
          <TabPanel label="الحجوزات الأخيرة" icon="📋">
            <div className="recent-bookings">
              <div className="bookings-header">
                <h4>آخر الحجوزات</h4>
                <Link to="/bookings" className="view-all-link">
                  عرض الكل
                </Link>
              </div>
              
              <div className="bookings-list">
                {dashboardData.recentBookings.map(booking => (
                  <div key={booking.id} className="booking-item">
                    <div className="booking-info">
                      <div className="booking-customer">{booking.customer}</div>
                      <div className="booking-service">{booking.service}</div>
                      <div className="booking-date">{booking.date}</div>
                    </div>
                    
                    <div className="booking-details">
                      <div className="booking-amount">
                        {formatCurrency(booking.amount)}
                      </div>
                      <StatusBadge variant={getStatusVariant(booking.status)}>
                        {getStatusLabel(booking.status)}
                      </StatusBadge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabPanel>
          
          <TabPanel label="أهم الوجهات" icon="🌍">
            <div className="top-destinations">
              <div className="destinations-header">
                <h4>أهم الوجهات السياحية</h4>
              </div>
              
              <div className="destinations-list">
                {dashboardData.topDestinations.map((destination, index) => (
                  <div key={index} className="destination-item">
                    <div className="destination-rank">#{index + 1}</div>
                    <div className="destination-info">
                      <div className="destination-name">{destination.name}</div>
                      <div className="destination-stats">
                        {destination.bookings} حجز • {formatCurrency(destination.revenue)}
                      </div>
                    </div>
                    <div className="destination-progress">
                      <div 
                        className="progress-bar"
                        style={{ 
                          width: `${(destination.bookings / dashboardData.topDestinations[0].bookings) * 100}%` 
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabPanel>
          
          <TabPanel label="التحليلات" icon="📈">
            <div className="analytics-content">
              <ModernGrid cols={2} gap="lg">
                <MetricDisplay
                  title="متوسط قيمة الحجز"
                  value={formatCurrency(dashboardData.stats.totalRevenue / dashboardData.stats.totalBookings)}
                  change="+5.2%"
                  changeType="positive"
                  icon="💳"
                  color="var(--success-500)"
                />
                
                <MetricDisplay
                  title="معدل التحويل"
                  value="68%"
                  change="+2.1%"
                  changeType="positive"
                  icon="🎯"
                  color="var(--primary-500)"
                />
                
                <MetricDisplay
                  title="وقت الاستجابة"
                  value="2.3 ساعة"
                  change="-15%"
                  changeType="positive"
                  icon="⚡"
                  color="var(--info-500)"
                />
                
                <MetricDisplay
                  title="معدل الإلغاء"
                  value="3.2%"
                  change="-0.8%"
                  changeType="positive"
                  icon="❌"
                  color="var(--warning-500)"
                />
              </ModernGrid>
            </div>
          </TabPanel>
        </Tabs>
      </ModernCard>

      {/* Quick Actions */}
      <ModernCard className="quick-actions-card">
        <div className="quick-actions-header">
          <h3>إجراءات سريعة</h3>
          <p>الوصول السريع للمهام الأكثر استخداماً</p>
        </div>
        
        <ModernGrid cols={4} gap="md" responsive className="quick-actions-grid">
          <Link to="/bookings/new" className="quick-action-item">
            <div className="quick-action-icon">✈️</div>
            <div className="quick-action-title">حجز طيران</div>
            <div className="quick-action-desc">إنشاء حجز طيران جديد</div>
          </Link>
          
          <Link to="/customers/new" className="quick-action-item">
            <div className="quick-action-icon">👤</div>
            <div className="quick-action-title">عميل جديد</div>
            <div className="quick-action-desc">إضافة عميل جديد</div>
          </Link>
          
          <Link to="/sales/invoices/new" className="quick-action-item">
            <div className="quick-action-icon">🧾</div>
            <div className="quick-action-title">فاتورة جديدة</div>
            <div className="quick-action-desc">إنشاء فاتورة جديدة</div>
          </Link>
          
          <Link to="/reports" className="quick-action-item">
            <div className="quick-action-icon">📊</div>
            <div className="quick-action-title">التقارير</div>
            <div className="quick-action-desc">عرض التقارير المالية</div>
          </Link>
        </ModernGrid>
      </ModernCard>

      {/* Alerts Section */}
      <div className="dashboard-alerts">
        <ModernAlert
          variant="info"
          title="تحديث النظام"
          dismissible
          icon="🔄"
        >
          يتوفر تحديث جديد للنظام. يُنصح بتحديث النظام للحصول على أحدث الميزات والتحسينات.
        </ModernAlert>
      </div>
    </div>
  );
};

export default ModernDashboard;