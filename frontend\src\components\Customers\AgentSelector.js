import React, { useState, useEffect } from 'react';
import agentsService from '../../services/AgentsService';
import './AgentSelector.css';

const AgentSelector = ({
  value,
  onChange,
  disabled = false,
  onAgentSelect = null,
  showActiveOnly = true
}) => {
  const [showAgentDetails, setShowAgentDetails] = useState(false);
  const [agentsList, setAgentsList] = useState([]);

  // تحميل قائمة الوكلاء من الخدمة
  useEffect(() => {
    const loadAgents = () => {
      const agents = showActiveOnly ? agentsService.getActiveAgents() : agentsService.getAllAgents();
      setAgentsList(agents);
    };

    loadAgents();

    // إضافة مستمع للتحديثات المباشرة
    const handleAgentsUpdate = () => {
      loadAgents();
    };

    agentsService.addListener(handleAgentsUpdate);

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      agentsService.removeListener(handleAgentsUpdate);
    };
  }, [showActiveOnly]);

  const selectedAgent = agentsList.find(agent => agent.agentName === value);

  const handleAgentChange = (e) => {
    const selectedAgentName = e.target.value;
    const agent = agentsList.find(a => a.agentName === selectedAgentName);
    
    onChange(e);
    
    if (onAgentSelect && agent) {
      onAgentSelect(agent);
    }
  };

  const getAgentStatusColor = (status) => {
    switch (status) {
      case 'active': return '#27ae60';
      case 'inactive': return '#e74c3c';
      case 'pending': return '#f39c12';
      default: return '#95a5a6';
    }
  };

  const getAgentStatusLabel = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'pending': return 'في الانتظار';
      default: return 'غير محدد';
    }
  };

  return (
    <div className="agent-selector">
      <div className="agent-select-container">
        <select
          value={value}
          onChange={handleAgentChange}
          disabled={disabled}
          className="agent-select"
        >
          <option value="">اختر الوكيل</option>
          {agentsList.map((agent) => (
            <option key={agent.id} value={agent.agentName}>
              {agent.agentName} ({agent.agentCode})
              {agent.specialty && ` - ${agent.specialty}`}
            </option>
          ))}
        </select>
        
        {selectedAgent && (
          <button
            type="button"
            className="agent-details-toggle"
            onClick={() => setShowAgentDetails(!showAgentDetails)}
            title="عرض تفاصيل الوكيل"
          >
            {showAgentDetails ? '👁️‍🗨️' : '👁️'}
          </button>
        )}
      </div>

      {selectedAgent && showAgentDetails && (
        <div className="agent-details">
          <div className="agent-header">
            <div className="agent-avatar">
              {selectedAgent.agentName.charAt(0)}
            </div>
            <div className="agent-info">
              <div className="agent-name">{selectedAgent.agentName}</div>
              <div className="agent-code">كود الوكيل: {selectedAgent.agentCode}</div>
            </div>
            <div 
              className="agent-status"
              style={{ 
                backgroundColor: getAgentStatusColor(selectedAgent.status),
                color: 'white'
              }}
            >
              {getAgentStatusLabel(selectedAgent.status)}
            </div>
          </div>

          <div className="agent-details-grid">
            {selectedAgent.office && (
              <div className="detail-item">
                <span className="detail-icon">🏢</span>
                <div className="detail-content">
                  <div className="detail-label">المكتب</div>
                  <div className="detail-value">{selectedAgent.office}</div>
                </div>
              </div>
            )}

            {selectedAgent.specialty && (
              <div className="detail-item">
                <span className="detail-icon">⭐</span>
                <div className="detail-content">
                  <div className="detail-label">التخصص</div>
                  <div className="detail-value">{selectedAgent.specialty}</div>
                </div>
              </div>
            )}

            {selectedAgent.phone && (
              <div className="detail-item">
                <span className="detail-icon">📞</span>
                <div className="detail-content">
                  <div className="detail-label">الهاتف</div>
                  <div className="detail-value">{selectedAgent.phone}</div>
                </div>
              </div>
            )}

            {selectedAgent.email && (
              <div className="detail-item">
                <span className="detail-icon">📧</span>
                <div className="detail-content">
                  <div className="detail-label">البريد الإلكتروني</div>
                  <div className="detail-value">{selectedAgent.email}</div>
                </div>
              </div>
            )}

            {selectedAgent.experience && (
              <div className="detail-item">
                <span className="detail-icon">🎯</span>
                <div className="detail-content">
                  <div className="detail-label">سنوات الخبرة</div>
                  <div className="detail-value">{selectedAgent.experience} سنة</div>
                </div>
              </div>
            )}

            {selectedAgent.rating && (
              <div className="detail-item">
                <span className="detail-icon">⭐</span>
                <div className="detail-content">
                  <div className="detail-label">التقييم</div>
                  <div className="detail-value">
                    {'⭐'.repeat(Math.floor(selectedAgent.rating))} 
                    ({selectedAgent.rating}/5)
                  </div>
                </div>
              </div>
            )}
          </div>

          {selectedAgent.notes && (
            <div className="agent-notes">
              <div className="notes-label">ملاحظات:</div>
              <div className="notes-content">{selectedAgent.notes}</div>
            </div>
          )}
        </div>
      )}

      {agentsList.length === 0 && (
        <div className="no-agents-message">
          <span className="no-agents-icon">⚠️</span>
          <span>لا توجد وكلاء متاحين. يرجى إضافة وكلاء أولاً.</span>
        </div>
      )}
    </div>
  );
};

export default AgentSelector;
