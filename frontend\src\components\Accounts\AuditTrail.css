.audit-trail {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.audit-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.audit-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
}

.audit-sidebar {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filters-section {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.filters-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #2c3e50;
  font-size: 14px;
}

.filter-input,
.filter-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-input {
  width: 100%;
  padding: 8px 10px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 13px;
}

.stats-section {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 10px;
  border: 2px solid #e9ecef;
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 2px;
}

.type-stats,
.user-stats {
  margin-top: 20px;
}

.type-stats h4,
.user-stats h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: bold;
}

.type-list,
.user-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.type-item,
.user-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.type-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.type-name,
.user-name {
  flex: 1;
  font-size: 12px;
  color: #2c3e50;
}

.type-count,
.user-count {
  font-size: 12px;
  font-weight: bold;
  color: #7f8c8d;
  background: white;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.type-item.revenue {
  border-left: 3px solid #27ae60;
}

.type-item.expense {
  border-left: 3px solid #e74c3c;
}

.type-item.transfer {
  border-left: 3px solid #3498db;
}

.type-item.manual {
  border-left: 3px solid #9b59b6;
}

.audit-main {
  flex: 1;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: auto;
}

.transactions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f2f6;
}

.transactions-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.view-options {
  display: flex;
  gap: 5px;
}

.view-btn {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.view-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.transactions-table-container {
  overflow-x: auto;
  min-height: 400px;
}

.no-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
  text-align: center;
}

.no-transactions .icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.no-transactions h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.no-transactions p {
  margin: 0;
  font-size: 14px;
}

.transactions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.transactions-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.transactions-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  white-space: nowrap;
}

.transaction-row:hover {
  background: #f8f9fa;
}

.transaction-row.revenue {
  border-left: 4px solid #27ae60;
}

.transaction-row.expense {
  border-left: 4px solid #e74c3c;
}

.transaction-row.transfer {
  border-left: 4px solid #3498db;
}

.transaction-row.manual {
  border-left: 4px solid #9b59b6;
}

.transaction-id {
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: center;
}

.id-text {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.auto-badge {
  font-size: 12px;
  background: #f39c1220;
  color: #f39c12;
  padding: 2px 4px;
  border-radius: 4px;
}

.description {
  text-align: right;
  max-width: 200px;
}

.desc-main {
  font-weight: 500;
  margin-bottom: 2px;
}

.desc-ref {
  font-size: 11px;
  color: #7f8c8d;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  white-space: nowrap;
}

.type-badge.revenue {
  background: #27ae6020;
  color: #27ae60;
}

.type-badge.expense {
  background: #e74c3c20;
  color: #e74c3c;
}

.type-badge.transfer {
  background: #3498db20;
  color: #3498db;
}

.type-badge.manual {
  background: #9b59b620;
  color: #9b59b6;
}

.user-cell {
  text-align: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: #2c3e50;
}

.current-user-badge {
  background: #3498db20;
  color: #3498db;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: bold;
}

.amount {
  font-weight: bold;
  color: #2c3e50;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.status-badge.success {
  background: #27ae6020;
  color: #27ae60;
}

.created-date {
  font-size: 11px;
  color: #7f8c8d;
}

.actions {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.view-btn:hover {
  background: #3498db20;
}

.print-btn:hover {
  background: #9b59b620;
}

/* نموذج تفاصيل المعاملة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.transaction-details-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 2px solid #f1f2f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 25px;
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
}

.info-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.info-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: bold;
  color: #7f8c8d;
  font-size: 14px;
}

.info-item span {
  color: #2c3e50;
  font-size: 14px;
}

.description-section,
.notes-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.description-section h4,
.notes-section h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.description-section p,
.notes-section p {
  margin: 0;
  color: #2c3e50;
  line-height: 1.6;
}

.entries-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.entries-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.entries-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.entries-table th {
  background: #2c3e50;
  color: white;
  padding: 12px 8px;
  text-align: center;
  font-weight: bold;
}

.entries-table td {
  padding: 10px 8px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

.entries-table tfoot td {
  background: #f8f9fa;
  font-weight: bold;
  border-top: 2px solid #2c3e50;
}

.account-info {
  text-align: right;
}

.account-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.account-code {
  font-size: 12px;
  color: #7f8c8d;
}

.debit {
  color: #27ae60;
  font-weight: bold;
}

.credit {
  color: #e74c3c;
  font-weight: bold;
}

.total-debit {
  color: #27ae60;
}

.total-credit {
  color: #e74c3c;
}

.modal-footer {
  padding: 25px;
  border-top: 2px solid #f1f2f6;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .audit-content {
    flex-direction: column;
  }
  
  .audit-sidebar {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .audit-sidebar > * {
    min-width: 280px;
  }
  
  .audit-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .transactions-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .date-inputs {
    flex-direction: row;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .audit-trail {
    padding: 10px;
  }
  
  .audit-header,
  .filters-section,
  .stats-section,
  .audit-main,
  .modal-content {
    padding: 15px;
  }
  
  .transaction-details-modal {
    margin: 10px;
    max-width: none;
  }
  
  .audit-sidebar {
    flex-direction: column;
  }
  
  .audit-sidebar > * {
    min-width: auto;
  }
  
  .transactions-table {
    font-size: 11px;
  }
  
  .transactions-table th,
  .transactions-table td {
    padding: 6px 4px;
  }
}