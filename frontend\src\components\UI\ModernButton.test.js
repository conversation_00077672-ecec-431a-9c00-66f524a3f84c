import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ModernButton, { IconButton, ButtonGroup } from './ModernButton';

describe('ModernButton', () => {
  test('renders with default props', () => {
    render(<ModernButton>Click me</ModernButton>);
    
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('modern-button');
  });

  test('applies variant classes correctly', () => {
    const { rerender } = render(<ModernButton variant="primary">Primary</ModernButton>);
    expect(screen.getByRole('button')).toHaveClass('modern-button--primary');

    rerender(<ModernButton variant="secondary">Secondary</ModernButton>);
    expect(screen.getByRole('button')).toHaveClass('modern-button--secondary');

    rerender(<ModernButton variant="outline">Outline</ModernButton>);
    expect(screen.getByRole('button')).toHaveClass('modern-button--outline');
  });

  test('applies size classes correctly', () => {
    const { rerender } = render(<ModernButton size="sm">Small</ModernButton>);
    expect(screen.getByRole('button')).toHaveClass('modern-button--sm');

    rerender(<ModernButton size="lg">Large</ModernButton>);
    expect(screen.getByRole('button')).toHaveClass('modern-button--lg');
  });

  test('handles click events', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(<ModernButton onClick={handleClick}>Click me</ModernButton>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('is disabled when disabled prop is true', () => {
    render(<ModernButton disabled>Disabled</ModernButton>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('modern-button--disabled');
  });

  test('shows loading state', () => {
    render(<ModernButton loading>Loading</ModernButton>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('modern-button--loading');
    expect(button).toBeDisabled();
  });

  test('renders with icon', () => {
    render(<ModernButton icon="🏠">Home</ModernButton>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('🏠');
    expect(button).toHaveTextContent('Home');
  });

  test('renders as full width', () => {
    render(<ModernButton fullWidth>Full Width</ModernButton>);
    
    expect(screen.getByRole('button')).toHaveClass('modern-button--full-width');
  });

  test('forwards ref correctly', () => {
    const ref = React.createRef();
    render(<ModernButton ref={ref}>Button</ModernButton>);
    
    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
  });

  test('applies custom className', () => {
    render(<ModernButton className="custom-class">Custom</ModernButton>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('modern-button');
    expect(button).toHaveClass('custom-class');
  });

  test('passes through other props', () => {
    render(<ModernButton data-testid="custom-button" aria-label="Custom label">Button</ModernButton>);
    
    const button = screen.getByTestId('custom-button');
    expect(button).toHaveAttribute('aria-label', 'Custom label');
  });
});

describe('IconButton', () => {
  test('renders icon button correctly', () => {
    render(<IconButton icon="🔍" tooltip="Search" />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('icon-button');
    expect(button).toHaveTextContent('🔍');
  });

  test('shows tooltip on hover', async () => {
    const user = userEvent.setup();
    render(<IconButton icon="🔍" tooltip="Search" />);
    
    const button = screen.getByRole('button');
    await user.hover(button);
    
    // Note: This test might need adjustment based on tooltip implementation
    expect(button).toHaveAttribute('title', 'Search');
  });

  test('handles click events', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(<IconButton icon="🔍" onClick={handleClick} />);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies size classes', () => {
    const { rerender } = render(<IconButton icon="🔍" size="sm" />);
    expect(screen.getByRole('button')).toHaveClass('icon-button--sm');

    rerender(<IconButton icon="🔍" size="lg" />);
    expect(screen.getByRole('button')).toHaveClass('icon-button--lg');
  });
});

describe('ButtonGroup', () => {
  test('renders button group correctly', () => {
    render(
      <ButtonGroup>
        <ModernButton>First</ModernButton>
        <ModernButton>Second</ModernButton>
        <ModernButton>Third</ModernButton>
      </ButtonGroup>
    );
    
    const group = screen.getByRole('group');
    expect(group).toHaveClass('button-group');
    
    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(3);
  });

  test('applies orientation classes', () => {
    const { rerender } = render(
      <ButtonGroup orientation="horizontal">
        <ModernButton>Button</ModernButton>
      </ButtonGroup>
    );
    expect(screen.getByRole('group')).toHaveClass('button-group--horizontal');

    rerender(
      <ButtonGroup orientation="vertical">
        <ModernButton>Button</ModernButton>
      </ButtonGroup>
    );
    expect(screen.getByRole('group')).toHaveClass('button-group--vertical');
  });

  test('applies size to all buttons', () => {
    render(
      <ButtonGroup size="lg">
        <ModernButton>First</ModernButton>
        <ModernButton>Second</ModernButton>
      </ButtonGroup>
    );
    
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toHaveClass('modern-button--lg');
    });
  });
});

describe('Button Accessibility', () => {
  test('has proper ARIA attributes', () => {
    render(<ModernButton aria-pressed="false">Toggle</ModernButton>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-pressed', 'false');
  });

  test('supports keyboard navigation', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(<ModernButton onClick={handleClick}>Button</ModernButton>);
    
    const button = screen.getByRole('button');
    button.focus();
    
    await user.keyboard('{Enter}');
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    await user.keyboard(' ');
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  test('has proper focus styles', () => {
    render(<ModernButton>Button</ModernButton>);
    
    const button = screen.getByRole('button');
    button.focus();
    
    expect(button).toHaveFocus();
  });
});

describe('Button Performance', () => {
  test('does not re-render unnecessarily', () => {
    const renderSpy = jest.fn();
    
    const TestButton = React.memo(({ children, ...props }) => {
      renderSpy();
      return <ModernButton {...props}>{children}</ModernButton>;
    });
    
    const { rerender } = render(<TestButton>Button</TestButton>);
    expect(renderSpy).toHaveBeenCalledTimes(1);
    
    // Re-render with same props
    rerender(<TestButton>Button</TestButton>);
    expect(renderSpy).toHaveBeenCalledTimes(1);
    
    // Re-render with different props
    rerender(<TestButton variant="primary">Button</TestButton>);
    expect(renderSpy).toHaveBeenCalledTimes(2);
  });
});