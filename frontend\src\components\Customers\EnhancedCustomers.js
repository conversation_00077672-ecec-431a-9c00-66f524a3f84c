import React, { useState, useEffect } from 'react';
import { useNotifications } from '../UI/EnhancedNotifications';
import { useShortcuts } from '../UI/KeyboardShortcuts';
import VisaSelector from './VisaSelector';
import './EnhancedCustomers.css';

// 👥 مكون إدارة العملاء المحسن مع ربط التأشيرات
const EnhancedCustomers = () => {
  const { success, error, warning, info } = useNotifications();
  const { registerShortcut, unregisterShortcut } = useShortcuts();

  // 👥 حالة العملاء
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // 📋 حالة التأشيرات المتاحة
  const [availableVisas, setAvailableVisas] = useState([]);
  const [visaSearchTerm, setVisaSearchTerm] = useState('');

  // 📝 بيانات النموذج
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    email: '',
    idNumber: '',
    passportNumber: '',
    nationality: '',
    profession: '',
    address: {
      street: '',
      city: '',
      state: '',
      country: 'السعودية'
    },
    // 🔗 ربط التأشيرة
    assignedVisa: null,
    visaIssueNumber: '',
    visaRegistryNumber: '',
    notes: ''
  });

  // 📊 تحميل بيانات العملاء
  useEffect(() => {
    loadCustomers();
    loadAvailableVisas();
  }, []);

  // 🎧 الاستماع لإضافة تأشيرات جديدة
  useEffect(() => {
    const handleVisaAdded = (event) => {
      const { visa } = event.detail;
      setAvailableVisas(prev => [visa, ...prev]);
      info(`تأشيرة جديدة متاحة: ${visa.issueNumber}`, {
        title: 'تحديث المخزون',
        icon: '📋'
      });
    };

    document.addEventListener('visaAdded', handleVisaAdded);
    return () => document.removeEventListener('visaAdded', handleVisaAdded);
  }, [info]);

  // ⌨️ تسجيل اختصارات لوحة المفاتيح
  useEffect(() => {
    registerShortcut('ctrl+shift+c', 'newCustomer', 'إضافة عميل جديد', 'customers');
    registerShortcut('ctrl+shift+s', 'searchCustomer', 'البحث في العملاء', 'customers');

    const handleShortcutAction = (event) => {
      switch (event.detail.action) {
        case 'newCustomer':
          setShowAddForm(true);
          break;
        case 'searchCustomer':
          document.querySelector('.customer-search-input')?.focus();
          break;
      }
    };

    document.addEventListener('shortcutAction', handleShortcutAction);

    return () => {
      unregisterShortcut('ctrl+shift+c');
      unregisterShortcut('ctrl+shift+s');
      document.removeEventListener('shortcutAction', handleShortcutAction);
    };
  }, [registerShortcut, unregisterShortcut]);

  // 📊 تحميل العملاء
  const loadCustomers = async () => {
    setLoading(true);
    try {
      setTimeout(() => {
        const mockCustomers = [
          {
            id: 1,
            name: 'أحمد محمد علي',
            phone: '+966501234567',
            email: '<EMAIL>',
            idNumber: '1234567890',
            passportNumber: '*********',
            nationality: 'سعودي',
            profession: 'مهندس',
            address: {
              street: 'شارع الملك فهد',
              city: 'الرياض',
              state: 'الرياض',
              country: 'السعودية'
            },
            assignedVisa: {
              issueNumber: '********',
              registryNumber: '********',
              visaType: 'work',
              nationality: 'بنغلاديش'
            },
            visaIssueNumber: '********',
            visaRegistryNumber: '********',
            totalBookings: 5,
            totalSpent: 15000,
            lastBooking: '2024-01-15',
            createdAt: new Date().toISOString()
          }
        ];
        
        setCustomers(mockCustomers);
        setLoading(false);
        success('تم تحميل بيانات العملاء بنجاح');
      }, 1000);
    } catch (err) {
      error('فشل في تحميل بيانات العملاء');
      setLoading(false);
    }
  };

  // 📋 تحميل التأشيرات المتاحة
  const loadAvailableVisas = async () => {
    try {
      // محاكاة تحميل التأشيرات المتاحة من المخزون
      const mockVisas = [
        {
          id: 1,
          issueNumber: '********',
          registryNumber: '********',
          visaType: 'work',
          nationality: 'بنغلاديش',
          profession: 'عامل',
          sponsorName: 'شركة الرياض للتجارة',
          expiryDate: '2024-04-15',
          status: 'available',
          sellingPrice: 800
        },
        {
          id: 2,
          issueNumber: 'V2024003',
          registryNumber: '********',
          visaType: 'visit',
          nationality: 'مصر',
          profession: 'زيارة عائلية',
          sponsorName: 'محمد أحمد سالم',
          expiryDate: '2024-03-20',
          status: 'available',
          sellingPrice: 350
        }
      ];
      
      setAvailableVisas(mockVisas);
    } catch (err) {
      console.error('فشل في تحميل التأشيرات المتاحة:', err);
    }
  };

  // ➕ إضافة عميل جديد
  const handleAddCustomer = async (e) => {
    e.preventDefault();
    
    // التحقق من البيانات الأساسية
    if (!newCustomer.name || !newCustomer.phone) {
      warning('الاسم ورقم الهاتف مطلوبان');
      return;
    }

    // التحقق من عدم تكرار رقم الهاتف
    const existingCustomer = customers.find(c => c.phone === newCustomer.phone);
    if (existingCustomer) {
      error('رقم الهاتف موجود مسبقاً');
      return;
    }

    try {
      const customer = {
        ...newCustomer,
        id: Date.now(),
        totalBookings: 0,
        totalSpent: 0,
        lastBooking: null,
        createdAt: new Date().toISOString()
      };

      // 🔗 ربط التأشيرة إذا تم اختيارها
      if (newCustomer.assignedVisa) {
        // تحديث حالة التأشيرة في المخزون
        const visaUpdateEvent = new CustomEvent('visaStatusUpdate', {
          detail: {
            visaId: newCustomer.assignedVisa.id,
            newStatus: 'reserved',
            customerId: customer.id,
            customerName: customer.name
          }
        });
        document.dispatchEvent(visaUpdateEvent);

        success(`تم إضافة العميل وربط التأشيرة ${newCustomer.assignedVisa.issueNumber} بنجاح`, {
          title: 'عميل جديد مع تأشيرة',
          icon: '👤📋',
          actions: [
            {
              label: 'عرض التفاصيل',
              onClick: () => handleViewCustomer(customer)
            }
          ]
        });
      } else {
        success(`تم إضافة العميل ${customer.name} بنجاح`);
      }

      setCustomers([customer, ...customers]);
      
      // إعادة تعيين النموذج
      setNewCustomer({
        name: '',
        phone: '',
        email: '',
        idNumber: '',
        passportNumber: '',
        nationality: '',
        profession: '',
        address: {
          street: '',
          city: '',
          state: '',
          country: 'السعودية'
        },
        assignedVisa: null,
        visaIssueNumber: '',
        visaRegistryNumber: '',
        notes: ''
      });

      setShowAddForm(false);

    } catch (err) {
      error('فشل في إضافة العميل');
    }
  };

  // 🔍 البحث في العملاء
  const filteredCustomers = customers.filter(customer => {
    const searchLower = searchTerm.toLowerCase();
    return (
      customer.name.toLowerCase().includes(searchLower) ||
      customer.phone.includes(searchTerm) ||
      customer.email?.toLowerCase().includes(searchLower) ||
      customer.idNumber?.includes(searchTerm) ||
      customer.passportNumber?.toLowerCase().includes(searchLower) ||
      customer.visaIssueNumber?.toLowerCase().includes(searchLower) ||
      customer.visaRegistryNumber?.toLowerCase().includes(searchLower)
    );
  });

  // 🔍 البحث في التأشيرات المتاحة
  const filteredVisas = availableVisas.filter(visa => {
    if (!visaSearchTerm) return visa.status === 'available';
    
    const searchLower = visaSearchTerm.toLowerCase();
    return (
      visa.status === 'available' && (
        visa.issueNumber.toLowerCase().includes(searchLower) ||
        visa.registryNumber.toLowerCase().includes(searchLower) ||
        visa.nationality.toLowerCase().includes(searchLower) ||
        visa.sponsorName.toLowerCase().includes(searchLower)
      )
    );
  });

  // 🔗 ربط تأشيرة بالعميل
  const handleVisaSelect = (visa) => {
    setNewCustomer({
      ...newCustomer,
      assignedVisa: visa,
      visaIssueNumber: visa.issueNumber,
      visaRegistryNumber: visa.registryNumber
    });
    
    info(`تم اختيار التأشيرة ${visa.issueNumber} للعميل`, {
      icon: '🔗'
    });
  };

  // 👁️ عرض تفاصيل العميل
  const handleViewCustomer = (customer) => {
    setSelectedCustomer(customer);
    setShowDetailsModal(true);
  };

  // 📊 إحصائيات العملاء
  const stats = {
    total: customers.length,
    withVisas: customers.filter(c => c.assignedVisa).length,
    activeBookings: customers.reduce((sum, c) => sum + c.totalBookings, 0),
    totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0)
  };

  if (loading) {
    return (
      <div className="customers-loading">
        <div className="loading-spinner-advanced"></div>
        <p>جاري تحميل بيانات العملاء...</p>
      </div>
    );
  }

  return (
    <div className="enhanced-customers">
      {/* 🎯 رأس الصفحة */}
      <div className="customers-header">
        <div className="header-title">
          <h1>👥 إدارة العملاء</h1>
          <p>إدارة العملاء مع ربط التأشيرات تلقائياً</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إضافة عميل جديد
          </button>
        </div>
      </div>

      {/* 📊 إحصائيات سريعة */}
      <div className="customers-stats">
        <div className="stat-card total">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>إجمالي العملاء</h3>
            <div className="stat-value">{stats.total}</div>
          </div>
        </div>
        <div className="stat-card with-visas">
          <div className="stat-icon">📋</div>
          <div className="stat-content">
            <h3>مع تأشيرات</h3>
            <div className="stat-value">{stats.withVisas}</div>
          </div>
        </div>
        <div className="stat-card bookings">
          <div className="stat-icon">✈️</div>
          <div className="stat-content">
            <h3>إجمالي الحجوزات</h3>
            <div className="stat-value">{stats.activeBookings}</div>
          </div>
        </div>
        <div className="stat-card revenue">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>إجمالي الإيرادات</h3>
            <div className="stat-value">{stats.totalRevenue.toLocaleString()} ر.س</div>
          </div>
        </div>
      </div>

      {/* 🔍 البحث */}
      <div className="customers-search">
        <input
          type="text"
          className="customer-search-input"
          placeholder="البحث بالاسم، الهاتف، البريد، الهوية، الجواز، أو رقم التأشيرة..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* 📋 قائمة العملاء */}
      <div className="customers-grid">
        {filteredCustomers.map(customer => (
          <div key={customer.id} className="customer-card">
            <div className="customer-header">
              <div className="customer-avatar">
                {customer.name.charAt(0)}
              </div>
              <div className="customer-info">
                <h3>{customer.name}</h3>
                <p>{customer.phone}</p>
                {customer.email && <p className="email">{customer.email}</p>}
              </div>
              {customer.assignedVisa && (
                <div className="visa-badge">
                  <span className="visa-icon">📋</span>
                  <span className="visa-number">{customer.assignedVisa.issueNumber}</span>
                </div>
              )}
            </div>
            
            <div className="customer-details">
              <div className="detail-row">
                <span className="label">الهوية:</span>
                <span className="value">{customer.idNumber || 'غير محدد'}</span>
              </div>
              <div className="detail-row">
                <span className="label">الجواز:</span>
                <span className="value">{customer.passportNumber || 'غير محدد'}</span>
              </div>
              {customer.assignedVisa && (
                <>
                  <div className="detail-row visa-info">
                    <span className="label">رقم الصادر:</span>
                    <span className="value visa-number">{customer.visaIssueNumber}</span>
                  </div>
                  <div className="detail-row visa-info">
                    <span className="label">رقم السجل:</span>
                    <span className="value visa-number">{customer.visaRegistryNumber}</span>
                  </div>
                </>
              )}
            </div>

            <div className="customer-stats">
              <div className="stat">
                <span className="stat-value">{customer.totalBookings}</span>
                <span className="stat-label">حجز</span>
              </div>
              <div className="stat">
                <span className="stat-value">{customer.totalSpent.toLocaleString()}</span>
                <span className="stat-label">ر.س</span>
              </div>
            </div>

            <div className="customer-actions">
              <button
                className="action-btn view"
                onClick={() => handleViewCustomer(customer)}
                title="عرض التفاصيل"
              >
                👁️
              </button>
              <button
                className="action-btn edit"
                title="تعديل"
              >
                ✏️
              </button>
              <button
                className="action-btn history"
                title="تاريخ الحجوزات"
              >
                📊
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* ➕ نموذج إضافة عميل جديد */}
      {showAddForm && (
        <div className="customer-form-overlay">
          <div className="customer-form-modal">
            <div className="form-header">
              <h2>➕ إضافة عميل جديد</h2>
              <button 
                className="close-btn"
                onClick={() => setShowAddForm(false)}
              >
                ✕
              </button>
            </div>
            
            <form onSubmit={handleAddCustomer} className="customer-form">
              <div className="form-sections">
                {/* معلومات شخصية */}
                <div className="form-section">
                  <h3>👤 المعلومات الشخصية</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>الاسم الكامل *</label>
                      <input
                        type="text"
                        value={newCustomer.name}
                        onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                        placeholder="أحمد محمد علي"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label>رقم الهاتف *</label>
                      <input
                        type="tel"
                        value={newCustomer.phone}
                        onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                        placeholder="+966501234567"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label>البريد الإلكتروني</label>
                      <input
                        type="email"
                        value={newCustomer.email}
                        onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="form-group">
                      <label>الجنسية</label>
                      <input
                        type="text"
                        value={newCustomer.nationality}
                        onChange={(e) => setNewCustomer({...newCustomer, nationality: e.target.value})}
                        placeholder="سعودي"
                      />
                    </div>
                  </div>
                </div>

                {/* الوثائق */}
                <div className="form-section">
                  <h3>📄 الوثائق</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>رقم الهوية</label>
                      <input
                        type="text"
                        value={newCustomer.idNumber}
                        onChange={(e) => setNewCustomer({...newCustomer, idNumber: e.target.value})}
                        placeholder="1234567890"
                      />
                    </div>
                    <div className="form-group">
                      <label>رقم الجواز</label>
                      <input
                        type="text"
                        value={newCustomer.passportNumber}
                        onChange={(e) => setNewCustomer({...newCustomer, passportNumber: e.target.value})}
                        placeholder="*********"
                      />
                    </div>
                  </div>
                </div>

                {/* 🔗 ربط التأشيرة */}
                <div className="form-section visa-section">
                  <h3>📋 ربط التأشيرة (اختياري)</h3>
                  <VisaSelector
                    availableVisas={filteredVisas}
                    selectedVisa={newCustomer.assignedVisa}
                    onVisaSelect={handleVisaSelect}
                    searchTerm={visaSearchTerm}
                    onSearchChange={setVisaSearchTerm}
                  />
                  
                  {newCustomer.assignedVisa && (
                    <div className="selected-visa-info">
                      <h4>✅ التأشيرة المختارة:</h4>
                      <div className="visa-details">
                        <p><strong>رقم الصادر:</strong> {newCustomer.assignedVisa.issueNumber}</p>
                        <p><strong>رقم السجل:</strong> {newCustomer.assignedVisa.registryNumber}</p>
                        <p><strong>النوع:</strong> {newCustomer.assignedVisa.visaType === 'work' ? 'عمل' : 'زيارة'}</p>
                        <p><strong>الجنسية:</strong> {newCustomer.assignedVisa.nationality}</p>
                        <p><strong>السعر:</strong> {newCustomer.assignedVisa.sellingPrice} ر.س</p>
                      </div>
                      <button
                        type="button"
                        className="btn btn-secondary btn-sm"
                        onClick={() => handleVisaSelect(null)}
                      >
                        إلغاء الربط
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="btn btn-primary">
                  💾 حفظ العميل
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowAddForm(false)}
                >
                  ❌ إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 👁️ مودال تفاصيل العميل */}
      {showDetailsModal && selectedCustomer && (
        <div className="customer-details-overlay">
          <div className="customer-details-modal">
            <div className="modal-header">
              <h2>👤 تفاصيل العميل</h2>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ✕
              </button>
            </div>
            
            <div className="customer-full-details">
              <div className="details-section">
                <h3>📋 المعلومات الأساسية</h3>
                <div className="details-grid">
                  <div className="detail-item">
                    <label>الاسم:</label>
                    <span>{selectedCustomer.name}</span>
                  </div>
                  <div className="detail-item">
                    <label>الهاتف:</label>
                    <span>{selectedCustomer.phone}</span>
                  </div>
                  <div className="detail-item">
                    <label>البريد:</label>
                    <span>{selectedCustomer.email || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>الجنسية:</label>
                    <span>{selectedCustomer.nationality || 'غير محدد'}</span>
                  </div>
                </div>
              </div>

              {selectedCustomer.assignedVisa && (
                <div className="details-section">
                  <h3>📋 معلومات التأشيرة</h3>
                  <div className="visa-details-card">
                    <div className="visa-header">
                      <span className="visa-type">
                        {selectedCustomer.assignedVisa.visaType === 'work' ? '💼 عمل' : '👥 زيارة'}
                      </span>
                      <span className="visa-status available">متاحة</span>
                    </div>
                    <div className="visa-info-grid">
                      <div className="visa-info-item">
                        <label>رقم الصادر:</label>
                        <span className="visa-number">{selectedCustomer.assignedVisa.issueNumber}</span>
                      </div>
                      <div className="visa-info-item">
                        <label>رقم السجل:</label>
                        <span className="visa-number">{selectedCustomer.assignedVisa.registryNumber}</span>
                      </div>
                      <div className="visa-info-item">
                        <label>الجنسية:</label>
                        <span>{selectedCustomer.assignedVisa.nationality}</span>
                      </div>
                      <div className="visa-info-item">
                        <label>المهنة:</label>
                        <span>{selectedCustomer.assignedVisa.profession}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="details-section">
                <h3>📊 إحصائيات العميل</h3>
                <div className="customer-stats-detailed">
                  <div className="stat-item">
                    <span className="stat-icon">📋</span>
                    <div className="stat-info">
                      <span className="stat-value">{selectedCustomer.totalBookings}</span>
                      <span className="stat-label">إجمالي الحجوزات</span>
                    </div>
                  </div>
                  <div className="stat-item">
                    <span className="stat-icon">💰</span>
                    <div className="stat-info">
                      <span className="stat-value">{selectedCustomer.totalSpent.toLocaleString()} ر.س</span>
                      <span className="stat-label">إجمالي المبلغ المدفوع</span>
                    </div>
                  </div>
                  <div className="stat-item">
                    <span className="stat-icon">📅</span>
                    <div className="stat-info">
                      <span className="stat-value">
                        {selectedCustomer.lastBooking ? 
                          new Date(selectedCustomer.lastBooking).toLocaleDateString('ar-SA') : 
                          'لا يوجد'
                        }
                      </span>
                      <span className="stat-label">آخر حجز</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedCustomers;
