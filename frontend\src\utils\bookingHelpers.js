// مساعدات ووظائف مشتركة للحجوزات

// تنسيق العملة
export const formatCurrency = (amount, currency = 'SAR') => {
  if (!amount && amount !== 0) return '-';
  
  const formattedAmount = parseFloat(amount).toLocaleString('ar-SA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  
  const currencySymbols = {
    'SAR': 'ر.س',
    'USD': '$',
    'EUR': '€',
    'GBP': '£'
  };
  
  return `${formattedAmount} ${currencySymbols[currency] || currency}`;
};

// تنسيق التاريخ
export const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// تنسيق التاريخ والوقت
export const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  
  const date = new Date(dateString);
  return date.toLocaleString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// تنسيق رقم الهاتف
export const formatPhoneNumber = (phone) => {
  if (!phone) return '-';
  
  // إزالة جميع الرموز غير الرقمية
  const cleaned = phone.replace(/\D/g, '');
  
  // تنسيق الرقم السعودي
  if (cleaned.startsWith('966')) {
    return `+966 ${cleaned.slice(3, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;
  } else if (cleaned.startsWith('05')) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  
  return phone;
};

// ألوان الحالات
export const getStatusColor = (status) => {
  const colors = {
    'pending': '#f39c12',
    'confirmed': '#27ae60',
    'cancelled': '#e74c3c',
    'completed': '#2ecc71',
    'in-progress': '#3498db',
    'ready': '#9b59b6',
    'delivered': '#1abc9c',
    'ministry-done': '#16a085',
    'embassy-done': '#8e44ad'
  };
  
  return colors[status] || '#95a5a6';
};

// نصوص الحالات
export const getStatusText = (status) => {
  const texts = {
    'pending': 'قيد الانتظار',
    'confirmed': 'مؤكد',
    'cancelled': 'ملغي',
    'completed': 'مكتمل',
    'in-progress': 'قيد التنفيذ',
    'ready': 'جاهز',
    'delivered': 'تم التسليم',
    'ministry-done': 'تم في الوزارة',
    'embassy-done': 'تم في السفارة'
  };
  
  return texts[status] || status;
};

// ألوان حالة الدفع
export const getPaymentStatusColor = (paymentStatus) => {
  const colors = {
    'paid': '#27ae60',
    'unpaid': '#e74c3c',
    'partial': '#f39c12',
    'refunded': '#9b59b6'
  };
  
  return colors[paymentStatus] || '#95a5a6';
};

// نصوص حالة الدفع
export const getPaymentStatusText = (paymentStatus) => {
  const texts = {
    'paid': 'مدفوع',
    'unpaid': 'غير مدفوع',
    'partial': 'مدفوع جزئياً',
    'refunded': 'مسترد'
  };
  
  return texts[paymentStatus] || paymentStatus;
};

// تصفية الحجوزات
export const filterBookings = (bookings, searchTerm, filterStatus, additionalFilters = {}) => {
  return bookings.filter(booking => {
    // تصفية النص
    const matchesSearch = !searchTerm || 
      booking.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.customerPhone?.includes(searchTerm) ||
      booking.bookingReference?.toLowerCase().includes(searchTerm.toLowerCase());
    
    // تصفية الحالة
    const matchesStatus = filterStatus === 'all' || booking.status === filterStatus;
    
    // تصفيات إضافية
    let matchesAdditional = true;
    Object.keys(additionalFilters).forEach(key => {
      if (additionalFilters[key] !== 'all' && booking[key] !== additionalFilters[key]) {
        matchesAdditional = false;
      }
    });
    
    return matchesSearch && matchesStatus && matchesAdditional;
  });
};

// إنشاء مرجع حجز
export const generateBookingReference = (type, existingBookings = []) => {
  const prefixes = {
    'flight': 'FL',
    'hajj': 'HJ',
    'car': 'CR',
    'umrah': 'UM',
    'bus': 'BUS',
    'passport': 'PP',
    'document': 'DOC'
  };
  
  const prefix = prefixes[type] || 'BK';
  const year = new Date().getFullYear();
  const nextNumber = existingBookings.length + 1;
  
  return `${prefix}${year}${String(nextNumber).padStart(3, '0')}`;
};

// حساب المبلغ المتبقي
export const calculateRemainingAmount = (totalAmount, paidAmount) => {
  const total = parseFloat(totalAmount) || 0;
  const paid = parseFloat(paidAmount) || 0;
  return Math.max(0, total - paid);
};

// التحقق من صحة البيانات
export const validateBookingData = (bookingData, requiredFields = []) => {
  const errors = [];
  
  // التحقق من الحقول المطلوبة
  requiredFields.forEach(field => {
    if (!bookingData[field] || bookingData[field].toString().trim() === '') {
      errors.push(`${field} مطلوب`);
    }
  });
  
  // التحقق من صحة الإيميل
  if (bookingData.customerEmail && !/\S+@\S+\.\S+/.test(bookingData.customerEmail)) {
    errors.push('الإيميل غير صحيح');
  }
  
  // التحقق من صحة رقم الهاتف
  if (bookingData.customerPhone && !/^(\+966|0)?5\d{8}$/.test(bookingData.customerPhone.replace(/\s/g, ''))) {
    errors.push('رقم الهاتف غير صحيح');
  }
  
  // التحقق من المبالغ
  if (bookingData.amount && isNaN(parseFloat(bookingData.amount))) {
    errors.push('المبلغ يجب أن يكون رقماً');
  }
  
  if (bookingData.paidAmount && isNaN(parseFloat(bookingData.paidAmount))) {
    errors.push('المبلغ المدفوع يجب أن يكون رقماً');
  }
  
  return errors;
};

// تصدير جميع الوظائف
export default {
  formatCurrency,
  formatDate,
  formatDateTime,
  formatPhoneNumber,
  getStatusColor,
  getStatusText,
  getPaymentStatusColor,
  getPaymentStatusText,
  filterBookings,
  generateBookingReference,
  calculateRemainingAmount,
  validateBookingData
};