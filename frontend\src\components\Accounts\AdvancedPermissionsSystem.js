import React, { useState, useEffect } from 'react';
import './AdvancedPermissionsSystem.css';

// نظام الصلاحيات الشامل لجميع النوافذ والعمليات
const AdvancedPermissionsSystem = ({ user, onPermissionsChange }) => {
  const [selectedModule, setSelectedModule] = useState('dashboard');
  const [userPermissions, setUserPermissions] = useState(user?.permissions || []);
  const [searchTerm, setSearchTerm] = useState('');

  // تعريف جميع وحدات النظام والصلاحيات المتاحة
  const systemModules = {
    // لوحة التحكم
    dashboard: {
      name: 'لوحة التحكم',
      icon: '🏠',
      permissions: {
        view: 'عرض لوحة التحكم',
        manage: 'إدارة لوحة التحكم',
        customize: 'تخصيص لوحة التحكم',
        export_data: 'تصدير بيانات لوحة التحكم',
        view_analytics: 'عرض التحليلات',
        manage_widgets: 'إدارة الودجات'
      }
    },
    
    // إدارة المستخدمين
    users: {
      name: 'إدارة المستخدمين',
      icon: '👥',
      permissions: {
        view: 'عرض المستخدمين',
        create: 'إضافة مستخدم جديد',
        edit: 'تعديل بيانات المستخدم',
        delete: 'حذف المستخدم',
        manage_roles: 'إدارة الأدوار',
        manage_permissions: 'إدارة الصلاحيات',
        reset_password: 'إعادة تعيين كلمة المرور',
        activate_deactivate: 'تفعيل/إلغاء تفعيل المستخدم',
        view_activity_log: 'عرض سجل النشاطات',
        export: 'تصدير بيانات المستخدمين',
        bulk_operations: 'العمليات المجمعة',
        impersonate: 'تسجيل الدخول كمستخدم آخر'
      }
    },
    
    // الحجوزات العامة
    bookings: {
      name: 'إدارة الحجوزات',
      icon: '📋',
      permissions: {
        view: 'عرض الحجوزات',
        create: 'إنشاء حجز جديد',
        edit: 'تعديل الحجز',
        delete: 'حذف الحجز',
        approve: 'الموافقة على الحجز',
        cancel: 'إلغاء الحجز',
        confirm: 'تأكيد الحجز',
        print: 'طباعة الحجز',
        export: 'تصدير الحجوزات',
        manage_payments: 'إدارة المدفوعات',
        view_history: 'عرض تاريخ الحجز',
        bulk_operations: 'العمليات المجمعة',
        duplicate: 'نسخ الحجز',
        transfer: 'نقل الحجز'
      }
    },
    
    // حجوزات الطيران
    flight_bookings: {
      name: 'حجوزات الطيران',
      icon: '✈️',
      permissions: {
        view: 'عرض حجوزات الطيران',
        create: 'إنشاء حجز طيران',
        edit: 'تعديل حجز الطيران',
        delete: 'حذف حجز الطيران',
        manage_tickets: 'إدارة التذاكر',
        change_booking: 'تغيير الحجز',
        refund: 'استرداد الحجز',
        issue_tickets: 'إصدار التذاكر',
        void_tickets: 'إلغاء التذاكر',
        manage_pnr: 'إدارة رقم الحجز'
      }
    },
    
    // حجوزات العمرة
    umrah_bookings: {
      name: 'حجوزات العمرة',
      icon: '🕋',
      permissions: {
        view: 'عرض حجوزات العمرة',
        create: 'إنشاء حجز عمرة',
        edit: 'تعديل حجز العمرة',
        delete: 'حذف حجز العمرة',
        manage_packages: 'إدارة باقات العمرة',
        manage_visas: 'إدارة تأشيرات العمرة',
        manage_hotels: 'إدارة الفنادق',
        manage_transport: 'إدارة النقل'
      }
    },
    
    // حجوزات الحج
    hajj_bookings: {
      name: 'حجوزات الحج',
      icon: '🕌',
      permissions: {
        view: 'عرض حجوزات الحج',
        create: 'إنشاء حجز حج',
        edit: 'تعديل حجز الحج',
        delete: 'حذف حجز الحج',
        manage_packages: 'إدارة باقات الحج',
        manage_permits: 'إدارة تصاريح الحج',
        manage_camps: 'إدارة المخيمات',
        manage_transportation: 'إدارة النقل'
      }
    },
    
    // حجوزات الحافلات
    bus_bookings: {
      name: 'حجوزات الحافلات',
      icon: '🚌',
      permissions: {
        view: 'عرض حجوزات الحافلات',
        create: 'إنشاء حجز حافلة',
        edit: 'تعديل حجز الحافلة',
        delete: 'حذف حجز الحافلة',
        manage_routes: 'إدارة الطرق',
        manage_schedules: 'إدارة الجداول'
      }
    },
    
    // حجوزات السيارات
    car_bookings: {
      name: 'حجوزات السيارات',
      icon: '🚗',
      permissions: {
        view: 'عرض حجوزات السيارات',
        create: 'إنشاء حجز سيارة',
        edit: 'تعديل حجز السيارة',
        delete: 'حذف حجز السيارة',
        manage_fleet: 'إدارة الأسطول',
        manage_drivers: 'إدارة السائقين'
      }
    },
    
    // خدمات الجوازات
    passport_services: {
      name: 'خدمات الجوازات',
      icon: '📘',
      permissions: {
        view: 'عرض خدمات الجوازات',
        create: 'إنشاء طلب جواز',
        edit: 'تعديل طلب الجواز',
        delete: 'حذف طلب الجواز',
        track_status: 'تتبع حالة الطلب',
        manage_documents: 'إدارة الوثائق'
      }
    },
    
    // توثيق الوثائق
    document_authentication: {
      name: 'توثيق الوثائق',
      icon: '📋',
      permissions: {
        view: 'عرض طلبات التوثيق',
        create: 'إنشاء طلب توثيق',
        edit: 'تعديل طلب التوثيق',
        delete: 'حذف طلب التوثيق',
        approve: 'الموافقة على التوثيق',
        track_status: 'تتبع حالة التوثيق'
      }
    },
    
    // العملاء
    customers: {
      name: 'إدارة العملاء',
      icon: '👤',
      permissions: {
        view: 'عرض العملاء',
        create: 'إضافة عميل جديد',
        edit: 'تعديل بيانات العميل',
        delete: 'حذف العميل',
        view_history: 'عرض تاريخ العميل',
        manage_documents: 'إدارة وثائق العميل',
        export: 'تصدير بيانات العملاء',
        merge_customers: 'دمج العملاء',
        view_financial_info: 'عرض المعلومات المالية',
        manage_loyalty: 'إدارة برنامج الولاء',
        send_notifications: 'إرسال الإشعارات'
      }
    },
    
    // الموردين
    suppliers: {
      name: 'إدارة الموردين',
      icon: '🏢',
      permissions: {
        view: 'عرض الموردين',
        create: 'إضافة مورد جديد',
        edit: 'تعديل بيانات المورد',
        delete: 'حذف المورد',
        manage_contracts: 'إدارة العقود',
        view_performance: 'عرض أداء المورد',
        export: 'تصدير بيانات الموردين',
        approve_payments: 'الموافقة على المدفوعات',
        manage_ratings: 'إدارة التقييمات',
        negotiate_rates: 'التفاوض على الأسعار'
      }
    },
    
    // الوكلاء
    agents: {
      name: 'إدارة الوكلاء',
      icon: '🤝',
      permissions: {
        view: 'عرض الوكلاء',
        create: 'إضافة وكيل جديد',
        edit: 'تعديل بيانات الوكيل',
        delete: 'حذف الوكيل',
        manage_commissions: 'إدارة العمولات',
        view_sales: 'عرض مبيعات الوكيل',
        export: 'تصدير بيانات الوكلاء',
        manage_territories: 'إدارة المناطق',
        set_targets: 'تحديد الأهداف'
      }
    },
    
    // المبيعات
    sales: {
      name: 'إدارة المبيعات',
      icon: '💰',
      permissions: {
        view: 'عرض المبيعات',
        create: 'إنشاء عملية بيع',
        edit: 'تعديل عملية البيع',
        delete: 'حذف عملية البيع',
        manage_invoices: 'إدارة الفواتير',
        manage_payments: 'إدارة المدفوعات',
        apply_discounts: 'تطبيق الخصومات',
        refund: 'استرداد المبيعات',
        export: 'تصدير بيانات المبيعات',
        view_reports: 'عرض تقارير المبيعات',
        manage_quotations: 'إدارة عروض الأسعار',
        approve_discounts: 'الموافقة على الخصومات'
      }
    },
    
    // المشتريات
    purchases: {
      name: 'إدارة المشتريات',
      icon: '🛒',
      permissions: {
        view: 'عرض المشتريات',
        create: 'إنشاء طلب شراء',
        edit: 'تعديل طلب الشراء',
        delete: 'حذف طلب الشراء',
        approve: 'الموافقة على الشراء',
        receive_goods: 'استلام البضائع',
        manage_invoices: 'إدارة فواتير الشراء',
        export: 'تصدير بيانات المشتريات',
        manage_vendors: 'إدارة البائعين',
        negotiate_prices: 'التفاوض على الأسعار'
      }
    },

    // المخزون
    inventory: {
      name: 'إدارة المخزون',
      icon: '📦',
      permissions: {
        view: 'عرض المخزون',
        create: 'إضافة عنصر جديد',
        edit: 'تعديل عنصر المخزون',
        delete: 'حذف عنصر المخزون',
        adjust_quantities: 'تعديل الكميات',
        transfer: 'نقل المخزون',
        view_movements: 'عرض حركات المخزون',
        export: 'تصدير بيانات المخزون',
        manage_locations: 'إدارة المواقع',
        stocktaking: 'الجرد',
        set_reorder_levels: 'تحديد مستويات إعادة الطلب'
      }
    },

    // مخزون التأشيرات
    visa_inventory: {
      name: 'مخزون التأشيرات',
      icon: '📋',
      permissions: {
        view: 'عرض مخزون التأشيرات',
        create: 'إضافة تأشيرة جديدة',
        edit: 'تعديل بيانات التأشيرة',
        delete: 'حذف التأشيرة',
        allocate: 'تخصيص التأشيرة',
        track_status: 'تتبع حالة التأشيرة',
        manage_types: 'إدارة أنواع التأشيرات',
        export: 'تصدير بيانات التأشيرات'
      }
    },

    // المالية والمحاسبة
    finance: {
      name: 'إدارة المالية',
      icon: '💼',
      permissions: {
        view: 'عرض البيانات المالية',
        create_entries: 'إنشاء قيود محاسبية',
        edit_entries: 'تعديل القيود المحاسبية',
        delete_entries: 'حذف القيود المحاسبية',
        approve_entries: 'الموافقة على القيود',
        post_entries: 'ترحيل القيود',
        manage_accounts: 'إدارة الحسابات',
        view_trial_balance: 'عرض ميزان المراجعة',
        view_income_statement: 'عرض قائمة الدخل',
        view_balance_sheet: 'عرض الميزانية العمومية',
        export_reports: 'تصدير التقارير المالية',
        manage_budgets: 'إدارة الميزانيات',
        cash_flow: 'إدارة التدفق النقدي',
        bank_reconciliation: 'تسوية البنك',
        manage_currencies: 'إدارة العملات'
      }
    },

    // الحسابات
    accounts: {
      name: 'إدارة الحسابات',
      icon: '📚',
      permissions: {
        view: 'عرض الحسابات',
        create: 'إنشاء حساب جديد',
        edit: 'تعديل الحساب',
        delete: 'حذف الحساب',
        activate_deactivate: 'تفعيل/إلغاء تفعيل الحساب',
        view_transactions: 'عرض معاملات الحساب',
        export: 'تصدير بيانات الحسابات',
        manage_chart: 'إدارة دليل الحسابات',
        set_opening_balances: 'تحديد الأرصدة الافتتاحية'
      }
    },

    // التقارير
    reports: {
      name: 'إدارة التقارير',
      icon: '📊',
      permissions: {
        view: 'عرض التقارير',
        create: 'إنشاء تقرير جديد',
        edit: 'تعديل التقرير',
        delete: 'حذف التقرير',
        export: 'تصدير التقارير',
        schedule: 'جدولة التقارير',
        share: 'مشاركة التقارير',
        customize: 'تخصيص التقارير',
        financial_reports: 'التقارير المالية',
        sales_reports: 'تقارير المبيعات',
        operational_reports: 'التقارير التشغيلية',
        analytics: 'التحليلات المتقدمة'
      }
    },

    // القوالب
    templates: {
      name: 'إدارة القوالب',
      icon: '📄',
      permissions: {
        view: 'عرض القوالب',
        create: 'إنشاء قالب جديد',
        edit: 'تعديل القالب',
        delete: 'حذف القالب',
        duplicate: 'نسخ القالب',
        export: 'تصدير القوالب',
        import: 'استيراد القوالب',
        manage_categories: 'إدارة فئات القوالب',
        set_default: 'تحديد القالب الافتراضي'
      }
    },

    // الإعدادات
    settings: {
      name: 'إعدادات النظام',
      icon: '⚙️',
      permissions: {
        view: 'عرض الإعدادات',
        edit_general: 'تعديل الإعدادات العامة',
        edit_security: 'تعديل إعدادات الأمان',
        edit_notifications: 'تعديل إعدادات الإشعارات',
        edit_integrations: 'تعديل إعدادات التكامل',
        backup: 'النسخ الاحتياطي',
        restore: 'الاستعادة',
        view_logs: 'عرض سجلات النظام',
        manage_themes: 'إدارة المظاهر',
        email_settings: 'إعدادات البريد الإلكتروني',
        sms_settings: 'إعدادات الرسائل النصية',
        payment_gateways: 'إعدادات بوابات الدفع'
      }
    },

    // إدارة النظام
    system: {
      name: 'إدارة النظام',
      icon: '🔧',
      permissions: {
        backup: 'النسخ الاحتياطي',
        restore: 'الاستعادة',
        maintenance: 'صيانة النظام',
        view_logs: 'عرض سجلات النظام',
        manage_database: 'إدارة قاعدة البيانات',
        system_monitoring: 'مراقبة النظام',
        update_system: 'تحديث النظام',
        manage_licenses: 'إدارة التراخيص',
        security_audit: 'مراجعة الأمان',
        performance_tuning: 'تحسين الأداء'
      }
    }
  };

  // فلترة الوحدات حسب البحث
  const filteredModules = Object.entries(systemModules).filter(([key, module]) =>
    module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    Object.values(module.permissions).some(permission =>
      permission.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // التحقق من وجود صلاحية
  const hasPermission = (module, permission) => {
    return userPermissions.includes(`${module}.${permission}`) || userPermissions.includes('*');
  };

  // تبديل الصلاحية
  const togglePermission = (module, permission) => {
    const permissionKey = `${module}.${permission}`;
    let newPermissions;
    
    if (hasPermission(module, permission)) {
      newPermissions = userPermissions.filter(p => p !== permissionKey);
    } else {
      newPermissions = [...userPermissions, permissionKey];
    }
    
    setUserPermissions(newPermissions);
    onPermissionsChange && onPermissionsChange(newPermissions);
  };

  // تبديل جميع صلاحيات الوحدة
  const toggleModulePermissions = (moduleKey) => {
    const module = systemModules[moduleKey];
    const modulePermissions = Object.keys(module.permissions).map(p => `${moduleKey}.${p}`);
    const hasAllPermissions = modulePermissions.every(p => userPermissions.includes(p));
    
    let newPermissions;
    if (hasAllPermissions) {
      // إزالة جميع صلاحيات الوحدة
      newPermissions = userPermissions.filter(p => !modulePermissions.includes(p));
    } else {
      // إضافة جميع صلاحيات الوحدة
      newPermissions = [...new Set([...userPermissions, ...modulePermissions])];
    }
    
    setUserPermissions(newPermissions);
    onPermissionsChange && onPermissionsChange(newPermissions);
  };

  return (
    <div className="advanced-permissions-system">
      <div className="permissions-header">
        <h3>🔐 نظام الصلاحيات المتقدم</h3>
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في الصلاحيات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      <div className="permissions-content">
        <div className="modules-sidebar">
          <h4>وحدات النظام</h4>
          {filteredModules.map(([key, module]) => (
            <div
              key={key}
              className={`module-item ${selectedModule === key ? 'active' : ''}`}
              onClick={() => setSelectedModule(key)}
            >
              <span className="module-icon">{module.icon}</span>
              <span className="module-name">{module.name}</span>
              <span className="permissions-count">
                {Object.keys(module.permissions).filter(p => hasPermission(key, p)).length}/
                {Object.keys(module.permissions).length}
              </span>
            </div>
          ))}
        </div>

        <div className="permissions-panel">
          {selectedModule && systemModules[selectedModule] && (
            <>
              <div className="module-header">
                <h4>
                  {systemModules[selectedModule].icon} {systemModules[selectedModule].name}
                </h4>
                <button
                  className="toggle-all-btn"
                  onClick={() => toggleModulePermissions(selectedModule)}
                >
                  {Object.keys(systemModules[selectedModule].permissions).every(p => 
                    hasPermission(selectedModule, p)
                  ) ? 'إلغاء الكل' : 'تحديد الكل'}
                </button>
              </div>

              <div className="permissions-grid">
                {Object.entries(systemModules[selectedModule].permissions).map(([key, description]) => (
                  <div key={key} className="permission-item">
                    <label className="permission-label">
                      <input
                        type="checkbox"
                        checked={hasPermission(selectedModule, key)}
                        onChange={() => togglePermission(selectedModule, key)}
                        className="permission-checkbox"
                      />
                      <span className="permission-text">
                        <strong>{description}</strong>
                        <small>{selectedModule}.{key}</small>
                      </span>
                    </label>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      <div className="permissions-summary">
        <h4>ملخص الصلاحيات</h4>
        <div className="summary-stats">
          <span>إجمالي الصلاحيات: {userPermissions.length}</span>
          <span>الوحدات المفعلة: {Object.keys(systemModules).filter(module => 
            Object.keys(systemModules[module].permissions).some(p => hasPermission(module, p))
          ).length}</span>
        </div>
      </div>
    </div>
  );
};

export default AdvancedPermissionsSystem;
