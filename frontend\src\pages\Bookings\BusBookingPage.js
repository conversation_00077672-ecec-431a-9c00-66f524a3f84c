import React, { useState, useEffect } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';

const BusBookingPage = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);

  const [newBooking, setNewBooking] = useState({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    fromCity: '',
    toCity: '',
    departureDate: '',
    departureTime: '',
    returnDate: '',
    returnTime: '',
    tripType: 'one-way', // one-way, round-trip
    passengers: 1,
    busType: 'regular', // regular, vip, luxury
    seatNumbers: '',
    amount: '',
    paidAmount: 0,
    currency: 'SAR',
    status: 'pending',
    paymentStatus: 'unpaid',
    bookingReference: '',
    notes: '',
    // الحقول الجديدة
    transportCompany: '',
    attachments: [],
    bookingType: 'domestic' // domestic, international
  });

  useEffect(() => {
    setTimeout(() => {
      setBookings([
        {
          id: 1,
          customerName: 'خالد أحمد المحمد',
          customerPhone: '+966501234567',
          customerEmail: '<EMAIL>',
          fromCity: 'الرياض',
          toCity: 'جدة',
          departureDate: '2024-02-15',
          departureTime: '08:00',
          returnDate: '2024-02-17',
          returnTime: '18:00',
          tripType: 'round-trip',
          passengers: 2,
          busType: 'vip',
          seatNumbers: 'A1, A2',
          amount: 400,
          paidAmount: 400,
          currency: 'SAR',
          status: 'confirmed',
          paymentStatus: 'paid',
          bookingReference: 'BUS2024001',
          bookingDate: '2024-01-15',
          notes: 'مقاعد في المقدمة',
          transportCompany: 'شركة سابتكو',
          attachments: ['صورة الهوية', 'تذكرة الحجز'],
          bookingType: 'domestic'
        },
        {
          id: 2,
          customerName: 'مريم سالم الأحمد',
          customerPhone: '+966507654321',
          customerEmail: '<EMAIL>',
          fromCity: 'الدمام',
          toCity: 'الرياض',
          departureDate: '2024-02-20',
          departureTime: '14:00',
          tripType: 'one-way',
          passengers: 1,
          busType: 'regular',
          seatNumbers: 'B5',
          amount: 150,
          paidAmount: 0,
          currency: 'YER',
          status: 'pending',
          paymentStatus: 'unpaid',
          bookingReference: 'BUS2024002',
          bookingDate: '2024-01-14',
          notes: 'رحلة عمل',
          transportCompany: 'شركة النقل الجماعي',
          attachments: ['صورة الهوية'],
          bookingType: 'domestic'
        },
        {
          id: 3,
          customerName: 'عبدالله محمد الخالد',
          customerPhone: '+966551234567',
          customerEmail: '<EMAIL>',
          fromCity: 'الرياض',
          toCity: 'دبي',
          departureDate: '2024-02-25',
          departureTime: '22:00',
          returnDate: '2024-02-28',
          returnTime: '20:00',
          tripType: 'round-trip',
          passengers: 3,
          busType: 'luxury',
          seatNumbers: 'A1, A2, A3',
          amount: 320,
          paidAmount: 160,
          currency: 'USD',
          status: 'confirmed',
          paymentStatus: 'partial',
          bookingReference: 'BUS2024003',
          bookingDate: '2024-01-13',
          notes: 'رحلة دولية - عائلة',
          transportCompany: 'شركة النقل الدولي',
          attachments: ['جوازات السفر', 'تأشيرات الدخول', 'تأمين السفر'],
          bookingType: 'international'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddBooking = (e) => {
    e.preventDefault();
    const booking = {
      id: bookings.length + 1,
      ...newBooking,
      amount: parseFloat(newBooking.amount),
      paidAmount: parseFloat(newBooking.paidAmount),
      bookingDate: new Date().toISOString().split('T')[0],
      bookingReference: `BUS${new Date().getFullYear()}${String(bookings.length + 1).padStart(3, '0')}`
    };
    setBookings([booking, ...bookings]);
    setNewBooking({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      fromCity: '',
      toCity: '',
      departureDate: '',
      departureTime: '',
      returnDate: '',
      returnTime: '',
      tripType: 'one-way',
      passengers: 1,
      busType: 'regular',
      seatNumbers: '',
      amount: '',
      paidAmount: 0,
      currency: 'SAR',
      status: 'pending',
      paymentStatus: 'unpaid',
      bookingReference: '',
      notes: '',
      // إعادة تعيين الحقول الجديدة
      transportCompany: '',
      attachments: [],
      bookingType: 'domestic'
    });
    setShowAddForm(false);
  };

  const handleStatusChange = (id, newStatus) => {
    setBookings(bookings.map(booking => 
      booking.id === id ? { ...booking, status: newStatus } : booking
    ));
  };

  const handleDeleteBooking = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
      setBookings(bookings.filter(booking => booking.id !== id));
    }
  };

  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    handleDeleteBooking(booking.id);
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, 'الباصات');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, 'الباصات');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, 'الباصات');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, 'الباصات');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedIds.length} حجز؟`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };

  const getBusTypeText = (busType) => {
    switch (busType) {
      case 'regular': return 'عادي';
      case 'vip': return 'VIP';
      case 'luxury': return 'فاخر';
      default: return busType;
    }
  };

  const getBookingTypeText = (bookingType) => {
    switch (bookingType) {
      case 'domestic': return 'داخلي';
      case 'international': return 'دولي';
      default: return bookingType;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'cancelled': return '#e74c3c';
      case 'completed': return '#8e44ad';
      default: return '#95a5a6';
    }
  };

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.fromCity.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.toCity.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || booking.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #f39c12',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل حجوزات الباصات...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h2 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>🚌 حجوزات الباصات</h2>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة وتتبع حجوزات رحلات الباصات</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ حجز باص جديد
        </button>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في حجوزات الباصات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="pending">معلق</option>
          <option value="confirmed">مؤكد</option>
          <option value="completed">مكتمل</option>
          <option value="cancelled">ملغي</option>
        </select>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي الحجوزات', value: bookings.length, color: '#f39c12', icon: '🚌' },
          { title: 'الحجوزات المؤكدة', value: bookings.filter(b => b.status === 'confirmed').length, color: '#27ae60', icon: '✅' },
          { title: 'الحجوزات المعلقة', value: bookings.filter(b => b.status === 'pending').length, color: '#f39c12', icon: '⏳' },
          { 
            title: 'إجمالي المبيعات', 
            value: (() => {
              const totals = bookings.reduce((acc, b) => {
                const currency = b.currency || 'SAR';
                acc[currency] = (acc[currency] || 0) + (b.amount || 0);
                return acc;
              }, {});
              
              return Object.entries(totals)
                .map(([currency, amount]) => formatCurrency(amount, currency))
                .join(' | ');
            })(),
            color: '#8e44ad', 
            icon: '💰' 
          }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {stat.value}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

      {/* Bookings Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>العميل</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الرحلة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الشركة والنوع</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>التاريخ والوقت</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>المسافرون</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>نوع الباص</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>المبلغ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookings.map((booking) => (
                <tr key={booking.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedBookings.includes(booking.id)}
                      onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{booking.customerName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{booking.customerPhone}</div>
                      <div style={{ fontSize: '11px', color: '#95a5a6' }}>{booking.bookingReference}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold' }}>
                      {booking.fromCity} → {booking.toCity}
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      {booking.tripType === 'round-trip' ? 'ذهاب وعودة' : 'ذهاب فقط'}
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>
                      🚌 {booking.transportCompany || 'غير محدد'}
                    </div>
                    <span style={{
                      padding: '2px 6px',
                      borderRadius: '8px',
                      fontSize: '10px',
                      fontWeight: 'bold',
                      background: booking.bookingType === 'international' ? '#e74c3c20' : '#27ae6020',
                      color: booking.bookingType === 'international' ? '#e74c3c' : '#27ae60'
                    }}>
                      {booking.bookingType === 'international' ? '🌍 دولي' : '🏠 داخلي'}
                    </span>
                    {booking.attachments && booking.attachments.length > 0 && (
                      <div style={{ fontSize: '10px', color: '#9b59b6', marginTop: '2px' }}>
                        📎 {booking.attachments.length} مرفق
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div>📅 {booking.departureDate}</div>
                    <div style={{ fontSize: '12px', color: '#7f8c8d' }}>🕐 {booking.departureTime}</div>
                    {booking.tripType === 'round-trip' && booking.returnDate && (
                      <div style={{ fontSize: '11px', color: '#95a5a6' }}>
                        ↩️ {booking.returnDate} {booking.returnTime}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold' }}>
                      {booking.passengers} 👤
                    </div>
                    {booking.seatNumbers && (
                      <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                        مقاعد: {booking.seatNumbers}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      background: booking.busType === 'luxury' ? '#8e44ad20' : 
                                 booking.busType === 'vip' ? '#f39c1220' : '#3498db20',
                      color: booking.busType === 'luxury' ? '#8e44ad' : 
                             booking.busType === 'vip' ? '#f39c12' : '#3498db'
                    }}>
                      {getBusTypeText(booking.busType)}
                    </span>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold', color: '#27ae60' }}>
                      {formatCurrency(booking.amount, booking.currency)}
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      background: `${getStatusColor(booking.status)}20`,
                      color: getStatusColor(booking.status)
                    }}>
                      {booking.status === 'confirmed' ? 'مؤكد' :
                       booking.status === 'pending' ? 'معلق' :
                       booking.status === 'cancelled' ? 'ملغي' :
                       booking.status === 'completed' ? 'مكتمل' : booking.status}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <BookingActions
                      booking={booking}
                      onView={handleViewBooking}
                      onEdit={handleEditBooking}
                      onDelete={handleDeleteSingleBooking}
                      onPrint={handlePrintBooking}
                      onSavePDF={handleSavePDFBooking}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Booking Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>🚌 حجز باص جديد</h2>
            
            <form onSubmit={handleAddBooking}>
              {/* معلومات العميل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>👤 معلومات العميل</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({...newBooking, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({...newBooking, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* تفاصيل الرحلة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>🚌 تفاصيل الرحلة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>من</label>
                    <input
                      type="text"
                      value={newBooking.fromCity}
                      onChange={(e) => setNewBooking({...newBooking, fromCity: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إلى</label>
                    <input
                      type="text"
                      value={newBooking.toCity}
                      onChange={(e) => setNewBooking({...newBooking, toCity: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الرحلة</label>
                    <select
                      value={newBooking.tripType}
                      onChange={(e) => setNewBooking({...newBooking, tripType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="one-way">ذهاب فقط</option>
                      <option value="round-trip">ذهاب وعودة</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الباص</label>
                    <select
                      value={newBooking.busType}
                      onChange={(e) => setNewBooking({...newBooking, busType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="regular">عادي</option>
                      <option value="vip">VIP</option>
                      <option value="luxury">فاخر</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* معلومات الشركة والحجز */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>🚌 معلومات الشركة والحجز</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>شركة النقل</label>
                    <input
                      type="text"
                      value={newBooking.transportCompany}
                      onChange={(e) => setNewBooking({...newBooking, transportCompany: e.target.value})}
                      placeholder="مثال: شركة سابتكو"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الحجز</label>
                    <select
                      value={newBooking.bookingType}
                      onChange={(e) => setNewBooking({...newBooking, bookingType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="domestic">داخلي</option>
                      <option value="international">دولي</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* المرفقات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>📎 المرفقات</h3>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إضافة مرفقات</label>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      const fileNames = files.map(file => file.name);
                      setNewBooking({...newBooking, attachments: [...newBooking.attachments, ...fileNames]});
                    }}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                  {newBooking.attachments.length > 0 && (
                    <div style={{ marginTop: '10px' }}>
                      <p style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>المرفقات المضافة:</p>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
                        {newBooking.attachments.map((attachment, index) => (
                          <span key={index} style={{
                            padding: '4px 8px',
                            background: '#f39c1220',
                            color: '#f39c12',
                            borderRadius: '12px',
                            fontSize: '11px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px'
                          }}>
                            📎 {attachment}
                            <button
                              type="button"
                              onClick={() => {
                                const newAttachments = newBooking.attachments.filter((_, i) => i !== index);
                                setNewBooking({...newBooking, attachments: newAttachments});
                              }}
                              style={{
                                background: 'none',
                                border: 'none',
                                color: '#f39c12',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              ✕
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* التواريخ والأوقات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>📅 التواريخ والأوقات</h3>
                <div style={{ display: 'grid', gridTemplateColumns: newBooking.tripType === 'round-trip' ? 'repeat(4, 1fr)' : 'repeat(2, 1fr)', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ المغادرة</label>
                    <input
                      type="date"
                      value={newBooking.departureDate}
                      onChange={(e) => setNewBooking({...newBooking, departureDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>وقت المغادرة</label>
                    <input
                      type="time"
                      value={newBooking.departureTime}
                      onChange={(e) => setNewBooking({...newBooking, departureTime: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  {newBooking.tripType === 'round-trip' && (
                    <>
                      <div>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ العودة</label>
                        <input
                          type="date"
                          value={newBooking.returnDate}
                          onChange={(e) => setNewBooking({...newBooking, returnDate: e.target.value})}
                          style={{
                            width: '100%',
                            padding: '12px',
                            border: '2px solid #e0e0e0',
                            borderRadius: '8px',
                            fontSize: '14px',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                      <div>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>وقت العودة</label>
                        <input
                          type="time"
                          value={newBooking.returnTime}
                          onChange={(e) => setNewBooking({...newBooking, returnTime: e.target.value})}
                          style={{
                            width: '100%',
                            padding: '12px',
                            border: '2px solid #e0e0e0',
                            borderRadius: '8px',
                            fontSize: '14px',
                            boxSizing: 'border-box'
                          }}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* المسافرون والمقاعد */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>👥 المسافرون والمقاعد</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>عدد المسافرين</label>
                    <input
                      type="number"
                      min="1"
                      value={newBooking.passengers}
                      onChange={(e) => setNewBooking({...newBooking, passengers: parseInt(e.target.value)})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>أرقام المقاعد</label>
                    <input
                      type="text"
                      value={newBooking.seatNumbers}
                      onChange={(e) => setNewBooking({...newBooking, seatNumbers: e.target.value})}
                      placeholder="مثال: A1, A2, B3"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إجمالي المبلغ</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.amount}
                      onChange={(e) => setNewBooking({...newBooking, amount: e.target.value})}
                      required
                      placeholder="0.00"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ المدفوع</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.paidAmount}
                      onChange={(e) => setNewBooking({...newBooking, paidAmount: e.target.value})}
                      placeholder="0.00"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع العملة</label>
                    <select
                      value={newBooking.currency}
                      onChange={(e) => setNewBooking({...newBooking, currency: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                      <option value="YER">🇾🇪 ريال يمني (YER)</option>
                      <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة الحجز
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />
    </div>
  );
};

export default BusBookingPage;