// 🌐 نظام تحسين الشبكة والطلبات

// 📡 فئة إدارة الطلبات المحسنة
export class OptimizedApiClient {
  constructor(baseURL = '', options = {}) {
    this.baseURL = baseURL;
    this.options = {
      timeout: 10000,
      retries: 3,
      retryDelay: 1000,
      cache: true,
      ...options
    };
    
    this.cache = new Map();
    this.pendingRequests = new Map();
    this.requestQueue = [];
    this.isOnline = navigator.onLine;
    
    this.initNetworkMonitoring();
  }

  // 🔍 مراقبة حالة الشبكة
  initNetworkMonitoring() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processOfflineQueue();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  // 📦 طلب محسن مع إعادة المحاولة والتخزين المؤقت
  async request(endpoint, options = {}) {
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    const cacheKey = this.generateCacheKey(endpoint, config);
    
    // 💾 التحقق من التخزين المؤقت
    if (config.method === 'GET' && this.options.cache) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }
    }

    // 🔄 منع الطلبات المكررة
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }

    // 📱 التعامل مع عدم الاتصال
    if (!this.isOnline && config.method !== 'GET') {
      return this.queueOfflineRequest(endpoint, config);
    }

    const requestPromise = this.executeRequest(endpoint, config, cacheKey);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      this.pendingRequests.delete(cacheKey);
      return result;
    } catch (error) {
      this.pendingRequests.delete(cacheKey);
      throw error;
    }
  }

  // 🚀 تنفيذ الطلب مع إعادة المحاولة
  async executeRequest(endpoint, config, cacheKey, attempt = 1) {
    const url = `${this.baseURL}${endpoint}`;
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.options.timeout);
      
      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // 💾 حفظ في التخزين المؤقت
      if (config.method === 'GET' && this.options.cache) {
        this.setCache(cacheKey, data);
      }
      
      return data;
      
    } catch (error) {
      // 🔄 إعادة المحاولة
      if (attempt < this.options.retries && this.shouldRetry(error)) {
        await this.delay(this.options.retryDelay * attempt);
        return this.executeRequest(endpoint, config, cacheKey, attempt + 1);
      }
      
      throw error;
    }
  }

  // ⏱️ تأخير
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 🔍 تحديد ما إذا كان يجب إعادة المحاولة
  shouldRetry(error) {
    return (
      error.name === 'AbortError' ||
      error.message.includes('NetworkError') ||
      error.message.includes('500') ||
      error.message.includes('502') ||
      error.message.includes('503') ||
      error.message.includes('504')
    );
  }

  // 🔑 إنشاء مفتاح التخزين المؤقت
  generateCacheKey(endpoint, config) {
    const key = `${config.method}:${endpoint}`;
    if (config.body) {
      return `${key}:${JSON.stringify(config.body)}`;
    }
    return key;
  }

  // 💾 إدارة التخزين المؤقت
  setCache(key, data, ttl = 300000) { // 5 دقائق افتراضي
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  clearCache() {
    this.cache.clear();
  }

  // 📱 إدارة الطلبات غير المتصلة
  queueOfflineRequest(endpoint, config) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        endpoint,
        config,
        resolve,
        reject,
        timestamp: Date.now()
      });
    });
  }

  async processOfflineQueue() {
    const queue = [...this.requestQueue];
    this.requestQueue = [];
    
    for (const request of queue) {
      try {
        const result = await this.executeRequest(
          request.endpoint,
          request.config,
          this.generateCacheKey(request.endpoint, request.config)
        );
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  // 📊 إحصائيات الأداء
  getStats() {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      queuedRequests: this.requestQueue.length,
      isOnline: this.isOnline
    };
  }
}

// 🔄 مجمع الطلبات المتقدم
export class RequestBatcher {
  constructor(options = {}) {
    this.options = {
      maxBatchSize: 10,
      batchDelay: 100,
      maxWaitTime: 1000,
      ...options
    };
    
    this.batches = new Map();
  }

  // 📦 إضافة طلب للدفعة
  add(key, request) {
    if (!this.batches.has(key)) {
      this.batches.set(key, {
        requests: [],
        timer: null,
        startTime: Date.now()
      });
    }

    const batch = this.batches.get(key);
    
    return new Promise((resolve, reject) => {
      batch.requests.push({ request, resolve, reject });
      
      // تنفيذ فوري إذا وصلت للحد الأقصى
      if (batch.requests.length >= this.options.maxBatchSize) {
        this.executeBatch(key);
        return;
      }
      
      // تنفيذ بعد تأخير
      if (batch.timer) {
        clearTimeout(batch.timer);
      }
      
      batch.timer = setTimeout(() => {
        this.executeBatch(key);
      }, this.options.batchDelay);
      
      // تنفيذ إجباري بعد الحد الأقصى للانتظار
      if (Date.now() - batch.startTime > this.options.maxWaitTime) {
        this.executeBatch(key);
      }
    });
  }

  // 🚀 تنفيذ الدفعة
  async executeBatch(key) {
    const batch = this.batches.get(key);
    if (!batch || batch.requests.length === 0) return;
    
    this.batches.delete(key);
    
    if (batch.timer) {
      clearTimeout(batch.timer);
    }
    
    try {
      const requests = batch.requests.map(item => item.request());
      const results = await Promise.allSettled(requests);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          batch.requests[index].resolve(result.value);
        } else {
          batch.requests[index].reject(result.reason);
        }
      });
    } catch (error) {
      batch.requests.forEach(item => item.reject(error));
    }
  }
}

// 📡 مراقب جودة الشبكة
export class NetworkQualityMonitor {
  constructor() {
    this.quality = 'unknown';
    this.speed = 0;
    this.latency = 0;
    this.observers = [];
    
    this.init();
  }

  init() {
    // 📊 مراقبة معلومات الاتصال
    if ('connection' in navigator) {
      const connection = navigator.connection;
      this.updateFromConnection(connection);
      
      connection.addEventListener('change', () => {
        this.updateFromConnection(connection);
      });
    }
    
    // 🏃‍♂️ اختبار السرعة الدوري
    this.startSpeedTest();
  }

  updateFromConnection(connection) {
    this.speed = connection.downlink || 0;
    this.quality = this.calculateQuality(connection);
    this.notifyObservers();
  }

  calculateQuality(connection) {
    const effectiveType = connection.effectiveType;
    
    switch (effectiveType) {
      case '4g':
        return 'excellent';
      case '3g':
        return 'good';
      case '2g':
        return 'poor';
      case 'slow-2g':
        return 'very-poor';
      default:
        return 'unknown';
    }
  }

  async startSpeedTest() {
    setInterval(async () => {
      try {
        const latency = await this.measureLatency();
        this.latency = latency;
        this.notifyObservers();
      } catch (error) {
        console.warn('فشل في قياس زمن الاستجابة:', error);
      }
    }, 30000); // كل 30 ثانية
  }

  async measureLatency() {
    const start = performance.now();
    
    try {
      await fetch('/api/ping', { 
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      return performance.now() - start;
    } catch (error) {
      throw new Error('فشل في قياس زمن الاستجابة');
    }
  }

  subscribe(callback) {
    this.observers.push(callback);
  }

  unsubscribe(callback) {
    this.observers = this.observers.filter(obs => obs !== callback);
  }

  notifyObservers() {
    this.observers.forEach(callback => {
      callback({
        quality: this.quality,
        speed: this.speed,
        latency: this.latency
      });
    });
  }

  getQuality() {
    return {
      quality: this.quality,
      speed: this.speed,
      latency: this.latency
    };
  }
}

// 🎯 تحسين الصور التكيفي
export class AdaptiveImageLoader {
  constructor() {
    this.networkMonitor = new NetworkQualityMonitor();
    this.qualitySettings = {
      'excellent': { quality: 90, format: 'webp' },
      'good': { quality: 75, format: 'webp' },
      'poor': { quality: 50, format: 'jpeg' },
      'very-poor': { quality: 30, format: 'jpeg' }
    };
  }

  getOptimizedImageUrl(src, width, height) {
    const networkQuality = this.networkMonitor.getQuality();
    const settings = this.qualitySettings[networkQuality.quality] || this.qualitySettings.good;
    
    return `${src}?w=${width}&h=${height}&q=${settings.quality}&f=${settings.format}`;
  }

  preloadImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  }
}

// 🚀 تهيئة نظام تحسين الشبكة
export const initNetworkOptimization = (baseURL = '') => {
  const apiClient = new OptimizedApiClient(baseURL);
  const batcher = new RequestBatcher();
  const networkMonitor = new NetworkQualityMonitor();
  const imageLoader = new AdaptiveImageLoader();
  
  return {
    apiClient,
    batcher,
    networkMonitor,
    imageLoader
  };
};

export default {
  OptimizedApiClient,
  RequestBatcher,
  NetworkQualityMonitor,
  AdaptiveImageLoader,
  initNetworkOptimization
};
