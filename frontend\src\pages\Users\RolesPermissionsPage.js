import React, { useState, useEffect } from 'react';
import './RolesPermissionsPage.css';

const RolesPermissionsPage = () => {
  const [activeTab, setActiveTab] = useState('roles');
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);

  // بيانات الأدوار الأولية
  const initialRoles = [
    {
      id: 1,
      name: 'مدير عام',
      key: 'super_admin',
      description: 'صلاحيات كاملة على النظام بدون قيود',
      color: '#e74c3c',
      icon: '👑',
      usersCount: 1,
      permissions: ['all'],
      isSystem: true,
      createdAt: '2023-01-01',
      updatedAt: '2024-01-15'
    },
    {
      id: 2,
      name: 'مدير',
      key: 'admin',
      description: 'صلاحيات إدارية متقدمة مع إمكانية إدارة المستخدمين',
      color: '#f39c12',
      icon: '🛡️',
      usersCount: 2,
      permissions: ['users_manage', 'sales_manage', 'customers_manage', 'bookings_manage', 'reports_view', 'finance_view'],
      isSystem: true,
      createdAt: '2023-01-01',
      updatedAt: '2024-01-10'
    },
    {
      id: 3,
      name: 'مشرف',
      key: 'manager',
      description: 'صلاحيات إشرافية محدودة للأقسام المحددة',
      color: '#3498db',
      icon: '👨‍💼',
      usersCount: 3,
      permissions: ['bookings_manage', 'customers_view', 'reports_view'],
      isSystem: true,
      createdAt: '2023-01-01',
      updatedAt: '2024-01-05'
    },
    {
      id: 4,
      name: 'موظف',
      key: 'employee',
      description: 'صلاحيات أساسية للعمل اليومي',
      color: '#27ae60',
      icon: '👤',
      usersCount: 8,
      permissions: ['bookings_view', 'customers_view'],
      isSystem: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-12-20'
    },
    {
      id: 5,
      name: 'محاسب',
      key: 'accountant',
      description: 'صلاحيات مالية ومحاسبية متخصصة',
      color: '#9b59b6',
      icon: '💰',
      usersCount: 2,
      permissions: ['finance_manage', 'reports_view', 'invoices_manage'],
      isSystem: false,
      createdAt: '2023-06-15',
      updatedAt: '2024-01-12'
    }
  ];

  // بيانات الصلاحيات الأولية
  const initialPermissions = [
    {
      id: 1,
      name: 'جميع الصلاحيات',
      key: 'all',
      description: 'صلاحية كاملة على جميع أجزاء النظام',
      category: 'system',
      icon: '🌟',
      isSystem: true,
      rolesCount: 1
    },
    {
      id: 2,
      name: 'إدارة المستخدمين',
      key: 'users_manage',
      description: 'إضافة وتعديل وحذف المستخدمين',
      category: 'users',
      icon: '👥',
      isSystem: true,
      rolesCount: 2
    },
    {
      id: 3,
      name: 'عرض المستخدمين',
      key: 'users_view',
      description: 'عرض قائمة المستخدمين فقط',
      category: 'users',
      icon: '👁️',
      isSystem: true,
      rolesCount: 3
    },
    {
      id: 4,
      name: 'إدارة المبيعات',
      key: 'sales_manage',
      description: 'إدارة كاملة لقسم المبيعات',
      category: 'sales',
      icon: '💼',
      isSystem: true,
      rolesCount: 2
    },
    {
      id: 5,
      name: 'عرض المبيعات',
      key: 'sales_view',
      description: 'عرض بيانات المبيعات فقط',
      category: 'sales',
      icon: '📊',
      isSystem: true,
      rolesCount: 4
    },
    {
      id: 6,
      name: 'إدارة العملاء',
      key: 'customers_manage',
      description: 'إضافة وتعديل وحذف العملاء',
      category: 'customers',
      icon: '👥',
      isSystem: true,
      rolesCount: 3
    },
    {
      id: 7,
      name: 'عرض العملاء',
      key: 'customers_view',
      description: 'عرض قائمة العملاء فقط',
      category: 'customers',
      icon: '👁️',
      isSystem: true,
      rolesCount: 5
    },
    {
      id: 8,
      name: 'إدارة الحجوزات',
      key: 'bookings_manage',
      description: 'إدارة كاملة للحجوزات',
      category: 'bookings',
      icon: '📋',
      isSystem: true,
      rolesCount: 3
    },
    {
      id: 9,
      name: 'عرض الحجوزات',
      key: 'bookings_view',
      description: 'عرض الحجوزات فقط',
      category: 'bookings',
      icon: '👁️',
      isSystem: true,
      rolesCount: 4
    },
    {
      id: 10,
      name: 'إدارة المالية',
      key: 'finance_manage',
      description: 'إدارة كاملة للشؤون المالية',
      category: 'finance',
      icon: '💰',
      isSystem: true,
      rolesCount: 2
    },
    {
      id: 11,
      name: 'عرض المالية',
      key: 'finance_view',
      description: 'عرض البيانات المالية فقط',
      category: 'finance',
      icon: '👁️',
      isSystem: true,
      rolesCount: 3
    },
    {
      id: 12,
      name: 'إدارة الفواتير',
      key: 'invoices_manage',
      description: 'إنشاء وتعديل الفواتير',
      category: 'finance',
      icon: '🧾',
      isSystem: true,
      rolesCount: 2
    },
    {
      id: 13,
      name: 'عرض التقارير',
      key: 'reports_view',
      description: 'عرض جميع التقارير',
      category: 'reports',
      icon: '📈',
      isSystem: true,
      rolesCount: 4
    },
    {
      id: 14,
      name: 'إدارة الإعدادات',
      key: 'settings_manage',
      description: 'تعديل إعدادات النظام',
      category: 'system',
      icon: '⚙️',
      isSystem: true,
      rolesCount: 1
    }
  ];

  const [newRole, setNewRole] = useState({
    name: '',
    key: '',
    description: '',
    color: '#3498db',
    icon: '👤',
    permissions: []
  });

  const [newPermission, setNewPermission] = useState({
    name: '',
    key: '',
    description: '',
    category: 'general',
    icon: '🔐'
  });

  const categories = {
    system: { name: 'النظام', icon: '⚙️', color: '#e74c3c' },
    users: { name: 'المستخدمين', icon: '👥', color: '#3498db' },
    sales: { name: 'المبيعات', icon: '💼', color: '#f39c12' },
    customers: { name: 'العملاء', icon: '👥', color: '#27ae60' },
    bookings: { name: 'الحجوزات', icon: '📋', color: '#9b59b6' },
    finance: { name: 'المالية', icon: '💰', color: '#e67e22' },
    reports: { name: 'التقارير', icon: '📈', color: '#1abc9c' },
    general: { name: 'عام', icon: '🔐', color: '#95a5a6' }
  };

  useEffect(() => {
    setRoles(initialRoles);
    setPermissions(initialPermissions);
  }, []);

  // إضافة دور جديد
  const handleAddRole = () => {
    if (!newRole.name || !newRole.key) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    const role = {
      id: roles.length + 1,
      ...newRole,
      usersCount: 0,
      isSystem: false,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };

    setRoles([...roles, role]);
    setShowRoleModal(false);
    setNewRole({
      name: '',
      key: '',
      description: '',
      color: '#3498db',
      icon: '👤',
      permissions: []
    });
  };

  // تعديل دور
  const handleEditRole = () => {
    const updatedRoles = roles.map(role => 
      role.id === selectedRole.id 
        ? { ...selectedRole, updatedAt: new Date().toISOString().split('T')[0] }
        : role
    );
    setRoles(updatedRoles);
    setShowRoleModal(false);
    setSelectedRole(null);
  };

  // حذف دور
  const handleDeleteRole = () => {
    if (deleteTarget.isSystem) {
      alert('لا يمكن حذف الأدوار الأساسية للنظام');
      return;
    }

    const updatedRoles = roles.filter(role => role.id !== deleteTarget.id);
    setRoles(updatedRoles);
    setShowDeleteModal(false);
    setDeleteTarget(null);
  };

  // إضافة صلاحية جديدة
  const handleAddPermission = () => {
    if (!newPermission.name || !newPermission.key) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    const permission = {
      id: permissions.length + 1,
      ...newPermission,
      isSystem: false,
      rolesCount: 0
    };

    setPermissions([...permissions, permission]);
    setShowPermissionModal(false);
    setNewPermission({
      name: '',
      key: '',
      description: '',
      category: 'general',
      icon: '🔐'
    });
  };

  // تعديل صلاحية
  const handleEditPermission = () => {
    const updatedPermissions = permissions.map(permission => 
      permission.id === selectedPermission.id ? selectedPermission : permission
    );
    setPermissions(updatedPermissions);
    setShowPermissionModal(false);
    setSelectedPermission(null);
  };

  // حذف صلاحية
  const handleDeletePermission = () => {
    if (deleteTarget.isSystem) {
      alert('لا يمكن حذف الصلاحيات الأساسية للنظام');
      return;
    }

    const updatedPermissions = permissions.filter(permission => permission.id !== deleteTarget.id);
    setPermissions(updatedPermissions);
    setShowDeleteModal(false);
    setDeleteTarget(null);
  };

  // تبديل صلاحية في الدور
  const togglePermissionInRole = (roleId, permissionKey) => {
    const updatedRoles = roles.map(role => {
      if (role.id === roleId) {
        const hasPermission = role.permissions.includes(permissionKey);
        const newPermissions = hasPermission
          ? role.permissions.filter(p => p !== permissionKey)
          : [...role.permissions, permissionKey];
        
        return { ...role, permissions: newPermissions };
      }
      return role;
    });
    setRoles(updatedRoles);
  };

  // إحصائيات
  const stats = {
    totalRoles: roles.length,
    systemRoles: roles.filter(r => r.isSystem).length,
    customRoles: roles.filter(r => !r.isSystem).length,
    totalPermissions: permissions.length,
    systemPermissions: permissions.filter(p => p.isSystem).length,
    customPermissions: permissions.filter(p => !p.isSystem).length
  };

  return (
    <div className="roles-permissions-page">
      {/* رأس الصفحة */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-text">
            <h1 className="page-title">
              <span className="title-icon">🛡️</span>
              إدارة الأدوار والصلاحيات
            </h1>
            <p className="page-description">
              تحكم كامل في أدوار المستخدمين وصلاحياتهم داخل النظام
            </p>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">🎭</div>
            <div className="stat-info">
              <div className="stat-number">{stats.totalRoles}</div>
              <div className="stat-label">إجمالي الأدوار</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">🔐</div>
            <div className="stat-info">
              <div className="stat-number">{stats.totalPermissions}</div>
              <div className="stat-label">إجمالي الصلاحيات</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">⚙️</div>
            <div className="stat-info">
              <div className="stat-number">{stats.systemRoles}</div>
              <div className="stat-label">أدوار النظام</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">🎨</div>
            <div className="stat-info">
              <div className="stat-number">{stats.customRoles}</div>
              <div className="stat-label">أدوار مخصصة</div>
            </div>
          </div>
        </div>
      </div>

      {/* تبويبات */}
      <div className="tabs-container">
        <div className="tabs">
          <button
            className={`tab ${activeTab === 'roles' ? 'active' : ''}`}
            onClick={() => setActiveTab('roles')}
          >
            <span className="tab-icon">🎭</span>
            الأدوار
          </button>
          <button
            className={`tab ${activeTab === 'permissions' ? 'active' : ''}`}
            onClick={() => setActiveTab('permissions')}
          >
            <span className="tab-icon">🔐</span>
            الصلاحيات
          </button>
          <button
            className={`tab ${activeTab === 'matrix' ? 'active' : ''}`}
            onClick={() => setActiveTab('matrix')}
          >
            <span className="tab-icon">📊</span>
            مصفوفة الصلاحيات
          </button>
        </div>

        <div className="tab-actions">
          {activeTab === 'roles' && (
            <button 
              className="btn-add"
              onClick={() => setShowRoleModal(true)}
            >
              <span className="btn-icon">➕</span>
              إضافة دور جديد
            </button>
          )}
          {activeTab === 'permissions' && (
            <button 
              className="btn-add"
              onClick={() => setShowPermissionModal(true)}
            >
              <span className="btn-icon">➕</span>
              إضافة صلاحية جديدة
            </button>
          )}
        </div>
      </div>

      {/* محتوى التبويبات */}
      <div className="tab-content">
        {/* تبويب الأدوار */}
        {activeTab === 'roles' && (
          <div className="roles-grid">
            {roles.map(role => (
              <div key={role.id} className="role-card">
                <div className="role-header">
                  <div className="role-info">
                    <div 
                      className="role-icon"
                      style={{ backgroundColor: role.color }}
                    >
                      {role.icon}
                    </div>
                    <div className="role-details">
                      <h3 className="role-name">{role.name}</h3>
                      <p className="role-description">{role.description}</p>
                    </div>
                  </div>
                  <div className="role-actions">
                    <button
                      className="action-btn edit"
                      onClick={() => {
                        setSelectedRole({...role});
                        setShowRoleModal(true);
                      }}
                      title="تعديل"
                    >
                      ✏️
                    </button>
                    {!role.isSystem && (
                      <button
                        className="action-btn delete"
                        onClick={() => {
                          setDeleteTarget(role);
                          setShowDeleteModal(true);
                        }}
                        title="حذف"
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </div>

                <div className="role-stats">
                  <div className="role-stat">
                    <span className="stat-icon">👥</span>
                    <span className="stat-text">{role.usersCount} مستخدم</span>
                  </div>
                  <div className="role-stat">
                    <span className="stat-icon">🔐</span>
                    <span className="stat-text">{role.permissions.length} صلاحية</span>
                  </div>
                  {role.isSystem && (
                    <div className="system-badge">
                      <span className="badge-icon">⚙️</span>
                      دور النظام
                    </div>
                  )}
                </div>

                <div className="role-permissions">
                  <h4>الصلاحيات:</h4>
                  <div className="permissions-tags">
                    {role.permissions.includes('all') ? (
                      <span className="permission-tag all">جميع الصلاحيات</span>
                    ) : (
                      role.permissions.map(permKey => {
                        const perm = permissions.find(p => p.key === permKey);
                        return perm ? (
                          <span key={permKey} className="permission-tag">
                            {perm.icon} {perm.name}
                          </span>
                        ) : null;
                      })
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* تبويب الصلاحيات */}
        {activeTab === 'permissions' && (
          <div className="permissions-container">
            {Object.entries(categories).map(([categoryKey, category]) => {
              const categoryPermissions = permissions.filter(p => p.category === categoryKey);
              if (categoryPermissions.length === 0) return null;

              return (
                <div key={categoryKey} className="permissions-category">
                  <div className="category-header">
                    <div className="category-info">
                      <span 
                        className="category-icon"
                        style={{ backgroundColor: category.color }}
                      >
                        {category.icon}
                      </span>
                      <h3 className="category-name">{category.name}</h3>
                    </div>
                    <div className="category-count">
                      {categoryPermissions.length} صلاحية
                    </div>
                  </div>

                  <div className="permissions-grid">
                    {categoryPermissions.map(permission => (
                      <div key={permission.id} className="permission-card">
                        <div className="permission-header">
                          <div className="permission-info">
                            <span className="permission-icon">{permission.icon}</span>
                            <div className="permission-details">
                              <h4 className="permission-name">{permission.name}</h4>
                              <p className="permission-description">{permission.description}</p>
                            </div>
                          </div>
                          <div className="permission-actions">
                            <button
                              className="action-btn edit"
                              onClick={() => {
                                setSelectedPermission({...permission});
                                setShowPermissionModal(true);
                              }}
                              title="تعديل"
                            >
                              ✏️
                            </button>
                            {!permission.isSystem && (
                              <button
                                className="action-btn delete"
                                onClick={() => {
                                  setDeleteTarget(permission);
                                  setShowDeleteModal(true);
                                }}
                                title="حذف"
                              >
                                🗑️
                              </button>
                            )}
                          </div>
                        </div>

                        <div className="permission-stats">
                          <div className="permission-stat">
                            <span className="stat-icon">🎭</span>
                            <span className="stat-text">{permission.rolesCount} دور</span>
                          </div>
                          {permission.isSystem && (
                            <div className="system-badge">
                              <span className="badge-icon">⚙️</span>
                              صلاحية النظام
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* تبويب مصفوفة الصلاحيات */}
        {activeTab === 'matrix' && (
          <div className="permissions-matrix">
            <div className="matrix-container">
              <table className="matrix-table">
                <thead>
                  <tr>
                    <th className="role-header">الدور</th>
                    {permissions.filter(p => p.key !== 'all').map(permission => (
                      <th key={permission.id} className="permission-header">
                        <div className="permission-header-content">
                          <span className="permission-icon">{permission.icon}</span>
                          <span className="permission-name">{permission.name}</span>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {roles.map(role => (
                    <tr key={role.id}>
                      <td className="role-cell">
                        <div className="role-cell-content">
                          <span 
                            className="role-icon"
                            style={{ backgroundColor: role.color }}
                          >
                            {role.icon}
                          </span>
                          <span className="role-name">{role.name}</span>
                        </div>
                      </td>
                      {permissions.filter(p => p.key !== 'all').map(permission => (
                        <td key={permission.id} className="permission-cell">
                          <button
                            className={`permission-toggle ${
                              role.permissions.includes('all') || role.permissions.includes(permission.key)
                                ? 'active' 
                                : ''
                            }`}
                            onClick={() => togglePermissionInRole(role.id, permission.key)}
                            disabled={role.permissions.includes('all') || role.isSystem}
                            title={
                              role.permissions.includes('all') 
                                ? 'هذا الدور لديه جميع الصلاحيات'
                                : role.isSystem
                                ? 'لا يمكن تعديل أدوار النظام'
                                : 'انقر للتبديل'
                            }
                          >
                            {role.permissions.includes('all') || role.permissions.includes(permission.key) 
                              ? '✅' 
                              : '❌'
                            }
                          </button>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* نافذة إضافة/تعديل دور */}
      {showRoleModal && (
        <div className="modal-overlay">
          <div className="modal role-modal">
            <div className="modal-header">
              <h3>{selectedRole ? 'تعديل الدور' : 'إضافة دور جديد'}</h3>
              <button 
                className="modal-close"
                onClick={() => {
                  setShowRoleModal(false);
                  setSelectedRole(null);
                }}
              >
                ✕
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم الدور *</label>
                  <input
                    type="text"
                    value={selectedRole ? selectedRole.name : newRole.name}
                    onChange={(e) => {
                      if (selectedRole) {
                        setSelectedRole({...selectedRole, name: e.target.value});
                      } else {
                        setNewRole({...newRole, name: e.target.value});
                      }
                    }}
                    placeholder="أدخل اسم الدور"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>مفتاح الدور *</label>
                  <input
                    type="text"
                    value={selectedRole ? selectedRole.key : newRole.key}
                    onChange={(e) => {
                      if (selectedRole) {
                        setSelectedRole({...selectedRole, key: e.target.value});
                      } else {
                        setNewRole({...newRole, key: e.target.value});
                      }
                    }}
                    placeholder="role_key"
                    required
                    disabled={selectedRole?.isSystem}
                  />
                </div>
                
                <div className="form-group">
                  <label>الأيقونة</label>
                  <input
                    type="text"
                    value={selectedRole ? selectedRole.icon : newRole.icon}
                    onChange={(e) => {
                      if (selectedRole) {
                        setSelectedRole({...selectedRole, icon: e.target.value});
                      } else {
                        setNewRole({...newRole, icon: e.target.value});
                      }
                    }}
                    placeholder="👤"
                  />
                </div>
                
                <div className="form-group">
                  <label>اللون</label>
                  <input
                    type="color"
                    value={selectedRole ? selectedRole.color : newRole.color}
                    onChange={(e) => {
                      if (selectedRole) {
                        setSelectedRole({...selectedRole, color: e.target.value});
                      } else {
                        setNewRole({...newRole, color: e.target.value});
                      }
                    }}
                  />
                </div>
              </div>
              
              <div className="form-group full-width">
                <label>الوصف</label>
                <textarea
                  value={selectedRole ? selectedRole.description : newRole.description}
                  onChange={(e) => {
                    if (selectedRole) {
                      setSelectedRole({...selectedRole, description: e.target.value});
                    } else {
                      setNewRole({...newRole, description: e.target.value});
                    }
                  }}
                  placeholder="وصف الدور وصلاحياته"
                  rows="3"
                />
              </div>
              
              {!selectedRole?.isSystem && (
                <div className="permissions-selection">
                  <h4>الصلاحيات:</h4>
                  <div className="permissions-checkboxes">
                    {permissions.filter(p => p.key !== 'all').map(permission => (
                      <label key={permission.id} className="permission-checkbox">
                        <input
                          type="checkbox"
                          checked={
                            selectedRole 
                              ? selectedRole.permissions.includes(permission.key)
                              : newRole.permissions.includes(permission.key)
                          }
                          onChange={(e) => {
                            const currentPermissions = selectedRole 
                              ? selectedRole.permissions 
                              : newRole.permissions;
                            
                            const newPermissions = e.target.checked
                              ? [...currentPermissions, permission.key]
                              : currentPermissions.filter(p => p !== permission.key);
                            
                            if (selectedRole) {
                              setSelectedRole({...selectedRole, permissions: newPermissions});
                            } else {
                              setNewRole({...newRole, permissions: newPermissions});
                            }
                          }}
                        />
                        <span className="permission-label">
                          {permission.icon} {permission.name}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-cancel"
                onClick={() => {
                  setShowRoleModal(false);
                  setSelectedRole(null);
                }}
              >
                إلغاء
              </button>
              <button 
                className="btn-save"
                onClick={selectedRole ? handleEditRole : handleAddRole}
              >
                {selectedRole ? 'حفظ التغييرات' : 'إضافة الدور'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة/تعديل صلاحية */}
      {showPermissionModal && (
        <div className="modal-overlay">
          <div className="modal permission-modal">
            <div className="modal-header">
              <h3>{selectedPermission ? 'تعديل الصلاحية' : 'إضافة صلاحية جديدة'}</h3>
              <button 
                className="modal-close"
                onClick={() => {
                  setShowPermissionModal(false);
                  setSelectedPermission(null);
                }}
              >
                ✕
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم الصلاحية *</label>
                  <input
                    type="text"
                    value={selectedPermission ? selectedPermission.name : newPermission.name}
                    onChange={(e) => {
                      if (selectedPermission) {
                        setSelectedPermission({...selectedPermission, name: e.target.value});
                      } else {
                        setNewPermission({...newPermission, name: e.target.value});
                      }
                    }}
                    placeholder="أدخل اسم الصلاحية"
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>مفتاح الصلاحية *</label>
                  <input
                    type="text"
                    value={selectedPermission ? selectedPermission.key : newPermission.key}
                    onChange={(e) => {
                      if (selectedPermission) {
                        setSelectedPermission({...selectedPermission, key: e.target.value});
                      } else {
                        setNewPermission({...newPermission, key: e.target.value});
                      }
                    }}
                    placeholder="permission_key"
                    required
                    disabled={selectedPermission?.isSystem}
                  />
                </div>
                
                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={selectedPermission ? selectedPermission.category : newPermission.category}
                    onChange={(e) => {
                      if (selectedPermission) {
                        setSelectedPermission({...selectedPermission, category: e.target.value});
                      } else {
                        setNewPermission({...newPermission, category: e.target.value});
                      }
                    }}
                  >
                    {Object.entries(categories).map(([key, category]) => (
                      <option key={key} value={key}>{category.name}</option>
                    ))}
                  </select>
                </div>
                
                <div className="form-group">
                  <label>الأيقونة</label>
                  <input
                    type="text"
                    value={selectedPermission ? selectedPermission.icon : newPermission.icon}
                    onChange={(e) => {
                      if (selectedPermission) {
                        setSelectedPermission({...selectedPermission, icon: e.target.value});
                      } else {
                        setNewPermission({...newPermission, icon: e.target.value});
                      }
                    }}
                    placeholder="🔐"
                  />
                </div>
              </div>
              
              <div className="form-group full-width">
                <label>الوصف</label>
                <textarea
                  value={selectedPermission ? selectedPermission.description : newPermission.description}
                  onChange={(e) => {
                    if (selectedPermission) {
                      setSelectedPermission({...selectedPermission, description: e.target.value});
                    } else {
                      setNewPermission({...newPermission, description: e.target.value});
                    }
                  }}
                  placeholder="وصف الصلاحية ووظيفتها"
                  rows="3"
                />
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-cancel"
                onClick={() => {
                  setShowPermissionModal(false);
                  setSelectedPermission(null);
                }}
              >
                إلغاء
              </button>
              <button 
                className="btn-save"
                onClick={selectedPermission ? handleEditPermission : handleAddPermission}
              >
                {selectedPermission ? 'حفظ التغييرات' : 'إضافة الصلاحية'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تأكيد الحذف */}
      {showDeleteModal && deleteTarget && (
        <div className="modal-overlay">
          <div className="modal delete-modal">
            <div className="modal-header">
              <h3>تأكيد الحذف</h3>
              <button 
                className="modal-close"
                onClick={() => setShowDeleteModal(false)}
              >
                ✕
              </button>
            </div>
            
            <div className="modal-body">
              <div className="delete-warning">
                <div className="warning-icon">⚠️</div>
                <p>هل أنت متأكد من حذف {deleteTarget.name ? 'الدور' : 'الصلاحية'}؟</p>
                <div className="item-to-delete">
                  <strong>{deleteTarget.name}</strong>
                  <br />
                  {deleteTarget.description}
                </div>
                <p className="warning-text">
                  هذا الإجراء لا يمكن التراجع عنه!
                </p>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-cancel"
                onClick={() => setShowDeleteModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn-delete"
                onClick={deleteTarget.name ? handleDeleteRole : handleDeletePermission}
              >
                حذف {deleteTarget.name ? 'الدور' : 'الصلاحية'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RolesPermissionsPage;