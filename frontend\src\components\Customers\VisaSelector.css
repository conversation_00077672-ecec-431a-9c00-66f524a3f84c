/* 🔍 أنماط مكون اختيار التأشيرة */

.visa-selector {
  margin: var(--space-4) 0;
  position: relative;
}

/* 🎯 رأس المكون */
.visa-selector-header {
  margin-bottom: var(--space-3);
}

.visa-selector-header label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.visa-search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.visa-search-input {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  padding-left: var(--space-12);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.visa-search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.visa-search-input::placeholder {
  color: var(--neutral-400);
}

.visa-search-toggle {
  position: absolute;
  left: var(--space-3);
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  color: var(--neutral-500);
  transition: all var(--transition-fast);
}

.visa-search-toggle:hover {
  color: var(--primary-500);
  transform: scale(1.1);
}

/* 📋 قائمة التأشيرات */
.visa-list-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 100;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-height: 500px;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.visa-list-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.visa-list-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.no-results {
  margin: var(--space-2) 0 0 0;
  font-size: 0.875rem;
  color: var(--neutral-500);
  font-style: italic;
}

.visa-list {
  max-height: 300px;
  overflow-y: auto;
  padding: var(--space-2);
}

/* 📋 عنصر التأشيرة */
.visa-item {
  padding: var(--space-4);
  margin-bottom: var(--space-2);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.visa-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--primary-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.visa-item.selected {
  background: rgba(59, 130, 246, 0.15);
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.visa-item.expired {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--danger-300);
  cursor: not-allowed;
  opacity: 0.7;
}

.visa-item.expired:hover {
  transform: none;
  background: rgba(239, 68, 68, 0.1);
}

/* 🎯 رأس عنصر التأشيرة */
.visa-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-3);
}

.visa-numbers {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.visa-issue-number,
.visa-registry-number {
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  font-weight: 700;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
  display: inline-block;
}

.visa-type-badge {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
}

.visa-type-icon {
  font-size: 0.875rem;
}

/* 📝 تفاصيل التأشيرة */
.visa-item-details {
  margin-bottom: var(--space-3);
}

.visa-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-1);
  font-size: 0.75rem;
}

.detail-label {
  color: var(--neutral-500);
  font-weight: 500;
}

.detail-value {
  color: var(--neutral-700);
  font-weight: 600;
}

/* 🔻 تذييل عنصر التأشيرة */
.visa-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-2);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.visa-expiry {
  flex: 1;
}

.expiry-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
}

.expiry-status.valid {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.expiry-status.expiring {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-600);
}

.expiry-status.expired {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
}

.visa-price {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
}

.price-label {
  color: var(--neutral-500);
}

.price-value {
  font-weight: 700;
  color: var(--neutral-700);
}

/* ✅ مؤشر الاختيار */
.selection-indicator {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  background: var(--secondary-500);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: 0.625rem;
  font-weight: 600;
  animation: bounceIn 0.3s ease-out;
}

/* 🔍 تفاصيل التمرير */
.visa-hover-details {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--neutral-800);
  color: white;
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 10;
  animation: fadeInUp 0.2s ease-out;
}

.hover-detail {
  margin-bottom: var(--space-1);
  font-size: 0.75rem;
}

.hover-detail:last-child {
  margin-bottom: 0;
}

/* 📊 إحصائيات القائمة */
.visa-list-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-2);
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid var(--glass-border);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
  color: var(--neutral-600);
}

.stat-icon {
  font-size: 0.875rem;
}

/* 🎬 إجراءات القائمة */
.visa-list-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  border-top: 1px solid var(--glass-border);
  background: rgba(255, 255, 255, 0.05);
}

/* 📋 معاينة التأشيرة المختارة */
.selected-visa-preview {
  padding: var(--space-4);
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: var(--radius-lg);
  margin-top: var(--space-3);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.preview-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--secondary-600);
}

.preview-edit-btn {
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  color: var(--neutral-500);
  transition: all var(--transition-fast);
  padding: var(--space-1);
  border-radius: var(--radius-md);
}

.preview-edit-btn:hover {
  color: var(--primary-500);
  background: rgba(59, 130, 246, 0.1);
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.preview-numbers {
  display: flex;
  gap: var(--space-2);
}

.preview-issue,
.preview-registry {
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  font-weight: 700;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

.preview-details {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 0.875rem;
}

.preview-type {
  color: var(--secondary-600);
  font-weight: 600;
}

.preview-nationality {
  color: var(--neutral-600);
}

.preview-price {
  color: var(--neutral-700);
  font-weight: 700;
  margin-right: auto;
}

/* 📋 حالة فارغة */
.visa-selector-empty {
  text-align: center;
  padding: var(--space-8);
  color: var(--neutral-500);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.visa-selector-empty h4 {
  margin: 0 0 var(--space-2) 0;
  font-size: 1.125rem;
  color: var(--neutral-600);
}

.visa-selector-empty p {
  margin: 0 0 var(--space-4) 0;
  font-size: 0.875rem;
}

/* 💡 نصائح البحث */
.search-tips {
  padding: var(--space-4);
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-lg);
  margin-top: var(--space-3);
}

.search-tips h5 {
  margin: 0 0 var(--space-2) 0;
  font-size: 0.875rem;
  color: var(--primary-600);
}

.search-tips ul {
  margin: 0;
  padding-right: var(--space-4);
  font-size: 0.75rem;
  color: var(--neutral-600);
}

.search-tips li {
  margin-bottom: var(--space-1);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .visa-list-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
  }
  
  .visa-item-header {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .visa-item-footer {
    flex-direction: column;
    gap: var(--space-2);
    align-items: flex-start;
  }
  
  .visa-list-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .visa-list-actions {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .preview-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

@media (max-width: 480px) {
  .visa-numbers {
    align-items: flex-start;
  }
  
  .preview-numbers {
    flex-direction: column;
  }
  
  .visa-list-stats {
    grid-template-columns: 1fr;
  }
}

/* 🎬 حركات خاصة */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .visa-list-container,
  .selection-indicator,
  .visa-hover-details {
    animation: none;
  }
  
  .visa-item:hover,
  .preview-edit-btn:hover {
    transform: none;
  }
}
