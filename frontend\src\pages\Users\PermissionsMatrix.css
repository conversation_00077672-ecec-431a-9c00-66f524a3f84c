/* مصفوفة الأذونات المتقدمة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

.permissions-matrix {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* رأس الصفحة */
.matrix-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 30px;
  box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.matrix-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: matrixFloat 8s ease-in-out infinite;
}

@keyframes matrixFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  position: relative;
  z-index: 2;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 20px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.title-icon {
  font-size: 3rem;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.page-description {
  font-size: 1.2rem;
  margin: 0;
  line-height: 1.6;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.btn-bulk-operations {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 15px 25px;
  border-radius: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: inherit;
  backdrop-filter: blur(10px);
}

.btn-bulk-operations:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-bulk-operations:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 1.2rem;
}

/* إحصائيات المصفوفة */
.matrix-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  position: relative;
  z-index: 2;
}

.stat-item {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
  display: block;
  margin-bottom: 10px;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 800;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

/* أدوات التحكم */
.matrix-controls {
  background: white;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 25px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.2rem;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 20px;
  border: 2px solid #e1e8ed;
  border-radius: 15px;
  font-size: 1.1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
}

.filters {
  display: flex;
  gap: 15px;
}

.filter-select {
  padding: 12px 18px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 180px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 5px;
}

.view-mode-btn {
  background: transparent;
  border: none;
  padding: 12px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: inherit;
}

.view-mode-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.view-mode-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.mode-icon {
  font-size: 1.1rem;
}

/* محتوى المصفوفة */
.matrix-content {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

/* جدول المصفوفة */
.matrix-table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 70vh;
  border-radius: 15px;
  border: 2px solid #f1f3f4;
}

.permissions-matrix-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1200px;
}

.permissions-matrix-table th {
  background: #f8f9fa;
  padding: 15px 10px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e8ed;
  position: sticky;
  top: 0;
  z-index: 10;
}

.user-header {
  text-align: right !important;
  width: 250px;
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px 15px !important;
}

.user-header input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: white;
}

.permission-header {
  min-width: 200px;
  max-width: 250px;
  position: relative;
  padding: 20px 15px !important;
}

.permission-header input[type="checkbox"] {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.permission-header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  text-align: center;
}

.permission-category-icon {
  width: 35px;
  height: 35px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  margin-bottom: 5px;
}

.permission-info {
  flex: 1;
}

.permission-name {
  font-size: 0.9rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
  line-height: 1.3;
}

.permission-key {
  font-size: 0.75rem;
  color: #7f8c8d;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  margin-bottom: 5px;
  display: inline-block;
}

.permission-stats {
  font-size: 0.75rem;
  color: #667eea;
  font-weight: 600;
}

.critical-indicator {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 1rem;
  color: #e74c3c;
  animation: criticalPulse 2s ease-in-out infinite;
}

@keyframes criticalPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.permissions-matrix-table td {
  padding: 10px;
  border-bottom: 1px solid #f1f3f4;
  border-right: 1px solid #f1f3f4;
  text-align: center;
  vertical-align: middle;
}

.permissions-matrix-table tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.user-cell {
  background: #f8f9fa !important;
  text-align: right !important;
  padding: 15px !important;
  position: sticky;
  right: 0;
  z-index: 5;
  border-right: 2px solid #e1e8ed;
}

.user-cell input[type="checkbox"] {
  margin-left: 10px;
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #e1e8ed;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  text-align: right;
}

.user-name {
  font-size: 0.95rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 3px;
}

.user-role {
  font-size: 0.8rem;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 2px;
}

.user-department {
  font-size: 0.75rem;
  color: #7f8c8d;
}

.permission-cell {
  padding: 8px !important;
}

.permission-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.permission-toggle:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.permission-toggle.granted {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.permission-toggle.denied {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.permission-toggle.critical {
  border: 2px solid #e74c3c;
  animation: criticalGlow 3s ease-in-out infinite;
}

@keyframes criticalGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.3); }
  50% { box-shadow: 0 0 20px rgba(231, 76, 60, 0.6); }
}

/* عرض حسب المستخدم */
.user-focused-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 25px;
}

.user-permissions-card {
  background: white;
  border: 2px solid #f1f3f4;
  border-radius: 20px;
  padding: 25px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.user-permissions-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.user-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f3f4;
}

.user-card-header .user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-card-header .user-avatar {
  width: 60px;
  height: 60px;
  border: 3px solid #667eea;
}

.user-card-header .user-name {
  font-size: 1.3rem;
  margin-bottom: 5px;
}

.user-card-header .user-role {
  font-size: 1rem;
  margin-bottom: 3px;
}

.user-card-header .user-department {
  font-size: 0.9rem;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat {
  text-align: center;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 12px;
  min-width: 80px;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 800;
  color: #667eea;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 600;
}

.user-permissions-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-category-section {
  border: 2px solid #f1f3f4;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.permission-category-section:hover {
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.category-header {
  background: #f8f9fa;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: white;
}

.category-name {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
}

.category-count {
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
}

.permissions-list {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.permission-item {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.permission-item.granted {
  background: rgba(39, 174, 96, 0.05);
  border: 2px solid rgba(39, 174, 96, 0.2);
}

.permission-item.denied {
  background: rgba(231, 76, 60, 0.05);
  border: 2px solid rgba(231, 76, 60, 0.2);
}

.permission-toggle-btn {
  width: 100%;
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.permission-toggle-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.toggle-icon {
  font-size: 1.3rem;
  width: 30px;
  text-align: center;
}

.permission-details {
  flex: 1;
  text-align: right;
}

.permission-details .permission-name {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.permission-description {
  font-size: 0.85rem;
  color: #7f8c8d;
  line-height: 1.4;
}

.critical-badge {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  padding: 6px 8px;
  border-radius: 50%;
  font-size: 0.9rem;
}

/* عرض حسب الإذن */
.permission-focused-view {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.permission-category-card {
  background: white;
  border: 2px solid #f1f3f4;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.permission-category-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.permission-category-card .category-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.category-info .category-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  font-size: 1.5rem;
}

.category-details {
  flex: 1;
}

.category-details .category-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.category-description {
  font-size: 1rem;
  color: #7f8c8d;
  line-height: 1.5;
}

.category-stats .permissions-count {
  background: #667eea;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 600;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  padding: 30px;
}

.permission-card {
  background: #f8f9fa;
  border: 2px solid #e1e8ed;
  border-radius: 15px;
  padding: 25px;
  transition: all 0.3s ease;
}

.permission-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
  background: white;
}

.permission-card .permission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.permission-card .permission-info {
  flex: 1;
}

.permission-card .permission-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.permission-card .permission-description {
  font-size: 0.95rem;
  color: #7f8c8d;
  line-height: 1.5;
  margin-bottom: 10px;
}

.permission-card .permission-key {
  font-size: 0.8rem;
  color: #95a5a6;
  font-family: 'Courier New', monospace;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.permission-badges {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.level-badge {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  min-width: 80px;
}

.level-badge.super_admin {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.level-badge.admin {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.level-badge.manager {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.level-badge.employee {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.permission-users {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e1e8ed;
}

.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.users-count {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.users-percentage {
  flex: 1;
  margin: 0 15px;
  height: 8px;
  background: #e1e8ed;
  border-radius: 4px;
  overflow: hidden;
}

.percentage-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.users-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.user-permission-toggle {
  background: none;
  border: 2px solid #e1e8ed;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-permission-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.user-permission-toggle.granted {
  border-color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
}

.user-permission-toggle.denied {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.user-avatar-small {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}

.permission-status {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  border: 2px solid #e1e8ed;
}

/* نافذة العمليات المجمعة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  backdrop-filter: blur(5px);
}

.modal {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  animation: slideUp 0.4s ease-out;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.modal-body {
  padding: 30px;
}

.bulk-summary {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 15px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.summary-icon {
  font-size: 1.5rem;
}

.bulk-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 25px;
}

.bulk-action-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: inherit;
}

.bulk-action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.bulk-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bulk-action-btn.grant {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.bulk-action-btn.revoke {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.bulk-warning {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: rgba(241, 196, 15, 0.1);
  border: 2px solid rgba(241, 196, 15, 0.3);
  border-radius: 12px;
  color: #f39c12;
  font-weight: 600;
}

.warning-icon {
  font-size: 1.3rem;
}

.modal-footer {
  padding: 25px 30px;
  border-top: 1px solid #f1f3f4;
  display: flex;
  justify-content: flex-end;
  background: #f8f9fa;
}

.btn-cancel {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-cancel:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

/* التجاوب مع الشاشات */
@media (max-width: 1200px) {
  .permissions-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  .user-focused-view {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .permissions-matrix {
    padding: 15px;
  }
  
  .matrix-header {
    padding: 25px;
  }
  
  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 15px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .matrix-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .controls-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filters {
    justify-content: stretch;
  }
  
  .filter-select {
    flex: 1;
    min-width: auto;
  }
  
  .view-modes {
    justify-content: center;
  }
  
  .matrix-table-container {
    max-height: 60vh;
  }
  
  .permission-header {
    min-width: 150px;
    max-width: 180px;
  }
  
  .user-header {
    width: 200px;
  }
  
  .permissions-grid {
    grid-template-columns: 1fr;
  }
  
  .user-card-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .user-stats {
    justify-content: center;
  }
  
  .bulk-summary {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .bulk-actions {
    flex-direction: column;
  }
  
  .modal {
    width: 95%;
    margin: 10px;
  }
}

@media (max-width: 480px) {
  .matrix-stats {
    grid-template-columns: 1fr;
  }
  
  .permission-toggle {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .user-permission-toggle {
    width: 45px;
    height: 45px;
  }
  
  .user-avatar-small {
    width: 30px;
    height: 30px;
  }
  
  .permission-status {
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
  }
}