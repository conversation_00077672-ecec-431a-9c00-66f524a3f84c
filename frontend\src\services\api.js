import axios from 'axios';
import { store } from '../store/store';
import { logout, refreshToken } from '../store/slices/authSlice';

// إعداد الـ Base URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

// إنشاء مثيل Axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request Interceptor - إضافة التوكن لكل طلب
api.interceptors.request.use(
  (config) => {
    const state = store.getState();
    const token = state.auth.token;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response Interceptor - معالجة الأخطاء وتجديد التوكن
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      const state = store.getState();
      const refreshTokenValue = state.auth.refreshToken;
      
      if (refreshTokenValue) {
        try {
          await store.dispatch(refreshToken());
          const newState = store.getState();
          const newToken = newState.auth.token;
          
          if (newToken) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return api(originalRequest);
          }
        } catch (refreshError) {
          store.dispatch(logout());
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        store.dispatch(logout());
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

// دوال مساعدة للطلبات
export const apiRequest = {
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
};

// دوال للتعامل مع رفع الملفات
export const uploadFile = async (file, onProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  
  if (onProgress) {
    config.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    };
  }
  
  return api.post('/upload', formData, config);
};

// دوال للتعامل مع تحميل الملفات
export const downloadFile = async (url, filename) => {
  const response = await api.get(url, {
    responseType: 'blob',
  });
  
  const blob = new Blob([response.data]);
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(downloadUrl);
};

// دالة لمعالجة أخطاء API
export const handleApiError = (error) => {
  if (error.response) {
    // الخادم أرجع خطأ
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return data.detail || 'طلب غير صحيح';
      case 401:
        return 'غير مصرح لك بالوصول';
      case 403:
        return 'ليس لديك صلاحية للقيام بهذا الإجراء';
      case 404:
        return 'المورد المطلوب غير موجود';
      case 422:
        return data.detail || 'بيانات غير صحيحة';
      case 500:
        return 'خطأ في الخادم الداخلي';
      default:
        return data.detail || 'حدث خطأ غير متوقع';
    }
  } else if (error.request) {
    // لا يوجد رد من الخادم
    return 'لا يمكن الاتصال بالخادم';
  } else {
    // خطأ في إعداد الطلب
    return 'حدث خطأ في إرسال الطلب';
  }
};

// دالة لتنسيق المعاملات للـ URL
export const buildQueryString = (params) => {
  const searchParams = new URLSearchParams();
  
  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, item));
      } else {
        searchParams.append(key, value);
      }
    }
  });
  
  return searchParams.toString();
};

// دالة للتحقق من حالة الاتصال
export const checkConnection = async () => {
  try {
    await api.get('/health');
    return true;
  } catch (error) {
    return false;
  }
};

export default api;