"""
نقاط نهاية المصادقة
Authentication Endpoints
"""

from datetime import timedelta
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.config import settings
from app.core.security import (
    create_access_token, 
    create_refresh_token,
    verify_password,
    get_password_hash,
    verify_token
)
from app.models.user import User
from app.schemas.auth import Token, UserLogin, UserRegister
from app.schemas.user import UserResponse

router = APIRouter()

@router.post("/login", response_model=Token, summary="تسجيل الدخول")
async def login(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    تسجيل دخول المستخدم والحصول على رمز الوصول
    """
    # البحث عن المستخدم
    user = db.query(User).filter(
        (User.username == form_data.username) | 
        (User.email == form_data.username)
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="اسم المستخدم أو كلمة المرور غير صحيحة",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # التحقق من كلمة المرور
    if not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="اسم المستخدم أو كلمة المرور غير صحيحة",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # التحقق من حالة المستخدم
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="الحساب غير نشط",
        )
    
    # إنشاء رموز الوصول
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.id,
        expires_delta=access_token_expires,
        additional_claims={
            "username": user.username,
            "email": user.email,
            "is_superuser": user.is_superuser
        }
    )
    
    refresh_token = create_refresh_token(subject=user.id)
    
    # تحديث آخر تسجيل دخول
    from datetime import datetime
    user.last_login = datetime.utcnow()
    user.failed_login_attempts = 0
    db.commit()
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "is_superuser": user.is_superuser
        }
    }

@router.post("/register", response_model=UserResponse, summary="تسجيل مستخدم جديد")
async def register(
    user_data: UserRegister,
    db: Session = Depends(get_db)
) -> Any:
    """
    تسجيل مستخدم جديد
    """
    # التحقق من عدم وجود المستخدم
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | 
        (User.email == user_data.email)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل"
        )
    
    # إنشاء المستخدم الجديد
    hashed_password = get_password_hash(user_data.password)
    
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        phone=user_data.phone,
        is_verified=False,  # يحتاج تفعيل
        is_active=True
    )
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return new_user

@router.post("/refresh", response_model=Token, summary="تجديد رمز الوصول")
async def refresh_token(
    refresh_token: str,
    db: Session = Depends(get_db)
) -> Any:
    """
    تجديد رمز الوصول باستخدام رمز التجديد
    """
    # التحقق من رمز التجديد
    user_id = verify_token(refresh_token, token_type="refresh")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="رمز التجديد غير صالح أو منتهي الصلاحية",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # البحث عن المستخدم
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="المستخدم غير موجود أو غير نشط",
        )
    
    # إنشاء رمز وصول جديد
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.id,
        expires_delta=access_token_expires,
        additional_claims={
            "username": user.username,
            "email": user.email,
            "is_superuser": user.is_superuser
        }
    )
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,  # نفس رمز التجديد
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.post("/logout", summary="تسجيل الخروج")
async def logout(
    # يمكن إضافة منطق إلغاء الرموز هنا
) -> Any:
    """
    تسجيل خروج المستخدم
    """
    return {"message": "تم تسجيل الخروج بنجاح"}

# دالة مساعدة للحصول على المستخدم الحالي
async def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login"))
) -> User:
    """
    الحصول على المستخدم الحالي من الرمز المميز
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="لا يمكن التحقق من الهوية",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # التحقق من الرمز
    user_id = verify_token(token)
    
    if not user_id:
        raise credentials_exception
    
    # البحث عن المستخدم
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        raise credentials_exception
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المستخدم غير نشط"
        )
    
    return user

@router.get("/me", response_model=UserResponse, summary="معلومات المستخدم الحالي")
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    الحصول على معلومات المستخدم الحالي
    """
    return current_user

