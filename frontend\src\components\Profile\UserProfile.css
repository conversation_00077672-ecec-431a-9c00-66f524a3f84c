/* User Profile Styles */

.user-profile {
  padding: var(--space-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
  min-height: calc(100vh - var(--navbar-height));
}

/* ===== PROFILE HEADER ===== */
.profile-header {
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-header-content {
  gap: var(--space-6);
}

.profile-title h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-2) 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-title p {
  color: var(--neutral-600);
  font-size: var(--text-base);
  margin: 0;
}

.profile-actions {
  flex-shrink: 0;
}

/* ===== PROFILE CONTENT ===== */
.profile-content {
  gap: var(--space-6);
}

.profile-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* ===== BASIC INFO SECTION ===== */
.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
}

.profile-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-4xl);
  color: var(--neutral-0);
  box-shadow: var(--shadow-lg);
  flex-shrink: 0;
}

.profile-basic-details h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-1) 0;
}

.profile-role {
  color: var(--neutral-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin: 0 0 var(--space-2) 0;
}

.profile-status {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  text-transform: uppercase;
}

.profile-status--active {
  background: var(--success-100);
  color: var(--success-700);
}

.profile-status--inactive {
  background: var(--error-100);
  color: var(--error-700);
}

/* ===== PROFILE FIELDS ===== */
.profile-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.profile-field {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.profile-field label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
}

.profile-field span {
  font-size: var(--text-base);
  color: var(--neutral-800);
  padding: var(--space-3);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
}

.profile-field small {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  font-style: italic;
}

.profile-input {
  padding: var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--neutral-800);
  background: var(--neutral-0);
  transition: all var(--transition-fast);
  font-family: var(--font-family-arabic);
}

.profile-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.profile-input:hover {
  border-color: var(--primary-400);
}

/* ===== ACTIONS SECTION ===== */
.profile-actions-section {
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--neutral-200);
}

/* ===== SECURITY CARD ===== */
.security-card {
  margin-top: var(--space-6);
}

.security-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.security-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.security-item:hover {
  background: var(--neutral-100);
  border-color: var(--primary-300);
  transform: translateY(-1px);
}

.security-icon {
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.security-content {
  flex: 1;
}

.security-content h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-1) 0;
}

.security-content p {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  margin: 0;
}

/* ===== PERMISSIONS ===== */
.permissions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.permission-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--success-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--success-200);
}

.permission-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.permission-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--success-800);
}

.permissions-note {
  padding: var(--space-4);
  background: var(--warning-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--warning-200);
}

.permissions-note p {
  font-size: var(--text-sm);
  color: var(--warning-800);
  margin: 0;
}

/* ===== STATS CARD ===== */
.stats-card {
  margin-top: var(--space-6);
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.stat-item {
  text-align: center;
  padding: var(--space-4);
  background: var(--gradient-subtle);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-600);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .permissions-info {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .user-profile {
    padding: var(--space-4);
  }
  
  .profile-header {
    padding: var(--space-4);
    margin-bottom: var(--space-6);
  }
  
  .profile-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }
  
  .profile-title h1 {
    font-size: var(--text-2xl);
  }
  
  .profile-content {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .profile-avatar-large {
    width: 60px;
    height: 60px;
    font-size: var(--text-3xl);
  }
  
  .user-stats {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .security-item {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .user-profile {
    padding: var(--space-3);
  }
  
  .profile-header {
    padding: var(--space-3);
  }
  
  .profile-title h1 {
    font-size: var(--text-xl);
  }
  
  .profile-avatar-large {
    width: 50px;
    height: 50px;
    font-size: var(--text-2xl);
  }
  
  .stat-value {
    font-size: var(--text-xl);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .profile-title h1 {
    color: var(--neutral-200);
  }
  
  .profile-title p {
    color: var(--neutral-400);
  }
  
  .profile-basic-details h3 {
    color: var(--neutral-200);
  }
  
  .profile-role {
    color: var(--neutral-400);
  }
  
  .profile-field label {
    color: var(--neutral-300);
  }
  
  .profile-field span {
    color: var(--neutral-200);
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .profile-input {
    color: var(--neutral-200);
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .profile-input:focus {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px var(--primary-800);
  }
  
  .security-item {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .security-item:hover {
    background: var(--neutral-600);
    border-color: var(--primary-600);
  }
  
  .security-content h4 {
    color: var(--neutral-200);
  }
  
  .security-content p {
    color: var(--neutral-400);
  }
  
  .permission-item {
    background: var(--success-800);
    border-color: var(--success-600);
  }
  
  .permission-text {
    color: var(--success-200);
  }
  
  .permissions-note {
    background: var(--warning-800);
    border-color: var(--warning-600);
  }
  
  .permissions-note p {
    color: var(--warning-200);
  }
  
  .stat-item {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .stat-value {
    color: var(--primary-400);
  }
  
  .stat-label {
    color: var(--neutral-400);
  }
}