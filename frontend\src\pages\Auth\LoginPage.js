import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './LoginPage.css';

const LoginPage = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  // تأثيرات بصرية للخلفية
  useEffect(() => {
    const createFloatingElements = () => {
      const container = document.querySelector('.login-background');
      if (!container) return;

      for (let i = 0; i < 20; i++) {
        const element = document.createElement('div');
        element.className = 'floating-element';
        element.style.left = Math.random() * 100 + '%';
        element.style.animationDelay = Math.random() * 20 + 's';
        element.style.animationDuration = (Math.random() * 10 + 10) + 's';
        container.appendChild(element);
      }
    };

    createFloatingElements();

    return () => {
      const container = document.querySelector('.login-background');
      if (container) {
        const elements = container.querySelectorAll('.floating-element');
        elements.forEach(el => el.remove());
      }
    };
  }, []);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setError(''); // مسح الخطأ عند الكتابة
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // محاكاة عملية تسجيل الدخول
      await new Promise(resolve => setTimeout(resolve, 1500));

      // التحقق من بيانات الاعتماد (مؤقت)
      if ((formData.email === '<EMAIL>' || formData.email === 'admin') && formData.password === 'admin123') {
        // حفظ حالة تسجيل الدخول
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('user', JSON.stringify({
          name: 'مدير النظام',
          email: formData.email,
          role: 'admin',
          avatar: '👤'
        }));

        // توجيه إلى لوحة التحكم
        navigate('/dashboard');
      } else {
        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
      }
    } catch (err) {
      setError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setFormData({
      email: '<EMAIL>',
      password: 'admin123',
      rememberMe: false
    });
  };

  return (
    <div className="login-page">
      <div className="login-background">
        <div className="gradient-overlay"></div>
      </div>

      {/* زر العودة للصفحة الرئيسية */}
      <button 
        className="back-to-home-btn"
        onClick={() => navigate('/')}
        title="العودة للصفحة الرئيسية"
      >
        <span className="back-icon">🏠</span>
        <span className="back-text">الصفحة الرئيسية</span>
      </button>

      <div className="login-container">
        <div className="login-card">
          {/* شعار الشركة */}
          <div className="login-header">
            <div className="company-logo">
              <div className="logo-icon">✈️</div>
              <h1 className="company-name">شراء السفر</h1>
              <p className="company-tagline">نظام إدارة السفر والسياحة</p>
            </div>
          </div>

          {/* نموذج تسجيل الدخول */}
          <form className="login-form" onSubmit={handleSubmit}>
            <h2 className="form-title">تسجيل الدخول</h2>
            
            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="email" className="form-label">
                <span className="label-icon">📧</span>
                البريد الإلكتروني
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="form-input"
                placeholder="أدخل بريدك الإلكتروني"
                required
                disabled={isLoading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password" className="form-label">
                <span className="label-icon">🔒</span>
                كلمة المرور
              </label>
              <div className="password-input-container">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="أدخل كلمة المرور"
                  required
                  disabled={isLoading}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
            </div>

            <div className="form-options">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
                <span className="checkbox-custom"></span>
                تذكرني
              </label>
              <a href="#" className="forgot-password">نسيت كلمة المرور؟</a>
            </div>

            <button
              type="submit"
              className={`login-button ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="loading-spinner"></span>
                  جاري تسجيل الدخول...
                </>
              ) : (
                <>
                  <span className="button-icon">🚀</span>
                  تسجيل الدخول
                </>
              )}
            </button>

            <div className="demo-section">
              <p className="demo-text">للتجربة السريعة:</p>
              <button
                type="button"
                className="demo-button"
                onClick={handleDemoLogin}
                disabled={isLoading}
              >
                <span className="demo-icon">⚡</span>
                ملء البيانات التجريبية
              </button>
              
              <div className="demo-credentials">
                <p className="credentials-title">بيانات تسجيل الدخول التجريبية:</p>
                <div className="credential-item">
                  <strong>البريد الإلكتروني:</strong> <EMAIL>
                </div>
                <div className="credential-item">
                  <strong>أو اكتب:</strong> admin
                </div>
                <div className="credential-item">
                  <strong>كلمة المرور:</strong> admin123
                </div>
              </div>
            </div>
          </form>

          {/* معلومات إضافية */}
          <div className="login-footer">
            <div className="features-list">
              <div className="feature-item">
                <span className="feature-icon">🎯</span>
                <span>إدارة شاملة للحجوزات</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📊</span>
                <span>تقارير مفصلة ومتقدمة</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔒</span>
                <span>أمان عالي وحماية البيانات</span>
              </div>
            </div>
            
            <div className="copyright">
              <p>© 2024 شراء السفر. جميع الحقوق محفوظة.</p>
              <p className="version">الإصدار 2.0.0</p>
            </div>
          </div>
        </div>

        {/* معلومات جانبية */}
        <div className="info-panel">
          <div className="info-content">
            <h3 className="info-title">مرحباً بك في نظام شراء السفر</h3>
            <p className="info-description">
              نظام متكامل لإدارة شركات السفر والسياحة مع أحدث التقنيات
              وأفضل معايير الأمان والجودة.
            </p>
            
            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-number">1000+</div>
                <div className="stat-label">عميل راضي</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">50+</div>
                <div className="stat-label">وجهة سياحية</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">24/7</div>
                <div className="stat-label">دعم فني</div>
              </div>
            </div>

            <div className="contact-info">
              <h4>تواصل معنا</h4>
              <div className="contact-item">
                <span className="contact-icon">📞</span>
                <span>+966 50 123 4567</span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📧</span>
                <span><EMAIL></span>
              </div>
              <div className="contact-item">
                <span className="contact-icon">🌐</span>
                <span>www.sharaubtravelsoft.com</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;