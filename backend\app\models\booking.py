"""
نموذج الحجوزات
Booking Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Decimal, ForeignKey, Enum, JSON
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.models.base import BaseModel

class BookingType(PyEnum):
    """أنواع الحجوزات"""
    FLIGHT = "flight"           # طيران
    HOTEL = "hotel"            # فندق
    CAR_RENTAL = "car_rental"  # تأجير سيارات
    TOUR = "tour"              # جولة سياحية
    PACKAGE = "package"        # باقة سياحية
    VISA = "visa"              # تأشيرة
    INSURANCE = "insurance"    # تأمين
    TRANSFER = "transfer"      # نقل

class BookingStatus(PyEnum):
    """حالة الحجز"""
    DRAFT = "draft"                    # مسودة
    PENDING = "pending"                # في الانتظار
    CONFIRMED = "confirmed"            # مؤكد
    CANCELLED = "cancelled"            # ملغي
    COMPLETED = "completed"            # مكتمل
    REFUNDED = "refunded"             # مسترد
    PARTIALLY_REFUNDED = "partially_refunded"  # مسترد جزئياً

class PaymentStatus(PyEnum):
    """حالة الدفع"""
    UNPAID = "unpaid"          # غير مدفوع
    PARTIALLY_PAID = "partially_paid"  # مدفوع جزئياً
    PAID = "paid"              # مدفوع
    REFUNDED = "refunded"      # مسترد
    OVERDUE = "overdue"        # متأخر

class Booking(BaseModel):
    """
    نموذج الحجوزات الرئيسي
    """
    __tablename__ = "bookings"
    
    # المعلومات الأساسية
    booking_number = Column(String(50), unique=True, nullable=False, index=True)
    booking_type = Column(Enum(BookingType), nullable=False)
    status = Column(Enum(BookingStatus), nullable=False, default=BookingStatus.DRAFT)
    payment_status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.UNPAID)
    
    # العلاقات الأساسية
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=True)
    
    # تواريخ مهمة
    booking_date = Column(DateTime, nullable=False)
    travel_date = Column(DateTime, nullable=True)
    return_date = Column(DateTime, nullable=True)
    confirmation_date = Column(DateTime, nullable=True)
    cancellation_date = Column(DateTime, nullable=True)
    
    # المعلومات المالية
    base_price = Column(Decimal(12, 2), nullable=False, default=0.00)
    taxes = Column(Decimal(12, 2), nullable=False, default=0.00)
    fees = Column(Decimal(12, 2), nullable=False, default=0.00)
    discount = Column(Decimal(12, 2), nullable=False, default=0.00)
    total_amount = Column(Decimal(12, 2), nullable=False, default=0.00)
    paid_amount = Column(Decimal(12, 2), nullable=False, default=0.00)
    currency = Column(String(3), nullable=False, default='USD')
    
    # معلومات العمولة
    commission_rate = Column(Decimal(5, 2), default=0.00)
    commission_amount = Column(Decimal(12, 2), default=0.00)
    agent_commission_rate = Column(Decimal(5, 2), default=0.00)
    agent_commission_amount = Column(Decimal(12, 2), default=0.00)
    
    # معلومات إضافية
    reference_number = Column(String(100), nullable=True)  # رقم مرجعي من المورد
    confirmation_number = Column(String(100), nullable=True)  # رقم التأكيد
    pnr = Column(String(20), nullable=True)  # رقم الحجز للطيران
    
    # تفاصيل الحجز (JSON)
    booking_details = Column(JSON, nullable=True)
    
    # ملاحظات
    internal_notes = Column(Text, nullable=True)  # ملاحظات داخلية
    customer_notes = Column(Text, nullable=True)  # ملاحظات العميل
    
    # العلاقات
    customer = relationship("Customer", back_populates="bookings")
    supplier = relationship("Supplier", back_populates="bookings")
    agent = relationship("Agent", back_populates="bookings")
    passengers = relationship("BookingPassenger", back_populates="booking")
    services = relationship("BookingService", back_populates="booking")
    payments = relationship("BookingPayment", back_populates="booking")
    documents = relationship("BookingDocument", back_populates="booking")
    
    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount
    
    @property
    def is_fully_paid(self):
        """هل الحجز مدفوع بالكامل"""
        return self.paid_amount >= self.total_amount
    
    def __repr__(self):
        return f"<Booking(number='{self.booking_number}', type='{self.booking_type}')>"

class BookingPassenger(BaseModel):
    """
    ركاب الحجز
    """
    __tablename__ = "booking_passengers"
    
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=False)
    passenger_type = Column(String(20), nullable=False)  # adult, child, infant
    
    # معلومات شخصية
    title = Column(String(10), nullable=True)  # Mr, Mrs, Ms, Dr
    first_name = Column(String(50), nullable=False)
    middle_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=False)
    birth_date = Column(DateTime, nullable=True)
    gender = Column(String(10), nullable=True)
    nationality = Column(String(50), nullable=True)
    
    # معلومات جواز السفر
    passport_number = Column(String(50), nullable=True)
    passport_expiry = Column(DateTime, nullable=True)
    passport_country = Column(String(50), nullable=True)
    
    # معلومات إضافية
    special_requests = Column(Text, nullable=True)
    meal_preference = Column(String(50), nullable=True)
    seat_preference = Column(String(50), nullable=True)
    
    # العلاقات
    booking = relationship("Booking", back_populates="passengers")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        names = [self.title, self.first_name, self.middle_name, self.last_name]
        return " ".join(filter(None, names))
    
    def __repr__(self):
        return f"<BookingPassenger(name='{self.full_name}', type='{self.passenger_type}')>"

class BookingService(BaseModel):
    """
    خدمات الحجز
    """
    __tablename__ = "booking_services"
    
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=False)
    service_type = Column(String(50), nullable=False)
    service_name = Column(String(100), nullable=False)
    
    # تفاصيل الخدمة
    description = Column(Text, nullable=True)
    quantity = Column(Integer, default=1)
    unit_price = Column(Decimal(10, 2), nullable=False)
    total_price = Column(Decimal(10, 2), nullable=False)
    
    # تواريخ الخدمة
    service_date = Column(DateTime, nullable=True)
    service_time = Column(String(10), nullable=True)
    
    # تفاصيل إضافية (JSON)
    service_details = Column(JSON, nullable=True)
    
    # العلاقات
    booking = relationship("Booking", back_populates="services")
    
    def __repr__(self):
        return f"<BookingService(name='{self.service_name}', price={self.total_price})>"

class BookingPayment(BaseModel):
    """
    مدفوعات الحجز
    """
    __tablename__ = "booking_payments"
    
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=False)
    payment_method = Column(String(50), nullable=False)  # cash, card, bank_transfer, etc.
    
    amount = Column(Decimal(12, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    exchange_rate = Column(Decimal(10, 4), default=1.0000)
    
    payment_date = Column(DateTime, nullable=False)
    reference_number = Column(String(100), nullable=True)
    
    # معلومات إضافية
    payment_details = Column(JSON, nullable=True)
    
    # العلاقات
    booking = relationship("Booking", back_populates="payments")
    
    def __repr__(self):
        return f"<BookingPayment(booking_id={self.booking_id}, amount={self.amount})>"

class BookingDocument(BaseModel):
    """
    مستندات الحجز
    """
    __tablename__ = "booking_documents"
    
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=False)
    document_type = Column(String(50), nullable=False)  # ticket, voucher, invoice, etc.
    document_name = Column(String(100), nullable=False)
    
    file_path = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=True)
    mime_type = Column(String(100), nullable=True)
    
    # العلاقات
    booking = relationship("Booking", back_populates="documents")
    
    def __repr__(self):
        return f"<BookingDocument(type='{self.document_type}', name='{self.document_name}')>"

class BookingHistory(BaseModel):
    """
    تاريخ تغييرات الحجز
    """
    __tablename__ = "booking_history"
    
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=False)
    action = Column(String(50), nullable=False)  # created, updated, confirmed, cancelled
    
    old_status = Column(String(50), nullable=True)
    new_status = Column(String(50), nullable=True)
    
    description = Column(Text, nullable=True)
    changed_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # العلاقات
    booking = relationship("Booking")
    user = relationship("User")
    
    def __repr__(self):
        return f"<BookingHistory(booking_id={self.booking_id}, action='{self.action}')>"