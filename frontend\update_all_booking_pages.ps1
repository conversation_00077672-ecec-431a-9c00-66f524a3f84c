# سكريبت PowerShell لتحديث جميع صفحات الحجوزات

$bookingPages = @(
    @{File="UmrahBookingPage.js"; Type="العمرة"},
    @{File="PassportBookingPage.js"; Type="الجوازات"},
    @{File="BusBookingPage.js"; Type="الباصات"},
    @{File="CarBookingPage.js"; Type="السيارات"},
    @{File="DocumentAuthenticationPage.js"; Type="تعميد الوثائق"}
)

$basePath = "src\pages\Bookings\"

# الاستيرادات المطلوبة
$importsToAdd = @"
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';
"@

# المتغيرات المطلوبة
$stateVariables = @"
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);
"@

function Add-BookingFunctions {
    param($bookingType)
    
    return @"

  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    handleDeleteBooking(booking.id);
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, '$bookingType');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, '$bookingType');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, '$bookingType');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, '$bookingType');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(`هل أنت متأكد من حذف `${selectedIds.length}` حجز؟`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };
"@
}

$bulkActionsComponent = @"
      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

"@

$detailsModal = @"

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />
"@

Write-Host "تحديث صفحات الحجوزات..." -ForegroundColor Green

foreach ($page in $bookingPages) {
    $filePath = Join-Path $basePath $page.File
    
    if (Test-Path $filePath) {
        Write-Host "تحديث $($page.File)..." -ForegroundColor Yellow
        
        $content = Get-Content $filePath -Raw -Encoding UTF8
        
        # إضافة الاستيرادات
        if ($content -notmatch "BookingActions") {
            $content = $content -replace "import React, { useState, useEffect } from 'react';", 
                "import React, { useState, useEffect } from 'react';`n$importsToAdd"
        }
        
        # إضافة المتغيرات
        if ($content -notmatch "selectedBookings") {
            $content = $content -replace "(\s+const \[filterStatus, setFilterStatus\] = useState\('all'\);)", 
                "`$1`n$stateVariables"
        }
        
        # إضافة الدوال
        $functions = Add-BookingFunctions -bookingType $page.Type
        if ($content -notmatch "handleViewBooking") {
            $content = $content -replace "(const handleDeleteBooking = \(id\) => \{[^}]+\};)", 
                "`$1$functions"
        }
        
        # إضافة عمود التحديد في الرأس
        if ($content -notmatch "checkbox.*selectedBookings") {
            $checkboxHeader = @"
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
"@
            $content = $content -replace "(<thead>\s*<tr[^>]*>\s*)", "`$1$checkboxHeader"
        }
        
        # إضافة مكون العمليات المجمعة
        if ($content -notmatch "BulkActions") {
            $content = $content -replace "(\s*{/\* Bookings Table \*/})", 
                "`n$bulkActionsComponent`$1"
        }
        
        # إضافة نافذة التفاصيل
        if ($content -notmatch "BookingDetailsModal") {
            $content = $content -replace "(export default [^;]+;)", 
                "$detailsModal`n    </div>`n  );`n};`n`n`$1"
        }
        
        Set-Content $filePath $content -Encoding UTF8
        Write-Host "تم تحديث $($page.File) بنجاح!" -ForegroundColor Green
    } else {
        Write-Host "الملف غير موجود: $filePath" -ForegroundColor Red
    }
}

Write-Host "`nتم الانتهاء من تحديث جميع الصفحات!" -ForegroundColor Green