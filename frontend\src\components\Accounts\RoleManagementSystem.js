import React, { useState, useEffect } from 'react';
import AdvancedPermissionsSystem from './AdvancedPermissionsSystem';
import './RoleManagementSystem.css';

const RoleManagementSystem = () => {
  const [roles, setRoles] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [showCreateRole, setShowCreateRole] = useState(false);
  const [showEditRole, setShowEditRole] = useState(false);
  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    color: '#007bff',
    icon: '👤',
    permissions: []
  });

  // الأدوار المحددة مسبقاً
  const predefinedRoles = {
    admin: {
      id: 'admin',
      name: 'مدير النظام',
      description: 'صلاحيات كاملة لجميع أجزاء النظام',
      permissions: ['*'],
      color: '#dc3545',
      icon: '👑',
      isSystem: true
    },
    financial_manager: {
      id: 'financial_manager',
      name: 'مدير مالي',
      description: 'إدارة كاملة للشؤون المالية والمحاسبية',
      permissions: [
        'dashboard.view', 'dashboard.view_analytics',
        'finance.*',
        'accounts.*',
        'reports.financial_reports', 'reports.export', 'reports.analytics',
        'customers.view', 'customers.view_financial_info',
        'suppliers.view', 'suppliers.approve_payments',
        'agents.view', 'agents.manage_commissions'
      ],
      color: '#28a745',
      icon: '💼',
      isSystem: true
    },
    accountant: {
      id: 'accountant',
      name: 'محاسب',
      description: 'صلاحيات محاسبية كاملة مع إمكانية الترحيل',
      permissions: [
        'dashboard.view',
        'finance.view', 'finance.create_entries', 'finance.edit_entries', 'finance.post_entries',
        'finance.view_trial_balance', 'finance.view_income_statement', 'finance.view_balance_sheet',
        'finance.bank_reconciliation', 'finance.export_reports',
        'accounts.view', 'accounts.create', 'accounts.edit',
        'reports.financial_reports', 'reports.export',
        'customers.view', 'suppliers.view'
      ],
      color: '#17a2b8',
      icon: '📊',
      isSystem: true
    },
    bookkeeper: {
      id: 'bookkeeper',
      name: 'مسك دفاتر',
      description: 'إدخال القيود وعرض التقارير الأساسية',
      permissions: [
        'dashboard.view',
        'finance.view', 'finance.create_entries', 'finance.edit_entries',
        'finance.view_trial_balance',
        'accounts.view',
        'reports.view', 'reports.financial_reports',
        'customers.view', 'suppliers.view'
      ],
      color: '#ffc107',
      icon: '📝',
      isSystem: true
    },
    sales_manager: {
      id: 'sales_manager',
      name: 'مدير مبيعات',
      description: 'إدارة العملاء والحجوزات والمبيعات',
      permissions: [
        'dashboard.view', 'dashboard.view_analytics',
        'bookings.*',
        'flight_bookings.*',
        'umrah_bookings.*',
        'hajj_bookings.*',
        'customers.*',
        'sales.*',
        'agents.view', 'agents.manage_commissions',
        'reports.sales_reports', 'reports.operational_reports'
      ],
      color: '#fd7e14',
      icon: '🎯',
      isSystem: true
    },
    operations_manager: {
      id: 'operations_manager',
      name: 'مدير عمليات',
      description: 'إدارة العمليات التشغيلية والموردين',
      permissions: [
        'dashboard.view', 'dashboard.view_analytics',
        'bookings.view', 'bookings.confirm', 'bookings.cancel',
        'suppliers.*',
        'inventory.*',
        'visa_inventory.*',
        'customers.view', 'customers.view_history',
        'reports.operational_reports',
        'purchases.view', 'purchases.approve'
      ],
      color: '#6f42c1',
      icon: '⚡',
      isSystem: true
    },
    customer_service: {
      id: 'customer_service',
      name: 'خدمة العملاء',
      description: 'التعامل مع العملاء وإدارة الحجوزات الأساسية',
      permissions: [
        'dashboard.view',
        'customers.view', 'customers.create', 'customers.edit',
        'bookings.view', 'bookings.create', 'bookings.edit',
        'flight_bookings.view', 'flight_bookings.create', 'flight_bookings.edit',
        'umrah_bookings.view', 'umrah_bookings.create', 'umrah_bookings.edit',
        'passport_services.*',
        'document_authentication.*'
      ],
      color: '#20c997',
      icon: '🎧',
      isSystem: true
    },
    auditor: {
      id: 'auditor',
      name: 'مراجع',
      description: 'مراجعة العمليات والتقارير دون إمكانية التعديل',
      permissions: [
        'dashboard.view',
        'finance.view', 'finance.view_trial_balance', 'finance.view_income_statement', 'finance.view_balance_sheet',
        'accounts.view', 'accounts.view_transactions',
        'bookings.view', 'bookings.view_history',
        'customers.view', 'customers.view_history',
        'suppliers.view', 'suppliers.view_performance',
        'reports.*',
        'system.view_logs', 'system.security_audit'
      ],
      color: '#e83e8c',
      icon: '🔍',
      isSystem: true
    },
    viewer: {
      id: 'viewer',
      name: 'مستعلم',
      description: 'عرض البيانات فقط دون إمكانية التعديل',
      permissions: [
        'dashboard.view',
        'customers.view',
        'bookings.view',
        'suppliers.view',
        'reports.view'
      ],
      color: '#6c757d',
      icon: '👁️',
      isSystem: true
    }
  };

  useEffect(() => {
    // تحميل الأدوار من التخزين المحلي أو API
    const savedRoles = localStorage.getItem('systemRoles');
    if (savedRoles) {
      setRoles(JSON.parse(savedRoles));
    } else {
      // تحميل الأدوار المحددة مسبقاً
      setRoles(Object.values(predefinedRoles));
    }
  }, []);

  // حفظ الأدوار في التخزين المحلي
  const saveRoles = (updatedRoles) => {
    setRoles(updatedRoles);
    localStorage.setItem('systemRoles', JSON.stringify(updatedRoles));
  };

  // إنشاء دور جديد
  const createRole = () => {
    if (!newRole.name.trim()) {
      alert('يرجى إدخال اسم الدور');
      return;
    }

    const roleId = newRole.name.toLowerCase().replace(/\s+/g, '_');
    const role = {
      ...newRole,
      id: roleId,
      isSystem: false,
      createdAt: new Date().toISOString()
    };

    const updatedRoles = [...roles, role];
    saveRoles(updatedRoles);
    setNewRole({ name: '', description: '', color: '#007bff', icon: '👤', permissions: [] });
    setShowCreateRole(false);
  };

  // تحديث دور
  const updateRole = (roleId, updates) => {
    const updatedRoles = roles.map(role => 
      role.id === roleId ? { ...role, ...updates } : role
    );
    saveRoles(updatedRoles);
  };

  // حذف دور
  const deleteRole = (roleId) => {
    const role = roles.find(r => r.id === roleId);
    if (role?.isSystem) {
      alert('لا يمكن حذف الأدوار النظامية');
      return;
    }

    if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
      const updatedRoles = roles.filter(role => role.id !== roleId);
      saveRoles(updatedRoles);
      if (selectedRole?.id === roleId) {
        setSelectedRole(null);
      }
    }
  };

  // تحديث صلاحيات الدور
  const updateRolePermissions = (permissions) => {
    if (selectedRole) {
      updateRole(selectedRole.id, { permissions });
      setSelectedRole({ ...selectedRole, permissions });
    }
  };

  return (
    <div className="role-management-system">
      <div className="role-management-header">
        <h2>🔐 إدارة الأدوار والصلاحيات</h2>
        <button 
          className="create-role-btn"
          onClick={() => setShowCreateRole(true)}
        >
          ➕ إنشاء دور جديد
        </button>
      </div>

      <div className="role-management-content">
        <div className="roles-sidebar">
          <h3>الأدوار المتاحة</h3>
          <div className="roles-list">
            {roles.map(role => (
              <div
                key={role.id}
                className={`role-item ${selectedRole?.id === role.id ? 'active' : ''}`}
                onClick={() => setSelectedRole(role)}
              >
                <div className="role-header">
                  <span className="role-icon" style={{ color: role.color }}>
                    {role.icon}
                  </span>
                  <div className="role-info">
                    <h4>{role.name}</h4>
                    <p>{role.description}</p>
                  </div>
                  {role.isSystem && (
                    <span className="system-badge">نظامي</span>
                  )}
                </div>
                <div className="role-stats">
                  <span>الصلاحيات: {role.permissions.length}</span>
                </div>
                {!role.isSystem && (
                  <div className="role-actions">
                    <button 
                      className="edit-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedRole(role);
                        setShowEditRole(true);
                      }}
                    >
                      ✏️
                    </button>
                    <button 
                      className="delete-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteRole(role.id);
                      }}
                    >
                      🗑️
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="role-details">
          {selectedRole ? (
            <>
              <div className="role-details-header">
                <h3>
                  <span style={{ color: selectedRole.color }}>{selectedRole.icon}</span>
                  {selectedRole.name}
                </h3>
                <p>{selectedRole.description}</p>
              </div>
              
              <AdvancedPermissionsSystem
                user={{ permissions: selectedRole.permissions }}
                onPermissionsChange={updateRolePermissions}
              />
            </>
          ) : (
            <div className="no-role-selected">
              <h3>اختر دوراً لعرض تفاصيله</h3>
              <p>قم بالنقر على أحد الأدوار من القائمة الجانبية لعرض وتعديل صلاحياته</p>
            </div>
          )}
        </div>
      </div>

      {/* نافذة إنشاء دور جديد */}
      {showCreateRole && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>إنشاء دور جديد</h3>
              <button onClick={() => setShowCreateRole(false)}>✕</button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>اسم الدور</label>
                <input
                  type="text"
                  value={newRole.name}
                  onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                  placeholder="أدخل اسم الدور"
                />
              </div>
              <div className="form-group">
                <label>الوصف</label>
                <textarea
                  value={newRole.description}
                  onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                  placeholder="أدخل وصف الدور"
                />
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>اللون</label>
                  <input
                    type="color"
                    value={newRole.color}
                    onChange={(e) => setNewRole({ ...newRole, color: e.target.value })}
                  />
                </div>
                <div className="form-group">
                  <label>الأيقونة</label>
                  <input
                    type="text"
                    value={newRole.icon}
                    onChange={(e) => setNewRole({ ...newRole, icon: e.target.value })}
                    placeholder="🔧"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button onClick={() => setShowCreateRole(false)}>إلغاء</button>
              <button onClick={createRole} className="primary">إنشاء</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleManagementSystem;
