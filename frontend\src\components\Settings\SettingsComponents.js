import React, { useState } from 'react';
import './SettingsComponents.css';

// مكون اختبار الاتصال
export const ConnectionTest = ({ apiName, apiKey, onTest }) => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState(null);

  const handleTest = async () => {
    setTesting(true);
    setResult(null);
    
    try {
      // محاكاة اختبار الاتصال
      await new Promise(resolve => setTimeout(resolve, 2000));
      const success = Math.random() > 0.3; // 70% نجاح
      
      setResult({
        success,
        message: success 
          ? `تم الاتصال بـ ${apiName} بنجاح` 
          : `فشل في الاتصال بـ ${apiName}`
      });
      
      if (onTest) onTest(success);
    } catch (error) {
      setResult({
        success: false,
        message: `خطأ في اختبار الاتصال: ${error.message}`
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="connection-test">
      <button 
        className={`test-btn ${testing ? 'testing' : ''}`}
        onClick={handleTest}
        disabled={testing || !apiKey}
      >
        {testing ? '🔄 جاري الاختبار...' : '🔍 اختبار الاتصال'}
      </button>
      
      {result && (
        <div className={`test-result ${result.success ? 'success' : 'error'}`}>
          {result.success ? '✅' : '❌'} {result.message}
        </div>
      )}
    </div>
  );
};

// مكون إدارة النسخ الاحتياطي
export const BackupManager = () => {
  const [backups, setBackups] = useState([
    {
      id: 1,
      name: 'نسخة احتياطية تلقائية',
      date: '2024-01-15 10:30:00',
      size: '2.5 MB',
      type: 'auto'
    },
    {
      id: 2,
      name: 'نسخة احتياطية يدوية',
      date: '2024-01-14 15:45:00',
      size: '2.3 MB',
      type: 'manual'
    },
    {
      id: 3,
      name: 'نسخة احتياطية أسبوعية',
      date: '2024-01-10 02:00:00',
      size: '2.1 MB',
      type: 'scheduled'
    }
  ]);

  const [creating, setCreating] = useState(false);

  const createBackup = async () => {
    setCreating(true);
    
    // محاكاة إنشاء نسخة احتياطية
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const newBackup = {
      id: Date.now(),
      name: 'نسخة احتياطية يدوية',
      date: new Date().toLocaleString('ar-SA'),
      size: '2.6 MB',
      type: 'manual'
    };
    
    setBackups(prev => [newBackup, ...prev]);
    setCreating(false);
  };

  const downloadBackup = (backup) => {
    // محاكاة تحميل النسخة الاحتياطية
    alert(`جاري تحميل: ${backup.name}`);
  };

  const deleteBackup = (backupId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
      setBackups(prev => prev.filter(b => b.id !== backupId));
    }
  };

  return (
    <div className="backup-manager">
      <div className="backup-header">
        <h4>إدارة النسخ الاحتياطية</h4>
        <button 
          className={`create-backup-btn ${creating ? 'creating' : ''}`}
          onClick={createBackup}
          disabled={creating}
        >
          {creating ? '⏳ جاري الإنشاء...' : '💾 إنشاء نسخة احتياطية'}
        </button>
      </div>

      <div className="backup-list">
        {backups.map(backup => (
          <div key={backup.id} className="backup-item">
            <div className="backup-info">
              <div className="backup-name">
                <span className={`backup-type-icon ${backup.type}`}>
                  {backup.type === 'auto' && '🤖'}
                  {backup.type === 'manual' && '👤'}
                  {backup.type === 'scheduled' && '📅'}
                </span>
                {backup.name}
              </div>
              <div className="backup-details">
                <span className="backup-date">{backup.date}</span>
                <span className="backup-size">{backup.size}</span>
              </div>
            </div>
            <div className="backup-actions">
              <button 
                className="download-btn"
                onClick={() => downloadBackup(backup)}
              >
                📥 تحميل
              </button>
              <button 
                className="delete-btn"
                onClick={() => deleteBackup(backup.id)}
              >
                🗑️ حذف
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// مكون إدارة المستخدمين
export const UserManagement = () => {
  const [users, setUsers] = useState([
    {
      id: 1,
      name: 'أحمد محمد',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2024-01-15 14:30:00'
    },
    {
      id: 2,
      name: 'فاطمة علي',
      email: '<EMAIL>',
      role: 'manager',
      status: 'active',
      lastLogin: '2024-01-15 12:15:00'
    },
    {
      id: 3,
      name: 'محمد سالم',
      email: '<EMAIL>',
      role: 'employee',
      status: 'inactive',
      lastLogin: '2024-01-10 09:45:00'
    }
  ]);

  const [showAddUser, setShowAddUser] = useState(false);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    role: 'employee',
    password: ''
  });

  const addUser = () => {
    if (newUser.name && newUser.email && newUser.password) {
      const user = {
        id: Date.now(),
        ...newUser,
        status: 'active',
        lastLogin: 'لم يسجل دخول بعد'
      };
      setUsers(prev => [...prev, user]);
      setNewUser({ name: '', email: '', role: 'employee', password: '' });
      setShowAddUser(false);
    }
  };

  const toggleUserStatus = (userId) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' }
        : user
    ));
  };

  const deleteUser = (userId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      setUsers(prev => prev.filter(user => user.id !== userId));
    }
  };

  const getRoleLabel = (role) => {
    const roles = {
      admin: 'مدير النظام',
      manager: 'مدير',
      employee: 'موظف'
    };
    return roles[role] || role;
  };

  return (
    <div className="user-management">
      <div className="user-header">
        <h4>إدارة المستخدمين</h4>
        <button 
          className="add-user-btn"
          onClick={() => setShowAddUser(!showAddUser)}
        >
          {showAddUser ? '❌ إلغاء' : '➕ إضافة مستخدم'}
        </button>
      </div>

      {showAddUser && (
        <div className="add-user-form">
          <div className="form-grid">
            <input
              type="text"
              placeholder="اسم المستخدم"
              value={newUser.name}
              onChange={(e) => setNewUser({...newUser, name: e.target.value})}
            />
            <input
              type="email"
              placeholder="البريد الإلكتروني"
              value={newUser.email}
              onChange={(e) => setNewUser({...newUser, email: e.target.value})}
            />
            <select
              value={newUser.role}
              onChange={(e) => setNewUser({...newUser, role: e.target.value})}
            >
              <option value="employee">موظف</option>
              <option value="manager">مدير</option>
              <option value="admin">مدير النظام</option>
            </select>
            <input
              type="password"
              placeholder="كلمة المرور"
              value={newUser.password}
              onChange={(e) => setNewUser({...newUser, password: e.target.value})}
            />
          </div>
          <button className="save-user-btn" onClick={addUser}>
            💾 حفظ المستخدم
          </button>
        </div>
      )}

      <div className="user-list">
        {users.map(user => (
          <div key={user.id} className="user-item">
            <div className="user-info">
              <div className="user-name">{user.name}</div>
              <div className="user-email">{user.email}</div>
              <div className="user-role">{getRoleLabel(user.role)}</div>
              <div className="user-last-login">آخر دخول: {user.lastLogin}</div>
            </div>
            <div className="user-status">
              <span className={`status-badge ${user.status}`}>
                {user.status === 'active' ? 'نشط' : 'غير نشط'}
              </span>
            </div>
            <div className="user-actions">
              <button 
                className="toggle-status-btn"
                onClick={() => toggleUserStatus(user.id)}
              >
                {user.status === 'active' ? '⏸️ إيقاف' : '▶️ تفعيل'}
              </button>
              <button 
                className="delete-user-btn"
                onClick={() => deleteUser(user.id)}
              >
                🗑️ حذف
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// مكون إعدادات البريد الإلكتروني
export const EmailSettings = ({ settings, onUpdate }) => {
  const [emailConfig, setEmailConfig] = useState({
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    fromName: 'شركة شراء السفر',
    fromEmail: '',
    encryption: 'tls',
    ...settings
  });

  const [testing, setTesting] = useState(false);

  const updateConfig = (key, value) => {
    const newConfig = { ...emailConfig, [key]: value };
    setEmailConfig(newConfig);
    if (onUpdate) onUpdate(newConfig);
  };

  const testEmailConfig = async () => {
    setTesting(true);
    
    // محاكاة اختبار إعدادات البريد
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const success = Math.random() > 0.3;
    alert(success ? 'تم اختبار البريد الإلكتروني بنجاح' : 'فشل في اختبار البريد الإلكتروني');
    
    setTesting(false);
  };

  return (
    <div className="email-settings">
      <h4>إعدادات البريد الإلكتروني</h4>
      
      <div className="email-config-grid">
        <div className="config-item">
          <label>خادم SMTP</label>
          <input
            type="text"
            value={emailConfig.smtpHost}
            onChange={(e) => updateConfig('smtpHost', e.target.value)}
            placeholder="smtp.gmail.com"
          />
        </div>

        <div className="config-item">
          <label>منفذ SMTP</label>
          <input
            type="number"
            value={emailConfig.smtpPort}
            onChange={(e) => updateConfig('smtpPort', parseInt(e.target.value))}
            placeholder="587"
          />
        </div>

        <div className="config-item">
          <label>اسم المستخدم</label>
          <input
            type="email"
            value={emailConfig.smtpUser}
            onChange={(e) => updateConfig('smtpUser', e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>

        <div className="config-item">
          <label>كلمة المرور</label>
          <input
            type="password"
            value={emailConfig.smtpPassword}
            onChange={(e) => updateConfig('smtpPassword', e.target.value)}
            placeholder="كلمة مرور البريد"
          />
        </div>

        <div className="config-item">
          <label>اسم المرسل</label>
          <input
            type="text"
            value={emailConfig.fromName}
            onChange={(e) => updateConfig('fromName', e.target.value)}
            placeholder="شركة شراء السفر"
          />
        </div>

        <div className="config-item">
          <label>بريد المرسل</label>
          <input
            type="email"
            value={emailConfig.fromEmail}
            onChange={(e) => updateConfig('fromEmail', e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>

        <div className="config-item">
          <label>نوع التشفير</label>
          <select
            value={emailConfig.encryption}
            onChange={(e) => updateConfig('encryption', e.target.value)}
          >
            <option value="tls">TLS</option>
            <option value="ssl">SSL</option>
            <option value="none">بدون تشفير</option>
          </select>
        </div>
      </div>

      <div className="email-test">
        <button 
          className={`test-email-btn ${testing ? 'testing' : ''}`}
          onClick={testEmailConfig}
          disabled={testing}
        >
          {testing ? '⏳ جاري الاختبار...' : '📧 اختبار إعدادات البريد'}
        </button>
      </div>
    </div>
  );
};

// مكون إحصائيات النظام
export const SystemStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 15,
    activeUsers: 12,
    totalBookings: 1250,
    totalRevenue: 2500000,
    systemUptime: '15 يوم، 8 ساعات',
    lastBackup: '2024-01-15 02:00:00',
    databaseSize: '125 MB',
    storageUsed: '2.3 GB'
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <div className="system-stats">
      <h4>إحصائيات النظام</h4>
      
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalUsers}</div>
            <div className="stat-label">إجمالي المستخدمين</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <div className="stat-value">{stats.activeUsers}</div>
            <div className="stat-label">المستخدمين النشطين</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalBookings}</div>
            <div className="stat-label">إجمالي الحجوزات</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <div className="stat-value">{formatCurrency(stats.totalRevenue)}</div>
            <div className="stat-label">إجمالي الإيرادات</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">⏱️</div>
          <div className="stat-content">
            <div className="stat-value">{stats.systemUptime}</div>
            <div className="stat-label">وقت تشغيل النظام</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💾</div>
          <div className="stat-content">
            <div className="stat-value">{stats.lastBackup}</div>
            <div className="stat-label">آخر نسخة احتياطية</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">🗄️</div>
          <div className="stat-content">
            <div className="stat-value">{stats.databaseSize}</div>
            <div className="stat-label">حجم قاعدة البيانات</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💽</div>
          <div className="stat-content">
            <div className="stat-value">{stats.storageUsed}</div>
            <div className="stat-label">المساحة المستخدمة</div>
          </div>
        </div>
      </div>
    </div>
  );
};