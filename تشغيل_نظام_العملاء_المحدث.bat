@echo off
chcp 65001 >nul
title نظام إدارة العملاء والتأشيرات - شراء السفريات

echo.
echo ========================================
echo    🎯 نظام إدارة العملاء والتأشيرات
echo        شراء السفريات المحدث
echo ========================================
echo.

echo 🔄 جاري تشغيل النظام...
echo.

echo 📱 تشغيل الواجهة الأمامية...
cd /d "%~dp0frontend"
start "Frontend Server" cmd /c "npm start"

echo.
echo ⏳ انتظار تحميل النظام...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 🌐 الروابط المتاحة:
echo.
echo 👥 صفحة العملاء (محدثة):
echo    http://localhost:3000/customers
echo.
echo 🏠 الصفحة الرئيسية:
echo    http://localhost:3000
echo.
echo 🔐 تسجيل الدخول:
echo    المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo ========================================
echo.
echo 🆕 المميزات الجديدة في صفحة العملاء:
echo.
echo ✨ المعلومات الأساسية:
echo    • اسم العميل
echo    • رقم الجوال
echo    • رقم الجواز
echo    • المهنة
echo    • البريد الإلكتروني
echo    • العنوان
echo.
echo 🛂 معلومات التأشيرة والمعاملة:
echo    • اسم الوكيل
echo    • مكتب التفويض
echo    • مكان تسليم المعاملة
echo    • رقم الطلب
echo    • نوع الفيزا
echo    • رقم الصادر
echo    • رقم السجل
echo    • اسم الشركة
echo.
echo 📅 التواريخ المهمة:
echo    • تاريخ التسليم
echo    • تاريخ الترحيل
echo    • تاريخ الوصول من السفارة
echo    • تاريخ التسليم للعميل
echo.
echo 💰 المعلومات المالية:
echo    • دفع رسوم المعاملة
echo    • المتبقي من رسوم المعاملة
echo    • دفع رسوم الفيزا
echo    • المتبقي من رسوم الفيزا
echo    • نوع العملة (ريال يمني/سعودي/دولار)
echo.
echo 📊 الحالة والملاحظات:
echo    • حالة المعاملة
echo    • حالة السداد لمكتب الترحيل
echo    • حالة التسليم للعميل
echo    • حالة العميل
echo    • ملاحظات
echo.
echo 📎 مرفقات المعاملة:
echo    • إرفاق الملفات (PDF, Word, صور)
echo.
echo ========================================
echo.
echo 🎉 استمتع بالنظام المحدث!
echo.
echo 💡 نصائح:
echo    • استخدم البحث للعثور على العملاء
echo    • استخدم الفلاتر لتصنيف العملاء
echo    • تابع حالات المعاملات والدفع
echo.
echo اضغط أي مفتاح للخروج...
pause >nul