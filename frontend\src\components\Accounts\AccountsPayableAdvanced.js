import React, { useState, useEffect, useMemo, useRef } from 'react';
import SupplierSelector from '../Finance/SupplierSelector';
import './AccountsPayable.css';

const AccountsPayableAdvanced = ({ accounts, transactions, suppliers, currentUser, suppliersService }) => {
  const [selectedPeriod, setSelectedPeriod] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSupplier, setSelectedSupplier] = useState('');
  const [ageingPeriod, setAgeingPeriod] = useState('current');
  const [viewMode, setViewMode] = useState('summary');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [selectedPayable, setSelectedPayable] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: 'dueDate', direction: 'asc' });
  
  const [newPayable, setNewPayable] = useState({
    supplierId: '',
    supplierName: '',
    amount: '',
    dueDate: '',
    invoiceNumber: '',
    description: '',
    terms: '30',
    category: 'services'
  });

  const [payment, setPayment] = useState({
    payableId: '',
    amount: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: 'bank',
    reference: '',
    notes: ''
  });

  const [filters, setFilters] = useState({
    status: 'all',
    ageGroup: 'all',
    amountRange: { min: '', max: '' },
    category: 'all',
    overdue: false,
    hasPayments: 'all',
    priority: 'all'
  });

  const [printOptions, setPrintOptions] = useState({
    includeDetails: true,
    includeAgeing: true,
    includePayments: true,
    showZeroBalances: false,
    groupBySupplier: true,
    includeNotes: true,
    pageSize: 'A4',
    orientation: 'portrait'
  });

  const [exportFormat, setExportFormat] = useState('csv');

  const printRef = useRef();

  // تحميل الذمم الدائنة من بيانات الموردين الحقيقية
  const [payables, setPayables] = useState([]);

  // تحميل البيانات من الخدمة
  useEffect(() => {
    if (suppliersService) {
      // تحويل بيانات الموردين إلى ذمم دائنة
      const suppliersWithPayables = suppliersService.getSuppliersWithPayables();
      const payablesData = suppliersWithPayables.map((supplier, index) => ({
        id: index + 1,
        supplierId: supplier.id,
        supplierName: supplier.name,
        amount: supplier.totalAmount || 0,
        paidAmount: supplier.paidAmount || 0,
        remainingAmount: supplier.remainingAmount || 0,
        invoiceNumber: `BILL-${supplier.id}`,
        invoiceDate: supplier.createdDate,
        dueDate: new Date(new Date(supplier.createdDate).getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        description: `خدمات ${supplier.type || 'متنوعة'} - ${supplier.contact}`,
        status: supplier.remainingAmount > 0 ?
          (new Date() > new Date(new Date(supplier.createdDate).getTime() + 30 * 24 * 60 * 60 * 1000) ? 'overdue' : 'current') : 'paid',
        terms: supplier.paymentTerms || '30',
        category: supplier.type === 'airline' ? 'travel' :
                 supplier.type === 'hotel' ? 'accommodation' :
                 supplier.type === 'transport' ? 'transport' : 'services',
        lastPaymentDate: supplier.paidAmount > 0 ? supplier.createdDate : null,
        followUps: [],
        priority: supplier.remainingAmount > 10000 ? 'high' :
                 supplier.remainingAmount > 5000 ? 'medium' : 'low'
      }));

      setPayables(payablesData);
    }
  }, [suppliersService, suppliers]);


  // حساب عمر الذمم
  const calculateAge = (dueDate) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today - due;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // تصنيف الذمم حسب العمر
  const getAgeGroup = (age) => {
    if (age <= 0) return 'current';
    if (age <= 30) return '1-30';
    if (age <= 60) return '31-60';
    if (age <= 90) return '61-90';
    return '90+';
  };

  // تصفية وترتيب الذمم
  const filteredPayables = useMemo(() => {
    let filtered = payables.filter(payable => {
      // تصفية النص
      const matchesSearch = !searchTerm || 
        payable.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payable.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payable.description.toLowerCase().includes(searchTerm.toLowerCase());

      // تصفية المورد
      const matchesSupplier = !selectedSupplier || payable.supplierId === selectedSupplier;

      // تصفية الفترة
      const invoiceDate = new Date(payable.invoiceDate);
      const startDate = new Date(selectedPeriod.startDate);
      const endDate = new Date(selectedPeriod.endDate);
      const matchesPeriod = invoiceDate >= startDate && invoiceDate <= endDate;

      // تصفية الحالة
      const matchesStatus = filters.status === 'all' || payable.status === filters.status;

      // تصفية عمر الذمة
      const age = calculateAge(payable.dueDate);
      const ageGroup = getAgeGroup(age);
      const matchesAge = filters.ageGroup === 'all' || ageGroup === filters.ageGroup;

      // تصفية المبلغ
      const matchesAmount = (!filters.amountRange.min || payable.remainingAmount >= parseFloat(filters.amountRange.min)) &&
                           (!filters.amountRange.max || payable.remainingAmount <= parseFloat(filters.amountRange.max));

      // تصفية الفئة
      const matchesCategory = filters.category === 'all' || payable.category === filters.category;

      // تصفية الأولوية
      const matchesPriority = filters.priority === 'all' || payable.priority === filters.priority;

      // تصفية المتأخرة
      const isOverdue = age > 0 && payable.remainingAmount > 0;
      const matchesOverdue = !filters.overdue || isOverdue;

      // تصفية وجود دفعات
      const hasPayments = payable.paidAmount > 0;
      const matchesPayments = filters.hasPayments === 'all' || 
                             (filters.hasPayments === 'yes' && hasPayments) ||
                             (filters.hasPayments === 'no' && !hasPayments);

      return matchesSearch && matchesSupplier && matchesPeriod && matchesStatus && 
             matchesAge && matchesAmount && matchesCategory && matchesPriority &&
             matchesOverdue && matchesPayments;
    });

    // ترتيب النتائج
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        if (sortConfig.key === 'age') {
          aValue = calculateAge(a.dueDate);
          bValue = calculateAge(b.dueDate);
        }

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [payables, searchTerm, selectedSupplier, selectedPeriod, filters, sortConfig]);

  // حساب الإحصائيات
  const statistics = useMemo(() => {
    const totalAmount = filteredPayables.reduce((sum, p) => sum + p.amount, 0);
    const totalPaid = filteredPayables.reduce((sum, p) => sum + p.paidAmount, 0);
    const totalRemaining = filteredPayables.reduce((sum, p) => sum + p.remainingAmount, 0);
    const overdueAmount = filteredPayables
      .filter(p => calculateAge(p.dueDate) > 0 && p.remainingAmount > 0)
      .reduce((sum, p) => sum + p.remainingAmount, 0);
    const currentAmount = filteredPayables
      .filter(p => calculateAge(p.dueDate) <= 0 && p.remainingAmount > 0)
      .reduce((sum, p) => sum + p.remainingAmount, 0);
    const highPriorityAmount = filteredPayables
      .filter(p => p.priority === 'high' && p.remainingAmount > 0)
      .reduce((sum, p) => sum + p.remainingAmount, 0);

    return {
      totalInvoices: filteredPayables.length,
      totalAmount,
      totalPaid,
      totalRemaining,
      overdueAmount,
      currentAmount,
      highPriorityAmount,
      paymentRate: totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0
    };
  }, [filteredPayables]);

  // تحليل أعمار الذمم
  const ageingAnalysis = useMemo(() => {
    const analysis = {
      current: 0,
      '1-30': 0,
      '31-60': 0,
      '61-90': 0,
      '90+': 0
    };

    filteredPayables.forEach(payable => {
      if (payable.remainingAmount > 0) {
        const age = calculateAge(payable.dueDate);
        const ageGroup = getAgeGroup(age);
        analysis[ageGroup] += payable.remainingAmount;
      }
    });

    return analysis;
  }, [filteredPayables]);

  // تحليل الفئات
  const categoryAnalysis = useMemo(() => {
    const analysis = {};
    
    filteredPayables.forEach(payable => {
      if (payable.remainingAmount > 0) {
        if (!analysis[payable.category]) {
          analysis[payable.category] = 0;
        }
        analysis[payable.category] += payable.remainingAmount;
      }
    });

    return analysis;
  }, [filteredPayables]);

  // وظائف الترتيب
  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // وظائف إدارة الذمم
  const handleAddPayable = () => {
    if (!newPayable.supplierId || !newPayable.amount || !newPayable.dueDate) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    const payable = {
      id: Date.now(),
      ...newPayable,
      supplierName: suppliers?.find(s => s.id === newPayable.supplierId)?.name || 'مورد غير محدد',
      amount: parseFloat(newPayable.amount),
      paidAmount: 0,
      remainingAmount: parseFloat(newPayable.amount),
      invoiceDate: new Date().toISOString().split('T')[0],
      status: 'current',
      priority: 'medium',
      lastPaymentDate: null,
      paymentSchedule: []
    };

    setPayables(prev => [...prev, payable]);
    setNewPayable({
      supplierId: '',
      amount: '',
      dueDate: '',
      invoiceNumber: '',
      description: '',
      terms: '30',
      category: 'services'
    });
    setShowAddModal(false);
    alert('تم إضافة الذمة الدائنة بنجاح');
  };

  const handlePayment = () => {
    // التحقق من الحقول المطلوبة
    if (!payment.payableId || !payment.amount || !payment.paymentDate) {
      alert('يرجى ملء جميع الحقول المطلوبة (الذمة الدائنة، مبلغ الدفعة، تاريخ الدفعة)');
      return;
    }

    const paymentAmount = parseFloat(payment.amount);
    
    // التحقق من صحة المبلغ
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
      alert('يرجى إدخال مبلغ صحيح أكبر من الصفر');
      return;
    }

    // العثور على الذمة المحددة للتحقق من المبلغ المتبقي
    const selectedPayable = payables.find(p => p.id === parseInt(payment.payableId));
    if (!selectedPayable) {
      alert('لم يتم العثور على الذمة المحددة');
      return;
    }

    // التحقق من أن مبلغ الدفعة لا يتجاوز المبلغ المتبقي
    if (paymentAmount > selectedPayable.remainingAmount) {
      alert(`مبلغ الدفعة (${paymentAmount.toLocaleString()}) يتجاوز المبلغ المتبقي (${selectedPayable.remainingAmount.toLocaleString()})`);
      return;
    }
    
    // تحديث الذمة الدائنة
    setPayables(prev => prev.map(payable => {
      if (payable.id === parseInt(payment.payableId)) {
        const newPaidAmount = payable.paidAmount + paymentAmount;
        const newRemainingAmount = payable.amount - newPaidAmount;
        
        return {
          ...payable,
          paidAmount: newPaidAmount,
          remainingAmount: Math.max(0, newRemainingAmount),
          lastPaymentDate: payment.paymentDate,
          status: newRemainingAmount <= 0 ? 'paid' : payable.status
        };
      }
      return payable;
    }));

    // إعادة تعيين النموذج
    setPayment({
      payableId: '',
      amount: '',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethod: 'bank',
      reference: '',
      notes: ''
    });
    
    setShowPaymentModal(false);
    
    // حساب المبلغ المتبقي الجديد
    const newRemainingAmount = selectedPayable.remainingAmount - paymentAmount;
    const isFullyPaid = newRemainingAmount <= 0;
    
    alert(`✅ تم تسجيل الدفعة بنجاح!\n\n` +
          `💰 مبلغ الدفعة: ${paymentAmount.toLocaleString()} ريال\n` +
          `🏢 المورد: ${selectedPayable.supplierName}\n` +
          `📄 رقم الفاتورة: ${selectedPayable.invoiceNumber}\n` +
          `🏷️ الفئة: ${selectedPayable.category === 'travel' ? 'سفر' :
                      selectedPayable.category === 'accommodation' ? 'إقامة' :
                      selectedPayable.category === 'transport' ? 'نقل' :
                      selectedPayable.category === 'services' ? 'خدمات' :
                      selectedPayable.category === 'catering' ? 'ضيافة' :
                      selectedPayable.category === 'insurance' ? 'تأمين' : 'أخرى'}\n` +
          `💳 طريقة الدفع: ${payment.paymentMethod === 'bank' ? 'تحويل بنكي' : 
                              payment.paymentMethod === 'check' ? 'شيك' :
                              payment.paymentMethod === 'cash' ? 'نقد' :
                              payment.paymentMethod === 'card' ? 'بطاقة ائتمان' : 'أخرى'}\n` +
          `📅 تاريخ الدفعة: ${new Date(payment.paymentDate).toLocaleDateString('ar-SA')}\n\n` +
          `${isFullyPaid ? 
            '🎉 تم سداد الذمة بالكامل!' : 
            `⏳ المبلغ المتبقي: ${newRemainingAmount.toLocaleString()} ريال`}`);
  };

  // وظائف الطباعة والتصدير
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير الذمم الدائنة</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .section { margin: 30px 0; page-break-inside: avoid; }
          .section-title { font-size: 16px; font-weight: bold; background: #f5f5f5; padding: 10px; margin-bottom: 15px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .supplier-name { text-align: right; }
          .amount { text-align: left; font-weight: bold; }
          .overdue { color: #e74c3c; }
          .current { color: #27ae60; }
          .paid { color: #3498db; }
          .high-priority { color: #e74c3c; font-weight: bold; }
          .total-row { background: #f8f9fa; font-weight: bold; border-top: 2px solid #333; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } .page-break { page-break-before: always; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    return `
      <div class="header">
        <div class="company-name">شركة شراء السياحية</div>
        <div class="report-title">تقرير الذمم الدائنة</div>
        <div class="date-range">
          للفترة من ${new Date(selectedPeriod.startDate).toLocaleDateString('ar-SA')} 
          إلى ${new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
        </div>
      </div>
      
      ${printOptions.includeDetails ? generatePayablesTable() : ''}
      ${printOptions.includeAgeing ? generateAgeingAnalysis() : ''}
      
      <div class="footer">
        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
        <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
      </div>
    `;
  };

  const generatePayablesTable = () => {
    return `
      <div class="section">
        <div class="section-title">تفاصيل الذمم الدائنة</div>
        <table>
          <thead>
            <tr>
              <th>المورد</th>
              <th>رقم الفاتورة</th>
              <th>تاريخ الفاتورة</th>
              <th>تاريخ الاستحقاق</th>
              <th>المبلغ الإجمالي</th>
              <th>المبلغ المدفوع</th>
              <th>المبلغ المتبقي</th>
              <th>العمر (يوم)</th>
              <th>الأولوية</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${filteredPayables.map(payable => {
              const age = calculateAge(payable.dueDate);
              return `
                <tr>
                  <td class="supplier-name">${payable.supplierName}</td>
                  <td>${payable.invoiceNumber}</td>
                  <td>${new Date(payable.invoiceDate).toLocaleDateString('ar-SA')}</td>
                  <td>${new Date(payable.dueDate).toLocaleDateString('ar-SA')}</td>
                  <td class="amount">${payable.amount.toLocaleString()}</td>
                  <td class="amount">${payable.paidAmount.toLocaleString()}</td>
                  <td class="amount ${payable.status}">${payable.remainingAmount.toLocaleString()}</td>
                  <td>${age}</td>
                  <td class="${payable.priority === 'high' ? 'high-priority' : ''}">
                    ${payable.priority === 'high' ? 'عالية' : 
                      payable.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                  </td>
                  <td class="${payable.status}">
                    ${payable.status === 'paid' ? 'مدفوع' : 
                      payable.status === 'overdue' ? 'متأخر' : 'جاري'}
                  </td>
                </tr>
              `;
            }).join('')}
            <tr class="total-row">
              <td colspan="4"><strong>الإجمالي</strong></td>
              <td class="amount"><strong>${statistics.totalAmount.toLocaleString()}</strong></td>
              <td class="amount"><strong>${statistics.totalPaid.toLocaleString()}</strong></td>
              <td class="amount"><strong>${statistics.totalRemaining.toLocaleString()}</strong></td>
              <td colspan="3"></td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateAgeingAnalysis = () => {
    return `
      <div class="section">
        <div class="section-title">تحليل أعمار الذمم</div>
        <table>
          <thead>
            <tr>
              <th>الفئة العمرية</th>
              <th>المبلغ</th>
              <th>النسبة %</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>جاري (غير مستحق)</td>
              <td class="amount current">${ageingAnalysis.current.toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis.current / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>1-30 يوم</td>
              <td class="amount">${ageingAnalysis['1-30'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['1-30'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>31-60 يوم</td>
              <td class="amount">${ageingAnalysis['31-60'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['31-60'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>61-90 يوم</td>
              <td class="amount">${ageingAnalysis['61-90'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['61-90'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>أكثر من 90 يوم</td>
              <td class="amount overdue">${ageingAnalysis['90+'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['90+'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr class="total-row">
              <td><strong>الإجمالي</strong></td>
              <td class="amount"><strong>${statistics.totalRemaining.toLocaleString()}</strong></td>
              <td><strong>100%</strong></td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    let csvContent = 'المورد,رقم الفاتورة,تاريخ الفاتورة,تاريخ الاستحقاق,المبلغ الإجمالي,المبلغ المدفوع,المبلغ المتبقي,العمر,الفئة,الأولوية,الحالة,الوصف\n';
    
    filteredPayables.forEach(payable => {
      const age = calculateAge(payable.dueDate);
      const status = payable.status === 'paid' ? 'مدفوع' : 
                    payable.status === 'overdue' ? 'متأخر' : 'جاري';
      const priority = payable.priority === 'high' ? 'عالية' : 
                      payable.priority === 'medium' ? 'متوسطة' : 'منخفضة';
      const category = payable.category === 'travel' ? 'سفر' :
                      payable.category === 'accommodation' ? 'إقامة' :
                      payable.category === 'transport' ? 'نقل' :
                      payable.category === 'services' ? 'خدمات' :
                      payable.category === 'catering' ? 'ضيافة' :
                      payable.category === 'insurance' ? 'تأمين' : 'أخرى';
      
      csvContent += `"${payable.supplierName}","${payable.invoiceNumber}","${payable.invoiceDate}","${payable.dueDate}",${payable.amount},${payable.paidAmount},${payable.remainingAmount},${age},"${category}","${priority}","${status}","${payable.description}"\n`;
    });

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `الذمم-الدائنة-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  return (
    <div className="accounts-payable-advanced">
      <div className="payables-header">
        <div className="header-content">
          <h2>💳 الذمم الدائنة المتقدمة</h2>
          <p>إدارة شاملة للذمم الدائنة والموردين وجدولة المدفوعات</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddModal(true)}
          >
            ➕ إضافة ذمة
          </button>
          <button 
            className="btn btn-success"
            onClick={() => setShowPaymentModal(true)}
          >
            💰 تسجيل دفعة
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowScheduleModal(true)}
          >
            📅 جدولة المدفوعات
          </button>
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-secondary"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-secondary"
            onClick={() => setShowFilterModal(true)}
          >
            🔍 تصفية متقدمة
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="payables-stats">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalInvoices}</div>
            <div className="stat-label">عدد الفواتير</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalAmount.toLocaleString()}</div>
            <div className="stat-label">إجمالي المبلغ</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalPaid.toLocaleString()}</div>
            <div className="stat-label">المبلغ المدفوع</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalRemaining.toLocaleString()}</div>
            <div className="stat-label">المبلغ المتبقي</div>
          </div>
        </div>
        
        <div className="stat-card overdue">
          <div className="stat-icon">⚠️</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.overdueAmount.toLocaleString()}</div>
            <div className="stat-label">المتأخر</div>
          </div>
        </div>
        
        <div className="stat-card priority">
          <div className="stat-icon">🔥</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.highPriorityAmount.toLocaleString()}</div>
            <div className="stat-label">أولوية عالية</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">📈</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.paymentRate.toFixed(1)}%</div>
            <div className="stat-label">معدل الدفع</div>
          </div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="payables-controls">
        <div className="controls-row">
          <div className="search-box">
            <input
              type="text"
              placeholder="البحث في الموردين أو الفواتير..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="date-range">
            <label>الفترة:</label>
            <input
              type="date"
              value={selectedPeriod.startDate}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={selectedPeriod.endDate}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
          
          <div className="supplier-filter">
            <select
              value={selectedSupplier}
              onChange={(e) => setSelectedSupplier(e.target.value)}
              className="form-control"
            >
              <option value="">جميع الموردين</option>
              {suppliers?.map(supplier => (
                <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
              ))}
            </select>
          </div>
          
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'summary' ? 'active' : ''}`}
              onClick={() => setViewMode('summary')}
            >
              📊 ملخص
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'detailed' ? 'active' : ''}`}
              onClick={() => setViewMode('detailed')}
            >
              📋 تفصيلي
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'ageing' ? 'active' : ''}`}
              onClick={() => setViewMode('ageing')}
            >
              📅 تحليل الأعمار
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'category' ? 'active' : ''}`}
              onClick={() => setViewMode('category')}
            >
              🏷️ تحليل الفئات
            </button>
          </div>
        </div>
      </div>

      {/* عرض البيانات */}
      <div className="payables-content">
        {viewMode === 'summary' && (
          <div className="summary-view">
            <div className="table-container">
              <table className="payables-table">
                <thead>
                  <tr>
                    <th onClick={() => handleSort('supplierName')}>
                      المورد {sortConfig.key === 'supplierName' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('invoiceNumber')}>
                      رقم الفاتورة {sortConfig.key === 'invoiceNumber' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('dueDate')}>
                      تاريخ الاستحقاق {sortConfig.key === 'dueDate' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('amount')}>
                      المبلغ الإجمالي {sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('remainingAmount')}>
                      المبلغ المتبقي {sortConfig.key === 'remainingAmount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('age')}>
                      العمر (يوم) {sortConfig.key === 'age' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('priority')}>
                      الأولوية {sortConfig.key === 'priority' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPayables.map(payable => {
                    const age = calculateAge(payable.dueDate);
                    return (
                      <tr key={payable.id} className={`${payable.status} ${payable.priority}`}>
                        <td className="supplier-name">{payable.supplierName}</td>
                        <td>{payable.invoiceNumber}</td>
                        <td>{new Date(payable.dueDate).toLocaleDateString('ar-SA')}</td>
                        <td className="amount">{payable.amount.toLocaleString()}</td>
                        <td className={`amount ${payable.status}`}>
                          {payable.remainingAmount.toLocaleString()}
                        </td>
                        <td className={age > 0 ? 'overdue' : 'current'}>{age}</td>
                        <td>
                          <span className={`priority-badge ${payable.priority}`}>
                            {payable.priority === 'high' ? 'عالية' : 
                             payable.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                          </span>
                        </td>
                        <td>
                          <span className={`status-badge ${payable.status}`}>
                            {payable.status === 'paid' ? 'مدفوع' : 
                             payable.status === 'overdue' ? 'متأخر' : 'جاري'}
                          </span>
                        </td>
                        <td className="actions">
                          <button
                            className="action-btn view-btn"
                            onClick={() => {
                              setSelectedPayable(payable);
                              setShowDetailsModal(true);
                            }}
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          {payable.remainingAmount > 0 && (
                            <>
                              <button
                                className="action-btn payment-btn"
                                onClick={() => {
                                  setPayment(prev => ({ ...prev, payableId: payable.id.toString() }));
                                  setShowPaymentModal(true);
                                }}
                                title="تسجيل دفعة"
                              >
                                💰
                              </button>
                              <button
                                className="action-btn schedule-btn"
                                onClick={() => {
                                  setSelectedPayable(payable);
                                  setShowScheduleModal(true);
                                }}
                                title="جدولة الدفع"
                              >
                                📅
                              </button>
                            </>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot>
                  <tr className="total-row">
                    <td colSpan="3"><strong>الإجمالي</strong></td>
                    <td className="amount"><strong>{statistics.totalAmount.toLocaleString()}</strong></td>
                    <td className="amount"><strong>{statistics.totalRemaining.toLocaleString()}</strong></td>
                    <td colSpan="4"></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}

        {viewMode === 'detailed' && (
          <div className="detailed-view">
            {filteredPayables.map(payable => {
              const age = calculateAge(payable.dueDate);
              return (
                <div key={payable.id} className={`payable-card ${payable.status} ${payable.priority}`}>
                  <div className="card-header">
                    <div className="supplier-info">
                      <h3>{payable.supplierName}</h3>
                      <span className="invoice-number">{payable.invoiceNumber}</span>
                    </div>
                    <div className="status-info">
                      <span className={`status-badge ${payable.status}`}>
                        {payable.status === 'paid' ? 'مدفوع' : 
                         payable.status === 'overdue' ? 'متأخر' : 'جاري'}
                      </span>
                      <span className={`priority-badge ${payable.priority}`}>
                        {payable.priority === 'high' ? 'عالية' : 
                         payable.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                      </span>
                      <span className={`age-badge ${age > 0 ? 'overdue' : 'current'}`}>
                        {age} يوم
                      </span>
                    </div>
                  </div>
                  
                  <div className="card-body">
                    <div className="payable-details">
                      <div className="detail-item">
                        <label>تاريخ الفاتورة:</label>
                        <span>{new Date(payable.invoiceDate).toLocaleDateString('ar-SA')}</span>
                      </div>
                      <div className="detail-item">
                        <label>تاريخ الاستحقاق:</label>
                        <span>{new Date(payable.dueDate).toLocaleDateString('ar-SA')}</span>
                      </div>
                      <div className="detail-item">
                        <label>الفئة:</label>
                        <span>
                          {payable.category === 'travel' ? 'سفر' :
                           payable.category === 'accommodation' ? 'إقامة' :
                           payable.category === 'transport' ? 'نقل' :
                           payable.category === 'services' ? 'خدمات' :
                           payable.category === 'catering' ? 'ضيافة' :
                           payable.category === 'insurance' ? 'تأمين' : 'أخرى'}
                        </span>
                      </div>
                      <div className="detail-item">
                        <label>الوصف:</label>
                        <span>{payable.description}</span>
                      </div>
                      <div className="detail-item">
                        <label>شروط الدفع:</label>
                        <span>{payable.terms} يوم</span>
                      </div>
                    </div>
                    
                    <div className="amount-details">
                      <div className="amount-item">
                        <label>المبلغ الإجمالي:</label>
                        <span className="amount">{payable.amount.toLocaleString()}</span>
                      </div>
                      <div className="amount-item">
                        <label>المبلغ المدفوع:</label>
                        <span className="amount paid">{payable.paidAmount.toLocaleString()}</span>
                      </div>
                      <div className="amount-item">
                        <label>المبلغ المتبقي:</label>
                        <span className={`amount ${payable.status}`}>
                          {payable.remainingAmount.toLocaleString()}
                        </span>
                      </div>
                      {payable.lastPaymentDate && (
                        <div className="amount-item">
                          <label>آخر دفعة:</label>
                          <span>{new Date(payable.lastPaymentDate).toLocaleDateString('ar-SA')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="card-footer">
                    <div className="payment-schedule">
                      {payable.paymentSchedule && payable.paymentSchedule.length > 0 && (
                        <div className="schedule-summary">
                          <span>جدولة الدفع: {payable.paymentSchedule.length} دفعة</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="card-actions">
                      <button
                        className="btn btn-sm btn-info"
                        onClick={() => {
                          setSelectedPayable(payable);
                          setShowDetailsModal(true);
                        }}
                      >
                        👁️ التفاصيل
                      </button>
                      {payable.remainingAmount > 0 && (
                        <>
                          <button
                            className="btn btn-sm btn-success"
                            onClick={() => {
                              setPayment(prev => ({ ...prev, payableId: payable.id.toString() }));
                              setShowPaymentModal(true);
                            }}
                          >
                            💰 دفعة
                          </button>
                          <button
                            className="btn btn-sm btn-warning"
                            onClick={() => {
                              setSelectedPayable(payable);
                              setShowScheduleModal(true);
                            }}
                          >
                            📅 جدولة
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {viewMode === 'ageing' && (
          <div className="ageing-view">
            <div className="ageing-summary">
              <h3>تحليل أعمار الذمم الدائنة</h3>
              <div className="ageing-chart">
                <div className="ageing-bars">
                  {Object.entries(ageingAnalysis).map(([ageGroup, amount]) => {
                    const percentage = statistics.totalRemaining > 0 ? (amount / statistics.totalRemaining) * 100 : 0;
                    return (
                      <div key={ageGroup} className="ageing-bar">
                        <div className="bar-header">
                          <span className="age-label">
                            {ageGroup === 'current' ? 'جاري (غير مستحق)' :
                             ageGroup === '1-30' ? '1-30 يوم' :
                             ageGroup === '31-60' ? '31-60 يوم' :
                             ageGroup === '61-90' ? '61-90 يوم' :
                             'أكثر من 90 يوم'}
                          </span>
                          <span className="amount">{amount.toLocaleString()}</span>
                        </div>
                        <div className="bar-container">
                          <div 
                            className={`bar-fill ${ageGroup === 'current' ? 'current' : 
                                                  ageGroup === '90+' ? 'critical' : 'warning'}`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <div className="bar-footer">
                          <span className="percentage">{percentage.toFixed(1)}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            
            <div className="ageing-table">
              <table className="payables-table">
                <thead>
                  <tr>
                    <th>الفئة العمرية</th>
                    <th>عدد الفواتير</th>
                    <th>المبلغ</th>
                    <th>النسبة %</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(ageingAnalysis).map(([ageGroup, amount]) => {
                    const count = filteredPayables.filter(p => {
                      const age = calculateAge(p.dueDate);
                      return getAgeGroup(age) === ageGroup && p.remainingAmount > 0;
                    }).length;
                    const percentage = statistics.totalRemaining > 0 ? (amount / statistics.totalRemaining) * 100 : 0;
                    
                    return (
                      <tr key={ageGroup} className={ageGroup === 'current' ? 'current' : 
                                                   ageGroup === '90+' ? 'critical' : 'warning'}>
                        <td>
                          {ageGroup === 'current' ? 'جاري (غير مستحق)' :
                           ageGroup === '1-30' ? '1-30 يوم' :
                           ageGroup === '31-60' ? '31-60 يوم' :
                           ageGroup === '61-90' ? '61-90 يوم' :
                           'أكثر من 90 يوم'}
                        </td>
                        <td>{count}</td>
                        <td className="amount">{amount.toLocaleString()}</td>
                        <td>{percentage.toFixed(1)}%</td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot>
                  <tr className="total-row">
                    <td><strong>الإجمالي</strong></td>
                    <td><strong>{filteredPayables.filter(p => p.remainingAmount > 0).length}</strong></td>
                    <td className="amount"><strong>{statistics.totalRemaining.toLocaleString()}</strong></td>
                    <td><strong>100%</strong></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}

        {viewMode === 'category' && (
          <div className="category-view">
            <div className="category-summary">
              <h3>تحليل الذمم حسب الفئة</h3>
              <div className="category-chart">
                <div className="category-bars">
                  {Object.entries(categoryAnalysis).map(([category, amount]) => {
                    const percentage = statistics.totalRemaining > 0 ? (amount / statistics.totalRemaining) * 100 : 0;
                    const categoryName = category === 'travel' ? 'سفر' :
                                        category === 'accommodation' ? 'إقامة' :
                                        category === 'transport' ? 'نقل' :
                                        category === 'services' ? 'خدمات' :
                                        category === 'catering' ? 'ضيافة' :
                                        category === 'insurance' ? 'تأمين' : 'أخرى';
                    
                    return (
                      <div key={category} className="category-bar">
                        <div className="bar-header">
                          <span className="category-label">{categoryName}</span>
                          <span className="amount">{amount.toLocaleString()}</span>
                        </div>
                        <div className="bar-container">
                          <div 
                            className="bar-fill category"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <div className="bar-footer">
                          <span className="percentage">{percentage.toFixed(1)}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* النماذج المنبثقة */}
      
      {/* نموذج إضافة ذمة دائنة */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="add-modal">
            <div className="modal-header">
              <h3>➕ إضافة ذمة دائنة جديدة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowAddModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>المورد *</label>
                  <SupplierSelector
                    value={newPayable.supplierName}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, supplierName: e.target.value }))}
                    onSelect={(supplier) => {
                      setNewPayable(prev => ({
                        ...prev,
                        supplierId: supplier.id,
                        supplierName: supplier.name
                      }));
                    }}
                    placeholder="اكتب اسم المورد أو ابحث..."
                    showActiveOnly={true}
                  />
                </div>

                <div className="form-group">
                  <label>رقم الفاتورة</label>
                  <input
                    type="text"
                    value={newPayable.invoiceNumber}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                    className="form-control"
                    placeholder="BILL-2024-001"
                  />
                </div>

                <div className="form-group">
                  <label>المبلغ *</label>
                  <input
                    type="number"
                    value={newPayable.amount}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, amount: e.target.value }))}
                    className="form-control"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>تاريخ الاستحقاق *</label>
                  <input
                    type="date"
                    value={newPayable.dueDate}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="form-control"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={newPayable.category}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, category: e.target.value }))}
                    className="form-control"
                  >
                    <option value="travel">سفر</option>
                    <option value="accommodation">إقامة</option>
                    <option value="transport">نقل</option>
                    <option value="services">خدمات</option>
                    <option value="catering">ضيافة</option>
                    <option value="insurance">تأمين</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>شروط الدفع (يوم)</label>
                  <select
                    value={newPayable.terms}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, terms: e.target.value }))}
                    className="form-control"
                  >
                    <option value="0">فوري</option>
                    <option value="15">15 يوم</option>
                    <option value="30">30 يوم</option>
                    <option value="45">45 يوم</option>
                    <option value="60">60 يوم</option>
                    <option value="90">90 يوم</option>
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={newPayable.description}
                    onChange={(e) => setNewPayable(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="وصف الخدمة أو المنتج..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowAddModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleAddPayable}
              >
                ➕ إضافة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تسجيل دفعة */}
      {showPaymentModal && (
        <div className="modal-overlay">
          <div className="payment-modal">
            <div className="modal-header">
              <h3>💰 تسجيل دفعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPaymentModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>الذمة الدائنة *</label>
                  <select
                    value={payment.payableId}
                    onChange={(e) => {
                      setPayment(prev => ({ ...prev, payableId: e.target.value, amount: '' }));
                    }}
                    className="form-control"
                    required
                  >
                    <option value="">اختر الذمة</option>
                    {filteredPayables.filter(p => p.remainingAmount > 0).map(payable => (
                      <option key={payable.id} value={payable.id}>
                        {payable.supplierName} - {payable.invoiceNumber} - المتبقي: {payable.remainingAmount.toLocaleString()} ريال
                      </option>
                    ))}
                  </select>
                </div>

                {payment.payableId && (
                  <div className="form-group full-width">
                    <label>معلومات الذمة المحددة</label>
                    <div className="selected-payable-info">
                      {(() => {
                        const selected = filteredPayables.find(p => p.id === parseInt(payment.payableId));
                        return selected ? (
                          <div className="payable-summary">
                            <div className="info-item">
                              <span className="label">المورد:</span>
                              <span className="value">{selected.supplierName}</span>
                            </div>
                            <div className="info-item">
                              <span className="label">رقم الفاتورة:</span>
                              <span className="value">{selected.invoiceNumber}</span>
                            </div>
                            <div className="info-item">
                              <span className="label">الفئة:</span>
                              <span className="value">
                                {selected.category === 'travel' ? 'سفر' :
                                 selected.category === 'accommodation' ? 'إقامة' :
                                 selected.category === 'transport' ? 'نقل' :
                                 selected.category === 'services' ? 'خدمات' :
                                 selected.category === 'catering' ? 'ضيافة' :
                                 selected.category === 'insurance' ? 'تأمين' : 'أخرى'}
                              </span>
                            </div>
                            <div className="info-item">
                              <span className="label">الأولوية:</span>
                              <span className={`value ${selected.priority}`}>
                                {selected.priority === 'high' ? 'عالية' : 
                                 selected.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                              </span>
                            </div>
                            <div className="info-item">
                              <span className="label">المبلغ الإجمالي:</span>
                              <span className="value">{selected.amount.toLocaleString()} ريال</span>
                            </div>
                            <div className="info-item">
                              <span className="label">المبلغ المدفوع:</span>
                              <span className="value paid">{selected.paidAmount.toLocaleString()} ريال</span>
                            </div>
                            <div className="info-item">
                              <span className="label">المبلغ المتبقي:</span>
                              <span className="value remaining">{selected.remainingAmount.toLocaleString()} ريال</span>
                            </div>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  </div>
                )}

                <div className="form-group">
                  <label>مبلغ الدفعة *</label>
                  <div className="amount-input-container">
                    <input
                      type="number"
                      value={payment.amount}
                      onChange={(e) => setPayment(prev => ({ ...prev, amount: e.target.value }))}
                      className="form-control"
                      placeholder="0.00"
                      min="0"
                      max={payment.payableId ? 
                        filteredPayables.find(p => p.id === parseInt(payment.payableId))?.remainingAmount || 0 
                        : undefined}
                      step="0.01"
                      required
                    />
                    {payment.payableId && (
                      <button
                        type="button"
                        className="btn btn-outline btn-sm full-payment-btn"
                        onClick={() => {
                          const selected = filteredPayables.find(p => p.id === parseInt(payment.payableId));
                          if (selected) {
                            setPayment(prev => ({ ...prev, amount: selected.remainingAmount.toString() }));
                          }
                        }}
                        title="دفع المبلغ المتبقي بالكامل"
                      >
                        💯 دفع كامل
                      </button>
                    )}
                  </div>
                  {payment.payableId && payment.amount && (
                    <div className="payment-validation">
                      {(() => {
                        const selected = filteredPayables.find(p => p.id === parseInt(payment.payableId));
                        const amount = parseFloat(payment.amount);
                        if (selected && amount > selected.remainingAmount) {
                          return (
                            <div className="validation-error">
                              ⚠️ المبلغ يتجاوز المبلغ المتبقي ({selected.remainingAmount.toLocaleString()} ريال)
                            </div>
                          );
                        } else if (selected && amount > 0) {
                          const remaining = selected.remainingAmount - amount;
                          return (
                            <div className="validation-success">
                              ✅ سيصبح المبلغ المتبقي: {remaining.toLocaleString()} ريال
                              {remaining === 0 && <span className="full-payment-indicator"> - دفع كامل 🎉</span>}
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label>تاريخ الدفعة *</label>
                  <input
                    type="date"
                    value={payment.paymentDate}
                    onChange={(e) => setPayment(prev => ({ ...prev, paymentDate: e.target.value }))}
                    className="form-control"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>طريقة الدفع</label>
                  <select
                    value={payment.paymentMethod}
                    onChange={(e) => setPayment(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="form-control"
                  >
                    <option value="bank">تحويل بنكي</option>
                    <option value="check">شيك</option>
                    <option value="cash">نقد</option>
                    <option value="card">بطاقة ائتمان</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>المرجع</label>
                  <input
                    type="text"
                    value={payment.reference}
                    onChange={(e) => setPayment(prev => ({ ...prev, reference: e.target.value }))}
                    className="form-control"
                    placeholder="رقم الشيك أو التحويل..."
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={payment.notes}
                    onChange={(e) => setPayment(prev => ({ ...prev, notes: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPaymentModal(false)}
              >
                إلغاء
              </button>
              <button 
                className={`btn btn-success ${
                  !payment.payableId || 
                  !payment.amount || 
                  !payment.paymentDate ||
                  parseFloat(payment.amount) <= 0 ||
                  (payment.payableId && parseFloat(payment.amount) > 
                    (filteredPayables.find(p => p.id === parseInt(payment.payableId))?.remainingAmount || 0))
                  ? 'disabled' : ''
                }`}
                onClick={handlePayment}
                disabled={
                  !payment.payableId || 
                  !payment.amount || 
                  !payment.paymentDate ||
                  parseFloat(payment.amount) <= 0 ||
                  (payment.payableId && parseFloat(payment.amount) > 
                    (filteredPayables.find(p => p.id === parseInt(payment.payableId))?.remainingAmount || 0))
                }
              >
                💰 تسجيل الدفعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeDetails}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                    />
                    تضمين تفاصيل الذمم
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeAgeing}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeAgeing: e.target.checked }))}
                    />
                    تضمين تحليل الأعمار
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includePayments}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includePayments: e.target.checked }))}
                    />
                    تضمين تاريخ الدفعات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.showZeroBalances}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, showZeroBalances: e.target.checked }))}
                    />
                    إظهار الأرصدة المدفوعة
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.groupBySupplier}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, groupBySupplier: e.target.checked }))}
                    />
                    التجميع حسب المورد
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">عدد الذمم:</span>
                      <span className="value">{filteredPayables.length}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">إجمالي المبلغ:</span>
                      <span className="value">{statistics.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">المبلغ المتبقي:</span>
                      <span className="value">{statistics.totalRemaining.toLocaleString()}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">المبلغ المتأخر:</span>
                      <span className="value overdue">{statistics.overdueAmount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصفية المتقدمة */}
      {showFilterModal && (
        <div className="modal-overlay">
          <div className="filter-modal">
            <div className="modal-header">
              <h3>🔍 تصفية متقدمة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowFilterModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="filter-form">
                <div className="filter-group">
                  <label>حالة الذمة:</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="current">جاري</option>
                    <option value="overdue">متأخر</option>
                    <option value="paid">مدفوع</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>الفئة العمرية:</label>
                  <select
                    value={filters.ageGroup}
                    onChange={(e) => setFilters(prev => ({ ...prev, ageGroup: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">جميع الأعمار</option>
                    <option value="current">جاري (غير مستحق)</option>
                    <option value="1-30">1-30 يوم</option>
                    <option value="31-60">31-60 يوم</option>
                    <option value="61-90">61-90 يوم</option>
                    <option value="90+">أكثر من 90 يوم</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>الفئة:</label>
                  <select
                    value={filters.category}
                    onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">جميع الفئات</option>
                    <option value="travel">سفر</option>
                    <option value="accommodation">إقامة</option>
                    <option value="transport">نقل</option>
                    <option value="services">خدمات</option>
                    <option value="catering">ضيافة</option>
                    <option value="insurance">تأمين</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>الأولوية:</label>
                  <select
                    value={filters.priority}
                    onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">جميع الأولويات</option>
                    <option value="high">عالية</option>
                    <option value="medium">متوسطة</option>
                    <option value="low">منخفضة</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>نطاق المبلغ:</label>
                  <div className="range-inputs">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.amountRange.min}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        amountRange: { ...prev.amountRange, min: e.target.value }
                      }))}
                      className="form-control"
                    />
                    <span>إلى</span>
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.amountRange.max}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        amountRange: { ...prev.amountRange, max: e.target.value }
                      }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="filter-group">
                  <label>وجود دفعات:</label>
                  <select
                    value={filters.hasPayments}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasPayments: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">الكل</option>
                    <option value="yes">يوجد دفعات</option>
                    <option value="no">لا يوجد دفعات</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={filters.overdue}
                      onChange={(e) => setFilters(prev => ({ ...prev, overdue: e.target.checked }))}
                    />
                    المتأخرة فقط
                  </label>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  setFilters({
                    status: 'all',
                    ageGroup: 'all',
                    amountRange: { min: '', max: '' },
                    category: 'all',
                    overdue: false,
                    hasPayments: 'all',
                    priority: 'all'
                  });
                }}
              >
                🔄 إعادة تعيين
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => setShowFilterModal(false)}
              >
                ✅ تطبيق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تفاصيل الذمة */}
      {showDetailsModal && selectedPayable && (
        <div className="modal-overlay">
          <div className="details-modal">
            <div className="modal-header">
              <h3>👁️ تفاصيل الذمة الدائنة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="payable-details">
                <div className="details-header">
                  <div className="detail-item">
                    <label>المورد:</label>
                    <span>{selectedPayable.supplierName}</span>
                  </div>
                  <div className="detail-item">
                    <label>رقم الفاتورة:</label>
                    <span>{selectedPayable.invoiceNumber}</span>
                  </div>
                  <div className="detail-item">
                    <label>تاريخ الفاتورة:</label>
                    <span>{new Date(selectedPayable.invoiceDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>تاريخ الاستحقاق:</label>
                    <span>{new Date(selectedPayable.dueDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>الفئة:</label>
                    <span>
                      {selectedPayable.category === 'travel' ? 'سفر' :
                       selectedPayable.category === 'accommodation' ? 'إقامة' :
                       selectedPayable.category === 'transport' ? 'نقل' :
                       selectedPayable.category === 'services' ? 'خدمات' :
                       selectedPayable.category === 'catering' ? 'ضيافة' :
                       selectedPayable.category === 'insurance' ? 'تأمين' : 'أخرى'}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>الأولوية:</label>
                    <span className={selectedPayable.priority}>
                      {selectedPayable.priority === 'high' ? 'عالية' : 
                       selectedPayable.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>شروط الدفع:</label>
                    <span>{selectedPayable.terms} يوم</span>
                  </div>
                  <div className="detail-item">
                    <label>العمر:</label>
                    <span className={calculateAge(selectedPayable.dueDate) > 0 ? 'overdue' : 'current'}>
                      {calculateAge(selectedPayable.dueDate)} يوم
                    </span>
                  </div>
                </div>

                <div className="amount-summary">
                  <h4>ملخص المبالغ:</h4>
                  <div className="amount-grid">
                    <div className="amount-item">
                      <label>المبلغ الإجمالي:</label>
                      <span className="amount">{selectedPayable.amount.toLocaleString()}</span>
                    </div>
                    <div className="amount-item">
                      <label>المبلغ المدفوع:</label>
                      <span className="amount paid">{selectedPayable.paidAmount.toLocaleString()}</span>
                    </div>
                    <div className="amount-item">
                      <label>المبلغ المتبقي:</label>
                      <span className={`amount ${selectedPayable.status}`}>
                        {selectedPayable.remainingAmount.toLocaleString()}
                      </span>
                    </div>
                    <div className="amount-item">
                      <label>نسبة الدفع:</label>
                      <span className="amount">
                        {selectedPayable.amount > 0 ? 
                          ((selectedPayable.paidAmount / selectedPayable.amount) * 100).toFixed(1) : 0}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="description-section">
                  <h4>الوصف:</h4>
                  <p>{selectedPayable.description}</p>
                </div>

                {selectedPayable.paymentSchedule && selectedPayable.paymentSchedule.length > 0 && (
                  <div className="schedule-section">
                    <h4>جدولة المدفوعات:</h4>
                    <div className="schedule-list">
                      {selectedPayable.paymentSchedule.map((schedule, index) => (
                        <div key={index} className={`schedule-item ${schedule.status}`}>
                          <div className="schedule-header">
                            <span className="schedule-date">
                              {new Date(schedule.date).toLocaleDateString('ar-SA')}
                            </span>
                            <span className="schedule-amount">
                              {schedule.amount.toLocaleString()}
                            </span>
                            <span className={`schedule-status ${schedule.status}`}>
                              {schedule.status === 'paid' ? 'مدفوع' : 
                               schedule.status === 'overdue' ? 'متأخر' : 'معلق'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDetailsModal(false)}
              >
                إغلاق
              </button>
              {selectedPayable.remainingAmount > 0 && (
                <>
                  <button 
                    className="btn btn-success"
                    onClick={() => {
                      setPayment(prev => ({ ...prev, payableId: selectedPayable.id.toString() }));
                      setShowDetailsModal(false);
                      setShowPaymentModal(true);
                    }}
                  >
                    💰 تسجيل دفعة
                  </button>
                  <button 
                    className="btn btn-warning"
                    onClick={() => {
                      setShowDetailsModal(false);
                      setShowScheduleModal(true);
                    }}
                  >
                    📅 جدولة الدفع
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* نموذج جدولة المدفوعات */}
      {showScheduleModal && (
        <div className="modal-overlay">
          <div className="schedule-modal">
            <div className="modal-header">
              <h3>📅 جدولة المدفوعات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowScheduleModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="schedule-form">
                <div className="supplier-info">
                  <h4>{selectedPayable?.supplierName || 'جدولة عامة'}</h4>
                  {selectedPayable && (
                    <p>فاتورة: {selectedPayable.invoiceNumber} - المبلغ المتبقي: {selectedPayable.remainingAmount.toLocaleString()}</p>
                  )}
                </div>

                <div className="schedule-options">
                  <h4>خيارات الجدولة:</h4>
                  <div className="option-buttons">
                    <button className="btn btn-outline">📅 أسبوعي</button>
                    <button className="btn btn-outline">📅 شهري</button>
                    <button className="btn btn-outline">📅 ربع سنوي</button>
                    <button className="btn btn-outline">📅 مخصص</button>
                  </div>
                </div>

                <div className="upcoming-payments">
                  <h4>المدفوعات المجدولة القادمة:</h4>
                  <div className="payments-calendar">
                    <p>سيتم تطوير تقويم المدفوعات التفاعلي قريباً</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowScheduleModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => {
                  alert('سيتم تطوير وظيفة الجدولة قريباً');
                  setShowScheduleModal(false);
                }}
              >
                📅 حفظ الجدولة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountsPayableAdvanced;