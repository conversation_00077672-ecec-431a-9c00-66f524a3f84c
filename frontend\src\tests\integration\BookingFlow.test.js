import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import FlightBookingPage from '../../pages/Bookings/FlightBookingPage';
import { EnhancedNotificationsProvider } from '../../components/UI/EnhancedNotifications';
import { KeyboardShortcutsProvider } from '../../components/UI/KeyboardShortcuts';

// 🧪 اختبارات التكامل لتدفق الحجوزات

// مكون التغليف للاختبار
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <EnhancedNotificationsProvider>
      <KeyboardShortcutsProvider>
        {children}
      </KeyboardShortcutsProvider>
    </EnhancedNotificationsProvider>
  </BrowserRouter>
);

// محاكاة خدمات الطباعة
jest.mock('../../utils/printService', () => ({
  printBooking: jest.fn(),
  printMultipleBookings: jest.fn(),
  savePDF: jest.fn(),
  saveMultiplePDF: jest.fn()
}));

describe('BookingFlow Integration Tests', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('تدفق إضافة حجز جديد', () => {
    test('يجب إكمال تدفق إضافة حجز طيران بنجاح', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      // انتظار تحميل الصفحة
      await waitFor(() => {
        expect(screen.getByText('حجوزات الطيران')).toBeInTheDocument();
      });

      // النقر على زر إضافة حجز جديد
      const addButton = screen.getByText('إضافة حجز جديد');
      await user.click(addButton);

      // التحقق من ظهور نموذج الإضافة
      await waitFor(() => {
        expect(screen.getByText('إضافة حجز طيران جديد')).toBeInTheDocument();
      });

      // ملء النموذج
      await user.type(screen.getByLabelText('اسم العميل'), 'محمد أحمد');
      await user.type(screen.getByLabelText('رقم الهاتف'), '+966501234567');
      await user.type(screen.getByLabelText('البريد الإلكتروني'), '<EMAIL>');
      await user.type(screen.getByLabelText('رقم الجواز'), 'A12345678');
      await user.type(screen.getByLabelText('مدينة المغادرة'), 'الرياض');
      await user.type(screen.getByLabelText('مدينة الوصول'), 'دبي');
      await user.type(screen.getByLabelText('تاريخ المغادرة'), '2024-03-15');
      await user.type(screen.getByLabelText('تاريخ العودة'), '2024-03-22');
      await user.type(screen.getByLabelText('المبلغ'), '2500');

      // حفظ الحجز
      const saveButton = screen.getByText('حفظ الحجز');
      await user.click(saveButton);

      // التحقق من ظهور إشعار النجاح
      await waitFor(() => {
        expect(screen.getByText(/تم إضافة حجز الطيران بنجاح/)).toBeInTheDocument();
      });

      // التحقق من ظهور الحجز في القائمة
      await waitFor(() => {
        expect(screen.getByText('محمد أحمد')).toBeInTheDocument();
        expect(screen.getByText('الرياض → دبي')).toBeInTheDocument();
      });
    });

    test('يجب عرض أخطاء التحقق عند ملء بيانات غير صالحة', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('حجوزات الطيران')).toBeInTheDocument();
      });

      // فتح نموذج الإضافة
      const addButton = screen.getByText('إضافة حجز جديد');
      await user.click(addButton);

      // محاولة الحفظ بدون ملء البيانات المطلوبة
      const saveButton = screen.getByText('حفظ الحجز');
      await user.click(saveButton);

      // التحقق من ظهور رسائل الخطأ
      await waitFor(() => {
        expect(screen.getByText('اسم العميل مطلوب')).toBeInTheDocument();
        expect(screen.getByText('رقم الهاتف مطلوب')).toBeInTheDocument();
      });
    });
  });

  describe('تدفق تعديل الحجز', () => {
    test('يجب تعديل حجز موجود بنجاح', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      // انتظار تحميل البيانات
      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
      });

      // النقر على زر التعديل للحجز الأول
      const editButtons = screen.getAllByLabelText('تعديل');
      await user.click(editButtons[0]);

      // التحقق من ظهور نموذج التعديل
      await waitFor(() => {
        expect(screen.getByText('تعديل حجز الطيران')).toBeInTheDocument();
      });

      // تعديل البيانات
      const nameInput = screen.getByDisplayValue('أحمد محمد علي');
      await user.clear(nameInput);
      await user.type(nameInput, 'أحمد محمد علي المحدث');

      // حفظ التعديلات
      const saveButton = screen.getByText('حفظ التعديلات');
      await user.click(saveButton);

      // التحقق من ظهور إشعار النجاح
      await waitFor(() => {
        expect(screen.getByText(/تم تحديث الحجز بنجاح/)).toBeInTheDocument();
      });

      // التحقق من تحديث البيانات في القائمة
      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي المحدث')).toBeInTheDocument();
      });
    });
  });

  describe('تدفق حذف الحجز', () => {
    test('يجب حذف حجز بنجاح', async () => {
      // محاكاة window.confirm
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true);

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      // انتظار تحميل البيانات
      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
      });

      // النقر على زر الحذف
      const deleteButtons = screen.getAllByLabelText('حذف');
      await user.click(deleteButtons[0]);

      // التحقق من ظهور إشعار النجاح
      await waitFor(() => {
        expect(screen.getByText(/تم حذف حجز/)).toBeInTheDocument();
      });

      // التحقق من إزالة الحجز من القائمة
      await waitFor(() => {
        expect(screen.queryByText('أحمد محمد علي')).not.toBeInTheDocument();
      });

      confirmSpy.mockRestore();
    });

    test('يجب إلغاء الحذف عند رفض التأكيد', async () => {
      // محاكاة window.confirm للإرجاع false
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(false);

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
      });

      // النقر على زر الحذف
      const deleteButtons = screen.getAllByLabelText('حذف');
      await user.click(deleteButtons[0]);

      // التحقق من بقاء الحجز في القائمة
      expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();

      confirmSpy.mockRestore();
    });
  });

  describe('تدفق البحث والفلترة', () => {
    test('يجب البحث في الحجوزات بنجاح', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
        expect(screen.getByText('فاطمة علي سالم')).toBeInTheDocument();
      });

      // البحث عن "أحمد"
      const searchInput = screen.getByPlaceholderText('البحث في الحجوزات...');
      await user.type(searchInput, 'أحمد');

      // التحقق من ظهور النتائج المطابقة فقط
      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
        expect(screen.queryByText('فاطمة علي سالم')).not.toBeInTheDocument();
      });
    });

    test('يجب فلترة الحجوزات حسب الحالة', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getAllByText(/مؤكد|معلق|ملغي/)).toHaveLength.greaterThan(0);
      });

      // فلترة الحجوزات المؤكدة فقط
      const statusFilter = screen.getByDisplayValue('الكل');
      await user.selectOptions(statusFilter, 'confirmed');

      // التحقق من ظهور الحجوزات المؤكدة فقط
      await waitFor(() => {
        const statusBadges = screen.getAllByText('مؤكد');
        expect(statusBadges.length).toBeGreaterThan(0);
        expect(screen.queryByText('معلق')).not.toBeInTheDocument();
      });
    });
  });

  describe('تدفق الطباعة والتصدير', () => {
    test('يجب طباعة حجز واحد بنجاح', async () => {
      const { printBooking } = require('../../utils/printService');

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
      });

      // النقر على زر الطباعة
      const printButtons = screen.getAllByLabelText('طباعة');
      await user.click(printButtons[0]);

      // التحقق من استدعاء دالة الطباعة
      expect(printBooking).toHaveBeenCalledTimes(1);

      // التحقق من ظهور إشعار النجاح
      await waitFor(() => {
        expect(screen.getByText(/تم إرسال.*للطباعة/)).toBeInTheDocument();
      });
    });

    test('يجب حفظ PDF بنجاح', async () => {
      const { savePDF } = require('../../utils/printService');

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('أحمد محمد علي')).toBeInTheDocument();
      });

      // النقر على زر حفظ PDF
      const pdfButtons = screen.getAllByLabelText('حفظ PDF');
      await user.click(pdfButtons[0]);

      // التحقق من استدعاء دالة حفظ PDF
      expect(savePDF).toHaveBeenCalledTimes(1);

      // التحقق من ظهور إشعار النجاح
      await waitFor(() => {
        expect(screen.getByText(/تم حفظ.*كملف PDF/)).toBeInTheDocument();
      });
    });
  });

  describe('تدفق العمليات المجمعة', () => {
    test('يجب تحديد عدة حجوزات وطباعتها', async () => {
      const { printMultipleBookings } = require('../../utils/printService');

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getAllByRole('checkbox')).toHaveLength.greaterThan(0);
      });

      // تحديد عدة حجوزات
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[1]); // الحجز الأول
      await user.click(checkboxes[2]); // الحجز الثاني

      // النقر على زر الطباعة المجمعة
      const bulkPrintButton = screen.getByText('طباعة المحدد');
      await user.click(bulkPrintButton);

      // التحقق من استدعاء دالة الطباعة المجمعة
      expect(printMultipleBookings).toHaveBeenCalledTimes(1);
    });

    test('يجب حذف عدة حجوزات مجمعة', async () => {
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true);

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getAllByRole('checkbox')).toHaveLength.greaterThan(0);
      });

      // تحديد عدة حجوزات
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[1]);
      await user.click(checkboxes[2]);

      // النقر على زر الحذف المجمع
      const bulkDeleteButton = screen.getByText('حذف المحدد');
      await user.click(bulkDeleteButton);

      // التحقق من ظهور إشعار النجاح
      await waitFor(() => {
        expect(screen.getByText(/تم حذف.*حجز بنجاح/)).toBeInTheDocument();
      });

      confirmSpy.mockRestore();
    });
  });

  describe('تدفق اختصارات لوحة المفاتيح', () => {
    test('يجب فتح نموذج إضافة حجز بالاختصار Ctrl+N', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('حجوزات الطيران')).toBeInTheDocument();
      });

      // محاكاة ضغط Ctrl+N
      fireEvent.keyDown(document, {
        key: 'n',
        code: 'KeyN',
        ctrlKey: true
      });

      // التحقق من ظهور نموذج الإضافة
      await waitFor(() => {
        expect(screen.getByText('إضافة حجز طيران جديد')).toBeInTheDocument();
      });
    });

    test('يجب التركيز على حقل البحث بالاختصار Ctrl+F', async () => {
      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('حجوزات الطيران')).toBeInTheDocument();
      });

      // محاكاة ضغط Ctrl+F
      fireEvent.keyDown(document, {
        key: 'f',
        code: 'KeyF',
        ctrlKey: true
      });

      // التحقق من التركيز على حقل البحث
      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('البحث في الحجوزات...');
        expect(searchInput).toHaveFocus();
      });
    });
  });

  describe('تدفق معالجة الأخطاء', () => {
    test('يجب معالجة أخطاء الشبكة بشكل صحيح', async () => {
      // محاكاة خطأ في الشبكة
      const originalFetch = global.fetch;
      global.fetch = jest.fn(() => Promise.reject(new Error('Network Error')));

      render(
        <TestWrapper>
          <FlightBookingPage />
        </TestWrapper>
      );

      // التحقق من ظهور رسالة خطأ
      await waitFor(() => {
        expect(screen.getByText(/حدث خطأ في تحميل البيانات/)).toBeInTheDocument();
      });

      global.fetch = originalFetch;
    });
  });
});
