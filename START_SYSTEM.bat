@echo off
chcp 65001 >nul
color 0A
title نظام محاسبي لوكالات السفريات - تشغيل النظام

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🧳 نظام محاسبي متكامل لوكالات السفريات 🧳                ║
echo ║                                                              ║
echo ║              ✨ النظام الآن جاهز للاستخدام ✨                ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 بدء تشغيل النظام...
echo.

echo 📊 تشغيل خادم Backend (FastAPI)...
cd /d "%~dp0backend"
start "🖥️ Backend Server - FastAPI" cmd /k "echo 🖥️ Backend Server && echo ================== && venv\Scripts\activate && echo ✅ تم تفعيل البيئة الافتراضية && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo ⏳ انتظار تشغيل Backend...
timeout /t 8 /nobreak >nul

echo 🌐 تشغيل خادم Frontend (React)...
cd /d "%~dp0frontend"
start "🌐 Frontend Server - React" cmd /k "echo 🌐 Frontend Server && echo =================== && echo ✅ بدء تشغيل React && npm start"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 15 /nobreak >nul

echo 🌍 فتح النظام في المتصفح...
start "" "http://localhost:3000"
timeout /t 2 /nobreak >nul
start "" "http://localhost:8000/docs"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ تم تشغيل النظام بنجاح! ✅                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 معلومات النظام:
echo ═══════════════════
echo 🌐 الواجهة الرئيسية:    http://localhost:3000
echo 🖥️  خادم API:           http://localhost:8000  
echo 📚 توثيق API:          http://localhost:8000/docs
echo 🔧 API البديل:         http://localhost:8000/redoc
echo.

echo 🔑 بيانات تسجيل الدخول:
echo ═══════════════════════
echo 👤 اسم المستخدم: admin
echo 🔒 كلمة المرور:   admin123
echo.

echo 🎯 المميزات المتاحة:
echo ═══════════════════
echo ✅ صفحة رئيسية تفاعلية
echo ✅ نظام تسجيل دخول
echo ✅ لوحة تحكم ذكية
echo ✅ إدارة الحجوزات
echo ✅ إحصائيات مباشرة
echo ✅ واجهات عربية RTL
echo ✅ تصميم متجاوب
echo.

echo 🛠️ للمطورين:
echo ═══════════
echo 📁 مجلد المشروع: %~dp0
echo 🔧 Backend:  FastAPI + SQLAlchemy + SQLite
echo 🎨 Frontend: React + React Router
echo 📱 التصميم:  CSS3 + Responsive Design
echo.

echo 💡 نصائح الاستخدام:
echo ═══════════════════
echo • استخدم admin/admin123 لتسجيل الدخول
echo • جرب إضافة حجوزات جديدة من قسم الحجوزات
echo • تصفح لوحة التحكم لرؤية الإحصائيات
echo • استكشف توثيق API التفاعلي
echo.

echo 🚨 لإيقاف النظام:
echo ═══════════════════
echo • أغلق نوافذ الخوادم (Backend & Frontend)
echo • أو اضغط Ctrl+C في كل نافذة
echo.

echo 📞 في حالة وجود مشاكل:
echo ═══════════════════════
echo 1. تأكد من تثبيت Node.js و Python
echo 2. تحقق من أن المنافذ 3000 و 8000 غير مستخدمة
echo 3. أعد تشغيل هذا الملف كمدير
echo 4. تحقق من اتصال الإنترنت لتحميل المكتبات
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🎉 استمتع باستخدام النظام! 🎉                      ║
echo ║                                                              ║
echo ║        النظام جاهز للاستخدام والتطوير والتخصيص              ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause