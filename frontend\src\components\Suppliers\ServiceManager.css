.service-manager {
  margin: 20px 0;
}

.current-services {
  margin-bottom: 20px;
}

.current-services label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #2c3e50;
}

.services-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  padding: 10px;
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.remove-service-btn {
  background: rgba(255, 255, 255, 0.3);
  border: none;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
  transition: background 0.3s ease;
}

.remove-service-btn:hover {
  background: rgba(255, 255, 255, 0.5);
}

.no-services {
  color: #6c757d;
  font-style: italic;
  margin: 0;
  text-align: center;
  width: 100%;
  padding: 10px;
}

.add-service-section {
  border-top: 1px solid #e1e8ed;
  padding-top: 20px;
}

.predefined-services {
  margin-bottom: 20px;
}

.predefined-services label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #2c3e50;
}

.predefined-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  background: white;
}

.predefined-service-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  color: #2c3e50;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  text-align: right;
  transition: all 0.3s ease;
}

.predefined-service-btn:hover {
  background: #e3f2fd;
  border-color: #667eea;
  color: #667eea;
}

.custom-service {
  margin-top: 15px;
}

.add-custom-service-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.add-custom-service-btn:hover {
  background: #218838;
}

.custom-service-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.custom-service-form input {
  padding: 10px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
}

.custom-service-form input:focus {
  outline: none;
  border-color: #667eea;
}

.custom-service-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.save-custom-service-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.save-custom-service-btn:hover {
  background: #5a67d8;
}

.save-custom-service-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.cancel-custom-service-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.cancel-custom-service-btn:hover {
  background: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
  .predefined-services-grid {
    grid-template-columns: 1fr;
    max-height: 150px;
  }
  
  .custom-service-actions {
    flex-direction: column;
  }
  
  .services-list {
    flex-direction: column;
    align-items: stretch;
  }
  
  .service-item {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .predefined-service-btn {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .service-item {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .remove-service-btn {
    width: 18px;
    height: 18px;
    font-size: 14px;
  }
}