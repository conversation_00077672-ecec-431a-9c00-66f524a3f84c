import React, { useState, useEffect, useRef, useMemo } from 'react';
import './AutomatedTransactions.css';

const AutomatedTransactionsAdvanced = ({ transactions, accounts, onAddTransaction, currentUser }) => {
  const [activeTab, setActiveTab] = useState('quick');
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [printOptions, setPrintOptions] = useState({
    includeDetails: true,
    includeStats: true,
    includeRules: false,
    pageSize: 'A4',
    orientation: 'portrait'
  });
  const [exportFormat, setExportFormat] = useState('csv');
  const [viewMode, setViewMode] = useState('cards');
  const [sortConfig, setSortConfig] = useState({ key: 'date', direction: 'desc' });

  const printRef = useRef();
  const fileInputRef = useRef();

  // بيانات العمليات السريعة
  const [quickTransactions, setQuickTransactions] = useState([
    {
      id: 'QT001',
      type: 'revenue',
      title: 'إيراد مبيعات',
      description: 'تسجيل إيراد من المبيعات',
      amount: 0,
      fromAccount: '',
      toAccount: '',
      icon: '💰',
      color: '#27ae60',
      category: 'sales'
    },
    {
      id: 'QT002',
      type: 'expense',
      title: 'مصروف تشغيلي',
      description: 'تسجيل مصروف تشغيلي',
      amount: 0,
      fromAccount: '',
      toAccount: '',
      icon: '💸',
      color: '#e74c3c',
      category: 'operations'
    },
    {
      id: 'QT003',
      type: 'transfer',
      title: 'تحويل بين الحسابات',
      description: 'تحويل مبلغ بين حسابين',
      amount: 0,
      fromAccount: '',
      toAccount: '',
      icon: '🔄',
      color: '#3498db',
      category: 'transfer'
    },
    {
      id: 'QT004',
      type: 'payment',
      title: 'دفع فاتورة',
      description: 'دفع فاتورة مورد',
      amount: 0,
      fromAccount: '',
      toAccount: '',
      icon: '🧾',
      color: '#f39c12',
      category: 'payments'
    }
  ]);

  // القواعد التلقائية
  const [automationRules, setAutomationRules] = useState([
    {
      id: 'AR001',
      name: 'إهلاك الأصول الثابتة',
      description: 'حساب الإهلاك الشهري للأصول الثابتة',
      frequency: 'monthly',
      isActive: true,
      lastRun: '2024-11-01',
      nextRun: '2024-12-01',
      conditions: {
        accounts: ['1200', '1210', '1220'],
        percentage: 8.33,
        method: 'straight-line'
      },
      actions: {
        debitAccount: '5100',
        creditAccount: '1250'
      },
      icon: '🏢',
      color: '#9b59b6'
    },
    {
      id: 'AR002',
      name: 'توزيع المصروفات المشتركة',
      description: 'توزيع المصروفات على مراكز التكلفة',
      frequency: 'monthly',
      isActive: true,
      lastRun: '2024-11-01',
      nextRun: '2024-12-01',
      conditions: {
        accounts: ['5000', '5100', '5200'],
        distributionMethod: 'percentage',
        centers: ['CC001', 'CC002', 'CC003']
      },
      actions: {
        distribute: true
      },
      icon: '📊',
      color: '#e67e22'
    },
    {
      id: 'AR003',
      name: 'احتساب الضرائب',
      description: 'احتساب ضريبة القيمة المضافة',
      frequency: 'monthly',
      isActive: false,
      lastRun: '2024-10-01',
      nextRun: '2024-12-01',
      conditions: {
        taxRate: 15,
        accounts: ['4000', '4100', '4200']
      },
      actions: {
        debitAccount: '2300',
        creditAccount: '2310'
      },
      icon: '🧮',
      color: '#34495e'
    }
  ]);

  // قوالب المعاملات
  const [templates, setTemplates] = useState([
    {
      id: 'TPL001',
      name: 'فاتورة مبيعات',
      description: 'قالب لتسجيل فواتير المبيعات',
      entries: [
        { accountId: '1112', accountName: 'البنك', debit: 0, credit: 0 },
        { accountId: '4000', accountName: 'مبيعات', debit: 0, credit: 0 },
        { accountId: '2300', accountName: 'ضريبة القيمة المضافة', debit: 0, credit: 0 }
      ],
      category: 'sales',
      icon: '🧾'
    },
    {
      id: 'TPL002',
      name: 'فاتورة مشتريات',
      description: 'قالب لتسجيل فواتير المشتريات',
      entries: [
        { accountId: '5000', accountName: 'مشتريات', debit: 0, credit: 0 },
        { accountId: '2300', accountName: 'ضريبة القيمة المضافة', debit: 0, credit: 0 },
        { accountId: '1112', accountName: 'البنك', debit: 0, credit: 0 }
      ],
      category: 'purchases',
      icon: '🛒'
    }
  ]);

  // تصفية المعاملات التلقائية
  const filteredAutomatedTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const isAutomated = transaction.type === 'automated' || transaction.source === 'automation';
      const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           transaction.reference?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const transactionDate = new Date(transaction.date);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      const matchesDate = transactionDate >= startDate && transactionDate <= endDate;

      return isAutomated && matchesSearch && matchesDate;
    }).sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (sortConfig.key === 'date') {
        return sortConfig.direction === 'asc' 
          ? new Date(aValue) - new Date(bValue)
          : new Date(bValue) - new Date(aValue);
      }
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [transactions, searchTerm, dateRange, sortConfig]);

  // حساب الإحصائيات
  const automationStats = useMemo(() => {
    const totalTransactions = filteredAutomatedTransactions.length;
    const totalAmount = filteredAutomatedTransactions.reduce((sum, trans) => 
      sum + (trans.entries?.reduce((entrySum, entry) => entrySum + (entry.debit || 0), 0) || 0), 0);
    const activeRules = automationRules.filter(rule => rule.isActive).length;
    const savedTime = totalTransactions * 5; // تقدير 5 دقائق لكل معاملة

    return {
      totalTransactions,
      totalAmount,
      activeRules,
      savedTime,
      averageAmount: totalTransactions > 0 ? totalAmount / totalTransactions : 0
    };
  }, [filteredAutomatedTransactions, automationRules]);

  // تنفيذ معاملة سريعة
  const executeQuickTransaction = (quickTrans) => {
    if (!quickTrans.amount || quickTrans.amount <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    if (!quickTrans.fromAccount || !quickTrans.toAccount) {
      alert('يرجى اختيار الحسابات المطلوبة');
      return;
    }

    const transaction = {
      id: `AT-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      description: quickTrans.description,
      reference: `QT-${quickTrans.id}`,
      type: 'automated',
      source: 'quick-transaction',
      user: currentUser?.name || 'النظام',
      entries: [],
      createdAt: new Date().toISOString(),
      automationRule: quickTrans.id
    };

    // إنشاء القيود حسب نوع المعاملة
    switch (quickTrans.type) {
      case 'revenue':
        transaction.entries = [
          {
            accountId: quickTrans.fromAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.fromAccount)?.name || '',
            debit: quickTrans.amount,
            credit: 0,
            description: 'استلام إيراد'
          },
          {
            accountId: quickTrans.toAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.toAccount)?.name || '',
            debit: 0,
            credit: quickTrans.amount,
            description: 'إيراد مبيعات'
          }
        ];
        break;
      case 'expense':
        transaction.entries = [
          {
            accountId: quickTrans.toAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.toAccount)?.name || '',
            debit: quickTrans.amount,
            credit: 0,
            description: 'مصروف تشغيلي'
          },
          {
            accountId: quickTrans.fromAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.fromAccount)?.name || '',
            debit: 0,
            credit: quickTrans.amount,
            description: 'دفع مصروف'
          }
        ];
        break;
      case 'transfer':
        transaction.entries = [
          {
            accountId: quickTrans.toAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.toAccount)?.name || '',
            debit: quickTrans.amount,
            credit: 0,
            description: 'تحويل وارد'
          },
          {
            accountId: quickTrans.fromAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.fromAccount)?.name || '',
            debit: 0,
            credit: quickTrans.amount,
            description: 'تحويل صادر'
          }
        ];
        break;
      default:
        transaction.entries = [
          {
            accountId: quickTrans.toAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.toAccount)?.name || '',
            debit: quickTrans.amount,
            credit: 0,
            description: quickTrans.description
          },
          {
            accountId: quickTrans.fromAccount,
            accountName: accounts.find(acc => acc.id === quickTrans.fromAccount)?.name || '',
            debit: 0,
            credit: quickTrans.amount,
            description: quickTrans.description
          }
        ];
    }

    onAddTransaction(transaction);
    
    // إعادة تعيين المعاملة
    setQuickTransactions(prev => prev.map(qt => 
      qt.id === quickTrans.id 
        ? { ...qt, amount: 0, fromAccount: '', toAccount: '' }
        : qt
    ));

    alert('تم تنفيذ المعاملة بنجاح');
  };

  // تنفيذ قاعدة تلقائية
  const executeAutomationRule = (rule) => {
    if (!rule.isActive) {
      alert('هذه القاعدة غير مفعلة');
      return;
    }

    const transaction = {
      id: `AR-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      description: rule.description,
      reference: `AUTO-${rule.id}`,
      type: 'automated',
      source: 'automation-rule',
      user: 'النظام التلقائي',
      entries: [],
      createdAt: new Date().toISOString(),
      automationRule: rule.id
    };

    // تنفيذ القاعدة حسب النوع
    switch (rule.id) {
      case 'AR001': // إهلاك الأصول
        const depreciationAmount = 5000; // مثال
        transaction.entries = [
          {
            accountId: rule.actions.debitAccount,
            accountName: 'مصروف الإهلاك',
            debit: depreciationAmount,
            credit: 0,
            description: 'إهلاك شهري للأصول الثابتة'
          },
          {
            accountId: rule.actions.creditAccount,
            accountName: 'مجمع إهلاك الأصول الثابتة',
            debit: 0,
            credit: depreciationAmount,
            description: 'إهلاك شهري للأصول الثابتة'
          }
        ];
        break;
      case 'AR002': // توزيع المصروفات
        const distributionAmount = 3000; // مثال
        transaction.entries = [
          {
            accountId: '5001',
            accountName: 'مصروفات مركز التكلفة 1',
            debit: distributionAmount * 0.4,
            credit: 0,
            description: 'توزيع مصروفات مشتركة'
          },
          {
            accountId: '5002',
            accountName: 'مصروفات مركز التكلفة 2',
            debit: distributionAmount * 0.35,
            credit: 0,
            description: 'توزيع مصروفات مشتركة'
          },
          {
            accountId: '5003',
            accountName: 'مصروفات مركز التكلفة 3',
            debit: distributionAmount * 0.25,
            credit: 0,
            description: 'توزيع مصروفات مشتركة'
          },
          {
            accountId: '5000',
            accountName: 'مصروفات مشتركة',
            debit: 0,
            credit: distributionAmount,
            description: 'توزيع مصروفات مشتركة'
          }
        ];
        break;
      default:
        alert('قاعدة غير مدعومة حالياً');
        return;
    }

    onAddTransaction(transaction);

    // تحديث تاريخ آخر تشغيل
    setAutomationRules(prev => prev.map(r => 
      r.id === rule.id 
        ? { ...r, lastRun: new Date().toISOString().split('T')[0] }
        : r
    ));

    alert('تم تنفيذ القاعدة التلقائية بنجاح');
  };

  // وظائف الطباعة
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير العمليات التلقائية</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .stats { display: flex; justify-content: space-around; margin: 20px 0; padding: 15px; background: #f5f5f5; }
          .stat-item { text-align: center; }
          .stat-value { font-size: 18px; font-weight: bold; color: #333; }
          .stat-label { font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .amount { font-weight: bold; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    return `
      <div class="header">
        <div class="company-name">شركة شراء السياحية</div>
        <div class="report-title">تقرير العمليات التلقائية</div>
        <div class="date-range">
          من ${new Date(dateRange.startDate).toLocaleDateString('ar-SA')} 
          إلى ${new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
        </div>
      </div>
      
      ${printOptions.includeStats ? `
        <div class="stats">
          <div class="stat-item">
            <div class="stat-value">${automationStats.totalTransactions}</div>
            <div class="stat-label">إجمالي المعاملات</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${automationStats.totalAmount.toLocaleString()}</div>
            <div class="stat-label">إجمالي المبلغ</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${automationStats.activeRules}</div>
            <div class="stat-label">القواعد النشطة</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${automationStats.savedTime}</div>
            <div class="stat-label">الوقت المُوفر (دقيقة)</div>
          </div>
        </div>
      ` : ''}
      
      <table>
        <thead>
          <tr>
            <th>التاريخ</th>
            <th>رقم المعاملة</th>
            <th>الوصف</th>
            <th>النوع</th>
            <th>المبلغ</th>
            <th>المصدر</th>
          </tr>
        </thead>
        <tbody>
          ${filteredAutomatedTransactions.map(transaction => `
            <tr>
              <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
              <td>${transaction.id}</td>
              <td>${transaction.description}</td>
              <td>${transaction.source === 'quick-transaction' ? 'معاملة سريعة' : 'قاعدة تلقائية'}</td>
              <td class="amount">${(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()}</td>
              <td>${transaction.automationRule || '-'}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      ${printOptions.includeRules ? `
        <h3>القواعد التلقائية النشطة</h3>
        <table>
          <thead>
            <tr>
              <th>اسم القاعدة</th>
              <th>الوصف</th>
              <th>التكرار</th>
              <th>آخر تشغيل</th>
              <th>التشغيل التالي</th>
            </tr>
          </thead>
          <tbody>
            ${automationRules.filter(rule => rule.isActive).map(rule => `
              <tr>
                <td>${rule.name}</td>
                <td>${rule.description}</td>
                <td>${rule.frequency === 'monthly' ? 'شهري' : rule.frequency}</td>
                <td>${new Date(rule.lastRun).toLocaleDateString('ar-SA')}</td>
                <td>${new Date(rule.nextRun).toLocaleDateString('ar-SA')}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      ` : ''}
      
      <div class="footer">
        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
        <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
      </div>
    `;
  };

  // وظائف التصدير
  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    const headers = ['التاريخ', 'رقم المعاملة', 'الوصف', 'النوع', 'المبلغ', 'المصدر', 'المستخدم'];
    const rows = filteredAutomatedTransactions.map(transaction => [
      new Date(transaction.date).toLocaleDateString('ar-SA'),
      transaction.id,
      transaction.description,
      transaction.source === 'quick-transaction' ? 'معاملة سريعة' : 'قاعدة تلقائية',
      transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0,
      transaction.automationRule || '',
      transaction.user || ''
    ]);

    const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `العمليات-التلقائية-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  // استخدام قالب
  const useTemplate = (template) => {
    const transaction = {
      id: `TPL-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      description: `${template.name} - ${template.description}`,
      reference: `TEMPLATE-${template.id}`,
      type: 'automated',
      source: 'template',
      user: currentUser?.name || 'النظام',
      entries: template.entries.map(entry => ({ ...entry })),
      createdAt: new Date().toISOString(),
      template: template.id
    };

    // فتح نموذج لتعديل المبالغ
    setShowTemplateModal({ template, transaction });
  };

  return (
    <div className="automated-transactions-advanced">
      <div className="automation-header">
        <div className="header-content">
          <h2>⚡ العمليات التلقائية المتقدمة</h2>
          <p>أتمتة المعاملات المحاسبية وتوفير الوقت والجهد</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowRuleModal(true)}
          >
            ➕ قاعدة جديدة
          </button>
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-success"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowHistoryModal(true)}
          >
            📋 السجل
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="automation-stats">
        <div className="stat-card">
          <div className="stat-icon">🔄</div>
          <div className="stat-info">
            <div className="stat-value">{automationStats.totalTransactions}</div>
            <div className="stat-label">المعاملات التلقائية</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{automationStats.totalAmount.toLocaleString()}</div>
            <div className="stat-label">إجمالي المبلغ</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⚙️</div>
          <div className="stat-info">
            <div className="stat-value">{automationStats.activeRules}</div>
            <div className="stat-label">القواعد النشطة</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⏱️</div>
          <div className="stat-info">
            <div className="stat-value">{automationStats.savedTime}</div>
            <div className="stat-label">الوقت المُوفر (دقيقة)</div>
          </div>
        </div>
      </div>

      {/* التبويبات */}
      <div className="automation-tabs">
        <button
          className={`tab-btn ${activeTab === 'quick' ? 'active' : ''}`}
          onClick={() => setActiveTab('quick')}
        >
          <span className="tab-icon">⚡</span>
          المعاملات السريعة
        </button>
        <button
          className={`tab-btn ${activeTab === 'rules' ? 'active' : ''}`}
          onClick={() => setActiveTab('rules')}
        >
          <span className="tab-icon">🤖</span>
          القواعد التلقائية
        </button>
        <button
          className={`tab-btn ${activeTab === 'templates' ? 'active' : ''}`}
          onClick={() => setActiveTab('templates')}
        >
          <span className="tab-icon">📋</span>
          القوالب
        </button>
        <button
          className={`tab-btn ${activeTab === 'history' ? 'active' : ''}`}
          onClick={() => setActiveTab('history')}
        >
          <span className="tab-icon">📊</span>
          السجل والتقارير
        </button>
      </div>

      {/* محتوى التبويبات */}
      <div className="automation-content">
        {activeTab === 'quick' && (
          <div className="quick-transactions">
            <div className="section-header">
              <h3>⚡ المعاملات السريعة</h3>
              <p>تنفيذ المعاملات الشائعة بنقرة واحدة</p>
            </div>
            
            <div className="quick-grid">
              {quickTransactions.map(quickTrans => (
                <div key={quickTrans.id} className="quick-card" style={{ borderColor: quickTrans.color }}>
                  <div className="quick-header">
                    <span className="quick-icon" style={{ color: quickTrans.color }}>
                      {quickTrans.icon}
                    </span>
                    <div className="quick-info">
                      <h4>{quickTrans.title}</h4>
                      <p>{quickTrans.description}</p>
                    </div>
                  </div>
                  
                  <div className="quick-form">
                    <div className="form-group">
                      <label>المبلغ *</label>
                      <input
                        type="number"
                        value={quickTrans.amount}
                        onChange={(e) => setQuickTransactions(prev => 
                          prev.map(qt => qt.id === quickTrans.id 
                            ? { ...qt, amount: parseFloat(e.target.value) || 0 }
                            : qt
                          )
                        )}
                        className="form-control"
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    
                    <div className="form-group">
                      <label>
                        {quickTrans.type === 'revenue' ? 'حساب الاستلام' : 
                         quickTrans.type === 'expense' ? 'حساب الدفع' : 'من حساب'}
                      </label>
                      <select
                        value={quickTrans.fromAccount}
                        onChange={(e) => setQuickTransactions(prev => 
                          prev.map(qt => qt.id === quickTrans.id 
                            ? { ...qt, fromAccount: e.target.value }
                            : qt
                          )
                        )}
                        className="form-control"
                      >
                        <option value="">اختر الحساب</option>
                        {accounts.filter(acc => acc.type === 'detail').map(account => (
                          <option key={account.id} value={account.id}>
                            {account.name} ({account.id})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="form-group">
                      <label>
                        {quickTrans.type === 'revenue' ? 'حساب الإيراد' : 
                         quickTrans.type === 'expense' ? 'حساب المصروف' : 'إلى حساب'}
                      </label>
                      <select
                        value={quickTrans.toAccount}
                        onChange={(e) => setQuickTransactions(prev => 
                          prev.map(qt => qt.id === quickTrans.id 
                            ? { ...qt, toAccount: e.target.value }
                            : qt
                          )
                        )}
                        className="form-control"
                      >
                        <option value="">اختر الحساب</option>
                        {accounts.filter(acc => acc.type === 'detail').map(account => (
                          <option key={account.id} value={account.id}>
                            {account.name} ({account.id})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <button
                      className="btn btn-primary btn-block"
                      onClick={() => executeQuickTransaction(quickTrans)}
                      disabled={!quickTrans.amount || !quickTrans.fromAccount || !quickTrans.toAccount}
                    >
                      ⚡ تنفيذ فوري
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'rules' && (
          <div className="automation-rules">
            <div className="section-header">
              <h3>🤖 القواعد التلقائية</h3>
              <p>قواعد محددة مسبقاً للمعاملات المتكررة</p>
            </div>
            
            <div className="rules-grid">
              {automationRules.map(rule => (
                <div key={rule.id} className={`rule-card ${rule.isActive ? 'active' : 'inactive'}`}>
                  <div className="rule-header">
                    <span className="rule-icon" style={{ color: rule.color }}>
                      {rule.icon}
                    </span>
                    <div className="rule-info">
                      <h4>{rule.name}</h4>
                      <p>{rule.description}</p>
                    </div>
                    <div className="rule-status">
                      <span className={`status-badge ${rule.isActive ? 'active' : 'inactive'}`}>
                        {rule.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="rule-details">
                    <div className="detail-item">
                      <span className="label">التكرار:</span>
                      <span className="value">
                        {rule.frequency === 'monthly' ? 'شهري' : rule.frequency}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="label">آخر تشغيل:</span>
                      <span className="value">
                        {new Date(rule.lastRun).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="label">التشغيل التالي:</span>
                      <span className="value">
                        {new Date(rule.nextRun).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  </div>
                  
                  <div className="rule-actions">
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={() => executeAutomationRule(rule)}
                      disabled={!rule.isActive}
                    >
                      ▶️ تشغيل الآن
                    </button>
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => {
                        setAutomationRules(prev => prev.map(r => 
                          r.id === rule.id ? { ...r, isActive: !r.isActive } : r
                        ));
                      }}
                    >
                      {rule.isActive ? '⏸️ إيقاف' : '▶️ تفعيل'}
                    </button>
                    <button className="btn btn-sm btn-info">
                      ⚙️ تعديل
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'templates' && (
          <div className="templates-section">
            <div className="section-header">
              <h3>📋 قوالب المعاملات</h3>
              <p>قوالب جاهزة للمعاملات الشائعة</p>
            </div>
            
            <div className="templates-grid">
              {templates.map(template => (
                <div key={template.id} className="template-card">
                  <div className="template-header">
                    <span className="template-icon">{template.icon}</span>
                    <div className="template-info">
                      <h4>{template.name}</h4>
                      <p>{template.description}</p>
                    </div>
                  </div>
                  
                  <div className="template-preview">
                    <h5>معاينة القيود:</h5>
                    <div className="entries-preview">
                      {template.entries.map((entry, index) => (
                        <div key={index} className="entry-preview">
                          <span className="account-name">{entry.accountName}</span>
                          <span className="entry-type">
                            {index === 0 ? 'مدين' : 'دائن'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="template-actions">
                    <button
                      className="btn btn-primary btn-block"
                      onClick={() => useTemplate(template)}
                    >
                      📝 استخدام القالب
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="automation-history">
            <div className="section-header">
              <h3>📊 سجل العمليات التلقائية</h3>
              <p>عرض وتحليل المعاملات التلقائية المنفذة</p>
            </div>
            
            <div className="history-controls">
              <div className="search-filters">
                <input
                  type="text"
                  placeholder="🔍 البحث في المعاملات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
                
                <div className="date-range">
                  <input
                    type="date"
                    value={dateRange.startDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                    className="date-input"
                  />
                  <span>إلى</span>
                  <input
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                    className="date-input"
                  />
                </div>
              </div>
              
              <div className="view-controls">
                <div className="view-modes">
                  <button
                    className={`view-mode-btn ${viewMode === 'table' ? 'active' : ''}`}
                    onClick={() => setViewMode('table')}
                  >
                    📋 جدول
                  </button>
                  <button
                    className={`view-mode-btn ${viewMode === 'cards' ? 'active' : ''}`}
                    onClick={() => setViewMode('cards')}
                  >
                    🗃️ بطاقات
                  </button>
                </div>
              </div>
            </div>
            
            {viewMode === 'table' ? (
              <div className="history-table">
                {filteredAutomatedTransactions.length === 0 ? (
                  <div className="no-data">
                    <span className="icon">🤖</span>
                    <h3>لا توجد معاملات تلقائية</h3>
                    <p>لم يتم تنفيذ أي معاملات تلقائية في الفترة المحددة</p>
                  </div>
                ) : (
                  <table className="automation-table">
                    <thead>
                      <tr>
                        <th onClick={() => handleSort('date')}>
                          التاريخ {sortConfig.key === 'date' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </th>
                        <th>رقم المعاملة</th>
                        <th>الوصف</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>المصدر</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredAutomatedTransactions.map(transaction => (
                        <tr key={transaction.id}>
                          <td>{new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                          <td className="transaction-id">{transaction.id}</td>
                          <td>{transaction.description}</td>
                          <td>
                            <span className={`type-badge ${transaction.source}`}>
                              {transaction.source === 'quick-transaction' ? 'معاملة سريعة' : 'قاعدة تلقائية'}
                            </span>
                          </td>
                          <td className="amount">
                            {(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()}
                          </td>
                          <td>{transaction.automationRule || '-'}</td>
                          <td className="actions">
                            <button className="action-btn view-btn" title="عرض التفاصيل">👁️</button>
                            <button className="action-btn print-btn" title="طباعة">🖨️</button>
                            <button className="action-btn edit-btn" title="تعديل">✏️</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            ) : (
              <div className="history-cards">
                {filteredAutomatedTransactions.map(transaction => (
                  <div key={transaction.id} className="history-card">
                    <div className="card-header">
                      <div className="card-title">
                        <span className="transaction-id">{transaction.id}</span>
                        <span className="transaction-date">
                          {new Date(transaction.date).toLocaleDateString('ar-SA')}
                        </span>
                      </div>
                      <span className={`type-badge ${transaction.source}`}>
                        {transaction.source === 'quick-transaction' ? 'سريعة' : 'تلقائية'}
                      </span>
                    </div>
                    
                    <div className="card-content">
                      <div className="description">{transaction.description}</div>
                      <div className="amount">
                        المبلغ: {(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()} ريال
                      </div>
                      <div className="source">
                        المصدر: {transaction.automationRule || 'غير محدد'}
                      </div>
                    </div>
                    
                    <div className="card-actions">
                      <button className="action-btn view-btn">👁️ عرض</button>
                      <button className="action-btn print-btn">🖨️ طباعة</button>
                      <button className="action-btn edit-btn">✏️ تعديل</button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* نموذج خيارات الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeDetails}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                    />
                    تضمين تفاصيل المعاملات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeStats}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeStats: e.target.checked }))}
                    />
                    تضمين الإحصائيات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeRules}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeRules: e.target.checked }))}
                    />
                    تضمين القواعد التلقائية
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">عدد المعاملات:</span>
                      <span className="value">{filteredAutomatedTransactions.length}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">إجمالي المبلغ:</span>
                      <span className="value">{automationStats.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">الفترة:</span>
                      <span className="value">
                        {new Date(dateRange.startDate).toLocaleDateString('ar-SA')} - 
                        {new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج استخدام القالب */}
      {showTemplateModal && (
        <div className="modal-overlay">
          <div className="template-modal">
            <div className="modal-header">
              <h3>📋 استخدام القالب: {showTemplateModal.template?.name}</h3>
              <button 
                className="close-btn"
                onClick={() => setShowTemplateModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="template-form">
                <div className="form-group">
                  <label>وصف المعاملة:</label>
                  <input
                    type="text"
                    value={showTemplateModal.transaction?.description || ''}
                    onChange={(e) => setShowTemplateModal(prev => ({
                      ...prev,
                      transaction: { ...prev.transaction, description: e.target.value }
                    }))}
                    className="form-control"
                  />
                </div>

                <div className="entries-section">
                  <h4>تفاصيل القيد:</h4>
                  <table className="entries-table">
                    <thead>
                      <tr>
                        <th>الحساب</th>
                        <th>مدين</th>
                        <th>دائن</th>
                      </tr>
                    </thead>
                    <tbody>
                      {showTemplateModal.transaction?.entries.map((entry, index) => (
                        <tr key={index}>
                          <td>{entry.accountName}</td>
                          <td>
                            <input
                              type="number"
                              value={entry.debit}
                              onChange={(e) => {
                                const newEntries = [...showTemplateModal.transaction.entries];
                                newEntries[index].debit = parseFloat(e.target.value) || 0;
                                newEntries[index].credit = 0;
                                setShowTemplateModal(prev => ({
                                  ...prev,
                                  transaction: { ...prev.transaction, entries: newEntries }
                                }));
                              }}
                              className="form-control"
                              min="0"
                              step="0.01"
                            />
                          </td>
                          <td>
                            <input
                              type="number"
                              value={entry.credit}
                              onChange={(e) => {
                                const newEntries = [...showTemplateModal.transaction.entries];
                                newEntries[index].credit = parseFloat(e.target.value) || 0;
                                newEntries[index].debit = 0;
                                setShowTemplateModal(prev => ({
                                  ...prev,
                                  transaction: { ...prev.transaction, entries: newEntries }
                                }));
                              }}
                              className="form-control"
                              min="0"
                              step="0.01"
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowTemplateModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => {
                  onAddTransaction(showTemplateModal.transaction);
                  setShowTemplateModal(false);
                  alert('تم إنشاء المعاملة من القالب بنجاح');
                }}
              >
                💾 إنشاء المعاملة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomatedTransactionsAdvanced;