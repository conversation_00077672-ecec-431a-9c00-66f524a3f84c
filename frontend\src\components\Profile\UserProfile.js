import React, { useState, useEffect } from 'react';
import ModernCard, { CardBody, CardHeader, CardTitle } from '../UI/ModernCard';
import ModernButton from '../UI/ModernButton';
import { ModernGrid, ModernFlex } from '../UI/ModernLayout';
import { ModernAlert } from '../UI/ModernAlert';
import './UserProfile.css';

const UserProfile = () => {
  const [user, setUser] = useState({
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'أحمد محمد العلي',
    phone: '+966501234567',
    role: 'مدير النظام',
    department: 'الإدارة العامة',
    joinDate: '2024-01-15',
    lastLogin: '2024-12-19 10:30:00',
    avatar: '👤',
    status: 'نشط',
    permissions: [
      'إدارة المستخدمين',
      'إدارة الحجوزات', 
      'إدارة المالية',
      'عرض التقارير',
      'إدارة النظام'
    ]
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    // تحميل بيانات المستخدم من API
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      // محاكاة تحميل البيانات من API
      // const response = await fetch('/api/user/profile');
      // const userData = await response.json();
      // setUser(userData);
      
      // بيانات تجريبية
      setEditForm({
        fullName: user.fullName,
        email: user.email,
        phone: user.phone,
        department: user.department
      });
    } catch (error) {
      console.error('خطأ في تحميل بيانات المستخدم:', error);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm({
      fullName: user.fullName,
      email: user.email,
      phone: user.phone,
      department: user.department
    });
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditForm({});
  };

  const handleSave = async () => {
    try {
      // محاكاة حفظ البيانات في API
      // const response = await fetch('/api/user/profile', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(editForm)
      // });

      // تحديث البيانات محلياً
      setUser(prev => ({
        ...prev,
        ...editForm
      }));

      setIsEditing(false);
      setAlertMessage('تم حفظ التغييرات بنجاح');
      setShowAlert(true);
      
      setTimeout(() => setShowAlert(false), 3000);
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      setAlertMessage('حدث خطأ في حفظ البيانات');
      setShowAlert(true);
    }
  };

  const handleInputChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = () => {
    // فتح نافذة تغيير كلمة المرور
    console.log('فتح نافذة تغيير كلمة المرور');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateTimeString) => {
    return new Date(dateTimeString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="user-profile">
      {/* Alert */}
      {showAlert && (
        <ModernAlert
          type="success"
          title="نجح الحفظ"
          message={alertMessage}
          onClose={() => setShowAlert(false)}
        />
      )}

      {/* Header */}
      <div className="profile-header">
        <ModernFlex align="center" justify="between" className="profile-header-content">
          <div className="profile-title">
            <h1>الملف الشخصي</h1>
            <p>إدارة معلوماتك الشخصية وإعدادات الحساب</p>
          </div>
          
          <div className="profile-actions">
            {!isEditing ? (
              <ModernButton variant="primary" onClick={handleEdit}>
                تعديل المعلومات
              </ModernButton>
            ) : (
              <ModernFlex gap="sm">
                <ModernButton variant="outline" onClick={handleCancel}>
                  إلغاء
                </ModernButton>
                <ModernButton variant="primary" onClick={handleSave}>
                  حفظ التغييرات
                </ModernButton>
              </ModernFlex>
            )}
          </div>
        </ModernFlex>
      </div>

      <ModernGrid cols={3} gap="lg" responsive className="profile-content">
        
        {/* معلومات أساسية */}
        <div className="profile-section basic-info">
          <ModernCard>
            <CardHeader>
              <CardTitle>المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="profile-avatar-section">
                <div className="profile-avatar-large">
                  {user.avatar}
                </div>
                <div className="profile-basic-details">
                  <h3>{user.fullName}</h3>
                  <p className="profile-role">{user.role}</p>
                  <span className={`profile-status profile-status--${user.status === 'نشط' ? 'active' : 'inactive'}`}>
                    {user.status}
                  </span>
                </div>
              </div>

              <div className="profile-fields">
                <div className="profile-field">
                  <label>الاسم الكامل</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.fullName || ''}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      className="profile-input"
                    />
                  ) : (
                    <span>{user.fullName}</span>
                  )}
                </div>

                <div className="profile-field">
                  <label>اسم المستخدم</label>
                  <span>{user.username}</span>
                  <small>لا يمكن تغيير اسم المستخدم</small>
                </div>

                <div className="profile-field">
                  <label>البريد الإلكتروني</label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={editForm.email || ''}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="profile-input"
                    />
                  ) : (
                    <span>{user.email}</span>
                  )}
                </div>

                <div className="profile-field">
                  <label>رقم الهاتف</label>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={editForm.phone || ''}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="profile-input"
                    />
                  ) : (
                    <span>{user.phone}</span>
                  )}
                </div>

                <div className="profile-field">
                  <label>القسم</label>
                  {isEditing ? (
                    <select
                      value={editForm.department || ''}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      className="profile-input"
                    >
                      <option value="الإدارة العامة">الإدارة العامة</option>
                      <option value="المبيعات">المبيعات</option>
                      <option value="المحاسبة">المحاسبة</option>
                      <option value="خدمة العملاء">خدمة العملاء</option>
                      <option value="تقنية المعلومات">تقنية المعلومات</option>
                    </select>
                  ) : (
                    <span>{user.department}</span>
                  )}
                </div>
              </div>
            </CardBody>
          </ModernCard>
        </div>

        {/* معلومات الحساب */}
        <div className="profile-section account-info">
          <ModernCard>
            <CardHeader>
              <CardTitle>معلومات الحساب</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="profile-fields">
                <div className="profile-field">
                  <label>تاريخ الانضمام</label>
                  <span>{formatDate(user.joinDate)}</span>
                </div>

                <div className="profile-field">
                  <label>آخر تسجيل دخول</label>
                  <span>{formatDateTime(user.lastLogin)}</span>
                </div>

                <div className="profile-field">
                  <label>الدور الوظيفي</label>
                  <span>{user.role}</span>
                </div>

                <div className="profile-field">
                  <label>رقم الموظف</label>
                  <span>EMP-{user.id.toString().padStart(4, '0')}</span>
                </div>
              </div>

              <div className="profile-actions-section">
                <ModernButton 
                  variant="outline" 
                  fullWidth 
                  onClick={handlePasswordChange}
                >
                  تغيير كلمة المرور
                </ModernButton>
              </div>
            </CardBody>
          </ModernCard>

          {/* الأمان */}
          <ModernCard className="security-card">
            <CardHeader>
              <CardTitle>الأمان</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="security-items">
                <div className="security-item">
                  <div className="security-icon">🔐</div>
                  <div className="security-content">
                    <h4>المصادقة الثنائية</h4>
                    <p>غير مفعلة</p>
                  </div>
                  <ModernButton variant="ghost" size="sm">
                    تفعيل
                  </ModernButton>
                </div>

                <div className="security-item">
                  <div className="security-icon">📱</div>
                  <div className="security-content">
                    <h4>الأجهزة المتصلة</h4>
                    <p>جهاز واحد نشط</p>
                  </div>
                  <ModernButton variant="ghost" size="sm">
                    إدارة
                  </ModernButton>
                </div>

                <div className="security-item">
                  <div className="security-icon">🔔</div>
                  <div className="security-content">
                    <h4>تنبيهات الأمان</h4>
                    <p>مفعلة</p>
                  </div>
                  <ModernButton variant="ghost" size="sm">
                    إعدادات
                  </ModernButton>
                </div>
              </div>
            </CardBody>
          </ModernCard>
        </div>

        {/* الصلاحيات */}
        <div className="profile-section permissions-info">
          <ModernCard>
            <CardHeader>
              <CardTitle>الصلاحيات</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="permissions-list">
                {user.permissions.map((permission, index) => (
                  <div key={index} className="permission-item">
                    <div className="permission-icon">✅</div>
                    <span className="permission-text">{permission}</span>
                  </div>
                ))}
              </div>

              <div className="permissions-note">
                <p>
                  <strong>ملاحظة:</strong> لتعديل الصلاحيات، يرجى التواصل مع مدير النظام.
                </p>
              </div>
            </CardBody>
          </ModernCard>

          {/* الإحصائيات */}
          <ModernCard className="stats-card">
            <CardHeader>
              <CardTitle>إحصائياتي</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="user-stats">
                <div className="stat-item">
                  <div className="stat-value">247</div>
                  <div className="stat-label">الحجوزات المعالجة</div>
                </div>

                <div className="stat-item">
                  <div className="stat-value">89</div>
                  <div className="stat-label">العملاء المخدومين</div>
                </div>

                <div className="stat-item">
                  <div className="stat-value">156</div>
                  <div className="stat-label">الفواتير المصدرة</div>
                </div>

                <div className="stat-item">
                  <div className="stat-value">98%</div>
                  <div className="stat-label">معدل رضا العملاء</div>
                </div>
              </div>
            </CardBody>
          </ModernCard>
        </div>
      </ModernGrid>
    </div>
  );
};

export default UserProfile;