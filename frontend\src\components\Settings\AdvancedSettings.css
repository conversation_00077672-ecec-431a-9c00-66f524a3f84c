/* Advanced Settings Styles */

/* Permissions Manager */
.permissions-manager {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.permissions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.permissions-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 700;
}

.add-role-btn {
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
}

.add-role-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.add-role-form {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
}

.form-row {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.form-row input {
  flex: 1;
  min-width: 200px;
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
}

.save-role-btn {
  padding: 10px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-role-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.roles-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.role-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.role-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.role-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.role-code {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.role-actions {
  display: flex;
  gap: 10px;
}

.edit-role-btn,
.delete-role-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.edit-role-btn {
  background: #17a2b8;
  color: white;
}

.edit-role-btn:hover {
  background: #138496;
}

.delete-role-btn {
  background: #dc3545;
  color: white;
}

.delete-role-btn:hover {
  background: #c82333;
}

.permissions-grid {
  padding: 20px;
  background: white;
}

.grid-header {
  display: grid;
  grid-template-columns: 2fr repeat(4, 1fr);
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #495057;
}

.permission-row {
  display: grid;
  grid-template-columns: 2fr repeat(4, 1fr);
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  align-items: center;
}

.permission-row:last-child {
  border-bottom: none;
}

.module-name {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  color: #495057;
}

.module-icon {
  font-size: 16px;
}

.permission-cell {
  display: flex;
  justify-content: center;
}

.permission-toggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.permission-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.permission-toggle input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* System Logs */
.system-logs {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.logs-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 700;
}

.logs-actions {
  display: flex;
  gap: 10px;
}

.export-logs-btn,
.clear-logs-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.export-logs-btn {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(23, 162, 184, 0.3);
}

.export-logs-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

.clear-logs-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
}

.clear-logs-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

.logs-filters {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
}

.filter-row {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-row select,
.filter-row input {
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  min-width: 150px;
}

.filter-row input {
  flex: 1;
  min-width: 200px;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.log-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  transition: all 0.3s ease;
}

.log-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.log-level {
  font-weight: 600;
  font-size: 14px;
}

.log-timestamp {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #6c757d;
}

.log-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.log-main {
  flex: 1;
}

.log-user-action {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.log-details {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.log-meta {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-end;
}

.log-module,
.log-ip {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
}

.logs-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.logs-pagination button {
  padding: 10px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logs-pagination button:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.logs-pagination button:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.page-info {
  font-weight: 600;
  color: #495057;
}

/* Email Templates */
.email-templates {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.templates-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 700;
}

.add-template-btn {
  padding: 10px 16px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.add-template-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.templates-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 25px;
}

.templates-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.template-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.template-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-item.active {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.template-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.template-type {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.template-status {
  display: flex;
  align-items: center;
}

.status-toggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.status-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.status-toggle .toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 20px;
}

.status-toggle .toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.status-toggle input:checked + .toggle-slider {
  background-color: #28a745;
}

.status-toggle input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.template-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.edit-btn,
.preview-btn,
.delete-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.edit-btn {
  background: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #138496;
}

.preview-btn {
  background: #6f42c1;
  color: white;
}

.preview-btn:hover {
  background: #5a2d91;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

.template-editor {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.editor-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.close-editor-btn {
  padding: 8px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.close-editor-btn:hover {
  background: #5a6268;
}

.editor-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 200px;
  font-family: 'Courier New', monospace;
}

.variables-helper {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
}

.variables-helper h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.variables-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.variable-tag {
  background: #e9ecef;
  color: #495057;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  cursor: pointer;
  transition: all 0.3s ease;
}

.variable-tag:hover {
  background: #667eea;
  color: white;
}

/* Maintenance Manager */
.maintenance-manager {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.maintenance-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.maintenance-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 700;
}

.maintenance-section,
.system-health-section,
.updates-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.maintenance-toggle {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.maintenance-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.maintenance-toggle .toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 30px;
}

.maintenance-toggle .toggle-slider:before {
  position: absolute;
  content: "";
  height: 24px;
  width: 24px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.maintenance-toggle input:checked + .toggle-slider {
  background-color: #dc3545;
}

.maintenance-toggle input:checked + .toggle-slider:before {
  transform: translateX(30px);
}

.maintenance-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.maintenance-actions {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.schedule-btn {
  padding: 10px 16px;
  background: #ffc107;
  color: #212529;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.schedule-btn:hover {
  background: #e0a800;
  transform: translateY(-1px);
}

.scheduled-info {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 14px;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.health-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.health-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}

.health-icon {
  font-size: 20px;
}

.component-name {
  font-weight: 600;
  color: #495057;
  text-transform: capitalize;
}

.health-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.health-status {
  font-weight: 600;
  text-transform: capitalize;
}

.response-time {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
}

.updates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.check-updates-btn {
  padding: 10px 16px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.check-updates-btn:hover {
  background: #138496;
  transform: translateY(-1px);
}

.updates-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.update-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.update-card.critical {
  border-color: #dc3545;
  background: #fff5f5;
}

.update-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.update-info h5 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.critical-badge {
  background: #dc3545;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  margin-left: 8px;
}

.update-version {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.install-btn {
  padding: 8px 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.install-btn:hover {
  background: #218838;
}

.installing-status {
  color: #ffc107;
  font-weight: 600;
  font-size: 14px;
}

.installed-status {
  color: #28a745;
  font-weight: 600;
  font-size: 14px;
}

.update-details p {
  margin: 0 0 10px 0;
  color: #6c757d;
  line-height: 1.5;
}

.update-meta {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .templates-container {
    grid-template-columns: 1fr;
  }
  
  .template-editor {
    max-height: none;
  }
}

@media (max-width: 768px) {
  .permissions-header,
  .logs-header,
  .templates-header,
  .maintenance-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
  
  .form-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-row input {
    min-width: auto;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-row select,
  .filter-row input {
    min-width: auto;
  }
  
  .log-content {
    flex-direction: column;
    gap: 15px;
  }
  
  .log-meta {
    align-items: flex-start;
    flex-direction: row;
    gap: 10px;
  }
  
  .permissions-grid {
    overflow-x: auto;
  }
  
  .grid-header,
  .permission-row {
    min-width: 600px;
  }
  
  .health-grid {
    grid-template-columns: 1fr;
  }
  
  .maintenance-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .update-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .update-meta {
    flex-direction: column;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .permissions-manager,
  .system-logs,
  .email-templates,
  .maintenance-manager {
    padding: 15px;
  }
  
  .role-header,
  .template-header,
  .update-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .role-actions,
  .template-actions {
    justify-content: center;
  }
  
  .logs-pagination {
    flex-direction: column;
    gap: 15px;
  }
  
  .logs-pagination button {
    width: 100%;
  }
}