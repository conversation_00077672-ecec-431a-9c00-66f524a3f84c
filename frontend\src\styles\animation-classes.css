/* 🎭 فئات الحركات الجاهزة للاستخدام */

/* ===== فئات الحركات الأساسية ===== */

/* 💫 حركات الظهور */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
  animation: fadeInDown 0.6s ease-out;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
  animation: fadeInRight 0.6s ease-out;
}

/* 🌟 حركات التكبير */
.animate-zoomIn {
  animation: zoomIn 0.6s ease-out;
}

.animate-zoomInUp {
  animation: zoomInUp 0.6s ease-out;
}

/* 🎯 حركات الانزلاق */
.animate-slideInUp {
  animation: slideInUp 0.6s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.6s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

/* 🌊 حركات الارتداد */
.animate-bounceIn {
  animation: bounceIn 0.75s ease-out;
}

.animate-bounceInUp {
  animation: bounceInUp 1s ease-out;
}

/* 🔄 حركات التدوير */
.animate-rotateIn {
  animation: rotateIn 0.6s ease-out;
}

.animate-rotateInUpLeft {
  animation: rotateInUpLeft 0.6s ease-out;
}

/* ✨ حركات خاصة */
.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-heartBeat {
  animation: heartBeat 1.3s ease-in-out infinite;
}

.animate-wobble {
  animation: wobble 1s ease-in-out;
}

.animate-jello {
  animation: jello 0.9s ease-in-out;
}

.animate-breathe {
  animation: breathe 3s ease-in-out infinite;
}

.animate-flash {
  animation: flash 2s infinite;
}

/* 🌈 حركات مستمرة */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-spinSlow {
  animation: spin 3s linear infinite;
}

.animate-spinReverse {
  animation: spinReverse 1s linear infinite;
}

.animate-gradientShift {
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-wave {
  animation: wave 2s ease-in-out infinite;
}

/* ===== تأخيرات الحركة ===== */
.animate-delay-75 {
  animation-delay: 75ms;
}

.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-150 {
  animation-delay: 150ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

.animate-delay-700 {
  animation-delay: 700ms;
}

.animate-delay-1000 {
  animation-delay: 1000ms;
}

/* ===== مدة الحركة ===== */
.animate-duration-75 {
  animation-duration: 75ms;
}

.animate-duration-100 {
  animation-duration: 100ms;
}

.animate-duration-150 {
  animation-duration: 150ms;
}

.animate-duration-200 {
  animation-duration: 200ms;
}

.animate-duration-300 {
  animation-duration: 300ms;
}

.animate-duration-500 {
  animation-duration: 500ms;
}

.animate-duration-700 {
  animation-duration: 700ms;
}

.animate-duration-1000 {
  animation-duration: 1000ms;
}

/* ===== تكرار الحركة ===== */
.animate-once {
  animation-iteration-count: 1;
}

.animate-twice {
  animation-iteration-count: 2;
}

.animate-infinite {
  animation-iteration-count: infinite;
}

/* ===== اتجاه الحركة ===== */
.animate-normal {
  animation-direction: normal;
}

.animate-reverse {
  animation-direction: reverse;
}

.animate-alternate {
  animation-direction: alternate;
}

.animate-alternate-reverse {
  animation-direction: alternate-reverse;
}

/* ===== حالة الحركة ===== */
.animate-paused {
  animation-play-state: paused;
}

.animate-running {
  animation-play-state: running;
}

/* ===== ملء الحركة ===== */
.animate-fill-none {
  animation-fill-mode: none;
}

.animate-fill-forwards {
  animation-fill-mode: forwards;
}

.animate-fill-backwards {
  animation-fill-mode: backwards;
}

.animate-fill-both {
  animation-fill-mode: both;
}

/* ===== حركات التفاعل ===== */
.hover-animate:hover {
  animation: pulse 0.6s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
  transition: transform 0.3s ease;
}

.hover-wobble:hover {
  animation: wobble 0.8s ease-in-out;
}

/* ===== حركات التحميل ===== */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* ===== حركات الإشعارات ===== */
.notification-enter {
  animation: slideInRight 0.3s ease-out;
}

.notification-exit {
  animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

/* ===== حركات المودال ===== */
.modal-enter {
  animation: zoomIn 0.3s ease-out;
}

.modal-exit {
  animation: zoomOut 0.3s ease-in;
}

@keyframes zoomOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* ===== حركات القوائم ===== */
.menu-item-enter {
  animation: fadeInUp 0.3s ease-out;
}

.menu-item-enter:nth-child(1) { animation-delay: 0ms; }
.menu-item-enter:nth-child(2) { animation-delay: 50ms; }
.menu-item-enter:nth-child(3) { animation-delay: 100ms; }
.menu-item-enter:nth-child(4) { animation-delay: 150ms; }
.menu-item-enter:nth-child(5) { animation-delay: 200ms; }

/* ===== تحسينات الأداء ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* ===== تقليل الحركة للمستخدمين الذين يفضلون ذلك ===== */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeIn,
  .animate-fadeInUp,
  .animate-fadeInDown,
  .animate-fadeInLeft,
  .animate-fadeInRight,
  .animate-zoomIn,
  .animate-zoomInUp,
  .animate-slideInUp,
  .animate-slideInDown,
  .animate-slideInLeft,
  .animate-slideInRight,
  .animate-bounceIn,
  .animate-bounceInUp,
  .animate-rotateIn,
  .animate-rotateInUpLeft,
  .animate-wobble,
  .animate-jello {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .animate-pulse,
  .animate-heartBeat,
  .animate-breathe,
  .animate-flash,
  .animate-spin,
  .animate-spinSlow,
  .animate-spinReverse,
  .animate-gradientShift,
  .animate-shimmer,
  .animate-wave {
    animation: none;
  }
}
