import React, { useState, useMemo } from 'react';
import './CurrencyManagement.css';

// مكون إدارة العملات والأسعار
export const CurrencyManagement = ({ isLoading }) => {
  const [activeTab, setActiveTab] = useState('currencies');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showRateForm, setShowRateForm] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // بيانات تجريبية للعملات
  const currenciesData = [
    {
      id: 'SAR',
      code: 'SAR',
      name: 'الريال السعودي',
      nameEn: 'Saudi Riyal',
      symbol: 'ر.س',
      isBaseCurrency: true,
      isActive: true,
      decimalPlaces: 2,
      lastUpdated: '2024-02-10 14:30',
      currentRate: 1.0000
    },
    {
      id: 'USD',
      code: 'USD',
      name: 'الدولار الأمريكي',
      nameEn: 'US Dollar',
      symbol: '$',
      isBaseCurrency: false,
      isActive: true,
      decimalPlaces: 2,
      lastUpdated: '2024-02-10 14:30',
      currentRate: 3.7500
    },
    {
      id: 'EUR',
      code: 'EUR',
      name: 'اليورو',
      nameEn: 'Euro',
      symbol: '€',
      isBaseCurrency: false,
      isActive: true,
      decimalPlaces: 2,
      lastUpdated: '2024-02-10 14:30',
      currentRate: 4.0625
    },
    {
      id: 'GBP',
      code: 'GBP',
      name: 'الجنيه الإسترليني',
      nameEn: 'British Pound',
      symbol: '£',
      isBaseCurrency: false,
      isActive: true,
      decimalPlaces: 2,
      lastUpdated: '2024-02-10 14:30',
      currentRate: 4.7250
    },
    {
      id: 'AED',
      code: 'AED',
      name: 'الدرهم الإماراتي',
      nameEn: 'UAE Dirham',
      symbol: 'د.إ',
      isBaseCurrency: false,
      isActive: true,
      decimalPlaces: 2,
      lastUpdated: '2024-02-10 14:30',
      currentRate: 1.0208
    }
  ];

  // بيانات تجريبية لتاريخ أسعار الصرف
  const exchangeRatesHistory = [
    {
      id: 'RATE-001',
      currencyCode: 'USD',
      date: '2024-02-10',
      rate: 3.7500,
      previousRate: 3.7480,
      change: 0.0020,
      changePercent: 0.053,
      source: 'البنك المركزي السعودي',
      updatedBy: 'النظام التلقائي'
    },
    {
      id: 'RATE-002',
      currencyCode: 'EUR',
      date: '2024-02-10',
      rate: 4.0625,
      previousRate: 4.0580,
      change: 0.0045,
      changePercent: 0.111,
      source: 'البنك المركزي السعودي',
      updatedBy: 'النظام التلقائي'
    },
    {
      id: 'RATE-003',
      currencyCode: 'GBP',
      date: '2024-02-10',
      rate: 4.7250,
      previousRate: 4.7180,
      change: 0.0070,
      changePercent: 0.148,
      source: 'البنك المركزي السعودي',
      updatedBy: 'النظام التلقائي'
    },
    {
      id: 'RATE-004',
      currencyCode: 'USD',
      date: '2024-02-09',
      rate: 3.7480,
      previousRate: 3.7465,
      change: 0.0015,
      changePercent: 0.040,
      source: 'البنك المركزي السعودي',
      updatedBy: 'النظام التلقائي'
    }
  ];

  const filteredCurrencies = useMemo(() => {
    return currenciesData.filter(currency => 
      currency.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      currency.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      currency.nameEn.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]); // eslint-disable-line react-hooks/exhaustive-deps

  const formatRate = (rate) => {
    return new Intl.NumberFormat('ar-SA', {
      minimumFractionDigits: 4,
      maximumFractionDigits: 4
    }).format(rate);
  };

  const formatChange = (change) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(4)}`;
  };

  const formatChangePercent = (percent) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(3)}%`;
  };

  const getChangeColor = (change) => {
    return change >= 0 ? '#28a745' : '#dc3545';
  };

  const handleUpdateRate = (currencyCode) => {
    setSelectedCurrency(currencyCode);
    setShowRateForm(true);
  };

  const handleToggleCurrency = (currencyId) => {
    console.log('تغيير حالة العملة:', currencyId);
  };

  const handleDeleteCurrency = (currencyId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه العملة؟')) {
      console.log('حذف العملة:', currencyId);
    }
  };

  // مكون قائمة العملات
  const CurrenciesList = () => (
    <div className="currencies-section">
      <div className="section-header">
        <h4>إدارة العملات</h4>
        <div className="header-actions">
          <button 
            className="update-rates-btn"
            title="تحديث جميع الأسعار"
          >
            🔄 تحديث الأسعار
          </button>
          <button 
            className="add-currency-btn"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إضافة عملة
          </button>
        </div>
      </div>

      {/* فلتر البحث */}
      <div className="currencies-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في العملات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* جدول العملات */}
      <div className="currencies-table">
        <div className="table-header">
          <div>العملة</div>
          <div>الرمز</div>
          <div>السعر الحالي</div>
          <div>آخر تحديث</div>
          <div>الحالة</div>
          <div>إجراءات</div>
        </div>

        {filteredCurrencies.map(currency => (
          <div key={currency.id} className={`table-row ${currency.isActive ? 'active' : 'inactive'}`}>
            <div className="currency-info">
              <div className="currency-name">
                {currency.name}
                {currency.isBaseCurrency && (
                  <span className="base-badge">أساسية</span>
                )}
              </div>
              <div className="currency-name-en">{currency.nameEn}</div>
            </div>
            
            <div className="currency-code">
              <span className="code-badge">{currency.code}</span>
              <span className="currency-symbol">{currency.symbol}</span>
            </div>
            
            <div className="current-rate">
              {currency.isBaseCurrency ? (
                <span className="base-rate">1.0000</span>
              ) : (
                <span className="rate-value">{formatRate(currency.currentRate)}</span>
              )}
            </div>
            
            <div className="last-updated">
              {currency.lastUpdated}
            </div>
            
            <div className="currency-status">
              <span 
                className={`status-badge ${currency.isActive ? 'active' : 'inactive'}`}
              >
                {currency.isActive ? 'نشطة' : 'غير نشطة'}
              </span>
            </div>
            
            <div className="currency-actions">
              {!currency.isBaseCurrency && (
                <button 
                  className="action-btn update"
                  title="تحديث السعر"
                  onClick={() => handleUpdateRate(currency.code)}
                >
                  💱
                </button>
              )}
              <button 
                className="action-btn edit"
                title="تعديل"
              >
                ✏️
              </button>
              <button 
                className="action-btn toggle"
                title={currency.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                onClick={() => handleToggleCurrency(currency.id)}
              >
                {currency.isActive ? '🔒' : '🔓'}
              </button>
              {!currency.isBaseCurrency && (
                <button 
                  className="action-btn delete"
                  title="حذف"
                  onClick={() => handleDeleteCurrency(currency.id)}
                >
                  🗑️
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // مكون تاريخ أسعار الصرف
  const ExchangeRatesHistory = () => (
    <div className="rates-history-section">
      <div className="section-header">
        <h4>تاريخ أسعار الصرف</h4>
        <div className="header-actions">
          <select className="currency-filter">
            <option value="all">جميع العملات</option>
            <option value="USD">الدولار الأمريكي</option>
            <option value="EUR">اليورو</option>
            <option value="GBP">الجنيه الإسترليني</option>
            <option value="AED">الدرهم الإماراتي</option>
          </select>
          <input type="date" className="date-filter" />
        </div>
      </div>

      {/* جدول تاريخ الأسعار */}
      <div className="rates-history-table">
        <div className="table-header">
          <div>العملة</div>
          <div>التاريخ</div>
          <div>السعر</div>
          <div>السعر السابق</div>
          <div>التغيير</div>
          <div>النسبة</div>
          <div>المصدر</div>
        </div>

        {exchangeRatesHistory.map(rate => (
          <div key={rate.id} className="table-row">
            <div className="rate-currency">
              <span className="currency-code">{rate.currencyCode}</span>
            </div>
            
            <div className="rate-date">{rate.date}</div>
            
            <div className="rate-current">
              {formatRate(rate.rate)}
            </div>
            
            <div className="rate-previous">
              {formatRate(rate.previousRate)}
            </div>
            
            <div className="rate-change">
              <span style={{ color: getChangeColor(rate.change) }}>
                {formatChange(rate.change)}
              </span>
            </div>
            
            <div className="rate-change-percent">
              <span style={{ color: getChangeColor(rate.changePercent) }}>
                {formatChangePercent(rate.changePercent)}
              </span>
            </div>
            
            <div className="rate-source">
              <div className="source-name">{rate.source}</div>
              <div className="updated-by">{rate.updatedBy}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // مكون حاسبة العملات
  const CurrencyCalculator = () => {
    const [fromCurrency, setFromCurrency] = useState('SAR');
    const [toCurrency, setToCurrency] = useState('USD');
    const [amount, setAmount] = useState(1000);
    const [result, setResult] = useState(0);

    const calculateConversion = () => {
      const fromRate = currenciesData.find(c => c.code === fromCurrency)?.currentRate || 1;
      const toRate = currenciesData.find(c => c.code === toCurrency)?.currentRate || 1;
      
      // تحويل إلى العملة الأساسية أولاً ثم إلى العملة المطلوبة
      const baseAmount = amount / fromRate;
      const convertedAmount = baseAmount * toRate;
      
      setResult(convertedAmount);
    };

    React.useEffect(() => {
      calculateConversion();
    }, [amount, fromCurrency, toCurrency]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
      <div className="currency-calculator-section">
        <div className="section-header">
          <h4>حاسبة تحويل العملات</h4>
        </div>

        <div className="calculator-card">
          <div className="calculator-form">
            <div className="conversion-row">
              <div className="amount-input">
                <label>المبلغ</label>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                />
              </div>
              
              <div className="currency-select">
                <label>من</label>
                <select
                  value={fromCurrency}
                  onChange={(e) => setFromCurrency(e.target.value)}
                >
                  {currenciesData.filter(c => c.isActive).map(currency => (
                    <option key={currency.code} value={currency.code}>
                      {currency.code} - {currency.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="swap-button">
                <button 
                  onClick={() => {
                    setFromCurrency(toCurrency);
                    setToCurrency(fromCurrency);
                  }}
                  title="تبديل العملات"
                >
                  ⇄
                </button>
              </div>
              
              <div className="currency-select">
                <label>إلى</label>
                <select
                  value={toCurrency}
                  onChange={(e) => setToCurrency(e.target.value)}
                >
                  {currenciesData.filter(c => c.isActive).map(currency => (
                    <option key={currency.code} value={currency.code}>
                      {currency.code} - {currency.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="result-display">
              <div className="result-amount">
                <span className="amount-value">
                  {new Intl.NumberFormat('ar-SA', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }).format(result)}
                </span>
                <span className="currency-symbol">
                  {currenciesData.find(c => c.code === toCurrency)?.symbol}
                </span>
              </div>
              
              <div className="exchange-rate-info">
                <span>
                  1 {fromCurrency} = {formatRate(
                    (currenciesData.find(c => c.code === toCurrency)?.currentRate || 1) /
                    (currenciesData.find(c => c.code === fromCurrency)?.currentRate || 1)
                  )} {toCurrency}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل إدارة العملات...</p>
      </div>
    );
  }

  return (
    <div className="currency-management">
      <div className="management-header">
        <h3>إدارة العملات والأسعار</h3>
      </div>

      {/* تبويبات إدارة العملات */}
      <div className="management-tabs">
        <button 
          className={`tab-btn ${activeTab === 'currencies' ? 'active' : ''}`}
          onClick={() => setActiveTab('currencies')}
        >
          💰 العملات
        </button>
        <button 
          className={`tab-btn ${activeTab === 'rates' ? 'active' : ''}`}
          onClick={() => setActiveTab('rates')}
        >
          📈 تاريخ الأسعار
        </button>
        <button 
          className={`tab-btn ${activeTab === 'calculator' ? 'active' : ''}`}
          onClick={() => setActiveTab('calculator')}
        >
          🧮 حاسبة العملات
        </button>
      </div>

      {/* محتوى الإدارة */}
      <div className="management-content">
        {activeTab === 'currencies' && <CurrenciesList />}
        {activeTab === 'rates' && <ExchangeRatesHistory />}
        {activeTab === 'calculator' && <CurrencyCalculator />}
      </div>

      {/* نموذج إضافة عملة جديدة */}
      {showAddForm && (
        <div className="currency-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>إضافة عملة جديدة</h4>
              <button 
                className="close-btn"
                onClick={() => setShowAddForm(false)}
              >
                ❌
              </button>
            </div>
            
            <div className="modal-body">
              <div className="currency-form">
                <div className="form-group">
                  <label>رمز العملة</label>
                  <input type="text" placeholder="مثل: USD" maxLength="3" />
                </div>

                <div className="form-group">
                  <label>اسم العملة (عربي)</label>
                  <input type="text" placeholder="مثل: الدولار الأمريكي" />
                </div>

                <div className="form-group">
                  <label>اسم العملة (إنجليزي)</label>
                  <input type="text" placeholder="مثل: US Dollar" />
                </div>

                <div className="form-group">
                  <label>رمز العملة</label>
                  <input type="text" placeholder="مثل: $" />
                </div>

                <div className="form-group">
                  <label>عدد المنازل العشرية</label>
                  <select>
                    <option value="0">0</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>السعر الحالي (مقابل الريال السعودي)</label>
                  <input type="number" step="0.0001" placeholder="0.0000" />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="save-btn">💾 حفظ العملة</button>
              <button 
                className="cancel-btn"
                onClick={() => setShowAddForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تحديث سعر الصرف */}
      {showRateForm && (
        <div className="currency-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>تحديث سعر الصرف - {selectedCurrency}</h4>
              <button 
                className="close-btn"
                onClick={() => setShowRateForm(false)}
              >
                ❌
              </button>
            </div>
            
            <div className="modal-body">
              <div className="rate-form">
                <div className="current-rate-display">
                  <label>السعر الحالي:</label>
                  <span className="current-rate-value">
                    {formatRate(currenciesData.find(c => c.code === selectedCurrency)?.currentRate || 0)}
                  </span>
                </div>

                <div className="form-group">
                  <label>السعر الجديد</label>
                  <input 
                    type="number" 
                    step="0.0001" 
                    placeholder="0.0000"
                    defaultValue={currenciesData.find(c => c.code === selectedCurrency)?.currentRate || 0}
                  />
                </div>

                <div className="form-group">
                  <label>مصدر السعر</label>
                  <select>
                    <option value="central_bank">البنك المركزي السعودي</option>
                    <option value="manual">تحديث يدوي</option>
                    <option value="api">مصدر خارجي</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>ملاحظات</label>
                  <textarea placeholder="أدخل أي ملاحظات حول التحديث"></textarea>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="save-btn">💾 تحديث السعر</button>
              <button 
                className="cancel-btn"
                onClick={() => setShowRateForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrencyManagement;