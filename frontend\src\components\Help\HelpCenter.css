/* Help Center Styles */

.help-center {
  padding: var(--space-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
  min-height: calc(100vh - var(--navbar-height));
}

/* ===== HELP HEADER ===== */
.help-header {
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.help-header-content h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-3) 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.help-header-content p {
  font-size: var(--text-lg);
  color: var(--neutral-600);
  margin: 0 0 var(--space-6) 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== SEARCH ===== */
.help-search {
  max-width: 500px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--space-4);
  font-size: var(--text-lg);
  color: var(--neutral-500);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--space-4) var(--space-4) var(--space-4) var(--space-12);
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-xl);
  font-size: var(--text-base);
  color: var(--neutral-800);
  background: var(--neutral-0);
  transition: all var(--transition-fast);
  font-family: var(--font-family-arabic);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 4px var(--primary-100);
}

.search-input::placeholder {
  color: var(--neutral-500);
}

/* ===== TABS ===== */
.help-tabs {
  margin-bottom: var(--space-8);
  padding: 0 var(--space-4);
}

.tabs-container {
  justify-content: center;
  flex-wrap: wrap;
}

.help-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: 2px solid var(--neutral-300);
  background: var(--neutral-0);
  color: var(--neutral-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  font-family: var(--font-family-arabic);
}

.help-tab:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-1px);
}

.help-tab.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: var(--neutral-0);
  box-shadow: var(--shadow-lg);
}

.tab-icon {
  font-size: var(--text-base);
}

/* ===== CONTENT SECTIONS ===== */
.help-content {
  animation: fadeIn 0.3s ease-out;
}

/* ===== FAQ SECTION ===== */
.faq-section {
  /* Grid handled by ModernGrid */
}

.categories-sidebar {
  /* Sidebar styling */
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.category-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border: 1px solid var(--neutral-200);
  background: var(--neutral-0);
  color: var(--neutral-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: right;
  width: 100%;
  font-family: var(--font-family-arabic);
}

.category-item:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateX(-2px);
}

.category-item.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.category-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.category-name {
  flex: 1;
}

/* FAQ Items */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.faq-item {
  transition: all var(--transition-fast);
}

.faq-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.faq-question h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  margin: 0 0 var(--space-3) 0;
  line-height: var(--leading-tight);
}

.faq-answer p {
  font-size: var(--text-base);
  color: var(--neutral-700);
  line-height: var(--leading-relaxed);
  margin: 0;
}

/* No Results */
.no-results {
  text-align: center;
  padding: var(--space-12);
  color: var(--neutral-600);
}

.no-results-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
}

.no-results h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin: 0 0 var(--space-2) 0;
}

.no-results p {
  font-size: var(--text-base);
  margin: 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== GUIDES SECTION ===== */
.guides-section {
  /* Grid handled by ModernGrid */
}

.guide-card {
  transition: all var(--transition-fast);
}

.guide-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.guide-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.guide-icon {
  font-size: var(--text-3xl);
  flex-shrink: 0;
}

.guide-info h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-2) 0;
}

.guide-info p {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
  margin: 0;
}

.guide-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--neutral-200);
}

.guide-duration {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.guide-level {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  text-transform: uppercase;
}

.guide-level--مبتدئ {
  background: var(--success-100);
  color: var(--success-700);
}

.guide-level--متوسط {
  background: var(--warning-100);
  color: var(--warning-700);
}

.guide-level--متقدم {
  background: var(--error-100);
  color: var(--error-700);
}

.guide-actions {
  /* Button styling handled by ModernButton */
}

/* ===== CONTACT SECTION ===== */
.contact-section {
  /* Grid handled by ModernGrid */
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
}

.form-input {
  padding: var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--neutral-800);
  background: var(--neutral-0);
  transition: all var(--transition-fast);
  font-family: var(--font-family-arabic);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.form-input:hover {
  border-color: var(--primary-400);
}

textarea.form-input {
  resize: vertical;
  min-height: 120px;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.contact-method:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-1px);
}

.contact-icon {
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.contact-details h4 {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-1) 0;
}

.contact-details p {
  font-size: var(--text-sm);
  color: var(--primary-600);
  font-weight: var(--font-medium);
  margin: 0 0 var(--space-1) 0;
}

.contact-details small {
  font-size: var(--text-xs);
  color: var(--neutral-600);
}

/* Emergency Card */
.emergency-card {
  border: 2px solid var(--error-300);
  background: var(--error-50);
}

.emergency-info {
  text-align: center;
}

.emergency-icon {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-3);
}

.emergency-info h4 {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--error-700);
  margin: 0 0 var(--space-2) 0;
}

.emergency-info p {
  font-size: var(--text-sm);
  color: var(--error-600);
  margin: 0 0 var(--space-3) 0;
}

.emergency-number {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--error-700);
  background: var(--error-100);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2);
  border: 1px solid var(--error-300);
}

.emergency-info small {
  font-size: var(--text-xs);
  color: var(--error-600);
}

/* ===== SHORTCUTS SECTION ===== */
.shortcuts-section {
  /* Grid handled by ModernGrid */
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.shortcut-item:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateX(-2px);
}

.shortcut-keys {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

.shortcut-keys kbd {
  background: var(--neutral-200);
  color: var(--neutral-800);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  font-family: monospace;
  border: 1px solid var(--neutral-300);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-description {
  font-size: var(--text-sm);
  color: var(--neutral-700);
  font-weight: var(--font-medium);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .faq-section .categories-sidebar {
    grid-column: span 4;
    margin-bottom: var(--space-6);
  }
  
  .faq-section .faq-list {
    grid-column: span 4;
  }
  
  .categories-list {
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--space-2);
  }
  
  .category-item {
    flex: 0 0 auto;
  }
}

@media (max-width: 768px) {
  .help-center {
    padding: var(--space-4);
  }
  
  .help-header {
    padding: var(--space-6);
    margin-bottom: var(--space-6);
  }
  
  .help-header-content h1 {
    font-size: var(--text-3xl);
  }
  
  .help-header-content p {
    font-size: var(--text-base);
  }
  
  .help-tabs {
    margin-bottom: var(--space-6);
    padding: 0;
  }
  
  .tabs-container {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }
  
  .help-tab {
    flex-shrink: 0;
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }
  
  .contact-section,
  .guides-section,
  .shortcuts-section {
    grid-template-columns: 1fr;
  }
  
  .guide-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .guide-meta {
    flex-direction: column;
    gap: var(--space-2);
    align-items: center;
  }
  
  .contact-method {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .help-center {
    padding: var(--space-3);
  }
  
  .help-header {
    padding: var(--space-4);
  }
  
  .help-header-content h1 {
    font-size: var(--text-2xl);
  }
  
  .search-input {
    padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
  }
  
  .help-tab {
    padding: var(--space-2) var(--space-3);
  }
  
  .faq-question h3 {
    font-size: var(--text-base);
  }
  
  .guide-icon {
    font-size: var(--text-2xl);
  }
  
  .emergency-number {
    font-size: var(--text-lg);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .help-header-content h1 {
    color: var(--neutral-200);
  }
  
  .help-header-content p {
    color: var(--neutral-400);
  }
  
  .search-input {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .search-input:focus {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 4px var(--primary-800);
  }
  
  .search-icon {
    color: var(--neutral-400);
  }
  
  .help-tab {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-300);
  }
  
  .help-tab:hover {
    background: var(--primary-800);
    border-color: var(--primary-600);
    color: var(--primary-300);
  }
  
  .help-tab.active {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: var(--neutral-0);
  }
  
  .category-item {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-300);
  }
  
  .category-item:hover {
    background: var(--primary-800);
    border-color: var(--primary-600);
    color: var(--primary-300);
  }
  
  .category-item.active {
    background: var(--primary-600);
    border-color: var(--primary-600);
  }
  
  .faq-question h3 {
    color: var(--primary-400);
  }
  
  .faq-answer p {
    color: var(--neutral-300);
  }
  
  .guide-info h3 {
    color: var(--neutral-200);
  }
  
  .guide-info p {
    color: var(--neutral-400);
  }
  
  .form-input {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .form-input:focus {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px var(--primary-800);
  }
  
  .form-group label {
    color: var(--neutral-300);
  }
  
  .contact-method {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .contact-method:hover {
    background: var(--primary-800);
    border-color: var(--primary-600);
  }
  
  .contact-details h4 {
    color: var(--neutral-200);
  }
  
  .contact-details p {
    color: var(--primary-400);
  }
  
  .contact-details small {
    color: var(--neutral-400);
  }
  
  .shortcut-item {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .shortcut-item:hover {
    background: var(--primary-800);
    border-color: var(--primary-600);
  }
  
  .shortcut-keys {
    color: var(--neutral-300);
  }
  
  .shortcut-keys kbd {
    background: var(--neutral-600);
    color: var(--neutral-200);
    border-color: var(--neutral-500);
  }
  
  .shortcut-description {
    color: var(--neutral-300);
  }
}