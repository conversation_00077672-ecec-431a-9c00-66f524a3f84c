/* Accounts Components Styles */

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Chart of Accounts */
.chart-of-accounts {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.accounts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.accounts-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.add-account-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
}

.add-account-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.accounts-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 250px;
}

.search-box input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.category-filter select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

/* Add Account Form */
.add-account-form {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  border: 2px solid #e1e8ed;
}

.add-account-form h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.save-btn,
.cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

/* Accounts Tree */
.accounts-tree {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.account-node {
  border-bottom: 1px solid #f1f3f4;
}

.account-node:last-child {
  border-bottom: none;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.account-item:hover {
  background: #f8f9fa;
}

.account-item.header {
  background: #667eea;
  color: white;
  font-weight: 600;
}

.account-item.header:hover {
  background: #5a67d8;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: inherit;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.expand-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.account-icon {
  font-size: 18px;
}

.account-details {
  flex: 1;
}

.account-name {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 4px;
}

.account-code {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.account-item.header .account-code {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.account-title {
  font-size: 14px;
  font-weight: 500;
}

.account-balance {
  font-size: 13px;
  color: #6c757d;
  font-weight: 600;
}

.account-item.header .account-balance {
  color: rgba(255, 255, 255, 0.8);
}

.account-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.account-item:hover .account-actions {
  opacity: 1;
}

.edit-btn,
.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.edit-btn:hover {
  background: rgba(255, 193, 7, 0.2);
}

.delete-btn:hover {
  background: rgba(220, 53, 69, 0.2);
}

.account-children {
  background: #f8f9fa;
}

/* Journal Entries */
.journal-entries {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.journal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.journal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.add-entry-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.add-entry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.journal-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.status-filter select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

/* Add Transaction Form */
.add-transaction-form {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  border: 2px solid #e1e8ed;
}

.add-transaction-form h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.transaction-header-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.entries-section {
  margin-bottom: 20px;
}

.entries-section h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.entries-table {
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 15px;
  padding: 15px;
  background: #667eea;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.entry-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.entry-row:last-child {
  border-bottom: none;
}

.account-select select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 13px;
}

.amount-input input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 13px;
  text-align: right;
}

.entry-actions {
  display: flex;
  justify-content: center;
}

.remove-entry-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.remove-entry-btn:hover:not(:disabled) {
  background: #c82333;
}

.remove-entry-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.entries-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.add-entry-line-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.3s ease;
}

.add-entry-line-btn:hover {
  background: #0056b3;
}

.totals {
  display: flex;
  align-items: center;
}

.total-item {
  display: flex;
  gap: 20px;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
}

.total-item.balanced {
  color: #28a745;
}

.total-item.unbalanced {
  color: #dc3545;
}

.balance-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.total-item.balanced .balance-status {
  background: #d4edda;
  color: #155724;
}

.total-item.unbalanced .balance-status {
  background: #f8d7da;
  color: #721c24;
}

/* Transaction Cards */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.transaction-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.transaction-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.transaction-card.posted {
  border-color: #28a745;
}

.transaction-card.draft {
  border-color: #ffc107;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.transaction-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.transaction-info p {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
}

.transaction-meta {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  font-size: 12px;
  color: #6c757d;
}

.transaction-meta .status {
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
}

.transaction-meta .status.posted {
  background: #d4edda;
  color: #155724;
}

.transaction-meta .status.draft {
  background: #fff3cd;
  color: #856404;
}

.transaction-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.edit-btn,
.post-btn,
.delete-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.edit-btn {
  background: #ffc107;
  color: #212529;
}

.edit-btn:hover {
  background: #e0a800;
}

.post-btn {
  background: #28a745;
  color: white;
}

.post-btn:hover {
  background: #218838;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

.transaction-entries {
  padding: 20px;
}

.transaction-entries .entries-table {
  border: 1px solid #e1e8ed;
}

.transaction-entries .table-header {
  background: #f8f9fa;
  color: #495057;
  grid-template-columns: 2fr 1fr 1fr;
}

.transaction-entries .entry-row {
  grid-template-columns: 2fr 1fr 1fr;
}

.account-name {
  font-size: 13px;
  color: #495057;
}

.debit-amount,
.credit-amount {
  font-size: 13px;
  font-weight: 600;
  text-align: right;
  color: #2c3e50;
}

.totals-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #667eea;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.total-label {
  font-weight: 700;
}

.total-debit,
.total-credit {
  text-align: right;
  font-weight: 700;
}

/* Trial Balance */
.trial-balance {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.trial-balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.trial-balance-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.export-btn,
.details-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.export-btn {
  background: #28a745;
  color: white;
}

.export-btn:hover {
  background: #218838;
}

.details-btn {
  background: #007bff;
  color: white;
}

.details-btn:hover {
  background: #0056b3;
}

.period-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.period-info h4 {
  margin: 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.trial-balance-filters {
  margin-bottom: 25px;
}

.trial-balance-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.trial-balance-table .table-header {
  display: grid;
  grid-template-columns: auto 2fr repeat(5, 1fr);
  gap: 15px;
  padding: 15px;
  background: #667eea;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.trial-balance-table .table-row {
  display: grid;
  grid-template-columns: auto 2fr repeat(5, 1fr);
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.trial-balance-table .table-row:last-child {
  border-bottom: none;
}

.trial-balance-table .table-row:nth-child(even) {
  background: #f8f9fa;
}

.trial-balance-table .table-footer {
  display: grid;
  grid-template-columns: auto 2fr repeat(5, 1fr);
  gap: 15px;
  padding: 15px;
  background: #2c3e50;
  color: white;
  font-weight: 700;
  font-size: 14px;
}

.account-code {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
}

.account-name {
  font-size: 14px;
  color: #2c3e50;
}

.opening-balance,
.debit-movements,
.credit-movements,
.debit-balance,
.credit-balance {
  text-align: right;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.total-label {
  font-weight: 700;
  color: white;
}

.total-debit-movements,
.total-credit-movements,
.total-debit-balance,
.total-credit-balance {
  text-align: right;
  font-weight: 700;
  color: white;
}

.balance-verification {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.verification-status,
.movements-status {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

.verification-status.balanced,
.movements-status.balanced {
  background: #d4edda;
  color: #155724;
  border: 2px solid #c3e6cb;
}

.verification-status.unbalanced,
.movements-status.unbalanced {
  background: #f8d7da;
  color: #721c24;
  border: 2px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .transaction-header-form {
    grid-template-columns: 1fr;
  }
  
  .trial-balance-table .table-header,
  .trial-balance-table .table-row,
  .trial-balance-table .table-footer {
    grid-template-columns: auto 1fr repeat(3, auto);
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .accounts-header,
  .journal-header,
  .trial-balance-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .accounts-filters,
  .journal-filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .transaction-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .transaction-actions {
    justify-content: center;
  }
  
  .table-header,
  .entry-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .table-header > div,
  .entry-row > div {
    padding: 8px;
    border-bottom: 1px solid #e1e8ed;
  }
  
  .table-header > div:before,
  .entry-row > div:before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: #495057;
  }
  
  .balance-verification {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .chart-of-accounts,
  .journal-entries,
  .trial-balance {
    padding: 15px;
  }
  
  .add-account-form,
  .add-transaction-form {
    padding: 20px;
  }
  
  .account-item {
    padding: 12px 15px;
  }
  
  .transaction-card {
    margin-bottom: 15px;
  }
  
  .transaction-header,
  .transaction-entries {
    padding: 15px;
  }
  
  .entries-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .total-item {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

/* Accounts Receivable & Payable Styles */
.receivables-summary,
.payables-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.summary-card.alert {
  border-left: 4px solid #dc3545;
}

.summary-card.urgent {
  border-left: 4px solid #ff6b6b;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.summary-icon {
  font-size: 32px;
  opacity: 0.8;
}

.summary-content {
  flex: 1;
}

.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.receivables-filters,
.payables-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  align-items: center;
  flex-wrap: wrap;
}

.quick-filters {
  display: flex;
  gap: 10px;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: #f8f9fa;
  border-color: #ced4da;
}

.filter-btn.urgent {
  border-color: #dc3545;
  color: #dc3545;
}

.filter-btn.urgent:hover {
  background: #dc3545;
  color: white;
}

.filter-btn.overdue {
  border-color: #fd7e14;
  color: #fd7e14;
}

.filter-btn.overdue:hover {
  background: #fd7e14;
  color: white;
}

.filter-btn.due-soon {
  border-color: #ffc107;
  color: #856404;
}

.filter-btn.due-soon:hover {
  background: #ffc107;
  color: #212529;
}

.receivables-table,
.payables-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.customer-info,
.supplier-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.customer-name,
.supplier-name {
  font-weight: 600;
  color: #2c3e50;
}

.customer-contact,
.supplier-contact {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 11px;
  color: #6c757d;
}

.overdue-days {
  color: #dc3545;
  font-weight: 600;
  font-size: 11px;
  display: block;
  margin-top: 2px;
}

.due-soon {
  color: #ffc107;
  font-weight: 600;
  font-size: 11px;
  display: block;
  margin-top: 2px;
}

.status-badge,
.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-align: center;
  display: inline-block;
  min-width: 60px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.action-btn.view:hover {
  background: rgba(23, 162, 184, 0.2);
}

.action-btn.edit:hover {
  background: rgba(255, 193, 7, 0.2);
}

.action-btn.payment:hover {
  background: rgba(40, 167, 69, 0.2);
}

.action-btn.reminder:hover {
  background: rgba(102, 126, 234, 0.2);
}

.action-btn.schedule:hover {
  background: rgba(108, 117, 125, 0.2);
}

/* Payment Modal */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
}

.modal-header h4 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
}

.modal-body {
  padding: 20px;
}

.bill-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
  font-weight: 600;
  color: #2c3e50;
}

.payment-form {
  display: grid;
  gap: 15px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px;
  border-top: 1px solid #e1e8ed;
}

/* Bank Reconciliation Styles */
.bank-reconciliation {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reconciliation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.reconciliation-settings {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 25px;
}

.settings-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.reconciliation-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.status-indicator.reconciled {
  background: #d4edda;
  color: #155724;
}

.status-indicator.unreconciled {
  background: #f8d7da;
  color: #721c24;
}

.reconciliation-summary {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f1f3f4;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item.total {
  font-weight: 700;
  border-top: 2px solid #2c3e50;
  margin-top: 10px;
  padding-top: 15px;
}

.summary-item.difference {
  font-size: 18px;
  font-weight: 700;
}

.summary-item.difference.zero .value {
  color: #28a745;
}

.summary-item.difference.non-zero .value {
  color: #dc3545;
}

.summary-item .label {
  color: #495057;
}

.summary-item .value.positive {
  color: #28a745;
}

.summary-item .value.negative {
  color: #dc3545;
}

.reconciliation-tables {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin-bottom: 25px;
}

.table-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.reconciliation-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.reconciliation-table .table-row.matched {
  background: #d4edda;
}

.reconciliation-table .table-row.unmatched {
  background: #f8d7da;
}

.matched-badge {
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.unmatched-badge {
  background: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.match-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
}

.view-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 5px;
}

.unmatched-items {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.unmatched-section {
  margin-bottom: 20px;
}

.unmatched-section:last-child {
  margin-bottom: 0;
}

.unmatched-section h5 {
  margin: 0 0 15px 0;
  color: #856404;
  font-size: 16px;
  font-weight: 600;
}

.unmatched-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.unmatched-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.unmatched-item.bank-item {
  border-left: 4px solid #007bff;
}

.unmatched-item.book-item {
  border-left: 4px solid #28a745;
}

.unmatched-item .date {
  font-size: 12px;
  color: #6c757d;
  min-width: 80px;
}

.unmatched-item .description {
  flex: 1;
  margin: 0 15px;
  font-weight: 500;
}

.unmatched-item .amount {
  font-weight: 600;
  min-width: 100px;
  text-align: right;
}

.action-btn.small {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  background: #667eea;
  color: white;
  transition: background-color 0.3s ease;
}

.action-btn.small:hover {
  background: #5a67d8;
}

/* Upload Modal */
.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.upload-area {
  border: 2px dashed #ced4da;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  background: #f8f9fa;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.6;
}

.upload-area p {
  margin: 0 0 15px 0;
  color: #6c757d;
}

.upload-area input[type="file"] {
  margin-top: 10px;
}

.upload-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.upload-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
}

/* Tax Reports Styles */
.tax-reports {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tax-reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.tax-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 25px;
  background: #f8f9fa;
  padding: 5px;
  border-radius: 12px;
}

.tax-tabs .tab-btn {
  flex: 1;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
}

.tax-tabs .tab-btn.active {
  background: white;
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.period-settings {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  align-items: end;
}

.vat-summary,
.zakat-summary {
  margin-bottom: 30px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.summary-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.summary-card.output {
  border-color: #dc3545;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.summary-card.input {
  border-color: #28a745;
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
}

.summary-card.net.payable {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fffbf0 0%, #fef5e7 100%);
}

.summary-card.net.refundable {
  border-color: #17a2b8;
  background: linear-gradient(135deg, #f0fdff 0%, #c6f7ff 100%);
}

.summary-card.assets {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6edff 100%);
}

.summary-card.nisab {
  border-color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.summary-card.zakat.liable {
  border-color: #28a745;
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
}

.summary-card.zakat.not-liable {
  border-color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h5 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.card-header .icon {
  font-size: 24px;
  opacity: 0.7;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.card-description {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.vat-details,
.zakat-assets {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 25px;
}

.vat-details h5,
.zakat-assets h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.vat-breakdown,
.assets-breakdown {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-item.total {
  font-weight: 700;
  border-top: 2px solid #2c3e50;
  margin-top: 10px;
  padding-top: 15px;
  font-size: 16px;
}

.vat-transactions {
  margin-bottom: 25px;
}

.vat-transactions h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.transactions-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.transactions-table .table-row.output_vat {
  background: #fff5f5;
}

.transactions-table .table-row.input_vat {
  background: #f0fff4;
}

.transactions-table .table-row.exempt {
  background: #f8f9fa;
}

.zakat-calculation {
  background: #f0f4ff;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #667eea;
}

.zakat-calculation h5 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.calculation-steps {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-description {
  flex: 1;
  font-weight: 500;
  color: #495057;
}

.step-value {
  font-weight: 700;
  color: #2c3e50;
  min-width: 120px;
  text-align: right;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.coming-soon .icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.coming-soon h5 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 20px;
  font-weight: 600;
}

.coming-soon p {
  margin: 0;
  font-size: 14px;
}

/* Print Styles */
@media print {
  .accounts-header,
  .journal-header,
  .trial-balance-header,
  .receivables-header,
  .payables-header,
  .reconciliation-header,
  .tax-reports-header {
    border-bottom: 2px solid #000;
    margin-bottom: 20px;
  }
  
  .add-account-btn,
  .add-entry-btn,
  .export-btn,
  .details-btn,
  .account-actions,
  .transaction-actions,
  .add-account-form,
  .add-transaction-form,
  .header-actions,
  .quick-filters,
  .action-btn,
  .payment-modal,
  .upload-modal {
    display: none !important;
  }
  
  .chart-of-accounts,
  .journal-entries,
  .trial-balance,
  .accounts-receivable,
  .accounts-payable,
  .bank-reconciliation,
  .tax-reports {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .transaction-card,
  .summary-card {
    border: 1px solid #000;
    margin-bottom: 20px;
    break-inside: avoid;
  }
  
  .trial-balance-table,
  .receivables-table,
  .payables-table,
  .reconciliation-table,
  .transactions-table {
    border: 2px solid #000;
  }
  
  .table-header,
  .table-footer {
    background: #f0f0f0 !important;
    color: #000 !important;
  }
}