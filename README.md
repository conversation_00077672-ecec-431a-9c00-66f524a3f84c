# 🧳 نظام محاسبي متكامل لوكالات السفريات

<div align="center">

![النظام](https://img.shields.io/badge/النظام-جاهز%20للاستخدام-brightgreen?style=for-the-badge)
![Frontend](https://img.shields.io/badge/Frontend-React-61DAFB?style=for-the-badge&logo=react)
![Backend](https://img.shields.io/badge/Backend-FastAPI-009688?style=for-the-badge&logo=fastapi)
![Database](https://img.shields.io/badge/Database-SQLite-003B57?style=for-the-badge&logo=sqlite)

**نظام محاسبي شامل ومتطور مصمم خصيصاً لوكالات السفريات**

</div>

---

## 🎉 النظام مكتمل وجاهز للاستخدام!

### ✨ المميزات الرئيسية

- 🏠 **صفحة رئيسية تفاعلية** مع تصميم عصري وتدرجات خرافية
- 🔐 **نظام تسجيل دخول آمن** مع واجهة أنيقة
- 📊 **لوحة تحكم ذكية** مع إحصائيات مباشرة ورسوم بيانية
- ✈️ **إدارة الحجوزات** مع إمكانيات البحث والتصفية
- 👥 **إدارة العملاء** (قيد التطوير)
- 🏢 **إدارة الموردين** (قيد التطوير)
- 💰 **نظام محاسبي** (قيد التطوير)
- 📈 **تقارير مفصلة** (قيد التطوير)
- 🌐 **واجهات عربية RTL** مع دعم كامل للغة العربية
- 📱 **تصميم متجاوب** يعمل على جميع الأجهزة

---

## 🚀 التشغيل السريع

### الطريقة الأسهل - ملف التشغيل التلقائي:

```batch
# انقر مرتين على هذا الملف
START_SYSTEM.bat
```

### أو استخدم الطريقة اليدوية:

**1. تشغيل Backend:**
```bash
cd backend
venv\Scripts\activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**2. تشغيل Frontend:**
```bash
cd frontend
npm start
```

---

## 🌐 الروابط والوصول

| 🎯 الخدمة | 🔗 الرابط | 📝 الوصف |
|-----------|-----------|----------|
| 🏠 **الواجهة الرئيسية** | [http://localhost:3000](http://localhost:3000) | الصفحة الرئيسية والتطبيق |
| 🔐 **تسجيل الدخول** | [http://localhost:3000/login](http://localhost:3000/login) | صفحة تسجيل الدخول |
| 📊 **لوحة التحكم** | [http://localhost:3000/dashboard](http://localhost:3000/dashboard) | لوحة التحكم الرئيسية |
| ✈️ **الحجوزات** | [http://localhost:3000/bookings](http://localhost:3000/bookings) | إدارة الحجوزات |
| 💰 **المبيعات** | [http://localhost:3000/sales](http://localhost:3000/sales) | إدارة المبيعات |
| 🛒 **المشتريات** | [http://localhost:3000/purchases](http://localhost:3000/purchases) | إدارة المشتريات |
| 📦 **المخزون** | [http://localhost:3000/inventory](http://localhost:3000/inventory) | إدارة المخزون |
| 💼 **المالية** | [http://localhost:3000/finance](http://localhost:3000/finance) | النظام المالي |
| 📄 **القوالب** | [http://localhost:3000/templates](http://localhost:3000/templates) | إدارة القوالب |
| 👥 **العملاء** | [http://localhost:3000/customers](http://localhost:3000/customers) | إدارة العملاء |
| 🖥️ **خادم API** | [http://localhost:8000](http://localhost:8000) | خادم FastAPI |
| 📚 **توثيق API** | [http://localhost:8000/docs](http://localhost:8000/docs) | واجهة Swagger التفاعلية |
| 📖 **توثيق مفصل** | [http://localhost:8000/redoc](http://localhost:8000/redoc) | توثيق ReDoc |

---

## 🔑 بيانات الدخول

```
👤 اسم المستخدم: admin
🔒 كلمة المرور: admin123
```

---

## 🛠️ التقنيات المستخدمة

### Frontend (الواجهة الأمامية)
- **React 18** - مكتبة JavaScript الحديثة
- **React Router 6** - للتنقل بين الصفحات
- **CSS3** - تصميم متقدم مع تدرجات وتأثيرات
- **Responsive Design** - متوافق مع جميع الأجهزة
- **RTL Support** - دعم كامل للغة العربية

### Backend (الخادم الخلفي)
- **FastAPI** - إطار عمل Python سريع وحديث
- **SQLAlchemy** - ORM متقدم لقواعد البيانات
- **SQLite** - قاعدة بيانات خفيفة ومدمجة
- **Pydantic** - للتحقق من صحة البيانات
- **Uvicorn** - خادم ASGI عالي الأداء

---

## 📁 هيكل المشروع

```
sharaubtravelsoft/
├── 📁 backend/                    # الخادم الخلفي
│   ├── 📁 app/                   # كود التطبيق
│   │   ├── 📄 main.py           # نقطة البداية
│   │   ├── 📁 models/           # نماذج قاعدة البيانات
│   │   ├── 📁 routes/           # مسارات API
│   │   ├── 📁 schemas/          # مخططات البيانات
│   │   └── 📁 database/         # إعدادات قاعدة البيانات
│   ├── 📁 venv/                 # البيئة الافتراضية
│   ├── 📄 requirements.txt      # متطلبات Python
│   └── 📄 .env                  # متغيرات البيئة
├── 📁 frontend/                   # الواجهة الأمامية
│   ├── 📁 src/                  # كود React
│   │   ├── 📁 components/       # المكونات المشتركة
│   │   ├── 📁 pages/           # صفحات التطبيق
│   │   ├── 📄 App.js           # المكون الرئيسي
│   │   └── 📄 index.js         # نقطة البداية
│   ├── 📁 public/              # الملفات العامة
│   └── 📄 package.json         # متطلبات Node.js
├── 📄 START_SYSTEM.bat          # ملف التشغيل الرئيسي
├── 📄 run_system.bat           # ملف تشغيل بديل
├── 📄 debug.html               # أدوات التشخيص
├── 📄 test_frontend.html       # اختبار Frontend
└── 📄 README.md                # هذا الملف
```

---

## 🎯 الصفحات والمميزات المتاحة

### ✅ مكتملة وجاهزة:

#### 🏠 الصفحة الرئيسية
- تصميم عصري مع تدرجات خرافية
- عرض مميزات النظام
- أزرار تفاعلية للتنقل
- تصميم متجاوب

#### 🔐 صفحة تسجيل الدخول
- واجهة أنيقة وبسيطة
- نظام مصادقة آمن
- رسائل خطأ واضحة
- إرشادات للمستخدم

#### 📊 لوحة التحكم
- إحصائيات مباشرة ومتحركة
- بطاقات تفاعلية للبيانات
- جدول الحجوزات الأخيرة
- أزرار الإجراءات السريعة

#### ✈️ إدارة الحجوزات
- عرض جميع الحجوزات في جدول منظم
- إضافة حجوزات جديدة
- البحث والتصفية المتقدمة
- تغيير حالة الحجوزات
- حذف وتعديل الحجوزات

#### 💰 إدارة المبيعات
- تتبع جميع المبيعات والعمولات
- إحصائيات مفصلة للمبيعات
- تصنيف حسب نوع الخدمة
- تقارير المبيعات اليومية والشهرية
- إدارة طرق الدفع المختلفة

#### 🛒 إدارة المشتريات
- تتبع المشتريات من الموردين
- إدارة أوامر الشراء
- متابعة حالات الدفع
- تقارير التكاليف والمصروفات
- إدارة العلاقات مع الموردين

#### 📦 إدارة المخزون
- تتبع جميع عناصر المخزون
- تنبيهات المخزون المنخفض
- إدارة تواريخ انتهاء الصلاحية
- تصنيف العناصر حسب الفئات
- تقارير قيمة المخزون

#### 💼 النظام المالي
- إدارة شاملة للمعاملات المالية
- تتبع الإيرادات والمصروفات
- إدارة الحسابات البنكية والنقدية
- مقارنة الميزانية مع الفعلي
- تقارير التدفق النقدي

#### 📄 إدارة القوالب
- قوالب للفواتير والعقود
- قوالب رسائل الإيميل
- قوالب التقارير والشهادات
- محرر قوالب متقدم
- نظام المتغيرات الديناميكية

#### 👥 إدارة العملاء والتأشيرات (محدث + عرض شامل)
- **تبويبين منفصلين:**
  - **قائمة العملاء:** عرض شامل مع 21 عمود + أدوات متقدمة
  - **إضافة عميل جديد:** نموذج شامل لإدارة التأشيرات والمعاملات

- **📊 عرض شامل للبيانات (21 عمود):**
  - **المعلومات الأساسية:** اسم العميل، الجوال، رقم الجواز، المهنة، الإيميل
  - **معلومات التأشيرة:** اسم الوكيل، مكتب التفويض، رقم الطلب، نوع الفيزا، رقم الصادر، اسم الشركة
  - **التواريخ:** تاريخ التسليم، تاريخ الترحيل
  - **المالية:** رسوم المعاملة، رسوم الفيزا، العملة (مع عرض المدفوع والمتبقي)
  - **الحالات:** حالة المعاملة، حالة السداد، حالة التسليم، حالة العميل

- **🛠️ أدوات متقدمة:**
  - **🖨️ طباعة احترافية:** تقارير جاهزة مع شعار الشركة والإحصائيات
  - **📊 تصدير Excel:** تصدير جميع البيانات (29 حقل) إلى CSV
  - **📥 استيراد:** استيراد عملاء من ملفات CSV مع تحويل تلقائي للبيانات

- **📈 إحصائيات فورية:**
  - إحصائيات علوية: إجمالي العملاء، النشطين، المميزين، الفيزا التجارية
  - إحصائيات الجدول: العملاء المعروضين، إجمالي الرسوم، المعاملات المكتملة

- **🎨 تصميم متقدم:**
  - تمرير أفقي للجدول الواسع (2000px)
  - ألوان مميزة لكل نوع بيانات
  - خطوط محسنة (11-12px) لاستيعاب المزيد
  - حالات ملونة تفاعلية

- **📝 المعلومات الأساسية:**
  - اسم العميل، رقم الجوال، رقم الجواز
  - المهنة، البريد الإلكتروني، العنوان

- **🛂 معلومات التأشيرة والمعاملة:**
  - اسم الوكيل، مكتب التفويض، مكان التسليم
  - رقم الطلب، نوع الفيزا، رقم الصادر
  - رقم السجل، اسم الشركة

- **📅 التواريخ المهمة:**
  - تاريخ التسليم، تاريخ الترحيل
  - تاريخ الوصول من السفارة، تاريخ التسليم للعميل

- **💰 المعلومات المالية:**
  - رسوم المعاملة (مدفوع/متبقي)
  - رسوم الفيزا (مدفوع/متبقي)
  - دعم العملات: ريال يمني، ريال سعودي، دولار أمريكي

- **📊 الحالة والملاحظات:**
  - حالة المعاملة (معلقة، قيد المعالجة، مكتملة، ملغية، مرفوضة)
  - حالة السداد لمكتب الترحيل (غير مدفوع، جزئي، مكتمل، متأخر)
  - حالة التسليم للعميل (انتظار، جاهز، تم التسليم، متأخر)
  - حالة العميل (نشط، غير نشط، محظور، مميز)

- **📎 مرفقات المعاملة:**
  - إرفاق ملفات متعددة (PDF, Word, صور)
  - إدارة الملفات مع إمكانية الحذف

- **🔍 بحث وتصفية متقدمة:**
  - البحث في الأسماء، الإيميل، الهاتف، رقم الجواز
  - فلترة حسب نوع الفيزا وحالة العميل

### 🚧 قيد التطوير:
- 🏢 **إدارة الموردين** - إدارة شركات الطيران والفنادق
- 🤝 **إدارة الوكلاء** - نظام العمولات والمبيعات
- 📈 **التقارير** - تقارير مالية وإحصائية مفصلة
- ⚙️ **الإعدادات** - إعدادات النظام والمستخدمين

---

## 🔧 التثبيت والإعداد

### المتطلبات:
- **Python 3.8+** مع pip
- **Node.js 16+** مع npm
- **Git** (اختياري)

### خطوات التثبيت:

1. **تحميل المشروع:**
   ```bash
   # إذا كان لديك Git
   git clone [repository-url]
   cd sharaubtravelsoft
   
   # أو قم بتحميل الملفات مباشرة
   ```

2. **إعداد Backend:**
   ```bash
   cd backend
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **إعداد Frontend:**
   ```bash
   cd frontend
   npm install
   ```

4. **تشغيل النظام:**
   ```bash
   # استخدم ملف التشغيل التلقائي
   START_SYSTEM.bat
   ```

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### ❌ Frontend لا يعمل:
```bash
# تأكد من تثبيت Node.js
node --version
npm --version

# أعد تثبيت المكتبات
cd frontend
npm install

# امسح cache
npm start -- --reset-cache
```

#### ❌ Backend لا يعمل:
```bash
# تأكد من Python
python --version

# فعل البيئة الافتراضية
cd backend
venv\Scripts\activate

# أعد تثبيت المكتبات
pip install -r requirements.txt
```

#### ❌ المنافذ مستخدمة:
```bash
# تحقق من المنافذ
netstat -ano | findstr ":3000"
netstat -ano | findstr ":8000"

# أوقف العمليات إذا لزم الأمر
taskkill /PID [رقم_العملية] /F
```

---

## 📱 الاستخدام

### 1. الوصول للنظام:
- افتح المتصفح واذهب إلى `http://localhost:3000`
- ستظهر لك الصفحة الرئيسية الخرافية

### 2. تسجيل الدخول:
- اضغط على "تسجيل الدخول"
- استخدم: `admin` / `admin123`
- ستنتقل إلى لوحة التحكم

### 3. استكشاف النظام:
- **لوحة التحكم**: عرض الإحصائيات والبيانات
- **العملاء**: إدارة شاملة للعملاء والتأشيرات والمعاملات
- **الحجوزات**: إدارة وإضافة الحجوزات
- **المبيعات**: تتبع المبيعات والعمولات
- **المشتريات**: إدارة المشتريات من الموردين
- **المخزون**: تتبع المخزون والمواد
- **المالية**: النظام المالي الشامل
- **القوالب**: إدارة قوالب الوثائق

### 4. إدارة العملاء والتأشيرات:
- اذهب إلى قسم "العملاء"
- **قائمة العملاء**: تصفح العملاء الموجودين، استخدم البحث والفلاتر
- **إضافة عميل جديد**: 
  - املأ المعلومات الأساسية (الاسم، الجوال، الجواز، المهنة)
  - أدخل معلومات التأشيرة والمعاملة
  - حدد التواريخ المهمة
  - أدخل المعلومات المالية واختر العملة
  - حدد الحالات المختلفة
  - أرفق الملفات المطلوبة
  - اضغط "إضافة العميل"

### 4. إضافة حجز جديد:
- اذهب إلى قسم "الحجوزات"
- اضغط "حجز جديد"
- املأ البيانات واضغط "إضافة"

---

## 🎊 الخلاصة

### ✨ ما تم إنجازه:

- ✅ **نظام كامل ومتكامل** للمحاسبة وإدارة وكالات السفريات
- ✅ **واجهات خرافية** مع تصميم عصري وتدرجات مذهلة
- ✅ **نظام تسجيل دخول** آمن وسهل الاستخدام
- ✅ **لوحة تحكم ذكية** مع إحصائيات مباشرة
- ✅ **إدارة العملاء والتأشيرات المحدثة** - نظام شامل لإدارة المعاملات
- ✅ **إدارة الحجوزات** مع جميع المميزات المطلوبة
- ✅ **نظام مالي متقدم** مع دعم عملات متعددة
- ✅ **إدارة المرفقات** مع رفع الملفات
- ✅ **API موثق تفاعلي** للمطورين
- ✅ **دعم كامل للعربية** مع RTL
- ✅ **تصميم متجاوب** يعمل على جميع الأجهزة
- ✅ **أدوات تشخيص متقدمة** لحل المشاكل
- ✅ **ملفات تشغيل تلقائية** لسهولة الاستخدام

### 🆕 **المميزات الجديدة في إدارة العملاء:**

- ✅ **نموذج شامل للتأشيرات** مع جميع الحقول المطلوبة
- ✅ **إدارة مالية متقدمة** مع دعم 3 عملات (ريال يمني، سعودي، دولار)
- ✅ **تتبع حالات المعاملات** في جميع مراحلها
- ✅ **نظام مرفقات متطور** لرفع الملفات
- ✅ **إحصائيات محدثة** تعكس طبيعة عمل وكالة السفريات
- ✅ **بحث وتصفية متقدمة** حسب نوع الفيزا والحالة

### 🚀 النظام جاهز للاستخدام الفوري!

**استمتع بنظامك الجديد وابدأ في إدارة وكالة السفريات بكفاءة عالية! 🎉**

---

<div align="center">

**🧳 نظام محاسبي متكامل لوكالات السفريات 🧳**

*تم التطوير بعناية فائقة وحب للتميز* ❤️

![مكتمل](https://img.shields.io/badge/الحالة-مكتمل%20100%25-success?style=for-the-badge)

</div>