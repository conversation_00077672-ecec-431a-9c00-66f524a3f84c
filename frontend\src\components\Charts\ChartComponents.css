/* Chart Components Styles */

.pie-chart-component,
.line-chart-component,
.bar-chart-component {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pie-chart-component h3,
.line-chart-component h3,
.bar-chart-component h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

/* Pie Chart */
.pie-chart-container {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: center;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.legend-label {
  flex: 1;
  color: #495057;
}

.legend-value {
  font-weight: 600;
  color: #2c3e50;
}

/* Line Chart & Bar Chart */
.chart-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.y-axis-label {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: rotate(-90deg);
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.x-axis-label {
  margin-top: 10px;
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* Performance Gauge */
.performance-gauge {
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.performance-gauge h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.gauge-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gauge-value {
  position: absolute;
  top: 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gauge-value .value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.gauge-value .unit {
  font-size: 12px;
  color: #6c757d;
}

.gauge-labels {
  display: flex;
  justify-content: space-between;
  width: 160px;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

/* Data Table */
.data-table-component {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.data-table-component h3 {
  margin: 0;
  padding: 20px 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background: #f8f9fa;
  padding: 15px 20px;
  text-align: right;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e1e8ed;
}

.data-table td {
  padding: 12px 20px;
  border-bottom: 1px solid #e1e8ed;
  color: #495057;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Stat Card */
.stat-card-component {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.stat-card-component:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card-component .stat-icon {
  font-size: 40px;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-content h4 {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 14px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.stat-change.positive {
  background: #d4edda;
  color: #155724;
}

.stat-change.negative {
  background: #f8d7da;
  color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pie-chart-container {
    flex-direction: column;
    gap: 20px;
  }
  
  .chart-container svg {
    width: 100%;
    height: auto;
  }
  
  .y-axis-label {
    display: none;
  }
  
  .stat-card-component {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .gauge-labels {
    width: 140px;
  }
  
  .data-table {
    font-size: 12px;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .pie-chart-component,
  .line-chart-component,
  .bar-chart-component,
  .performance-gauge {
    padding: 15px;
  }
  
  .stat-card-component {
    padding: 20px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .gauge-value .value {
    font-size: 20px;
  }
  
  .data-table th,
  .data-table td {
    padding: 6px 8px;
  }
}

/* Animation Effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pie-chart-component,
.line-chart-component,
.bar-chart-component,
.performance-gauge,
.data-table-component,
.stat-card-component {
  animation: fadeInUp 0.5s ease-out;
}

/* Hover Effects */
.data-table tr {
  transition: background-color 0.2s ease;
}

.legend-item {
  transition: all 0.2s ease;
  padding: 5px;
  border-radius: 5px;
}

.legend-item:hover {
  background: #f8f9fa;
  transform: translateX(-3px);
}

/* Chart Tooltips */
.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chart-tooltip.visible {
  opacity: 1;
}

/* Loading States */
.chart-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
}

.chart-loading::after {
  content: '';
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}