<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 50px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 نظام محاسبي لوكالات السفريات</h1>
        <h2>اختبار حالة النظام</h2>
        
        <div id="frontend-status" class="status">
            <h3>🌐 Frontend Status</h3>
            <p id="frontend-result">جاري الفحص...</p>
        </div>
        
        <div id="backend-status" class="status">
            <h3>🖥️ Backend Status</h3>
            <p id="backend-result">جاري الفحص...</p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="http://localhost:3000" class="link" target="_blank">🌐 فتح التطبيق الرئيسي</a>
            <a href="http://localhost:8000/docs" class="link" target="_blank">📚 فتح واجهة API</a>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>📋 معلومات النظام</h3>
            <p><strong>Frontend:</strong> React.js + Material-UI</p>
            <p><strong>Backend:</strong> FastAPI + SQLite</p>
            <p><strong>المنفذ الأمامي:</strong> 3000</p>
            <p><strong>المنفذ الخلفي:</strong> 8000</p>
        </div>
    </div>

    <script>
        // Test Frontend
        fetch('http://localhost:3000')
            .then(response => {
                if (response.ok) {
                    document.getElementById('frontend-result').textContent = '✅ يعمل بشكل طبيعي';
                    document.getElementById('frontend-status').className = 'status success';
                } else {
                    throw new Error('Frontend not responding');
                }
            })
            .catch(error => {
                document.getElementById('frontend-result').textContent = '❌ لا يعمل - ' + error.message;
                document.getElementById('frontend-status').className = 'status error';
            });

        // Test Backend
        fetch('http://localhost:8000')
            .then(response => response.json())
            .then(data => {
                document.getElementById('backend-result').textContent = '✅ يعمل بشكل طبيعي - ' + data.message;
                document.getElementById('backend-status').className = 'status success';
            })
            .catch(error => {
                document.getElementById('backend-result').textContent = '❌ لا يعمل - ' + error.message;
                document.getElementById('backend-status').className = 'status error';
            });
    </script>
</body>
</html>