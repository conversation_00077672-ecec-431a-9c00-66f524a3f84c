import React, { useState, useEffect } from 'react';
import ModernCard, { StatsCard, FeatureCard, MetricCard, CardBody, CardHeader, CardTitle } from '../UI/ModernCard';
import ModernButton from '../UI/ModernButton';
import { ModernGrid, ModernFlex } from '../UI/ModernLayout';
import './ModernDashboard.css';

const ModernDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalRevenue: 245680,
      totalBookings: 1247,
      activeCustomers: 892,
      pendingPayments: 45230
    },
    recentActivities: [],
    quickActions: [],
    charts: {}
  });

  const [timeRange, setTimeRange] = useState('today');

  useEffect(() => {
    // Simulate data loading
    const loadDashboardData = () => {
      setDashboardData({
        stats: {
          totalRevenue: 245680,
          totalBookings: 1247,
          activeCustomers: 892,
          pendingPayments: 45230
        },
        recentActivities: [
          {
            id: 1,
            type: 'booking',
            title: 'حجز جديد - رحلة الرياض إلى دبي',
            customer: 'أحمد محمد العلي',
            amount: 2500,
            time: '10:30 ص',
            status: 'confirmed'
          },
          {
            id: 2,
            type: 'payment',
            title: 'دفعة مستلمة',
            customer: 'فاطمة سالم أحمد',
            amount: 1800,
            time: '09:45 ص',
            status: 'completed'
          },
          {
            id: 3,
            type: 'visa',
            title: 'طلب تأشيرة جديد',
            customer: 'محمد عبدالله الزهراني',
            amount: 450,
            time: '09:15 ص',
            status: 'pending'
          },
          {
            id: 4,
            type: 'hotel',
            title: 'حجز فندق - 5 ليالي',
            customer: 'نورا خالد المطيري',
            amount: 3200,
            time: '08:30 ص',
            status: 'confirmed'
          }
        ],
        quickActions: [
          {
            title: 'حجز جديد',
            description: 'إنشاء حجز سفر جديد للعملاء',
            icon: '✈️',
            color: 'primary',
            path: '/bookings/new'
          },
          {
            title: 'فاتورة جديدة',
            description: 'إصدار فاتورة للعملاء',
            icon: '🧾',
            color: 'success',
            path: '/sales/invoices/new'
          },
          {
            title: 'عميل جديد',
            description: 'إضافة عميل جديد للنظام',
            icon: '👤',
            color: 'secondary',
            path: '/customers/new'
          },
          {
            title: 'تقرير مالي',
            description: 'عرض التقارير المالية',
            icon: '📊',
            color: 'info',
            path: '/reports/financial'
          }
        ]
      });
    };

    loadDashboardData();
  }, [timeRange]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getActivityIcon = (type) => {
    const icons = {
      booking: '✈️',
      payment: '💰',
      visa: '📋',
      hotel: '🏨',
      transport: '🚗'
    };
    return icons[type] || '📝';
  };

  const getActivityColor = (type) => {
    const colors = {
      booking: 'primary',
      payment: 'success',
      visa: 'warning',
      hotel: 'info',
      transport: 'secondary'
    };
    return colors[type] || 'neutral';
  };

  const getStatusColor = (status) => {
    const colors = {
      confirmed: 'success',
      pending: 'warning',
      completed: 'success',
      cancelled: 'error'
    };
    return colors[status] || 'neutral';
  };

  const getStatusLabel = (status) => {
    const labels = {
      confirmed: 'مؤكد',
      pending: 'قيد الانتظار',
      completed: 'مكتمل',
      cancelled: 'ملغي'
    };
    return labels[status] || status;
  };

  return (
    <div className="modern-dashboard">
      {/* Dashboard Header */}
      <div className="dashboard-header">
        <div className="dashboard-title">
          <h1>لوحة التحكم</h1>
          <p>مرحباً بك في نظام إدارة شراء للسفر والسياحة</p>
        </div>
        
        <div className="dashboard-controls">
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className="time-range-selector"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="year">هذا العام</option>
          </select>
          
          <ModernButton variant="primary" icon="🔄">
            تحديث البيانات
          </ModernButton>
        </div>
      </div>

      {/* Stats Cards */}
      <ModernGrid cols={4} gap="lg" responsive className="stats-grid">
        <StatsCard
          title="إجمالي الإيرادات"
          value={formatCurrency(dashboardData.stats.totalRevenue)}
          change="+12.5%"
          changeType="positive"
          icon="💰"
          color="success"
        />
        
        <StatsCard
          title="إجمالي الحجوزات"
          value={dashboardData.stats.totalBookings.toLocaleString('ar')}
          change="+8.2%"
          changeType="positive"
          icon="✈️"
          color="primary"
        />
        
        <StatsCard
          title="العملاء النشطين"
          value={dashboardData.stats.activeCustomers.toLocaleString('ar')}
          change="+15.3%"
          changeType="positive"
          icon="👥"
          color="secondary"
        />
        
        <StatsCard
          title="المدفوعات المعلقة"
          value={formatCurrency(dashboardData.stats.pendingPayments)}
          change="-5.1%"
          changeType="negative"
          icon="⏳"
          color="warning"
        />
      </ModernGrid>

      {/* Main Content Grid */}
      <ModernGrid cols={3} gap="lg" responsive className="main-content-grid">
        
        {/* Recent Activities */}
        <div className="dashboard-section activities-section">
          <ModernCard>
            <CardHeader>
              <CardTitle>النشاطات الأخيرة</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="activities-list">
                {dashboardData.recentActivities.map((activity) => (
                  <div key={activity.id} className="activity-item">
                    <div className="activity-icon">
                      <span className={`activity-icon-bg activity-icon-bg--${getActivityColor(activity.type)}`}>
                        {getActivityIcon(activity.type)}
                      </span>
                    </div>
                    
                    <div className="activity-content">
                      <div className="activity-header">
                        <h4 className="activity-title">{activity.title}</h4>
                        <span className="activity-time">{activity.time}</span>
                      </div>
                      
                      <div className="activity-details">
                        <span className="activity-customer">{activity.customer}</span>
                        <span className="activity-amount">{formatCurrency(activity.amount)}</span>
                      </div>
                      
                      <div className="activity-footer">
                        <span className={`activity-status activity-status--${getStatusColor(activity.status)}`}>
                          {getStatusLabel(activity.status)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="activities-footer">
                <ModernButton variant="outline" size="sm" fullWidth>
                  عرض جميع النشاطات
                </ModernButton>
              </div>
            </CardBody>
          </ModernCard>
        </div>

        {/* Quick Actions */}
        <div className="dashboard-section quick-actions-section">
          <ModernCard>
            <CardHeader>
              <CardTitle>الإجراءات السريعة</CardTitle>
            </CardHeader>
            <CardBody>
              <ModernGrid cols={2} gap="md" className="quick-actions-grid">
                {dashboardData.quickActions.map((action, index) => (
                  <FeatureCard
                    key={index}
                    title={action.title}
                    description={action.description}
                    icon={action.icon}
                    action={
                      <ModernButton 
                        variant={action.color} 
                        size="sm"
                        onClick={() => console.log('Navigate to:', action.path)}
                      >
                        ابدأ الآن
                      </ModernButton>
                    }
                  />
                ))}
              </ModernGrid>
            </CardBody>
          </ModernCard>
        </div>

        {/* Performance Metrics */}
        <div className="dashboard-section metrics-section">
          <ModernCard>
            <CardHeader>
              <CardTitle>مؤشرات الأداء</CardTitle>
            </CardHeader>
            <CardBody>
              <div className="metrics-grid">
                <MetricCard
                  label="معدل التحويل"
                  value="68.5"
                  unit="%"
                  trend="up"
                  trendValue="+5.2%"
                  color="success"
                  size="sm"
                />
                
                <MetricCard
                  label="متوسط قيمة الحجز"
                  value="1,850"
                  unit="ر.س"
                  trend="up"
                  trendValue="+12.3%"
                  color="primary"
                  size="sm"
                />
                
                <MetricCard
                  label="رضا العملاء"
                  value="4.8"
                  unit="/5"
                  trend="up"
                  trendValue="+0.3"
                  color="secondary"
                  size="sm"
                />
                
                <MetricCard
                  label="وقت الاستجابة"
                  value="2.3"
                  unit="دقيقة"
                  trend="down"
                  trendValue="-0.8"
                  color="info"
                  size="sm"
                />
              </div>
            </CardBody>
          </ModernCard>
        </div>
      </ModernGrid>

      {/* Charts Section */}
      <ModernGrid cols={2} gap="lg" responsive className="charts-section">
        <ModernCard>
          <CardHeader>
            <CardTitle>الإيرادات الشهرية</CardTitle>
          </CardHeader>
          <CardBody>
            <div className="chart-placeholder">
              <div className="chart-icon">📈</div>
              <p>مخطط الإيرادات الشهرية</p>
              <small>سيتم إضافة المخططات قريباً</small>
            </div>
          </CardBody>
        </ModernCard>
        
        <ModernCard>
          <CardHeader>
            <CardTitle>توزيع أنواع الحجوزات</CardTitle>
          </CardHeader>
          <CardBody>
            <div className="chart-placeholder">
              <div className="chart-icon">🥧</div>
              <p>مخطط دائري لأنواع الحجوزات</p>
              <small>سيتم إضافة المخططات قريباً</small>
            </div>
          </CardBody>
        </ModernCard>
      </ModernGrid>

      {/* Weather Widget */}
      <ModernCard className="weather-widget">
        <CardBody>
          <ModernFlex align="center" justify="between">
            <div className="weather-info">
              <h3>الطقس في الرياض</h3>
              <p>مشمس، 28°م</p>
            </div>
            <div className="weather-icon">☀️</div>
          </ModernFlex>
        </CardBody>
      </ModernCard>
    </div>
  );
};

export default ModernDashboard;