import React, { useState, useMemo } from 'react';
import './AdvancedReports.css';

// مكون التقارير المتقدمة
export const AdvancedReports = ({ accounts, transactions, isLoading }) => {
  const [activeReport, setActiveReport] = useState('aging');
  const [reportPeriod, setReportPeriod] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // تقرير أعمار الذمم
  const AgingReport = () => {
    const agingData = [
      {
        customerName: 'أحمد محمد العلي',
        totalBalance: 25000,
        current: 10000,
        days30: 8000,
        days60: 5000,
        days90: 2000,
        over90: 0
      },
      {
        customerName: 'شركة السفر الذهبي',
        totalBalance: 18000,
        current: 18000,
        days30: 0,
        days60: 0,
        days90: 0,
        over90: 0
      },
      {
        customerName: 'فاطمة سالم',
        totalBalance: 12000,
        current: 0,
        days30: 0,
        days60: 7000,
        days90: 5000,
        over90: 0
      }
    ];

    const totals = agingData.reduce((acc, item) => ({
      totalBalance: acc.totalBalance + item.totalBalance,
      current: acc.current + item.current,
      days30: acc.days30 + item.days30,
      days60: acc.days60 + item.days60,
      days90: acc.days90 + item.days90,
      over90: acc.over90 + item.over90
    }), { totalBalance: 0, current: 0, days30: 0, days60: 0, days90: 0, over90: 0 });

    return (
      <div className="aging-report">
        <h4>تقرير أعمار الذمم المدينة</h4>
        
        <div className="aging-summary">
          <div className="summary-item">
            <span className="label">إجمالي الذمم:</span>
            <span className="value">{formatCurrency(totals.totalBalance)}</span>
          </div>
          <div className="summary-item current">
            <span className="label">جاري (0-30 يوم):</span>
            <span className="value">{formatCurrency(totals.current)}</span>
          </div>
          <div className="summary-item days30">
            <span className="label">31-60 يوم:</span>
            <span className="value">{formatCurrency(totals.days30)}</span>
          </div>
          <div className="summary-item days60">
            <span className="label">61-90 يوم:</span>
            <span className="value">{formatCurrency(totals.days60)}</span>
          </div>
          <div className="summary-item over90">
            <span className="label">أكثر من 90 يوم:</span>
            <span className="value">{formatCurrency(totals.over90)}</span>
          </div>
        </div>

        <div className="aging-table">
          <div className="table-header">
            <div>العميل</div>
            <div>إجمالي الرصيد</div>
            <div>جاري (0-30)</div>
            <div>31-60 يوم</div>
            <div>61-90 يوم</div>
            <div>أكثر من 90</div>
          </div>

          {agingData.map((item, index) => (
            <div key={index} className="table-row">
              <div className="customer-name">{item.customerName}</div>
              <div className="total-balance">{formatCurrency(item.totalBalance)}</div>
              <div className="current">{formatCurrency(item.current)}</div>
              <div className="days30">{formatCurrency(item.days30)}</div>
              <div className="days60">{formatCurrency(item.days60)}</div>
              <div className="over90">{formatCurrency(item.over90)}</div>
            </div>
          ))}

          <div className="table-footer">
            <div className="total-label">الإجمالي</div>
            <div className="total-balance">{formatCurrency(totals.totalBalance)}</div>
            <div className="total-current">{formatCurrency(totals.current)}</div>
            <div className="total-days30">{formatCurrency(totals.days30)}</div>
            <div className="total-days60">{formatCurrency(totals.days60)}</div>
            <div className="total-over90">{formatCurrency(totals.over90)}</div>
          </div>
        </div>
      </div>
    );
  };

  // تقرير الربحية
  const ProfitabilityReport = () => {
    const profitabilityData = [
      {
        service: 'تذاكر الطيران',
        revenue: 450000,
        cost: 380000,
        grossProfit: 70000,
        margin: 15.56
      },
      {
        service: 'الفنادق',
        revenue: 320000,
        cost: 280000,
        grossProfit: 40000,
        margin: 12.5
      },
      {
        service: 'العمرة والحج',
        revenue: 280000,
        cost: 220000,
        grossProfit: 60000,
        margin: 21.43
      },
      {
        service: 'التأشيرات',
        revenue: 150000,
        cost: 100000,
        grossProfit: 50000,
        margin: 33.33
      },
      {
        service: 'النقل',
        revenue: 120000,
        cost: 95000,
        grossProfit: 25000,
        margin: 20.83
      }
    ];

    const totals = profitabilityData.reduce((acc, item) => ({
      revenue: acc.revenue + item.revenue,
      cost: acc.cost + item.cost,
      grossProfit: acc.grossProfit + item.grossProfit
    }), { revenue: 0, cost: 0, grossProfit: 0 });

    totals.margin = (totals.grossProfit / totals.revenue) * 100;

    return (
      <div className="profitability-report">
        <h4>تقرير الربحية حسب الخدمة</h4>
        
        <div className="profitability-summary">
          <div className="summary-card">
            <h5>إجمالي الإيرادات</h5>
            <div className="value">{formatCurrency(totals.revenue)}</div>
          </div>
          <div className="summary-card">
            <h5>إجمالي التكاليف</h5>
            <div className="value">{formatCurrency(totals.cost)}</div>
          </div>
          <div className="summary-card">
            <h5>إجمالي الربح</h5>
            <div className="value">{formatCurrency(totals.grossProfit)}</div>
          </div>
          <div className="summary-card">
            <h5>هامش الربح</h5>
            <div className="value">{totals.margin.toFixed(2)}%</div>
          </div>
        </div>

        <div className="profitability-table">
          <div className="table-header">
            <div>الخدمة</div>
            <div>الإيرادات</div>
            <div>التكاليف</div>
            <div>الربح الإجمالي</div>
            <div>هامش الربح</div>
          </div>

          {profitabilityData.map((item, index) => (
            <div key={index} className="table-row">
              <div className="service-name">{item.service}</div>
              <div className="revenue">{formatCurrency(item.revenue)}</div>
              <div className="cost">{formatCurrency(item.cost)}</div>
              <div className="gross-profit">{formatCurrency(item.grossProfit)}</div>
              <div className="margin">{item.margin.toFixed(2)}%</div>
            </div>
          ))}

          <div className="table-footer">
            <div className="total-label">الإجمالي</div>
            <div className="total-revenue">{formatCurrency(totals.revenue)}</div>
            <div className="total-cost">{formatCurrency(totals.cost)}</div>
            <div className="total-profit">{formatCurrency(totals.grossProfit)}</div>
            <div className="total-margin">{totals.margin.toFixed(2)}%</div>
          </div>
        </div>
      </div>
    );
  };

  // تقرير التدفق النقدي
  const CashFlowReport = () => {
    const cashFlowData = {
      operating: {
        netIncome: 245000,
        depreciation: 25000,
        receivablesChange: -15000,
        payablesChange: 8000,
        inventoryChange: -5000,
        total: 258000
      },
      investing: {
        equipmentPurchase: -45000,
        softwarePurchase: -12000,
        total: -57000
      },
      financing: {
        loanProceeds: 100000,
        loanRepayment: -25000,
        ownerWithdrawals: -50000,
        total: 25000
      }
    };

    const netCashFlow = cashFlowData.operating.total + 
                       cashFlowData.investing.total + 
                       cashFlowData.financing.total;

    return (
      <div className="cash-flow-report">
        <h4>تقرير التدفق النقدي</h4>
        
        <div className="cash-flow-sections">
          <div className="cash-flow-section operating">
            <h5>الأنشطة التشغيلية</h5>
            <div className="flow-item">
              <span>صافي الدخل</span>
              <span>{formatCurrency(cashFlowData.operating.netIncome)}</span>
            </div>
            <div className="flow-item">
              <span>الاستهلاك</span>
              <span>{formatCurrency(cashFlowData.operating.depreciation)}</span>
            </div>
            <div className="flow-item">
              <span>تغيير في الذمم المدينة</span>
              <span>{formatCurrency(cashFlowData.operating.receivablesChange)}</span>
            </div>
            <div className="flow-item">
              <span>تغيير في الذمم الدائنة</span>
              <span>{formatCurrency(cashFlowData.operating.payablesChange)}</span>
            </div>
            <div className="flow-item">
              <span>تغيير في المخزون</span>
              <span>{formatCurrency(cashFlowData.operating.inventoryChange)}</span>
            </div>
            <div className="flow-total">
              <span>صافي التدفق من الأنشطة التشغيلية</span>
              <span>{formatCurrency(cashFlowData.operating.total)}</span>
            </div>
          </div>

          <div className="cash-flow-section investing">
            <h5>الأنشطة الاستثمارية</h5>
            <div className="flow-item">
              <span>شراء معدات</span>
              <span>{formatCurrency(cashFlowData.investing.equipmentPurchase)}</span>
            </div>
            <div className="flow-item">
              <span>شراء برمجيات</span>
              <span>{formatCurrency(cashFlowData.investing.softwarePurchase)}</span>
            </div>
            <div className="flow-total">
              <span>صافي التدفق من الأنشطة الاستثمارية</span>
              <span>{formatCurrency(cashFlowData.investing.total)}</span>
            </div>
          </div>

          <div className="cash-flow-section financing">
            <h5>الأنشطة التمويلية</h5>
            <div className="flow-item">
              <span>حصيلة القروض</span>
              <span>{formatCurrency(cashFlowData.financing.loanProceeds)}</span>
            </div>
            <div className="flow-item">
              <span>سداد القروض</span>
              <span>{formatCurrency(cashFlowData.financing.loanRepayment)}</span>
            </div>
            <div className="flow-item">
              <span>مسحوبات المالك</span>
              <span>{formatCurrency(cashFlowData.financing.ownerWithdrawals)}</span>
            </div>
            <div className="flow-total">
              <span>صافي التدفق من الأنشطة التمويلية</span>
              <span>{formatCurrency(cashFlowData.financing.total)}</span>
            </div>
          </div>
        </div>

        <div className="net-cash-flow">
          <div className="net-flow-item">
            <span>صافي التدفق النقدي</span>
            <span className={netCashFlow >= 0 ? 'positive' : 'negative'}>
              {formatCurrency(netCashFlow)}
            </span>
          </div>
        </div>
      </div>
    );
  };

  // تقرير النسب المالية
  const FinancialRatiosReport = () => {
    const ratiosData = {
      liquidity: {
        currentRatio: 2.5,
        quickRatio: 1.8,
        cashRatio: 0.9
      },
      profitability: {
        grossProfitMargin: 18.5,
        netProfitMargin: 12.3,
        returnOnAssets: 15.2,
        returnOnEquity: 22.1
      },
      efficiency: {
        receivablesTurnover: 8.5,
        inventoryTurnover: 12.3,
        assetTurnover: 1.4
      },
      leverage: {
        debtToAssets: 0.35,
        debtToEquity: 0.54,
        interestCoverage: 8.2
      }
    };

    return (
      <div className="ratios-report">
        <h4>تقرير النسب المالية</h4>
        
        <div className="ratios-grid">
          <div className="ratio-category">
            <h5>نسب السيولة</h5>
            <div className="ratio-item">
              <span>النسبة الجارية</span>
              <span className="ratio-value">{ratiosData.liquidity.currentRatio}</span>
            </div>
            <div className="ratio-item">
              <span>النسبة السريعة</span>
              <span className="ratio-value">{ratiosData.liquidity.quickRatio}</span>
            </div>
            <div className="ratio-item">
              <span>النسبة النقدية</span>
              <span className="ratio-value">{ratiosData.liquidity.cashRatio}</span>
            </div>
          </div>

          <div className="ratio-category">
            <h5>نسب الربحية</h5>
            <div className="ratio-item">
              <span>هامش الربح الإجمالي</span>
              <span className="ratio-value">{ratiosData.profitability.grossProfitMargin}%</span>
            </div>
            <div className="ratio-item">
              <span>هامش الربح الصافي</span>
              <span className="ratio-value">{ratiosData.profitability.netProfitMargin}%</span>
            </div>
            <div className="ratio-item">
              <span>العائد على الأصول</span>
              <span className="ratio-value">{ratiosData.profitability.returnOnAssets}%</span>
            </div>
            <div className="ratio-item">
              <span>العائد على حقوق الملكية</span>
              <span className="ratio-value">{ratiosData.profitability.returnOnEquity}%</span>
            </div>
          </div>

          <div className="ratio-category">
            <h5>نسب الكفاءة</h5>
            <div className="ratio-item">
              <span>معدل دوران الذمم</span>
              <span className="ratio-value">{ratiosData.efficiency.receivablesTurnover}</span>
            </div>
            <div className="ratio-item">
              <span>معدل دوران المخزون</span>
              <span className="ratio-value">{ratiosData.efficiency.inventoryTurnover}</span>
            </div>
            <div className="ratio-item">
              <span>معدل دوران الأصول</span>
              <span className="ratio-value">{ratiosData.efficiency.assetTurnover}</span>
            </div>
          </div>

          <div className="ratio-category">
            <h5>نسب الرافعة المالية</h5>
            <div className="ratio-item">
              <span>الدين إلى الأصول</span>
              <span className="ratio-value">{ratiosData.leverage.debtToAssets}</span>
            </div>
            <div className="ratio-item">
              <span>الدين إلى حقوق الملكية</span>
              <span className="ratio-value">{ratiosData.leverage.debtToEquity}</span>
            </div>
            <div className="ratio-item">
              <span>تغطية الفوائد</span>
              <span className="ratio-value">{ratiosData.leverage.interestCoverage}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل التقارير المتقدمة...</p>
      </div>
    );
  }

  return (
    <div className="advanced-reports">
      <div className="reports-header">
        <h3>التقارير المتقدمة</h3>
        <div className="header-actions">
          <button className="export-btn">📊 تصدير Excel</button>
          <button className="print-btn">🖨️ طباعة</button>
        </div>
      </div>

      {/* تبويبات التقارير */}
      <div className="reports-tabs">
        <button 
          className={`tab-btn ${activeReport === 'aging' ? 'active' : ''}`}
          onClick={() => setActiveReport('aging')}
        >
          أعمار الذمم
        </button>
        <button 
          className={`tab-btn ${activeReport === 'profitability' ? 'active' : ''}`}
          onClick={() => setActiveReport('profitability')}
        >
          الربحية
        </button>
        <button 
          className={`tab-btn ${activeReport === 'cashflow' ? 'active' : ''}`}
          onClick={() => setActiveReport('cashflow')}
        >
          التدفق النقدي
        </button>
        <button 
          className={`tab-btn ${activeReport === 'ratios' ? 'active' : ''}`}
          onClick={() => setActiveReport('ratios')}
        >
          النسب المالية
        </button>
      </div>

      {/* إعدادات الفترة */}
      <div className="period-settings">
        <div className="form-group">
          <label>من تاريخ</label>
          <input
            type="date"
            value={reportPeriod.startDate}
            onChange={(e) => setReportPeriod({
              ...reportPeriod,
              startDate: e.target.value
            })}
          />
        </div>
        <div className="form-group">
          <label>إلى تاريخ</label>
          <input
            type="date"
            value={reportPeriod.endDate}
            onChange={(e) => setReportPeriod({
              ...reportPeriod,
              endDate: e.target.value
            })}
          />
        </div>
        <button className="refresh-btn">🔄 تحديث</button>
      </div>

      {/* محتوى التقارير */}
      <div className="reports-content">
        {activeReport === 'aging' && <AgingReport />}
        {activeReport === 'profitability' && <ProfitabilityReport />}
        {activeReport === 'cashflow' && <CashFlowReport />}
        {activeReport === 'ratios' && <FinancialRatiosReport />}
      </div>
    </div>
  );
};

export default AdvancedReports;