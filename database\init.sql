-- إن<PERSON>ا<PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS travel_accounting 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE travel_accounting;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER IF NOT EXISTS 'travel_user'@'localhost' IDENTIFIED BY 'travel_password_2024';
GRANT ALL PRIVILEGES ON travel_accounting.* TO 'travel_user'@'localhost';
FLUSH PRIVILEGES;

-- إعد<PERSON><PERSON><PERSON> قاعدة البيانات
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET time_zone = '+00:00';

-- تفعيل الفهرسة الكاملة للنصوص العربية
SET GLOBAL innodb_ft_min_token_size = 1;
SET GLOBAL ft_min_word_len = 1;

-- إعدادات الأداء
SET GLOBAL innodb_buffer_pool_size = *********; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = ********; -- 64MB
SET GLOBAL query_cache_type = 1;

-- إنشاء جداول النظام الأساسية

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    avatar VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    is_superuser BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    failed_login_attempts INT DEFAULT 0,
    locked_until DATETIME,
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'UTC',
    theme VARCHAR(20) DEFAULT 'light',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأدوار
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    INDEX idx_name (name),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    INDEX idx_name (name),
    INDEX idx_module (module),
    INDEX idx_action (action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العلاقة بين المستخدمين والأدوار
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العلاقة بين الأدوار والصلاحيات
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج البيانات الأولية

-- إدراج الأدوار الأساسية
INSERT IGNORE INTO roles (name, display_name, description, color) VALUES
('super_admin', 'مدير عام', 'مدير عام للنظام مع جميع الصلاحيات', '#dc3545'),
('admin', 'مدير', 'مدير النظام', '#007bff'),
('manager', 'مدير قسم', 'مدير قسم أو فرع', '#28a745'),
('accountant', 'محاسب', 'محاسب مالي', '#ffc107'),
('employee', 'موظف', 'موظف عادي', '#6c757d'),
('agent', 'وكيل', 'وكيل مبيعات', '#17a2b8');

-- إدراج الصلاحيات الأساسية
INSERT IGNORE INTO permissions (name, display_name, module, action) VALUES
-- صلاحيات المستخدمين
('users.create', 'إنشاء مستخدم', 'users', 'create'),
('users.read', 'عرض المستخدمين', 'users', 'read'),
('users.update', 'تعديل مستخدم', 'users', 'update'),
('users.delete', 'حذف مستخدم', 'users', 'delete'),

-- صلاحيات العملاء
('customers.create', 'إنشاء عميل', 'customers', 'create'),
('customers.read', 'عرض العملاء', 'customers', 'read'),
('customers.update', 'تعديل عميل', 'customers', 'update'),
('customers.delete', 'حذف عميل', 'customers', 'delete'),

-- صلاحيات الموردين
('suppliers.create', 'إنشاء مورد', 'suppliers', 'create'),
('suppliers.read', 'عرض الموردين', 'suppliers', 'read'),
('suppliers.update', 'تعديل مورد', 'suppliers', 'update'),
('suppliers.delete', 'حذف مورد', 'suppliers', 'delete'),

-- صلاحيات الوكلاء
('agents.create', 'إنشاء وكيل', 'agents', 'create'),
('agents.read', 'عرض الوكلاء', 'agents', 'read'),
('agents.update', 'تعديل وكيل', 'agents', 'update'),
('agents.delete', 'حذف وكيل', 'agents', 'delete'),

-- صلاحيات الحجوزات
('bookings.create', 'إنشاء حجز', 'bookings', 'create'),
('bookings.read', 'عرض الحجوزات', 'bookings', 'read'),
('bookings.update', 'تعديل حجز', 'bookings', 'update'),
('bookings.delete', 'حذف حجز', 'bookings', 'delete'),
('bookings.confirm', 'تأكيد حجز', 'bookings', 'confirm'),
('bookings.cancel', 'إلغاء حجز', 'bookings', 'cancel'),

-- صلاحيات المحاسبة
('accounting.create', 'إنشاء قيد محاسبي', 'accounting', 'create'),
('accounting.read', 'عرض القيود المحاسبية', 'accounting', 'read'),
('accounting.update', 'تعديل قيد محاسبي', 'accounting', 'update'),
('accounting.delete', 'حذف قيد محاسبي', 'accounting', 'delete'),
('accounting.post', 'ترحيل قيد محاسبي', 'accounting', 'post'),

-- صلاحيات التقارير
('reports.financial', 'التقارير المالية', 'reports', 'financial'),
('reports.sales', 'تقارير المبيعات', 'reports', 'sales'),
('reports.customers', 'تقارير العملاء', 'reports', 'customers'),
('reports.suppliers', 'تقارير الموردين', 'reports', 'suppliers'),

-- صلاحيات الإعدادات
('settings.system', 'إعدادات النظام', 'settings', 'system'),
('settings.users', 'إعدادات المستخدمين', 'settings', 'users'),
('settings.backup', 'النسخ الاحتياطي', 'settings', 'backup');

-- إنشاء المستخدم الافتراضي (مدير عام)
INSERT IGNORE INTO users (
    username, 
    email, 
    full_name, 
    hashed_password, 
    is_verified, 
    is_superuser, 
    is_active
) VALUES (
    'admin',
    '<EMAIL>',
    'مدير النظام',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- password: admin123
    TRUE,
    TRUE,
    TRUE
);

-- ربط المستخدم الافتراضي بدور المدير العام
INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'admin' AND r.name = 'super_admin';

-- منح جميع الصلاحيات لدور المدير العام
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'super_admin';

-- إنشاء فهارس إضافية للأداء
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_last_login ON users(last_login);
CREATE INDEX idx_roles_created_at ON roles(created_at);
CREATE INDEX idx_permissions_module_action ON permissions(module, action);

-- إعدادات الأمان
-- تفعيل SSL (يجب تكوينه في إعدادات MySQL)
-- SET GLOBAL require_secure_transport = ON;

COMMIT;