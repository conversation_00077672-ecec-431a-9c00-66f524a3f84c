import React, { useState, useEffect } from 'react';
import './SystemSettings.css';
import './SettingsComponents.css';
import './AdvancedSettings.css';
import './ThemeManager.css';
import { 
  ConnectionTest, 
  BackupManager, 
  UserManagement, 
  EmailSettings, 
  SystemStats 
} from './SettingsComponents';
import { 
  PermissionsManager, 
  SystemLogs, 
  EmailTemplates, 
  MaintenanceManager 
} from './AdvancedSettings';
import ThemeManager from './ThemeManager';

// مكون إعدادات النظام الشامل
const SystemSettings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      companyName: 'شركة شراء السياحة',
      companyLogo: '',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      currency: 'SAR',
      dateFormat: 'DD/MM/YYYY',
      theme: 'light',
      autoSave: true,
      notifications: true
    },
    security: {
      passwordMinLength: 8,
      requireSpecialChars: true,
      sessionTimeout: 30,
      twoFactorAuth: false,
      loginAttempts: 5,
      lockoutDuration: 15,
      ipWhitelist: '',
      auditLog: true
    },
    integrations: {
      amadeus: {
        enabled: false,
        apiKey: '',
        apiSecret: '',
        environment: 'test'
      },
      sabre: {
        enabled: false,
        username: '',
        password: '',
        pcc: ''
      },
      payment: {
        stripe: {
          enabled: false,
          publicKey: '',
          secretKey: ''
        },
        paypal: {
          enabled: false,
          clientId: '',
          clientSecret: ''
        }
      }
    },
    notifications: {
      email: {
        bookingConfirmation: true,
        paymentReceived: true,
        systemAlerts: true,
        dailyReports: false
      },
      sms: {
        enabled: false,
        provider: 'twilio',
        apiKey: '',
        fromNumber: ''
      },
      push: {
        enabled: true,
        vapidKey: ''
      }
    },
    performance: {
      cacheEnabled: true,
      cacheDuration: 3600,
      compressionEnabled: true,
      minifyAssets: true,
      lazyLoading: true,
      maxFileSize: 10,
      sessionStorage: 'database'
    }
  });

  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  // تحميل الإعدادات من localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('systemSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  // حفظ الإعدادات
  const saveSettings = async () => {
    setSaving(true);
    
    try {
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      localStorage.setItem('systemSettings', JSON.stringify(settings));
      setUnsavedChanges(false);
      setLastSaved(new Date().toLocaleString('ar-SA'));
      
      // إظهار رسالة نجاح
      showNotification('تم حفظ الإعدادات بنجاح', 'success');
    } catch (error) {
      showNotification('فشل في حفظ الإعدادات', 'error');
    } finally {
      setSaving(false);
    }
  };

  // إعادة تعيين الإعدادات
  const resetSettings = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
      setSettings({
        general: {
          companyName: 'شركة شراء السياحة',
          companyLogo: '',
          timezone: 'Asia/Riyadh',
          language: 'ar',
          currency: 'SAR',
          dateFormat: 'DD/MM/YYYY',
          theme: 'light',
          autoSave: true,
          notifications: true
        },
        security: {
          passwordMinLength: 8,
          requireSpecialChars: true,
          sessionTimeout: 30,
          twoFactorAuth: false,
          loginAttempts: 5,
          lockoutDuration: 15,
          ipWhitelist: '',
          auditLog: true
        },
        integrations: {
          amadeus: {
            enabled: false,
            apiKey: '',
            apiSecret: '',
            environment: 'test'
          },
          sabre: {
            enabled: false,
            username: '',
            password: '',
            pcc: ''
          },
          payment: {
            stripe: {
              enabled: false,
              publicKey: '',
              secretKey: ''
            },
            paypal: {
              enabled: false,
              clientId: '',
              clientSecret: ''
            }
          }
        },
        notifications: {
          email: {
            bookingConfirmation: true,
            paymentReceived: true,
            systemAlerts: true,
            dailyReports: false
          },
          sms: {
            enabled: false,
            provider: 'twilio',
            apiKey: '',
            fromNumber: ''
          },
          push: {
            enabled: true,
            vapidKey: ''
          }
        },
        performance: {
          cacheEnabled: true,
          cacheDuration: 3600,
          compressionEnabled: true,
          minifyAssets: true,
          lazyLoading: true,
          maxFileSize: 10,
          sessionStorage: 'database'
        }
      });
      setUnsavedChanges(true);
      showNotification('تم إعادة تعيين الإعدادات', 'info');
    }
  };

  // تحديث إعداد معين
  const updateSetting = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setUnsavedChanges(true);
  };

  // تحديث إعداد متداخل
  const updateNestedSetting = (category, subCategory, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [subCategory]: {
          ...prev[category][subCategory],
          [key]: value
        }
      }
    }));
    setUnsavedChanges(true);
  };

  // إظهار الإشعارات
  const showNotification = (message, type) => {
    // يمكن تطوير نظام إشعارات أكثر تقدماً
    alert(message);
  };

  // تصدير الإعدادات
  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // استيراد الإعدادات
  const importSettings = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target.result);
          setSettings(importedSettings);
          setUnsavedChanges(true);
          showNotification('تم استيراد الإعدادات بنجاح', 'success');
        } catch (error) {
          showNotification('فشل في استيراد الإعدادات - ملف غير صالح', 'error');
        }
      };
      reader.readAsText(file);
    }
  };

  // تبويبات الإعدادات
  const tabs = [
    { id: 'general', label: 'عام', icon: '⚙️' },
    { id: 'appearance', label: 'المظهر', icon: '🎨' },
    { id: 'security', label: 'الأمان', icon: '🔒' },
    { id: 'integrations', label: 'التكاملات', icon: '🔗' },
    { id: 'notifications', label: 'الإشعارات', icon: '🔔' },
    { id: 'performance', label: 'الأداء', icon: '⚡' },
    { id: 'users', label: 'المستخدمين', icon: '👥' },
    { id: 'permissions', label: 'الأذونات', icon: '🛡️' },
    { id: 'backup', label: 'النسخ الاحتياطي', icon: '💾' },
    { id: 'email', label: 'البريد الإلكتروني', icon: '📧' },
    { id: 'templates', label: 'قوالب البريد', icon: '📝' },
    { id: 'logs', label: 'سجلات النظام', icon: '📋' },
    { id: 'maintenance', label: 'الصيانة', icon: '🔧' },
    { id: 'stats', label: 'الإحصائيات', icon: '📊' }
  ];

  return (
    <div className="system-settings">
      {/* رأس الصفحة */}
      <div className="settings-header">
        <div className="header-content">
          <h2>إعدادات النظام</h2>
          <p>إدارة وتخصيص إعدادات النظام والتطبيق</p>
        </div>
        
        <div className="header-actions">
          {lastSaved && (
            <div className="last-saved">
              آخر حفظ: {lastSaved}
            </div>
          )}
          
          <div className="action-buttons">
            <button 
              className="export-btn"
              onClick={exportSettings}
              title="تصدير الإعدادات"
            >
              📤 تصدير
            </button>
            
            <label className="import-btn" title="استيراد الإعدادات">
              📥 استيراد
              <input 
                type="file" 
                accept=".json" 
                onChange={importSettings}
                style={{ display: 'none' }}
              />
            </label>
            
            <button 
              className="reset-btn"
              onClick={resetSettings}
              title="إعادة تعيين"
            >
              🔄 إعادة تعيين
            </button>
            
            <button 
              className={`save-btn ${saving ? 'saving' : ''} ${unsavedChanges ? 'has-changes' : ''}`}
              onClick={saveSettings}
              disabled={saving || !unsavedChanges}
            >
              {saving ? '⏳ جاري الحفظ...' : '💾 حفظ الإعدادات'}
            </button>
          </div>
        </div>
      </div>

      {/* تحذير التغييرات غير المحفوظة */}
      {unsavedChanges && (
        <div className="unsaved-warning">
          ⚠️ يوجد تغييرات غير محفوظة. تأكد من حفظ الإعدادات قبل المغادرة.
        </div>
      )}

      <div className="settings-container">
        {/* شريط التبويبات */}
        <div className="settings-sidebar">
          <div className="tabs-list">
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className="tab-icon">{tab.icon}</span>
                <span className="tab-label">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* محتوى التبويبات */}
        <div className="settings-content">
          {activeTab === 'general' && (
            <GeneralSettings 
              settings={settings.general}
              onUpdate={(key, value) => updateSetting('general', key, value)}
            />
          )}
          
          {activeTab === 'appearance' && <ThemeManager />}
          
          {activeTab === 'security' && (
            <SecuritySettings 
              settings={settings.security}
              onUpdate={(key, value) => updateSetting('security', key, value)}
            />
          )}
          
          {activeTab === 'integrations' && (
            <IntegrationsSettings 
              settings={settings.integrations}
              onUpdate={(category, key, value) => updateNestedSetting('integrations', category, key, value)}
            />
          )}
          
          {activeTab === 'notifications' && (
            <NotificationsSettings 
              settings={settings.notifications}
              onUpdate={(category, key, value) => updateNestedSetting('notifications', category, key, value)}
            />
          )}
          
          {activeTab === 'performance' && (
            <PerformanceSettings 
              settings={settings.performance}
              onUpdate={(key, value) => updateSetting('performance', key, value)}
            />
          )}
          
          {activeTab === 'users' && <UserManagement />}
          {activeTab === 'permissions' && <PermissionsManager />}
          {activeTab === 'backup' && <BackupManager />}
          {activeTab === 'email' && <EmailSettings />}
          {activeTab === 'templates' && <EmailTemplates />}
          {activeTab === 'logs' && <SystemLogs />}
          {activeTab === 'maintenance' && <MaintenanceManager />}
          {activeTab === 'stats' && <SystemStats />}
        </div>
      </div>
    </div>
  );
};

// مكون الإعدادات العامة
const GeneralSettings = ({ settings, onUpdate }) => {
  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        onUpdate('companyLogo', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>الإعدادات العامة</h3>
        <p>إعدادات أساسية للنظام والشركة</p>
      </div>

      <div className="settings-grid">
        <div className="setting-item">
          <label>اسم الشركة</label>
          <input
            type="text"
            value={settings.companyName}
            onChange={(e) => onUpdate('companyName', e.target.value)}
            placeholder="اسم الشركة"
          />
        </div>

        <div className="setting-item">
          <label>شعار الشركة</label>
          <div className="logo-upload">
            {settings.companyLogo && (
              <div className="logo-preview">
                <img src={settings.companyLogo} alt="شعار الشركة" />
              </div>
            )}
            <label className="upload-btn">
              📁 اختيار شعار
              <input 
                type="file" 
                accept="image/*" 
                onChange={handleLogoUpload}
                style={{ display: 'none' }}
              />
            </label>
          </div>
        </div>

        <div className="setting-item">
          <label>المنطقة الزمنية</label>
          <select
            value={settings.timezone}
            onChange={(e) => onUpdate('timezone', e.target.value)}
          >
            <option value="Asia/Riyadh">الرياض (GMT+3)</option>
            <option value="Asia/Dubai">دبي (GMT+4)</option>
            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
            <option value="Asia/Qatar">قطر (GMT+3)</option>
          </select>
        </div>

        <div className="setting-item">
          <label>اللغة الافتراضية</label>
          <select
            value={settings.language}
            onChange={(e) => onUpdate('language', e.target.value)}
          >
            <option value="ar">العربية</option>
            <option value="en">English</option>
            <option value="fr">Français</option>
          </select>
        </div>

        <div className="setting-item">
          <label>العملة الافتراضية</label>
          <select
            value={settings.currency}
            onChange={(e) => onUpdate('currency', e.target.value)}
          >
            <option value="SAR">ريال سعودي (SAR)</option>
            <option value="USD">دولار أمريكي (USD)</option>
            <option value="EUR">يورو (EUR)</option>
            <option value="AED">درهم إماراتي (AED)</option>
          </select>
        </div>

        <div className="setting-item">
          <label>تنسيق التاريخ</label>
          <select
            value={settings.dateFormat}
            onChange={(e) => onUpdate('dateFormat', e.target.value)}
          >
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>

        <div className="setting-item">
          <label>المظهر</label>
          <select
            value={settings.theme}
            onChange={(e) => onUpdate('theme', e.target.value)}
          >
            <option value="light">فاتح</option>
            <option value="dark">داكن</option>
            <option value="auto">تلقائي</option>
          </select>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.autoSave}
              onChange={(e) => onUpdate('autoSave', e.target.checked)}
            />
            <span className="checkmark"></span>
            الحفظ التلقائي
          </label>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.notifications}
              onChange={(e) => onUpdate('notifications', e.target.checked)}
            />
            <span className="checkmark"></span>
            تفعيل الإشعارات
          </label>
        </div>
      </div>
    </div>
  );
};

// مكون إعدادات الأمان
const SecuritySettings = ({ settings, onUpdate }) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>إعدادات الأمان</h3>
        <p>إعدادات الحماية والأمان للنظام</p>
      </div>

      <div className="settings-grid">
        <div className="setting-item">
          <label>الحد الأدنى لطول كلمة المرور</label>
          <input
            type="number"
            min="6"
            max="20"
            value={settings.passwordMinLength}
            onChange={(e) => onUpdate('passwordMinLength', parseInt(e.target.value))}
          />
        </div>

        <div className="setting-item">
          <label>مهلة انتهاء الجلسة (بالدقائق)</label>
          <input
            type="number"
            min="5"
            max="120"
            value={settings.sessionTimeout}
            onChange={(e) => onUpdate('sessionTimeout', parseInt(e.target.value))}
          />
        </div>

        <div className="setting-item">
          <label>عدد محاولات تسجيل الدخول المسموحة</label>
          <input
            type="number"
            min="3"
            max="10"
            value={settings.loginAttempts}
            onChange={(e) => onUpdate('loginAttempts', parseInt(e.target.value))}
          />
        </div>

        <div className="setting-item">
          <label>مدة القفل بعد المحاولات الفاشلة (بالدقائق)</label>
          <input
            type="number"
            min="5"
            max="60"
            value={settings.lockoutDuration}
            onChange={(e) => onUpdate('lockoutDuration', parseInt(e.target.value))}
          />
        </div>

        <div className="setting-item">
          <label>قائمة IP المسموحة (اختياري)</label>
          <textarea
            value={settings.ipWhitelist}
            onChange={(e) => onUpdate('ipWhitelist', e.target.value)}
            placeholder="***********&#10;********"
            rows="3"
          />
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.requireSpecialChars}
              onChange={(e) => onUpdate('requireSpecialChars', e.target.checked)}
            />
            <span className="checkmark"></span>
            يتطلب أحرف خاصة في كلمة المرور
          </label>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.twoFactorAuth}
              onChange={(e) => onUpdate('twoFactorAuth', e.target.checked)}
            />
            <span className="checkmark"></span>
            تفعيل المصادقة الثنائية
          </label>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.auditLog}
              onChange={(e) => onUpdate('auditLog', e.target.checked)}
            />
            <span className="checkmark"></span>
            تسجيل سجل المراجعة
          </label>
        </div>
      </div>
    </div>
  );
};

// مكون إعدادات التكاملات
const IntegrationsSettings = ({ settings, onUpdate }) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>إعدادات التكاملات</h3>
        <p>ربط النظام مع الخدمات الخارجية</p>
      </div>

      {/* تكامل Amadeus */}
      <div className="integration-group">
        <div className="integration-header">
          <h4>🛫 Amadeus API</h4>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={settings.amadeus.enabled}
              onChange={(e) => onUpdate('amadeus', 'enabled', e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {settings.amadeus.enabled && (
          <div className="integration-settings">
            <div className="setting-item">
              <label>API Key</label>
              <input
                type="text"
                value={settings.amadeus.apiKey}
                onChange={(e) => onUpdate('amadeus', 'apiKey', e.target.value)}
                placeholder="أدخل API Key"
              />
            </div>
            <div className="setting-item">
              <label>API Secret</label>
              <input
                type="password"
                value={settings.amadeus.apiSecret}
                onChange={(e) => onUpdate('amadeus', 'apiSecret', e.target.value)}
                placeholder="أدخل API Secret"
              />
            </div>
            <div className="setting-item">
              <label>البيئة</label>
              <select
                value={settings.amadeus.environment}
                onChange={(e) => onUpdate('amadeus', 'environment', e.target.value)}
              >
                <option value="test">اختبار</option>
                <option value="production">إنتاج</option>
              </select>
            </div>
            <ConnectionTest 
              apiName="Amadeus"
              apiKey={settings.amadeus.apiKey}
            />
          </div>
        )}
      </div>

      {/* تكامل Sabre */}
      <div className="integration-group">
        <div className="integration-header">
          <h4>✈️ Sabre API</h4>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={settings.sabre.enabled}
              onChange={(e) => onUpdate('sabre', 'enabled', e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {settings.sabre.enabled && (
          <div className="integration-settings">
            <div className="setting-item">
              <label>اسم المستخدم</label>
              <input
                type="text"
                value={settings.sabre.username}
                onChange={(e) => onUpdate('sabre', 'username', e.target.value)}
                placeholder="اسم المستخدم"
              />
            </div>
            <div className="setting-item">
              <label>كلمة المرور</label>
              <input
                type="password"
                value={settings.sabre.password}
                onChange={(e) => onUpdate('sabre', 'password', e.target.value)}
                placeholder="كلمة المرور"
              />
            </div>
            <div className="setting-item">
              <label>PCC</label>
              <input
                type="text"
                value={settings.sabre.pcc}
                onChange={(e) => onUpdate('sabre', 'pcc', e.target.value)}
                placeholder="رمز PCC"
              />
            </div>
            <ConnectionTest 
              apiName="Sabre"
              apiKey={settings.sabre.username}
            />
          </div>
        )}
      </div>

      {/* تكامل المدفوعات */}
      <div className="integration-group">
        <div className="integration-header">
          <h4>💳 بوابات الدفع</h4>
        </div>
        
        {/* Stripe */}
        <div className="payment-provider">
          <div className="provider-header">
            <h5>Stripe</h5>
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={settings.payment.stripe.enabled}
                onChange={(e) => onUpdate('payment', 'stripe', { ...settings.payment.stripe, enabled: e.target.checked })}
              />
              <span className="slider"></span>
            </label>
          </div>
          
          {settings.payment.stripe.enabled && (
            <div className="provider-settings">
              <div className="setting-item">
                <label>Public Key</label>
                <input
                  type="text"
                  value={settings.payment.stripe.publicKey}
                  onChange={(e) => onUpdate('payment', 'stripe', { ...settings.payment.stripe, publicKey: e.target.value })}
                  placeholder="pk_test_..."
                />
              </div>
              <div className="setting-item">
                <label>Secret Key</label>
                <input
                  type="password"
                  value={settings.payment.stripe.secretKey}
                  onChange={(e) => onUpdate('payment', 'stripe', { ...settings.payment.stripe, secretKey: e.target.value })}
                  placeholder="sk_test_..."
                />
              </div>
            </div>
          )}
        </div>

        {/* PayPal */}
        <div className="payment-provider">
          <div className="provider-header">
            <h5>PayPal</h5>
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={settings.payment.paypal.enabled}
                onChange={(e) => onUpdate('payment', 'paypal', { ...settings.payment.paypal, enabled: e.target.checked })}
              />
              <span className="slider"></span>
            </label>
          </div>
          
          {settings.payment.paypal.enabled && (
            <div className="provider-settings">
              <div className="setting-item">
                <label>Client ID</label>
                <input
                  type="text"
                  value={settings.payment.paypal.clientId}
                  onChange={(e) => onUpdate('payment', 'paypal', { ...settings.payment.paypal, clientId: e.target.value })}
                  placeholder="Client ID"
                />
              </div>
              <div className="setting-item">
                <label>Client Secret</label>
                <input
                  type="password"
                  value={settings.payment.paypal.clientSecret}
                  onChange={(e) => onUpdate('payment', 'paypal', { ...settings.payment.paypal, clientSecret: e.target.value })}
                  placeholder="Client Secret"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// مكون إعدادات الإشعارات
const NotificationsSettings = ({ settings, onUpdate }) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>إعدادات الإشعارات</h3>
        <p>إدارة أنواع الإشعارات المختلفة</p>
      </div>

      {/* إشعارات البريد الإلكتروني */}
      <div className="notification-group">
        <h4>📧 إشعارات البريد الإلكتروني</h4>
        <div className="notification-options">
          <div className="notification-item">
            <label>
              <input
                type="checkbox"
                checked={settings.email.bookingConfirmation}
                onChange={(e) => onUpdate('email', 'bookingConfirmation', e.target.checked)}
              />
              <span className="checkmark"></span>
              تأكيد الحجز
            </label>
          </div>
          <div className="notification-item">
            <label>
              <input
                type="checkbox"
                checked={settings.email.paymentReceived}
                onChange={(e) => onUpdate('email', 'paymentReceived', e.target.checked)}
              />
              <span className="checkmark"></span>
              استلام الدفعة
            </label>
          </div>
          <div className="notification-item">
            <label>
              <input
                type="checkbox"
                checked={settings.email.systemAlerts}
                onChange={(e) => onUpdate('email', 'systemAlerts', e.target.checked)}
              />
              <span className="checkmark"></span>
              تنبيهات النظام
            </label>
          </div>
          <div className="notification-item">
            <label>
              <input
                type="checkbox"
                checked={settings.email.dailyReports}
                onChange={(e) => onUpdate('email', 'dailyReports', e.target.checked)}
              />
              <span className="checkmark"></span>
              التقارير اليومية
            </label>
          </div>
        </div>
      </div>

      {/* إشعارات SMS */}
      <div className="notification-group">
        <div className="group-header">
          <h4>📱 إشعارات SMS</h4>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={settings.sms.enabled}
              onChange={(e) => onUpdate('sms', 'enabled', e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {settings.sms.enabled && (
          <div className="sms-settings">
            <div className="setting-item">
              <label>مزود الخدمة</label>
              <select
                value={settings.sms.provider}
                onChange={(e) => onUpdate('sms', 'provider', e.target.value)}
              >
                <option value="twilio">Twilio</option>
                <option value="nexmo">Nexmo</option>
                <option value="local">مزود محلي</option>
              </select>
            </div>
            <div className="setting-item">
              <label>API Key</label>
              <input
                type="text"
                value={settings.sms.apiKey}
                onChange={(e) => onUpdate('sms', 'apiKey', e.target.value)}
                placeholder="API Key"
              />
            </div>
            <div className="setting-item">
              <label>رقم المرسل</label>
              <input
                type="text"
                value={settings.sms.fromNumber}
                onChange={(e) => onUpdate('sms', 'fromNumber', e.target.value)}
                placeholder="+966501234567"
              />
            </div>
          </div>
        )}
      </div>

      {/* الإشعارات الفورية */}
      <div className="notification-group">
        <div className="group-header">
          <h4>🔔 الإشعارات الفورية</h4>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={settings.push.enabled}
              onChange={(e) => onUpdate('push', 'enabled', e.target.checked)}
            />
            <span className="slider"></span>
          </label>
        </div>
        
        {settings.push.enabled && (
          <div className="push-settings">
            <div className="setting-item">
              <label>VAPID Key</label>
              <input
                type="text"
                value={settings.push.vapidKey}
                onChange={(e) => onUpdate('push', 'vapidKey', e.target.value)}
                placeholder="VAPID Public Key"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// مكون إعدادات الأداء
const PerformanceSettings = ({ settings, onUpdate }) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>إعدادات الأداء</h3>
        <p>تحسين أداء النظام والتطبيق</p>
      </div>

      <div className="settings-grid">
        <div className="setting-item">
          <label>مدة التخزين المؤقت (بالثواني)</label>
          <input
            type="number"
            min="300"
            max="86400"
            value={settings.cacheDuration}
            onChange={(e) => onUpdate('cacheDuration', parseInt(e.target.value))}
          />
        </div>

        <div className="setting-item">
          <label>الحد الأقصى لحجم الملف (MB)</label>
          <input
            type="number"
            min="1"
            max="100"
            value={settings.maxFileSize}
            onChange={(e) => onUpdate('maxFileSize', parseInt(e.target.value))}
          />
        </div>

        <div className="setting-item">
          <label>تخزين الجلسات</label>
          <select
            value={settings.sessionStorage}
            onChange={(e) => onUpdate('sessionStorage', e.target.value)}
          >
            <option value="database">قاعدة البيانات</option>
            <option value="redis">Redis</option>
            <option value="memory">الذاكرة</option>
          </select>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.cacheEnabled}
              onChange={(e) => onUpdate('cacheEnabled', e.target.checked)}
            />
            <span className="checkmark"></span>
            تفعيل التخزين المؤقت
          </label>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.compressionEnabled}
              onChange={(e) => onUpdate('compressionEnabled', e.target.checked)}
            />
            <span className="checkmark"></span>
            تفعيل ضغط البيانات
          </label>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.minifyAssets}
              onChange={(e) => onUpdate('minifyAssets', e.target.checked)}
            />
            <span className="checkmark"></span>
            ضغط ملفات CSS/JS
          </label>
        </div>

        <div className="setting-item checkbox-item">
          <label>
            <input
              type="checkbox"
              checked={settings.lazyLoading}
              onChange={(e) => onUpdate('lazyLoading', e.target.checked)}
            />
            <span className="checkmark"></span>
            التحميل التدريجي للصور
          </label>
        </div>
      </div>
    </div>
  );
};

export default SystemSettings;