/* Modern Layout Components Styles */

/* ===== MODERN HEADER ===== */
.modern-header {
  position: relative;
  background: var(--neutral-0);
  border-bottom: 1px solid var(--neutral-200);
  transition: all var(--transition-base);
  z-index: var(--z-sticky);
}

.modern-header--sticky {
  position: sticky;
  top: 0;
}

.modern-header--glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-header--scrolled {
  box-shadow: var(--shadow-lg);
  border-bottom-color: var(--neutral-300);
}

.modern-header__container {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ===== MODERN SIDEBAR ===== */
.modern-sidebar {
  position: fixed;
  top: 0;
  height: 100vh;
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-base);
  z-index: var(--z-fixed);
  overflow-y: auto;
  overflow-x: hidden;
}

.modern-sidebar--right {
  right: 0;
  border-left: 1px solid var(--neutral-200);
  border-right: none;
}

.modern-sidebar--left {
  left: 0;
  border-right: 1px solid var(--neutral-200);
  border-left: none;
}

.modern-sidebar--collapsed {
  width: 80px;
}

.modern-sidebar--overlay {
  position: fixed;
  width: 280px;
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-modal);
}

.modern-sidebar:not(.modern-sidebar--collapsed) {
  width: 280px;
}

.modern-sidebar__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
}

.modern-sidebar__content {
  padding: var(--space-6);
  height: 100%;
}

/* ===== MODERN MAIN ===== */
.modern-main {
  flex: 1;
  min-height: 100vh;
  background: var(--neutral-50);
  transition: all var(--transition-base);
}

.modern-main--padded {
  padding: var(--space-6);
}

/* ===== MODERN FOOTER ===== */
.modern-footer {
  background: var(--neutral-0);
  border-top: 1px solid var(--neutral-200);
  margin-top: auto;
}

.modern-footer--sticky {
  position: sticky;
  bottom: 0;
  z-index: var(--z-sticky);
}

.modern-footer__container {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* ===== MODERN CONTAINER ===== */
.modern-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.modern-container--sm {
  max-width: 640px;
}

.modern-container--md {
  max-width: 768px;
}

.modern-container--lg {
  max-width: 1024px;
}

.modern-container--xl {
  max-width: 1280px;
}

.modern-container--2xl {
  max-width: 1536px;
}

.modern-container--default {
  max-width: 1280px;
}

.modern-container--full {
  max-width: none;
}

.modern-container--centered {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

/* ===== MODERN GRID ===== */
.modern-grid {
  display: grid;
  width: 100%;
}

.modern-grid--cols-1 { grid-template-columns: repeat(1, 1fr); }
.modern-grid--cols-2 { grid-template-columns: repeat(2, 1fr); }
.modern-grid--cols-3 { grid-template-columns: repeat(3, 1fr); }
.modern-grid--cols-4 { grid-template-columns: repeat(4, 1fr); }
.modern-grid--cols-5 { grid-template-columns: repeat(5, 1fr); }
.modern-grid--cols-6 { grid-template-columns: repeat(6, 1fr); }
.modern-grid--cols-12 { grid-template-columns: repeat(12, 1fr); }

.modern-grid--gap-xs { gap: var(--space-1); }
.modern-grid--gap-sm { gap: var(--space-2); }
.modern-grid--gap-md { gap: var(--space-4); }
.modern-grid--gap-lg { gap: var(--space-6); }
.modern-grid--gap-xl { gap: var(--space-8); }

.modern-grid--responsive.modern-grid--cols-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.modern-grid--responsive.modern-grid--cols-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.modern-grid--responsive.modern-grid--cols-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.modern-grid--responsive.modern-grid--cols-5 {
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

.modern-grid--responsive.modern-grid--cols-6 {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

/* ===== MODERN FLEX ===== */
.modern-flex {
  display: flex;
}

.modern-flex--row { flex-direction: row; }
.modern-flex--col { flex-direction: column; }
.modern-flex--row-reverse { flex-direction: row-reverse; }
.modern-flex--col-reverse { flex-direction: column-reverse; }

.modern-flex--align-start { align-items: flex-start; }
.modern-flex--align-center { align-items: center; }
.modern-flex--align-end { align-items: flex-end; }
.modern-flex--align-stretch { align-items: stretch; }
.modern-flex--align-baseline { align-items: baseline; }

.modern-flex--justify-start { justify-content: flex-start; }
.modern-flex--justify-center { justify-content: center; }
.modern-flex--justify-end { justify-content: flex-end; }
.modern-flex--justify-between { justify-content: space-between; }
.modern-flex--justify-around { justify-content: space-around; }
.modern-flex--justify-evenly { justify-content: space-evenly; }

.modern-flex--gap-xs { gap: var(--space-1); }
.modern-flex--gap-sm { gap: var(--space-2); }
.modern-flex--gap-md { gap: var(--space-4); }
.modern-flex--gap-lg { gap: var(--space-6); }
.modern-flex--gap-xl { gap: var(--space-8); }

.modern-flex--wrap { flex-wrap: wrap; }

/* ===== MODERN SECTION ===== */
.modern-section {
  width: 100%;
}

.modern-section--bg-default {
  background: var(--neutral-50);
}

.modern-section--bg-white {
  background: var(--neutral-0);
}

.modern-section--bg-primary {
  background: var(--primary-50);
}

.modern-section--bg-secondary {
  background: var(--secondary-50);
}

.modern-section--bg-gradient {
  background: var(--gradient-primary);
  color: var(--neutral-0);
}

.modern-section--padding-none { padding: 0; }
.modern-section--padding-sm { padding: var(--space-4) 0; }
.modern-section--padding-md { padding: var(--space-6) 0; }
.modern-section--padding-lg { padding: var(--space-8) 0; }
.modern-section--padding-xl { padding: var(--space-12) 0; }

/* ===== MODERN PAGE LAYOUT ===== */
.modern-page-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: var(--neutral-50);
}

.modern-page-layout__body {
  display: flex;
  flex: 1;
  position: relative;
}

.modern-page-layout--with-sidebar .modern-main {
  margin-right: 280px;
}

.modern-page-layout--sidebar-left.modern-page-layout--with-sidebar .modern-main {
  margin-right: 0;
  margin-left: 280px;
}

.modern-page-layout--sidebar-collapsed .modern-main {
  margin-right: 80px;
}

.modern-page-layout--sidebar-left.modern-page-layout--sidebar-collapsed .modern-main {
  margin-right: 0;
  margin-left: 80px;
}

/* ===== MODERN CARD LAYOUT ===== */
.modern-card-layout {
  /* Inherits from modern-grid */
}

/* ===== MODERN DASHBOARD LAYOUT ===== */
.modern-dashboard-layout {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.modern-dashboard-layout__widgets {
  /* Widgets container */
}

.modern-dashboard-layout__content {
  flex: 1;
}

/* ===== MODERN SPLIT LAYOUT ===== */
.modern-split-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.modern-split-layout--horizontal {
  flex-direction: row;
}

.modern-split-layout--vertical {
  flex-direction: column;
}

.modern-split-layout__left,
.modern-split-layout__right {
  overflow: auto;
}

.modern-split-layout__divider {
  background: var(--neutral-300);
  cursor: col-resize;
  transition: background-color var(--transition-fast);
}

.modern-split-layout--horizontal .modern-split-layout__divider {
  width: 4px;
  cursor: col-resize;
}

.modern-split-layout--vertical .modern-split-layout__divider {
  height: 4px;
  cursor: row-resize;
}

.modern-split-layout__divider:hover {
  background: var(--primary-500);
}

.modern-split-layout--resizable .modern-split-layout__divider {
  position: relative;
}

.modern-split-layout--resizable .modern-split-layout__divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: var(--neutral-400);
  border-radius: var(--radius-full);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.modern-split-layout--resizable .modern-split-layout__divider:hover::before {
  opacity: 1;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .modern-page-layout--with-sidebar .modern-main {
    margin-right: 0;
    margin-left: 0;
  }
  
  .modern-page-layout--sidebar-left.modern-page-layout--with-sidebar .modern-main {
    margin-left: 0;
  }
  
  .modern-sidebar {
    transform: translateX(100%);
  }
  
  .modern-sidebar--left {
    transform: translateX(-100%);
  }
  
  .modern-sidebar--overlay {
    transform: translateX(0);
  }
  
  .modern-grid--responsive {
    grid-template-columns: 1fr;
  }
  
  .modern-split-layout--horizontal {
    flex-direction: column;
  }
  
  .modern-split-layout--horizontal .modern-split-layout__divider {
    width: 100%;
    height: 4px;
    cursor: row-resize;
  }
}

@media (max-width: 768px) {
  .modern-header__container {
    padding: var(--space-3) var(--space-4);
  }
  
  .modern-main--padded {
    padding: var(--space-4);
  }
  
  .modern-footer__container {
    padding: var(--space-4);
  }
  
  .modern-container {
    padding: 0 var(--space-3);
  }
  
  .modern-sidebar__content {
    padding: var(--space-4);
  }
  
  .modern-grid--gap-lg {
    gap: var(--space-4);
  }
  
  .modern-flex--gap-lg {
    gap: var(--space-4);
  }
  
  .modern-dashboard-layout {
    gap: var(--space-6);
  }
}

@media (max-width: 480px) {
  .modern-header__container {
    padding: var(--space-2) var(--space-3);
  }
  
  .modern-main--padded {
    padding: var(--space-3);
  }
  
  .modern-footer__container {
    padding: var(--space-3);
  }
  
  .modern-container {
    padding: 0 var(--space-2);
  }
  
  .modern-sidebar {
    width: 100vw;
  }
  
  .modern-sidebar--overlay {
    width: 100vw;
  }
  
  .modern-grid--gap-md {
    gap: var(--space-3);
  }
  
  .modern-flex--gap-md {
    gap: var(--space-3);
  }
  
  .modern-section--padding-lg {
    padding: var(--space-6) 0;
  }
  
  .modern-section--padding-xl {
    padding: var(--space-8) 0;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-header {
    background: var(--neutral-100);
    border-bottom-color: var(--neutral-300);
  }
  
  .modern-header--glass {
    background: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .modern-sidebar {
    background: var(--neutral-100);
    border-color: var(--neutral-300);
  }
  
  .modern-main {
    background: var(--neutral-50);
  }
  
  .modern-footer {
    background: var(--neutral-100);
    border-top-color: var(--neutral-300);
  }
  
  .modern-section--bg-default {
    background: var(--neutral-50);
  }
  
  .modern-section--bg-white {
    background: var(--neutral-100);
  }
  
  .modern-split-layout__divider {
    background: var(--neutral-600);
  }
  
  .modern-split-layout__divider:hover {
    background: var(--primary-400);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-header,
  .modern-sidebar,
  .modern-footer {
    display: none !important;
  }
  
  .modern-page-layout {
    display: block !important;
  }
  
  .modern-main {
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
  }
  
  .modern-split-layout {
    flex-direction: column !important;
  }
  
  .modern-split-layout__divider {
    display: none !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-header,
  .modern-sidebar,
  .modern-main,
  .modern-split-layout__divider {
    transition: none !important;
  }
}

/* Focus styles for keyboard navigation */
.modern-sidebar:focus-within {
  outline: 2px solid var(--primary-500);
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-header,
  .modern-sidebar,
  .modern-footer {
    border: 2px solid currentColor;
  }
  
  .modern-split-layout__divider {
    background: currentColor;
  }
}