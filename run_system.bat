@echo off
echo ========================================
echo   تشغيل نظام محاسبي لوكالات السفريات
echo   Running Travel Agency Accounting System
echo ========================================
echo.

:: التحقق من وجود الملفات المطلوبة
if not exist "backend\venv" (
    echo خطأ: لم يتم إعداد النظام بعد
    echo Error: System not set up yet
    echo يرجى تشغيل start.bat أولاً
    echo Please run start.bat first
    pause
    exit /b 1
)

if not exist "frontend\node_modules" (
    echo خطأ: لم يتم تثبيت حزم Frontend
    echo Error: Frontend packages not installed
    echo يرجى تشغيل start.bat أولاً
    echo Please run start.bat first
    pause
    exit /b 1
)

echo بدء تشغيل خادم Backend...
echo Starting Backend server...
start "Backend Server" cmd /k "cd backend && call venv\Scripts\activate.bat && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

:: انتظار قليل لبدء Backend
timeout /t 5 /nobreak >nul

echo بدء تشغيل خادم Frontend...
echo Starting Frontend server...
start "Frontend Server" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo تم بدء تشغيل النظام!
echo System started successfully!
echo ========================================
echo.
echo الروابط:
echo Links:
echo - التطبيق الرئيسي: http://localhost:3000
echo   Main Application: http://localhost:3000
echo.
echo - واجهة API: http://localhost:8000/api/docs
echo   API Interface: http://localhost:8000/api/docs
echo.
echo بيانات تسجيل الدخول:
echo Login credentials:
echo - اسم المستخدم: admin
echo   Username: admin
echo - كلمة المرور: admin123
echo   Password: admin123
echo.
echo لإيقاف النظام، أغلق نوافذ الخوادم
echo To stop the system, close the server windows
echo.
pause