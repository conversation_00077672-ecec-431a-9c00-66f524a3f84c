import React, { useState, useEffect } from 'react';
import ModernCard, { Card<PERSON>ody, CardHeader, CardTitle } from '../UI/ModernCard';
import ModernButton from '../UI/ModernButton';
import { ModernGrid, ModernFlex } from '../UI/ModernLayout';
import { ModernAlert } from '../UI/ModernAlert';
import { CurrencyManager, CURRENCIES } from './CurrencyManager';
import VoucherPrint from './VoucherPrint';
import './ReceiptVouchers.css';

const ReceiptVouchers = () => {
  const [vouchers, setVouchers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingVoucher, setEditingVoucher] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDateRange, setFilterDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState('success');
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [printVoucher, setPrintVoucher] = useState(null);
  const [exchangeRates, setExchangeRates] = useState({});

  const [formData, setFormData] = useState({
    voucherNumber: '',
    date: new Date().toISOString().split('T')[0],
    customerName: '',
    customerPhone: '',
    amount: '',
    currency: 'SAR',
    paymentMethod: 'cash',
    description: '',
    reference: '',
    accountCode: '1110', // حساب الصندوق
    status: 'active',
    notes: ''
  });

  // بيانات تجريبية وتحميل أسعار الصرف
  useEffect(() => {
    loadSampleData();
    loadExchangeRates();
  }, []);

  const loadExchangeRates = async () => {
    try {
      const rates = await CurrencyManager.getLatestRates();
      setExchangeRates(rates);
    } catch (error) {
      console.error('Error loading exchange rates:', error);
    }
  };

  const loadSampleData = () => {
    const sampleVouchers = [
      {
        id: 1,
        voucherNumber: 'RV-2024-001',
        date: '2024-12-19',
        customerName: 'أحمد محمد العلي',
        customerPhone: '+************',
        amount: 5000,
        currency: 'SAR',
        paymentMethod: 'cash',
        description: 'دفعة مقدمة لحجز عمرة',
        reference: 'BK-2024-001',
        accountCode: '1110',
        status: 'active',
        notes: 'تم الاستلام نقداً',
        createdBy: 'محمد أحمد',
        createdAt: '2024-12-19 10:30:00'
      },
      {
        id: 2,
        voucherNumber: 'RV-2024-002',
        date: '2024-12-18',
        customerName: 'فاطمة سالم',
        customerPhone: '+************',
        amount: 3500,
        currency: 'SAR',
        paymentMethod: 'bank_transfer',
        description: 'سداد فاتورة رحلة حج',
        reference: 'INV-2024-015',
        accountCode: '1120',
        status: 'active',
        notes: 'تحويل بنكي - البنك الأهلي',
        createdBy: 'سارة محمد',
        createdAt: '2024-12-18 14:20:00'
      },
      {
        id: 3,
        voucherNumber: 'RV-2024-003',
        date: '2024-12-17',
        customerName: 'عبدالله خالد',
        customerPhone: '+************',
        amount: 1000,
        currency: 'USD',
        paymentMethod: 'credit_card',
        description: 'دفع تذاكر طيران دولية',
        reference: 'TK-2024-089',
        accountCode: '1110',
        status: 'cancelled',
        notes: 'تم الإلغاء - خطأ في المبلغ',
        createdBy: 'أحمد سالم',
        createdAt: '2024-12-17 09:15:00'
      },
      {
        id: 4,
        voucherNumber: 'RV-2024-004',
        date: '2024-12-16',
        customerName: 'نورا عبدالرحمن',
        customerPhone: '+************',
        amount: 7200,
        currency: 'SAR',
        paymentMethod: 'check',
        description: 'سداد باقة سياحية كاملة',
        reference: 'PKG-2024-025',
        accountCode: '1110',
        status: 'active',
        notes: 'شيك رقم 123456 - بنك الراجحي',
        createdBy: 'خالد محمد',
        createdAt: '2024-12-16 16:45:00'
      },
      {
        id: 5,
        voucherNumber: 'RV-2024-005',
        date: '2024-12-15',
        customerName: 'محمد علي الحوثي',
        customerPhone: '+************',
        amount: 500000,
        currency: 'YER',
        paymentMethod: 'cash',
        description: 'دفعة مقدمة لحجز عمرة من اليمن',
        reference: 'YE-2024-001',
        accountCode: '1110',
        status: 'active',
        notes: 'استلام نقدي بالريال اليمني',
        createdBy: 'أحمد محمد',
        createdAt: '2024-12-15 11:20:00'
      }
    ];
    setVouchers(sampleVouchers);
  };

  const generateVoucherNumber = () => {
    const year = new Date().getFullYear();
    const count = vouchers.length + 1;
    return `RV-${year}-${count.toString().padStart(3, '0')}`;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const voucherData = {
        ...formData,
        id: editingVoucher ? editingVoucher.id : Date.now(),
        voucherNumber: editingVoucher ? editingVoucher.voucherNumber : generateVoucherNumber(),
        amount: parseFloat(formData.amount),
        createdBy: 'المستخدم الحالي',
        createdAt: new Date().toLocaleString('ar-SA')
      };

      if (editingVoucher) {
        setVouchers(prev => prev.map(v => v.id === editingVoucher.id ? voucherData : v));
        setAlertMessage('تم تحديث سند القبض بنجاح');
      } else {
        setVouchers(prev => [voucherData, ...prev]);
        setAlertMessage('تم إنشاء سند القبض بنجاح');
      }

      setAlertType('success');
      setShowAlert(true);
      resetForm();
      
      setTimeout(() => setShowAlert(false), 3000);
    } catch (error) {
      setAlertMessage('حدث خطأ في حفظ البيانات');
      setAlertType('error');
      setShowAlert(true);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      voucherNumber: '',
      date: new Date().toISOString().split('T')[0],
      customerName: '',
      customerPhone: '',
      amount: '',
      currency: 'SAR',
      paymentMethod: 'cash',
      description: '',
      reference: '',
      accountCode: '1110',
      status: 'active',
      notes: ''
    });
    setEditingVoucher(null);
    setShowForm(false);
  };

  const handlePrint = (voucher) => {
    setPrintVoucher(voucher);
    setShowPrintModal(true);
  };

  const closePrintModal = () => {
    setShowPrintModal(false);
    setPrintVoucher(null);
  };

  const handleEdit = (voucher) => {
    setFormData({
      ...voucher,
      amount: voucher.amount.toString()
    });
    setEditingVoucher(voucher);
    setShowForm(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا السند؟')) {
      setVouchers(prev => prev.filter(v => v.id !== id));
      setAlertMessage('تم حذف سند القبض بنجاح');
      setAlertType('success');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    }
  };

  const handleCancel = (id) => {
    if (window.confirm('هل أنت متأكد من إلغاء هذا السند؟')) {
      setVouchers(prev => prev.map(v => 
        v.id === id ? { ...v, status: 'cancelled' } : v
      ));
      setAlertMessage('تم إلغاء سند القبض');
      setAlertType('warning');
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    }
  };

  const filteredVouchers = vouchers.filter(voucher => {
    const matchesSearch = voucher.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voucher.voucherNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voucher.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || voucher.status === filterStatus;
    
    const matchesDateRange = (!filterDateRange.startDate || voucher.date >= filterDateRange.startDate) &&
                            (!filterDateRange.endDate || voucher.date <= filterDateRange.endDate);
    
    return matchesSearch && matchesStatus && matchesDateRange;
  });

  const totalAmount = filteredVouchers
    .filter(v => v.status === 'active')
    .reduce((sum, voucher) => {
      // تحويل جميع المبالغ إلى الريال السعودي للمجموع
      const amountInSAR = CurrencyManager.convertCurrency(voucher.amount, voucher.currency, 'SAR');
      return sum + amountInSAR;
    }, 0);

  const getTotalByCurrency = (currencyCode) => {
    return filteredVouchers
      .filter(v => v.status === 'active' && v.currency === currencyCode)
      .reduce((sum, voucher) => sum + voucher.amount, 0);
  };

  const paymentMethods = {
    cash: 'نقدي',
    bank_transfer: 'تحويل بنكي',
    credit_card: 'بطاقة ائتمان',
    check: 'شيك'
  };

  const statusOptions = {
    active: 'نشط',
    cancelled: 'ملغي'
  };

  return (
    <div className="receipt-vouchers">
      {/* Alert */}
      {showAlert && (
        <ModernAlert
          type={alertType}
          title={alertType === 'success' ? 'نجح العملية' : 'تحذير'}
          message={alertMessage}
          onClose={() => setShowAlert(false)}
        />
      )}

      {/* Header */}
      <div className="vouchers-header">
        <ModernFlex align="center" justify="between" className="header-content">
          <div className="header-title">
            <h1>سندات القبض</h1>
            <p>إدارة سندات استلام الأموال من العملاء</p>
          </div>
          
          <div className="header-actions">
            <ModernButton 
              variant="primary" 
              onClick={() => setShowForm(true)}
              disabled={showForm}
            >
              <span className="btn-icon">➕</span>
              سند قبض جديد
            </ModernButton>
          </div>
        </ModernFlex>
      </div>

      {/* Statistics */}
      <ModernGrid cols={4} gap="lg" responsive className="vouchers-stats">
        <ModernCard className="stat-card">
          <CardBody>
            <div className="stat-content">
              <div className="stat-icon">💰</div>
              <div className="stat-details">
                <div className="stat-value">{CurrencyManager.formatAmount(totalAmount, 'SAR')}</div>
                <div className="stat-label">إجمالي المبالغ المستلمة (ر.س)</div>
                <div className="currency-breakdown">
                  {Object.keys(CURRENCIES).map(currencyCode => {
                    const total = getTotalByCurrency(currencyCode);
                    if (total > 0) {
                      return (
                        <div key={currencyCode} className="currency-item">
                          {CURRENCIES[currencyCode].flag} {CurrencyManager.formatAmount(total, currencyCode)}
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            </div>
          </CardBody>
        </ModernCard>

        <ModernCard className="stat-card">
          <CardBody>
            <div className="stat-content">
              <div className="stat-icon">📄</div>
              <div className="stat-details">
                <div className="stat-value">{vouchers.filter(v => v.status === 'active').length}</div>
                <div className="stat-label">السندات النشطة</div>
              </div>
            </div>
          </CardBody>
        </ModernCard>

        <ModernCard className="stat-card">
          <CardBody>
            <div className="stat-content">
              <div className="stat-icon">❌</div>
              <div className="stat-details">
                <div className="stat-value">{vouchers.filter(v => v.status === 'cancelled').length}</div>
                <div className="stat-label">السندات الملغية</div>
              </div>
            </div>
          </CardBody>
        </ModernCard>

        <ModernCard className="stat-card">
          <CardBody>
            <div className="stat-content">
              <div className="stat-icon">📊</div>
              <div className="stat-details">
                <div className="stat-value">{vouchers.length}</div>
                <div className="stat-label">إجمالي السندات</div>
              </div>
            </div>
          </CardBody>
        </ModernCard>
      </ModernGrid>

      {/* Form */}
      {showForm && (
        <ModernCard className="voucher-form-card">
          <CardHeader>
            <CardTitle>
              {editingVoucher ? 'تعديل سند القبض' : 'سند قبض جديد'}
            </CardTitle>
          </CardHeader>
          <CardBody>
            <form onSubmit={handleSubmit} className="voucher-form">
              <ModernGrid cols={3} gap="lg" responsive>
                
                <div className="form-group">
                  <label>رقم السند</label>
                  <input
                    type="text"
                    value={editingVoucher ? editingVoucher.voucherNumber : 'سيتم إنشاؤه تلقائياً'}
                    disabled
                    className="form-input disabled"
                  />
                </div>

                <div className="form-group">
                  <label>التاريخ *</label>
                  <input
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    required
                    className="form-input"
                  />
                </div>

                <div className="form-group">
                  <label>طريقة الدفع *</label>
                  <select
                    value={formData.paymentMethod}
                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                    required
                    className="form-input"
                  >
                    {Object.entries(paymentMethods).map(([key, value]) => (
                      <option key={key} value={key}>{value}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>اسم العميل *</label>
                  <input
                    type="text"
                    value={formData.customerName}
                    onChange={(e) => handleInputChange('customerName', e.target.value)}
                    required
                    className="form-input"
                    placeholder="أدخل اسم العميل"
                  />
                </div>

                <div className="form-group">
                  <label>رقم الهاتف</label>
                  <input
                    type="tel"
                    value={formData.customerPhone}
                    onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                    className="form-input"
                    placeholder="+966xxxxxxxxx"
                  />
                </div>

                <div className="form-group">
                  <label>المبلغ *</label>
                  <input
                    type="number"
                    value={formData.amount}
                    onChange={(e) => handleInputChange('amount', e.target.value)}
                    required
                    min="0"
                    step="0.01"
                    className="form-input"
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>العملة *</label>
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    required
                    className="form-input"
                  >
                    {Object.values(CURRENCIES).map(currency => (
                      <option key={currency.code} value={currency.code}>
                        {currency.flag} {currency.name} ({currency.symbol})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>البيان *</label>
                  <input
                    type="text"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    required
                    className="form-input"
                    placeholder="وصف سبب الاستلام"
                  />
                </div>

                <div className="form-group">
                  <label>المرجع</label>
                  <input
                    type="text"
                    value={formData.reference}
                    onChange={(e) => handleInputChange('reference', e.target.value)}
                    className="form-input"
                    placeholder="رقم الفاتورة أو الحجز"
                  />
                </div>

                <div className="form-group">
                  <label>رمز الحساب</label>
                  <select
                    value={formData.accountCode}
                    onChange={(e) => handleInputChange('accountCode', e.target.value)}
                    className="form-input"
                  >
                    <option value="1110">1110 - الصندوق</option>
                    <option value="1120">1120 - البنك الأهلي</option>
                    <option value="1121">1121 - بنك الراجحي</option>
                    <option value="1122">1122 - بنك الرياض</option>
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    className="form-input"
                    rows="3"
                    placeholder="ملاحظات إضافية"
                  />
                </div>
              </ModernGrid>

              <div className="form-actions">
                <ModernButton type="button" variant="outline" onClick={resetForm}>
                  إلغاء
                </ModernButton>
                <ModernButton type="submit" variant="primary" disabled={isLoading}>
                  {isLoading ? 'جاري الحفظ...' : (editingVoucher ? 'تحديث' : 'حفظ')}
                </ModernButton>
              </div>
            </form>
          </CardBody>
        </ModernCard>
      )}

      {/* Filters */}
      <ModernCard className="filters-card">
        <CardBody>
          <ModernGrid cols={4} gap="lg" responsive className="filters-grid">
            
            <div className="filter-group">
              <label>البحث</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input"
                placeholder="ابحث بالاسم أو رقم السند..."
              />
            </div>

            <div className="filter-group">
              <label>الحالة</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="form-input"
              >
                <option value="all">جميع الحالات</option>
                {Object.entries(statusOptions).map(([key, value]) => (
                  <option key={key} value={key}>{value}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>من تاريخ</label>
              <input
                type="date"
                value={filterDateRange.startDate}
                onChange={(e) => setFilterDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                className="form-input"
              />
            </div>

            <div className="filter-group">
              <label>إلى تاريخ</label>
              <input
                type="date"
                value={filterDateRange.endDate}
                onChange={(e) => setFilterDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                className="form-input"
              />
            </div>
          </ModernGrid>
        </CardBody>
      </ModernCard>

      {/* Vouchers List */}
      <ModernCard className="vouchers-list-card">
        <CardHeader>
          <CardTitle>قائمة سندات القبض ({filteredVouchers.length})</CardTitle>
        </CardHeader>
        <CardBody>
          {filteredVouchers.length > 0 ? (
            <div className="vouchers-table-container">
              <table className="vouchers-table">
                <thead>
                  <tr>
                    <th>رقم السند</th>
                    <th>التاريخ</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>البيان</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredVouchers.map(voucher => (
                    <tr key={voucher.id} className={voucher.status === 'cancelled' ? 'cancelled-row' : ''}>
                      <td className="voucher-number">{voucher.voucherNumber}</td>
                      <td>{new Date(voucher.date).toLocaleDateString('ar-SA')}</td>
                      <td>
                        <div className="customer-info">
                          <div className="customer-name">{voucher.customerName}</div>
                          {voucher.customerPhone && (
                            <div className="customer-phone">{voucher.customerPhone}</div>
                          )}
                        </div>
                      </td>
                      <td className="amount">
                        <div className="amount-display">
                          <div className="primary-amount">
                            {CurrencyManager.formatAmount(voucher.amount, voucher.currency)}
                          </div>
                          {voucher.currency !== 'SAR' && (
                            <div className="converted-amount">
                              ≈ {CurrencyManager.formatAmount(
                                CurrencyManager.convertCurrency(voucher.amount, voucher.currency, 'SAR'),
                                'SAR'
                              )}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className={`payment-method payment-method--${voucher.paymentMethod}`}>
                          {paymentMethods[voucher.paymentMethod]}
                        </span>
                      </td>
                      <td className="description">{voucher.description}</td>
                      <td>
                        <span className={`status status--${voucher.status}`}>
                          {statusOptions[voucher.status]}
                        </span>
                      </td>
                      <td>
                        <div className="actions">
                          {voucher.status === 'active' && (
                            <>
                              <button
                                className="action-btn edit-btn"
                                onClick={() => handleEdit(voucher)}
                                title="تعديل"
                              >
                                ✏️
                              </button>
                              <button
                                className="action-btn cancel-btn"
                                onClick={() => handleCancel(voucher.id)}
                                title="إلغاء"
                              >
                                ❌
                              </button>
                            </>
                          )}
                          <button
                            className="action-btn print-btn"
                            onClick={() => handlePrint(voucher)}
                            title="طباعة"
                          >
                            🖨️
                          </button>
                          <button
                            className="action-btn delete-btn"
                            onClick={() => handleDelete(voucher.id)}
                            title="حذف"
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="no-data">
              <div className="no-data-icon">📄</div>
              <h3>لا توجد سندات قبض</h3>
              <p>لم يتم العثور على سندات قبض تطابق معايير البحث</p>
            </div>
          )}
        </CardBody>
      </ModernCard>

      {/* Print Modal */}
      {showPrintModal && printVoucher && (
        <VoucherPrint
          voucher={printVoucher}
          type="receipt"
          onClose={closePrintModal}
        />
      )}
    </div>
  );
};

export default ReceiptVouchers;