import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { KeyboardShortcutsProvider, useShortcuts } from '../../../components/UI/KeyboardShortcuts';

// 🧪 اختبارات نظام اختصارات لوحة المفاتيح

// مكون اختبار لاستخدام الهوك
const TestComponent = () => {
  const { registerShortcut, unregisterShortcut, getShortcuts } = useShortcuts();
  const [shortcuts, setShortcuts] = React.useState([]);
  
  React.useEffect(() => {
    // تسجيل اختصارات للاختبار
    registerShortcut('ctrl+s', 'save', 'حفظ الملف', 'general');
    registerShortcut('ctrl+n', 'new', 'ملف جديد', 'general');
    registerShortcut('ctrl+shift+d', 'delete', 'حذف', 'actions');
    
    // إعداد معالج الأحداث
    const handleShortcutAction = (event) => {
      const action = event.detail.action;
      setShortcuts(prev => [...prev, action]);
    };
    
    document.addEventListener('shortcutAction', handleShortcutAction);
    
    return () => {
      document.removeEventListener('shortcutAction', handleShortcutAction);
      unregisterShortcut('ctrl+s');
      unregisterShortcut('ctrl+n');
      unregisterShortcut('ctrl+shift+d');
    };
  }, [registerShortcut, unregisterShortcut]);
  
  const handleGetShortcuts = () => {
    const allShortcuts = getShortcuts();
    setShortcuts(allShortcuts.map(s => s.action));
  };
  
  return (
    <div>
      <button onClick={handleGetShortcuts}>Get Shortcuts</button>
      <div data-testid="shortcuts-list">
        {shortcuts.map((shortcut, index) => (
          <div key={index}>{shortcut}</div>
        ))}
      </div>
    </div>
  );
};

const TestWrapper = ({ children }) => (
  <KeyboardShortcutsProvider>
    {children}
  </KeyboardShortcutsProvider>
);

describe('KeyboardShortcuts', () => {
  beforeEach(() => {
    // تنظيف DOM قبل كل اختبار
    document.body.innerHTML = '';
    // إعادة تعيين معالجات الأحداث
    document.removeEventListener('keydown', () => {});
  });

  describe('تسجيل الاختصارات', () => {
    test('يجب تسجيل اختصار جديد بنجاح', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const getShortcutsButton = screen.getByText('Get Shortcuts');
      fireEvent.click(getShortcutsButton);

      await waitFor(() => {
        expect(screen.getByText('save')).toBeInTheDocument();
        expect(screen.getByText('new')).toBeInTheDocument();
        expect(screen.getByText('delete')).toBeInTheDocument();
      });
    });

    test('يجب منع تسجيل اختصارات مكررة', () => {
      const TestDuplicateComponent = () => {
        const { registerShortcut } = useShortcuts();
        const [error, setError] = React.useState('');
        
        const handleRegister = () => {
          try {
            registerShortcut('ctrl+s', 'save1', 'حفظ 1', 'general');
            registerShortcut('ctrl+s', 'save2', 'حفظ 2', 'general');
          } catch (err) {
            setError(err.message);
          }
        };
        
        return (
          <div>
            <button onClick={handleRegister}>Register Duplicate</button>
            {error && <div data-testid="error">{error}</div>}
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestDuplicateComponent />
        </TestWrapper>
      );

      const button = screen.getByText('Register Duplicate');
      fireEvent.click(button);

      // يجب أن يظهر خطأ أو تحذير
      // (هذا يعتمد على تنفيذ المكون)
    });
  });

  describe('تنفيذ الاختصارات', () => {
    test('يجب تنفيذ اختصار Ctrl+S', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // محاكاة ضغط Ctrl+S
      fireEvent.keyDown(document, {
        key: 's',
        code: 'KeyS',
        ctrlKey: true
      });

      await waitFor(() => {
        expect(screen.getByText('save')).toBeInTheDocument();
      });
    });

    test('يجب تنفيذ اختصار Ctrl+N', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // محاكاة ضغط Ctrl+N
      fireEvent.keyDown(document, {
        key: 'n',
        code: 'KeyN',
        ctrlKey: true
      });

      await waitFor(() => {
        expect(screen.getByText('new')).toBeInTheDocument();
      });
    });

    test('يجب تنفيذ اختصار Ctrl+Shift+D', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // محاكاة ضغط Ctrl+Shift+D
      fireEvent.keyDown(document, {
        key: 'd',
        code: 'KeyD',
        ctrlKey: true,
        shiftKey: true
      });

      await waitFor(() => {
        expect(screen.getByText('delete')).toBeInTheDocument();
      });
    });

    test('يجب عدم تنفيذ اختصار غير مسجل', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // محاكاة ضغط Ctrl+X (غير مسجل)
      fireEvent.keyDown(document, {
        key: 'x',
        code: 'KeyX',
        ctrlKey: true
      });

      // انتظار قصير للتأكد من عدم حدوث شيء
      await new Promise(resolve => setTimeout(resolve, 100));

      const shortcutsList = screen.getByTestId('shortcuts-list');
      expect(shortcutsList).toBeEmptyDOMElement();
    });
  });

  describe('إلغاء تسجيل الاختصارات', () => {
    test('يجب إلغاء تسجيل اختصار بنجاح', async () => {
      const TestUnregisterComponent = () => {
        const { registerShortcut, unregisterShortcut } = useShortcuts();
        const [registered, setRegistered] = React.useState(false);
        
        const handleRegister = () => {
          registerShortcut('ctrl+t', 'test', 'اختبار', 'test');
          setRegistered(true);
        };
        
        const handleUnregister = () => {
          unregisterShortcut('ctrl+t');
          setRegistered(false);
        };
        
        return (
          <div>
            <button onClick={handleRegister}>Register</button>
            <button onClick={handleUnregister}>Unregister</button>
            <div data-testid="status">{registered ? 'مسجل' : 'غير مسجل'}</div>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestUnregisterComponent />
        </TestWrapper>
      );

      const registerButton = screen.getByText('Register');
      const unregisterButton = screen.getByText('Unregister');

      // تسجيل الاختصار
      fireEvent.click(registerButton);
      expect(screen.getByText('مسجل')).toBeInTheDocument();

      // إلغاء تسجيل الاختصار
      fireEvent.click(unregisterButton);
      expect(screen.getByText('غير مسجل')).toBeInTheDocument();
    });
  });

  describe('عرض قائمة الاختصارات', () => {
    test('يجب عرض قائمة الاختصارات المسجلة', async () => {
      const TestListComponent = () => {
        const { registerShortcut, getShortcuts } = useShortcuts();
        const [shortcuts, setShortcuts] = React.useState([]);
        
        React.useEffect(() => {
          registerShortcut('ctrl+1', 'action1', 'إجراء 1', 'numbers');
          registerShortcut('ctrl+2', 'action2', 'إجراء 2', 'numbers');
        }, [registerShortcut]);
        
        const handleShowShortcuts = () => {
          const allShortcuts = getShortcuts();
          setShortcuts(allShortcuts);
        };
        
        return (
          <div>
            <button onClick={handleShowShortcuts}>Show Shortcuts</button>
            <div data-testid="shortcuts-display">
              {shortcuts.map((shortcut, index) => (
                <div key={index} data-testid={`shortcut-${index}`}>
                  {shortcut.combination} - {shortcut.description}
                </div>
              ))}
            </div>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestListComponent />
        </TestWrapper>
      );

      const showButton = screen.getByText('Show Shortcuts');
      fireEvent.click(showButton);

      await waitFor(() => {
        expect(screen.getByText(/ctrl\+1.*إجراء 1/)).toBeInTheDocument();
        expect(screen.getByText(/ctrl\+2.*إجراء 2/)).toBeInTheDocument();
      });
    });
  });

  describe('تجميع الاختصارات', () => {
    test('يجب تجميع الاختصارات حسب الفئة', async () => {
      const TestGroupComponent = () => {
        const { registerShortcut, getShortcutsByCategory } = useShortcuts();
        const [groups, setGroups] = React.useState({});
        
        React.useEffect(() => {
          registerShortcut('ctrl+s', 'save', 'حفظ', 'file');
          registerShortcut('ctrl+o', 'open', 'فتح', 'file');
          registerShortcut('ctrl+c', 'copy', 'نسخ', 'edit');
          registerShortcut('ctrl+v', 'paste', 'لصق', 'edit');
        }, [registerShortcut]);
        
        const handleGroupShortcuts = () => {
          const fileShortcuts = getShortcutsByCategory('file');
          const editShortcuts = getShortcutsByCategory('edit');
          setGroups({ file: fileShortcuts, edit: editShortcuts });
        };
        
        return (
          <div>
            <button onClick={handleGroupShortcuts}>Group Shortcuts</button>
            <div data-testid="file-group">
              File: {groups.file?.length || 0}
            </div>
            <div data-testid="edit-group">
              Edit: {groups.edit?.length || 0}
            </div>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestGroupComponent />
        </TestWrapper>
      );

      const groupButton = screen.getByText('Group Shortcuts');
      fireEvent.click(groupButton);

      await waitFor(() => {
        expect(screen.getByText('File: 2')).toBeInTheDocument();
        expect(screen.getByText('Edit: 2')).toBeInTheDocument();
      });
    });
  });

  describe('منع السلوك الافتراضي', () => {
    test('يجب منع السلوك الافتراضي للاختصارات المسجلة', async () => {
      const TestPreventDefaultComponent = () => {
        const { registerShortcut } = useShortcuts();
        
        React.useEffect(() => {
          registerShortcut('ctrl+s', 'save', 'حفظ', 'general');
        }, [registerShortcut]);
        
        return <div>Test Component</div>;
      };

      render(
        <TestWrapper>
          <TestPreventDefaultComponent />
        </TestWrapper>
      );

      const mockPreventDefault = jest.fn();
      
      // محاكاة حدث لوحة المفاتيح مع preventDefault
      const keyEvent = new KeyboardEvent('keydown', {
        key: 's',
        ctrlKey: true,
        bubbles: true
      });
      
      keyEvent.preventDefault = mockPreventDefault;
      
      document.dispatchEvent(keyEvent);

      // يجب استدعاء preventDefault
      expect(mockPreventDefault).toHaveBeenCalled();
    });
  });

  describe('إمكانية الوصول', () => {
    test('يجب عرض مساعدة الاختصارات', async () => {
      const TestHelpComponent = () => {
        const { registerShortcut, showHelp } = useShortcuts();
        
        React.useEffect(() => {
          registerShortcut('ctrl+h', 'help', 'مساعدة', 'general');
        }, [registerShortcut]);
        
        return (
          <div>
            <button onClick={showHelp}>Show Help</button>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestHelpComponent />
        </TestWrapper>
      );

      const helpButton = screen.getByText('Show Help');
      fireEvent.click(helpButton);

      // يجب عرض مودال المساعدة أو قائمة الاختصارات
      await waitFor(() => {
        // هذا يعتمد على تنفيذ showHelp
        expect(document.body).toContainHTML('help');
      });
    });
  });

  describe('معالجة الأخطاء', () => {
    test('يجب معالجة الاختصارات غير الصالحة', () => {
      const TestInvalidComponent = () => {
        const { registerShortcut } = useShortcuts();
        const [error, setError] = React.useState('');
        
        const handleInvalidShortcut = () => {
          try {
            registerShortcut('', 'invalid', 'غير صالح', 'test');
          } catch (err) {
            setError('خطأ في الاختصار');
          }
        };
        
        return (
          <div>
            <button onClick={handleInvalidShortcut}>Invalid Shortcut</button>
            {error && <div data-testid="error">{error}</div>}
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestInvalidComponent />
        </TestWrapper>
      );

      const button = screen.getByText('Invalid Shortcut');
      fireEvent.click(button);

      expect(screen.getByTestId('error')).toHaveTextContent('خطأ في الاختصار');
    });
  });
});
