import React, { useState, useEffect } from 'react';

const PurchasesPage = () => {
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSupplier, setFilterSupplier] = useState('all');

  const [newPurchase, setNewPurchase] = useState({
    supplierName: '',
    supplierContact: '',
    serviceType: 'work_visa',
    description: '',
    purchaseDate: new Date().toISOString().split('T')[0],
    cost: '',
    quantity: 1,
    totalCost: '',
    currency: 'SAR',
    paymentMethod: 'bank_transfer',
    status: 'pending',
    dueDate: '',
    notes: ''
  });

  const [purchaseStats, setPurchaseStats] = useState({
    totalPurchases: 0,
    totalCost: 0,
    pendingPayments: 0,
    completedPurchases: 0,
    todayPurchases: 0,
    monthPurchases: 0
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      const mockPurchases = [
        {
          id: 1,
          supplierName: 'الخطوط السعودية',
          supplierContact: '*********',
          serviceType: 'flight_ticket',
          description: 'تذاكر طيران الرياض - دبي',
          purchaseDate: '2024-01-15',
          cost: 1200,
          quantity: 10,
          totalCost: 12000,
          currency: 'SAR',
          paymentMethod: 'bank_transfer',
          status: 'completed',
          dueDate: '2024-01-20',
          notes: 'تذاكر درجة اقتصادية',
          purchaseOrder: 'PO-2024-001'
        },
        {
          id: 2,
          supplierName: 'مكتب الفيزا السريعة',
          supplierContact: '*********',
          serviceType: 'work_visa',
          description: 'معاملة تأشيرة عمل - دولة الإمارات',
          purchaseDate: '2024-01-14',
          cost: 2500,
          quantity: 5,
          totalCost: 12500,
          currency: 'SAR',
          paymentMethod: 'credit',
          status: 'pending',
          dueDate: '2024-01-25',
          notes: 'تأشيرة عمل لمدة سنتين',
          purchaseOrder: 'PO-2024-002'
        },
        {
          id: 3,
          supplierName: 'شركة النقل السياحي',
          supplierContact: '*********',
          serviceType: 'bus_booking',
          description: 'حافلات سياحية - رحلة جماعية',
          purchaseDate: '2024-01-13',
          cost: 800,
          quantity: 3,
          totalCost: 2400,
          currency: 'SAR',
          paymentMethod: 'cash',
          status: 'completed',
          dueDate: '2024-01-18',
          notes: 'حافلات مكيفة 50 راكب',
          purchaseOrder: 'PO-2024-003'
        },
        {
          id: 4,
          supplierName: 'مكتب الحج والعمرة',
          supplierContact: '*********',
          serviceType: 'umrah_visa',
          description: 'استخراج تأشيرة عمرة',
          purchaseDate: '2024-01-12',
          cost: 500,
          quantity: 8,
          totalCost: 4000,
          currency: 'SAR',
          paymentMethod: 'bank_transfer',
          status: 'processing',
          dueDate: '2024-01-22',
          notes: 'تأشيرة عمرة لمدة 30 يوم',
          purchaseOrder: 'PO-2024-004'
        },
        {
          id: 5,
          supplierName: 'مكتب الجوازات السريع',
          supplierContact: '*********',
          serviceType: 'passport',
          description: 'تجديد جوازات سفر',
          purchaseDate: '2024-01-11',
          cost: 150,
          quantity: 20,
          totalCost: 3000,
          currency: 'YER',
          paymentMethod: 'credit',
          status: 'completed',
          dueDate: '2024-01-16',
          notes: 'تجديد جوازات سفر يمنية',
          purchaseOrder: 'PO-2024-005'
        },
        {
          id: 6,
          supplierName: 'مكتب الفيزا الدولية',
          supplierContact: '*********',
          serviceType: 'three_month_visa',
          description: 'فيزا أبو ثلاثة أشهر - أوروبا',
          purchaseDate: '2024-01-10',
          cost: 800,
          quantity: 3,
          totalCost: 2400,
          currency: 'USD',
          paymentMethod: 'bank_transfer',
          status: 'pending',
          dueDate: '2024-01-20',
          notes: 'فيزا شنغن متعددة الدخول',
          purchaseOrder: 'PO-2024-006'
        },
        {
          id: 7,
          supplierName: 'مكتب الحج والعمرة الذهبي',
          supplierContact: '*********',
          serviceType: 'hajj_visa',
          description: 'تأشيرة حج - موسم 1445',
          purchaseDate: '2024-01-09',
          cost: 1500,
          quantity: 10,
          totalCost: 15000,
          currency: 'SAR',
          paymentMethod: 'cash',
          status: 'completed',
          dueDate: '2024-01-15',
          notes: 'تأشيرة حج مع خدمات إضافية',
          purchaseOrder: 'PO-2024-007'
        },
        {
          id: 8,
          supplierName: 'مكتب الإقامة السريع',
          supplierContact: '*********',
          serviceType: 'residence_visa',
          description: 'فيزا أبو إقامة سنة - الإمارات',
          purchaseDate: '2024-01-08',
          cost: 1200,
          quantity: 2,
          totalCost: 2400,
          currency: 'USD',
          paymentMethod: 'credit',
          status: 'processing',
          dueDate: '2024-01-25',
          notes: 'إقامة عمل لمدة سنة',
          purchaseOrder: 'PO-2024-008'
        }
      ];

      setPurchases(mockPurchases);

      // حساب الإحصائيات حسب العملة
      const currencyStats = {};
      
      mockPurchases.forEach(purchase => {
        const currency = purchase.currency || 'SAR';
        
        if (!currencyStats[currency]) {
          currencyStats[currency] = {
            totalCost: 0,
            pendingPayments: 0,
            completedPayments: 0,
            count: 0,
            pendingCount: 0,
            completedCount: 0
          };
        }
        
        currencyStats[currency].totalCost += purchase.totalCost;
        currencyStats[currency].count += 1;
        
        if (purchase.status === 'pending') {
          currencyStats[currency].pendingPayments += purchase.totalCost;
          currencyStats[currency].pendingCount += 1;
        } else if (purchase.status === 'completed') {
          currencyStats[currency].completedPayments += purchase.totalCost;
          currencyStats[currency].completedCount += 1;
        }
      });

      // حساب الإحصائيات العامة
      const totalCost = mockPurchases.reduce((sum, purchase) => sum + purchase.totalCost, 0);
      const pendingPayments = mockPurchases.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.totalCost, 0);
      const completedPurchases = mockPurchases.filter(p => p.status === 'completed').length;
      
      setPurchaseStats({
        totalPurchases: mockPurchases.length,
        totalCost: totalCost,
        pendingPayments: pendingPayments,
        completedPurchases: completedPurchases,
        todayPurchases: mockPurchases.filter(p => p.purchaseDate === new Date().toISOString().split('T')[0]).length,
        monthPurchases: mockPurchases.length,
        currencyStats: currencyStats
      });

      setLoading(false);
    }, 1000);
  }, []);

  // دالة تحديث الإحصائيات
  const updateStats = (purchasesList) => {
    // حساب الإحصائيات حسب العملة
    const currencyStats = {};
    
    purchasesList.forEach(purchase => {
      const currency = purchase.currency || 'SAR';
      
      if (!currencyStats[currency]) {
        currencyStats[currency] = {
          totalCost: 0,
          pendingPayments: 0,
          completedPayments: 0,
          count: 0,
          pendingCount: 0,
          completedCount: 0
        };
      }
      
      currencyStats[currency].totalCost += purchase.totalCost;
      currencyStats[currency].count += 1;
      
      if (purchase.status === 'pending') {
        currencyStats[currency].pendingPayments += purchase.totalCost;
        currencyStats[currency].pendingCount += 1;
      } else if (purchase.status === 'completed') {
        currencyStats[currency].completedPayments += purchase.totalCost;
        currencyStats[currency].completedCount += 1;
      }
    });

    // حساب الإحصائيات العامة
    const totalCost = purchasesList.reduce((sum, purchase) => sum + purchase.totalCost, 0);
    const pendingPayments = purchasesList.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.totalCost, 0);
    const completedPurchases = purchasesList.filter(p => p.status === 'completed').length;
    
    setPurchaseStats({
      totalPurchases: purchasesList.length,
      totalCost: totalCost,
      pendingPayments: pendingPayments,
      completedPurchases: completedPurchases,
      todayPurchases: purchasesList.filter(p => p.purchaseDate === new Date().toISOString().split('T')[0]).length,
      monthPurchases: purchasesList.length,
      currencyStats: currencyStats
    });
  };

  const handleAddPurchase = (e) => {
    e.preventDefault();
    const purchase = {
      id: purchases.length + 1,
      ...newPurchase,
      cost: parseFloat(newPurchase.cost),
      quantity: parseInt(newPurchase.quantity),
      totalCost: parseFloat(newPurchase.cost) * parseInt(newPurchase.quantity),
      purchaseOrder: `PO-2024-${String(purchases.length + 6).padStart(3, '0')}`
    };
    const updatedPurchases = [purchase, ...purchases];
    setPurchases(updatedPurchases);
    
    // تحديث الإحصائيات
    updateStats(updatedPurchases);
    
    setNewPurchase({
      supplierName: '',
      supplierContact: '',
      serviceType: 'work_visa',
      description: '',
      purchaseDate: new Date().toISOString().split('T')[0],
      cost: '',
      quantity: 1,
      totalCost: '',
      currency: 'SAR',
      paymentMethod: 'bank_transfer',
      status: 'pending',
      dueDate: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const handleStatusChange = (id, newStatus) => {
    const updatedPurchases = purchases.map(purchase => 
      purchase.id === id ? { ...purchase, status: newStatus } : purchase
    );
    setPurchases(updatedPurchases);
    updateStats(updatedPurchases);
  };

  const handleDeletePurchase = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المشترية؟')) {
      const updatedPurchases = purchases.filter(purchase => purchase.id !== id);
      setPurchases(updatedPurchases);
      updateStats(updatedPurchases);
    }
  };

  // وظائف الأزرار الجديدة
  const handleViewPurchase = (purchase) => {
    setSelectedPurchase(purchase);
    setShowViewModal(true);
  };

  const handleEditPurchase = (purchase) => {
    setSelectedPurchase(purchase);
    setNewPurchase({
      supplierName: purchase.supplierName,
      supplierContact: purchase.supplierContact,
      serviceType: purchase.serviceType,
      description: purchase.description,
      purchaseDate: purchase.purchaseDate,
      cost: purchase.cost.toString(),
      quantity: purchase.quantity.toString(),
      totalCost: purchase.totalCost.toString(),
      currency: purchase.currency,
      paymentMethod: purchase.paymentMethod,
      status: purchase.status,
      dueDate: purchase.dueDate,
      notes: purchase.notes
    });
    setShowEditModal(true);
  };

  const handleUpdatePurchase = (e) => {
    e.preventDefault();
    const updatedPurchase = {
      ...selectedPurchase,
      ...newPurchase,
      cost: parseFloat(newPurchase.cost),
      quantity: parseInt(newPurchase.quantity),
      totalCost: parseFloat(newPurchase.cost) * parseInt(newPurchase.quantity)
    };
    
    const updatedPurchases = purchases.map(purchase => 
      purchase.id === selectedPurchase.id ? updatedPurchase : purchase
    );
    setPurchases(updatedPurchases);
    updateStats(updatedPurchases);
    
    setShowEditModal(false);
    setSelectedPurchase(null);
    setNewPurchase({
      supplierName: '',
      supplierContact: '',
      serviceType: 'work_visa',
      description: '',
      purchaseDate: new Date().toISOString().split('T')[0],
      cost: '',
      quantity: 1,
      totalCost: '',
      currency: 'SAR',
      paymentMethod: 'bank_transfer',
      status: 'pending',
      dueDate: '',
      notes: ''
    });
  };

  const handlePrintPurchase = (purchase) => {
    setSelectedPurchase(purchase);
    setShowPrintModal(true);
  };

  const handlePrint = () => {
    window.print();
    setShowPrintModal(false);
  };

  const getServiceTypeLabel = (type) => {
    const types = {
      work_visa: 'معاملة تأشيرة عمل',
      visit_visa: 'معاملة تأشيرة زيارة',
      umrah_visa: 'تأشيرة عمرة',
      hajj_visa: 'تأشيرة حج',
      three_month_visa: 'فيزا أبو ثلاثة أشهر',
      residence_visa: 'فيزا أبو إقامة سنة',
      bus_booking: 'حجز باص',
      passport: 'جوازات',
      flight_ticket: 'تذكرة طيران',
      other: 'أخرى'
    };
    return types[type] || type;
  };

  const getServiceTypeIcon = (type) => {
    const icons = {
      work_visa: '🔧',
      visit_visa: '👥',
      umrah_visa: '🕋',
      hajj_visa: '🕋',
      three_month_visa: '📋',
      residence_visa: '🏠',
      bus_booking: '🚌',
      passport: '📘',
      flight_ticket: '✈️',
      other: '📋'
    };
    return icons[type] || '📋';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'processing': return '#3498db';
      case 'cancelled': return '#e74c3c';
      case 'overdue': return '#e67e22';
      default: return '#95a5a6';
    }
  };

  const getStatusLabel = (status) => {
    const labels = {
      completed: 'مكتملة',
      pending: 'معلقة',
      processing: 'قيد المعالجة',
      cancelled: 'ملغية',
      overdue: 'متأخرة'
    };
    return labels[status] || status;
  };

  const getPaymentMethodLabel = (method) => {
    const methods = {
      cash: 'نقداً',
      credit: 'آجل',
      bank_transfer: 'تحويل بنكي',
      check: 'شيك'
    };
    return methods[method] || method;
  };

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencyMap = {
      'SAR': { locale: 'ar-SA', currency: 'SAR' },
      'YER': { locale: 'ar-YE', currency: 'YER' },
      'USD': { locale: 'en-US', currency: 'USD' }
    };
    
    const config = currencyMap[currency] || currencyMap['SAR'];
    
    return new Intl.NumberFormat(config.locale, {
      style: 'currency',
      currency: config.currency
    }).format(amount);
  };

  const filteredPurchases = purchases.filter(purchase => {
    const matchesSearch = purchase.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         purchase.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || purchase.status === filterStatus;
    const matchesSupplier = filterSupplier === 'all' || purchase.supplierName === filterSupplier;
    return matchesSearch && matchesStatus && matchesSupplier;
  });

  const uniqueSuppliers = [...new Set(purchases.map(p => p.supplierName))];

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل بيانات المشتريات...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h1 style={{ margin: 0, color: '#2c3e50', fontSize: '28px' }}>🛒 إدارة المشتريات</h1>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة وتتبع جميع المشتريات من الموردين</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ مشترية جديدة
        </button>
      </div>

      {/* Stats Cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي المشتريات', value: purchaseStats.totalPurchases, color: '#3498db', icon: '📊' },
          { title: 'إجمالي التكلفة', value: formatCurrency(purchaseStats.totalCost), color: '#e74c3c', icon: '💸' },
          { title: 'المدفوعات المعلقة', value: formatCurrency(purchaseStats.pendingPayments), color: '#f39c12', icon: '⏳' },
          { title: 'المشتريات المكتملة', value: purchaseStats.completedPurchases, color: '#27ae60', icon: '✅' },
          { title: 'مشتريات اليوم', value: purchaseStats.todayPurchases, color: '#9b59b6', icon: '📅' },
          { title: 'مشتريات الشهر', value: purchaseStats.monthPurchases, color: '#1abc9c', icon: '📆' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center',
            transition: 'transform 0.3s ease'
          }}
          onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
          onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '20px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {typeof stat.value === 'string' ? stat.value : stat.value.toLocaleString()}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* إحصائيات العملات */}
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '25px',
        boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
        border: '1px solid #e0e0e0',
        marginBottom: '30px'
      }}>
        <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50', textAlign: 'center' }}>
          💰 إحصائيات العملات والمدفوعات
        </h2>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: '25px'
        }}>
          {purchaseStats.currencyStats && Object.entries(purchaseStats.currencyStats).map(([currency, stats]) => {
            const getCurrencyName = (curr) => {
              const names = {
                'SAR': 'الريال السعودي',
                'YER': 'الريال اليمني', 
                'USD': 'الدولار الأمريكي'
              };
              return names[curr] || curr;
            };

            const getCurrencyIcon = (curr) => {
              const icons = {
                'SAR': '🇸🇦',
                'YER': '🇾🇪',
                'USD': '🇺🇸'
              };
              return icons[curr] || '💱';
            };

            return (
              <div key={currency} style={{
                background: `linear-gradient(135deg, ${currency === 'SAR' ? '#27ae60' : currency === 'YER' ? '#e74c3c' : '#3498db'}15, ${currency === 'SAR' ? '#27ae60' : currency === 'YER' ? '#e74c3c' : '#3498db'}05)`,
                border: `2px solid ${currency === 'SAR' ? '#27ae60' : currency === 'YER' ? '#e74c3c' : '#3498db'}30`,
                borderRadius: '15px',
                padding: '20px'
              }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  marginBottom: '15px',
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: currency === 'SAR' ? '#27ae60' : currency === 'YER' ? '#e74c3c' : '#3498db'
                }}>
                  <span style={{ fontSize: '24px', marginLeft: '10px' }}>{getCurrencyIcon(currency)}</span>
                  {getCurrencyName(currency)} ({currency})
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div style={{ textAlign: 'center', padding: '10px', background: 'white', borderRadius: '8px' }}>
                    <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>عدد المشتريات</div>
                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#2c3e50' }}>{stats.count}</div>
                  </div>
                  <div style={{ textAlign: 'center', padding: '10px', background: 'white', borderRadius: '8px' }}>
                    <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>إجمالي التكلفة</div>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#e74c3c' }}>
                      {formatCurrency(stats.totalCost, currency)}
                    </div>
                  </div>
                  <div style={{ textAlign: 'center', padding: '10px', background: 'white', borderRadius: '8px' }}>
                    <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>مدفوعات معلقة</div>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#f39c12' }}>
                      {formatCurrency(stats.pendingPayments, currency)}
                    </div>
                    <div style={{ fontSize: '10px', color: '#95a5a6' }}>({stats.pendingCount} مشترية)</div>
                  </div>
                  <div style={{ textAlign: 'center', padding: '10px', background: 'white', borderRadius: '8px' }}>
                    <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>مدفوعات مكتملة</div>
                    <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#27ae60' }}>
                      {formatCurrency(stats.completedPayments, currency)}
                    </div>
                    <div style={{ fontSize: '10px', color: '#95a5a6' }}>({stats.completedCount} مشترية)</div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* إجمالي عام لجميع العملات */}
        <div style={{
          marginTop: '25px',
          padding: '20px',
          background: 'linear-gradient(135deg, #34495e, #2c3e50)',
          borderRadius: '15px',
          color: 'white',
          textAlign: 'center'
        }}>
          <h3 style={{ margin: '0 0 15px 0', fontSize: '20px' }}>📈 الإجمالي العام لجميع العملات</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            <div>
              <div style={{ fontSize: '14px', opacity: 0.8 }}>إجمالي المشتريات</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{purchaseStats.totalPurchases}</div>
            </div>
            <div>
              <div style={{ fontSize: '14px', opacity: 0.8 }}>إجمالي المدفوعات المعلقة</div>
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#f39c12' }}>
                {purchaseStats.currencyStats && Object.entries(purchaseStats.currencyStats).map(([currency, stats]) => (
                  <div key={currency} style={{ fontSize: '14px', margin: '2px 0' }}>
                    {formatCurrency(stats.pendingPayments, currency)}
                  </div>
                ))}
              </div>
            </div>
            <div>
              <div style={{ fontSize: '14px', opacity: 0.8 }}>إجمالي المدفوعات المكتملة</div>
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#27ae60' }}>
                {purchaseStats.currencyStats && Object.entries(purchaseStats.currencyStats).map(([currency, stats]) => (
                  <div key={currency} style={{ fontSize: '14px', margin: '2px 0' }}>
                    {formatCurrency(stats.completedPayments, currency)}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في المشتريات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="completed">مكتملة</option>
          <option value="pending">معلقة</option>
          <option value="processing">قيد المعالجة</option>
          <option value="cancelled">ملغية</option>
          <option value="overdue">متأخرة</option>
        </select>
        <select
          value={filterSupplier}
          onChange={(e) => setFilterSupplier(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '150px'
          }}
        >
          <option value="all">جميع الموردين</option>
          {uniqueSuppliers.map(supplier => (
            <option key={supplier} value={supplier}>{supplier}</option>
          ))}
        </select>
      </div>

      {/* Purchases Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>رقم الطلب</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>المورد</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>الخدمة</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>الوصف</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الكمية</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>التكلفة الإجمالية</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الدفع</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredPurchases.map((purchase) => (
                <tr key={purchase.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#3498db' }}>
                    {purchase.purchaseOrder}
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{purchase.supplierName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{purchase.supplierContact}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ fontSize: '18px' }}>{getServiceTypeIcon(purchase.serviceType)}</span>
                      <span>{getServiceTypeLabel(purchase.serviceType)}</span>
                    </div>
                  </td>
                  <td style={{ padding: '15px', maxWidth: '200px' }}>
                    <div style={{ 
                      overflow: 'hidden', 
                      textOverflow: 'ellipsis', 
                      whiteSpace: 'nowrap' 
                    }} title={purchase.description}>
                      {purchase.description}
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>
                    {purchase.quantity}
                  </td>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#e74c3c' }}>
                    {formatCurrency(purchase.totalCost, purchase.currency)}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      background: '#e3f2fd',
                      color: '#1976d2'
                    }}>
                      {getPaymentMethodLabel(purchase.paymentMethod)}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <select
                      value={purchase.status}
                      onChange={(e) => handleStatusChange(purchase.id, e.target.value)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '20px',
                        border: 'none',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        background: `${getStatusColor(purchase.status)}20`,
                        color: getStatusColor(purchase.status),
                        cursor: 'pointer'
                      }}
                    >
                      <option value="pending">معلقة</option>
                      <option value="processing">قيد المعالجة</option>
                      <option value="completed">مكتملة</option>
                      <option value="cancelled">ملغية</option>
                      <option value="overdue">متأخرة</option>
                    </select>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                      <button
                        onClick={() => handleViewPurchase(purchase)}
                        style={{
                          background: '#3498db',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="عرض التفاصيل"
                      >
                        👁️
                      </button>
                      <button
                        onClick={() => handleEditPurchase(purchase)}
                        style={{
                          background: '#f39c12',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="تعديل"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handlePrintPurchase(purchase)}
                        style={{
                          background: '#27ae60',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="طباعة"
                      >
                        🖨️
                      </button>
                      <button
                        onClick={() => handleDeletePurchase(purchase.id)}
                        style={{
                          background: '#e74c3c',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="حذف"
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Purchase Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>➕ إضافة مشترية جديدة</h2>
            
            <form onSubmit={handleAddPurchase}>
              {/* خانة العملة */}
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العملة</label>
                <select
                  value={newPurchase.currency}
                  onChange={(e) => setNewPurchase({...newPurchase, currency: e.target.value})}
                  style={{
                    width: '200px',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="SAR">الريال السعودي (SAR)</option>
                  <option value="YER">الريال اليمني (YER)</option>
                  <option value="USD">الدولار الأمريكي (USD)</option>
                </select>
              </div>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم المورد</label>
                  <input
                    type="text"
                    value={newPurchase.supplierName}
                    onChange={(e) => setNewPurchase({...newPurchase, supplierName: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>معلومات الاتصال</label>
                  <input
                    type="text"
                    value={newPurchase.supplierContact}
                    onChange={(e) => setNewPurchase({...newPurchase, supplierContact: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الخدمة</label>
                  <select
                    value={newPurchase.serviceType}
                    onChange={(e) => setNewPurchase({...newPurchase, serviceType: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="work_visa">معاملة تأشيرة عمل</option>
                    <option value="visit_visa">معاملة تأشيرة زيارة</option>
                    <option value="umrah_visa">تأشيرة عمرة</option>
                    <option value="hajj_visa">تأشيرة حج</option>
                    <option value="three_month_visa">فيزا أبو ثلاثة أشهر</option>
                    <option value="residence_visa">فيزا أبو إقامة سنة</option>
                    <option value="bus_booking">حجز باص</option>
                    <option value="passport">جوازات</option>
                    <option value="flight_ticket">تذكرة طيران</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الشراء</label>
                  <input
                    type="date"
                    value={newPurchase.purchaseDate}
                    onChange={(e) => setNewPurchase({...newPurchase, purchaseDate: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>وصف المشترية</label>
                <textarea
                  value={newPurchase.description}
                  onChange={(e) => setNewPurchase({...newPurchase, description: e.target.value})}
                  required
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التكلفة الوحدة (ريال)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={newPurchase.cost}
                    onChange={(e) => setNewPurchase({...newPurchase, cost: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الكمية</label>
                  <input
                    type="number"
                    min="1"
                    value={newPurchase.quantity}
                    onChange={(e) => setNewPurchase({...newPurchase, quantity: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التكلفة الإجمالية</label>
                  <input
                    type="text"
                    value={newPurchase.cost && newPurchase.quantity ? 
                      formatCurrency(parseFloat(newPurchase.cost) * parseInt(newPurchase.quantity)) : ''}
                    readOnly
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      background: '#f8f9fa'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>طريقة الدفع</label>
                  <select
                    value={newPurchase.paymentMethod}
                    onChange={(e) => setNewPurchase({...newPurchase, paymentMethod: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="cash">نقداً</option>
                    <option value="credit">آجل</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="check">شيك</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الحالة</label>
                  <select
                    value={newPurchase.status}
                    onChange={(e) => setNewPurchase({...newPurchase, status: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="pending">معلقة</option>
                    <option value="processing">قيد المعالجة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الاستحقاق</label>
                  <input
                    type="date"
                    value={newPurchase.dueDate}
                    onChange={(e) => setNewPurchase({...newPurchase, dueDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newPurchase.notes}
                  onChange={(e) => setNewPurchase({...newPurchase, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة المشترية
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة عرض تفاصيل المشترية */}
      {showViewModal && selectedPurchase && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '25px' }}>
              <h2 style={{ margin: 0, color: '#2c3e50' }}>📋 تفاصيل المشترية</h2>
              <button
                onClick={() => setShowViewModal(false)}
                style={{
                  background: '#e74c3c',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '35px',
                  height: '35px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ×
              </button>
            </div>

            {/* معلومات المورد */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#3498db' }}>🏢 معلومات المورد</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div><strong>اسم المورد:</strong> {selectedPurchase.supplierName}</div>
                <div><strong>معلومات الاتصال:</strong> {selectedPurchase.supplierContact}</div>
              </div>
            </div>

            {/* تفاصيل المشترية */}
            <div style={{ 
              background: '#e8f5e8', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#27ae60' }}>📦 تفاصيل المشترية</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div><strong>رقم الطلب:</strong> {selectedPurchase.purchaseOrder}</div>
                <div><strong>نوع الخدمة:</strong> {getServiceTypeIcon(selectedPurchase.serviceType)} {getServiceTypeLabel(selectedPurchase.serviceType)}</div>
                <div><strong>تاريخ الشراء:</strong> {selectedPurchase.purchaseDate}</div>
                <div><strong>تاريخ الاستحقاق:</strong> {selectedPurchase.dueDate}</div>
              </div>
              <div style={{ marginTop: '15px' }}>
                <strong>الوصف:</strong> {selectedPurchase.description}
              </div>
            </div>

            {/* التكلفة والدفع */}
            <div style={{ 
              background: '#fff3cd', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#f39c12' }}>💰 التكلفة والدفع</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                <div><strong>تكلفة الوحدة:</strong> {formatCurrency(selectedPurchase.cost, selectedPurchase.currency)}</div>
                <div><strong>الكمية:</strong> {selectedPurchase.quantity}</div>
                <div><strong>التكلفة الإجمالية:</strong> <span style={{ color: '#e74c3c', fontWeight: 'bold', fontSize: '18px' }}>{formatCurrency(selectedPurchase.totalCost, selectedPurchase.currency)}</span></div>
                <div><strong>طريقة الدفع:</strong> {getPaymentMethodLabel(selectedPurchase.paymentMethod)}</div>
                <div>
                  <strong>الحالة:</strong> 
                  <span style={{
                    background: getStatusColor(selectedPurchase.status),
                    color: 'white',
                    padding: '3px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    marginRight: '8px'
                  }}>
                    {getStatusLabel(selectedPurchase.status)}
                  </span>
                </div>
              </div>
            </div>

            {/* ملاحظات */}
            {selectedPurchase.notes && (
              <div style={{ 
                background: '#f0f8ff', 
                padding: '20px', 
                borderRadius: '10px',
                marginBottom: '20px'
              }}>
                <h4 style={{ margin: '0 0 15px 0', color: '#0066cc' }}>📝 ملاحظات</h4>
                <p style={{ margin: 0, lineHeight: '1.6' }}>{selectedPurchase.notes}</p>
              </div>
            )}

            <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  handleEditPurchase(selectedPurchase);
                }}
                style={{
                  padding: '12px 25px',
                  background: '#f39c12',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                ✏️ تعديل
              </button>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  handlePrintPurchase(selectedPurchase);
                }}
                style={{
                  padding: '12px 25px',
                  background: '#27ae60',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل المشترية */}
      {showEditModal && selectedPurchase && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>✏️ تعديل المشترية</h2>
            
            <form onSubmit={handleUpdatePurchase}>
              {/* خانة العملة */}
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العملة</label>
                <select
                  value={newPurchase.currency}
                  onChange={(e) => setNewPurchase({...newPurchase, currency: e.target.value})}
                  style={{
                    width: '200px',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="SAR">الريال السعودي (SAR)</option>
                  <option value="YER">الريال اليمني (YER)</option>
                  <option value="USD">الدولار الأمريكي (USD)</option>
                </select>
              </div>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم المورد</label>
                  <input
                    type="text"
                    value={newPurchase.supplierName}
                    onChange={(e) => setNewPurchase({...newPurchase, supplierName: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>معلومات الاتصال</label>
                  <input
                    type="text"
                    value={newPurchase.supplierContact}
                    onChange={(e) => setNewPurchase({...newPurchase, supplierContact: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الخدمة</label>
                  <select
                    value={newPurchase.serviceType}
                    onChange={(e) => setNewPurchase({...newPurchase, serviceType: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="work_visa">🔧 معاملة تأشيرة عمل</option>
                    <option value="visit_visa">👥 معاملة تأشيرة زيارة</option>
                    <option value="umrah_visa">🕋 تأشيرة عمرة</option>
                    <option value="hajj_visa">🕋 تأشيرة حج</option>
                    <option value="three_month_visa">📋 فيزا أبو ثلاثة أشهر</option>
                    <option value="residence_visa">🏠 فيزا أبو إقامة سنة</option>
                    <option value="bus_booking">🚌 حجز باص</option>
                    <option value="passport">📘 جوازات</option>
                    <option value="flight_ticket">✈️ تذكرة طيران</option>
                    <option value="other">📋 أخرى</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الشراء</label>
                  <input
                    type="date"
                    value={newPurchase.purchaseDate}
                    onChange={(e) => setNewPurchase({...newPurchase, purchaseDate: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>وصف المشترية</label>
                <textarea
                  value={newPurchase.description}
                  onChange={(e) => setNewPurchase({...newPurchase, description: e.target.value})}
                  required
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التكلفة الوحدة (ريال)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={newPurchase.cost}
                    onChange={(e) => setNewPurchase({...newPurchase, cost: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الكمية</label>
                  <input
                    type="number"
                    min="1"
                    value={newPurchase.quantity}
                    onChange={(e) => setNewPurchase({...newPurchase, quantity: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التكلفة الإجمالية</label>
                  <input
                    type="text"
                    value={newPurchase.cost && newPurchase.quantity ? 
                      formatCurrency(parseFloat(newPurchase.cost) * parseInt(newPurchase.quantity)) : ''}
                    readOnly
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      background: '#f8f9fa'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>طريقة الدفع</label>
                  <select
                    value={newPurchase.paymentMethod}
                    onChange={(e) => setNewPurchase({...newPurchase, paymentMethod: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="cash">نقداً</option>
                    <option value="credit">آجل</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="check">شيك</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الحالة</label>
                  <select
                    value={newPurchase.status}
                    onChange={(e) => setNewPurchase({...newPurchase, status: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="pending">معلقة</option>
                    <option value="processing">قيد المعالجة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الاستحقاق</label>
                  <input
                    type="date"
                    value={newPurchase.dueDate}
                    onChange={(e) => setNewPurchase({...newPurchase, dueDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newPurchase.notes}
                  onChange={(e) => setNewPurchase({...newPurchase, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: '#f39c12',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  حفظ التعديلات
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة الطباعة */}
      {showPrintModal && selectedPurchase && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '25px' }}>
              <h2 style={{ margin: 0, color: '#2c3e50' }}>🖨️ طباعة المشترية</h2>
              <button
                onClick={() => setShowPrintModal(false)}
                style={{
                  background: '#e74c3c',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '35px',
                  height: '35px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ×
              </button>
            </div>

            {/* محتوى الطباعة */}
            <div id="printContent" style={{ 
              padding: '20px',
              border: '2px solid #dee2e6',
              borderRadius: '10px',
              background: '#fff'
            }}>
              {/* رأس الشركة */}
              <div style={{ textAlign: 'center', marginBottom: '30px', borderBottom: '2px solid #3498db', paddingBottom: '20px' }}>
                <h1 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>شركة شراء السفر</h1>
                <p style={{ margin: '5px 0', color: '#7f8c8d' }}>أمر شراء رقم: {selectedPurchase.purchaseOrder}</p>
                <p style={{ margin: '5px 0', color: '#7f8c8d' }}>تاريخ الطباعة: {new Date().toLocaleDateString('ar-SA')}</p>
              </div>

              {/* معلومات المورد */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#3498db', borderBottom: '1px solid #3498db', paddingBottom: '5px' }}>معلومات المورد</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginTop: '15px' }}>
                  <div><strong>اسم المورد:</strong> {selectedPurchase.supplierName}</div>
                  <div><strong>معلومات الاتصال:</strong> {selectedPurchase.supplierContact}</div>
                </div>
              </div>

              {/* تفاصيل المشترية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#27ae60', borderBottom: '1px solid #27ae60', paddingBottom: '5px' }}>تفاصيل المشترية</h3>
                <div style={{ marginTop: '15px' }}>
                  <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '10px' }}>
                    <thead>
                      <tr style={{ background: '#f8f9fa' }}>
                        <th style={{ padding: '12px', border: '1px solid #dee2e6', textAlign: 'right' }}>البيان</th>
                        <th style={{ padding: '12px', border: '1px solid #dee2e6', textAlign: 'center' }}>القيمة</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>نوع الخدمة</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {getServiceTypeIcon(selectedPurchase.serviceType)} {getServiceTypeLabel(selectedPurchase.serviceType)}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>الوصف</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {selectedPurchase.description}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>تاريخ الشراء</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {selectedPurchase.purchaseDate}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>تاريخ الاستحقاق</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {selectedPurchase.dueDate}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>تكلفة الوحدة</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {formatCurrency(selectedPurchase.cost, selectedPurchase.currency)}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>الكمية</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {selectedPurchase.quantity}
                        </td>
                      </tr>
                      <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>التكلفة الإجمالية</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6', color: '#e74c3c', fontSize: '18px' }}>
                          {formatCurrency(selectedPurchase.totalCost, selectedPurchase.currency)}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>طريقة الدفع</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {getPaymentMethodLabel(selectedPurchase.paymentMethod)}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>الحالة</td>
                        <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                          {getStatusLabel(selectedPurchase.status)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* ملاحظات */}
              {selectedPurchase.notes && (
                <div style={{ marginBottom: '25px' }}>
                  <h3 style={{ color: '#f39c12', borderBottom: '1px solid #f39c12', paddingBottom: '5px' }}>ملاحظات</h3>
                  <p style={{ marginTop: '15px', lineHeight: '1.6', padding: '15px', background: '#fff3cd', borderRadius: '5px' }}>
                    {selectedPurchase.notes}
                  </p>
                </div>
              )}

              {/* توقيعات */}
              <div style={{ marginTop: '40px', display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '30px' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ borderTop: '1px solid #000', marginTop: '40px', paddingTop: '10px' }}>
                    <strong>المشتري</strong>
                  </div>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ borderTop: '1px solid #000', marginTop: '40px', paddingTop: '10px' }}>
                    <strong>المدير المالي</strong>
                  </div>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ borderTop: '1px solid #000', marginTop: '40px', paddingTop: '10px' }}>
                    <strong>المدير العام</strong>
                  </div>
                </div>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginTop: '25px' }}>
              <button
                onClick={handlePrint}
                style={{
                  padding: '12px 25px',
                  background: '#27ae60',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                🖨️ طباعة
              </button>
              <button
                onClick={() => setShowPrintModal(false)}
                style={{
                  padding: '12px 25px',
                  border: '2px solid #e0e0e0',
                  background: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS للطباعة */}
      <style>
        {`
          @media print {
            body * {
              visibility: hidden;
            }
            #printContent, #printContent * {
              visibility: visible;
            }
            #printContent {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
            }
            @page {
              margin: 1cm;
              size: A4;
            }
          }
        `}
      </style>
    </div>
  );
};

export default PurchasesPage;