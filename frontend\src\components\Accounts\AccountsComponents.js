import React, { useState, useMemo } from 'react';
import './AccountsComponents.css';
import GeneralLedger from './GeneralLedger';
import JournalBooks from './JournalBooks';
import AccountsHierarchy from './AccountsHierarchy';
import AutomatedTransactions from './AutomatedTransactions';
import AuditTrail from './AuditTrail';
import AccountingPeriods from './AccountingPeriods';
import JournalBooksAdvanced from './JournalBooksAdvanced';
import AutomatedTransactionsAdvanced from './AutomatedTransactionsAdvanced';
import AuditTrailAdvanced from './AuditTrailAdvanced';
import GeneralLedgerAdvanced from './GeneralLedgerAdvanced';
import JournalEntriesAdvanced from './JournalEntriesAdvanced';
import TrialBalanceAdvanced from './TrialBalanceAdvanced';
import FinancialStatementsAdvanced from './FinancialStatementsAdvanced';
import AccountsReceivableAdvanced from './AccountsReceivableAdvanced';
import AccountsPayableAdvanced from './AccountsPayableAdvanced';

// مكون دليل الحسابات
export const ChartOfAccounts = ({ 
  accounts, 
  onAddAccount, 
  onUpdateAccount, 
  onDeleteAccount,
  isLoading 
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set(['1000', '2000', '3000', '4000', '5000']));
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  const [newAccount, setNewAccount] = useState({
    name: '',
    type: 'detail',
    category: 'assets',
    parent: null,
    description: ''
  });

  // تنظيم الحسابات في هيكل شجري
  const accountTree = useMemo(() => {
    const filtered = accounts.filter(account => {
      const matchesSearch = account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           account.id.includes(searchTerm);
      const matchesCategory = filterCategory === 'all' || account.category === filterCategory;
      return matchesSearch && matchesCategory;
    });

    const tree = {};
    const rootAccounts = [];

    // إنشاء خريطة للحسابات
    filtered.forEach(account => {
      tree[account.id] = { ...account, children: [] };
    });

    // بناء الهيكل الشجري
    filtered.forEach(account => {
      if (account.parent && tree[account.parent]) {
        tree[account.parent].children.push(tree[account.id]);
      } else {
        rootAccounts.push(tree[account.id]);
      }
    });

    return rootAccounts;
  }, [accounts, searchTerm, filterCategory]);

  const toggleNode = (accountId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId);
    } else {
      newExpanded.add(accountId);
    }
    setExpandedNodes(newExpanded);
  };

  const handleAddAccount = () => {
    if (newAccount.name.trim()) {
      onAddAccount(newAccount);
      setNewAccount({
        name: '',
        type: 'detail',
        category: 'assets',
        parent: null,
        description: ''
      });
      setShowAddForm(false);
    }
  };

  const handleEditAccount = (account) => {
    setEditingAccount(account);
    setNewAccount({
      name: account.name,
      type: account.type,
      category: account.category,
      parent: account.parent,
      description: account.description || ''
    });
    setShowAddForm(true);
  };

  const handleUpdateAccount = () => {
    if (editingAccount && newAccount.name.trim()) {
      onUpdateAccount(editingAccount.id, newAccount);
      setEditingAccount(null);
      setNewAccount({
        name: '',
        type: 'detail',
        category: 'assets',
        parent: null,
        description: ''
      });
      setShowAddForm(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getCategoryIcon = (category) => {
    const icons = {
      assets: '💰',
      liabilities: '📋',
      equity: '🏛️',
      revenue: '📈',
      expenses: '📉'
    };
    return icons[category] || '📊';
  };

  const renderAccountNode = (account, level = 0) => {
    const hasChildren = account.children && account.children.length > 0;
    const isExpanded = expandedNodes.has(account.id);
    const indent = level * 20;

    return (
      <div key={account.id} className="account-node">
        <div 
          className={`account-item ${account.type}`}
          style={{ paddingRight: `${indent}px` }}
        >
          <div className="account-info">
            {hasChildren && (
              <button 
                className="expand-btn"
                onClick={() => toggleNode(account.id)}
              >
                {isExpanded ? '▼' : '▶'}
              </button>
            )}
            <span className="account-icon">{getCategoryIcon(account.category)}</span>
            <div className="account-details">
              <div className="account-name">
                <span className="account-code">{account.id}</span>
                <span className="account-title">{account.name}</span>
              </div>
              {account.type === 'detail' && (
                <div className="account-balance">
                  {formatCurrency(account.balance)}
                </div>
              )}
            </div>
          </div>
          
          <div className="account-actions">
            <button 
              className="edit-btn"
              onClick={() => handleEditAccount(account)}
              title="تعديل الحساب"
            >
              ✏️
            </button>
            <button 
              className="delete-btn"
              onClick={() => onDeleteAccount(account.id)}
              title="حذف الحساب"
            >
              🗑️
            </button>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="account-children">
            {account.children.map(child => renderAccountNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل دليل الحسابات...</p>
      </div>
    );
  }

  return (
    <div className="chart-of-accounts">
      <div className="accounts-header">
        <h3>دليل الحسابات</h3>
        <button 
          className="add-account-btn"
          onClick={() => setShowAddForm(!showAddForm)}
        >
          {showAddForm ? '❌ إلغاء' : '➕ إضافة حساب'}
        </button>
      </div>

      <div className="accounts-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في الحسابات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="category-filter">
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <option value="all">جميع الفئات</option>
            <option value="assets">الأصول</option>
            <option value="liabilities">الخصوم</option>
            <option value="equity">حقوق الملكية</option>
            <option value="revenue">الإيرادات</option>
            <option value="expenses">المصروفات</option>
          </select>
        </div>
      </div>

      {showAddForm && (
        <div className="add-account-form">
          <h4>{editingAccount ? 'تعديل الحساب' : 'إضافة حساب جديد'}</h4>
          
          <div className="form-grid">
            <div className="form-group">
              <label>اسم الحساب</label>
              <input
                type="text"
                value={newAccount.name}
                onChange={(e) => setNewAccount({...newAccount, name: e.target.value})}
                placeholder="أدخل اسم الحساب"
              />
            </div>

            <div className="form-group">
              <label>نوع الحساب</label>
              <select
                value={newAccount.type}
                onChange={(e) => setNewAccount({...newAccount, type: e.target.value})}
              >
                <option value="header">حساب رئيسي</option>
                <option value="detail">حساب تفصيلي</option>
              </select>
            </div>

            <div className="form-group">
              <label>فئة الحساب</label>
              <select
                value={newAccount.category}
                onChange={(e) => setNewAccount({...newAccount, category: e.target.value})}
              >
                <option value="assets">الأصول</option>
                <option value="liabilities">الخصوم</option>
                <option value="equity">حقوق الملكية</option>
                <option value="revenue">الإيرادات</option>
                <option value="expenses">المصروفات</option>
              </select>
            </div>

            <div className="form-group">
              <label>الحساب الأب</label>
              <select
                value={newAccount.parent || ''}
                onChange={(e) => setNewAccount({...newAccount, parent: e.target.value || null})}
              >
                <option value="">لا يوجد</option>
                {accounts
                  .filter(acc => acc.type === 'header')
                  .map(acc => (
                    <option key={acc.id} value={acc.id}>
                      {acc.id} - {acc.name}
                    </option>
                  ))
                }
              </select>
            </div>

            <div className="form-group full-width">
              <label>الوصف</label>
              <textarea
                value={newAccount.description}
                onChange={(e) => setNewAccount({...newAccount, description: e.target.value})}
                placeholder="وصف الحساب (اختياري)"
                rows="3"
              />
            </div>
          </div>

          <div className="form-actions">
            <button 
              className="save-btn"
              onClick={editingAccount ? handleUpdateAccount : handleAddAccount}
            >
              {editingAccount ? '💾 تحديث' : '💾 حفظ'}
            </button>
            <button 
              className="cancel-btn"
              onClick={() => {
                setShowAddForm(false);
                setEditingAccount(null);
                setNewAccount({
                  name: '',
                  type: 'detail',
                  category: 'assets',
                  parent: null,
                  description: ''
                });
              }}
            >
              ❌ إلغاء
            </button>
          </div>
        </div>
      )}

      <div className="accounts-tree">
        {accountTree.map(account => renderAccountNode(account))}
      </div>
    </div>
  );
};

// مكون قيود اليومية
export const JournalEntries = ({ 
  transactions, 
  accounts,
  onAddTransaction, 
  onUpdateTransaction, 
  onDeleteTransaction,
  onPostTransaction,
  isLoading 
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const [newTransaction, setNewTransaction] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    entries: [
      { accountId: '', accountName: '', debit: 0, credit: 0 },
      { accountId: '', accountName: '', debit: 0, credit: 0 }
    ]
  });

  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           transaction.id.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || transaction.status === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [transactions, searchTerm, filterStatus]);

  const addEntry = () => {
    setNewTransaction(prev => ({
      ...prev,
      entries: [...prev.entries, { accountId: '', accountName: '', debit: 0, credit: 0 }]
    }));
  };

  const removeEntry = (index) => {
    if (newTransaction.entries.length > 2) {
      setNewTransaction(prev => ({
        ...prev,
        entries: prev.entries.filter((_, i) => i !== index)
      }));
    }
  };

  const updateEntry = (index, field, value) => {
    setNewTransaction(prev => ({
      ...prev,
      entries: prev.entries.map((entry, i) => 
        i === index ? { ...entry, [field]: value } : entry
      )
    }));
  };

  const updateEntryAccount = (index, accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    if (account) {
      updateEntry(index, 'accountId', accountId);
      updateEntry(index, 'accountName', account.name);
    }
  };

  const calculateTotals = () => {
    const totalDebit = newTransaction.entries.reduce((sum, entry) => sum + (parseFloat(entry.debit) || 0), 0);
    const totalCredit = newTransaction.entries.reduce((sum, entry) => sum + (parseFloat(entry.credit) || 0), 0);
    return { totalDebit, totalCredit };
  };

  const isBalanced = () => {
    const { totalDebit, totalCredit } = calculateTotals();
    return Math.abs(totalDebit - totalCredit) < 0.01;
  };

  const handleSaveTransaction = () => {
    if (!newTransaction.description.trim()) {
      alert('يرجى إدخال وصف المعاملة');
      return;
    }

    if (!isBalanced()) {
      alert('القيد غير متوازن. يجب أن يكون مجموع المدين مساوياً لمجموع الدائن');
      return;
    }

    const { totalDebit, totalCredit } = calculateTotals();
    const transaction = {
      ...newTransaction,
      totalDebit,
      totalCredit,
      entries: newTransaction.entries.filter(entry => entry.accountId && (entry.debit > 0 || entry.credit > 0))
    };

    if (editingTransaction) {
      onUpdateTransaction(editingTransaction.id, transaction);
    } else {
      onAddTransaction(transaction);
    }

    resetForm();
  };

  const resetForm = () => {
    setNewTransaction({
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      entries: [
        { accountId: '', accountName: '', debit: 0, credit: 0 },
        { accountId: '', accountName: '', debit: 0, credit: 0 }
      ]
    });
    setShowAddForm(false);
    setEditingTransaction(null);
  };

  const handleEditTransaction = (transaction) => {
    setEditingTransaction(transaction);
    setNewTransaction({
      date: transaction.date,
      description: transaction.description,
      reference: transaction.reference,
      entries: [...transaction.entries]
    });
    setShowAddForm(true);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل قيود اليومية...</p>
      </div>
    );
  }

  return (
    <div className="journal-entries">
      <div className="journal-header">
        <h3>قيود اليومية</h3>
        <button 
          className="add-entry-btn"
          onClick={() => setShowAddForm(!showAddForm)}
        >
          {showAddForm ? '❌ إلغاء' : '➕ إضافة قيد'}
        </button>
      </div>

      <div className="journal-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في القيود..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="status-filter">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="draft">مسودة</option>
            <option value="posted">مرحل</option>
          </select>
        </div>
      </div>

      {showAddForm && (
        <div className="add-transaction-form">
          <h4>{editingTransaction ? 'تعديل القيد' : 'إضافة قيد جديد'}</h4>
          
          <div className="transaction-header-form">
            <div className="form-group">
              <label>التاريخ</label>
              <input
                type="date"
                value={newTransaction.date}
                onChange={(e) => setNewTransaction({...newTransaction, date: e.target.value})}
              />
            </div>

            <div className="form-group">
              <label>المرجع</label>
              <input
                type="text"
                value={newTransaction.reference}
                onChange={(e) => setNewTransaction({...newTransaction, reference: e.target.value})}
                placeholder="رقم المرجع"
              />
            </div>

            <div className="form-group full-width">
              <label>الوصف</label>
              <input
                type="text"
                value={newTransaction.description}
                onChange={(e) => setNewTransaction({...newTransaction, description: e.target.value})}
                placeholder="وصف المعاملة"
              />
            </div>
          </div>

          <div className="entries-section">
            <h5>القيود التفصيلية</h5>
            
            <div className="entries-table">
              <div className="table-header">
                <div>الحساب</div>
                <div>مدين</div>
                <div>دائن</div>
                <div>إجراءات</div>
              </div>

              {newTransaction.entries.map((entry, index) => (
                <div key={index} className="entry-row">
                  <div className="account-select">
                    <select
                      value={entry.accountId}
                      onChange={(e) => updateEntryAccount(index, e.target.value)}
                    >
                      <option value="">اختر الحساب</option>
                      {accounts
                        .filter(acc => acc.type === 'detail')
                        .map(acc => (
                          <option key={acc.id} value={acc.id}>
                            {acc.id} - {acc.name}
                          </option>
                        ))
                      }
                    </select>
                  </div>

                  <div className="amount-input">
                    <input
                      type="number"
                      step="0.01"
                      value={entry.debit}
                      onChange={(e) => updateEntry(index, 'debit', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>

                  <div className="amount-input">
                    <input
                      type="number"
                      step="0.01"
                      value={entry.credit}
                      onChange={(e) => updateEntry(index, 'credit', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>

                  <div className="entry-actions">
                    <button 
                      className="remove-entry-btn"
                      onClick={() => removeEntry(index)}
                      disabled={newTransaction.entries.length <= 2}
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="entries-footer">
              <button className="add-entry-line-btn" onClick={addEntry}>
                ➕ إضافة سطر
              </button>
              
              <div className="totals">
                <div className={`total-item ${isBalanced() ? 'balanced' : 'unbalanced'}`}>
                  <span>إجمالي المدين: {formatCurrency(calculateTotals().totalDebit)}</span>
                  <span>إجمالي الدائن: {formatCurrency(calculateTotals().totalCredit)}</span>
                  <span className="balance-status">
                    {isBalanced() ? '✅ متوازن' : '❌ غير متوازن'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="form-actions">
            <button 
              className="save-btn"
              onClick={handleSaveTransaction}
              disabled={!isBalanced()}
            >
              💾 حفظ القيد
            </button>
            <button className="cancel-btn" onClick={resetForm}>
              ❌ إلغاء
            </button>
          </div>
        </div>
      )}

      <div className="transactions-list">
        {filteredTransactions.map(transaction => (
          <div key={transaction.id} className={`transaction-card ${transaction.status}`}>
            <div className="transaction-header">
              <div className="transaction-info">
                <h4>{transaction.id}</h4>
                <p>{transaction.description}</p>
                <div className="transaction-meta">
                  <span>📅 {transaction.date}</span>
                  <span>📄 {transaction.reference}</span>
                  <span>👤 {transaction.createdBy}</span>
                  <span className={`status ${transaction.status}`}>
                    {transaction.status === 'posted' ? '✅ مرحل' : '📝 مسودة'}
                  </span>
                </div>
              </div>
              
              <div className="transaction-actions">
                {transaction.status === 'draft' && (
                  <>
                    <button 
                      className="edit-btn"
                      onClick={() => handleEditTransaction(transaction)}
                    >
                      ✏️ تعديل
                    </button>
                    <button 
                      className="post-btn"
                      onClick={() => onPostTransaction(transaction.id)}
                    >
                      ✅ ترحيل
                    </button>
                  </>
                )}
                <button 
                  className="delete-btn"
                  onClick={() => onDeleteTransaction(transaction.id)}
                >
                  🗑️ حذف
                </button>
              </div>
            </div>

            <div className="transaction-entries">
              <div className="entries-table">
                <div className="table-header">
                  <div>الحساب</div>
                  <div>مدين</div>
                  <div>دائن</div>
                </div>
                
                {transaction.entries.map((entry, index) => (
                  <div key={index} className="entry-row">
                    <div className="account-name">
                      {entry.accountId} - {entry.accountName}
                    </div>
                    <div className="debit-amount">
                      {entry.debit > 0 ? formatCurrency(entry.debit) : '-'}
                    </div>
                    <div className="credit-amount">
                      {entry.credit > 0 ? formatCurrency(entry.credit) : '-'}
                    </div>
                  </div>
                ))}
                
                <div className="totals-row">
                  <div className="total-label">الإجمالي</div>
                  <div className="total-debit">{formatCurrency(transaction.totalDebit)}</div>
                  <div className="total-credit">{formatCurrency(transaction.totalCredit)}</div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// مكون ميزان المراجعة
export const TrialBalance = ({ accounts, transactions, financialPeriod, isLoading }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [filterCategory, setFilterCategory] = useState('all');

  const trialBalanceData = useMemo(() => {
    // حساب الأرصدة لكل حساب
    const balances = {};
    
    // تهيئة الأرصدة
    accounts.forEach(account => {
      if (account.type === 'detail') {
        balances[account.id] = {
          ...account,
          openingBalance: account.balance || 0,
          debitMovements: 0,
          creditMovements: 0,
          closingBalance: account.balance || 0
        };
      }
    });

    // حساب الحركات من المعاملات المرحلة
    transactions
      .filter(trans => trans.status === 'posted')
      .filter(trans => {
        const transDate = new Date(trans.date);
        const startDate = new Date(financialPeriod.startDate);
        const endDate = new Date(financialPeriod.endDate);
        return transDate >= startDate && transDate <= endDate;
      })
      .forEach(transaction => {
        transaction.entries.forEach(entry => {
          if (balances[entry.accountId]) {
            balances[entry.accountId].debitMovements += entry.debit;
            balances[entry.accountId].creditMovements += entry.credit;
            
            // تحديث الرصيد الختامي حسب طبيعة الحساب
            const account = accounts.find(acc => acc.id === entry.accountId);
            if (account) {
              if (['assets', 'expenses'].includes(account.category)) {
                // الأصول والمصروفات تزيد بالمدين وتنقص بالدائن
                balances[entry.accountId].closingBalance += (entry.debit - entry.credit);
              } else {
                // الخصوم وحقوق الملكية والإيرادات تزيد بالدائن وتنقص بالمدين
                balances[entry.accountId].closingBalance += (entry.credit - entry.debit);
              }
            }
          }
        });
      });

    return Object.values(balances).filter(account => {
      if (filterCategory === 'all') return true;
      return account.category === filterCategory;
    });
  }, [accounts, transactions, financialPeriod, filterCategory]);

  const totals = useMemo(() => {
    return trialBalanceData.reduce((acc, account) => {
      const debitBalance = account.closingBalance > 0 ? account.closingBalance : 0;
      const creditBalance = account.closingBalance < 0 ? Math.abs(account.closingBalance) : 0;
      
      return {
        totalDebitMovements: acc.totalDebitMovements + account.debitMovements,
        totalCreditMovements: acc.totalCreditMovements + account.creditMovements,
        totalDebitBalance: acc.totalDebitBalance + debitBalance,
        totalCreditBalance: acc.totalCreditBalance + creditBalance
      };
    }, {
      totalDebitMovements: 0,
      totalCreditMovements: 0,
      totalDebitBalance: 0,
      totalCreditBalance: 0
    });
  }, [trialBalanceData]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(Math.abs(amount));
  };

  const exportToExcel = () => {
    // تصدير البيانات إلى Excel
    const csvContent = [
      ['رقم الحساب', 'اسم الحساب', 'الرصيد الافتتاحي', 'حركة مدينة', 'حركة دائنة', 'الرصيد الختامي مدين', 'الرصيد الختامي دائن'],
      ...trialBalanceData.map(account => [
        account.id,
        account.name,
        account.openingBalance,
        account.debitMovements,
        account.creditMovements,
        account.closingBalance > 0 ? account.closingBalance : 0,
        account.closingBalance < 0 ? Math.abs(account.closingBalance) : 0
      ]),
      ['', 'الإجمالي', '', totals.totalDebitMovements, totals.totalCreditMovements, totals.totalDebitBalance, totals.totalCreditBalance]
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `trial_balance_${financialPeriod.startDate}_${financialPeriod.endDate}.csv`;
    link.click();
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل ميزان المراجعة...</p>
      </div>
    );
  }

  return (
    <div className="trial-balance">
      <div className="trial-balance-header">
        <h3>ميزان المراجعة</h3>
        <div className="header-actions">
          <button className="export-btn" onClick={exportToExcel}>
            📊 تصدير Excel
          </button>
          <button 
            className="details-btn"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? '📋 عرض مبسط' : '📋 عرض تفصيلي'}
          </button>
        </div>
      </div>

      <div className="period-info">
        <h4>الفترة المالية: من {financialPeriod.startDate} إلى {financialPeriod.endDate}</h4>
      </div>

      <div className="trial-balance-filters">
        <div className="category-filter">
          <label>فئة الحسابات:</label>
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <option value="all">جميع الفئات</option>
            <option value="assets">الأصول</option>
            <option value="liabilities">الخصوم</option>
            <option value="equity">حقوق الملكية</option>
            <option value="revenue">الإيرادات</option>
            <option value="expenses">المصروفات</option>
          </select>
        </div>
      </div>

      <div className="trial-balance-table">
        <div className="table-header">
          <div>رقم الحساب</div>
          <div>اسم الحساب</div>
          {showDetails && <div>الرصيد الافتتاحي</div>}
          {showDetails && <div>حركة مدينة</div>}
          {showDetails && <div>حركة دائنة</div>}
          <div>رصيد مدين</div>
          <div>رصيد دائن</div>
        </div>

        {trialBalanceData.map(account => (
          <div key={account.id} className="table-row">
            <div className="account-code">{account.id}</div>
            <div className="account-name">{account.name}</div>
            {showDetails && (
              <div className="opening-balance">
                {formatCurrency(account.openingBalance)}
              </div>
            )}
            {showDetails && (
              <div className="debit-movements">
                {account.debitMovements > 0 ? formatCurrency(account.debitMovements) : '-'}
              </div>
            )}
            {showDetails && (
              <div className="credit-movements">
                {account.creditMovements > 0 ? formatCurrency(account.creditMovements) : '-'}
              </div>
            )}
            <div className="debit-balance">
              {account.closingBalance > 0 ? formatCurrency(account.closingBalance) : '-'}
            </div>
            <div className="credit-balance">
              {account.closingBalance < 0 ? formatCurrency(account.closingBalance) : '-'}
            </div>
          </div>
        ))}

        <div className="table-footer">
          <div className="total-label">الإجمالي</div>
          <div></div>
          {showDetails && <div></div>}
          {showDetails && (
            <div className="total-debit-movements">
              {formatCurrency(totals.totalDebitMovements)}
            </div>
          )}
          {showDetails && (
            <div className="total-credit-movements">
              {formatCurrency(totals.totalCreditMovements)}
            </div>
          )}
          <div className="total-debit-balance">
            {formatCurrency(totals.totalDebitBalance)}
          </div>
          <div className="total-credit-balance">
            {formatCurrency(totals.totalCreditBalance)}
          </div>
        </div>
      </div>

      <div className="balance-verification">
        <div className={`verification-status ${
          Math.abs(totals.totalDebitBalance - totals.totalCreditBalance) < 0.01 ? 'balanced' : 'unbalanced'
        }`}>
          {Math.abs(totals.totalDebitBalance - totals.totalCreditBalance) < 0.01 
            ? '✅ الميزان متوازن' 
            : '❌ الميزان غير متوازن'
          }
        </div>
        
        {showDetails && (
          <div className="movements-verification">
            <div className={`movements-status ${
              Math.abs(totals.totalDebitMovements - totals.totalCreditMovements) < 0.01 ? 'balanced' : 'unbalanced'
            }`}>
              الحركات: {Math.abs(totals.totalDebitMovements - totals.totalCreditMovements) < 0.01 
                ? '✅ متوازنة' 
                : '❌ غير متوازنة'
              }
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// مكون القوائم المالية
export const FinancialStatements = ({ accounts, transactions, financialPeriod, isLoading }) => {
  const [activeStatement, setActiveStatement] = useState('income');
  
  const calculateFinancialData = useMemo(() => {
    const data = {
      income: { revenue: 0, expenses: 0, netIncome: 0 },
      balance: { 
        assets: { current: 0, fixed: 0, total: 0 },
        liabilities: { current: 0, longTerm: 0, total: 0 },
        equity: { capital: 0, retained: 0, current: 0, total: 0 }
      },
      cashFlow: { operating: 0, investing: 0, financing: 0, net: 0 }
    };

    // حساب بيانات قائمة الدخل
    accounts.forEach(account => {
      if (account.category === 'revenue') {
        data.income.revenue += Math.abs(account.balance);
      } else if (account.category === 'expenses') {
        data.income.expenses += Math.abs(account.balance);
      }
    });
    data.income.netIncome = data.income.revenue - data.income.expenses;

    // حساب بيانات الميزانية العمومية
    accounts.forEach(account => {
      if (account.category === 'assets') {
        if (account.id.startsWith('11')) { // الأصول المتداولة
          data.balance.assets.current += account.balance;
        } else if (account.id.startsWith('12')) { // الأصول الثابتة
          data.balance.assets.fixed += account.balance;
        }
      } else if (account.category === 'liabilities') {
        if (account.id.startsWith('21')) { // الخصوم المتداولة
          data.balance.liabilities.current += account.balance;
        } else if (account.id.startsWith('22')) { // الخصوم طويلة الأجل
          data.balance.liabilities.longTerm += account.balance;
        }
      } else if (account.category === 'equity') {
        if (account.id === '3100') { // رأس المال
          data.balance.equity.capital += account.balance;
        } else if (account.id === '3200') { // الأرباح المحتجزة
          data.balance.equity.retained += account.balance;
        } else if (account.id === '3300') { // أرباح السنة الحالية
          data.balance.equity.current += account.balance;
        }
      }
    });

    data.balance.assets.total = data.balance.assets.current + data.balance.assets.fixed;
    data.balance.liabilities.total = data.balance.liabilities.current + data.balance.liabilities.longTerm;
    data.balance.equity.total = data.balance.equity.capital + data.balance.equity.retained + data.balance.equity.current;

    return data;
  }, [accounts]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const renderIncomeStatement = () => (
    <div className="income-statement">
      <h4>قائمة الدخل</h4>
      <div className="statement-period">
        للفترة من {financialPeriod.startDate} إلى {financialPeriod.endDate}
      </div>
      
      <div className="statement-table">
        <div className="statement-section">
          <div className="section-header">الإيرادات</div>
          <div className="statement-row">
            <span>إجمالي الإيرادات</span>
            <span className="amount positive">{formatCurrency(calculateFinancialData.income.revenue)}</span>
          </div>
        </div>

        <div className="statement-section">
          <div className="section-header">المصروفات</div>
          <div className="statement-row">
            <span>إجمالي المصروفات</span>
            <span className="amount negative">{formatCurrency(calculateFinancialData.income.expenses)}</span>
          </div>
        </div>

        <div className="statement-total">
          <div className="total-row">
            <span>صافي الدخل</span>
            <span className={`amount ${calculateFinancialData.income.netIncome >= 0 ? 'positive' : 'negative'}`}>
              {formatCurrency(calculateFinancialData.income.netIncome)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBalanceSheet = () => (
    <div className="balance-sheet">
      <h4>الميزانية العمومية</h4>
      <div className="statement-period">
        كما في {financialPeriod.endDate}
      </div>
      
      <div className="balance-sheet-grid">
        <div className="balance-section">
          <div className="section-header">الأصول</div>
          
          <div className="subsection">
            <div className="subsection-header">الأصول المتداولة</div>
            <div className="statement-row">
              <span>النقدية والبنوك</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.assets.current)}</span>
            </div>
          </div>

          <div className="subsection">
            <div className="subsection-header">الأصول الثابتة</div>
            <div className="statement-row">
              <span>الأثاث والمعدات</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.assets.fixed)}</span>
            </div>
          </div>

          <div className="section-total">
            <div className="total-row">
              <span>إجمالي الأصول</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.assets.total)}</span>
            </div>
          </div>
        </div>

        <div className="balance-section">
          <div className="section-header">الخصوم وحقوق الملكية</div>
          
          <div className="subsection">
            <div className="subsection-header">الخصوم المتداولة</div>
            <div className="statement-row">
              <span>الموردون والذمم الدائنة</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.liabilities.current)}</span>
            </div>
          </div>

          <div className="subsection">
            <div className="subsection-header">الخصوم طويلة الأجل</div>
            <div className="statement-row">
              <span>قروض البنوك</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.liabilities.longTerm)}</span>
            </div>
          </div>

          <div className="subsection">
            <div className="subsection-header">حقوق الملكية</div>
            <div className="statement-row">
              <span>رأس المال</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.equity.capital)}</span>
            </div>
            <div className="statement-row">
              <span>الأرباح المحتجزة</span>
              <span className="amount">{formatCurrency(calculateFinancialData.balance.equity.retained)}</span>
            </div>
          </div>

          <div className="section-total">
            <div className="total-row">
              <span>إجمالي الخصوم وحقوق الملكية</span>
              <span className="amount">
                {formatCurrency(calculateFinancialData.balance.liabilities.total + calculateFinancialData.balance.equity.total)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل القوائم المالية...</p>
      </div>
    );
  }

  return (
    <div className="financial-statements">
      <div className="statements-header">
        <h3>القوائم المالية</h3>
        <div className="statement-tabs">
          <button 
            className={`tab-btn ${activeStatement === 'income' ? 'active' : ''}`}
            onClick={() => setActiveStatement('income')}
          >
            قائمة الدخل
          </button>
          <button 
            className={`tab-btn ${activeStatement === 'balance' ? 'active' : ''}`}
            onClick={() => setActiveStatement('balance')}
          >
            الميزانية العمومية
          </button>
          <button 
            className={`tab-btn ${activeStatement === 'cashflow' ? 'active' : ''}`}
            onClick={() => setActiveStatement('cashflow')}
          >
            قائمة التدفقات النقدية
          </button>
        </div>
      </div>

      <div className="statement-content">
        {activeStatement === 'income' && renderIncomeStatement()}
        {activeStatement === 'balance' && renderBalanceSheet()}
        {activeStatement === 'cashflow' && (
          <div className="cash-flow-statement">
            <h4>قائمة التدفقات النقدية</h4>
            <p>قيد التطوير...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export const AccountsReceivable = ({ accounts, transactions, isLoading }) => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // بيانات تجريبية للذمم المدينة
  const receivablesData = [
    {
      id: 'AR-001',
      customerName: 'أحمد محمد العلي',
      invoiceNumber: 'INV-2024-001',
      invoiceDate: '2024-01-15',
      dueDate: '2024-02-14',
      originalAmount: 15000,
      paidAmount: 5000,
      remainingAmount: 10000,
      status: 'overdue',
      daysPastDue: 15,
      customerPhone: '+************',
      customerEmail: '<EMAIL>'
    },
    {
      id: 'AR-002',
      customerName: 'شركة السفر الذهبي',
      invoiceNumber: 'INV-2024-002',
      invoiceDate: '2024-01-20',
      dueDate: '2024-02-19',
      originalAmount: 25000,
      paidAmount: 25000,
      remainingAmount: 0,
      status: 'paid',
      daysPastDue: 0,
      customerPhone: '+************',
      customerEmail: '<EMAIL>'
    },
    {
      id: 'AR-003',
      customerName: 'فاطمة سالم',
      invoiceNumber: 'INV-2024-003',
      invoiceDate: '2024-02-01',
      dueDate: '2024-03-02',
      originalAmount: 8000,
      paidAmount: 0,
      remainingAmount: 8000,
      status: 'pending',
      daysPastDue: 0,
      customerPhone: '+966503456789',
      customerEmail: '<EMAIL>'
    },
    {
      id: 'AR-004',
      customerName: 'مؤسسة الرحلات المميزة',
      invoiceNumber: 'INV-2024-004',
      invoiceDate: '2024-02-05',
      dueDate: '2024-03-06',
      originalAmount: 35000,
      paidAmount: 15000,
      remainingAmount: 20000,
      status: 'partial',
      daysPastDue: 0,
      customerPhone: '+966504567890',
      customerEmail: '<EMAIL>'
    }
  ];

  const filteredReceivables = useMemo(() => {
    return receivablesData.filter(item => {
      const matchesSearch = item.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [searchTerm, filterStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  const summary = useMemo(() => {
    return receivablesData.reduce((acc, item) => {
      acc.totalOriginal += item.originalAmount;
      acc.totalPaid += item.paidAmount;
      acc.totalRemaining += item.remainingAmount;
      
      if (item.status === 'overdue') {
        acc.totalOverdue += item.remainingAmount;
        acc.overdueCount++;
      }
      
      return acc;
    }, {
      totalOriginal: 0,
      totalPaid: 0,
      totalRemaining: 0,
      totalOverdue: 0,
      overdueCount: 0
    });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusLabel = (status) => {
    const labels = {
      paid: 'مدفوع',
      pending: 'معلق',
      partial: 'مدفوع جزئياً',
      overdue: 'متأخر'
    };
    return labels[status] || status;
  };

  const getStatusColor = (status) => {
    const colors = {
      paid: '#28a745',
      pending: '#ffc107',
      partial: '#17a2b8',
      overdue: '#dc3545'
    };
    return colors[status] || '#6c757d';
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الذمم المدينة...</p>
      </div>
    );
  }

  return (
    <div className="accounts-receivable">
      <div className="receivables-header">
        <h3>الذمم المدينة</h3>
        <div className="header-actions">
          <button className="export-btn">📊 تصدير Excel</button>
          <button className="print-btn">🖨️ طباعة</button>
        </div>
      </div>

      {/* ملخص الذمم المدينة */}
      <div className="receivables-summary">
        <div className="summary-card">
          <div className="summary-icon">💰</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalOriginal)}</div>
            <div className="summary-label">إجمالي الفواتير</div>
          </div>
        </div>

        <div className="summary-card">
          <div className="summary-icon">✅</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalPaid)}</div>
            <div className="summary-label">المبلغ المحصل</div>
          </div>
        </div>

        <div className="summary-card">
          <div className="summary-icon">⏳</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalRemaining)}</div>
            <div className="summary-label">المبلغ المتبقي</div>
          </div>
        </div>

        <div className="summary-card alert">
          <div className="summary-icon">⚠️</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalOverdue)}</div>
            <div className="summary-label">المتأخرات ({summary.overdueCount})</div>
          </div>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="receivables-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في الذمم المدينة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="status-filter">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="pending">معلق</option>
            <option value="partial">مدفوع جزئياً</option>
            <option value="overdue">متأخر</option>
            <option value="paid">مدفوع</option>
          </select>
        </div>
      </div>

      {/* جدول الذمم المدينة */}
      <div className="receivables-table">
        <div className="table-header">
          <div>العميل</div>
          <div>رقم الفاتورة</div>
          <div>تاريخ الفاتورة</div>
          <div>تاريخ الاستحقاق</div>
          <div>المبلغ الأصلي</div>
          <div>المبلغ المدفوع</div>
          <div>المبلغ المتبقي</div>
          <div>الحالة</div>
          <div>إجراءات</div>
        </div>

        {filteredReceivables.map(item => (
          <div key={item.id} className="table-row">
            <div className="customer-info">
              <div className="customer-name">{item.customerName}</div>
              <div className="customer-contact">
                <span>{item.customerPhone}</span>
                <span>{item.customerEmail}</span>
              </div>
            </div>
            
            <div className="invoice-number">{item.invoiceNumber}</div>
            <div className="invoice-date">{item.invoiceDate}</div>
            <div className="due-date">
              {item.dueDate}
              {item.daysPastDue > 0 && (
                <span className="overdue-days">({item.daysPastDue} يوم متأخر)</span>
              )}
            </div>
            <div className="original-amount">{formatCurrency(item.originalAmount)}</div>
            <div className="paid-amount">{formatCurrency(item.paidAmount)}</div>
            <div className="remaining-amount">{formatCurrency(item.remainingAmount)}</div>
            
            <div className="status">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(item.status) }}
              >
                {getStatusLabel(item.status)}
              </span>
            </div>
            
            <div className="actions">
              <button className="action-btn view" title="عرض التفاصيل">👁️</button>
              <button className="action-btn edit" title="تعديل">✏️</button>
              <button className="action-btn payment" title="تسجيل دفعة">💳</button>
              <button className="action-btn reminder" title="إرسال تذكير">📧</button>
            </div>
          </div>
        ))}
      </div>

      {filteredReceivables.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">📋</div>
          <h4>لا توجد ذمم مدينة</h4>
          <p>لم يتم العثور على أي ذمم مدينة تطابق معايير البحث</p>
        </div>
      )}
    </div>
  );
};

export const AccountsPayable = ({ accounts, transactions, isLoading }) => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [selectedBill, setSelectedBill] = useState(null);

  // بيانات تجريبية للذمم الدائنة
  const payablesData = [
    {
      id: 'AP-001',
      supplierName: 'شركة الطيران السعودي',
      billNumber: 'BILL-2024-001',
      billDate: '2024-01-10',
      dueDate: '2024-02-09',
      originalAmount: 45000,
      paidAmount: 15000,
      remainingAmount: 30000,
      status: 'partial',
      daysToDue: 5,
      supplierPhone: '+************',
      supplierEmail: '<EMAIL>',
      category: 'airline_tickets',
      priority: 'high'
    },
    {
      id: 'AP-002',
      supplierName: 'فندق الريتز كارلتون',
      billNumber: 'BILL-2024-002',
      billDate: '2024-01-15',
      dueDate: '2024-02-14',
      originalAmount: 28000,
      paidAmount: 0,
      remainingAmount: 28000,
      status: 'pending',
      daysToDue: 10,
      supplierPhone: '+966114567890',
      supplierEmail: '<EMAIL>',
      category: 'accommodation',
      priority: 'medium'
    },
    {
      id: 'AP-003',
      supplierName: 'شركة النقل المتميز',
      billNumber: 'BILL-2024-003',
      billDate: '2024-01-20',
      dueDate: '2024-01-25',
      originalAmount: 12000,
      paidAmount: 0,
      remainingAmount: 12000,
      status: 'overdue',
      daysToDue: -10,
      supplierPhone: '+966115678901',
      supplierEmail: '<EMAIL>',
      category: 'transportation',
      priority: 'urgent'
    },
    {
      id: 'AP-004',
      supplierName: 'مكتب الخدمات السياحية',
      billNumber: 'BILL-2024-004',
      billDate: '2024-02-01',
      dueDate: '2024-03-02',
      originalAmount: 18000,
      paidAmount: 18000,
      remainingAmount: 0,
      status: 'paid',
      daysToDue: 0,
      supplierPhone: '+966116789012',
      supplierEmail: '<EMAIL>',
      category: 'tour_services',
      priority: 'low'
    }
  ];

  const filteredPayables = useMemo(() => {
    return payablesData.filter(item => {
      const matchesSearch = item.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.billNumber.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [searchTerm, filterStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  const summary = useMemo(() => {
    return payablesData.reduce((acc, item) => {
      acc.totalOriginal += item.originalAmount;
      acc.totalPaid += item.paidAmount;
      acc.totalRemaining += item.remainingAmount;
      
      if (item.status === 'overdue') {
        acc.totalOverdue += item.remainingAmount;
        acc.overdueCount++;
      }

      if (item.priority === 'urgent') {
        acc.urgentCount++;
        acc.urgentAmount += item.remainingAmount;
      }
      
      return acc;
    }, {
      totalOriginal: 0,
      totalPaid: 0,
      totalRemaining: 0,
      totalOverdue: 0,
      overdueCount: 0,
      urgentCount: 0,
      urgentAmount: 0
    });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusLabel = (status) => {
    const labels = {
      paid: 'مدفوع',
      pending: 'معلق',
      partial: 'مدفوع جزئياً',
      overdue: 'متأخر'
    };
    return labels[status] || status;
  };

  const getStatusColor = (status) => {
    const colors = {
      paid: '#28a745',
      pending: '#ffc107',
      partial: '#17a2b8',
      overdue: '#dc3545'
    };
    return colors[status] || '#6c757d';
  };

  const getPriorityLabel = (priority) => {
    const labels = {
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      urgent: 'عاجل'
    };
    return labels[priority] || priority;
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#fd7e14',
      urgent: '#dc3545'
    };
    return colors[priority] || '#6c757d';
  };

  const getCategoryLabel = (category) => {
    const labels = {
      airline_tickets: 'تذاكر طيران',
      accommodation: 'إقامة',
      transportation: 'نقل',
      tour_services: 'خدمات سياحية',
      visa_services: 'خدمات تأشيرات',
      insurance: 'تأمين'
    };
    return labels[category] || category;
  };

  const handlePayment = (bill) => {
    setSelectedBill(bill);
    setShowPaymentForm(true);
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الذمم الدائنة...</p>
      </div>
    );
  }

  return (
    <div className="accounts-payable">
      <div className="payables-header">
        <h3>الذمم الدائنة</h3>
        <div className="header-actions">
          <button className="action-btn primary">➕ إضافة فاتورة</button>
          <button className="export-btn">📊 تصدير Excel</button>
          <button className="print-btn">🖨️ طباعة</button>
        </div>
      </div>

      {/* ملخص الذمم الدائنة */}
      <div className="payables-summary">
        <div className="summary-card">
          <div className="summary-icon">💰</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalOriginal)}</div>
            <div className="summary-label">إجمالي الفواتير</div>
          </div>
        </div>

        <div className="summary-card">
          <div className="summary-icon">✅</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalPaid)}</div>
            <div className="summary-label">المبلغ المدفوع</div>
          </div>
        </div>

        <div className="summary-card">
          <div className="summary-icon">⏳</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalRemaining)}</div>
            <div className="summary-label">المبلغ المتبقي</div>
          </div>
        </div>

        <div className="summary-card alert">
          <div className="summary-icon">⚠️</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.totalOverdue)}</div>
            <div className="summary-label">المتأخرات ({summary.overdueCount})</div>
          </div>
        </div>

        <div className="summary-card urgent">
          <div className="summary-icon">🚨</div>
          <div className="summary-content">
            <div className="summary-value">{formatCurrency(summary.urgentAmount)}</div>
            <div className="summary-label">عاجل ({summary.urgentCount})</div>
          </div>
        </div>
      </div>

      {/* فلاتر البحث */}
      <div className="payables-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في الذمم الدائنة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="status-filter">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="pending">معلق</option>
            <option value="partial">مدفوع جزئياً</option>
            <option value="overdue">متأخر</option>
            <option value="paid">مدفوع</option>
          </select>
        </div>

        <div className="quick-filters">
          <button className="filter-btn urgent">🚨 عاجل</button>
          <button className="filter-btn overdue">⚠️ متأخر</button>
          <button className="filter-btn due-soon">📅 مستحق قريباً</button>
        </div>
      </div>

      {/* جدول الذمم الدائنة */}
      <div className="payables-table">
        <div className="table-header">
          <div>المورد</div>
          <div>رقم الفاتورة</div>
          <div>تاريخ الفاتورة</div>
          <div>تاريخ الاستحقاق</div>
          <div>الفئة</div>
          <div>المبلغ الأصلي</div>
          <div>المبلغ المدفوع</div>
          <div>المبلغ المتبقي</div>
          <div>الأولوية</div>
          <div>الحالة</div>
          <div>إجراءات</div>
        </div>

        {filteredPayables.map(item => (
          <div key={item.id} className={`table-row ${item.priority}`}>
            <div className="supplier-info">
              <div className="supplier-name">{item.supplierName}</div>
              <div className="supplier-contact">
                <span>{item.supplierPhone}</span>
                <span>{item.supplierEmail}</span>
              </div>
            </div>
            
            <div className="bill-number">{item.billNumber}</div>
            <div className="bill-date">{item.billDate}</div>
            <div className="due-date">
              {item.dueDate}
              {item.daysToDue < 0 && (
                <span className="overdue-days">({Math.abs(item.daysToDue)} يوم متأخر)</span>
              )}
              {item.daysToDue > 0 && item.daysToDue <= 7 && (
                <span className="due-soon">({item.daysToDue} يوم متبقي)</span>
              )}
            </div>
            <div className="category">{getCategoryLabel(item.category)}</div>
            <div className="original-amount">{formatCurrency(item.originalAmount)}</div>
            <div className="paid-amount">{formatCurrency(item.paidAmount)}</div>
            <div className="remaining-amount">{formatCurrency(item.remainingAmount)}</div>
            
            <div className="priority">
              <span 
                className="priority-badge"
                style={{ backgroundColor: getPriorityColor(item.priority) }}
              >
                {getPriorityLabel(item.priority)}
              </span>
            </div>

            <div className="status">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(item.status) }}
              >
                {getStatusLabel(item.status)}
              </span>
            </div>
            
            <div className="actions">
              <button className="action-btn view" title="عرض التفاصيل">👁️</button>
              <button className="action-btn edit" title="تعديل">✏️</button>
              {item.remainingAmount > 0 && (
                <button 
                  className="action-btn payment" 
                  title="تسجيل دفعة"
                  onClick={() => handlePayment(item)}
                >
                  💳
                </button>
              )}
              <button className="action-btn schedule" title="جدولة دفع">📅</button>
            </div>
          </div>
        ))}
      </div>

      {/* نموذج تسجيل الدفعة */}
      {showPaymentForm && selectedBill && (
        <div className="payment-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>تسجيل دفعة - {selectedBill.supplierName}</h4>
              <button 
                className="close-btn"
                onClick={() => setShowPaymentForm(false)}
              >
                ❌
              </button>
            </div>
            
            <div className="modal-body">
              <div className="bill-info">
                <div className="info-row">
                  <span>رقم الفاتورة:</span>
                  <span>{selectedBill.billNumber}</span>
                </div>
                <div className="info-row">
                  <span>المبلغ المتبقي:</span>
                  <span>{formatCurrency(selectedBill.remainingAmount)}</span>
                </div>
              </div>

              <div className="payment-form">
                <div className="form-group">
                  <label>تاريخ الدفع</label>
                  <input type="date" defaultValue={new Date().toISOString().split('T')[0]} />
                </div>

                <div className="form-group">
                  <label>مبلغ الدفعة</label>
                  <input 
                    type="number" 
                    step="0.01"
                    max={selectedBill.remainingAmount}
                    placeholder="أدخل مبلغ الدفعة"
                  />
                </div>

                <div className="form-group">
                  <label>طريقة الدفع</label>
                  <select>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="check">شيك</option>
                    <option value="cash">نقداً</option>
                    <option value="credit_card">بطاقة ائتمان</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>رقم المرجع</label>
                  <input type="text" placeholder="رقم التحويل أو الشيك" />
                </div>

                <div className="form-group">
                  <label>ملاحظات</label>
                  <textarea rows="3" placeholder="ملاحظات إضافية"></textarea>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="save-btn">💾 حفظ الدفعة</button>
              <button 
                className="cancel-btn"
                onClick={() => setShowPaymentForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {filteredPayables.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">📋</div>
          <h4>لا توجد ذمم دائنة</h4>
          <p>لم يتم العثور على أي ذمم دائنة تطابق معايير البحث</p>
        </div>
      )}
    </div>
  );
};

export const BankReconciliation = ({ accounts, transactions, isLoading }) => {
  const [selectedBank, setSelectedBank] = useState('');
  const [reconciliationPeriod, setReconciliationPeriod] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [bankStatement, setBankStatement] = useState([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [reconciliationStatus, setReconciliationStatus] = useState('in_progress');

  // بيانات تجريبية للبنوك
  const bankAccounts = [
    { id: '1112001', name: 'البنك الأهلي - الحساب الجاري', balance: 250000 },
    { id: '1112002', name: 'بنك الراجحي - حساب التوفير', balance: 180000 },
    { id: '1112003', name: 'بنك سامبا - الحساب التجاري', balance: 320000 },
    { id: '1112004', name: 'بنك الرياض - حساب بالدولار', balance: 75000 }
  ];

  // بيانات تجريبية لكشف البنك
  const bankStatementData = [
    {
      id: 'BS-001',
      date: '2024-02-01',
      description: 'تحويل من العميل أحمد محمد',
      reference: 'TRF-240201-001',
      debit: 0,
      credit: 15000,
      balance: 265000,
      matched: true,
      matchedTransactionId: 'JE-001'
    },
    {
      id: 'BS-002',
      date: '2024-02-02',
      description: 'دفع لشركة الطيران السعودي',
      reference: 'PAY-240202-001',
      debit: 45000,
      credit: 0,
      balance: 220000,
      matched: false,
      matchedTransactionId: null
    },
    {
      id: 'BS-003',
      date: '2024-02-03',
      description: 'رسوم بنكية',
      reference: 'FEE-240203-001',
      debit: 150,
      credit: 0,
      balance: 219850,
      matched: false,
      matchedTransactionId: null
    },
    {
      id: 'BS-004',
      date: '2024-02-04',
      description: 'تحويل من العميل فاطمة سالم',
      reference: 'TRF-240204-001',
      debit: 0,
      credit: 8000,
      balance: 227850,
      matched: true,
      matchedTransactionId: 'JE-003'
    },
    {
      id: 'BS-005',
      date: '2024-02-05',
      description: 'سحب نقدي',
      reference: 'ATM-240205-001',
      debit: 5000,
      credit: 0,
      balance: 222850,
      matched: false,
      matchedTransactionId: null
    }
  ];

  // بيانات تجريبية للمعاملات المحاسبية
  const bookTransactions = [
    {
      id: 'JE-001',
      date: '2024-02-01',
      description: 'تحصيل من العميل أحمد محمد',
      debit: 15000,
      credit: 0,
      matched: true,
      matchedStatementId: 'BS-001'
    },
    {
      id: 'JE-002',
      date: '2024-02-02',
      description: 'دفع فاتورة كهرباء',
      debit: 0,
      credit: 2500,
      matched: false,
      matchedStatementId: null
    },
    {
      id: 'JE-003',
      date: '2024-02-04',
      description: 'تحصيل من العميل فاطمة سالم',
      debit: 8000,
      credit: 0,
      matched: true,
      matchedStatementId: 'BS-004'
    },
    {
      id: 'JE-004',
      date: '2024-02-06',
      description: 'إيداع نقدي',
      debit: 10000,
      credit: 0,
      matched: false,
      matchedStatementId: null
    }
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const calculateReconciliation = () => {
    const bookBalance = bookTransactions.reduce((acc, trans) => {
      return acc + trans.debit - trans.credit;
    }, 250000); // الرصيد الافتتاحي

    const bankBalance = bankStatementData[bankStatementData.length - 1]?.balance || 0;
    
    const unmatchedBankItems = bankStatementData.filter(item => !item.matched);
    const unmatchedBookItems = bookTransactions.filter(item => !item.matched);
    
    const outstandingDeposits = unmatchedBookItems
      .filter(item => item.debit > 0)
      .reduce((sum, item) => sum + item.debit, 0);
    
    const outstandingChecks = unmatchedBookItems
      .filter(item => item.credit > 0)
      .reduce((sum, item) => sum + item.credit, 0);

    const bankCharges = unmatchedBankItems
      .filter(item => item.debit > 0)
      .reduce((sum, item) => sum + item.debit, 0);

    const bankInterest = unmatchedBankItems
      .filter(item => item.credit > 0)
      .reduce((sum, item) => sum + item.credit, 0);

    const adjustedBookBalance = bookBalance + bankInterest - bankCharges;
    const adjustedBankBalance = bankBalance + outstandingDeposits - outstandingChecks;

    return {
      bookBalance,
      bankBalance,
      adjustedBookBalance,
      adjustedBankBalance,
      difference: adjustedBookBalance - adjustedBankBalance,
      outstandingDeposits,
      outstandingChecks,
      bankCharges,
      bankInterest,
      unmatchedBankItems,
      unmatchedBookItems,
      isReconciled: Math.abs(adjustedBookBalance - adjustedBankBalance) < 0.01
    };
  };

  const reconciliationData = calculateReconciliation();

  const handleMatch = (bankItem, bookItem) => {
    // تطبيق المطابقة
    console.log('مطابقة:', bankItem, bookItem);
  };

  const handleAutoMatch = () => {
    // مطابقة تلقائية
    console.log('مطابقة تلقائية');
  };

  const handleUploadStatement = () => {
    setShowUploadModal(true);
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل تسوية البنوك...</p>
      </div>
    );
  }

  return (
    <div className="bank-reconciliation">
      <div className="reconciliation-header">
        <h3>تسوية البنوك</h3>
        <div className="header-actions">
          <button className="action-btn primary" onClick={handleUploadStatement}>
            📤 رفع كشف البنك
          </button>
          <button className="action-btn secondary" onClick={handleAutoMatch}>
            🔄 مطابقة تلقائية
          </button>
          <button className="export-btn">📊 تصدير التقرير</button>
        </div>
      </div>

      {/* إعدادات التسوية */}
      <div className="reconciliation-settings">
        <div className="settings-row">
          <div className="form-group">
            <label>البنك</label>
            <select
              value={selectedBank}
              onChange={(e) => setSelectedBank(e.target.value)}
            >
              <option value="">اختر البنك</option>
              {bankAccounts.map(bank => (
                <option key={bank.id} value={bank.id}>
                  {bank.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label>من تاريخ</label>
            <input
              type="date"
              value={reconciliationPeriod.startDate}
              onChange={(e) => setReconciliationPeriod({
                ...reconciliationPeriod,
                startDate: e.target.value
              })}
            />
          </div>

          <div className="form-group">
            <label>إلى تاريخ</label>
            <input
              type="date"
              value={reconciliationPeriod.endDate}
              onChange={(e) => setReconciliationPeriod({
                ...reconciliationPeriod,
                endDate: e.target.value
              })}
            />
          </div>

          <div className="reconciliation-status">
            <span className={`status-indicator ${reconciliationData.isReconciled ? 'reconciled' : 'unreconciled'}`}>
              {reconciliationData.isReconciled ? '✅ متوازن' : '❌ غير متوازن'}
            </span>
          </div>
        </div>
      </div>

      {/* ملخص التسوية */}
      <div className="reconciliation-summary">
        <div className="summary-section">
          <h4>ملخص التسوية</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <span className="label">رصيد الدفاتر:</span>
              <span className="value">{formatCurrency(reconciliationData.bookBalance)}</span>
            </div>
            <div className="summary-item">
              <span className="label">رصيد البنك:</span>
              <span className="value">{formatCurrency(reconciliationData.bankBalance)}</span>
            </div>
            <div className="summary-item">
              <span className="label">الإيداعات المعلقة:</span>
              <span className="value positive">{formatCurrency(reconciliationData.outstandingDeposits)}</span>
            </div>
            <div className="summary-item">
              <span className="label">الشيكات المعلقة:</span>
              <span className="value negative">{formatCurrency(reconciliationData.outstandingChecks)}</span>
            </div>
            <div className="summary-item">
              <span className="label">رسوم بنكية:</span>
              <span className="value negative">{formatCurrency(reconciliationData.bankCharges)}</span>
            </div>
            <div className="summary-item">
              <span className="label">فوائد بنكية:</span>
              <span className="value positive">{formatCurrency(reconciliationData.bankInterest)}</span>
            </div>
            <div className="summary-item total">
              <span className="label">الرصيد المعدل - الدفاتر:</span>
              <span className="value">{formatCurrency(reconciliationData.adjustedBookBalance)}</span>
            </div>
            <div className="summary-item total">
              <span className="label">الرصيد المعدل - البنك:</span>
              <span className="value">{formatCurrency(reconciliationData.adjustedBankBalance)}</span>
            </div>
            <div className={`summary-item difference ${reconciliationData.difference === 0 ? 'zero' : 'non-zero'}`}>
              <span className="label">الفرق:</span>
              <span className="value">{formatCurrency(reconciliationData.difference)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* جداول المطابقة */}
      <div className="reconciliation-tables">
        <div className="table-section">
          <h4>كشف البنك</h4>
          <div className="reconciliation-table">
            <div className="table-header">
              <div>التاريخ</div>
              <div>الوصف</div>
              <div>المرجع</div>
              <div>مدين</div>
              <div>دائن</div>
              <div>الرصيد</div>
              <div>الحالة</div>
              <div>إجراءات</div>
            </div>

            {bankStatementData.map(item => (
              <div key={item.id} className={`table-row ${item.matched ? 'matched' : 'unmatched'}`}>
                <div className="date">{item.date}</div>
                <div className="description">{item.description}</div>
                <div className="reference">{item.reference}</div>
                <div className="debit">{item.debit > 0 ? formatCurrency(item.debit) : '-'}</div>
                <div className="credit">{item.credit > 0 ? formatCurrency(item.credit) : '-'}</div>
                <div className="balance">{formatCurrency(item.balance)}</div>
                <div className="status">
                  {item.matched ? (
                    <span className="matched-badge">✅ مطابق</span>
                  ) : (
                    <span className="unmatched-badge">❌ غير مطابق</span>
                  )}
                </div>
                <div className="actions">
                  {!item.matched && (
                    <button className="match-btn" title="مطابقة">🔗</button>
                  )}
                  <button className="view-btn" title="عرض">👁️</button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="table-section">
          <h4>معاملات الدفاتر</h4>
          <div className="reconciliation-table">
            <div className="table-header">
              <div>التاريخ</div>
              <div>الوصف</div>
              <div>مدين</div>
              <div>دائن</div>
              <div>الحالة</div>
              <div>إجراءات</div>
            </div>

            {bookTransactions.map(item => (
              <div key={item.id} className={`table-row ${item.matched ? 'matched' : 'unmatched'}`}>
                <div className="date">{item.date}</div>
                <div className="description">{item.description}</div>
                <div className="debit">{item.debit > 0 ? formatCurrency(item.debit) : '-'}</div>
                <div className="credit">{item.credit > 0 ? formatCurrency(item.credit) : '-'}</div>
                <div className="status">
                  {item.matched ? (
                    <span className="matched-badge">✅ مطابق</span>
                  ) : (
                    <span className="unmatched-badge">❌ غير مطابق</span>
                  )}
                </div>
                <div className="actions">
                  {!item.matched && (
                    <button className="match-btn" title="مطابقة">🔗</button>
                  )}
                  <button className="view-btn" title="عرض">👁️</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* العناصر غير المطابقة */}
      {(reconciliationData.unmatchedBankItems.length > 0 || reconciliationData.unmatchedBookItems.length > 0) && (
        <div className="unmatched-items">
          <h4>العناصر غير المطابقة</h4>
          
          {reconciliationData.unmatchedBankItems.length > 0 && (
            <div className="unmatched-section">
              <h5>عناصر كشف البنك غير المطابقة</h5>
              <div className="unmatched-list">
                {reconciliationData.unmatchedBankItems.map(item => (
                  <div key={item.id} className="unmatched-item bank-item">
                    <span className="date">{item.date}</span>
                    <span className="description">{item.description}</span>
                    <span className="amount">
                      {item.debit > 0 ? `-${formatCurrency(item.debit)}` : `+${formatCurrency(item.credit)}`}
                    </span>
                    <button className="action-btn small">إنشاء قيد</button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {reconciliationData.unmatchedBookItems.length > 0 && (
            <div className="unmatched-section">
              <h5>معاملات الدفاتر غير المطابقة</h5>
              <div className="unmatched-list">
                {reconciliationData.unmatchedBookItems.map(item => (
                  <div key={item.id} className="unmatched-item book-item">
                    <span className="date">{item.date}</span>
                    <span className="description">{item.description}</span>
                    <span className="amount">
                      {item.debit > 0 ? `+${formatCurrency(item.debit)}` : `-${formatCurrency(item.credit)}`}
                    </span>
                    <button className="action-btn small">تحقق من البنك</button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* نموذج رفع كشف البنك */}
      {showUploadModal && (
        <div className="upload-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>رفع كشف البنك</h4>
              <button 
                className="close-btn"
                onClick={() => setShowUploadModal(false)}
              >
                ❌
              </button>
            </div>
            
            <div className="modal-body">
              <div className="upload-section">
                <div className="upload-area">
                  <div className="upload-icon">📤</div>
                  <p>اسحب وأفلت ملف كشف البنك هنا أو اضغط للاختيار</p>
                  <input type="file" accept=".csv,.xlsx,.xls" />
                </div>
                
                <div className="upload-options">
                  <div className="form-group">
                    <label>تنسيق الملف</label>
                    <select>
                      <option value="csv">CSV</option>
                      <option value="excel">Excel</option>
                      <option value="pdf">PDF</option>
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label>البنك</label>
                    <select>
                      <option value="alahli">البنك الأهلي</option>
                      <option value="alrajhi">بنك الراجحي</option>
                      <option value="samba">بنك سامبا</option>
                      <option value="riyad">بنك الرياض</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="upload-btn">📤 رفع الملف</button>
              <button 
                className="cancel-btn"
                onClick={() => setShowUploadModal(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const TaxReports = ({ accounts, transactions, financialPeriod, isLoading }) => {
  const [activeReport, setActiveReport] = useState('vat');
  const [reportPeriod, setReportPeriod] = useState({
    type: 'quarterly',
    year: new Date().getFullYear(),
    quarter: Math.ceil((new Date().getMonth() + 1) / 3),
    month: new Date().getMonth() + 1
  });

  // بيانات تجريبية للمعاملات الضريبية
  const taxTransactions = [
    {
      id: 'TX-001',
      date: '2024-01-15',
      description: 'مبيعات تذاكر طيران',
      customerName: 'أحمد محمد العلي',
      invoiceNumber: 'INV-2024-001',
      baseAmount: 13043.48, // المبلغ قبل الضريبة
      vatAmount: 1956.52,   // ضريبة القيمة المضافة 15%
      totalAmount: 15000,   // المبلغ الإجمالي
      vatRate: 0.15,
      category: 'standard_rated',
      type: 'output_vat'
    },
    {
      id: 'TX-002',
      date: '2024-01-20',
      description: 'شراء خدمات فندقية',
      supplierName: 'فندق الريتز كارلتون',
      billNumber: 'BILL-2024-002',
      baseAmount: 24347.83,
      vatAmount: 3652.17,
      totalAmount: 28000,
      vatRate: 0.15,
      category: 'standard_rated',
      type: 'input_vat'
    },
    {
      id: 'TX-003',
      date: '2024-01-25',
      description: 'مبيعات خدمات عمرة',
      customerName: 'شركة السفر الذهبي',
      invoiceNumber: 'INV-2024-003',
      baseAmount: 21739.13,
      vatAmount: 3260.87,
      totalAmount: 25000,
      vatRate: 0.15,
      category: 'standard_rated',
      type: 'output_vat'
    },
    {
      id: 'TX-004',
      date: '2024-02-01',
      description: 'خدمات طبية - معفاة',
      customerName: 'مستشفى الملك فيصل',
      invoiceNumber: 'INV-2024-004',
      baseAmount: 5000,
      vatAmount: 0,
      totalAmount: 5000,
      vatRate: 0,
      category: 'exempt',
      type: 'exempt'
    },
    {
      id: 'TX-005',
      date: '2024-02-05',
      description: 'شراء معدات مكتبية',
      supplierName: 'مكتبة جرير',
      billNumber: 'BILL-2024-005',
      baseAmount: 2173.91,
      vatAmount: 326.09,
      totalAmount: 2500,
      vatRate: 0.15,
      category: 'standard_rated',
      type: 'input_vat'
    }
  ];

  const calculateVATSummary = () => {
    const summary = {
      outputVAT: 0,      // ضريبة المخرجات
      inputVAT: 0,       // ضريبة المدخلات
      netVAT: 0,         // صافي الضريبة
      standardRated: 0,   // المعاملات الخاضعة للضريبة
      zeroRated: 0,      // المعاملات بمعدل صفر
      exempt: 0,         // المعاملات المعفاة
      totalSales: 0,     // إجمالي المبيعات
      totalPurchases: 0  // إجمالي المشتريات
    };

    taxTransactions.forEach(transaction => {
      if (transaction.type === 'output_vat') {
        summary.outputVAT += transaction.vatAmount;
        summary.totalSales += transaction.totalAmount;
      } else if (transaction.type === 'input_vat') {
        summary.inputVAT += transaction.vatAmount;
        summary.totalPurchases += transaction.totalAmount;
      }

      if (transaction.category === 'standard_rated') {
        summary.standardRated += transaction.baseAmount;
      } else if (transaction.category === 'zero_rated') {
        summary.zeroRated += transaction.baseAmount;
      } else if (transaction.category === 'exempt') {
        summary.exempt += transaction.baseAmount;
      }
    });

    summary.netVAT = summary.outputVAT - summary.inputVAT;

    return summary;
  };

  const calculateZakat = () => {
    // حساب الزكاة على الأصول الزكوية
    const zakatableAssets = {
      cash: 150000,           // النقدية
      bankDeposits: 300000,   // الودائع البنكية
      receivables: 75000,     // الذمم المدينة
      inventory: 200000,      // المخزون
      total: 0
    };

    zakatableAssets.total = zakatableAssets.cash + 
                           zakatableAssets.bankDeposits + 
                           zakatableAssets.receivables + 
                           zakatableAssets.inventory;

    const nisab = 85000; // النصاب بالريال السعودي
    const zakatRate = 0.025; // 2.5%
    
    const zakatDue = zakatableAssets.total >= nisab ? 
                     zakatableAssets.total * zakatRate : 0;

    return {
      zakatableAssets,
      nisab,
      zakatDue,
      isLiable: zakatableAssets.total >= nisab
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatPercentage = (rate) => {
    return `${(rate * 100).toFixed(1)}%`;
  };

  const exportVATReturn = () => {
    const summary = calculateVATSummary();
    const vatReturn = {
      period: `${reportPeriod.year}-Q${reportPeriod.quarter}`,
      outputVAT: summary.outputVAT,
      inputVAT: summary.inputVAT,
      netVAT: summary.netVAT,
      standardRatedSales: summary.standardRated,
      zeroRatedSales: summary.zeroRated,
      exemptSales: summary.exempt
    };

    console.log('تصدير إقرار ضريبة القيمة المضافة:', vatReturn);
  };

  const exportZakatReturn = () => {
    const zakatData = calculateZakat();
    console.log('تصدير إقرار الزكاة:', zakatData);
  };

  const vatSummary = calculateVATSummary();
  const zakatData = calculateZakat();

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل التقارير الضريبية...</p>
      </div>
    );
  }

  return (
    <div className="tax-reports">
      <div className="tax-reports-header">
        <h3>التقارير الضريبية</h3>
        <div className="header-actions">
          <button className="action-btn primary" onClick={exportVATReturn}>
            📤 تصدير إقرار ضريبة القيمة المضافة
          </button>
          <button className="action-btn secondary" onClick={exportZakatReturn}>
            📤 تصدير إقرار الزكاة
          </button>
        </div>
      </div>

      {/* تبويبات التقارير */}
      <div className="tax-tabs">
        <button 
          className={`tab-btn ${activeReport === 'vat' ? 'active' : ''}`}
          onClick={() => setActiveReport('vat')}
        >
          ضريبة القيمة المضافة
        </button>
        <button 
          className={`tab-btn ${activeReport === 'zakat' ? 'active' : ''}`}
          onClick={() => setActiveReport('zakat')}
        >
          الزكاة
        </button>
        <button 
          className={`tab-btn ${activeReport === 'withholding' ? 'active' : ''}`}
          onClick={() => setActiveReport('withholding')}
        >
          ضريبة الاستقطاع
        </button>
        <button 
          className={`tab-btn ${activeReport === 'excise' ? 'active' : ''}`}
          onClick={() => setActiveReport('excise')}
        >
          ضريبة الانتقائية
        </button>
      </div>

      {/* إعدادات الفترة */}
      <div className="period-settings">
        <div className="form-group">
          <label>نوع الفترة</label>
          <select
            value={reportPeriod.type}
            onChange={(e) => setReportPeriod({...reportPeriod, type: e.target.value})}
          >
            <option value="monthly">شهرية</option>
            <option value="quarterly">ربع سنوية</option>
            <option value="annual">سنوية</option>
          </select>
        </div>

        <div className="form-group">
          <label>السنة</label>
          <select
            value={reportPeriod.year}
            onChange={(e) => setReportPeriod({...reportPeriod, year: parseInt(e.target.value)})}
          >
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
        </div>

        {reportPeriod.type === 'quarterly' && (
          <div className="form-group">
            <label>الربع</label>
            <select
              value={reportPeriod.quarter}
              onChange={(e) => setReportPeriod({...reportPeriod, quarter: parseInt(e.target.value)})}
            >
              <option value="1">الربع الأول</option>
              <option value="2">الربع الثاني</option>
              <option value="3">الربع الثالث</option>
              <option value="4">الربع الرابع</option>
            </select>
          </div>
        )}

        {reportPeriod.type === 'monthly' && (
          <div className="form-group">
            <label>الشهر</label>
            <select
              value={reportPeriod.month}
              onChange={(e) => setReportPeriod({...reportPeriod, month: parseInt(e.target.value)})}
            >
              <option value="1">يناير</option>
              <option value="2">فبراير</option>
              <option value="3">مارس</option>
              <option value="4">أبريل</option>
              <option value="5">مايو</option>
              <option value="6">يونيو</option>
              <option value="7">يوليو</option>
              <option value="8">أغسطس</option>
              <option value="9">سبتمبر</option>
              <option value="10">أكتوبر</option>
              <option value="11">نوفمبر</option>
              <option value="12">ديسمبر</option>
            </select>
          </div>
        )}
      </div>

      {/* محتوى التقارير */}
      <div className="tax-content">
        {activeReport === 'vat' && (
          <div className="vat-report">
            <h4>تقرير ضريبة القيمة المضافة</h4>
            
            {/* ملخص ضريبة القيمة المضافة */}
            <div className="vat-summary">
              <div className="summary-cards">
                <div className="summary-card output">
                  <div className="card-header">
                    <h5>ضريبة المخرجات</h5>
                    <span className="icon">📤</span>
                  </div>
                  <div className="card-value">{formatCurrency(vatSummary.outputVAT)}</div>
                  <div className="card-description">الضريبة على المبيعات</div>
                </div>

                <div className="summary-card input">
                  <div className="card-header">
                    <h5>ضريبة المدخلات</h5>
                    <span className="icon">📥</span>
                  </div>
                  <div className="card-value">{formatCurrency(vatSummary.inputVAT)}</div>
                  <div className="card-description">الضريبة على المشتريات</div>
                </div>

                <div className={`summary-card net ${vatSummary.netVAT >= 0 ? 'payable' : 'refundable'}`}>
                  <div className="card-header">
                    <h5>صافي الضريبة</h5>
                    <span className="icon">{vatSummary.netVAT >= 0 ? '💰' : '💸'}</span>
                  </div>
                  <div className="card-value">{formatCurrency(Math.abs(vatSummary.netVAT))}</div>
                  <div className="card-description">
                    {vatSummary.netVAT >= 0 ? 'مستحقة للدفع' : 'مستحقة الاسترداد'}
                  </div>
                </div>
              </div>
            </div>

            {/* تفاصيل المعاملات */}
            <div className="vat-details">
              <h5>تفاصيل المعاملات الضريبية</h5>
              
              <div className="vat-breakdown">
                <div className="breakdown-item">
                  <span className="label">المبيعات الخاضعة للضريبة (15%):</span>
                  <span className="value">{formatCurrency(vatSummary.standardRated)}</span>
                </div>
                <div className="breakdown-item">
                  <span className="label">المبيعات بمعدل صفر:</span>
                  <span className="value">{formatCurrency(vatSummary.zeroRated)}</span>
                </div>
                <div className="breakdown-item">
                  <span className="label">المبيعات المعفاة:</span>
                  <span className="value">{formatCurrency(vatSummary.exempt)}</span>
                </div>
                <div className="breakdown-item total">
                  <span className="label">إجمالي المبيعات:</span>
                  <span className="value">{formatCurrency(vatSummary.totalSales)}</span>
                </div>
              </div>
            </div>

            {/* جدول المعاملات */}
            <div className="vat-transactions">
              <h5>سجل المعاملات الضريبية</h5>
              
              <div className="transactions-table">
                <div className="table-header">
                  <div>التاريخ</div>
                  <div>الوصف</div>
                  <div>رقم الفاتورة</div>
                  <div>المبلغ الأساسي</div>
                  <div>معدل الضريبة</div>
                  <div>مبلغ الضريبة</div>
                  <div>المبلغ الإجمالي</div>
                  <div>النوع</div>
                </div>

                {taxTransactions.map(transaction => (
                  <div key={transaction.id} className={`table-row ${transaction.type}`}>
                    <div className="date">{transaction.date}</div>
                    <div className="description">{transaction.description}</div>
                    <div className="invoice-number">
                      {transaction.invoiceNumber || transaction.billNumber}
                    </div>
                    <div className="base-amount">{formatCurrency(transaction.baseAmount)}</div>
                    <div className="vat-rate">{formatPercentage(transaction.vatRate)}</div>
                    <div className="vat-amount">{formatCurrency(transaction.vatAmount)}</div>
                    <div className="total-amount">{formatCurrency(transaction.totalAmount)}</div>
                    <div className="type">
                      {transaction.type === 'output_vat' ? 'مخرجات' : 
                       transaction.type === 'input_vat' ? 'مدخلات' : 'معفاة'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeReport === 'zakat' && (
          <div className="zakat-report">
            <h4>تقرير الزكاة</h4>
            
            {/* ملخص الزكاة */}
            <div className="zakat-summary">
              <div className="summary-cards">
                <div className="summary-card assets">
                  <div className="card-header">
                    <h5>الأصول الزكوية</h5>
                    <span className="icon">💰</span>
                  </div>
                  <div className="card-value">{formatCurrency(zakatData.zakatableAssets.total)}</div>
                  <div className="card-description">إجمالي الأصول الخاضعة للزكاة</div>
                </div>

                <div className="summary-card nisab">
                  <div className="card-header">
                    <h5>النصاب</h5>
                    <span className="icon">📏</span>
                  </div>
                  <div className="card-value">{formatCurrency(zakatData.nisab)}</div>
                  <div className="card-description">الحد الأدنى لوجوب الزكاة</div>
                </div>

                <div className={`summary-card zakat ${zakatData.isLiable ? 'liable' : 'not-liable'}`}>
                  <div className="card-header">
                    <h5>الزكاة المستحقة</h5>
                    <span className="icon">{zakatData.isLiable ? '✅' : '❌'}</span>
                  </div>
                  <div className="card-value">{formatCurrency(zakatData.zakatDue)}</div>
                  <div className="card-description">
                    {zakatData.isLiable ? 'واجبة الدفع (2.5%)' : 'غير واجبة'}
                  </div>
                </div>
              </div>
            </div>

            {/* تفاصيل الأصول الزكوية */}
            <div className="zakat-assets">
              <h5>تفاصيل الأصول الزكوية</h5>
              
              <div className="assets-breakdown">
                <div className="breakdown-item">
                  <span className="label">النقدية:</span>
                  <span className="value">{formatCurrency(zakatData.zakatableAssets.cash)}</span>
                </div>
                <div className="breakdown-item">
                  <span className="label">الودائع البنكية:</span>
                  <span className="value">{formatCurrency(zakatData.zakatableAssets.bankDeposits)}</span>
                </div>
                <div className="breakdown-item">
                  <span className="label">الذمم المدينة:</span>
                  <span className="value">{formatCurrency(zakatData.zakatableAssets.receivables)}</span>
                </div>
                <div className="breakdown-item">
                  <span className="label">المخزون:</span>
                  <span className="value">{formatCurrency(zakatData.zakatableAssets.inventory)}</span>
                </div>
                <div className="breakdown-item total">
                  <span className="label">إجمالي الأصول الزكوية:</span>
                  <span className="value">{formatCurrency(zakatData.zakatableAssets.total)}</span>
                </div>
              </div>
            </div>

            {/* حساب الزكاة */}
            <div className="zakat-calculation">
              <h5>حساب الزكاة</h5>
              
              <div className="calculation-steps">
                <div className="step">
                  <span className="step-number">1</span>
                  <span className="step-description">إجمالي الأصول الزكوية</span>
                  <span className="step-value">{formatCurrency(zakatData.zakatableAssets.total)}</span>
                </div>
                
                <div className="step">
                  <span className="step-number">2</span>
                  <span className="step-description">النصاب المطلوب</span>
                  <span className="step-value">{formatCurrency(zakatData.nisab)}</span>
                </div>
                
                <div className="step">
                  <span className="step-number">3</span>
                  <span className="step-description">
                    {zakatData.isLiable ? 'تطبيق معدل الزكاة (2.5%)' : 'لا تطبق الزكاة'}
                  </span>
                  <span className="step-value">
                    {zakatData.isLiable ? formatCurrency(zakatData.zakatDue) : 'غير واجبة'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeReport === 'withholding' && (
          <div className="withholding-report">
            <h4>تقرير ضريبة الاستقطاع</h4>
            <div className="coming-soon">
              <div className="icon">🚧</div>
              <h5>قيد التطوير</h5>
              <p>سيتم إضافة تقارير ضريبة الاستقطاع قريباً</p>
            </div>
          </div>
        )}

        {activeReport === 'excise' && (
          <div className="excise-report">
            <h4>تقرير الضريبة الانتقائية</h4>
            <div className="coming-soon">
              <div className="icon">🚧</div>
              <h5>قيد التطوير</h5>
              <p>سيتم إضافة تقارير الضريبة الانتقائية قريباً</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// تصدير المكونات الجديدة
export { 
  GeneralLedger, 
  JournalBooks, 
  AccountsHierarchy, 
  AutomatedTransactions, 
  AuditTrail, 
  AccountingPeriods, 
  JournalBooksAdvanced,
  AutomatedTransactionsAdvanced,
  AuditTrailAdvanced,
  GeneralLedgerAdvanced,
  JournalEntriesAdvanced,
  TrialBalanceAdvanced,
  FinancialStatementsAdvanced,
  AccountsReceivableAdvanced,
  AccountsPayableAdvanced
};