import React from 'react';
import { useNotifications } from '../UI/EnhancedNotifications';
import { useShortcuts } from '../UI/KeyboardShortcuts';

// 🧪 مكون اختبار الإشعارات واختصارات لوحة المفاتيح
const TestNotifications = () => {
  const { success, error, warning, info, loading } = useNotifications();
  const { registerShortcut, unregisterShortcut } = useShortcuts();

  React.useEffect(() => {
    // تسجيل اختصار للاختبار
    registerShortcut('ctrl+t', 'test', 'اختبار الإشعارات', 'test');

    const handleShortcutAction = (event) => {
      if (event.detail.action === 'test') {
        success('تم تشغيل الاختصار بنجاح! 🎉');
      }
    };

    document.addEventListener('shortcutAction', handleShortcutAction);

    return () => {
      unregisterShortcut('ctrl+t');
      document.removeEventListener('shortcutAction', handleShortcutAction);
    };
  }, [registerShortcut, unregisterShortcut, success]);

  const handleTestSuccess = () => {
    success('هذا إشعار نجاح! ✅', {
      title: 'نجح الاختبار',
      duration: 3000
    });
  };

  const handleTestError = () => {
    error('هذا إشعار خطأ! ❌', {
      title: 'خطأ في الاختبار',
      duration: 5000
    });
  };

  const handleTestWarning = () => {
    warning('هذا إشعار تحذير! ⚠️', {
      title: 'تحذير',
      duration: 4000
    });
  };

  const handleTestInfo = () => {
    info('هذا إشعار معلومات! ℹ️', {
      title: 'معلومة',
      duration: 3000
    });
  };

  const handleTestLoading = () => {
    loading('جاري التحميل...', {
      title: 'انتظر قليلاً',
      duration: 2000
    });
  };

  const handleTestWithActions = () => {
    success('تم الحفظ بنجاح!', {
      title: 'عملية ناجحة',
      actions: [
        {
          label: 'عرض',
          onClick: () => alert('تم النقر على عرض!')
        },
        {
          label: 'تراجع',
          onClick: () => alert('تم النقر على تراجع!')
        }
      ]
    });
  };

  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '600px', 
      margin: '0 auto',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>🧪 اختبار نظام الإشعارات</h1>
      <p>اختبر جميع أنواع الإشعارات واختصارات لوحة المفاتيح</p>
      
      <div style={{ 
        display: 'grid', 
        gap: '1rem', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        marginBottom: '2rem'
      }}>
        <button 
          onClick={handleTestSuccess}
          style={{
            padding: '1rem',
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          ✅ اختبار النجاح
        </button>

        <button 
          onClick={handleTestError}
          style={{
            padding: '1rem',
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          ❌ اختبار الخطأ
        </button>

        <button 
          onClick={handleTestWarning}
          style={{
            padding: '1rem',
            backgroundColor: '#f59e0b',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          ⚠️ اختبار التحذير
        </button>

        <button 
          onClick={handleTestInfo}
          style={{
            padding: '1rem',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          ℹ️ اختبار المعلومات
        </button>

        <button 
          onClick={handleTestLoading}
          style={{
            padding: '1rem',
            backgroundColor: '#6b7280',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          🔄 اختبار التحميل
        </button>

        <button 
          onClick={handleTestWithActions}
          style={{
            padding: '1rem',
            backgroundColor: '#8b5cf6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          🎯 اختبار مع إجراءات
        </button>
      </div>

      <div style={{
        padding: '1rem',
        backgroundColor: '#f3f4f6',
        borderRadius: '8px',
        marginTop: '2rem'
      }}>
        <h3>⌨️ اختصارات لوحة المفاتيح:</h3>
        <ul>
          <li><code>Ctrl + T</code> - اختبار الإشعارات</li>
          <li><code>Ctrl + N</code> - إضافة حجز جديد (في صفحات الحجوزات)</li>
          <li><code>Ctrl + F</code> - البحث السريع</li>
        </ul>
        <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
          جرب الضغط على Ctrl + T لاختبار الاختصارات!
        </p>
      </div>
    </div>
  );
};

export default TestNotifications;
