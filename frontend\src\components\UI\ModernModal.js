import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import ModernButton, { IconButton } from './ModernButton';
import './ModernModal.css';

// Modern Modal Component
export const ModernModal = ({
  children,
  isOpen = false,
  onClose,
  title,
  subtitle,
  size = 'md',
  variant = 'default',
  closable = true,
  backdrop = true,
  centered = true,
  fullscreen = false,
  className = '',
  overlayClassName = '',
  ...props
}) => {
  const modalRef = useRef(null);
  const previousFocusRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      previousFocusRef.current = document.activeElement;
      document.body.style.overflow = 'hidden';
      
      // Focus the modal
      setTimeout(() => {
        if (modalRef.current) {
          modalRef.current.focus();
        }
      }, 100);
    } else {
      document.body.style.overflow = '';
      
      // Restore focus
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && closable) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, closable, onClose]);

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && backdrop && closable) {
      onClose?.();
    }
  };

  const modalClasses = [
    'modern-modal',
    `modern-modal--${size}`,
    `modern-modal--${variant}`,
    centered && 'modern-modal--centered',
    fullscreen && 'modern-modal--fullscreen',
    className
  ].filter(Boolean).join(' ');

  const overlayClasses = [
    'modern-modal-overlay',
    isOpen && 'modern-modal-overlay--open',
    overlayClassName
  ].filter(Boolean).join(' ');

  if (!isOpen) return null;

  const modalContent = (
    <div className={overlayClasses} onClick={handleBackdropClick}>
      <div
        ref={modalRef}
        className={modalClasses}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}
        tabIndex={-1}
        {...props}
      >
        {(title || subtitle || closable) && (
          <div className="modern-modal-header">
            <div className="modern-modal-title-section">
              {title && (
                <h2 id="modal-title" className="modern-modal-title">
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="modern-modal-subtitle">{subtitle}</p>
              )}
            </div>
            
            {closable && (
              <IconButton
                icon="✕"
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="modern-modal-close"
                aria-label="إغلاق"
              />
            )}
          </div>
        )}
        
        <div className="modern-modal-body">
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

// Modal with Footer
export const ModalWithFooter = ({
  children,
  footer,
  primaryAction,
  secondaryAction,
  ...modalProps
}) => {
  return (
    <ModernModal {...modalProps}>
      <div className="modern-modal-content">
        <div className="modern-modal-main">
          {children}
        </div>
        
        {(footer || primaryAction || secondaryAction) && (
          <div className="modern-modal-footer">
            {footer || (
              <div className="modern-modal-actions">
                {secondaryAction && (
                  <ModernButton
                    variant="outline"
                    onClick={secondaryAction.onClick}
                    disabled={secondaryAction.disabled}
                  >
                    {secondaryAction.label || 'إلغاء'}
                  </ModernButton>
                )}
                {primaryAction && (
                  <ModernButton
                    variant={primaryAction.variant || 'primary'}
                    onClick={primaryAction.onClick}
                    disabled={primaryAction.disabled}
                    loading={primaryAction.loading}
                  >
                    {primaryAction.label || 'موافق'}
                  </ModernButton>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </ModernModal>
  );
};

// Confirmation Modal
export const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'تأكيد العملية',
  message = 'هل أنت متأكد من أنك تريد المتابعة؟',
  confirmText = 'تأكيد',
  cancelText = 'إلغاء',
  variant = 'warning',
  loading = false,
  ...props
}) => {
  const getIcon = () => {
    const icons = {
      warning: '⚠️',
      error: '❌',
      info: 'ℹ️',
      success: '✅'
    };
    return icons[variant] || icons.warning;
  };

  return (
    <ModalWithFooter
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      variant={variant}
      primaryAction={{
        label: confirmText,
        onClick: onConfirm,
        variant: variant === 'error' ? 'error' : 'primary',
        loading
      }}
      secondaryAction={{
        label: cancelText,
        onClick: onClose
      }}
      {...props}
    >
      <div className="confirmation-modal-content">
        <div className="confirmation-modal-icon">
          {getIcon()}
        </div>
        <div className="confirmation-modal-message">
          {message}
        </div>
      </div>
    </ModalWithFooter>
  );
};

// Form Modal
export const FormModal = ({
  children,
  isOpen,
  onClose,
  onSubmit,
  title,
  submitText = 'حفظ',
  cancelText = 'إلغاء',
  loading = false,
  disabled = false,
  ...props
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit?.(e);
  };

  return (
    <ModernModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      {...props}
    >
      <form onSubmit={handleSubmit} className="form-modal-content">
        <div className="form-modal-body">
          {children}
        </div>
        
        <div className="form-modal-footer">
          <div className="form-modal-actions">
            <ModernButton
              type="button"
              variant="outline"
              onClick={onClose}
            >
              {cancelText}
            </ModernButton>
            <ModernButton
              type="submit"
              variant="primary"
              loading={loading}
              disabled={disabled}
            >
              {submitText}
            </ModernButton>
          </div>
        </div>
      </form>
    </ModernModal>
  );
};

// Image Modal
export const ImageModal = ({
  isOpen,
  onClose,
  src,
  alt,
  title,
  description,
  ...props
}) => {
  return (
    <ModernModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="lg"
      variant="minimal"
      {...props}
    >
      <div className="image-modal-content">
        <img
          src={src}
          alt={alt}
          className="image-modal-image"
        />
        {description && (
          <div className="image-modal-description">
            {description}
          </div>
        )}
      </div>
    </ModernModal>
  );
};

// Drawer Component (Side Modal)
export const Drawer = ({
  children,
  isOpen = false,
  onClose,
  title,
  position = 'right',
  size = 'md',
  overlay = true,
  className = '',
  ...props
}) => {
  const drawerRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && overlay) {
      onClose?.();
    }
  };

  const drawerClasses = [
    'modern-drawer',
    `modern-drawer--${position}`,
    `modern-drawer--${size}`,
    isOpen && 'modern-drawer--open',
    className
  ].filter(Boolean).join(' ');

  if (!isOpen) return null;

  const drawerContent = (
    <div className="modern-drawer-overlay" onClick={handleOverlayClick}>
      <div
        ref={drawerRef}
        className={drawerClasses}
        role="dialog"
        aria-modal="true"
        {...props}
      >
        {title && (
          <div className="modern-drawer-header">
            <h3 className="modern-drawer-title">{title}</h3>
            <IconButton
              icon="✕"
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="modern-drawer-close"
            />
          </div>
        )}
        
        <div className="modern-drawer-body">
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(drawerContent, document.body);
};

// Bottom Sheet Component
export const BottomSheet = ({
  children,
  isOpen = false,
  onClose,
  title,
  height = 'auto',
  className = '',
  ...props
}) => {
  const sheetRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose?.();
    }
  };

  const sheetClasses = [
    'modern-bottom-sheet',
    isOpen && 'modern-bottom-sheet--open',
    className
  ].filter(Boolean).join(' ');

  if (!isOpen) return null;

  const sheetContent = (
    <div className="modern-bottom-sheet-overlay" onClick={handleOverlayClick}>
      <div
        ref={sheetRef}
        className={sheetClasses}
        style={{ height }}
        role="dialog"
        aria-modal="true"
        {...props}
      >
        <div className="modern-bottom-sheet-handle" />
        
        {title && (
          <div className="modern-bottom-sheet-header">
            <h3 className="modern-bottom-sheet-title">{title}</h3>
            <IconButton
              icon="✕"
              variant="ghost"
              size="sm"
              onClick={onClose}
            />
          </div>
        )}
        
        <div className="modern-bottom-sheet-body">
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(sheetContent, document.body);
};

export default ModernModal;