// خدمة إدارة الوكلاء المشتركة
class AgentsService {
  constructor() {
    this.agents = [];
    this.listeners = [];
    this.loadAgents();
  }

  // تحميل الوكلاء من localStorage أو البيانات التجريبية
  loadAgents() {
    const savedAgents = localStorage.getItem('agents');
    if (savedAgents) {
      this.agents = JSON.parse(savedAgents);
    } else {
      // البيانات التجريبية الافتراضية
      this.agents = [
        {
          id: 1,
          agentName: 'أحمد محمد السالم',
          agentCode: 'AGT001',
          phone: '+966501234567',
          email: '<EMAIL>',
          nationalId: '1234567890',
          address: 'الرياض، حي النخيل، شارع الملك فهد',
          office: 'مكتب الرياض الرئيسي',
          specialty: 'تأشيرات الإمارات',
          joinDate: '2023-01-15',
          totalTransactions: 245,
          currentMonthTransactions: 28,
          rating: 4.8,
          status: 'active',
          lastActivity: '2024-01-20',
          notes: 'وكيل متميز في تأشيرات الإمارات',
          emergencyContact: 'فاطمة السالم',
          emergencyPhone: '+966507654321',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة الخبرة']
        },
        {
          id: 2,
          agentName: 'فاطمة أحمد الزهراني',
          agentCode: 'AGT002',
          phone: '+966502345678',
          email: '<EMAIL>',
          nationalId: '2345678901',
          address: 'جدة، حي الروضة، شارع التحلية',
          office: 'مكتب جدة',
          specialty: 'تأشيرات أوروبا وتركيا',
          joinDate: '2023-03-10',
          totalTransactions: 189,
          currentMonthTransactions: 22,
          rating: 4.6,
          status: 'active',
          lastActivity: '2024-01-19',
          notes: 'متخصصة في تأشيرات تركيا والدول الأوروبية',
          emergencyContact: 'محمد الزهراني',
          emergencyPhone: '+966508765432',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة التدريب']
        },
        {
          id: 3,
          agentName: 'محمد علي حسن',
          agentCode: 'AGT003',
          phone: '+966503456789',
          email: '<EMAIL>',
          nationalId: '3456789012',
          address: 'الدمام، حي الفيصلية، شارع الأمير محمد',
          office: 'مكتب الدمام',
          specialty: 'تأشيرات الدول العربية',
          joinDate: '2022-11-20',
          totalTransactions: 156,
          currentMonthTransactions: 18,
          rating: 4.4,
          status: 'active',
          lastActivity: '2024-01-18',
          notes: 'خبرة واسعة في تأشيرات الدول العربية',
          emergencyContact: 'سارة حسن',
          emergencyPhone: '+966509876543',
          documents: ['الهوية الوطنية', 'عقد العمل']
        },
        {
          id: 4,
          agentName: 'سارة محمد الأحمد',
          agentCode: 'AGT004',
          phone: '+966504567890',
          email: '<EMAIL>',
          nationalId: '4567890123',
          address: 'مكة المكرمة، حي العزيزية، شارع إبراهيم الخليل',
          office: 'مكتب مكة المكرمة',
          specialty: 'تأشيرات آسيا',
          joinDate: '2023-06-01',
          totalTransactions: 98,
          currentMonthTransactions: 15,
          rating: 4.7,
          status: 'active',
          lastActivity: '2024-01-20',
          notes: 'وكيلة جديدة ومتحمسة',
          emergencyContact: 'خالد الأحمد',
          emergencyPhone: '+966500987654',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة التأهيل']
        },
        {
          id: 5,
          agentName: 'خالد عبدالله المطيري',
          agentCode: 'AGT005',
          phone: '+966505678901',
          email: '<EMAIL>',
          nationalId: '5678901234',
          address: 'المدينة المنورة، حي قباء، شارع قباء',
          office: 'مكتب المدينة المنورة',
          specialty: 'تأشيرات أمريكا',
          joinDate: '2022-08-15',
          totalTransactions: 67,
          currentMonthTransactions: 8,
          rating: 4.2,
          status: 'inactive',
          lastActivity: '2024-01-10',
          notes: 'في إجازة مؤقتة',
          emergencyContact: 'نورا المطيري',
          emergencyPhone: '+966501098765',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة اللغة الإنجليزية']
        },
        {
          id: 6,
          agentName: 'نورا عبدالرحمن القحطاني',
          agentCode: 'AGT006',
          phone: '+966506789012',
          email: '<EMAIL>',
          nationalId: '6789012345',
          address: 'أبها، حي الراقي، شارع الملك عبدالعزيز',
          office: 'مكتب أبها',
          specialty: 'تأشيرات الهند وباكستان',
          joinDate: '2023-09-01',
          totalTransactions: 45,
          currentMonthTransactions: 12,
          rating: 4.5,
          status: 'active',
          lastActivity: '2024-01-21',
          notes: 'متخصصة في تأشيرات شبه القارة الهندية',
          emergencyContact: 'عبدالرحمن القحطاني',
          emergencyPhone: '+966502109876',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة اللغة الإنجليزية']
        }
      ];
      this.saveAgents();
    }
  }

  // حفظ الوكلاء في localStorage
  saveAgents() {
    localStorage.setItem('agents', JSON.stringify(this.agents));
    this.notifyListeners();
  }

  // الحصول على جميع الوكلاء
  getAllAgents() {
    return [...this.agents];
  }

  // الحصول على الوكلاء النشطين فقط
  getActiveAgents() {
    return this.agents.filter(agent => agent.status === 'active');
  }

  // الحصول على وكيل بالمعرف
  getAgentById(id) {
    return this.agents.find(agent => agent.id === id);
  }

  // الحصول على وكيل بالاسم
  getAgentByName(name) {
    return this.agents.find(agent => agent.agentName === name);
  }

  // إضافة وكيل جديد
  addAgent(agentData) {
    const newAgent = {
      id: Math.max(...this.agents.map(a => a.id), 0) + 1,
      ...agentData,
      joinDate: new Date().toISOString().split('T')[0],
      totalTransactions: 0,
      currentMonthTransactions: 0,
      rating: 5.0,
      lastActivity: new Date().toISOString().split('T')[0]
    };
    
    this.agents.push(newAgent);
    this.saveAgents();
    return newAgent;
  }

  // تحديث وكيل
  updateAgent(id, agentData) {
    const index = this.agents.findIndex(agent => agent.id === id);
    if (index !== -1) {
      this.agents[index] = { ...this.agents[index], ...agentData };
      this.saveAgents();
      return this.agents[index];
    }
    return null;
  }

  // حذف وكيل
  deleteAgent(id) {
    const index = this.agents.findIndex(agent => agent.id === id);
    if (index !== -1) {
      const deletedAgent = this.agents.splice(index, 1)[0];
      this.saveAgents();
      return deletedAgent;
    }
    return null;
  }

  // البحث في الوكلاء
  searchAgents(searchTerm) {
    const term = searchTerm.toLowerCase();
    return this.agents.filter(agent => 
      agent.agentName.toLowerCase().includes(term) ||
      agent.agentCode.toLowerCase().includes(term) ||
      agent.email.toLowerCase().includes(term) ||
      agent.phone.includes(term) ||
      (agent.specialty && agent.specialty.toLowerCase().includes(term))
    );
  }

  // إضافة مستمع للتغييرات
  addListener(callback) {
    this.listeners.push(callback);
  }

  // إزالة مستمع
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // إشعار المستمعين بالتغييرات
  notifyListeners() {
    this.listeners.forEach(callback => callback(this.agents));
  }

  // الحصول على إحصائيات الوكلاء
  getAgentsStats() {
    const activeAgents = this.agents.filter(a => a.status === 'active').length;
    const topPerformers = this.agents.filter(a => a.rating >= 4.5).length;
    const newThisMonth = this.agents.filter(a => {
      const joinDate = new Date(a.joinDate);
      const now = new Date();
      return joinDate.getMonth() === now.getMonth() && joinDate.getFullYear() === now.getFullYear();
    }).length;
    const totalTransactions = this.agents.reduce((sum, a) => sum + a.totalTransactions, 0);

    return {
      totalAgents: this.agents.length,
      activeAgents: activeAgents,
      topPerformers: topPerformers,
      newThisMonth: newThisMonth,
      totalTransactions: totalTransactions
    };
  }
}

// إنشاء مثيل واحد من الخدمة
const agentsService = new AgentsService();

export default agentsService;
