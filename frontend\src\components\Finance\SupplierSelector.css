/* منتقي المورد */
.supplier-selector {
  position: relative;
  width: 100%;
  direction: rtl;
  font-family: 'Cairo', Arial, sans-serif;
}

.selector-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.selector-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: white;
  color: #2c3e50;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.selector-input:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.selector-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  border-color: #dee2e6;
}

.clear-button {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: #c0392b;
  transform: translateY(-50%) scale(1.1);
}

/* قائمة الاقتراحات */
.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e74c3c;
  border-top: none;
  border-radius: 0 0 12px 12px;
  max-height: 350px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* عنصر الاقتراح */
.suggestion-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ecf0f1;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 12px;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background: linear-gradient(135deg, #f8f9fa 0%, #fdf2f2 100%);
  border-left: 4px solid #e74c3c;
}

.suggestion-item:last-child {
  border-bottom: none;
  border-radius: 0 0 10px 10px;
}

.suggestion-icon {
  font-size: 1.8rem;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.suggestion-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 2px;
}

.suggestion-details {
  font-size: 0.85rem;
  color: #7f8c8d;
  font-weight: 500;
}

.suggestion-contact {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #95a5a6;
  margin-top: 2px;
}

.suggestion-contact span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.suggestion-services {
  font-size: 0.8rem;
  color: #3498db;
  font-weight: 500;
  margin-top: 2px;
}

.suggestion-debt {
  font-size: 0.8rem;
  color: #e74c3c;
  font-weight: 600;
  background: rgba(231, 76, 60, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  margin-top: 4px;
  display: inline-block;
  width: fit-content;
}

.suggestion-rating {
  font-size: 0.8rem;
  color: #f39c12;
  font-weight: 500;
  margin-top: 2px;
}

.suggestion-type {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  min-width: 50px;
  flex-shrink: 0;
}

/* رسالة عدم وجود نتائج */
.no-suggestions {
  padding: 30px 20px;
  text-align: center;
  color: #7f8c8d;
}

.no-suggestions-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.no-suggestions-text {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
}

.no-suggestions-hint {
  font-size: 0.9rem;
  color: #95a5a6;
  line-height: 1.4;
}

/* تحسينات للوضع المظلم */
.dark-mode .selector-input {
  background: #34495e;
  color: #ecf0f1;
  border-color: #4a5568;
}

.dark-mode .selector-input:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}

.dark-mode .suggestions-container {
  background: #2c3e50;
  border-color: #e74c3c;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.dark-mode .suggestion-item {
  border-bottom-color: #4a5568;
}

.dark-mode .suggestion-item:hover,
.dark-mode .suggestion-item.selected {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

.dark-mode .suggestion-name {
  color: #ecf0f1;
}

.dark-mode .suggestion-details {
  color: #bdc3c7;
}

.dark-mode .suggestion-contact {
  color: #95a5a6;
}

.dark-mode .no-suggestions {
  color: #bdc3c7;
}

.dark-mode .no-suggestions-text {
  color: #ecf0f1;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .suggestion-item {
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .suggestion-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }
  
  .suggestion-contact {
    flex-direction: column;
    gap: 8px;
  }
  
  .suggestion-type {
    align-self: flex-end;
    margin-top: 8px;
  }
  
  .suggestions-container {
    max-height: 280px;
  }
}

@media (max-width: 480px) {
  .selector-input {
    padding: 10px 35px 10px 12px;
    font-size: 13px;
  }
  
  .clear-button {
    width: 20px;
    height: 20px;
    font-size: 10px;
    left: 8px;
  }
  
  .suggestion-item {
    padding: 10px;
  }
  
  .suggestion-name {
    font-size: 1rem;
  }
  
  .suggestion-details,
  .suggestion-contact {
    font-size: 0.75rem;
  }
}

/* تأثيرات إضافية */
.suggestion-item:hover .suggestion-icon {
  transform: scale(1.1);
  background: rgba(231, 76, 60, 0.2);
}

.suggestion-item.selected .suggestion-icon {
  transform: scale(1.1);
  background: rgba(231, 76, 60, 0.3);
}

/* شريط التمرير المخصص */
.suggestions-container::-webkit-scrollbar {
  width: 6px;
}

.suggestions-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-container::-webkit-scrollbar-thumb {
  background: #e74c3c;
  border-radius: 3px;
}

.suggestions-container::-webkit-scrollbar-thumb:hover {
  background: #c0392b;
}

/* تحسينات الأداء */
.suggestions-container {
  will-change: transform, opacity;
}

.suggestion-item {
  will-change: background-color, transform;
}

/* ألوان مخصصة لأنواع الموردين */
.suggestion-item[data-supplier-type="airline"] .suggestion-icon {
  background: rgba(52, 152, 219, 0.1);
}

.suggestion-item[data-supplier-type="hotel"] .suggestion-icon {
  background: rgba(231, 76, 60, 0.1);
}

.suggestion-item[data-supplier-type="transport"] .suggestion-icon {
  background: rgba(243, 156, 18, 0.1);
}

.suggestion-item[data-supplier-type="visa"] .suggestion-icon {
  background: rgba(155, 89, 182, 0.1);
}

.suggestion-item[data-supplier-type="insurance"] .suggestion-icon {
  background: rgba(39, 174, 96, 0.1);
}

.suggestion-item[data-supplier-type="restaurant"] .suggestion-icon {
  background: rgba(230, 126, 34, 0.1);
}
