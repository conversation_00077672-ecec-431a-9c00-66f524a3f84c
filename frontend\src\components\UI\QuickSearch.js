import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import './QuickSearch.css';

// 🔍 سياق البحث السريع
const QuickSearchContext = createContext();

// 🎯 بيانات البحث الافتراضية
const DEFAULT_SEARCH_DATA = [
  // 📊 لوحة التحكم
  { id: 'dashboard', title: 'لوحة التحكم', description: 'نظرة عامة على النظام', path: '/dashboard', category: 'navigation', icon: '🏠' },
  
  // 👥 العملاء
  { id: 'customers', title: 'العملاء', description: 'إدارة بيانات العملاء', path: '/customers', category: 'customers', icon: '👥' },
  { id: 'add-customer', title: 'إضافة عميل جديد', description: 'تسجيل عميل جديد', path: '/customers/add', category: 'customers', icon: '➕' },
  
  // 📋 الحجوزات
  { id: 'bookings', title: 'الحجوزات', description: 'إدارة حجوزات السفر', path: '/bookings', category: 'bookings', icon: '📋' },
  { id: 'new-booking', title: 'حجز جديد', description: 'إنشاء حجز سفر جديد', path: '/bookings/new', category: 'bookings', icon: '✈️' },
  
  // 💰 المبيعات
  { id: 'sales', title: 'المبيعات', description: 'إدارة المبيعات والفواتير', path: '/sales', category: 'sales', icon: '💰' },
  { id: 'invoices', title: 'الفواتير', description: 'إدارة الفواتير', path: '/sales/invoices', category: 'sales', icon: '📄' },
  { id: 'payments', title: 'المدفوعات', description: 'إدارة المدفوعات', path: '/sales/payments', category: 'sales', icon: '💳' },
  
  // 📊 التقارير
  { id: 'reports', title: 'التقارير', description: 'تقارير وإحصائيات', path: '/reports', category: 'reports', icon: '📊' },
  { id: 'financial-reports', title: 'التقارير المالية', description: 'تقارير الأرباح والخسائر', path: '/reports/financial', category: 'reports', icon: '💹' },
  
  // ⚙️ الإعدادات
  { id: 'settings', title: 'الإعدادات', description: 'إعدادات النظام', path: '/settings', category: 'settings', icon: '⚙️' },
  { id: 'users', title: 'المستخدمين', description: 'إدارة المستخدمين', path: '/users', category: 'settings', icon: '👤' },
];

// 🏷️ فئات البحث
const SEARCH_CATEGORIES = {
  navigation: { name: 'التنقل', icon: '🧭', color: '#3b82f6' },
  customers: { name: 'العملاء', icon: '👥', color: '#10b981' },
  bookings: { name: 'الحجوزات', icon: '📋', color: '#8b5cf6' },
  sales: { name: 'المبيعات', icon: '💰', color: '#f59e0b' },
  reports: { name: 'التقارير', icon: '📊', color: '#ef4444' },
  settings: { name: 'الإعدادات', icon: '⚙️', color: '#6b7280' }
};

// 🔍 مزود البحث السريع
export const QuickSearchProvider = ({ children, customData = [] }) => {
  const [searchData, setSearchData] = useState([...DEFAULT_SEARCH_DATA, ...customData]);
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentSearches, setRecentSearches] = useState([]);

  // 📖 تحميل البحثات الأخيرة من localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // 💾 حفظ البحثات الأخيرة
  const saveRecentSearch = (item) => {
    const updated = [item, ...recentSearches.filter(r => r.id !== item.id)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  // 🔍 تنفيذ البحث
  useEffect(() => {
    if (query.trim() === '') {
      setResults(recentSearches.length > 0 ? recentSearches : searchData.slice(0, 8));
    } else {
      const filtered = searchData.filter(item => {
        const searchText = `${item.title} ${item.description} ${item.category}`.toLowerCase();
        const searchQuery = query.toLowerCase();
        return searchText.includes(searchQuery);
      });
      
      // 🎯 ترتيب النتائج حسب الصلة
      const sorted = filtered.sort((a, b) => {
        const aTitle = a.title.toLowerCase();
        const bTitle = b.title.toLowerCase();
        const queryLower = query.toLowerCase();
        
        // أولوية للعناوين التي تبدأ بالبحث
        if (aTitle.startsWith(queryLower) && !bTitle.startsWith(queryLower)) return -1;
        if (!aTitle.startsWith(queryLower) && bTitle.startsWith(queryLower)) return 1;
        
        // ثم العناوين التي تحتوي على البحث
        if (aTitle.includes(queryLower) && !bTitle.includes(queryLower)) return -1;
        if (!aTitle.includes(queryLower) && bTitle.includes(queryLower)) return 1;
        
        return 0;
      });
      
      setResults(sorted.slice(0, 10));
    }
    setSelectedIndex(0);
  }, [query, searchData, recentSearches]);

  // 🎯 إضافة عنصر بحث جديد
  const addSearchItem = (item) => {
    setSearchData(prev => [...prev, { ...item, id: item.id || Date.now().toString() }]);
  };

  // ❌ إزالة عنصر بحث
  const removeSearchItem = (id) => {
    setSearchData(prev => prev.filter(item => item.id !== id));
  };

  // 🔄 تحديث بيانات البحث
  const updateSearchData = (newData) => {
    setSearchData(newData);
  };

  const value = {
    searchData,
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results,
    selectedIndex,
    setSelectedIndex,
    recentSearches,
    saveRecentSearch,
    addSearchItem,
    removeSearchItem,
    updateSearchData
  };

  return (
    <QuickSearchContext.Provider value={value}>
      {children}
      {isOpen && <QuickSearchModal />}
    </QuickSearchContext.Provider>
  );
};

// 🎣 هوك استخدام البحث السريع
export const useQuickSearch = () => {
  const context = useContext(QuickSearchContext);
  if (!context) {
    throw new Error('useQuickSearch must be used within QuickSearchProvider');
  }
  return context;
};

// 🔍 مودال البحث السريع
const QuickSearchModal = () => {
  const {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results,
    selectedIndex,
    setSelectedIndex,
    saveRecentSearch
  } = useQuickSearch();
  
  const navigate = useNavigate();
  const inputRef = useRef(null);

  // 🎯 التركيز على حقل البحث عند الفتح
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // ⌨️ معالجة الضغط على المفاتيح
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev => (prev + 1) % results.length);
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev => (prev - 1 + results.length) % results.length);
          break;
        case 'Enter':
          event.preventDefault();
          if (results[selectedIndex]) {
            handleSelect(results[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, setIsOpen, setSelectedIndex]);

  // 🎯 معالجة اختيار عنصر
  const handleSelect = (item) => {
    saveRecentSearch(item);
    setIsOpen(false);
    setQuery('');
    
    if (item.path) {
      navigate(item.path);
    } else if (item.action) {
      item.action();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="quick-search-overlay" onClick={() => setIsOpen(false)}>
      <div className="quick-search-modal" onClick={(e) => e.stopPropagation()}>
        {/* 🔍 حقل البحث */}
        <div className="quick-search-input-container">
          <div className="quick-search-icon">🔍</div>
          <input
            ref={inputRef}
            type="text"
            className="quick-search-input"
            placeholder="ابحث في النظام... (اكتب للبحث)"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <div className="quick-search-shortcut">
            <kbd>Esc</kbd>
          </div>
        </div>

        {/* 📋 النتائج */}
        <div className="quick-search-results">
          {results.length === 0 ? (
            <div className="quick-search-empty">
              <div className="empty-icon">🔍</div>
              <div className="empty-message">
                {query ? 'لا توجد نتائج' : 'ابدأ بالكتابة للبحث'}
              </div>
            </div>
          ) : (
            <>
              {query === '' && recentSearches.length > 0 && (
                <div className="quick-search-section">
                  <div className="quick-search-section-title">
                    🕒 البحثات الأخيرة
                  </div>
                </div>
              )}
              
              {results.map((item, index) => (
                <div
                  key={item.id}
                  className={`quick-search-item ${index === selectedIndex ? 'selected' : ''}`}
                  onClick={() => handleSelect(item)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <div className="quick-search-item-icon">
                    {item.icon}
                  </div>
                  <div className="quick-search-item-content">
                    <div className="quick-search-item-title">
                      {highlightMatch(item.title, query)}
                    </div>
                    <div className="quick-search-item-description">
                      {item.description}
                    </div>
                    {item.category && SEARCH_CATEGORIES[item.category] && (
                      <div className="quick-search-item-category">
                        <span className="category-icon">
                          {SEARCH_CATEGORIES[item.category].icon}
                        </span>
                        <span className="category-name">
                          {SEARCH_CATEGORIES[item.category].name}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="quick-search-item-action">
                    <kbd>↵</kbd>
                  </div>
                </div>
              ))}
            </>
          )}
        </div>

        {/* 💡 نصائح */}
        <div className="quick-search-footer">
          <div className="quick-search-tips">
            <span className="tip">
              <kbd>↑</kbd><kbd>↓</kbd> للتنقل
            </span>
            <span className="tip">
              <kbd>↵</kbd> للاختيار
            </span>
            <span className="tip">
              <kbd>Esc</kbd> للإغلاق
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

// 🎨 تمييز النص المطابق
const highlightMatch = (text, query) => {
  if (!query) return text;
  
  const regex = new RegExp(`(${query})`, 'gi');
  const parts = text.split(regex);
  
  return parts.map((part, index) => 
    regex.test(part) ? (
      <mark key={index} className="search-highlight">{part}</mark>
    ) : part
  );
};

// 🔘 زر فتح البحث السريع
export const QuickSearchButton = ({ className = '', ...props }) => {
  const { setIsOpen } = useQuickSearch();

  return (
    <button
      className={`quick-search-button ${className}`}
      onClick={() => setIsOpen(true)}
      title="البحث السريع (Ctrl+K)"
      {...props}
    >
      <span className="quick-search-button-icon">🔍</span>
      <span className="quick-search-button-text">بحث سريع</span>
      <span className="quick-search-button-shortcut">
        <kbd>Ctrl</kbd> + <kbd>K</kbd>
      </span>
    </button>
  );
};

// 📊 مكون إحصائيات البحث
export const SearchStats = () => {
  const { searchData, recentSearches } = useQuickSearch();

  return (
    <div className="search-stats">
      <div className="search-stats-item">
        <span className="search-stats-label">العناصر المتاحة:</span>
        <span className="search-stats-value">{searchData.length}</span>
      </div>
      <div className="search-stats-item">
        <span className="search-stats-label">البحثات الأخيرة:</span>
        <span className="search-stats-value">{recentSearches.length}</span>
      </div>
    </div>
  );
};

export default QuickSearchProvider;
