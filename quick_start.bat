@echo off
title نظام محاسبي لوكالات السفريات - بدء سريع
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                                                              ║
echo  ║           نظام محاسبي متكامل لوكالات السفريات                ║
echo  ║        Travel Agency Accounting System - Quick Start        ║
echo  ║                                                              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo    يرجى تحميل وتثبيت Python من: https://python.org
    pause
    exit /b 1
)

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo    يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python و Node.js
echo.

:: إعداد Backend
echo 🔧 إعداد Backend...
cd backend

if not exist "venv" (
    echo    إنشاء البيئة الافتراضية...
    python -m venv venv
)

call venv\Scripts\activate.bat
echo    تثبيت المتطلبات...
pip install -q -r requirements.txt

cd ..

:: إعداد Frontend
echo 🔧 إعداد Frontend...
cd frontend

if not exist "node_modules" (
    echo    تثبيت حزم Node.js...
    npm install --silent
)

cd ..

:: إنشاء ملف .env
if not exist "backend\.env" (
    echo 📝 إنشاء ملف الإعدادات...
    copy "backend\.env.example" "backend\.env" >nul
)

echo.
echo ✅ تم إعداد النظام بنجاح!
echo.
echo 🚀 بدء تشغيل النظام...
echo.

:: تشغيل Backend
echo 🔥 تشغيل خادم Backend...
start "Travel System - Backend" /min cmd /c "cd backend && call venv\Scripts\activate.bat && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

:: انتظار قليل
timeout /t 3 /nobreak >nul

:: تشغيل Frontend
echo 🌐 تشغيل خادم Frontend...
start "Travel System - Frontend" /min cmd /c "cd frontend && npm start"

:: انتظار لبدء الخوادم
echo ⏳ انتظار بدء الخوادم...
timeout /t 10 /nobreak >nul

:: فتح المتصفح
echo 🌍 فتح المتصفح...
start http://localhost:3000

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                        النظام جاهز!                         ║
echo  ║                      System Ready!                          ║
echo  ╠══════════════════════════════════════════════════════════════╣
echo  ║                                                              ║
echo  ║  🌐 التطبيق الرئيسي: http://localhost:3000                  ║
echo  ║  📚 واجهة API: http://localhost:8000/api/docs               ║
echo  ║                                                              ║
echo  ║  👤 بيانات تسجيل الدخول:                                    ║
echo  ║     اسم المستخدم: admin                                     ║
echo  ║     كلمة المرور: admin123                                   ║
echo  ║                                                              ║
echo  ║  💡 لإيقاف النظام: أغلق نوافذ الخوادم                       ║
echo  ║                                                              ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

pause