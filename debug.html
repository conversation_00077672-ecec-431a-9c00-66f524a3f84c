<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .status-box {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #FFD700;
        }
        .success { border-left-color: #4CAF50; }
        .error { border-left-color: #f44336; }
        .warning { border-left-color: #ff9800; }
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
        }
        #results {
            margin-top: 20px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص نظام محاسبي لوكالات السفريات</h1>
        
        <div class="status-box">
            <h3>📋 معلومات النظام</h3>
            <p><strong>الوقت الحالي:</strong> <span id="currentTime"></span></p>
            <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
            <p><strong>نظام التشغيل:</strong> <span id="osInfo"></span></p>
        </div>

        <div class="status-box">
            <h3>🌐 اختبار الاتصال</h3>
            <button class="btn" onclick="testConnections()">🔄 اختبار الاتصال</button>
            <button class="btn" onclick="openFrontend()">🌐 فتح Frontend</button>
            <button class="btn" onclick="openBackend()">🖥️ فتح Backend</button>
            <button class="btn" onclick="openAPI()">📚 فتح API</button>
        </div>

        <div id="results"></div>

        <div class="status-box">
            <h3>🛠️ أدوات التشخيص</h3>
            <button class="btn" onclick="checkConsole()">🔍 فحص Console</button>
            <button class="btn" onclick="clearCache()">🗑️ مسح Cache</button>
            <button class="btn" onclick="reloadPage()">🔄 إعادة تحميل</button>
        </div>

        <div class="status-box">
            <h3>📞 معلومات الدعم</h3>
            <p><strong>Frontend URL:</strong> http://localhost:3000</p>
            <p><strong>Backend URL:</strong> http://localhost:8000</p>
            <p><strong>API Docs:</strong> http://localhost:8000/docs</p>
            <p><strong>بيانات الدخول:</strong> admin / admin123</p>
        </div>
    </div>

    <script>
        // تحديث الوقت
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        
        // معلومات المتصفح
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        document.getElementById('osInfo').textContent = navigator.platform;
        
        updateTime();
        setInterval(updateTime, 1000);

        // اختبار الاتصالات
        async function testConnections() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="status-box"><h3>🔄 جاري الاختبار...</h3></div>';
            
            const tests = [
                { name: 'Frontend', url: 'http://localhost:3000', type: 'html' },
                { name: 'Backend', url: 'http://localhost:8000', type: 'json' },
                { name: 'API Docs', url: 'http://localhost:8000/docs', type: 'html' }
            ];
            
            let html = '<div class="status-box"><h3>📊 نتائج الاختبار</h3>';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { 
                        method: 'GET',
                        mode: 'cors'
                    });
                    
                    if (response.ok) {
                        html += `<p>✅ <strong>${test.name}:</strong> يعمل بشكل طبيعي (${response.status})</p>`;
                    } else {
                        html += `<p>⚠️ <strong>${test.name}:</strong> مشكلة في الاستجابة (${response.status})</p>`;
                    }
                } catch (error) {
                    html += `<p>❌ <strong>${test.name}:</strong> لا يمكن الوصول - ${error.message}</p>`;
                }
            }
            
            html += '</div>';
            results.innerHTML = html;
        }

        // فتح الروابط
        function openFrontend() {
            window.open('http://localhost:3000', '_blank');
        }
        
        function openBackend() {
            window.open('http://localhost:8000', '_blank');
        }
        
        function openAPI() {
            window.open('http://localhost:8000/docs', '_blank');
        }

        // أدوات التشخيص
        function checkConsole() {
            console.log('🔍 فحص Console - إذا رأيت هذه الرسالة فـ JavaScript يعمل بشكل طبيعي');
            alert('تم فحص Console - تحقق من أدوات المطور (F12) لرؤية الرسائل');
        }
        
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            alert('تم مسح Cache - أعد تحميل الصفحة');
        }
        
        function reloadPage() {
            window.location.reload(true);
        }

        // اختبار تلقائي عند التحميل
        window.addEventListener('load', () => {
            setTimeout(testConnections, 1000);
        });
    </script>
</body>
</html>