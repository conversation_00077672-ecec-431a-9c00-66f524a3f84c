/* 📊 أنماط لوحة تحكم الحجوزات */

/* 📦 الحاوية الرئيسية */
.bookings-dashboard {
  padding: var(--space-6);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  min-height: 100vh;
  animation: fadeInUp 0.6s ease-out;
}

/* 🎯 رأس لوحة التحكم */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.dashboard-title h1 {
  margin: 0 0 var(--space-1) 0;
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dashboard-title p {
  margin: 0;
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

.dashboard-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.period-selector {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.period-selector:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 📊 الإحصائيات السريعة */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0.8;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.stat-card.primary::before {
  background: var(--gradient-primary);
}

.stat-card.success::before {
  background: var(--gradient-secondary);
}

.stat-card.warning::before {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.stat-card.info::before {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

.stat-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2xl);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  flex-shrink: 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
  line-height: 1;
}

.stat-change {
  font-size: 0.875rem;
  font-weight: 600;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  display: inline-block;
}

.stat-change.positive {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.stat-subtitle {
  font-size: 0.75rem;
  color: var(--neutral-500);
  font-weight: 500;
}

/* 🚨 التنبيهات */
.dashboard-alerts {
  margin-bottom: var(--space-8);
}

.dashboard-alerts h2 {
  margin: 0 0 var(--space-6) 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.alerts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-4);
}

.alert-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-5);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.alert-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
}

.alert-card.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.alert-card.warning::before {
  background: var(--warning-500);
}

.alert-card.info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.alert-card.info::before {
  background: var(--primary-500);
}

.alert-card.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.alert-card.success::before {
  background: var(--secondary-500);
}

.alert-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.alert-content h4 {
  margin: 0 0 var(--space-1) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.alert-content p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--neutral-600);
  line-height: 1.5;
}

.alert-action {
  padding: var(--space-2) var(--space-4);
  background: var(--primary-500);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.alert-action:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 📋 الحجوزات الأخيرة */
.recent-bookings {
  margin-bottom: var(--space-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.view-all-link {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.view-all-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.bookings-list {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
}

.booking-item {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  transition: all var(--transition-fast);
}

.booking-item:last-child {
  border-bottom: none;
}

.booking-item:hover {
  background: rgba(59, 130, 246, 0.05);
}

.booking-icon {
  font-size: 1.5rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.booking-details h4 {
  margin: 0 0 var(--space-1) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.booking-details p {
  margin: 0 0 var(--space-1) 0;
  font-size: 0.875rem;
  color: var(--neutral-600);
}

.booking-time {
  font-size: 0.75rem;
  color: var(--neutral-500);
}

.booking-amount {
  font-size: 1rem;
  font-weight: 700;
  color: var(--neutral-800);
}

/* ✅ المهام القادمة */
.upcoming-tasks {
  margin-bottom: var(--space-8);
}

.upcoming-tasks h2 {
  margin: 0 0 var(--space-6) 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.tasks-list {
  display: grid;
  gap: var(--space-3);
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.task-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
}

.task-item.priority-high::before {
  background: var(--danger-500);
}

.task-item.priority-medium::before {
  background: var(--warning-500);
}

.task-item.priority-low::before {
  background: var(--secondary-500);
}

.task-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.task-content h4 {
  margin: 0 0 var(--space-1) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.task-content p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--neutral-500);
}

.task-priority {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.task-priority.high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
}

.task-priority.medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-600);
}

.task-priority.low {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

/* 📊 التحليلات المدمجة */
.embedded-analytics {
  margin-top: var(--space-8);
}

/* 🔄 حالة التحميل */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
  min-height: 50vh;
}

.dashboard-loading p {
  margin-top: var(--space-4);
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .bookings-dashboard,
.dark-mode .bookings-dashboard {
  background: var(--gradient-cosmic);
}

[data-theme="dark"] .dashboard-title h1,
.dark-mode .dashboard-title h1 {
  color: var(--neutral-200);
}

[data-theme="dark"] .dashboard-title p,
.dark-mode .dashboard-title p {
  color: var(--neutral-400);
}

[data-theme="dark"] .stat-value,
.dark-mode .stat-value {
  color: var(--neutral-200);
}

[data-theme="dark"] .section-header h2,
.dark-mode .section-header h2 {
  color: var(--neutral-200);
}

[data-theme="dark"] .booking-details h4,
.dark-mode .booking-details h4 {
  color: var(--neutral-200);
}

[data-theme="dark"] .booking-amount,
.dark-mode .booking-amount {
  color: var(--neutral-200);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 1024px) {
  .quick-stats {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .alerts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .bookings-dashboard {
    padding: var(--space-4);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .quick-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .booking-item {
    grid-template-columns: auto 1fr;
    gap: var(--space-3);
  }
  
  .booking-status,
  .booking-amount {
    grid-column: 2;
    justify-self: end;
  }
}

@media (max-width: 480px) {
  .dashboard-title h1 {
    font-size: 2rem;
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .alert-card {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
}

/* 🎬 حركات خاصة */
.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .bookings-dashboard,
  .stat-card,
  .alert-card,
  .task-item {
    animation: none;
    transition: none;
  }
  
  .stat-card:hover,
  .alert-card:hover,
  .task-item:hover {
    transform: none;
  }
}
