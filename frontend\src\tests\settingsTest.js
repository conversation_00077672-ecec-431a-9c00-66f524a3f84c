// اختبار مكونات الإعدادات
import { validateSettings, sanitizeSettings, getDefaultSettings } from '../utils/settingsValidator';

// اختبار التحقق من صحة الإعدادات
export const testSettingsValidation = () => {
  console.log('🧪 بدء اختبار التحقق من صحة الإعدادات...');

  // اختبار إعدادات صحيحة
  const validSettings = getDefaultSettings();
  validSettings.general.companyName = 'شركة اختبار';
  validSettings.business.email = '<EMAIL>';
  validSettings.business.phone = '+966501234567';

  const validationResult = validateSettings(validSettings);
  console.log('✅ اختبار الإعدادات الصحيحة:', validationResult.isValid ? 'نجح' : 'فشل');

  // اختبار إعدادات خاطئة
  const invalidSettings = {
    general: { companyName: '' },
    business: { email: 'invalid-email', phone: '' },
    financial: { defaultCommissionRate: 150 }
  };

  const invalidValidationResult = validateSettings(invalidSettings);
  console.log('✅ اختبار الإعدادات الخاطئة:', !invalidValidationResult.isValid ? 'نجح' : 'فشل');
  console.log('📋 الأخطاء المكتشفة:', invalidValidationResult.errors);

  return {
    validTest: validationResult.isValid,
    invalidTest: !invalidValidationResult.isValid
  };
};

// اختبار تنظيف البيانات
export const testDataSanitization = () => {
  console.log('🧪 بدء اختبار تنظيف البيانات...');

  const dirtyData = {
    general: {
      companyName: '  شركة اختبار  '
    },
    business: {
      email: '  <EMAIL>  ',
      phone: '+966 50 123 4567'
    },
    financial: {
      defaultCommissionRate: '5.5',
      defaultTaxRate: '15.0'
    }
  };

  const cleanData = sanitizeSettings(dirtyData);
  
  const tests = {
    companyNameTrimmed: cleanData.general.companyName === 'شركة اختبار',
    emailLowercase: cleanData.business.email === '<EMAIL>',
    phoneNoSpaces: cleanData.business.phone === '+966501234567',
    commissionAsNumber: typeof cleanData.financial.defaultCommissionRate === 'number',
    taxAsNumber: typeof cleanData.financial.defaultTaxRate === 'number'
  };

  console.log('✅ نتائج اختبار تنظيف البيانات:', tests);
  
  return tests;
};

// اختبار الإعدادات الافتراضية
export const testDefaultSettings = () => {
  console.log('🧪 بدء اختبار الإعدادات الافتراضية...');

  const defaults = getDefaultSettings();
  
  const tests = {
    hasGeneralSettings: !!defaults.general,
    hasBusinessSettings: !!defaults.business,
    hasFinancialSettings: !!defaults.financial,
    hasNotificationSettings: !!defaults.notifications,
    hasSecuritySettings: !!defaults.security,
    hasIntegrationSettings: !!defaults.integrations,
    companyNameExists: !!defaults.general.companyName,
    currencyIsSAR: defaults.general.currency === 'SAR',
    languageIsArabic: defaults.general.language === 'ar'
  };

  console.log('✅ نتائج اختبار الإعدادات الافتراضية:', tests);
  
  return tests;
};

// تشغيل جميع الاختبارات
export const runAllSettingsTests = () => {
  console.log('🚀 بدء تشغيل جميع اختبارات الإعدادات...');
  
  const validationTests = testSettingsValidation();
  const sanitizationTests = testDataSanitization();
  const defaultTests = testDefaultSettings();

  const allTestsPassed = 
    validationTests.validTest &&
    validationTests.invalidTest &&
    Object.values(sanitizationTests).every(test => test) &&
    Object.values(defaultTests).every(test => test);

  console.log(allTestsPassed ? '🎉 جميع الاختبارات نجحت!' : '❌ بعض الاختبارات فشلت!');
  
  return {
    allPassed: allTestsPassed,
    validation: validationTests,
    sanitization: sanitizationTests,
    defaults: defaultTests
  };
};

// تصدير دالة للاختبار السريع
export const quickSettingsTest = () => {
  try {
    const results = runAllSettingsTests();
    return results.allPassed;
  } catch (error) {
    console.error('❌ خطأ في اختبار الإعدادات:', error);
    return false;
  }
};