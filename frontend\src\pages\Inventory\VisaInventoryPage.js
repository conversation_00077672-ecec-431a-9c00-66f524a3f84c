import React, { useState, useEffect } from 'react';

const VisaInventoryPage = () => {
  const [visaInventory, setVisaInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedVisa, setSelectedVisa] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCountry, setFilterCountry] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const [newVisa, setNewVisa] = useState({
    visaNumber: '',
    country: '',
    visaType: 'individual_yearly', // فردي سنة أو عادي 3 شهور
    totalCount: '',
    usedCount: '',
    remainingCount: '',
    issuerNumber: '',
    registrationNumber: '',
    companyName: '',
    profession: '',
    issueDate: '',
    supplierName: '',
    agentName: '',
    authorizationOffice: '',
    cost: '',
    sellingPrice: '',
    status: 'available',
    notes: ''
  });

  const [visaStats, setVisaStats] = useState({
    totalVisas: 0,
    availableVisas: 0,
    soldVisas: 0,
    expiredVisas: 0,
    totalValue: 0,
    totalProfit: 0
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      const mockVisaInventory = [
        {
          id: 1,
          visaNumber: 'UAE-2024-001',
          country: 'الإمارات العربية المتحدة',
          countryCode: 'AE',
          visaType: 'individual_yearly',
          totalCount: 100,
          usedCount: 25,
          remainingCount: 75,
          issuerNumber: 'ISS-2024-001',
          registrationNumber: 'REG-UAE-001',
          companyName: 'شركة الإمارات للسياحة',
          profession: 'سائح',
          issueDate: '2024-01-15',
          supplierName: 'وكالة الإمارات للسفر',
          agentName: 'أحمد محمد السالم',
          authorizationOffice: 'مكتب دبي للتفويض',
          cost: 800,
          sellingPrice: 1200,
          status: 'available',
          notes: 'تأشيرة فردية سنوية - متعددة الاستخدام'
        },
        {
          id: 2,
          visaNumber: 'TUR-2024-002',
          country: 'تركيا',
          countryCode: 'TR',
          visaType: 'regular_3months',
          totalCount: 50,
          usedCount: 30,
          remainingCount: 20,
          issuerNumber: 'ISS-2024-002',
          registrationNumber: 'REG-TUR-002',
          companyName: 'شركة الأناضول للسياحة',
          profession: 'رجل أعمال',
          issueDate: '2024-01-10',
          supplierName: 'القنصلية التركية',
          agentName: 'فاطمة أحمد الزهراني',
          authorizationOffice: 'مكتب إسطنبول للتفويض',
          cost: 600,
          sellingPrice: 900,
          status: 'sold',
          notes: 'تأشيرة عادية 3 شهور - تم البيع'
        },
        {
          id: 3,
          visaNumber: 'EGY-2024-003',
          country: 'مصر',
          countryCode: 'EG',
          visaType: 'regular_3months',
          totalCount: 200,
          usedCount: 150,
          remainingCount: 50,
          issuerNumber: 'ISS-2024-003',
          registrationNumber: 'REG-EGY-003',
          companyName: 'شركة النيل للسياحة',
          profession: 'سائح',
          issueDate: '2024-01-05',
          supplierName: 'القنصلية المصرية',
          agentName: 'محمد علي حسن',
          authorizationOffice: 'مكتب القاهرة للتفويض',
          cost: 300,
          sellingPrice: 500,
          status: 'available',
          notes: 'تأشيرة عادية 3 شهور - سياحية'
        },
        {
          id: 4,
          visaNumber: 'IND-2024-004',
          country: 'الهند',
          countryCode: 'IN',
          visaType: 'individual_yearly',
          totalCount: 75,
          usedCount: 75,
          remainingCount: 0,
          issuerNumber: 'ISS-2023-004',
          registrationNumber: 'REG-IND-004',
          companyName: 'شركة بومباي للتجارة',
          profession: 'تاجر',
          issueDate: '2023-12-20',
          supplierName: 'القنصلية الهندية',
          agentName: 'سارة محمد الأحمد',
          authorizationOffice: 'مكتب مومباي للتفويض',
          cost: 1000,
          sellingPrice: 1500,
          status: 'expired',
          notes: 'تأشيرة فردية سنوية - منتهية الصلاحية'
        },
        {
          id: 5,
          visaNumber: 'USA-2024-005',
          country: 'الولايات المتحدة',
          countryCode: 'US',
          visaType: 'individual_yearly',
          totalCount: 25,
          usedCount: 5,
          remainingCount: 20,
          issuerNumber: 'ISS-2024-005',
          registrationNumber: 'REG-USA-005',
          companyName: 'شركة أمريكا للسياحة',
          profession: 'مهندس',
          issueDate: '2024-01-20',
          supplierName: 'السفارة الأمريكية',
          agentName: 'خالد عبدالله المطيري',
          authorizationOffice: 'مكتب واشنطن للتفويض',
          cost: 2000,
          sellingPrice: 2800,
          status: 'reserved',
          notes: 'تأشيرة فردية سنوية - محجوزة للعميل'
        }
      ];

      setVisaInventory(mockVisaInventory);
      
      // حساب الإحصائيات
      const stats = {
        totalVisas: mockVisaInventory.length,
        availableVisas: mockVisaInventory.filter(v => v.status === 'available').length,
        soldVisas: mockVisaInventory.filter(v => v.status === 'sold').length,
        expiredVisas: mockVisaInventory.filter(v => v.status === 'expired').length,
        totalValue: mockVisaInventory.reduce((sum, v) => sum + v.sellingPrice, 0),
        totalProfit: mockVisaInventory.reduce((sum, v) => sum + (v.sellingPrice - v.cost), 0)
      };
      setVisaStats(stats);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddVisa = (e) => {
    e.preventDefault();
    const visa = {
      id: visaInventory.length + 1,
      ...newVisa,
      cost: parseFloat(newVisa.cost),
      sellingPrice: parseFloat(newVisa.sellingPrice)
    };
    setVisaInventory([visa, ...visaInventory]);
    setNewVisa({
      visaNumber: '',
      country: '',
      visaType: 'individual_yearly',
      totalCount: '',
      usedCount: '',
      remainingCount: '',
      issuerNumber: '',
      registrationNumber: '',
      companyName: '',
      profession: '',
      issueDate: '',
      supplierName: '',
      agentName: '',
      authorizationOffice: '',
      cost: '',
      sellingPrice: '',
      status: 'available',
      notes: ''
    });
    setShowAddForm(false);
  };

  const handleViewVisa = (visa) => {
    setSelectedVisa(visa);
    setShowViewModal(true);
  };

  const handleStatusChange = (visaId, newStatus) => {
    setVisaInventory(visaInventory.map(visa => 
      visa.id === visaId ? { ...visa, status: newStatus } : visa
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#27ae60';
      case 'sold': return '#3498db';
      case 'reserved': return '#f39c12';
      case 'expired': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'available': return 'متاح';
      case 'sold': return 'مباع';
      case 'reserved': return 'محجوز';
      case 'expired': return 'منتهي';
      default: return 'غير محدد';
    }
  };

  const getVisaTypeText = (type) => {
    switch (type) {
      case 'individual_yearly': return 'فردي سنة';
      case 'regular_3months': return 'عادي 3 شهور';
      default: return type;
    }
  };

  const getEntryTypeText = (type) => {
    switch (type) {
      case 'single': return 'دخول واحد';
      case 'multiple': return 'متعدد الدخول';
      default: return type;
    }
  };

  const getCountryFlag = (countryCode) => {
    const flags = {
      'AE': '🇦🇪',
      'TR': '🇹🇷',
      'EG': '🇪🇬',
      'IN': '🇮🇳',
      'US': '🇺🇸',
      'GB': '🇬🇧',
      'FR': '🇫🇷',
      'DE': '🇩🇪',
      'IT': '🇮🇹',
      'ES': '🇪🇸'
    };
    return flags[countryCode] || '🌍';
  };

  const formatCurrency = (amount) => {
    return `${amount.toLocaleString()} ر.س`;
  };

  const filteredVisas = visaInventory.filter(visa => {
    const matchesSearch = visa.visaNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         visa.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         visa.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCountry = filterCountry === 'all' || visa.country === filterCountry;
    const matchesType = filterType === 'all' || visa.visaType === filterType;
    const matchesStatus = filterStatus === 'all' || visa.status === filterStatus;
    return matchesSearch && matchesCountry && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#666' }}>جاري تحميل مخزون التأشيرات...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          📋 مخزون التأشيرات
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          إدارة ومتابعة مخزون التأشيرات والوثائق السفر
        </p>
      </div>

      {/* إحصائيات سريعة */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        {[
          { title: 'إجمالي التأشيرات', value: visaStats.totalVisas, color: '#3498db', icon: '📋' },
          { title: 'التأشيرات المتاحة', value: visaStats.availableVisas, color: '#27ae60', icon: '✅' },
          { title: 'التأشيرات المباعة', value: visaStats.soldVisas, color: '#8e44ad', icon: '💰' },
          { title: 'التأشيرات المنتهية', value: visaStats.expiredVisas, color: '#e74c3c', icon: '⏰' },
          { title: 'القيمة الإجمالية', value: formatCurrency(visaStats.totalValue), color: '#f39c12', icon: '💎' },
          { title: 'إجمالي الربح', value: formatCurrency(visaStats.totalProfit), color: '#16a085', icon: '📈' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '25px',
            borderRadius: '15px',
            boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}`,
            textAlign: 'center',
            transition: 'transform 0.3s ease',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
          onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <h3 style={{ color: stat.color, margin: '0 0 10px 0', fontSize: '14px' }}>{stat.title}</h3>
            <p style={{ fontSize: '20px', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>{stat.value}</p>
          </div>
        ))}
      </div>

      {/* أدوات التحكم */}
      <div style={{ 
        background: 'white', 
        padding: '25px', 
        borderRadius: '15px', 
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        marginBottom: '30px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2 style={{ color: '#2c3e50', margin: 0 }}>🔍 البحث والتصفية</h2>
          <button
            onClick={() => setShowAddForm(true)}
            style={{
              background: '#27ae60',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            ➕ إضافة تأشيرة جديدة
          </button>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div>
            <input
              type="text"
              placeholder="🔍 البحث برقم التأشيرة أو الدولة أو العميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                boxSizing: 'border-box'
              }}
            />
          </div>
          <div>
            <select
              value={filterCountry}
              onChange={(e) => setFilterCountry(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                boxSizing: 'border-box'
              }}
            >
              <option value="all">جميع الدول</option>
              <option value="الإمارات العربية المتحدة">الإمارات</option>
              <option value="تركيا">تركيا</option>
              <option value="مصر">مصر</option>
              <option value="الهند">الهند</option>
              <option value="الولايات المتحدة">أمريكا</option>
            </select>
          </div>
          <div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                boxSizing: 'border-box'
              }}
            >
              <option value="all">جميع الأنواع</option>
              <option value="individual_yearly">فردي سنة</option>
              <option value="regular_3months">عادي 3 شهور</option>
            </select>
          </div>
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '14px',
                boxSizing: 'border-box'
              }}
            >
              <option value="all">جميع الحالات</option>
              <option value="available">متاح</option>
              <option value="sold">مباع</option>
              <option value="reserved">محجوز</option>
              <option value="expired">منتهي</option>
            </select>
          </div>
        </div>
      </div>

      {/* جدول التأشيرات */}
      <div style={{ 
        background: 'white', 
        borderRadius: '15px', 
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ 
          background: '#34495e', 
          color: 'white', 
          padding: '20px',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          📋 قائمة التأشيرات ({filteredVisas.length})
        </div>
        
        <div style={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>رقم التأشيرة</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الدولة</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>النوع</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>العدد</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الشركة</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>التكلفة</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>سعر البيع</th>
                <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredVisas.map((visa) => (
                <tr key={visa.id} style={{ borderBottom: '1px solid #dee2e6' }}>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold', color: '#2c3e50' }}>{visa.visaNumber}</div>
                    <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                      {visa.issueDate}
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ fontSize: '20px' }}>{getCountryFlag(visa.countryCode)}</span>
                      <span>{visa.country}</span>
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold' }}>{getVisaTypeText(visa.visaType)}</div>
                    <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                      {visa.profession}
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontSize: '14px' }}>
                      <div>الإجمالي: <span style={{ fontWeight: 'bold', color: '#3498db' }}>{visa.totalCount}</span></div>
                      <div>المستخدم: <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>{visa.usedCount}</span></div>
                      <div>المتبقي: <span style={{ fontWeight: 'bold', color: '#27ae60' }}>{visa.remainingCount}</span></div>
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold' }}>{visa.companyName}</div>
                    <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                      {visa.agentName}
                    </div>
                  </td>
                  <td style={{ padding: '15px', color: '#e74c3c', fontWeight: 'bold' }}>
                    {formatCurrency(visa.cost)}
                  </td>
                  <td style={{ padding: '15px', color: '#27ae60', fontWeight: 'bold' }}>
                    {formatCurrency(visa.sellingPrice)}
                  </td>
                  <td style={{ padding: '15px' }}>
                    <span style={{
                      background: getStatusColor(visa.status),
                      color: 'white',
                      padding: '5px 12px',
                      borderRadius: '15px',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}>
                      {getStatusText(visa.status)}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', flexWrap: 'wrap' }}>
                      <button 
                        onClick={() => handleViewVisa(visa)}
                        style={{
                          background: '#3498db',
                          color: 'white',
                          border: 'none',
                          padding: '5px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        👁️ عرض
                      </button>
                      {visa.status === 'available' && (
                        <button 
                          onClick={() => handleStatusChange(visa.id, 'reserved')}
                          style={{
                            background: '#f39c12',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '5px',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                        >
                          📝 حجز
                        </button>
                      )}
                      {(visa.status === 'available' || visa.status === 'reserved') && (
                        <button 
                          onClick={() => handleStatusChange(visa.id, 'sold')}
                          style={{
                            background: '#27ae60',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '5px',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                        >
                          💰 بيع
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* نموذج إضافة تأشيرة جديدة */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '25px', textAlign: 'center' }}>
              ➕ إضافة تأشيرة جديدة
            </h2>
            
            <form onSubmit={handleAddVisa}>
              {/* معلومات التأشيرة الأساسية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#3498db', marginBottom: '15px' }}>📋 معلومات التأشيرة الأساسية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم التأشيرة</label>
                    <input
                      type="text"
                      value={newVisa.visaNumber}
                      onChange={(e) => setNewVisa({...newVisa, visaNumber: e.target.value})}
                      required
                      placeholder="مثال: UAE-2024-001"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الدولة</label>
                    <input
                      type="text"
                      value={newVisa.country}
                      onChange={(e) => setNewVisa({...newVisa, country: e.target.value})}
                      required
                      placeholder="مثال: الإمارات العربية المتحدة"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع التأشيرة</label>
                    <select
                      value={newVisa.visaType}
                      onChange={(e) => setNewVisa({...newVisa, visaType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="individual_yearly">فردي سنة</option>
                      <option value="regular_3months">عادي 3 شهور</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الصادر</label>
                    <input
                      type="text"
                      value={newVisa.issuerNumber}
                      onChange={(e) => setNewVisa({...newVisa, issuerNumber: e.target.value})}
                      required
                      placeholder="مثال: ISS-2024-001"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم السجل</label>
                    <input
                      type="text"
                      value={newVisa.registrationNumber}
                      onChange={(e) => setNewVisa({...newVisa, registrationNumber: e.target.value})}
                      required
                      placeholder="مثال: REG-UAE-001"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الإصدار</label>
                    <input
                      type="date"
                      value={newVisa.issueDate}
                      onChange={(e) => setNewVisa({...newVisa, issueDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* معلومات الشركة والمهنة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>🏢 معلومات الشركة والمهنة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم الشركة</label>
                    <input
                      type="text"
                      value={newVisa.companyName}
                      onChange={(e) => setNewVisa({...newVisa, companyName: e.target.value})}
                      required
                      placeholder="مثال: شركة الإمارات للسياحة"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المهنة</label>
                    <input
                      type="text"
                      value={newVisa.profession}
                      onChange={(e) => setNewVisa({...newVisa, profession: e.target.value})}
                      required
                      placeholder="مثال: سائح، مهندس، تاجر"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم المورد</label>
                    <input
                      type="text"
                      value={newVisa.supplierName}
                      onChange={(e) => setNewVisa({...newVisa, supplierName: e.target.value})}
                      required
                      placeholder="مثال: القنصلية الإماراتية"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم الوكيل المستخدم</label>
                    <input
                      type="text"
                      value={newVisa.agentName}
                      onChange={(e) => setNewVisa({...newVisa, agentName: e.target.value})}
                      required
                      placeholder="مثال: أحمد محمد السالم"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مكتب التفويض</label>
                    <input
                      type="text"
                      value={newVisa.authorizationOffice}
                      onChange={(e) => setNewVisa({...newVisa, authorizationOffice: e.target.value})}
                      required
                      placeholder="مثال: مكتب دبي للتفويض"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* معلومات العدد */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>🔢 معلومات العدد</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العدد الإجمالي</label>
                    <input
                      type="number"
                      value={newVisa.totalCount}
                      onChange={(e) => {
                        const total = parseInt(e.target.value) || 0;
                        const used = parseInt(newVisa.usedCount) || 0;
                        setNewVisa({
                          ...newVisa, 
                          totalCount: e.target.value,
                          remainingCount: (total - used).toString()
                        });
                      }}
                      required
                      min="0"
                      placeholder="مثال: 100"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العدد المستخدم</label>
                    <input
                      type="number"
                      value={newVisa.usedCount}
                      onChange={(e) => {
                        const used = parseInt(e.target.value) || 0;
                        const total = parseInt(newVisa.totalCount) || 0;
                        setNewVisa({
                          ...newVisa, 
                          usedCount: e.target.value,
                          remainingCount: (total - used).toString()
                        });
                      }}
                      min="0"
                      placeholder="مثال: 25"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العدد المتبقي</label>
                    <input
                      type="number"
                      value={newVisa.remainingCount}
                      readOnly
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box',
                        background: '#f8f9fa',
                        color: '#27ae60',
                        fontWeight: 'bold'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* المعلومات المالية والحالة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e67e22', marginBottom: '15px' }}>💰 المعلومات المالية والحالة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التكلفة (ر.س)</label>
                    <input
                      type="number"
                      value={newVisa.cost}
                      onChange={(e) => setNewVisa({...newVisa, cost: e.target.value})}
                      required
                      min="0"
                      step="0.01"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>سعر البيع (ر.س)</label>
                    <input
                      type="number"
                      value={newVisa.sellingPrice}
                      onChange={(e) => setNewVisa({...newVisa, sellingPrice: e.target.value})}
                      required
                      min="0"
                      step="0.01"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الربح المتوقع</label>
                    <input
                      type="text"
                      value={newVisa.sellingPrice && newVisa.cost ? 
                        formatCurrency(parseFloat(newVisa.sellingPrice) - parseFloat(newVisa.cost)) : '0 ر.س'}
                      readOnly
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box',
                        background: '#f8f9fa',
                        color: '#27ae60',
                        fontWeight: 'bold'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الحالة</label>
                    <select
                      value={newVisa.status}
                      onChange={(e) => setNewVisa({...newVisa, status: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="available">متاح</option>
                      <option value="reserved">محجوز</option>
                      <option value="sold">مباع</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>📝 ملاحظات</h3>
                <textarea
                  value={newVisa.notes}
                  onChange={(e) => setNewVisa({...newVisa, notes: e.target.value})}
                  rows="3"
                  placeholder="ملاحظات إضافية حول التأشيرة..."
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              {/* أزرار التحكم */}
              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    background: '#95a5a6',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    background: '#27ae60',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  ➕ إضافة التأشيرة
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة عرض تفاصيل التأشيرة */}
      {showViewModal && selectedVisa && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{ textAlign: 'center', marginBottom: '25px' }}>
              <h2 style={{ color: '#2c3e50', margin: 0 }}>📋 تفاصيل التأشيرة</h2>
              <p style={{ color: '#7f8c8d', margin: '5px 0 0 0' }}>{selectedVisa.visaNumber}</p>
            </div>

            {/* معلومات التأشيرة الأساسية */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ color: '#34495e', margin: '0 0 15px 0' }}>🌍 معلومات التأشيرة الأساسية</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <strong>الدولة:</strong> 
                  <span style={{ marginRight: '8px' }}>
                    {getCountryFlag(selectedVisa.countryCode)} {selectedVisa.country}
                  </span>
                </div>
                <div><strong>النوع:</strong> {getVisaTypeText(selectedVisa.visaType)}</div>
                <div><strong>رقم الصادر:</strong> {selectedVisa.issuerNumber}</div>
                <div><strong>رقم السجل:</strong> {selectedVisa.registrationNumber}</div>
                <div><strong>المهنة:</strong> {selectedVisa.profession}</div>
                <div>
                  <strong>الحالة:</strong> 
                  <span style={{
                    background: getStatusColor(selectedVisa.status),
                    color: 'white',
                    padding: '3px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    marginRight: '8px'
                  }}>
                    {getStatusText(selectedVisa.status)}
                  </span>
                </div>
              </div>
            </div>

            {/* معلومات الشركة والوكلاء */}
            <div style={{ 
              background: '#e8f5e8', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#27ae60' }}>🏢 معلومات الشركة والوكلاء</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div><strong>اسم الشركة:</strong> {selectedVisa.companyName}</div>
                <div><strong>اسم المورد:</strong> {selectedVisa.supplierName}</div>
                <div><strong>اسم الوكيل:</strong> {selectedVisa.agentName}</div>
                <div><strong>مكتب التفويض:</strong> {selectedVisa.authorizationOffice}</div>
              </div>
            </div>

            {/* معلومات العدد */}
            <div style={{ 
              background: '#fff3cd', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#856404' }}>🔢 معلومات العدد</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                <div>
                  <strong>العدد الإجمالي:</strong>
                  <div style={{ color: '#3498db', fontWeight: 'bold', fontSize: '18px' }}>
                    {selectedVisa.totalCount}
                  </div>
                </div>
                <div>
                  <strong>العدد المستخدم:</strong>
                  <div style={{ color: '#e74c3c', fontWeight: 'bold', fontSize: '18px' }}>
                    {selectedVisa.usedCount}
                  </div>
                </div>
                <div>
                  <strong>العدد المتبقي:</strong>
                  <div style={{ color: '#27ae60', fontWeight: 'bold', fontSize: '18px' }}>
                    {selectedVisa.remainingCount}
                  </div>
                </div>
              </div>
            </div>

            {/* المعلومات المالية */}
            <div style={{ 
              background: '#e8f5e8', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#27ae60' }}>💰 المعلومات المالية</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                <div>
                  <strong>التكلفة:</strong>
                  <div style={{ color: '#e74c3c', fontWeight: 'bold', fontSize: '16px' }}>
                    {formatCurrency(selectedVisa.cost)}
                  </div>
                </div>
                <div>
                  <strong>سعر البيع:</strong>
                  <div style={{ color: '#27ae60', fontWeight: 'bold', fontSize: '16px' }}>
                    {formatCurrency(selectedVisa.sellingPrice)}
                  </div>
                </div>
                <div>
                  <strong>الربح:</strong>
                  <div style={{ color: '#3498db', fontWeight: 'bold', fontSize: '16px' }}>
                    {formatCurrency(selectedVisa.sellingPrice - selectedVisa.cost)}
                  </div>
                </div>
              </div>
            </div>

            {/* التواريخ */}
            <div style={{ 
              background: '#d1ecf1', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#0c5460' }}>📅 التواريخ</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '15px' }}>
                <div><strong>تاريخ الإصدار:</strong> {selectedVisa.issueDate}</div>
              </div>
            </div>

            {/* معلومات العميل (إذا كانت متوفرة) */}
            {(selectedVisa.customerName || selectedVisa.customerPhone) && (
              <div style={{ 
                background: '#d1ecf1', 
                padding: '20px', 
                borderRadius: '10px',
                marginBottom: '20px'
              }}>
                <h4 style={{ margin: '0 0 15px 0', color: '#0c5460' }}>👤 معلومات العميل</h4>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                  {selectedVisa.customerName && <div><strong>الاسم:</strong> {selectedVisa.customerName}</div>}
                  {selectedVisa.customerPhone && <div><strong>الهاتف:</strong> {selectedVisa.customerPhone}</div>}
                  {selectedVisa.customerPassport && <div><strong>رقم الجواز:</strong> {selectedVisa.customerPassport}</div>}
                </div>
              </div>
            )}

            {/* الملاحظات */}
            {selectedVisa.notes && (
              <div style={{ 
                background: '#f0f0f0', 
                padding: '15px', 
                borderRadius: '10px',
                marginBottom: '20px'
              }}>
                <strong>📝 ملاحظات:</strong> {selectedVisa.notes}
              </div>
            )}

            <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
              <button
                onClick={() => setShowViewModal(false)}
                style={{
                  background: '#95a5a6',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إغلاق
              </button>
              {selectedVisa.status === 'available' && (
                <button
                  onClick={() => {
                    handleStatusChange(selectedVisa.id, 'sold');
                    setShowViewModal(false);
                  }}
                  style={{
                    background: '#27ae60',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  💰 بيع التأشيرة
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* CSS للأنيميشن */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default VisaInventoryPage;