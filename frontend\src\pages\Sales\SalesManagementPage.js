import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const SalesManagementPage = () => {
  const [salesData, setSalesData] = useState({
    todaySales: 0,
    monthSales: 0,
    yearSales: 0,
    totalInvoices: 0,
    pendingPayments: 0,
    totalCustomers: 0
  });

  const [recentInvoices, setRecentInvoices] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setSalesData({
        todaySales: 15750,
        monthSales: 485000,
        yearSales: 2850000,
        totalInvoices: 1247,
        pendingPayments: 125000,
        totalCustomers: 856
      });

      setRecentInvoices([
        {
          id: 'INV-2024-001',
          customerName: 'أحمد محمد العلي',
          service: 'حجز طيران',
          amount: 2500,
          currency: 'SAR',
          status: 'paid',
          date: '2024-01-20',
          paymentMethod: 'نقدي'
        },
        {
          id: 'INV-2024-002',
          customerName: 'فاطمة سالم',
          service: 'حجز عمرة',
          amount: 8500,
          currency: 'SAR',
          status: 'pending',
          date: '2024-01-20',
          paymentMethod: 'تحويل بنكي'
        },
        {
          id: 'INV-2024-003',
          customerName: 'خالد الأحمد',
          service: 'حجز سيارة',
          amount: 450,
          currency: 'USD',
          status: 'partial',
          date: '2024-01-19',
          paymentMethod: 'نقدي'
        }
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'partial': return '#e67e22';
      case 'overdue': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'partial': return 'جزئي';
      case 'overdue': return 'متأخر';
      default: return status;
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#666' }}>جاري تحميل بيانات المبيعات...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          💼 نظام إدارة المبيعات المحاسبي
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          إدارة شاملة للمبيعات والفواتير والتقارير المالية
        </p>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        {[
          { title: 'مبيعات اليوم', value: formatCurrency(salesData.todaySales), color: '#3498db', icon: '📈' },
          { title: 'مبيعات الشهر', value: formatCurrency(salesData.monthSales), color: '#27ae60', icon: '📊' },
          { title: 'مبيعات السنة', value: formatCurrency(salesData.yearSales), color: '#8e44ad', icon: '💰' },
          { title: 'إجمالي الفواتير', value: salesData.totalInvoices, color: '#e67e22', icon: '📄' },
          { title: 'المدفوعات المعلقة', value: formatCurrency(salesData.pendingPayments), color: '#e74c3c', icon: '⏳' },
          { title: 'إجمالي العملاء', value: salesData.totalCustomers, color: '#16a085', icon: '👥' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '25px',
            borderRadius: '15px',
            boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}`,
            textAlign: 'center',
            transition: 'transform 0.3s ease',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
          onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <h3 style={{ color: stat.color, margin: '0 0 10px 0', fontSize: '16px' }}>{stat.title}</h3>
            <p style={{ fontSize: '24px', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>{stat.value}</p>
          </div>
        ))}
      </div>

      {/* الوصول السريع */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '22px' }}>🚀 الوصول السريع</h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '15px' 
        }}>
          {[
            { title: 'إنشاء فاتورة جديدة', icon: '📝', color: '#3498db', path: '/sales/invoices/new' },
            { title: 'إدارة الفواتير', icon: '📋', color: '#27ae60', path: '/sales/invoices' },
            { title: 'إدارة العملاء', icon: '👥', color: '#8e44ad', path: '/sales/customers' },
            { title: 'التقارير المالية', icon: '📊', color: '#e67e22', path: '/sales/reports' },
            { title: 'إدارة المدفوعات', icon: '💳', color: '#e74c3c', path: '/sales/payments' },
            { title: 'إعدادات النظام', icon: '⚙️', color: '#16a085', path: '/sales/settings' }
          ].map((item, index) => (
            <Link 
              key={index}
              to={item.path}
              style={{ textDecoration: 'none' }}
            >
              <div style={{
                background: 'white',
                padding: '20px',
                borderRadius: '12px',
                boxShadow: '0 3px 10px rgba(0,0,0,0.1)',
                border: `2px solid ${item.color}`,
                textAlign: 'center',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-3px)';
                e.target.style.boxShadow = '0 5px 20px rgba(0,0,0,0.2)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 3px 10px rgba(0,0,0,0.1)';
              }}
              >
                <div style={{ fontSize: '24px', marginBottom: '10px' }}>{item.icon}</div>
                <h4 style={{ color: item.color, margin: 0, fontSize: '14px' }}>{item.title}</h4>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* الفواتير الحديثة */}
      <div style={{ marginBottom: '30px' }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '20px' 
        }}>
          <h2 style={{ color: '#2c3e50', margin: 0, fontSize: '22px' }}>📄 الفواتير الحديثة</h2>
          <Link 
            to="/sales/invoices"
            style={{
              background: '#3498db',
              color: 'white',
              padding: '10px 20px',
              borderRadius: '8px',
              textDecoration: 'none',
              fontSize: '14px',
              transition: 'background 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.background = '#2980b9'}
            onMouseLeave={(e) => e.target.style.background = '#3498db'}
          >
            عرض جميع الفواتير
          </Link>
        </div>

        <div style={{
          background: 'white',
          borderRadius: '15px',
          boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>رقم الفاتورة</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>العميل</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>الخدمة</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>المبلغ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>التاريخ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {recentInvoices.map((invoice, index) => (
                <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#3498db' }}>
                    {invoice.id}
                  </td>
                  <td style={{ padding: '15px' }}>{invoice.customerName}</td>
                  <td style={{ padding: '15px' }}>{invoice.service}</td>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#27ae60' }}>
                    {formatCurrency(invoice.amount, invoice.currency)}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      background: getStatusColor(invoice.status),
                      color: 'white',
                      padding: '5px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}>
                      {getStatusText(invoice.status)}
                    </span>
                  </td>
                  <td style={{ padding: '15px' }}>{invoice.date}</td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <button style={{
                      background: '#3498db',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      marginLeft: '5px'
                    }}>
                      عرض
                    </button>
                    <button style={{
                      background: '#27ae60',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}>
                      طباعة
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* CSS للأنيميشن */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default SalesManagementPage;
