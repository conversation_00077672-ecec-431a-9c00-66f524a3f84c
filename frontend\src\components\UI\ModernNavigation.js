import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { IconButton } from './ModernButton';
import './ModernNavigation.css';

// Modern Navigation Bar
export const ModernNavbar = ({
  brand,
  children,
  variant = 'default',
  fixed = false,
  transparent = false,
  className = '',
  ...props
}) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    if (transparent) {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [transparent]);

  const navbarClasses = [
    'modern-navbar',
    `modern-navbar--${variant}`,
    fixed && 'modern-navbar--fixed',
    transparent && 'modern-navbar--transparent',
    transparent && isScrolled && 'modern-navbar--scrolled',
    className
  ].filter(Boolean).join(' ');

  return (
    <nav className={navbarClasses} {...props}>
      <div className="modern-navbar-container">
        {brand && (
          <div className="modern-navbar-brand">
            {brand}
          </div>
        )}
        
        <div className="modern-navbar-content">
          {children}
        </div>
      </div>
    </nav>
  );
};

// Navigation Menu
export const NavMenu = ({
  children,
  direction = 'horizontal',
  className = '',
  ...props
}) => {
  const menuClasses = [
    'nav-menu',
    `nav-menu--${direction}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <ul className={menuClasses} {...props}>
      {children}
    </ul>
  );
};

// Navigation Item
export const NavItem = ({
  children,
  to,
  href,
  active,
  disabled = false,
  icon,
  badge,
  onClick,
  className = '',
  ...props
}) => {
  const location = useLocation();
  const isActive = active || (to && location.pathname === to);

  const itemClasses = [
    'nav-item',
    isActive && 'nav-item--active',
    disabled && 'nav-item--disabled',
    className
  ].filter(Boolean).join(' ');

  const content = (
    <>
      {icon && <span className="nav-item-icon">{icon}</span>}
      <span className="nav-item-text">{children}</span>
      {badge && <span className="nav-item-badge">{badge}</span>}
    </>
  );

  if (to) {
    return (
      <li className={itemClasses}>
        <Link to={to} className="nav-item-link" onClick={onClick} {...props}>
          {content}
        </Link>
      </li>
    );
  }

  if (href) {
    return (
      <li className={itemClasses}>
        <a href={href} className="nav-item-link" onClick={onClick} {...props}>
          {content}
        </a>
      </li>
    );
  }

  return (
    <li className={itemClasses}>
      <button 
        className="nav-item-button" 
        onClick={onClick} 
        disabled={disabled}
        {...props}
      >
        {content}
      </button>
    </li>
  );
};

// Dropdown Menu
export const Dropdown = ({
  trigger,
  children,
  position = 'bottom-right',
  className = '',
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [menuStyle, setMenuStyle] = useState({});
  const dropdownRef = useRef(null);
  const triggerRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    const updateMenuPosition = () => {
      if (isOpen && triggerRef.current) {
        const triggerRect = triggerRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const menuWidth = 220; // عرض القائمة المتوقع

        // Debug: طباعة معلومات للتأكد من عمل الكود
        console.log('Dropdown positioning:', {
          position,
          className,
          triggerRect,
          isMoreMenu: className.includes('more-menu-dropdown')
        });

        // تحديد نوع التموضع حسب position prop
        let leftPosition, topPosition, transform = 'none';

        if (position === 'bottom-left' || className.includes('more-menu-dropdown')) {
          // محاذاة القائمة تحت الزر من اليسار مباشرة
          leftPosition = triggerRect.left;
          topPosition = triggerRect.bottom + 8;
          
          // التأكد من أن القائمة لا تخرج من حدود الشاشة
          if (leftPosition + menuWidth > viewportWidth) {
            leftPosition = viewportWidth - menuWidth - 16;
          } else if (leftPosition < 16) {
            leftPosition = 16;
          }
        } else if (position === 'bottom-right') {
          // محاذاة القائمة تحت الزر من اليمين
          leftPosition = triggerRect.right - menuWidth;
          topPosition = triggerRect.bottom + 8;
          
          if (leftPosition < 16) {
            leftPosition = 16;
          } else if (leftPosition + menuWidth > viewportWidth) {
            leftPosition = viewportWidth - menuWidth - 16;
          }
        } else if (position === 'bottom-center') {
          // محاذاة القائمة في المنتصف (الافتراضي القديم)
          leftPosition = triggerRect.left + (triggerRect.width / 2);
          topPosition = triggerRect.bottom + 8;
          transform = 'translateX(-50%)';
          
          // تعديل للتأكد من عدم الخروج من الشاشة
          const halfMenuWidth = menuWidth / 2;
          if (leftPosition - halfMenuWidth < 16) {
            leftPosition = halfMenuWidth + 16;
          } else if (leftPosition + halfMenuWidth > viewportWidth) {
            leftPosition = viewportWidth - halfMenuWidth - 16;
          }
        } else {
          // افتراضي: bottom-left
          leftPosition = triggerRect.left;
          topPosition = triggerRect.bottom + 8;
        }

        const style = {
          position: 'fixed',
          top: topPosition,
          left: leftPosition,
          transform: transform,
          zIndex: 9999,
          minWidth: '220px'
        };
        
        // Debug: طباعة الـ style النهائي
        console.log('Final dropdown style:', style);
        
        setMenuStyle(style);
      } else {
        setMenuStyle({});
      }
    };

    updateMenuPosition();

    // إعادة حساب الموضع عند تغيير حجم النافذة
    if (isOpen) {
      window.addEventListener('resize', updateMenuPosition);
      window.addEventListener('scroll', updateMenuPosition);

      return () => {
        window.removeEventListener('resize', updateMenuPosition);
        window.removeEventListener('scroll', updateMenuPosition);
      };
    }
  }, [isOpen, className, position]);

  const dropdownClasses = [
    'dropdown',
    isOpen && 'dropdown--open',
    className
  ].filter(Boolean).join(' ');

  const menuClasses = [
    'dropdown-menu',
    `dropdown-menu--${position}`
  ].filter(Boolean).join(' ');

  return (
    <div ref={dropdownRef} className={dropdownClasses} data-open={isOpen} {...props}>
      <div 
        ref={triggerRef}
        className="dropdown-trigger"
        onClick={() => setIsOpen(!isOpen)}
      >
        {trigger}
      </div>
      
      {isOpen && (
        <div className={menuClasses} style={menuStyle}>
          {children}
        </div>
      )}
    </div>
  );
};

// Dropdown Item
export const DropdownItem = ({
  children,
  to,
  href,
  icon,
  disabled = false,
  onClick,
  className = '',
  ...props
}) => {
  const itemClasses = [
    'dropdown-item',
    disabled && 'dropdown-item--disabled',
    className
  ].filter(Boolean).join(' ');

  const content = (
    <>
      {icon && <span className="dropdown-item-icon">{icon}</span>}
      <span className="dropdown-item-text">{children}</span>
    </>
  );

  if (to) {
    return (
      <Link to={to} className={itemClasses} onClick={onClick} {...props}>
        {content}
      </Link>
    );
  }

  if (href) {
    return (
      <a href={href} className={itemClasses} onClick={onClick} {...props}>
        {content}
      </a>
    );
  }

  return (
    <button 
      className={itemClasses} 
      onClick={onClick} 
      disabled={disabled}
      {...props}
    >
      {content}
    </button>
  );
};

// Breadcrumb Navigation
export const Breadcrumb = ({
  items = [],
  separator = '/',
  className = '',
  ...props
}) => {
  const breadcrumbClasses = [
    'breadcrumb',
    className
  ].filter(Boolean).join(' ');

  return (
    <nav className={breadcrumbClasses} aria-label="breadcrumb" {...props}>
      <ol className="breadcrumb-list">
        {items.map((item, index) => (
          <li key={index} className="breadcrumb-item">
            {index < items.length - 1 ? (
              <>
                {item.to ? (
                  <Link to={item.to} className="breadcrumb-link">
                    {item.label}
                  </Link>
                ) : (
                  <span className="breadcrumb-text">{item.label}</span>
                )}
                <span className="breadcrumb-separator">{separator}</span>
              </>
            ) : (
              <span className="breadcrumb-current" aria-current="page">
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Tabs Navigation
export const Tabs = ({
  children,
  defaultTab = 0,
  onChange,
  variant = 'default',
  className = '',
  ...props
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleTabChange = (index) => {
    setActiveTab(index);
    onChange?.(index);
  };

  const tabsClasses = [
    'tabs',
    `tabs--${variant}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={tabsClasses} {...props}>
      <div className="tabs-header">
        {React.Children.map(children, (child, index) => (
          <button
            key={index}
            className={`tab-button ${activeTab === index ? 'tab-button--active' : ''}`}
            onClick={() => handleTabChange(index)}
          >
            {child.props.icon && (
              <span className="tab-button-icon">{child.props.icon}</span>
            )}
            <span className="tab-button-text">{child.props.label}</span>
            {child.props.badge && (
              <span className="tab-button-badge">{child.props.badge}</span>
            )}
          </button>
        ))}
      </div>
      
      <div className="tabs-content">
        {React.Children.map(children, (child, index) => (
          <div
            key={index}
            className={`tab-panel ${activeTab === index ? 'tab-panel--active' : ''}`}
          >
            {activeTab === index && child.props.children}
          </div>
        ))}
      </div>
    </div>
  );
};

// Tab Panel
export const TabPanel = ({ children, label, icon, badge }) => {
  return <div>{children}</div>;
};

// Pagination
export const Pagination = ({
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  maxVisible = 5,
  className = '',
  ...props
}) => {
  const getVisiblePages = () => {
    const pages = [];
    const start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    const end = Math.min(totalPages, start + maxVisible - 1);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  };

  const paginationClasses = [
    'pagination',
    className
  ].filter(Boolean).join(' ');

  return (
    <nav className={paginationClasses} {...props}>
      <ul className="pagination-list">
        {showFirstLast && currentPage > 1 && (
          <li className="pagination-item">
            <button
              className="pagination-button"
              onClick={() => onPageChange(1)}
              aria-label="الصفحة الأولى"
            >
              ⏮
            </button>
          </li>
        )}
        
        {showPrevNext && currentPage > 1 && (
          <li className="pagination-item">
            <button
              className="pagination-button"
              onClick={() => onPageChange(currentPage - 1)}
              aria-label="الصفحة السابقة"
            >
              ◀
            </button>
          </li>
        )}
        
        {getVisiblePages().map(page => (
          <li key={page} className="pagination-item">
            <button
              className={`pagination-button ${page === currentPage ? 'pagination-button--active' : ''}`}
              onClick={() => onPageChange(page)}
              aria-label={`الصفحة ${page}`}
              aria-current={page === currentPage ? 'page' : undefined}
            >
              {page}
            </button>
          </li>
        ))}
        
        {showPrevNext && currentPage < totalPages && (
          <li className="pagination-item">
            <button
              className="pagination-button"
              onClick={() => onPageChange(currentPage + 1)}
              aria-label="الصفحة التالية"
            >
              ▶
            </button>
          </li>
        )}
        
        {showFirstLast && currentPage < totalPages && (
          <li className="pagination-item">
            <button
              className="pagination-button"
              onClick={() => onPageChange(totalPages)}
              aria-label="الصفحة الأخيرة"
            >
              ⏭
            </button>
          </li>
        )}
      </ul>
    </nav>
  );
};

// Stepper Navigation
export const Stepper = ({
  steps = [],
  currentStep = 0,
  onStepClick,
  variant = 'default',
  className = '',
  ...props
}) => {
  const stepperClasses = [
    'stepper',
    `stepper--${variant}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={stepperClasses} {...props}>
      {steps.map((step, index) => (
        <div
          key={index}
          className={`stepper-step ${
            index === currentStep ? 'stepper-step--current' :
            index < currentStep ? 'stepper-step--completed' :
            'stepper-step--pending'
          }`}
        >
          <button
            className="stepper-step-button"
            onClick={() => onStepClick?.(index)}
            disabled={!onStepClick}
          >
            <div className="stepper-step-indicator">
              {index < currentStep ? '✓' : index + 1}
            </div>
            <div className="stepper-step-content">
              <div className="stepper-step-title">{step.title}</div>
              {step.description && (
                <div className="stepper-step-description">{step.description}</div>
              )}
            </div>
          </button>
          
          {index < steps.length - 1 && (
            <div className="stepper-connector" />
          )}
        </div>
      ))}
    </div>
  );
};

export default ModernNavbar;