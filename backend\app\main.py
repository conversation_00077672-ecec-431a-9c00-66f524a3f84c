"""
نظام محاسبي متكامل لوكالات السفريات
Travel Agency Accounting System - Main Application
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import time
import os
from pathlib import Path

from app.core.config import settings
from app.core.database import engine, Base
from app.api.api_v1.api import api_router

# إنشاء جداول قاعدة البيانات
Base.metadata.create_all(bind=engine)

# إنشاء التطبيق
app = FastAPI(
    title="نظام محاسبي لوكالات السفريات",
    description="نظام محاسبي شامل ومتطور لإدارة وكالات السفريات",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد Trusted Host (مستند إلى الإعدادات)
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# Middleware لقياس وقت الاستجابة
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# إعداد الملفات الثابتة
uploads_dir = Path("uploads")
uploads_dir.mkdir(exist_ok=True)
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# تضمين المسارات
app.include_router(api_router, prefix=settings.API_V1_STR)

# معالج الأخطاء العام
@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "message": "حدث خطأ داخلي في الخادم",
            "detail": str(exc) if settings.DEBUG else "خطأ داخلي"
        }
    )

# الصفحة الرئيسية
@app.get("/")
async def root():
    return {
        "message": "مرحباً بك في نظام محاسبي لوكالات السفريات",
        "version": "1.0.0",
        "docs": "/api/docs",
        "status": "running"
    }

# فحص حالة النظام
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )