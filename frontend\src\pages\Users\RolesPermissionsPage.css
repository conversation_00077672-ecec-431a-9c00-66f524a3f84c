/* صفحة إدارة الأدوار والصلاحيات */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

.roles-permissions-page {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* رأس الصفحة */
.page-header {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
  margin-bottom: 30px;
}

.header-text {
  text-align: center;
}

.page-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.title-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.6;
}

/* إحصائيات سريعة */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid #667eea;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.95rem;
  font-weight: 500;
}

/* التبويبات */
.tabs-container {
  background: white;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.tabs {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 5px;
}

.tab {
  background: transparent;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: inherit;
  color: #7f8c8d;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-actions {
  display: flex;
  gap: 15px;
}

.btn-add {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: inherit;
}

.btn-add:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.btn-icon {
  font-size: 1.1rem;
}

/* محتوى التبويبات */
.tab-content {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* شبكة الأدوار */
.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 25px;
}

.role-card {
  background: white;
  border: 2px solid #f1f3f4;
  border-radius: 16px;
  padding: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.role-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.role-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.role-info {
  display: flex;
  gap: 15px;
  flex: 1;
}

.role-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.role-details {
  flex: 1;
}

.role-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.role-description {
  color: #7f8c8d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

.role-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 35px;
  height: 35px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.action-btn.delete {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.role-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.role-stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.stat-icon {
  font-size: 1rem;
}

.system-badge {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.badge-icon {
  font-size: 0.9rem;
}

.role-permissions h4 {
  font-size: 1rem;
  color: #2c3e50;
  margin: 0 0 12px 0;
}

.permissions-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.permission-tag {
  background: #f8f9fa;
  color: #2c3e50;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #e1e8ed;
}

.permission-tag.all {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

/* حاوية الصلاحيات */
.permissions-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.permissions-category {
  border: 2px solid #f1f3f4;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.permissions-category:hover {
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
}

.category-header {
  background: #f8f9fa;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #f1f3f4;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: white;
}

.category-name {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.category-count {
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  padding: 25px;
}

.permission-card {
  background: white;
  border: 1px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.permission-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.permission-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.permission-icon {
  font-size: 1.3rem;
  width: 35px;
  text-align: center;
}

.permission-details {
  flex: 1;
}

.permission-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 6px 0;
}

.permission-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.permission-actions {
  display: flex;
  gap: 6px;
}

.permission-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.permission-stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #7f8c8d;
  font-size: 0.85rem;
}

/* مصفوفة الصلاحيات */
.permissions-matrix {
  overflow-x: auto;
}

.matrix-container {
  min-width: 800px;
}

.matrix-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #f1f3f4;
  border-radius: 12px;
  overflow: hidden;
}

.matrix-table th {
  background: #f8f9fa;
  padding: 15px 10px;
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e8ed;
  font-size: 0.9rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.matrix-table th.role-header {
  text-align: right;
  width: 200px;
  background: #667eea;
  color: white;
}

.matrix-table th.permission-header {
  min-width: 120px;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.permission-header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.permission-header-content .permission-icon {
  font-size: 1.2rem;
}

.permission-header-content .permission-name {
  font-size: 0.8rem;
  writing-mode: horizontal-tb;
  text-orientation: upright;
}

.matrix-table td {
  padding: 12px 10px;
  border-bottom: 1px solid #f1f3f4;
  text-align: center;
  vertical-align: middle;
}

.matrix-table tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.role-cell {
  background: #f8f9fa;
  text-align: right !important;
  font-weight: 600;
}

.role-cell-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-cell-content .role-icon {
  width: 30px;
  height: 30px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
  flex-shrink: 0;
}

.role-cell-content .role-name {
  font-size: 0.95rem;
}

.permission-cell {
  padding: 8px;
}

.permission-toggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permission-toggle:hover:not(:disabled) {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

.permission-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.permission-toggle.active {
  background: rgba(39, 174, 96, 0.1);
}

/* النوافذ المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal {
  background: white;
  border-radius: 16px;
  max-width: 700px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 25px 30px 20px;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f1f3f4;
  color: #2c3e50;
}

.modal-body {
  padding: 25px 30px;
}

.modal-footer {
  padding: 20px 30px 25px;
  border-top: 1px solid #f1f3f4;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

/* نموذج الإضافة/التعديل */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* اختيار الصلاحيات */
.permissions-selection {
  margin-top: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.permissions-selection h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.permissions-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.permission-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.permission-checkbox:hover {
  background: rgba(102, 126, 234, 0.1);
}

.permission-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #667eea;
}

.permission-label {
  font-size: 0.95rem;
  color: #2c3e50;
  font-weight: 500;
}

/* أزرار النموذج */
.btn-cancel {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-cancel:hover {
  background: #7f8c8d;
}

.btn-save {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-save:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-delete {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-delete:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* نافذة الحذف */
.delete-modal {
  max-width: 450px;
}

.delete-warning {
  text-align: center;
  padding: 20px 0;
}

.warning-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.delete-warning p {
  margin: 10px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.item-to-delete {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 15px 0;
  color: #2c3e50;
}

.warning-text {
  color: #e74c3c !important;
  font-weight: 600;
  font-size: 0.95rem !important;
}

/* التجاوب مع الشاشات */
@media (max-width: 1024px) {
  .tabs-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .tabs {
    justify-content: center;
  }
  
  .tab-actions {
    justify-content: center;
  }
  
  .roles-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
  
  .permissions-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .roles-permissions-page {
    padding: 15px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .page-title {
    font-size: 1.8rem;
    flex-direction: column;
    gap: 10px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .roles-grid {
    grid-template-columns: 1fr;
  }
  
  .permissions-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .permissions-checkboxes {
    grid-template-columns: 1fr;
  }
  
  .modal {
    width: 95%;
    margin: 10px;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }
  
  .matrix-table th.permission-header {
    writing-mode: horizontal-tb;
    text-orientation: upright;
    min-width: 100px;
  }
  
  .permission-header-content {
    flex-direction: row;
    writing-mode: horizontal-tb;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .tabs {
    flex-direction: column;
    width: 100%;
  }
  
  .tab {
    justify-content: center;
  }
  
  .role-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .role-actions {
    justify-content: center;
  }
  
  .role-stats {
    justify-content: center;
    text-align: center;
  }
  
  .category-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .permission-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .permission-actions {
    justify-content: center;
  }
}