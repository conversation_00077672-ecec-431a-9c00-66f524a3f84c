/* 🎛️ أنماط إعدادات الثيم المتقدمة */

/* 🌫️ خلفية المودال */
.theme-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

/* 📦 مودال الإعدادات */
.theme-settings-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: zoomIn 0.3s ease-out;
}

/* 🎯 رأس المودال */
.theme-settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

.theme-settings-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
  border-radius: var(--radius-full);
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: var(--danger-500);
  color: white;
  transform: scale(1.1);
}

/* 📑 التبويبات */
.theme-settings-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid var(--glass-border);
}

.tab {
  flex: 1;
  padding: var(--space-4) var(--space-3);
  border: none;
  background: transparent;
  color: var(--neutral-600);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.tab:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

.tab.active {
  background: rgba(59, 130, 246, 0.15);
  color: var(--primary-700);
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
}

/* 📄 محتوى الإعدادات */
.theme-settings-content {
  padding: var(--space-6);
  max-height: 60vh;
  overflow-y: auto;
}

.settings-section h3 {
  margin: 0 0 var(--space-6) 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-800);
}

/* 🎛️ مجموعة الإعدادات */
.setting-group {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
}

.setting-group label {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-700);
  margin-bottom: var(--space-3);
}

.setting-description {
  font-size: 0.875rem;
  color: var(--neutral-500);
  margin-top: var(--space-2);
  margin-bottom: 0;
}

/* 🔘 مجموعة الراديو */
.radio-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-3);
}

.radio-option {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: rgba(255, 255, 255, 0.02);
}

.radio-option:hover {
  border-color: var(--primary-300);
  background: rgba(59, 130, 246, 0.05);
}

.radio-option input[type="radio"] {
  margin-bottom: var(--space-2);
}

.radio-option input[type="radio"]:checked + .radio-label {
  color: var(--primary-600);
  font-weight: 600;
}

.radio-option:has(input:checked) {
  border-color: var(--primary-500);
  background: rgba(59, 130, 246, 0.1);
}

.radio-label {
  font-size: 0.75rem;
  text-align: center;
  margin-bottom: var(--space-2);
  color: var(--neutral-600);
}

/* 📐 معاينة نصف القطر */
.radius-preview {
  width: 30px;
  height: 20px;
  background: var(--gradient-primary);
  margin-top: var(--space-1);
}

.radius-preview.radius-none { border-radius: 0; }
.radius-preview.radius-small { border-radius: 4px; }
.radius-preview.radius-medium { border-radius: 8px; }
.radius-preview.radius-large { border-radius: 12px; }
.radius-preview.radius-xlarge { border-radius: 16px; }

/* 📝 معاينة الخط */
.font-preview {
  padding: var(--space-2);
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-md);
  text-align: center;
  margin-top: var(--space-1);
}

.font-preview.font-small { font-size: 14px; }
.font-preview.font-medium { font-size: 16px; }
.font-preview.font-large { font-size: 18px; }
.font-preview.font-xlarge { font-size: 20px; }

/* 🔄 مفتاح التبديل */
.toggle-setting {
  display: flex !important;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
}

.toggle-setting input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  width: 50px;
  height: 26px;
  background: var(--neutral-300);
  border-radius: 26px;
  position: relative;
  transition: all var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-setting input:checked + .toggle-slider {
  background: var(--primary-500);
}

.toggle-setting input:checked + .toggle-slider::before {
  transform: translateX(24px);
}

.toggle-label {
  font-size: 1rem;
  font-weight: 500;
  color: var(--neutral-700);
}

/* 🎨 معلومات الثيم الحالي */
.current-theme-info {
  margin-top: var(--space-6);
  padding: var(--space-5);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
}

.theme-card {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.theme-icon-large {
  font-size: 3rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.theme-details h5 {
  margin: 0 0 var(--space-1) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.theme-details p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--neutral-500);
}

.theme-colors-large {
  display: flex;
  gap: var(--space-1);
  margin-right: auto;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-md);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-sm);
  cursor: help;
}

/* 📝 معاينة الخطوط */
.font-preview-section {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
}

.font-samples h1 {
  margin: 0 0 var(--space-2) 0;
  font-size: 2rem;
}

.font-samples h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: 1.25rem;
}

.font-samples p {
  margin: 0 0 var(--space-2) 0;
  line-height: 1.6;
}

.font-samples small {
  font-size: 0.75rem;
  color: var(--neutral-500);
}

/* 🎬 معاينة الحركات */
.animations-preview {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
}

.animation-demos {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
  margin-top: var(--space-3);
}

.demo-card {
  padding: var(--space-3) var(--space-4);
  background: var(--gradient-primary);
  color: white;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
}

/* ♿ معلومات إمكانية الوصول */
.accessibility-info {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background: rgba(34, 197, 94, 0.1);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.accessibility-info ul {
  margin: var(--space-3) 0 0 0;
  padding: 0;
  list-style: none;
}

.accessibility-info li {
  padding: var(--space-1) 0;
  font-size: 0.875rem;
  color: var(--neutral-600);
}

/* 🔧 تذييل الإعدادات */
.theme-settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-top: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .theme-settings-modal {
  background: rgba(15, 23, 42, 0.95);
}

[data-theme="dark"] .settings-section h3 {
  color: var(--neutral-200);
}

[data-theme="dark"] .setting-group label {
  color: var(--neutral-300);
}

[data-theme="dark"] .toggle-label {
  color: var(--neutral-300);
}

[data-theme="dark"] .theme-details h5 {
  color: var(--neutral-200);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .theme-settings-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .theme-settings-tabs {
    flex-wrap: wrap;
  }
  
  .tab {
    flex: 1 1 50%;
    font-size: 0.75rem;
    padding: var(--space-3) var(--space-2);
  }
  
  .radio-group {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--space-2);
  }
  
  .theme-card {
    flex-direction: column;
    text-align: center;
  }
  
  .animation-demos {
    flex-direction: column;
    align-items: center;
  }
  
  .theme-settings-footer {
    flex-direction: column;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .theme-settings-content {
    padding: var(--space-4);
  }
  
  .theme-settings-header {
    padding: var(--space-4);
  }
  
  .theme-settings-footer {
    padding: var(--space-4);
  }
}
