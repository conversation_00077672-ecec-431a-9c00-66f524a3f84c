/* 📋 أنماط صفحات الحجوزات المحسنة */

/* 📦 حاوية الصفحة الرئيسية */
.booking-page {
  padding: var(--space-6);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  min-height: 100vh;
  transition: all var(--transition-normal);
}

/* 🎯 رأس الصفحة */
.booking-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.booking-page-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.booking-page-title h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 800;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.booking-page-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.booking-page-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

/* 🔍 شريط البحث والفلاتر */
.booking-filters {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-5);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.search-input {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 1rem;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.search-input::placeholder {
  color: var(--neutral-400);
}

.filter-select {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 📊 إحصائيات سريعة */
.booking-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.stat-card {
  padding: var(--space-5);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0.8;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: var(--space-2);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--neutral-600);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 📋 جدول الحجوزات */
.bookings-table-container {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-bottom: var(--space-6);
}

.bookings-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.bookings-table th {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  padding: var(--space-4) var(--space-5);
  text-align: right;
  font-weight: 600;
  color: var(--neutral-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid var(--glass-border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.bookings-table td {
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  color: var(--neutral-600);
  font-size: 0.875rem;
  vertical-align: middle;
}

.bookings-table tbody tr {
  transition: all var(--transition-fast);
}

.bookings-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.bookings-table tbody tr:last-child td {
  border-bottom: none;
}

/* 🏷️ شارات الحالة */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all var(--transition-fast);
}

.status-badge.confirmed {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
  color: white;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.status-badge.pending {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.status-badge.cancelled {
  background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.status-badge.completed {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
  color: white;
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.3);
}

/* 🎬 أزرار الإجراءات */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.action-btn:hover::before {
  opacity: 0.1;
}

.action-btn.view {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

.action-btn.edit {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-600);
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
}

.action-btn.print {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 1024px) {
  .booking-filters {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
  }
  
  .booking-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .booking-page {
    padding: var(--space-4);
  }
  
  .booking-page-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .booking-filters {
    grid-template-columns: 1fr;
  }
  
  .booking-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .bookings-table-container {
    overflow-x: auto;
  }
  
  .bookings-table {
    min-width: 800px;
  }
}

@media (max-width: 480px) {
  .booking-page-title h1 {
    font-size: 1.5rem;
  }
  
  .booking-stats {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: var(--space-1);
  }
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .booking-page,
.dark-mode .booking-page {
  background: var(--gradient-cosmic);
}

[data-theme="dark"] .search-input,
[data-theme="dark"] .filter-select,
.dark-mode .search-input,
.dark-mode .filter-select {
  background: var(--glass-bg);
  color: var(--neutral-200);
  border-color: var(--glass-border);
}

[data-theme="dark"] .search-input::placeholder,
.dark-mode .search-input::placeholder {
  color: var(--neutral-500);
}

[data-theme="dark"] .bookings-table th,
.dark-mode .bookings-table th {
  color: var(--neutral-300);
}

[data-theme="dark"] .bookings-table td,
.dark-mode .bookings-table td {
  color: var(--neutral-400);
}

[data-theme="dark"] .stat-value,
.dark-mode .stat-value {
  color: var(--neutral-200);
}

[data-theme="dark"] .stat-label,
.dark-mode .stat-label {
  color: var(--neutral-400);
}

/* 🎬 حركات خاصة */
.booking-page {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .booking-page,
  .stat-card,
  .bookings-table tbody tr,
  .action-btn {
    animation: none;
    transition: none;
  }
  
  .stat-card:hover,
  .action-btn:hover,
  .bookings-table tbody tr:hover {
    transform: none;
  }
}
