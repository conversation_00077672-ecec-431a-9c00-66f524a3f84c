import React, { useState, useEffect, useRef, useMemo } from 'react';
import './AuditTrail.css';

const AuditTrailAdvanced = ({ transactions, accounts, currentUser }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedAction, setSelectedAction] = useState('');
  const [selectedAccount, setSelectedAccount] = useState('');
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [viewMode, setViewMode] = useState('table');
  const [sortConfig, setSortConfig] = useState({ key: 'createdAt', direction: 'desc' });
  const [printOptions, setPrintOptions] = useState({
    includeDetails: true,
    includeUserInfo: true,
    includeTimestamps: true,
    includeChanges: false,
    pageSize: 'A4',
    orientation: 'landscape'
  });
  const [exportFormat, setExportFormat] = useState('csv');
  const [advancedFilters, setAdvancedFilters] = useState({
    amountRange: { min: '', max: '' },
    transactionTypes: [],
    hasChanges: false,
    riskLevel: '',
    timeRange: 'all'
  });

  const printRef = useRef();

  // أنواع الإجراءات
  const actionTypes = {
    create: { name: 'إنشاء', icon: '➕', color: '#27ae60' },
    update: { name: 'تعديل', icon: '✏️', color: '#f39c12' },
    delete: { name: 'حذف', icon: '🗑️', color: '#e74c3c' },
    view: { name: 'عرض', icon: '👁️', color: '#3498db' },
    print: { name: 'طباعة', icon: '🖨️', color: '#9b59b6' },
    export: { name: 'تصدير', icon: '📊', color: '#1abc9c' },
    approve: { name: 'موافقة', icon: '✅', color: '#2ecc71' },
    reject: { name: 'رفض', icon: '❌', color: '#e74c3c' }
  };

  // مستويات المخاطر
  const riskLevels = {
    low: { name: 'منخفض', color: '#27ae60', icon: '🟢' },
    medium: { name: 'متوسط', color: '#f39c12', icon: '🟡' },
    high: { name: 'عالي', color: '#e74c3c', icon: '🔴' },
    critical: { name: 'حرج', color: '#8e44ad', icon: '🚨' }
  };

  // إنشاء سجل التدقيق من المعاملات
  const auditLog = useMemo(() => {
    return transactions.map(transaction => ({
      id: `AUDIT-${transaction.id}`,
      transactionId: transaction.id,
      action: transaction.action || 'create',
      user: transaction.user || 'غير محدد',
      timestamp: transaction.createdAt || transaction.date,
      description: transaction.description,
      amount: transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0,
      accountsAffected: transaction.entries?.map(entry => entry.accountName).join(', ') || '',
      ipAddress: transaction.ipAddress || '*************',
      userAgent: transaction.userAgent || 'Unknown Browser',
      changes: transaction.changes || [],
      riskLevel: calculateRiskLevel(transaction),
      category: transaction.type || 'general',
      reference: transaction.reference || '',
      notes: transaction.notes || '',
      status: transaction.status || 'completed'
    }));
  }, [transactions]);

  // حساب مستوى المخاطر
  function calculateRiskLevel(transaction) {
    const amount = transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0;
    
    if (amount > 100000) return 'critical';
    if (amount > 50000) return 'high';
    if (amount > 10000) return 'medium';
    return 'low';
  }

  // تصفية سجل التدقيق
  const filteredAuditLog = useMemo(() => {
    return auditLog.filter(entry => {
      const matchesSearch = 
        entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.accountsAffected.toLowerCase().includes(searchTerm.toLowerCase());

      const entryDate = new Date(entry.timestamp);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      const matchesDate = entryDate >= startDate && entryDate <= endDate;

      const matchesUser = !selectedUser || entry.user === selectedUser;
      const matchesAction = !selectedAction || entry.action === selectedAction;
      const matchesAccount = !selectedAccount || entry.accountsAffected.includes(selectedAccount);

      // التصفية المتقدمة
      const matchesAmount = (!advancedFilters.amountRange.min || entry.amount >= parseFloat(advancedFilters.amountRange.min)) &&
                           (!advancedFilters.amountRange.max || entry.amount <= parseFloat(advancedFilters.amountRange.max));

      const matchesType = advancedFilters.transactionTypes.length === 0 || 
                         advancedFilters.transactionTypes.includes(entry.category);

      const matchesChanges = !advancedFilters.hasChanges || 
                            (entry.changes && entry.changes.length > 0);

      const matchesRisk = !advancedFilters.riskLevel || entry.riskLevel === advancedFilters.riskLevel;

      return matchesSearch && matchesDate && matchesUser && matchesAction && 
             matchesAccount && matchesAmount && matchesType && matchesChanges && matchesRisk;
    }).sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (sortConfig.key === 'timestamp') {
        return sortConfig.direction === 'asc' 
          ? new Date(aValue) - new Date(bValue)
          : new Date(bValue) - new Date(aValue);
      }
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [auditLog, searchTerm, dateRange, selectedUser, selectedAction, selectedAccount, advancedFilters, sortConfig]);

  // حساب الإحصائيات
  const auditStats = useMemo(() => {
    const totalEntries = filteredAuditLog.length;
    const uniqueUsers = [...new Set(filteredAuditLog.map(entry => entry.user))].length;
    const totalAmount = filteredAuditLog.reduce((sum, entry) => sum + entry.amount, 0);
    const riskDistribution = {
      low: filteredAuditLog.filter(entry => entry.riskLevel === 'low').length,
      medium: filteredAuditLog.filter(entry => entry.riskLevel === 'medium').length,
      high: filteredAuditLog.filter(entry => entry.riskLevel === 'high').length,
      critical: filteredAuditLog.filter(entry => entry.riskLevel === 'critical').length
    };

    return {
      totalEntries,
      uniqueUsers,
      totalAmount,
      riskDistribution,
      averageAmount: totalEntries > 0 ? totalAmount / totalEntries : 0
    };
  }, [filteredAuditLog]);

  // الحصول على قائمة المستخدمين
  const users = useMemo(() => {
    return [...new Set(auditLog.map(entry => entry.user))].filter(Boolean);
  }, [auditLog]);

  // وظائف الطباعة
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير مسار التدقيق</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .stats { display: flex; justify-content: space-around; margin: 20px 0; padding: 15px; background: #f5f5f5; }
          .stat-item { text-align: center; }
          .stat-value { font-size: 18px; font-weight: bold; color: #333; }
          .stat-label { font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 12px; }
          th, td { border: 1px solid #ddd; padding: 6px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .risk-low { color: #27ae60; }
          .risk-medium { color: #f39c12; }
          .risk-high { color: #e74c3c; }
          .risk-critical { color: #8e44ad; font-weight: bold; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    return `
      <div class="header">
        <div class="company-name">شركة شراء السياحية</div>
        <div class="report-title">تقرير مسار التدقيق</div>
        <div class="date-range">
          من ${new Date(dateRange.startDate).toLocaleDateString('ar-SA')} 
          إلى ${new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
        </div>
      </div>
      
      ${printOptions.includeUserInfo ? `
        <div class="stats">
          <div class="stat-item">
            <div class="stat-value">${auditStats.totalEntries}</div>
            <div class="stat-label">إجمالي العمليات</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${auditStats.uniqueUsers}</div>
            <div class="stat-label">عدد المستخدمين</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${auditStats.totalAmount.toLocaleString()}</div>
            <div class="stat-label">إجمالي المبلغ</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${auditStats.riskDistribution.critical + auditStats.riskDistribution.high}</div>
            <div class="stat-label">عمليات عالية المخاطر</div>
          </div>
        </div>
      ` : ''}
      
      <table>
        <thead>
          <tr>
            ${printOptions.includeTimestamps ? '<th>التاريخ والوقت</th>' : ''}
            <th>رقم المعاملة</th>
            <th>الإجراء</th>
            <th>المستخدم</th>
            <th>الوصف</th>
            <th>المبلغ</th>
            <th>الحسابات المتأثرة</th>
            <th>مستوى المخاطر</th>
          </tr>
        </thead>
        <tbody>
          ${filteredAuditLog.map(entry => `
            <tr>
              ${printOptions.includeTimestamps ? `<td>${new Date(entry.timestamp).toLocaleString('ar-SA')}</td>` : ''}
              <td>${entry.transactionId}</td>
              <td>${actionTypes[entry.action]?.name || entry.action}</td>
              <td>${entry.user}</td>
              <td>${entry.description}</td>
              <td>${entry.amount.toLocaleString()}</td>
              <td>${entry.accountsAffected}</td>
              <td class="risk-${entry.riskLevel}">${riskLevels[entry.riskLevel]?.name || entry.riskLevel}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div class="footer">
        <p>تم إنشاء التقرير في: ${new Date().toLocaleString('ar-SA')}</p>
        <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
        <p>عدد السجلات: ${filteredAuditLog.length}</p>
      </div>
    `;
  };

  // وظائف التصدير
  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    const headers = ['التاريخ والوقت', 'رقم المعاملة', 'الإجراء', 'المستخدم', 'الوصف', 'المبلغ', 'الحسابات المتأثرة', 'مستوى المخاطر', 'عنوان IP'];
    const rows = filteredAuditLog.map(entry => [
      new Date(entry.timestamp).toLocaleString('ar-SA'),
      entry.transactionId,
      actionTypes[entry.action]?.name || entry.action,
      entry.user,
      entry.description,
      entry.amount,
      entry.accountsAffected,
      riskLevels[entry.riskLevel]?.name || entry.riskLevel,
      entry.ipAddress
    ]);

    const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `مسار-التدقيق-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  // وظائف التصفية
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedUser('');
    setSelectedAction('');
    setSelectedAccount('');
    setAdvancedFilters({
      amountRange: { min: '', max: '' },
      transactionTypes: [],
      hasChanges: false,
      riskLevel: '',
      timeRange: 'all'
    });
    setDateRange({
      startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    });
  };

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  return (
    <div className="audit-trail-advanced">
      <div className="audit-header">
        <div className="header-content">
          <h2>🔍 مسار التدقيق المتقدم</h2>
          <p>مراقبة وتتبع جميع العمليات المحاسبية والمستخدمين</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-success"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowFilterModal(true)}
          >
            🔍 تصفية متقدمة
          </button>
          <button 
            className="btn btn-secondary"
            onClick={resetFilters}
          >
            🔄 إعادة تعيين
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="audit-stats">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <div className="stat-value">{auditStats.totalEntries}</div>
            <div className="stat-label">إجمالي العمليات</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-info">
            <div className="stat-value">{auditStats.uniqueUsers}</div>
            <div className="stat-label">عدد المستخدمين</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{auditStats.totalAmount.toLocaleString()}</div>
            <div className="stat-label">إجمالي المبلغ</div>
          </div>
        </div>
        
        <div className="stat-card risk-card">
          <div className="stat-icon">🚨</div>
          <div className="stat-info">
            <div className="stat-value">
              {auditStats.riskDistribution.critical + auditStats.riskDistribution.high}
            </div>
            <div className="stat-label">عمليات عالية المخاطر</div>
          </div>
        </div>
      </div>

      {/* توزيع المخاطر */}
      <div className="risk-distribution">
        <h3>توزيع مستويات المخاطر</h3>
        <div className="risk-bars">
          {Object.entries(auditStats.riskDistribution).map(([level, count]) => (
            <div key={level} className="risk-bar">
              <div className="risk-info">
                <span className="risk-icon" style={{ color: riskLevels[level]?.color }}>
                  {riskLevels[level]?.icon}
                </span>
                <span className="risk-name">{riskLevels[level]?.name}</span>
                <span className="risk-count">{count}</span>
              </div>
              <div className="risk-progress">
                <div 
                  className="risk-fill" 
                  style={{ 
                    width: `${auditStats.totalEntries > 0 ? (count / auditStats.totalEntries) * 100 : 0}%`,
                    backgroundColor: riskLevels[level]?.color 
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* أدوات التصفية */}
      <div className="audit-filters">
        <div className="filter-row">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث في سجل التدقيق..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="date-range">
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
        </div>
        
        <div className="filter-row">
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
            className="filter-select"
          >
            <option value="">جميع المستخدمين</option>
            {users.map(user => (
              <option key={user} value={user}>{user}</option>
            ))}
          </select>
          
          <select
            value={selectedAction}
            onChange={(e) => setSelectedAction(e.target.value)}
            className="filter-select"
          >
            <option value="">جميع الإجراءات</option>
            {Object.entries(actionTypes).map(([key, action]) => (
              <option key={key} value={key}>{action.name}</option>
            ))}
          </select>
          
          <select
            value={selectedAccount}
            onChange={(e) => setSelectedAccount(e.target.value)}
            className="filter-select"
          >
            <option value="">جميع الحسابات</option>
            {accounts.map(account => (
              <option key={account.id} value={account.name}>{account.name}</option>
            ))}
          </select>
          
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => setViewMode('table')}
            >
              📋 جدول
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'cards' ? 'active' : ''}`}
              onClick={() => setViewMode('cards')}
            >
              🗃️ بطاقات
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'timeline' ? 'active' : ''}`}
              onClick={() => setViewMode('timeline')}
            >
              📅 خط زمني
            </button>
          </div>
        </div>
      </div>

      {/* عرض البيانات */}
      <div className="audit-content">
        {viewMode === 'table' && (
          <div className="table-view">
            {filteredAuditLog.length === 0 ? (
              <div className="no-data">
                <span className="icon">🔍</span>
                <h3>لا توجد سجلات</h3>
                <p>لا توجد سجلات تدقيق تطابق المعايير المحددة</p>
              </div>
            ) : (
              <div className="table-container">
                <table className="audit-table">
                  <thead>
                    <tr>
                      <th onClick={() => handleSort('timestamp')}>
                        التاريخ والوقت {sortConfig.key === 'timestamp' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('transactionId')}>
                        رقم المعاملة {sortConfig.key === 'transactionId' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th>الإجراء</th>
                      <th onClick={() => handleSort('user')}>
                        المستخدم {sortConfig.key === 'user' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th>الوصف</th>
                      <th onClick={() => handleSort('amount')}>
                        المبلغ {sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th>مستوى المخاطر</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAuditLog.map(entry => (
                      <tr key={entry.id} className={`audit-row risk-${entry.riskLevel}`}>
                        <td className="timestamp">
                          {new Date(entry.timestamp).toLocaleString('ar-SA')}
                        </td>
                        <td className="transaction-id">{entry.transactionId}</td>
                        <td className="action">
                          <span 
                            className="action-badge"
                            style={{ color: actionTypes[entry.action]?.color }}
                          >
                            {actionTypes[entry.action]?.icon} {actionTypes[entry.action]?.name || entry.action}
                          </span>
                        </td>
                        <td className="user">{entry.user}</td>
                        <td className="description">{entry.description}</td>
                        <td className="amount">{entry.amount.toLocaleString()}</td>
                        <td className="risk-level">
                          <span 
                            className={`risk-badge risk-${entry.riskLevel}`}
                            style={{ color: riskLevels[entry.riskLevel]?.color }}
                          >
                            {riskLevels[entry.riskLevel]?.icon} {riskLevels[entry.riskLevel]?.name}
                          </span>
                        </td>
                        <td className="actions">
                          <button
                            className="action-btn view-btn"
                            onClick={() => {
                              setSelectedTransaction(entry);
                              setShowDetailsModal(true);
                            }}
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          <button
                            className="action-btn print-btn"
                            title="طباعة"
                          >
                            🖨️
                          </button>
                          <button
                            className="action-btn export-btn"
                            title="تصدير"
                          >
                            📊
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {viewMode === 'cards' && (
          <div className="cards-view">
            {filteredAuditLog.map(entry => (
              <div key={entry.id} className={`audit-card risk-${entry.riskLevel}`}>
                <div className="card-header">
                  <div className="card-title">
                    <span className="transaction-id">{entry.transactionId}</span>
                    <span className="timestamp">
                      {new Date(entry.timestamp).toLocaleString('ar-SA')}
                    </span>
                  </div>
                  <div className="card-badges">
                    <span 
                      className="action-badge"
                      style={{ color: actionTypes[entry.action]?.color }}
                    >
                      {actionTypes[entry.action]?.icon}
                    </span>
                    <span 
                      className={`risk-badge risk-${entry.riskLevel}`}
                      style={{ color: riskLevels[entry.riskLevel]?.color }}
                    >
                      {riskLevels[entry.riskLevel]?.icon}
                    </span>
                  </div>
                </div>
                
                <div className="card-content">
                  <div className="description">{entry.description}</div>
                  <div className="user">👤 {entry.user}</div>
                  <div className="amount">💰 {entry.amount.toLocaleString()} ريال</div>
                  <div className="accounts">🏦 {entry.accountsAffected}</div>
                </div>
                
                <div className="card-actions">
                  <button
                    className="action-btn view-btn"
                    onClick={() => {
                      setSelectedTransaction(entry);
                      setShowDetailsModal(true);
                    }}
                  >
                    👁️ عرض
                  </button>
                  <button className="action-btn print-btn">🖨️ طباعة</button>
                  <button className="action-btn export-btn">📊 تصدير</button>
                </div>
              </div>
            ))}
          </div>
        )}

        {viewMode === 'timeline' && (
          <div className="timeline-view">
            {filteredAuditLog.map((entry, index) => (
              <div key={entry.id} className="timeline-item">
                <div className="timeline-marker" style={{ backgroundColor: riskLevels[entry.riskLevel]?.color }}></div>
                <div className="timeline-content">
                  <div className="timeline-header">
                    <span className="timeline-time">
                      {new Date(entry.timestamp).toLocaleString('ar-SA')}
                    </span>
                    <span className="timeline-user">{entry.user}</span>
                  </div>
                  <div className="timeline-action">
                    <span style={{ color: actionTypes[entry.action]?.color }}>
                      {actionTypes[entry.action]?.icon} {actionTypes[entry.action]?.name}
                    </span>
                  </div>
                  <div className="timeline-description">{entry.description}</div>
                  <div className="timeline-details">
                    <span>المعاملة: {entry.transactionId}</span>
                    <span>المبلغ: {entry.amount.toLocaleString()}</span>
                    <span className={`risk-${entry.riskLevel}`}>
                      {riskLevels[entry.riskLevel]?.icon} {riskLevels[entry.riskLevel]?.name}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* نموذج تفاصيل السجل */}
      {showDetailsModal && selectedTransaction && (
        <div className="modal-overlay">
          <div className="details-modal">
            <div className="modal-header">
              <h3>🔍 تفاصيل سجل التدقيق</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="audit-details">
                <div className="details-grid">
                  <div className="detail-item">
                    <label>رقم المعاملة:</label>
                    <span>{selectedTransaction.transactionId}</span>
                  </div>
                  <div className="detail-item">
                    <label>الإجراء:</label>
                    <span style={{ color: actionTypes[selectedTransaction.action]?.color }}>
                      {actionTypes[selectedTransaction.action]?.icon} {actionTypes[selectedTransaction.action]?.name}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>المستخدم:</label>
                    <span>{selectedTransaction.user}</span>
                  </div>
                  <div className="detail-item">
                    <label>التاريخ والوقت:</label>
                    <span>{new Date(selectedTransaction.timestamp).toLocaleString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>المبلغ:</label>
                    <span>{selectedTransaction.amount.toLocaleString()} ريال</span>
                  </div>
                  <div className="detail-item">
                    <label>مستوى المخاطر:</label>
                    <span 
                      className={`risk-badge risk-${selectedTransaction.riskLevel}`}
                      style={{ color: riskLevels[selectedTransaction.riskLevel]?.color }}
                    >
                      {riskLevels[selectedTransaction.riskLevel]?.icon} {riskLevels[selectedTransaction.riskLevel]?.name}
                    </span>
                  </div>
                </div>

                <div className="description-section">
                  <label>الوصف:</label>
                  <p>{selectedTransaction.description}</p>
                </div>

                <div className="accounts-section">
                  <label>الحسابات المتأثرة:</label>
                  <p>{selectedTransaction.accountsAffected}</p>
                </div>

                <div className="technical-info">
                  <h4>المعلومات التقنية:</h4>
                  <div className="tech-grid">
                    <div className="tech-item">
                      <label>عنوان IP:</label>
                      <span>{selectedTransaction.ipAddress}</span>
                    </div>
                    <div className="tech-item">
                      <label>المتصفح:</label>
                      <span>{selectedTransaction.userAgent}</span>
                    </div>
                    <div className="tech-item">
                      <label>الحالة:</label>
                      <span>{selectedTransaction.status}</span>
                    </div>
                  </div>
                </div>

                {selectedTransaction.changes && selectedTransaction.changes.length > 0 && (
                  <div className="changes-section">
                    <h4>التغييرات:</h4>
                    <div className="changes-list">
                      {selectedTransaction.changes.map((change, index) => (
                        <div key={index} className="change-item">
                          <span className="field">{change.field}:</span>
                          <span className="old-value">من: {change.oldValue}</span>
                          <span className="new-value">إلى: {change.newValue}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDetailsModal(false)}
              >
                إغلاق
              </button>
              <button className="btn btn-info">
                🖨️ طباعة
              </button>
              <button className="btn btn-success">
                📊 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeDetails}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                    />
                    تضمين تفاصيل المعاملات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeUserInfo}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeUserInfo: e.target.checked }))}
                    />
                    تضمين معلومات المستخدمين
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeTimestamps}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeTimestamps: e.target.checked }))}
                    />
                    تضمين التواريخ والأوقات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeChanges}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeChanges: e.target.checked }))}
                    />
                    تضمين سجل التغييرات
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">عدد السجلات:</span>
                      <span className="value">{filteredAuditLog.length}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">عدد المستخدمين:</span>
                      <span className="value">{auditStats.uniqueUsers}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">الفترة:</span>
                      <span className="value">
                        {new Date(dateRange.startDate).toLocaleDateString('ar-SA')} - 
                        {new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصفية المتقدمة */}
      {showFilterModal && (
        <div className="modal-overlay">
          <div className="filter-modal">
            <div className="modal-header">
              <h3>🔍 تصفية متقدمة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowFilterModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="filter-form">
                <div className="filter-group">
                  <label>نطاق المبلغ:</label>
                  <div className="range-inputs">
                    <input
                      type="number"
                      placeholder="من"
                      value={advancedFilters.amountRange.min}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        amountRange: { ...prev.amountRange, min: e.target.value }
                      }))}
                      className="form-control"
                    />
                    <span>إلى</span>
                    <input
                      type="number"
                      placeholder="إلى"
                      value={advancedFilters.amountRange.max}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        amountRange: { ...prev.amountRange, max: e.target.value }
                      }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="filter-group">
                  <label>مستوى المخاطر:</label>
                  <select
                    value={advancedFilters.riskLevel}
                    onChange={(e) => setAdvancedFilters(prev => ({
                      ...prev,
                      riskLevel: e.target.value
                    }))}
                    className="form-control"
                  >
                    <option value="">جميع المستويات</option>
                    {Object.entries(riskLevels).map(([key, level]) => (
                      <option key={key} value={key}>
                        {level.icon} {level.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="filter-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={advancedFilters.hasChanges}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        hasChanges: e.target.checked
                      }))}
                    />
                    العمليات التي تحتوي على تغييرات فقط
                  </label>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowFilterModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-warning"
                onClick={resetFilters}
              >
                🔄 إعادة تعيين
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => setShowFilterModal(false)}
              >
                ✅ تطبيق المرشحات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuditTrailAdvanced;