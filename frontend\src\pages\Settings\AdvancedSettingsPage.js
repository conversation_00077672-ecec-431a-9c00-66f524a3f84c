import React, { useState } from 'react';
import { 
  BackupManager, 
  UserManagement, 
  EmailSettings, 
  SystemStats,
  ConnectionTest 
} from '../../components/Settings/SettingsComponents';
import './SettingsPage.css';

const AdvancedSettingsPage = () => {
  const [activeTab, setActiveTab] = useState('system');
  const [settings, setSettings] = useState({
    email: {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      fromName: 'شركة شراء السفر',
      fromEmail: '',
      encryption: 'tls'
    },
    system: {
      maintenanceMode: false,
      debugMode: false,
      logLevel: 'info',
      maxFileSize: 10,
      sessionTimeout: 30,
      autoBackup: true,
      backupFrequency: 'daily'
    },
    performance: {
      cacheEnabled: true,
      compressionEnabled: true,
      minifyAssets: true,
      lazyLoading: true,
      maxConnections: 100,
      queryTimeout: 30
    },
    monitoring: {
      errorTracking: true,
      performanceMonitoring: true,
      userAnalytics: false,
      systemAlerts: true,
      emailAlerts: true,
      smsAlerts: false
    }
  });

  const updateEmailSettings = (emailConfig) => {
    setSettings(prev => ({
      ...prev,
      email: emailConfig
    }));
  };

  const updateSystemSetting = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const renderSystemTab = () => (
    <div className="settings-section">
      <h3>إعدادات النظام المتقدمة</h3>
      
      <div className="advanced-settings-grid">
        <div className="settings-group">
          <h4>وضع النظام</h4>
          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.system.maintenanceMode}
                onChange={(e) => updateSystemSetting('system', 'maintenanceMode', e.target.checked)}
              />
              وضع الصيانة
            </label>
            <p className="setting-description">
              تفعيل وضع الصيانة يمنع المستخدمين من الوصول للنظام
            </p>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.system.debugMode}
                onChange={(e) => updateSystemSetting('system', 'debugMode', e.target.checked)}
              />
              وضع التطوير
            </label>
            <p className="setting-description">
              يعرض معلومات تفصيلية للأخطاء والتطوير
            </p>
          </div>
        </div>

        <div className="settings-group">
          <h4>إعدادات السجلات</h4>
          <div className="setting-item">
            <label>مستوى السجلات</label>
            <select
              value={settings.system.logLevel}
              onChange={(e) => updateSystemSetting('system', 'logLevel', e.target.value)}
            >
              <option value="error">أخطاء فقط</option>
              <option value="warning">تحذيرات وأخطاء</option>
              <option value="info">معلومات عامة</option>
              <option value="debug">تفاصيل كاملة</option>
            </select>
          </div>

          <div className="setting-item">
            <label>الحد الأقصى لحجم الملف (MB)</label>
            <input
              type="number"
              min="1"
              max="100"
              value={settings.system.maxFileSize}
              onChange={(e) => updateSystemSetting('system', 'maxFileSize', parseInt(e.target.value))}
            />
          </div>
        </div>

        <div className="settings-group">
          <h4>النسخ الاحتياطي</h4>
          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.system.autoBackup}
                onChange={(e) => updateSystemSetting('system', 'autoBackup', e.target.checked)}
              />
              النسخ الاحتياطي التلقائي
            </label>
          </div>

          {settings.system.autoBackup && (
            <div className="setting-item">
              <label>تكرار النسخ الاحتياطي</label>
              <select
                value={settings.system.backupFrequency}
                onChange={(e) => updateSystemSetting('system', 'backupFrequency', e.target.value)}
              >
                <option value="hourly">كل ساعة</option>
                <option value="daily">يومياً</option>
                <option value="weekly">أسبوعياً</option>
                <option value="monthly">شهرياً</option>
              </select>
            </div>
          )}
        </div>
      </div>

      <SystemStats />
    </div>
  );

  const renderPerformanceTab = () => (
    <div className="settings-section">
      <h3>إعدادات الأداء</h3>
      
      <div className="advanced-settings-grid">
        <div className="settings-group">
          <h4>تحسين الأداء</h4>
          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.performance.cacheEnabled}
                onChange={(e) => updateSystemSetting('performance', 'cacheEnabled', e.target.checked)}
              />
              تفعيل التخزين المؤقت
            </label>
            <p className="setting-description">
              يحسن سرعة تحميل الصفحات والبيانات
            </p>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.performance.compressionEnabled}
                onChange={(e) => updateSystemSetting('performance', 'compressionEnabled', e.target.checked)}
              />
              ضغط البيانات
            </label>
            <p className="setting-description">
              يقلل حجم البيانات المنقولة
            </p>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.performance.minifyAssets}
                onChange={(e) => updateSystemSetting('performance', 'minifyAssets', e.target.checked)}
              />
              ضغط ملفات CSS/JS
            </label>
            <p className="setting-description">
              يقلل حجم ملفات التصميم والبرمجة
            </p>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.performance.lazyLoading}
                onChange={(e) => updateSystemSetting('performance', 'lazyLoading', e.target.checked)}
              />
              التحميل التدريجي
            </label>
            <p className="setting-description">
              تحميل المحتوى عند الحاجة فقط
            </p>
          </div>
        </div>

        <div className="settings-group">
          <h4>إعدادات الاتصال</h4>
          <div className="setting-item">
            <label>الحد الأقصى للاتصالات المتزامنة</label>
            <input
              type="number"
              min="10"
              max="1000"
              value={settings.performance.maxConnections}
              onChange={(e) => updateSystemSetting('performance', 'maxConnections', parseInt(e.target.value))}
            />
          </div>

          <div className="setting-item">
            <label>مهلة انتهاء الاستعلام (ثانية)</label>
            <input
              type="number"
              min="5"
              max="300"
              value={settings.performance.queryTimeout}
              onChange={(e) => updateSystemSetting('performance', 'queryTimeout', parseInt(e.target.value))}
            />
          </div>
        </div>

        <div className="settings-group">
          <h4>مراقبة الأداء</h4>
          <div className="performance-metrics">
            <div className="metric-item">
              <div className="metric-label">استخدام المعالج</div>
              <div className="metric-bar">
                <div className="metric-fill" style={{ width: '45%' }}></div>
              </div>
              <div className="metric-value">45%</div>
            </div>

            <div className="metric-item">
              <div className="metric-label">استخدام الذاكرة</div>
              <div className="metric-bar">
                <div className="metric-fill" style={{ width: '62%' }}></div>
              </div>
              <div className="metric-value">62%</div>
            </div>

            <div className="metric-item">
              <div className="metric-label">استخدام القرص الصلب</div>
              <div className="metric-bar">
                <div className="metric-fill" style={{ width: '28%' }}></div>
              </div>
              <div className="metric-value">28%</div>
            </div>

            <div className="metric-item">
              <div className="metric-label">سرعة الشبكة</div>
              <div className="metric-bar">
                <div className="metric-fill" style={{ width: '78%' }}></div>
              </div>
              <div className="metric-value">78 Mbps</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMonitoringTab = () => (
    <div className="settings-section">
      <h3>المراقبة والتنبيهات</h3>
      
      <div className="advanced-settings-grid">
        <div className="settings-group">
          <h4>مراقبة النظام</h4>
          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.monitoring.errorTracking}
                onChange={(e) => updateSystemSetting('monitoring', 'errorTracking', e.target.checked)}
              />
              تتبع الأخطاء
            </label>
            <p className="setting-description">
              مراقبة وتسجيل جميع أخطاء النظام
            </p>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.monitoring.performanceMonitoring}
                onChange={(e) => updateSystemSetting('monitoring', 'performanceMonitoring', e.target.checked)}
              />
              مراقبة الأداء
            </label>
            <p className="setting-description">
              تتبع أداء النظام والاستجابة
            </p>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.monitoring.userAnalytics}
                onChange={(e) => updateSystemSetting('monitoring', 'userAnalytics', e.target.checked)}
              />
              تحليلات المستخدمين
            </label>
            <p className="setting-description">
              جمع بيانات استخدام المستخدمين
            </p>
          </div>
        </div>

        <div className="settings-group">
          <h4>التنبيهات</h4>
          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.monitoring.systemAlerts}
                onChange={(e) => updateSystemSetting('monitoring', 'systemAlerts', e.target.checked)}
              />
              تنبيهات النظام
            </label>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.monitoring.emailAlerts}
                onChange={(e) => updateSystemSetting('monitoring', 'emailAlerts', e.target.checked)}
              />
              تنبيهات البريد الإلكتروني
            </label>
          </div>

          <div className="setting-item checkbox-item">
            <label>
              <input
                type="checkbox"
                checked={settings.monitoring.smsAlerts}
                onChange={(e) => updateSystemSetting('monitoring', 'smsAlerts', e.target.checked)}
              />
              تنبيهات الرسائل النصية
            </label>
          </div>
        </div>

        <div className="settings-group">
          <h4>سجل الأحداث الأخيرة</h4>
          <div className="event-log">
            <div className="event-item success">
              <div className="event-time">2024-01-15 14:30:25</div>
              <div className="event-message">تم إنشاء نسخة احتياطية بنجاح</div>
            </div>
            <div className="event-item warning">
              <div className="event-time">2024-01-15 14:15:10</div>
              <div className="event-message">استخدام الذاكرة وصل إلى 80%</div>
            </div>
            <div className="event-item info">
              <div className="event-time">2024-01-15 14:00:00</div>
              <div className="event-message">تم تسجيل دخول مستخدم جديد</div>
            </div>
            <div className="event-item error">
              <div className="event-time">2024-01-15 13:45:33</div>
              <div className="event-message">فشل في الاتصال بخدمة الدفع</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUsersTab = () => (
    <div className="settings-section">
      <h3>إدارة المستخدمين والصلاحيات</h3>
      <UserManagement />
    </div>
  );

  const renderBackupTab = () => (
    <div className="settings-section">
      <h3>النسخ الاحتياطي والاستعادة</h3>
      <BackupManager />
    </div>
  );

  const renderEmailTab = () => (
    <div className="settings-section">
      <h3>إعدادات البريد الإلكتروني</h3>
      <EmailSettings 
        settings={settings.email}
        onUpdate={updateEmailSettings}
      />
    </div>
  );

  return (
    <div className="settings-page advanced">
      <div className="page-header">
        <div className="header-content">
          <h1>الإعدادات المتقدمة</h1>
          <p>إدارة متقدمة لإعدادات النظام والأداء والمراقبة</p>
        </div>
      </div>

      <div className="settings-container">
        <div className="settings-sidebar">
          <div className="settings-tabs">
            <button 
              className={`tab-btn ${activeTab === 'system' ? 'active' : ''}`}
              onClick={() => setActiveTab('system')}
            >
              <span className="tab-icon">🖥️</span>
              إعدادات النظام
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'performance' ? 'active' : ''}`}
              onClick={() => setActiveTab('performance')}
            >
              <span className="tab-icon">⚡</span>
              الأداء والتحسين
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'monitoring' ? 'active' : ''}`}
              onClick={() => setActiveTab('monitoring')}
            >
              <span className="tab-icon">📊</span>
              المراقبة والتنبيهات
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              <span className="tab-icon">👥</span>
              إدارة المستخدمين
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'backup' ? 'active' : ''}`}
              onClick={() => setActiveTab('backup')}
            >
              <span className="tab-icon">💾</span>
              النسخ الاحتياطي
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'email' ? 'active' : ''}`}
              onClick={() => setActiveTab('email')}
            >
              <span className="tab-icon">📧</span>
              إعدادات البريد
            </button>
          </div>
        </div>

        <div className="settings-content">
          {activeTab === 'system' && renderSystemTab()}
          {activeTab === 'performance' && renderPerformanceTab()}
          {activeTab === 'monitoring' && renderMonitoringTab()}
          {activeTab === 'users' && renderUsersTab()}
          {activeTab === 'backup' && renderBackupTab()}
          {activeTab === 'email' && renderEmailTab()}
        </div>
      </div>
    </div>
  );
};

export default AdvancedSettingsPage;