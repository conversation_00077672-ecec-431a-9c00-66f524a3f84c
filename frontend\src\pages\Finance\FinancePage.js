import React, { useState, useEffect } from 'react';
import customersService from '../../services/CustomersService';
import agentsService from '../../services/AgentsService';
import suppliersService from '../../services/SuppliersService';
import {
  ChartOfAccounts,
  JournalEntries,
  TrialBalance,
  FinancialStatements,
  AccountsReceivable,
  AccountsPayable,
  BankReconciliation,
  TaxReports,
  GeneralLedger,
  JournalBooks,
  AccountsHierarchy,
  AutomatedTransactions,
  AuditTrail,
  AccountingPeriods,
  JournalBooksAdvanced,
  AutomatedTransactionsAdvanced,
  AuditTrailAdvanced,
  GeneralLedgerAdvanced,
  JournalEntriesAdvanced,
  TrialBalanceAdvanced,
  FinancialStatementsAdvanced,
  AccountsReceivableAdvanced,
  AccountsPayableAdvanced
} from '../../components/Accounts/AccountsComponents';
import AdvancedReports from '../../components/Accounts/AdvancedReports';
import UserManagement from '../../components/Accounts/UserManagement';
import AccountingSettings from '../../components/Accounts/AccountingSettings';
import AgentsSuppliers from '../../components/Accounts/AgentsSuppliers';
import ReceiptVouchers from '../../components/Finance/ReceiptVouchers';
import PaymentVouchers from '../../components/Finance/PaymentVouchers';
import './FinancePage.css';

const FinancePage = () => {
  const [activeTab, setActiveTab] = useState('hierarchy');
  const [accounts, setAccounts] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [financialPeriod, setFinancialPeriod] = useState({
    startDate: '2024-01-01',
    endDate: '2024-12-31'
  });

  // بيانات تجريبية للحسابات
  const sampleAccounts = [
    // الأصول (Assets)
    { id: '1000', name: 'الأصول', type: 'header', category: 'assets', balance: 0, parent: null },
    { id: '1100', name: 'الأصول المتداولة', type: 'header', category: 'assets', balance: 0, parent: '1000' },
    { id: '1110', name: 'النقدية والبنوك', type: 'detail', category: 'assets', balance: 500000, parent: '1100' },
    { id: '1111', name: 'الصندوق', type: 'detail', category: 'assets', balance: 25000, parent: '1110' },
    { id: '1112', name: 'البنك الأهلي - الحساب الجاري', type: 'detail', category: 'assets', balance: 350000, parent: '1110' },
    { id: '1113', name: 'بنك الراجحي - حساب التوفير', type: 'detail', category: 'assets', balance: 125000, parent: '1110' },
    { id: '1120', name: 'العملاء والذمم المدينة', type: 'detail', category: 'assets', balance: 180000, parent: '1100' },
    { id: '1130', name: 'المخزون', type: 'detail', category: 'assets', balance: 75000, parent: '1100' },
    { id: '1140', name: 'المصروفات المدفوعة مقدماً', type: 'detail', category: 'assets', balance: 15000, parent: '1100' },
    
    { id: '1200', name: 'الأصول الثابتة', type: 'header', category: 'assets', balance: 0, parent: '1000' },
    { id: '1210', name: 'الأثاث والمعدات', type: 'detail', category: 'assets', balance: 120000, parent: '1200' },
    { id: '1220', name: 'أجهزة الكمبيوتر', type: 'detail', category: 'assets', balance: 80000, parent: '1200' },
    { id: '1230', name: 'السيارات', type: 'detail', category: 'assets', balance: 200000, parent: '1200' },
    { id: '1240', name: 'مجمع الإهلاك', type: 'detail', category: 'assets', balance: -50000, parent: '1200' },

    // الخصوم (Liabilities)
    { id: '2000', name: 'الخصوم', type: 'header', category: 'liabilities', balance: 0, parent: null },
    { id: '2100', name: 'الخصوم المتداولة', type: 'header', category: 'liabilities', balance: 0, parent: '2000' },
    { id: '2110', name: 'الموردون والذمم الدائنة', type: 'detail', category: 'liabilities', balance: 95000, parent: '2100' },
    { id: '2120', name: 'المصروفات المستحقة', type: 'detail', category: 'liabilities', balance: 25000, parent: '2100' },
    { id: '2130', name: 'ضريبة القيمة المضافة', type: 'detail', category: 'liabilities', balance: 18000, parent: '2100' },
    { id: '2140', name: 'رواتب الموظفين المستحقة', type: 'detail', category: 'liabilities', balance: 45000, parent: '2100' },
    
    { id: '2200', name: 'الخصوم طويلة الأجل', type: 'header', category: 'liabilities', balance: 0, parent: '2000' },
    { id: '2210', name: 'قروض البنوك', type: 'detail', category: 'liabilities', balance: 300000, parent: '2200' },

    // حقوق الملكية (Equity)
    { id: '3000', name: 'حقوق الملكية', type: 'header', category: 'equity', balance: 0, parent: null },
    { id: '3100', name: 'رأس المال', type: 'detail', category: 'equity', balance: 500000, parent: '3000' },
    { id: '3200', name: 'الأرباح المحتجزة', type: 'detail', category: 'equity', balance: 150000, parent: '3000' },
    { id: '3300', name: 'أرباح السنة الحالية', type: 'detail', category: 'equity', balance: 0, parent: '3000' },

    // الإيرادات (Revenue)
    { id: '4000', name: 'الإيرادات', type: 'header', category: 'revenue', balance: 0, parent: null },
    { id: '4100', name: 'إيرادات الخدمات السياحية', type: 'detail', category: 'revenue', balance: 850000, parent: '4000' },
    { id: '4110', name: 'إيرادات حجز الطيران', type: 'detail', category: 'revenue', balance: 450000, parent: '4100' },
    { id: '4120', name: 'إيرادات حجز الفنادق', type: 'detail', category: 'revenue', balance: 280000, parent: '4100' },
    { id: '4130', name: 'إيرادات العمرة والحج', type: 'detail', category: 'revenue', balance: 120000, parent: '4100' },
    { id: '4200', name: 'إيرادات أخرى', type: 'detail', category: 'revenue', balance: 25000, parent: '4000' },

    // المصروفات (Expenses)
    { id: '5000', name: 'المصروفات', type: 'header', category: 'expenses', balance: 0, parent: null },
    { id: '5100', name: 'مصروفات التشغيل', type: 'header', category: 'expenses', balance: 0, parent: '5000' },
    { id: '5110', name: 'الرواتب والأجور', type: 'detail', category: 'expenses', balance: 180000, parent: '5100' },
    { id: '5120', name: 'الإيجار', type: 'detail', category: 'expenses', balance: 60000, parent: '5100' },
    { id: '5130', name: 'الكهرباء والماء', type: 'detail', category: 'expenses', balance: 18000, parent: '5100' },
    { id: '5140', name: 'الاتصالات والإنترنت', type: 'detail', category: 'expenses', balance: 12000, parent: '5100' },
    { id: '5150', name: 'مصروفات التسويق', type: 'detail', category: 'expenses', balance: 35000, parent: '5100' },
    { id: '5160', name: 'مصروفات السفر', type: 'detail', category: 'expenses', balance: 25000, parent: '5100' },
    { id: '5170', name: 'مصروفات الصيانة', type: 'detail', category: 'expenses', balance: 15000, parent: '5100' },
    
    { id: '5200', name: 'المصروفات الإدارية', type: 'header', category: 'expenses', balance: 0, parent: '5000' },
    { id: '5210', name: 'القرطاسية والمطبوعات', type: 'detail', category: 'expenses', balance: 8000, parent: '5200' },
    { id: '5220', name: 'الرسوم الحكومية', type: 'detail', category: 'expenses', balance: 12000, parent: '5200' },
    { id: '5230', name: 'مصروفات قانونية ومحاسبية', type: 'detail', category: 'expenses', balance: 20000, parent: '5200' },
    
    { id: '5300', name: 'المصروفات المالية', type: 'header', category: 'expenses', balance: 0, parent: '5000' },
    { id: '5310', name: 'فوائد القروض', type: 'detail', category: 'expenses', balance: 18000, parent: '5300' },
    { id: '5320', name: 'رسوم بنكية', type: 'detail', category: 'expenses', balance: 5000, parent: '5300' }
  ];

  // بيانات تجريبية للمعاملات
  const sampleTransactions = [
    {
      id: 'JE-001',
      date: '2024-01-15',
      description: 'إيداع رأس المال الأولي',
      reference: 'INIT-001',
      entries: [
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 500000, credit: 0 },
        { accountId: '3100', accountName: 'رأس المال', debit: 0, credit: 500000 }
      ]
    },
    {
      id: 'JE-002',
      date: '2024-01-16',
      description: 'بيع خدمات سياحية نقداً',
      reference: 'INV-001',
      entries: [
        { accountId: '1111', accountName: 'الصندوق', debit: 15000, credit: 0 },
        { accountId: '4110', accountName: 'إيرادات حجز الطيران', debit: 0, credit: 15000 }
      ]
    },
    {
      id: 'JE-003',
      date: '2024-01-17',
      description: 'دفع راتب الموظفين',
      reference: 'SAL-001',
      entries: [
        { accountId: '5110', accountName: 'الرواتب والأجور', debit: 25000, credit: 0 },
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 0, credit: 25000 }
      ]
    },
    {
      id: 'JE-004',
      date: '2024-01-18',
      description: 'شراء أثاث ومعدات',
      reference: 'PUR-001',
      entries: [
        { accountId: '1210', accountName: 'الأثاث والمعدات', debit: 50000, credit: 0 },
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 0, credit: 50000 }
      ]
    },
    {
      id: 'JE-005',
      date: '2024-01-20',
      description: 'دفع إيجار المكتب',
      reference: 'RENT-001',
      entries: [
        { accountId: '5120', accountName: 'الإيجار', debit: 12000, credit: 0 },
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 0, credit: 12000 }
      ]
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    setIsLoading(true);
    setTimeout(() => {
      setAccounts(sampleAccounts);
      setTransactions(sampleTransactions);
      setIsLoading(false);
    }, 1000);
  }, []);

  const updateFinancialPeriod = (newPeriod) => {
    setFinancialPeriod(newPeriod);
  };

  const addAccount = (account) => {
    const newAccount = {
      ...account,
      id: `ACC-${Date.now()}`,
      balance: 0
    };
    setAccounts(prev => [...prev, newAccount]);
  };

  const updateAccount = (accountId, updatedAccount) => {
    setAccounts(prev => prev.map(acc => 
      acc.id === accountId ? { ...acc, ...updatedAccount } : acc
    ));
  };

  const deleteAccount = (accountId) => {
    setAccounts(prev => prev.filter(acc => acc.id !== accountId));
  };

  const addTransaction = (transaction) => {
    const newTransaction = {
      ...transaction,
      id: `JE-${String(transactions.length + 1).padStart(3, '0')}`
    };
    setTransactions(prev => [...prev, newTransaction]);
    
    // تحديث أرصدة الحسابات
    transaction.entries.forEach(entry => {
      updateAccountBalance(entry.accountId, entry.debit, entry.credit);
    });
  };

  const updateAccountBalance = (accountId, debit, credit) => {
    setAccounts(prev => prev.map(acc => {
      if (acc.id === accountId) {
        const netChange = debit - credit;
        return { ...acc, balance: acc.balance + netChange };
      }
      return acc;
    }));
  };

  const renderTabContent = () => {
    const commonProps = {
      accounts,
      transactions,
      financialPeriod,
      isLoading,
      onAddAccount: addAccount,
      onUpdateAccount: updateAccount,
      onDeleteAccount: deleteAccount,
      onAddTransaction: addTransaction,
      updateFinancialPeriod
    };

    switch (activeTab) {
      case 'hierarchy':
        return <AccountsHierarchy {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'receipt-vouchers':
        return <ReceiptVouchers {...commonProps} />;
      case 'payment-vouchers':
        return <PaymentVouchers {...commonProps} />;
      case 'chart':
        return <ChartOfAccounts {...commonProps} />;
      case 'ledger':
        return <GeneralLedgerAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'journals':
        return <JournalBooksAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'automated':
        return <AutomatedTransactionsAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'audit':
        return <AuditTrailAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'journal':
        return <JournalEntriesAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'trial':
        return <TrialBalanceAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'statements':
        return <FinancialStatementsAdvanced {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'receivables':
        return <AccountsReceivableAdvanced
          {...commonProps}
          currentUser={{ name: 'المدير العام' }}
          customers={customersService.getCustomersForReceivables()}
          customersService={customersService}
          agentsService={agentsService}
        />;
      case 'payables':
        return <AccountsPayableAdvanced
          {...commonProps}
          currentUser={{ name: 'المدير العام' }}
          suppliers={suppliersService.getAllSuppliers()}
          suppliersService={suppliersService}
        />;
      case 'reconciliation':
        return <BankReconciliation {...commonProps} />;
      case 'tax':
        return <TaxReports {...commonProps} />;
      case 'advanced':
        return <AdvancedReports {...commonProps} />;
      case 'users':
        return <UserManagement {...commonProps} />;
      case 'agents-suppliers':
        return <AgentsSuppliers {...commonProps} />;
      case 'periods':
        return <AccountingPeriods {...commonProps} currentUser={{ name: 'المدير العام' }} />;
      case 'settings':
        return <AccountingSettings {...commonProps} />;
      default:
        return <AccountsHierarchy {...commonProps} currentUser={{ name: 'المدير العام' }} />;
    }
  };

  return (
    <div className="accounts-page">
      <div className="page-header">
        <div className="header-content">
          <h1>نظام الحسابات</h1>
          <p>إدارة شاملة للحسابات والمعاملات المالية</p>
        </div>
        
        <div className="header-controls">
          <div className="period-selector">
            <label>الفترة المالية:</label>
            <input
              type="date"
              value={financialPeriod.startDate}
              onChange={(e) => updateFinancialPeriod({
                ...financialPeriod,
                startDate: e.target.value
              })}
            />
            <span>إلى</span>
            <input
              type="date"
              value={financialPeriod.endDate}
              onChange={(e) => updateFinancialPeriod({
                ...financialPeriod,
                endDate: e.target.value
              })}
            />
          </div>
        </div>
      </div>

      <div className="accounts-container">
        <div className="accounts-sidebar">
          <div className="accounts-tabs">
            <button 
              className={`tab-btn ${activeTab === 'hierarchy' ? 'active' : ''}`}
              onClick={() => setActiveTab('hierarchy')}
            >
              <span className="tab-icon">🏗️</span>
              هيكل الحسابات
            </button>

            <button 
              className={`tab-btn ${activeTab === 'receipt-vouchers' ? 'active' : ''}`}
              onClick={() => setActiveTab('receipt-vouchers')}
            >
              <span className="tab-icon">💰</span>
              سندات القبض
            </button>

            <button 
              className={`tab-btn ${activeTab === 'payment-vouchers' ? 'active' : ''}`}
              onClick={() => setActiveTab('payment-vouchers')}
            >
              <span className="tab-icon">💸</span>
              سندات الصرف
            </button>

            <button 
              className={`tab-btn ${activeTab === 'chart' ? 'active' : ''}`}
              onClick={() => setActiveTab('chart')}
            >
              <span className="tab-icon">📊</span>
              دليل الحسابات
            </button>

            <button 
              className={`tab-btn ${activeTab === 'ledger' ? 'active' : ''}`}
              onClick={() => setActiveTab('ledger')}
            >
              <span className="tab-icon">📚</span>
              دفتر الأستاذ
            </button>

            <button 
              className={`tab-btn ${activeTab === 'journals' ? 'active' : ''}`}
              onClick={() => setActiveTab('journals')}
            >
              <span className="tab-icon">📖</span>
              دفاتر اليومية
            </button>

            <button 
              className={`tab-btn ${activeTab === 'automated' ? 'active' : ''}`}
              onClick={() => setActiveTab('automated')}
            >
              <span className="tab-icon">🤖</span>
              العمليات التلقائية
            </button>

            <button 
              className={`tab-btn ${activeTab === 'audit' ? 'active' : ''}`}
              onClick={() => setActiveTab('audit')}
            >
              <span className="tab-icon">🔍</span>
              مسار التدقيق
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'journal' ? 'active' : ''}`}
              onClick={() => setActiveTab('journal')}
            >
              <span className="tab-icon">📝</span>
              قيود اليومية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'trial' ? 'active' : ''}`}
              onClick={() => setActiveTab('trial')}
            >
              <span className="tab-icon">⚖️</span>
              ميزان المراجعة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'statements' ? 'active' : ''}`}
              onClick={() => setActiveTab('statements')}
            >
              <span className="tab-icon">📋</span>
              القوائم المالية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'receivables' ? 'active' : ''}`}
              onClick={() => setActiveTab('receivables')}
            >
              <span className="tab-icon">💰</span>
              الذمم المدينة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'payables' ? 'active' : ''}`}
              onClick={() => setActiveTab('payables')}
            >
              <span className="tab-icon">💳</span>
              الذمم الدائنة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'reconciliation' ? 'active' : ''}`}
              onClick={() => setActiveTab('reconciliation')}
            >
              <span className="tab-icon">🏦</span>
              تسوية البنوك
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'tax' ? 'active' : ''}`}
              onClick={() => setActiveTab('tax')}
            >
              <span className="tab-icon">📄</span>
              التقارير الضريبية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'advanced' ? 'active' : ''}`}
              onClick={() => setActiveTab('advanced')}
            >
              <span className="tab-icon">📈</span>
              التقارير المتقدمة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              <span className="tab-icon">👥</span>
              إدارة المستخدمين
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'agents-suppliers' ? 'active' : ''}`}
              onClick={() => setActiveTab('agents-suppliers')}
            >
              <span className="tab-icon">🏢</span>
              الوكلاء والموردين
            </button>

            <button 
              className={`tab-btn ${activeTab === 'periods' ? 'active' : ''}`}
              onClick={() => setActiveTab('periods')}
            >
              <span className="tab-icon">📅</span>
              الفترات المحاسبية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              <span className="tab-icon">⚙️</span>
              الإعدادات
            </button>
          </div>
        </div>

        <div className="accounts-content">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default FinancePage;
