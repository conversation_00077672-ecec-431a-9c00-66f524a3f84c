import React, { useState, useEffect } from 'react';
import './AutomatedTransactions.css';

const AutomatedTransactions = ({ accounts, onAddTransaction, currentUser }) => {
  const [automationRules, setAutomationRules] = useState([]);
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);
  const [automatedTransactions, setAutomatedTransactions] = useState([]);

  const [newRule, setNewRule] = useState({
    name: '',
    type: 'revenue', // revenue, expense, transfer, recurring
    trigger: 'manual', // manual, scheduled, event
    frequency: 'monthly', // daily, weekly, monthly, yearly
    amount: 0,
    fromAccount: '',
    toAccount: '',
    description: '',
    isActive: true,
    conditions: [],
    nextExecution: new Date().toISOString().split('T')[0]
  });

  // قواعد الأتمتة المحددة مسبقاً
  const predefinedRules = [
    {
      id: 'auto-revenue-1',
      name: 'إيرادات الخدمات السياحية',
      type: 'revenue',
      description: 'تسجيل تلقائي لإيرادات الخدمات السياحية',
      fromAccount: '1111', // الصندوق
      toAccount: '4100', // إيرادات الخدمات السياحية
      isActive: true
    },
    {
      id: 'auto-expense-1',
      name: 'مصروفات الرواتب الشهرية',
      type: 'expense',
      description: 'تسجيل تلقائي لمصروفات الرواتب',
      fromAccount: '1112', // البنك
      toAccount: '5110', // الرواتب والأجور
      frequency: 'monthly',
      isActive: true
    },
    {
      id: 'auto-transfer-1',
      name: 'تحويل من البنك للصندوق',
      type: 'transfer',
      description: 'تحويل تلقائي من البنك إلى الصندوق',
      fromAccount: '1112', // البنك
      toAccount: '1111', // الصندوق
      isActive: true
    }
  ];

  // إنشاء معاملة تلقائية
  const createAutomatedTransaction = (rule, amount, description) => {
    const transactionId = `AUTO-${Date.now()}`;
    let entries = [];
    let transactionDescription = description || rule.description;

    switch (rule.type) {
      case 'revenue':
        entries = [
          { 
            accountId: rule.fromAccount, 
            accountName: accounts.find(acc => acc.id === rule.fromAccount)?.name || 'حساب غير معروف',
            debit: amount, 
            credit: 0,
            description: transactionDescription
          },
          { 
            accountId: rule.toAccount, 
            accountName: accounts.find(acc => acc.id === rule.toAccount)?.name || 'حساب غير معروف',
            debit: 0, 
            credit: amount,
            description: transactionDescription
          }
        ];
        break;

      case 'expense':
        entries = [
          { 
            accountId: rule.toAccount, 
            accountName: accounts.find(acc => acc.id === rule.toAccount)?.name || 'حساب غير معروف',
            debit: amount, 
            credit: 0,
            description: transactionDescription
          },
          { 
            accountId: rule.fromAccount, 
            accountName: accounts.find(acc => acc.id === rule.fromAccount)?.name || 'حساب غير معروف',
            debit: 0, 
            credit: amount,
            description: transactionDescription
          }
        ];
        break;

      case 'transfer':
        entries = [
          { 
            accountId: rule.toAccount, 
            accountName: accounts.find(acc => acc.id === rule.toAccount)?.name || 'حساب غير معروف',
            debit: amount, 
            credit: 0,
            description: transactionDescription
          },
          { 
            accountId: rule.fromAccount, 
            accountName: accounts.find(acc => acc.id === rule.fromAccount)?.name || 'حساب غير معروف',
            debit: 0, 
            credit: amount,
            description: transactionDescription
          }
        ];
        break;

      default:
        return null;
    }

    const transaction = {
      id: transactionId,
      date: new Date().toISOString().split('T')[0],
      description: transactionDescription,
      reference: `AUTO-${rule.id || rule.name.replace(/\s+/g, '-')}`,
      type: rule.type,
      user: 'النظام التلقائي',
      notes: `تم إنشاؤها تلقائياً بواسطة القاعدة: ${rule.name}`,
      entries,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isAutomated: true,
      ruleId: rule.id
    };

    return transaction;
  };

  // تنفيذ قاعدة تلقائية
  const executeAutomationRule = (rule, amount, description) => {
    if (!rule.isActive) {
      alert('هذه القاعدة غير مفعلة');
      return;
    }

    if (!amount || amount <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    const transaction = createAutomatedTransaction(rule, amount, description);
    if (transaction) {
      onAddTransaction(transaction);
      
      // إضافة المعاملة لقائمة المعاملات التلقائية
      setAutomatedTransactions(prev => [transaction, ...prev]);
      
      alert(`تم تنفيذ القاعدة "${rule.name}" بنجاح`);
    }
  };

  // إنشاء معاملات سريعة
  const QuickTransactionButtons = () => {
    const quickTransactions = [
      {
        name: 'إيراد نقدي',
        icon: '💰',
        color: '#27ae60',
        type: 'revenue',
        fromAccount: '1111',
        toAccount: '4100'
      },
      {
        name: 'مصروف نقدي',
        icon: '💸',
        color: '#e74c3c',
        type: 'expense',
        fromAccount: '1111',
        toAccount: '5100'
      },
      {
        name: 'تحويل بنكي',
        icon: '🏦',
        color: '#3498db',
        type: 'transfer',
        fromAccount: '1112',
        toAccount: '1111'
      },
      {
        name: 'دفع راتب',
        icon: '👥',
        color: '#9b59b6',
        type: 'expense',
        fromAccount: '1112',
        toAccount: '5110'
      }
    ];

    return (
      <div className="quick-transactions">
        <h3>معاملات سريعة</h3>
        <div className="quick-buttons">
          {quickTransactions.map((transaction, index) => (
            <button
              key={index}
              className="quick-btn"
              style={{ borderColor: transaction.color }}
              onClick={() => {
                const amount = prompt(`أدخل المبلغ لـ ${transaction.name}:`);
                if (amount && parseFloat(amount) > 0) {
                  const description = prompt('أدخل وصف المعاملة (اختياري):') || transaction.name;
                  executeAutomationRule({
                    name: transaction.name,
                    type: transaction.type,
                    fromAccount: transaction.fromAccount,
                    toAccount: transaction.toAccount,
                    description: description,
                    isActive: true
                  }, parseFloat(amount), description);
                }
              }}
            >
              <span className="quick-icon" style={{ color: transaction.color }}>
                {transaction.icon}
              </span>
              <span className="quick-name">{transaction.name}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="automated-transactions">
      <div className="automation-header">
        <div className="header-content">
          <h2>🤖 العمليات المالية التلقائية</h2>
          <p>أتمتة وتسريع العمليات المحاسبية المتكررة</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowRuleModal(true)}
          >
            ➕ قاعدة جديدة
          </button>
        </div>
      </div>

      <div className="automation-content">
        <div className="automation-sidebar">
          <QuickTransactionButtons />
          
          <div className="automation-stats">
            <h3>إحصائيات الأتمتة</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-value">{predefinedRules.filter(r => r.isActive).length}</span>
                <span className="stat-label">قواعد نشطة</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{automatedTransactions.length}</span>
                <span className="stat-label">معاملات تلقائية</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">
                  {automatedTransactions.reduce((sum, trans) => 
                    sum + (trans.entries?.reduce((entrySum, entry) => entrySum + (entry.debit || 0), 0) || 0), 0
                  ).toLocaleString()}
                </span>
                <span className="stat-label">إجمالي المبالغ</span>
              </div>
            </div>
          </div>
        </div>

        <div className="automation-main">
          <div className="predefined-rules">
            <h3>القواعد المحددة مسبقاً</h3>
            <div className="rules-grid">
              {predefinedRules.map(rule => (
                <div key={rule.id} className={`rule-card ${rule.type}`}>
                  <div className="rule-header">
                    <div className="rule-info">
                      <h4>{rule.name}</h4>
                      <p>{rule.description}</p>
                    </div>
                    <div className="rule-status">
                      <span className={`status-badge ${rule.isActive ? 'active' : 'inactive'}`}>
                        {rule.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>
                  </div>

                  <div className="rule-details">
                    <div className="account-flow">
                      <div className="from-account">
                        <span className="label">من:</span>
                        <span className="account">
                          {accounts.find(acc => acc.id === rule.fromAccount)?.name || 'حساب غير معروف'}
                        </span>
                      </div>
                      <div className="flow-arrow">→</div>
                      <div className="to-account">
                        <span className="label">إلى:</span>
                        <span className="account">
                          {accounts.find(acc => acc.id === rule.toAccount)?.name || 'حساب غير معروف'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="rule-actions">
                    <button
                      className="btn btn-success"
                      onClick={() => {
                        const amount = prompt(`أدخل المبلغ لتنفيذ "${rule.name}":`);
                        if (amount && parseFloat(amount) > 0) {
                          const description = prompt('أدخل وصف إضافي (اختياري):') || rule.description;
                          executeAutomationRule(rule, parseFloat(amount), description);
                        }
                      }}
                      disabled={!rule.isActive}
                    >
                      ⚡ تنفيذ
                    </button>
                    <button
                      className="btn btn-secondary"
                      onClick={() => {
                        setSelectedRule(rule);
                        setShowRuleModal(true);
                      }}
                    >
                      ⚙️ تعديل
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="automated-history">
            <h3>سجل المعاملات التلقائية</h3>
            {automatedTransactions.length === 0 ? (
              <div className="no-transactions">
                <span className="icon">🤖</span>
                <h4>لا توجد معاملات تلقائية</h4>
                <p>ابدأ بتنفيذ قاعدة تلقائية أو إنشاء معاملة سريعة</p>
              </div>
            ) : (
              <div className="transactions-table">
                <table>
                  <thead>
                    <tr>
                      <th>التاريخ</th>
                      <th>الوصف</th>
                      <th>النوع</th>
                      <th>المبلغ</th>
                      <th>الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    {automatedTransactions.map(transaction => (
                      <tr key={transaction.id} className={`transaction-row ${transaction.type}`}>
                        <td>{new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                        <td>{transaction.description}</td>
                        <td>
                          <span className={`type-badge ${transaction.type}`}>
                            {transaction.type === 'revenue' ? 'إيراد' : 
                             transaction.type === 'expense' ? 'مصروف' : 'تحويل'}
                          </span>
                        </td>
                        <td className="amount">
                          {transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0).toLocaleString()} ر.س
                        </td>
                        <td>
                          <span className="status-badge success">مكتمل</span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* نموذج إضافة/تعديل قاعدة */}
      {showRuleModal && (
        <div className="modal-overlay">
          <div className="rule-modal">
            <div className="modal-header">
              <h3>{selectedRule ? '⚙️ تعديل القاعدة' : '➕ قاعدة تلقائية جديدة'}</h3>
              <button 
                className="close-btn"
                onClick={() => {
                  setShowRuleModal(false);
                  setSelectedRule(null);
                }}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم القاعدة *</label>
                  <input
                    type="text"
                    value={newRule.name}
                    onChange={(e) => setNewRule(prev => ({ ...prev, name: e.target.value }))}
                    className="form-control"
                    placeholder="مثال: إيرادات يومية"
                  />
                </div>

                <div className="form-group">
                  <label>نوع المعاملة *</label>
                  <select
                    value={newRule.type}
                    onChange={(e) => setNewRule(prev => ({ ...prev, type: e.target.value }))}
                    className="form-control"
                  >
                    <option value="revenue">إيراد</option>
                    <option value="expense">مصروف</option>
                    <option value="transfer">تحويل</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>الحساب المدين *</label>
                  <select
                    value={newRule.fromAccount}
                    onChange={(e) => setNewRule(prev => ({ ...prev, fromAccount: e.target.value }))}
                    className="form-control"
                  >
                    <option value="">اختر الحساب</option>
                    {accounts.filter(acc => acc.type === 'detail').map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.id})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>الحساب الدائن *</label>
                  <select
                    value={newRule.toAccount}
                    onChange={(e) => setNewRule(prev => ({ ...prev, toAccount: e.target.value }))}
                    className="form-control"
                  >
                    <option value="">اختر الحساب</option>
                    {accounts.filter(acc => acc.type === 'detail').map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.id})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>الوصف *</label>
                  <textarea
                    value={newRule.description}
                    onChange={(e) => setNewRule(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="وصف القاعدة التلقائية..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  setShowRuleModal(false);
                  setSelectedRule(null);
                }}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => {
                  // حفظ القاعدة (يمكن تطويرها لاحقاً)
                  alert('سيتم تطوير حفظ القواعد المخصصة قريباً');
                  setShowRuleModal(false);
                }}
              >
                💾 حفظ القاعدة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomatedTransactions;