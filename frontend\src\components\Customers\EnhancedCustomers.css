/* 👥 أنماط إدارة العملاء المحسنة */

.enhanced-customers {
  padding: var(--space-6);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  min-height: 100vh;
  animation: fadeInUp 0.6s ease-out;
}

/* 🎯 رأس الصفحة */
.customers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.header-title h1 {
  margin: 0 0 var(--space-1) 0;
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-title p {
  margin: 0;
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

/* 📊 إحصائيات العملاء */
.customers-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  opacity: 0.8;
}

.stat-card.total::before {
  background: var(--gradient-primary);
}

.stat-card.with-visas::before {
  background: var(--gradient-secondary);
}

.stat-card.bookings::before {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

.stat-card.revenue::before {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.stat-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2xl);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  flex-shrink: 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.stat-content h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--neutral-800);
  line-height: 1;
}

/* 🔍 البحث */
.customers-search {
  margin-bottom: var(--space-6);
  padding: var(--space-5);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.customer-search-input {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 1rem;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.customer-search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.customer-search-input::placeholder {
  color: var(--neutral-400);
}

/* 👥 شبكة العملاء */
.customers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* 👤 بطاقة العميل */
.customer-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.customer-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-300);
}

.customer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  opacity: 0.8;
}

/* 🎯 رأس بطاقة العميل */
.customer-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  position: relative;
}

.customer-avatar {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
}

.customer-info {
  flex: 1;
}

.customer-info h3 {
  margin: 0 0 var(--space-1) 0;
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.customer-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--neutral-600);
  font-weight: 500;
}

.customer-info .email {
  font-size: 0.75rem;
  color: var(--neutral-500);
}

/* 📋 شارة التأشيرة */
.visa-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: var(--gradient-secondary);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: 0 0 var(--radius-lg) 0;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  box-shadow: var(--shadow-md);
}

.visa-icon {
  font-size: 0.875rem;
}

.visa-number {
  font-family: 'Courier New', monospace;
  font-weight: 700;
}

/* 📝 تفاصيل العميل */
.customer-details {
  margin-bottom: var(--space-4);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  color: var(--neutral-500);
  font-weight: 500;
}

.detail-row .value {
  color: var(--neutral-700);
  font-weight: 600;
}

.detail-row.visa-info .value.visa-number {
  font-family: 'Courier New', monospace;
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
}

/* 📊 إحصائيات العميل */
.customer-stats {
  display: flex;
  justify-content: space-around;
  padding: var(--space-3) 0;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  margin-bottom: var(--space-4);
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--neutral-800);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--neutral-500);
  margin-top: var(--space-1);
}

/* 🎬 إجراءات العميل */
.customer-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: 1rem;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-btn.view {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

.action-btn.edit {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-600);
}

.action-btn.history {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* ➕ نموذج إضافة العميل */
.customer-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.customer-form-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInUp 0.4s ease-out;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
}

.form-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
  cursor: pointer;
  font-size: 1.25rem;
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background: var(--danger-500);
  color: white;
  transform: scale(1.1);
}

/* 📝 نموذج العميل */
.customer-form {
  padding: var(--space-6);
}

.form-sections {
  display: grid;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.form-section {
  padding: var(--space-5);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-700);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-section.visa-section {
  background: rgba(34, 197, 94, 0.05);
  border-color: rgba(34, 197, 94, 0.2);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-3);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

/* ✅ معلومات التأشيرة المختارة */
.selected-visa-info {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: var(--radius-lg);
}

.selected-visa-info h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: 1rem;
  color: var(--secondary-600);
}

.visa-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.visa-details p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--neutral-600);
}

/* 🎬 إجراءات النموذج */
.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-6);
  border-top: 1px solid var(--glass-border);
}

/* 👁️ مودال تفاصيل العميل */
.customer-details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.customer-details-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInUp 0.4s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.customer-full-details {
  padding: var(--space-6);
}

.details-section {
  margin-bottom: var(--space-6);
}

.details-section:last-child {
  margin-bottom: 0;
}

.details-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-item label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--neutral-500);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

/* 📋 بطاقة تفاصيل التأشيرة */
.visa-details-card {
  padding: var(--space-4);
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-lg);
}

.visa-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.visa-type {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-600);
}

.visa-status {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.visa-status.available {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.visa-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.visa-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.visa-info-item label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--neutral-500);
}

.visa-info-item span.visa-number {
  font-family: 'Courier New', monospace;
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 700;
  display: inline-block;
}

/* 📊 إحصائيات مفصلة */
.customer-stats-detailed {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item .stat-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
}

.stat-info {
  flex: 1;
}

.stat-info .stat-value {
  display: block;
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--neutral-800);
  line-height: 1;
}

.stat-info .stat-label {
  font-size: 0.75rem;
  color: var(--neutral-500);
  margin-top: var(--space-1);
}

/* 🔄 حالة التحميل */
.customers-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
  min-height: 50vh;
}

.customers-loading p {
  margin-top: var(--space-4);
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

/* 📱 التصميم المتجاوب */
@media (max-width: 1024px) {
  .customers-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .customers-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .enhanced-customers {
    padding: var(--space-4);
  }
  
  .customers-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .customers-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .customers-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .visa-details {
    grid-template-columns: 1fr;
  }
  
  .visa-info-grid {
    grid-template-columns: 1fr;
  }
  
  .customer-stats-detailed {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header-title h1 {
    font-size: 2rem;
  }
  
  .customers-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
  
  .customer-form-modal,
  .customer-details-modal {
    width: 95%;
    margin: var(--space-4);
  }
}

/* 🎬 حركات خاصة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.customer-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .enhanced-customers,
  .stat-card,
  .customer-card,
  .action-btn {
    animation: none;
    transition: none;
  }
  
  .stat-card:hover,
  .customer-card:hover,
  .action-btn:hover {
    transform: none;
  }
}
