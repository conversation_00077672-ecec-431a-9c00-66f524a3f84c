import React, { useState, useEffect } from 'react';

const PaymentsPage = () => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterMethod, setFilterMethod] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const [newPayment, setNewPayment] = useState({
    invoiceId: '',
    customerName: '',
    amount: '',
    currency: 'SAR',
    paymentMethod: 'cash',
    paymentDate: new Date().toISOString().split('T')[0],
    referenceNumber: '',
    bankName: '',
    checkNumber: '',
    notes: ''
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setPayments([
        {
          id: 'PAY-2024-001',
          invoiceId: 'INV-2024-001',
          customerName: 'أحمد محمد العلي',
          amount: 2300,
          currency: 'SAR',
          paymentMethod: 'cash',
          paymentDate: '2024-01-20',
          referenceNumber: '',
          bankName: '',
          checkNumber: '',
          status: 'completed',
          processedBy: 'محمد الإداري',
          notes: 'دفع نقدي كامل'
        },
        {
          id: 'PAY-2024-002',
          invoiceId: 'INV-2024-002',
          customerName: 'شركة السفر الذهبي',
          amount: 15000,
          currency: 'SAR',
          paymentMethod: 'bank_transfer',
          paymentDate: '2024-01-19',
          referenceNumber: 'TRF-*********',
          bankName: 'البنك الأهلي السعودي',
          checkNumber: '',
          status: 'pending',
          processedBy: 'فاطمة المحاسبة',
          notes: 'في انتظار تأكيد التحويل'
        },
        {
          id: 'PAY-2024-003',
          invoiceId: 'INV-2024-003',
          customerName: 'خالد أحمد الزهراني',
          amount: 200,
          currency: 'USD',
          paymentMethod: 'cash',
          paymentDate: '2024-01-18',
          referenceNumber: '',
          bankName: '',
          checkNumber: '',
          status: 'completed',
          processedBy: 'أحمد الكاشير',
          notes: 'دفع جزئي - 200 دولار من أصل 410'
        },
        {
          id: 'PAY-2024-004',
          invoiceId: 'INV-2024-004',
          customerName: 'فاطمة سالم الأحمد',
          amount: 5000,
          currency: 'SAR',
          paymentMethod: 'check',
          paymentDate: '2024-01-17',
          referenceNumber: '',
          bankName: 'بنك الراجحي',
          checkNumber: 'CHK-456789',
          status: 'failed',
          processedBy: 'علي المحاسب',
          notes: 'الشيك مرتد - رصيد غير كافي'
        },
        {
          id: 'PAY-2024-005',
          invoiceId: 'INV-2024-005',
          customerName: 'نورا علي السالم',
          amount: 138000,
          currency: 'YER',
          paymentMethod: 'cash',
          paymentDate: '2024-01-16',
          referenceNumber: '',
          bankName: '',
          checkNumber: '',
          status: 'completed',
          processedBy: 'سارة الكاشيرة',
          notes: 'دفع نقدي - عملة يمنية'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const getPaymentMethodText = (method) => {
    switch (method) {
      case 'cash': return 'نقدي';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'check': return 'شيك';
      case 'credit_card': return 'بطاقة ائتمان';
      default: return method;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'failed': return '#e74c3c';
      case 'cancelled': return '#95a5a6';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return 'مكتمل';
      case 'pending': return 'معلق';
      case 'failed': return 'فاشل';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const handleAddPayment = (e) => {
    e.preventDefault();
    const payment = {
      id: `PAY-${new Date().getFullYear()}-${String(payments.length + 1).padStart(3, '0')}`,
      ...newPayment,
      amount: parseFloat(newPayment.amount),
      status: 'completed',
      processedBy: 'المستخدم الحالي'
    };
    setPayments([payment, ...payments]);
    setNewPayment({
      invoiceId: '',
      customerName: '',
      amount: '',
      currency: 'SAR',
      paymentMethod: 'cash',
      paymentDate: new Date().toISOString().split('T')[0],
      referenceNumber: '',
      bankName: '',
      checkNumber: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.invoiceId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesMethod = filterMethod === 'all' || payment.paymentMethod === filterMethod;
    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus;
    return matchesSearch && matchesMethod && matchesStatus;
  });

  const getTotalsByMethod = () => {
    const totals = {};
    payments.forEach(payment => {
      if (payment.status === 'completed') {
        if (!totals[payment.paymentMethod]) {
          totals[payment.paymentMethod] = 0;
        }
        // تحويل تقريبي للعملات للعرض
        let amountInSAR = payment.amount;
        if (payment.currency === 'USD') {
          amountInSAR = payment.amount * 3.75; // سعر تقريبي
        } else if (payment.currency === 'YER') {
          amountInSAR = payment.amount / 1000; // سعر تقريبي
        }
        totals[payment.paymentMethod] += amountInSAR;
      }
    });
    return totals;
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#666' }}>جاري تحميل المدفوعات...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          💳 إدارة المدفوعات
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          تسجيل ومتابعة جميع المدفوعات والتحصيلات
        </p>
      </div>

      {/* الإحصائيات السريعة */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        {[
          { title: 'إجمالي المدفوعات', value: payments.length, color: '#3498db', icon: '💳' },
          { title: 'مدفوعات مكتملة', value: payments.filter(p => p.status === 'completed').length, color: '#27ae60', icon: '✅' },
          { title: 'مدفوعات معلقة', value: payments.filter(p => p.status === 'pending').length, color: '#f39c12', icon: '⏳' },
          { title: 'مدفوعات فاشلة', value: payments.filter(p => p.status === 'failed').length, color: '#e74c3c', icon: '❌' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '12px',
            boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '10px' }}>{stat.icon}</div>
            <h3 style={{ color: stat.color, margin: '0 0 10px 0', fontSize: '14px' }}>{stat.title}</h3>
            <p style={{ fontSize: '20px', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>{stat.value}</p>
          </div>
        ))}
      </div>

      {/* إحصائيات طرق الدفع */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>📊 إحصائيات طرق الدفع</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          {Object.entries(getTotalsByMethod()).map(([method, total]) => (
            <div key={method} style={{
              background: '#f8f9fa',
              padding: '15px',
              borderRadius: '10px',
              textAlign: 'center',
              border: '2px solid #e0e0e0'
            }}>
              <div style={{ fontSize: '24px', marginBottom: '10px' }}>
                {method === 'cash' ? '💵' :
                 method === 'bank_transfer' ? '🏦' :
                 method === 'check' ? '📝' :
                 method === 'credit_card' ? '💳' : '💰'}
              </div>
              <h4 style={{ color: '#3498db', margin: '0 0 10px 0' }}>{getPaymentMethodText(method)}</h4>
              <p style={{ fontSize: '16px', fontWeight: 'bold', margin: 0, color: '#27ae60' }}>
                {formatCurrency(total)}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* أدوات التحكم */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '20px',
          flexWrap: 'wrap',
          gap: '10px'
        }}>
          <button
            onClick={() => setShowAddForm(true)}
            style={{
              background: '#27ae60',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'background 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.background = '#219a52'}
            onMouseLeave={(e) => e.target.style.background = '#27ae60'}
          >
            ➕ تسجيل دفعة جديدة
          </button>

          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <button style={{
              background: '#3498db',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              📊 تقرير المدفوعات
            </button>
            <button style={{
              background: '#e67e22',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              📤 تصدير Excel
            </button>
          </div>
        </div>

        {/* البحث والفلترة */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '15px' 
        }}>
          <input
            type="text"
            placeholder="البحث في المدفوعات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          />
          
          <select
            value={filterMethod}
            onChange={(e) => setFilterMethod(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          >
            <option value="all">جميع طرق الدفع</option>
            <option value="cash">نقدي</option>
            <option value="bank_transfer">تحويل بنكي</option>
            <option value="check">شيك</option>
            <option value="credit_card">بطاقة ائتمان</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          >
            <option value="all">جميع الحالات</option>
            <option value="completed">مكتمل</option>
            <option value="pending">معلق</option>
            <option value="failed">فاشل</option>
            <option value="cancelled">ملغي</option>
          </select>
        </div>
      </div>

      {/* جدول المدفوعات */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ background: '#f8f9fa' }}>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>رقم الدفعة</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>رقم الفاتورة</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>العميل</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>المبلغ</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>طريقة الدفع</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>التاريخ</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الحالة</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>معالج بواسطة</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredPayments.map((payment, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '15px', fontWeight: 'bold', color: '#3498db' }}>
                  {payment.id}
                </td>
                <td style={{ padding: '15px', color: '#8e44ad' }}>
                  {payment.invoiceId}
                </td>
                <td style={{ padding: '15px' }}>{payment.customerName}</td>
                <td style={{ padding: '15px', fontWeight: 'bold', color: '#27ae60' }}>
                  {formatCurrency(payment.amount, payment.currency)}
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <span style={{
                    background: '#f8f9fa',
                    padding: '5px 12px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#2c3e50'
                  }}>
                    {getPaymentMethodText(payment.paymentMethod)}
                  </span>
                </td>
                <td style={{ padding: '15px' }}>{payment.paymentDate}</td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <span style={{
                    background: getStatusColor(payment.status),
                    color: 'white',
                    padding: '5px 12px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    {getStatusText(payment.status)}
                  </span>
                </td>
                <td style={{ padding: '15px', fontSize: '12px', color: '#666' }}>
                  {payment.processedBy}
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                    <button style={{
                      background: '#3498db',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}>
                      عرض
                    </button>
                    <button style={{
                      background: '#27ae60',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}>
                      طباعة
                    </button>
                    {payment.status === 'pending' && (
                      <button style={{
                        background: '#e67e22',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}>
                        تأكيد
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredPayments.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '50px', 
            color: '#666',
            fontSize: '16px'
          }}>
            لا توجد مدفوعات تطابق معايير البحث
          </div>
        )}
      </div>

      {/* نموذج إضافة دفعة جديدة */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>💳 تسجيل دفعة جديدة</h2>
            
            <form onSubmit={handleAddPayment}>
              {/* معلومات الدفعة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#3498db', marginBottom: '15px' }}>📋 معلومات الدفعة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الفاتورة</label>
                    <input
                      type="text"
                      value={newPayment.invoiceId}
                      onChange={(e) => setNewPayment({...newPayment, invoiceId: e.target.value})}
                      required
                      placeholder="INV-2024-001"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newPayment.customerName}
                      onChange={(e) => setNewPayment({...newPayment, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الدفع</label>
                    <input
                      type="date"
                      value={newPayment.paymentDate}
                      onChange={(e) => setNewPayment({...newPayment, paymentDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newPayment.amount}
                      onChange={(e) => setNewPayment({...newPayment, amount: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العملة</label>
                    <select
                      value={newPayment.currency}
                      onChange={(e) => setNewPayment({...newPayment, currency: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                      <option value="YER">🇾🇪 ريال يمني (YER)</option>
                      <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>طريقة الدفع</label>
                    <select
                      value={newPayment.paymentMethod}
                      onChange={(e) => setNewPayment({...newPayment, paymentMethod: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="cash">نقدي</option>
                      <option value="bank_transfer">تحويل بنكي</option>
                      <option value="check">شيك</option>
                      <option value="credit_card">بطاقة ائتمان</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* تفاصيل إضافية */}
              {(newPayment.paymentMethod === 'bank_transfer' || newPayment.paymentMethod === 'check') && (
                <div style={{ marginBottom: '25px' }}>
                  <h3 style={{ color: '#e67e22', marginBottom: '15px' }}>🏦 تفاصيل إضافية</h3>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                    {newPayment.paymentMethod === 'bank_transfer' && (
                      <>
                        <div>
                          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم المرجع</label>
                          <input
                            type="text"
                            value={newPayment.referenceNumber}
                            onChange={(e) => setNewPayment({...newPayment, referenceNumber: e.target.value})}
                            placeholder="TRF-*********"
                            style={{
                              width: '100%',
                              padding: '12px',
                              border: '2px solid #e0e0e0',
                              borderRadius: '8px',
                              fontSize: '14px',
                              boxSizing: 'border-box'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم البنك</label>
                          <input
                            type="text"
                            value={newPayment.bankName}
                            onChange={(e) => setNewPayment({...newPayment, bankName: e.target.value})}
                            placeholder="البنك الأهلي السعودي"
                            style={{
                              width: '100%',
                              padding: '12px',
                              border: '2px solid #e0e0e0',
                              borderRadius: '8px',
                              fontSize: '14px',
                              boxSizing: 'border-box'
                            }}
                          />
                        </div>
                      </>
                    )}
                    {newPayment.paymentMethod === 'check' && (
                      <>
                        <div>
                          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الشيك</label>
                          <input
                            type="text"
                            value={newPayment.checkNumber}
                            onChange={(e) => setNewPayment({...newPayment, checkNumber: e.target.value})}
                            placeholder="CHK-123456"
                            style={{
                              width: '100%',
                              padding: '12px',
                              border: '2px solid #e0e0e0',
                              borderRadius: '8px',
                              fontSize: '14px',
                              boxSizing: 'border-box'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البنك المسحوب عليه</label>
                          <input
                            type="text"
                            value={newPayment.bankName}
                            onChange={(e) => setNewPayment({...newPayment, bankName: e.target.value})}
                            placeholder="بنك الراجحي"
                            style={{
                              width: '100%',
                              padding: '12px',
                              border: '2px solid #e0e0e0',
                              borderRadius: '8px',
                              fontSize: '14px',
                              boxSizing: 'border-box'
                            }}
                          />
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>📝 ملاحظات</h3>
                <textarea
                  value={newPayment.notes}
                  onChange={(e) => setNewPayment({...newPayment, notes: e.target.value})}
                  rows="3"
                  placeholder="ملاحظات إضافية..."
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              {/* أزرار التحكم */}
              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    background: '#95a5a6',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    background: '#27ae60',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  تسجيل الدفعة
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* CSS للأنيميشن */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default PaymentsPage;
