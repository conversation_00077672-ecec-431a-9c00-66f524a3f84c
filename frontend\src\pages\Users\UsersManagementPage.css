/* نظام إدارة المستخدمين المتقدم */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

.advanced-users-management, .users-management-page {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* رأس الصفحة المتقدم */
.page-header-advanced {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 30px;
  box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.page-header-advanced::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  position: relative;
  z-index: 2;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 20px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.title-icon {
  font-size: 3rem;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.page-description {
  font-size: 1.2rem;
  margin: 0;
  line-height: 1.6;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.btn-add-user {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 15px 30px;
  border-radius: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: inherit;
  backdrop-filter: blur(10px);
}

.btn-add-user:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-icon {
  font-size: 1.2rem;
}

/* إحصائيات متقدمة */
.advanced-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 25px;
  position: relative;
  z-index: 2;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 18px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.4s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.15);
  background: rgba(255, 255, 255, 1);
}

.stat-card.primary::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.stat-card.success::before { background: linear-gradient(90deg, #27ae60, #2ecc71); }
.stat-card.warning::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
.stat-card.info::before { background: linear-gradient(90deg, #3498db, #2980b9); }
.stat-card.secondary::before { background: linear-gradient(90deg, #95a5a6, #7f8c8d); }

.stat-icon {
  font-size: 3rem;
  opacity: 0.8;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.stat-info {
  flex: 1;
  color: #2c3e50;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 5px;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: #7f8c8d;
}

.stat-trend, .stat-percentage {
  font-size: 0.85rem;
  font-weight: 500;
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

/* أدوات التحكم المتقدمة */
.advanced-controls {
  background: white;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.search-and-filters {
  display: flex;
  gap: 25px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box-advanced {
  position: relative;
  flex: 1;
  min-width: 350px;
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.2rem;
}

.search-input-advanced {
  width: 100%;
  padding: 15px 55px 15px 50px;
  border: 2px solid #e1e8ed;
  border-radius: 15px;
  font-size: 1.1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input-advanced:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
}

.search-clear {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  font-size: 1.1rem;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.search-clear:hover {
  background: #f1f3f4;
  color: #2c3e50;
}

.filters-advanced {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-select {
  padding: 12px 18px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.view-mode-selector {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 5px;
}

.view-mode-btn {
  background: transparent;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  color: #7f8c8d;
}

.view-mode-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.view-mode-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.sort-controls {
  display: flex;
  gap: 15px;
}

.sort-select {
  padding: 12px 18px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

/* العمليات المجمعة المتقدمة */
.bulk-actions-advanced {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 15px;
  padding: 20px 30px;
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  animation: slideDown 0.4s ease-out;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bulk-info {
  display: flex;
  align-items: center;
  gap: 15px;
  font-weight: 600;
  font-size: 1.1rem;
}

.bulk-count {
  background: rgba(255, 255, 255, 0.25);
  padding: 8px 16px;
  border-radius: 25px;
  font-weight: 800;
  font-size: 1.2rem;
}

.bulk-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.bulk-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 18px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: inherit;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.bulk-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.bulk-btn.delete:hover {
  background: #e74c3c;
  border-color: #e74c3c;
}

.bulk-clear {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-family: inherit;
}

.bulk-clear:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* عرض المستخدمين */
.users-display {
  margin-bottom: 30px;
}

.users-grid-advanced {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 25px;
}

/* بطاقة المستخدم المتقدمة */
.user-card {
  background: white;
  border-radius: 20px;
  padding: 25px;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.user-card:hover::before {
  transform: scaleX(1);
}

.user-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.user-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.user-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: #667eea;
}

.user-status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.active {
  background: #27ae60;
  box-shadow: 0 0 0 4px rgba(39, 174, 96, 0.2);
}

.status-dot.inactive {
  background: #e74c3c;
  box-shadow: 0 0 0 4px rgba(231, 76, 60, 0.2);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.user-avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid #f8f9fa;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.user-card:hover .user-avatar {
  transform: scale(1.05);
  border-color: #667eea;
}

.user-role-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-info-section {
  text-align: center;
  margin-bottom: 20px;
}

.user-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.user-position {
  font-size: 1rem;
  font-weight: 600;
  color: #667eea;
  margin: 0 0 5px 0;
}

.user-department {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin: 0 0 8px 0;
}

.user-email {
  font-size: 0.85rem;
  color: #95a5a6;
  margin: 0;
  word-break: break-word;
}

.user-stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 12px;
}

.user-stat {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-stat .stat-icon {
  font-size: 1.2rem;
}

.user-stat .stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
}

.user-stat .stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 500;
}

.user-security-section {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.security-badge {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.badge-icon {
  font-size: 0.9rem;
}

.user-tags-section {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.user-tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.user-actions-section {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  position: relative;
}

.action-btn::before {
  content: attr(title);
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: #2c3e50;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn.view {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.action-btn.permissions {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.action-btn.edit {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.action-btn.status {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.action-btn.delete {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.user-last-login {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 10px;
  border-top: 2px solid #e1e8ed;
}

.last-login-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.last-login-time {
  font-size: 0.85rem;
  color: #2c3e50;
  font-weight: 500;
}

/* عدم وجود مستخدمين */
.no-users-advanced {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.no-users-icon {
  font-size: 5rem;
  margin-bottom: 25px;
  opacity: 0.5;
  filter: grayscale(100%);
}

.no-users-text {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: #2c3e50;
}

.no-users-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #7f8c8d;
  margin-bottom: 30px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.btn-add-first-user {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-add-first-user:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* ترقيم الصفحات المتقدم */
.pagination-advanced {
  background: white;
  border-radius: 15px;
  padding: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  flex-wrap: wrap;
  gap: 20px;
}

.pagination-info {
  color: #7f8c8d;
  font-weight: 500;
  font-size: 0.95rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  background: white;
  border: 2px solid #e1e8ed;
  padding: 10px 16px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: inherit;
  color: #2c3e50;
}

.pagination-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 4px;
}

.pagination-number {
  width: 45px;
  height: 45px;
  border: 2px solid #e1e8ed;
  background: white;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #2c3e50;
}

.pagination-number:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.pagination-number.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-size {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-size-select {
  padding: 10px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 0.9rem;
  font-family: inherit;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-size-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* نافذة إدارة الأذونات */
.permissions-modal {
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  backdrop-filter: blur(5px);
}

.modal {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  animation: slideUp 0.4s ease-out;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
}

.user-info-header {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 15px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.user-avatar-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name-small {
  font-weight: 600;
  font-size: 1rem;
}

.user-role-small {
  font-size: 0.85rem;
  opacity: 0.8;
}

.modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.modal-body {
  padding: 30px;
  max-height: 70vh;
  overflow-y: auto;
}

.permissions-body {
  padding: 0;
}

.permissions-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 30px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.summary-stat {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.summary-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 600;
}

.permissions-categories {
  padding: 30px;
}

.permission-category {
  margin-bottom: 35px;
  border: 2px solid #f1f3f4;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.permission-category:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
}

.category-header {
  background: #f8f9fa;
  padding: 20px 25px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.category-icon {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: white;
}

.category-name {
  flex: 1;
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.category-stats {
  background: #667eea;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  padding: 25px;
}

.permission-item {
  background: white;
  border: 2px solid #f1f3f4;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.permission-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.permission-item.granted {
  border-color: #27ae60;
  background: rgba(39, 174, 96, 0.05);
}

.permission-item.critical {
  border-left: 4px solid #e74c3c;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.permission-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  flex: 1;
}

.permission-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #667eea;
}

.permission-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.permission-badges {
  display: flex;
  gap: 6px;
}

.level-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.level-badge.admin {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.level-badge.manager {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.level-badge.employee {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.critical-badge {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  padding: 4px 6px;
  border-radius: 50%;
  font-size: 0.8rem;
}

.permission-key {
  font-size: 0.8rem;
  color: #95a5a6;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  margin-top: 8px;
}

.custom-permissions-section {
  padding: 30px;
  border-top: 2px solid #f1f3f4;
  background: #f8f9fa;
}

.custom-permissions-section h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 700;
}

.custom-permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.custom-permission-item {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.custom-permission-name {
  font-family: 'Courier New', monospace;
}

.remove-custom-permission {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.remove-custom-permission:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.add-custom-permission {
  display: flex;
  gap: 10px;
}

.custom-permission-input {
  flex: 1;
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  font-family: 'Courier New', monospace;
  transition: all 0.3s ease;
}

.custom-permission-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-footer {
  padding: 25px 30px;
  border-top: 1px solid #f1f3f4;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: #f8f9fa;
}

.btn-cancel {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-cancel:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.btn-save {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: inherit;
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* التجاوب مع الشاشات */
@media (max-width: 1200px) {
  .advanced-stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .users-grid-advanced {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .advanced-users-management {
    padding: 15px;
  }
  
  .page-header-advanced {
    padding: 25px;
  }
  
  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 15px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .advanced-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .search-and-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box-advanced {
    min-width: auto;
  }
  
  .filters-advanced {
    justify-content: stretch;
  }
  
  .filter-select {
    flex: 1;
    min-width: auto;
  }
  
  .view-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .users-grid-advanced {
    grid-template-columns: 1fr;
  }
  
  .bulk-actions-advanced {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .bulk-buttons {
    justify-content: center;
  }
  
  .pagination-advanced {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .permissions-summary {
    grid-template-columns: 1fr;
  }
  
  .permissions-grid {
    grid-template-columns: 1fr;
  }
  
  .modal {
    width: 95%;
    margin: 10px;
  }
}

@media (max-width: 480px) {
  .advanced-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .user-stats-section {
    grid-template-columns: 1fr;
  }
  
  .user-actions-section {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
  
  .action-btn {
    width: 100%;
    height: 45px;
    border-radius: 8px;
  }
  
  .pagination-numbers {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .category-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

/* أنماط النوافذ المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal, .modal-large, .modal-small {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

.modal {
  width: 500px;
  max-width: 90vw;
}

.modal-large {
  width: 800px;
  max-width: 95vw;
}

.modal-small {
  width: 400px;
  max-width: 90vw;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 25px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px 20px 0 0;
}

.modal-header.danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.modal-body {
  padding: 30px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  background: #f8f9fa;
  border-radius: 0 0 20px 20px;
}

/* أنماط التبويبات */
.modal-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.tab-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab-icon {
  font-size: 1.2rem;
}

/* أنماط النماذج */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

/* أنماط الأزرار */
.btn-save, .btn-cancel, .btn-delete, .btn-edit {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-save {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-cancel {
  background: #95a5a6;
  color: white;
}

.btn-cancel:hover {
  background: #7f8c8d;
}

.btn-delete {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

.btn-delete:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-edit {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.btn-edit:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

/* أنماط عرض تفاصيل المستخدم */
.user-details {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.user-profile-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-info h2 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.5rem;
}

.profile-email {
  color: #666;
  margin: 0 0 5px 0;
}

.profile-role {
  color: #667eea;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.detail-label {
  font-weight: 600;
  color: #333;
}

.detail-value {
  color: #666;
}

.notes-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 15px;
  border-left: 4px solid #f39c12;
}

.notes-section h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.permissions-summary {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  text-align: center;
}

.permissions-count {
  font-size: 1.2rem;
  font-weight: 600;
}

/* أنماط تأكيد الحذف */
.delete-warning {
  text-align: center;
  padding: 20px;
}

.warning-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.warning-text {
  color: #e74c3c;
  font-weight: 600;
  margin-top: 10px;
}

/* أنماط إدارة الأذونات */
.permissions-overview {
  margin-bottom: 25px;
}

.permissions-summary {
  display: flex;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  color: white;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-icon {
  font-size: 1.5rem;
}

.summary-text {
  font-weight: 600;
}

.permissions-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-category {
  border: 2px solid #e1e5e9;
  border-radius: 15px;
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.category-icon {
  font-size: 1.5rem;
  margin-left: 10px;
}

.category-name {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.category-count {
  background: #667eea;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.category-toggle {
  background: #27ae60;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.category-toggle:hover {
  background: #2ecc71;
  transform: translateY(-1px);
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
  padding: 20px;
}

.permission-item {
  border: 1px solid #e1e5e9;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.permission-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1);
}

.permission-label {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.permission-label:hover {
  background: rgba(102, 126, 234, 0.05);
}

.permission-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

.permission-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.permission-level {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.permission-level.admin {
  background: #e74c3c;
  color: white;
}

.permission-level.manager {
  background: #f39c12;
  color: white;
}

.permission-level.employee {
  background: #27ae60;
  color: white;
}

.critical-badge {
  background: #e74c3c;
  color: white;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

/* أنماط العمليات المجمعة */
.bulk-summary {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
}

.bulk-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.bulk-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px 20px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bulk-action-btn.activate {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
}

.bulk-action-btn.deactivate {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
}

.bulk-action-btn.export {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.bulk-action-btn.delete {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

.bulk-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}