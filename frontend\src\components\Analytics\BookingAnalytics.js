import React, { useState, useEffect } from 'react';
import { useNotifications } from '../UI/EnhancedNotifications';
import './BookingAnalytics.css';

// 📊 مكون تحليلات الحجوزات المتقدم
const BookingAnalytics = ({ bookings = [], type = 'all' }) => {
  const { info } = useNotifications();
  const [analytics, setAnalytics] = useState({});
  const [timeRange, setTimeRange] = useState('month');
  const [loading, setLoading] = useState(true);

  // 📈 حساب التحليلات
  useEffect(() => {
    const calculateAnalytics = () => {
      setLoading(true);
      
      // 📊 إحصائيات أساسية
      const totalBookings = bookings.length;
      const confirmedBookings = bookings.filter(b => b.status === 'confirmed').length;
      const pendingBookings = bookings.filter(b => b.status === 'pending').length;
      const cancelledBookings = bookings.filter(b => b.status === 'cancelled').length;
      const completedBookings = bookings.filter(b => b.status === 'completed').length;

      // 💰 تحليلات مالية
      const totalRevenue = bookings.reduce((sum, booking) => {
        return sum + (parseFloat(booking.amount) || 0);
      }, 0);

      const paidAmount = bookings.reduce((sum, booking) => {
        return sum + (parseFloat(booking.paidAmount) || 0);
      }, 0);

      const remainingAmount = totalRevenue - paidAmount;

      // 📅 تحليلات زمنية
      const currentDate = new Date();
      const timeRanges = {
        week: 7,
        month: 30,
        quarter: 90,
        year: 365
      };

      const daysBack = timeRanges[timeRange] || 30;
      const startDate = new Date(currentDate.getTime() - (daysBack * 24 * 60 * 60 * 1000));

      const recentBookings = bookings.filter(booking => {
        const bookingDate = new Date(booking.createdAt || booking.departureDate);
        return bookingDate >= startDate;
      });

      // 📈 معدل النمو
      const previousPeriodStart = new Date(startDate.getTime() - (daysBack * 24 * 60 * 60 * 1000));
      const previousBookings = bookings.filter(booking => {
        const bookingDate = new Date(booking.createdAt || booking.departureDate);
        return bookingDate >= previousPeriodStart && bookingDate < startDate;
      });

      const growthRate = previousBookings.length > 0 
        ? ((recentBookings.length - previousBookings.length) / previousBookings.length) * 100 
        : 0;

      // 🎯 معدل التحويل
      const conversionRate = totalBookings > 0 ? (confirmedBookings / totalBookings) * 100 : 0;

      // 📊 توزيع الحجوزات حسب النوع
      const bookingsByType = bookings.reduce((acc, booking) => {
        const bookingType = booking.type || type;
        acc[bookingType] = (acc[bookingType] || 0) + 1;
        return acc;
      }, {});

      // 💳 تحليل طرق الدفع
      const paymentMethods = bookings.reduce((acc, booking) => {
        const method = booking.paymentMethod || 'نقدي';
        acc[method] = (acc[method] || 0) + 1;
        return acc;
      }, {});

      // 🌍 تحليل الوجهات الشائعة
      const popularDestinations = bookings.reduce((acc, booking) => {
        const destination = booking.arrivalCity || booking.destination;
        if (destination) {
          acc[destination] = (acc[destination] || 0) + 1;
        }
        return acc;
      }, {});

      // 👥 تحليل العملاء
      const uniqueCustomers = new Set(bookings.map(b => b.customerPhone || b.customerEmail)).size;
      const repeatCustomers = bookings.length - uniqueCustomers;
      const customerRetentionRate = uniqueCustomers > 0 ? (repeatCustomers / uniqueCustomers) * 100 : 0;

      setAnalytics({
        basic: {
          totalBookings,
          confirmedBookings,
          pendingBookings,
          cancelledBookings,
          completedBookings,
          conversionRate
        },
        financial: {
          totalRevenue,
          paidAmount,
          remainingAmount,
          averageBookingValue: totalBookings > 0 ? totalRevenue / totalBookings : 0
        },
        growth: {
          recentBookings: recentBookings.length,
          previousBookings: previousBookings.length,
          growthRate
        },
        distribution: {
          bookingsByType,
          paymentMethods,
          popularDestinations: Object.entries(popularDestinations)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
        },
        customers: {
          uniqueCustomers,
          repeatCustomers,
          customerRetentionRate
        }
      });

      setLoading(false);
    };

    calculateAnalytics();
  }, [bookings, timeRange, type]);

  if (loading) {
    return (
      <div className="analytics-loading">
        <div className="loading-spinner-advanced"></div>
        <p>جاري تحليل البيانات...</p>
      </div>
    );
  }

  return (
    <div className="booking-analytics">
      {/* 🎛️ أدوات التحكم */}
      <div className="analytics-controls">
        <div className="analytics-header">
          <h2>📊 تحليلات الحجوزات</h2>
          <div className="time-range-selector">
            <label>الفترة الزمنية:</label>
            <select 
              value={timeRange} 
              onChange={(e) => setTimeRange(e.target.value)}
              className="time-range-select"
            >
              <option value="week">آخر أسبوع</option>
              <option value="month">آخر شهر</option>
              <option value="quarter">آخر 3 أشهر</option>
              <option value="year">آخر سنة</option>
            </select>
          </div>
        </div>
      </div>

      {/* 📊 الإحصائيات الأساسية */}
      <div className="analytics-grid">
        <div className="analytics-card primary">
          <div className="card-icon">📋</div>
          <div className="card-content">
            <h3>إجمالي الحجوزات</h3>
            <div className="card-value">{analytics.basic?.totalBookings || 0}</div>
            <div className="card-change positive">
              +{analytics.growth?.growthRate?.toFixed(1) || 0}%
            </div>
          </div>
        </div>

        <div className="analytics-card success">
          <div className="card-icon">✅</div>
          <div className="card-content">
            <h3>الحجوزات المؤكدة</h3>
            <div className="card-value">{analytics.basic?.confirmedBookings || 0}</div>
            <div className="card-percentage">
              {analytics.basic?.conversionRate?.toFixed(1) || 0}% معدل التحويل
            </div>
          </div>
        </div>

        <div className="analytics-card warning">
          <div className="card-icon">⏳</div>
          <div className="card-content">
            <h3>في الانتظار</h3>
            <div className="card-value">{analytics.basic?.pendingBookings || 0}</div>
          </div>
        </div>

        <div className="analytics-card info">
          <div className="card-icon">💰</div>
          <div className="card-content">
            <h3>إجمالي الإيرادات</h3>
            <div className="card-value">
              {(analytics.financial?.totalRevenue || 0).toLocaleString()} ر.س
            </div>
            <div className="card-subtitle">
              متوسط القيمة: {(analytics.financial?.averageBookingValue || 0).toLocaleString()} ر.س
            </div>
          </div>
        </div>
      </div>

      {/* 📈 الرسوم البيانية */}
      <div className="analytics-charts">
        <div className="chart-container">
          <h3>📊 توزيع الحجوزات حسب الحالة</h3>
          <div className="status-chart">
            <div className="status-bar">
              <div 
                className="status-segment confirmed" 
                style={{ width: `${(analytics.basic?.confirmedBookings / analytics.basic?.totalBookings) * 100 || 0}%` }}
              ></div>
              <div 
                className="status-segment pending" 
                style={{ width: `${(analytics.basic?.pendingBookings / analytics.basic?.totalBookings) * 100 || 0}%` }}
              ></div>
              <div 
                className="status-segment cancelled" 
                style={{ width: `${(analytics.basic?.cancelledBookings / analytics.basic?.totalBookings) * 100 || 0}%` }}
              ></div>
              <div 
                className="status-segment completed" 
                style={{ width: `${(analytics.basic?.completedBookings / analytics.basic?.totalBookings) * 100 || 0}%` }}
              ></div>
            </div>
            <div className="status-legend">
              <div className="legend-item">
                <span className="legend-color confirmed"></span>
                <span>مؤكد ({analytics.basic?.confirmedBookings || 0})</span>
              </div>
              <div className="legend-item">
                <span className="legend-color pending"></span>
                <span>معلق ({analytics.basic?.pendingBookings || 0})</span>
              </div>
              <div className="legend-item">
                <span className="legend-color cancelled"></span>
                <span>ملغي ({analytics.basic?.cancelledBookings || 0})</span>
              </div>
              <div className="legend-item">
                <span className="legend-color completed"></span>
                <span>مكتمل ({analytics.basic?.completedBookings || 0})</span>
              </div>
            </div>
          </div>
        </div>

        <div className="chart-container">
          <h3>🌍 الوجهات الأكثر شعبية</h3>
          <div className="destinations-chart">
            {analytics.distribution?.popularDestinations?.map(([destination, count], index) => (
              <div key={destination} className="destination-item">
                <div className="destination-name">{destination}</div>
                <div className="destination-bar">
                  <div 
                    className="destination-fill" 
                    style={{ 
                      width: `${(count / analytics.distribution.popularDestinations[0][1]) * 100}%`,
                      backgroundColor: `hsl(${index * 60}, 70%, 60%)`
                    }}
                  ></div>
                </div>
                <div className="destination-count">{count}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 💡 رؤى وتوصيات */}
      <div className="analytics-insights">
        <h3>💡 رؤى وتوصيات</h3>
        <div className="insights-grid">
          {analytics.basic?.conversionRate < 50 && (
            <div className="insight-card warning">
              <div className="insight-icon">⚠️</div>
              <div className="insight-content">
                <h4>معدل تحويل منخفض</h4>
                <p>معدل التحويل الحالي {analytics.basic.conversionRate.toFixed(1)}%. يُنصح بمراجعة عملية التأكيد.</p>
              </div>
            </div>
          )}
          
          {analytics.financial?.remainingAmount > analytics.financial?.paidAmount && (
            <div className="insight-card info">
              <div className="insight-icon">💳</div>
              <div className="insight-content">
                <h4>مبالغ معلقة</h4>
                <p>يوجد {analytics.financial.remainingAmount.toLocaleString()} ر.س من المبالغ المعلقة.</p>
              </div>
            </div>
          )}
          
          {analytics.growth?.growthRate > 20 && (
            <div className="insight-card success">
              <div className="insight-icon">📈</div>
              <div className="insight-content">
                <h4>نمو ممتاز</h4>
                <p>نمو بنسبة {analytics.growth.growthRate.toFixed(1)}% مقارنة بالفترة السابقة!</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookingAnalytics;
