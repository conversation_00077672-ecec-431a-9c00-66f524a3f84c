// ⚡ نظام تحسين الأداء المتقدم

// 🎯 تحسين التحميل البطيء للمكونات
export const lazyLoadComponent = (importFunc, fallback = null) => {
  const LazyComponent = React.lazy(importFunc);
  
  return (props) => (
    <React.Suspense fallback={fallback || <ComponentLoader />}>
      <LazyComponent {...props} />
    </React.Suspense>
  );
};

// 🔄 مكون التحميل المحسن
const ComponentLoader = () => (
  <div className="component-loader">
    <div className="loader-spinner"></div>
    <p>جاري التحميل...</p>
  </div>
);

// 📊 مراقب الأداء
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      loadTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      networkRequests: [],
      errors: []
    };
    this.observers = [];
    this.init();
  }

  init() {
    // 📈 مراقبة أداء التحميل
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0];
        this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
        this.notifyObservers('loadTime', this.metrics.loadTime);
      });
    }

    // 🔍 مراقبة استخدام الذاكرة
    if ('memory' in performance) {
      setInterval(() => {
        this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
        this.notifyObservers('memoryUsage', this.metrics.memoryUsage);
      }, 5000);
    }

    // 🌐 مراقبة طلبات الشبكة
    this.interceptNetworkRequests();

    // ❌ مراقبة الأخطاء
    window.addEventListener('error', (error) => {
      this.metrics.errors.push({
        message: error.message,
        filename: error.filename,
        lineno: error.lineno,
        timestamp: Date.now()
      });
      this.notifyObservers('error', error);
    });
  }

  interceptNetworkRequests() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        
        this.metrics.networkRequests.push({
          url: args[0],
          method: args[1]?.method || 'GET',
          duration: endTime - startTime,
          status: response.status,
          timestamp: Date.now()
        });
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        this.metrics.networkRequests.push({
          url: args[0],
          method: args[1]?.method || 'GET',
          duration: endTime - startTime,
          error: error.message,
          timestamp: Date.now()
        });
        throw error;
      }
    };
  }

  subscribe(callback) {
    this.observers.push(callback);
  }

  unsubscribe(callback) {
    this.observers = this.observers.filter(obs => obs !== callback);
  }

  notifyObservers(metric, value) {
    this.observers.forEach(callback => callback(metric, value));
  }

  getMetrics() {
    return { ...this.metrics };
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: this.getMetrics(),
      recommendations: this.generateRecommendations()
    };
    
    console.group('📊 تقرير الأداء');
    console.table(report.metrics);
    console.log('💡 التوصيات:', report.recommendations);
    console.groupEnd();
    
    return report;
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (this.metrics.loadTime > 3000) {
      recommendations.push('⚠️ وقت التحميل بطيء - فكر في تحسين الصور وتقليل حجم الملفات');
    }
    
    if (this.metrics.memoryUsage > 50 * 1024 * 1024) { // 50MB
      recommendations.push('🧠 استخدام ذاكرة عالي - تحقق من تسريبات الذاكرة');
    }
    
    const slowRequests = this.metrics.networkRequests.filter(req => req.duration > 2000);
    if (slowRequests.length > 0) {
      recommendations.push(`🌐 ${slowRequests.length} طلبات شبكة بطيئة - فكر في التخزين المؤقت`);
    }
    
    if (this.metrics.errors.length > 0) {
      recommendations.push(`❌ ${this.metrics.errors.length} أخطاء JavaScript - يجب إصلاحها`);
    }
    
    return recommendations;
  }
}

// 🎯 تحسين الصور
export const optimizeImage = (src, options = {}) => {
  const {
    width = 'auto',
    height = 'auto',
    quality = 80,
    format = 'webp',
    lazy = true
  } = options;

  // إنشاء URL محسن للصورة
  const optimizedSrc = `${src}?w=${width}&h=${height}&q=${quality}&f=${format}`;
  
  return {
    src: optimizedSrc,
    loading: lazy ? 'lazy' : 'eager',
    decoding: 'async'
  };
};

// 📦 تجميع الطلبات
export class RequestBatcher {
  constructor(batchSize = 10, delay = 100) {
    this.batchSize = batchSize;
    this.delay = delay;
    this.queue = [];
    this.processing = false;
  }

  add(request) {
    return new Promise((resolve, reject) => {
      this.queue.push({ request, resolve, reject });
      this.processBatch();
    });
  }

  async processBatch() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    
    // انتظار لتجميع المزيد من الطلبات
    await new Promise(resolve => setTimeout(resolve, this.delay));
    
    const batch = this.queue.splice(0, this.batchSize);
    
    try {
      const requests = batch.map(item => item.request());
      const results = await Promise.allSettled(requests);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          batch[index].resolve(result.value);
        } else {
          batch[index].reject(result.reason);
        }
      });
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
    
    this.processing = false;
    
    // معالجة الدفعة التالية إذا كانت موجودة
    if (this.queue.length > 0) {
      this.processBatch();
    }
  }
}

// 💾 إدارة التخزين المؤقت المتقدم
export class CacheManager {
  constructor(maxSize = 100, ttl = 300000) { // 5 دقائق افتراضي
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.accessTimes = new Map();
  }

  set(key, value, customTTL = null) {
    const ttl = customTTL || this.ttl;
    const expiryTime = Date.now() + ttl;
    
    // إزالة العناصر المنتهية الصلاحية
    this.cleanup();
    
    // إزالة أقدم عنصر إذا تم الوصول للحد الأقصى
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.getOldestKey();
      this.delete(oldestKey);
    }
    
    this.cache.set(key, { value, expiryTime });
    this.accessTimes.set(key, Date.now());
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() > item.expiryTime) {
      this.delete(key);
      return null;
    }
    
    this.accessTimes.set(key, Date.now());
    return item.value;
  }

  delete(key) {
    this.cache.delete(key);
    this.accessTimes.delete(key);
  }

  clear() {
    this.cache.clear();
    this.accessTimes.clear();
  }

  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiryTime) {
        this.delete(key);
      }
    }
  }

  getOldestKey() {
    let oldestKey = null;
    let oldestTime = Infinity;
    
    for (const [key, time] of this.accessTimes.entries()) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }
    
    return oldestKey;
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate()
    };
  }

  calculateHitRate() {
    // حساب معدل النجاح (يحتاج تتبع إضافي)
    return 0; // مبسط للمثال
  }
}

// 🔄 تحسين إعادة الرسم
export const useOptimizedRender = (dependencies = []) => {
  const [renderKey, setRenderKey] = React.useState(0);
  const lastDepsRef = React.useRef(dependencies);
  
  React.useEffect(() => {
    const hasChanged = dependencies.some((dep, index) => 
      dep !== lastDepsRef.current[index]
    );
    
    if (hasChanged) {
      setRenderKey(prev => prev + 1);
      lastDepsRef.current = dependencies;
    }
  }, dependencies);
  
  return renderKey;
};

// 📱 تحسين الأجهزة المحمولة
export const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
};

export const getDeviceInfo = () => {
  return {
    isMobile: isMobileDevice(),
    isOnline: navigator.onLine,
    connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection,
    memory: navigator.deviceMemory,
    cores: navigator.hardwareConcurrency
  };
};

// 🎯 تحسين التمرير
export const useVirtualScrolling = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );
  
  const visibleItems = items.slice(visibleStart, visibleEnd);
  const offsetY = visibleStart * itemHeight;
  
  return {
    visibleItems,
    offsetY,
    totalHeight: items.length * itemHeight,
    onScroll: (e) => setScrollTop(e.target.scrollTop)
  };
};

// 🚀 تهيئة نظام تحسين الأداء
export const initPerformanceOptimization = () => {
  const monitor = new PerformanceMonitor();
  const cache = new CacheManager();
  const batcher = new RequestBatcher();
  
  // تقرير دوري عن الأداء
  setInterval(() => {
    monitor.generateReport();
  }, 60000); // كل دقيقة
  
  return {
    monitor,
    cache,
    batcher
  };
};

export default {
  PerformanceMonitor,
  CacheManager,
  RequestBatcher,
  lazyLoadComponent,
  optimizeImage,
  useOptimizedRender,
  useVirtualScrolling,
  getDeviceInfo,
  initPerformanceOptimization
};
