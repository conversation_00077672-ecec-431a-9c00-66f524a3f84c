import React, { useState } from 'react';
import './ServiceManager.css';

const ServiceManager = ({ services, onServicesChange }) => {
  const [newService, setNewService] = useState('');
  const [showAddService, setShowAddService] = useState(false);

  const predefinedServices = [
    'تذاكر طيران',
    'حجوزات فنادق',
    'خدمات النقل',
    'رحلات سياحية',
    'تأشيرات سياحية',
    'تأشيرات عمل',
    'تأشيرات دراسة',
    'حج وعمرة',
    'سياحة داخلية',
    'سياحة خارجية',
    'تأمين سفر',
    'خدمات المطار',
    'استقبال ووداع',
    'حجز سيارات',
    'جولات سياحية',
    'خدمات الترجمة',
    'خدمات التصوير',
    'تنظيم المؤتمرات',
    'حجز قاعات',
    'خدمات الضيافة'
  ];

  const handleAddService = () => {
    if (newService.trim() && !services.includes(newService.trim())) {
      onServicesChange([...services, newService.trim()]);
      setNewService('');
      setShowAddService(false);
    }
  };

  const handleRemoveService = (serviceToRemove) => {
    onServicesChange(services.filter(service => service !== serviceToRemove));
  };

  const handleAddPredefinedService = (service) => {
    if (!services.includes(service)) {
      onServicesChange([...services, service]);
    }
  };

  return (
    <div className="service-manager">
      <div className="current-services">
        <label>الخدمات المحددة:</label>
        <div className="services-list">
          {services.map((service, index) => (
            <div key={index} className="service-item">
              <span>{service}</span>
              <button
                type="button"
                className="remove-service-btn"
                onClick={() => handleRemoveService(service)}
              >
                ×
              </button>
            </div>
          ))}
          {services.length === 0 && (
            <p className="no-services">لم يتم تحديد خدمات بعد</p>
          )}
        </div>
      </div>

      <div className="add-service-section">
        <div className="predefined-services">
          <label>الخدمات المتاحة:</label>
          <div className="predefined-services-grid">
            {predefinedServices
              .filter(service => !services.includes(service))
              .map((service, index) => (
                <button
                  key={index}
                  type="button"
                  className="predefined-service-btn"
                  onClick={() => handleAddPredefinedService(service)}
                >
                  + {service}
                </button>
              ))}
          </div>
        </div>

        <div className="custom-service">
          {!showAddService ? (
            <button
              type="button"
              className="add-custom-service-btn"
              onClick={() => setShowAddService(true)}
            >
              + إضافة خدمة مخصصة
            </button>
          ) : (
            <div className="custom-service-form">
              <input
                type="text"
                value={newService}
                onChange={(e) => setNewService(e.target.value)}
                placeholder="اسم الخدمة الجديدة"
                onKeyPress={(e) => e.key === 'Enter' && handleAddService()}
              />
              <div className="custom-service-actions">
                <button
                  type="button"
                  className="save-custom-service-btn"
                  onClick={handleAddService}
                  disabled={!newService.trim()}
                >
                  إضافة
                </button>
                <button
                  type="button"
                  className="cancel-custom-service-btn"
                  onClick={() => {
                    setShowAddService(false);
                    setNewService('');
                  }}
                >
                  إلغاء
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServiceManager;