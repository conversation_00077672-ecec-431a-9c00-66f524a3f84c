import React, { useState } from 'react';
import { useTheme } from './ThemeProvider';
import { ThemeSelector } from './ThemeProvider';
import ThemeSettings from './ThemeSettings';
import './ThemeToggle.css';

// 🎨 مكون زر تبديل الثيم السريع
const ThemeToggle = ({ showLabel = true, size = 'medium' }) => {
  const { current, isAutoMode, actions } = useTheme();
  const [showSelector, setShowSelector] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // 🔄 تبديل سريع بين النهاري والليلي
  const quickToggle = () => {
    if (isAutoMode) {
      actions.toggleAutoMode();
    }

    const newTheme = current.id === 'dark' ? 'light' : 'dark';
    actions.changeTheme(newTheme);
  };

  return (
    <>
      {/* 🎯 زر التبديل الرئيسي */}
      <div className={`theme-toggle-container ${size}`}>
        <button
          className={`theme-toggle-btn ${current.id}`}
          onClick={quickToggle}
          title={`تبديل إلى ${current.id === 'dark' ? 'الوضع النهاري' : 'الوضع الليلي'}`}
        >
          <span className="theme-icon">{current.icon}</span>
          {showLabel && (
            <span className="theme-label">{current.name}</span>
          )}
          {isAutoMode && (
            <span className="auto-indicator" title="الوضع التلقائي مفعل">
              🤖
            </span>
          )}
        </button>

        {/* 🎨 زر اختيار الثيمات */}
        <button
          className="theme-selector-btn"
          onClick={() => setShowSelector(!showSelector)}
          title="اختيار ثيم"
        >
          🎨
        </button>

        {/* ⚙️ زر الإعدادات */}
        <button
          className="theme-settings-btn"
          onClick={() => setShowSettings(true)}
          title="إعدادات الثيم"
        >
          ⚙️
        </button>

        {/* 📋 قائمة الثيمات المنسدلة */}
        {showSelector && (
          <div className="theme-dropdown">
            <div className="theme-dropdown-header">
              <h4>🎨 اختيار الثيم</h4>
              <button
                className="close-dropdown"
                onClick={() => setShowSelector(false)}
              >
                ✕
              </button>
            </div>
            <ThemeSelector className="compact" />
          </div>
        )}
      </div>

      {/* 🛠️ مودال الإعدادات */}
      <ThemeSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* 🌫️ خلفية لإغلاق القائمة */}
      {showSelector && (
        <div
          className="theme-dropdown-overlay"
          onClick={() => setShowSelector(false)}
        />
      )}
    </>
  );
};

// 🌟 مكون زر الثيم المبسط
export const SimpleThemeToggle = () => {
  const { current, actions } = useTheme();

  const quickToggle = () => {
    const newTheme = current.id === 'dark' ? 'light' : 'dark';
    actions.changeTheme(newTheme);
  };

  return (
    <button
      className="simple-theme-toggle"
      onClick={quickToggle}
      title={`تبديل إلى ${current.id === 'dark' ? 'الوضع النهاري' : 'الوضع الليلي'}`}
    >
      {current.icon}
    </button>
  );
};

// 🎭 مكون عرض الثيم الحالي
export const ThemeDisplay = () => {
  const { current, isAutoMode } = useTheme();

  return (
    <div className="theme-display">
      <div className="theme-info">
        <span className="theme-icon-display">{current.icon}</span>
        <div className="theme-text">
          <span className="theme-name-display">{current.name}</span>
          {isAutoMode && (
            <span className="auto-badge">تلقائي</span>
          )}
        </div>
      </div>
      <div className="theme-colors-display">
        {Object.values(current.colors).slice(0, 4).map((color, index) => (
          <div
            key={index}
            className="color-indicator"
            style={{ backgroundColor: color }}
          />
        ))}
      </div>
    </div>
  );
};

export default ThemeToggle;
