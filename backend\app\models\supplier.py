"""
نموذج الموردين
Supplier Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Decimal, ForeignKey, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.models.base import BaseModel

class SupplierType(PyEnum):
    """أنواع الموردين"""
    AIRLINE = "airline"           # شركة طيران
    HOTEL = "hotel"              # فندق
    CAR_RENTAL = "car_rental"    # تأجير سيارات
    TOUR_OPERATOR = "tour_operator"  # منظم رحلات
    INSURANCE = "insurance"      # تأمين
    VISA_SERVICE = "visa_service"  # خدمات تأشيرات
    TRANSPORT = "transport"      # نقل
    OTHER = "other"              # أخرى

class SupplierStatus(PyEnum):
    """حالة المورد"""
    ACTIVE = "active"      # نشط
    INACTIVE = "inactive"  # غير نشط
    BLOCKED = "blocked"    # محظور
    PREFERRED = "preferred"  # مورد مفضل

class Supplier(BaseModel):
    """
    نموذج الموردين
    """
    __tablename__ = "suppliers"
    
    # المعلومات الأساسية
    supplier_code = Column(String(20), unique=True, nullable=False, index=True)
    supplier_type = Column(Enum(SupplierType), nullable=False)
    status = Column(Enum(SupplierStatus), nullable=False, default=SupplierStatus.ACTIVE)
    
    # معلومات الشركة
    company_name = Column(String(100), nullable=False)
    trade_name = Column(String(100), nullable=True)
    registration_number = Column(String(50), nullable=True)
    tax_number = Column(String(50), nullable=True)
    
    # معلومات الاتصال
    email = Column(String(100), nullable=True, index=True)
    phone = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    website = Column(String(100), nullable=True)
    
    # العنوان
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # معلومات مالية
    credit_limit = Column(Decimal(15, 2), default=0.00)
    current_balance = Column(Decimal(15, 2), default=0.00)
    payment_terms = Column(Integer, default=30)  # شروط الدفع بالأيام
    commission_rate = Column(Decimal(5, 2), default=0.00)  # معدل العمولة
    
    # معلومات بنكية
    bank_name = Column(String(100), nullable=True)
    bank_account = Column(String(50), nullable=True)
    iban = Column(String(50), nullable=True)
    swift_code = Column(String(20), nullable=True)
    
    # تقييم المورد
    rating = Column(Decimal(3, 2), default=0.00)  # من 0 إلى 5
    total_bookings = Column(Integer, default=0)
    successful_bookings = Column(Integer, default=0)
    
    # معلومات إضافية
    preferred_currency = Column(String(3), default='USD')
    time_zone = Column(String(50), nullable=True)
    
    # العلاقات
    contacts = relationship("SupplierContact", back_populates="supplier")
    contracts = relationship("SupplierContract", back_populates="supplier")
    bookings = relationship("Booking", back_populates="supplier")
    invoices = relationship("PurchaseInvoice", back_populates="supplier")
    payments = relationship("SupplierPayment", back_populates="supplier")
    
    @property
    def success_rate(self):
        """معدل النجاح"""
        if self.total_bookings == 0:
            return 0
        return (self.successful_bookings / self.total_bookings) * 100
    
    def __repr__(self):
        return f"<Supplier(code='{self.supplier_code}', name='{self.company_name}')>"

class SupplierContact(BaseModel):
    """
    جهات اتصال المورد
    """
    __tablename__ = "supplier_contacts"
    
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    contact_type = Column(String(20), nullable=False)  # sales, support, finance, management
    
    name = Column(String(100), nullable=False)
    title = Column(String(50), nullable=True)
    department = Column(String(50), nullable=True)
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    
    is_primary = Column(Boolean, default=False)
    
    # العلاقات
    supplier = relationship("Supplier", back_populates="contacts")
    
    def __repr__(self):
        return f"<SupplierContact(name='{self.name}', type='{self.contact_type}')>"

class SupplierContract(BaseModel):
    """
    عقود الموردين
    """
    __tablename__ = "supplier_contracts"
    
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    contract_number = Column(String(50), unique=True, nullable=False)
    contract_type = Column(String(50), nullable=False)  # exclusive, non-exclusive, preferred
    
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=True)
    auto_renewal = Column(Boolean, default=False)
    
    # شروط العقد
    commission_rate = Column(Decimal(5, 2), nullable=True)
    payment_terms = Column(Integer, nullable=True)  # أيام الدفع
    credit_limit = Column(Decimal(15, 2), nullable=True)
    
    # ملفات العقد
    contract_file = Column(String(255), nullable=True)
    
    # حالة العقد
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    supplier = relationship("Supplier", back_populates="contracts")
    
    @property
    def is_expired(self):
        """هل العقد منتهي الصلاحية"""
        if not self.end_date:
            return False
        from datetime import datetime
        return datetime.now() > self.end_date
    
    def __repr__(self):
        return f"<SupplierContract(number='{self.contract_number}', supplier='{self.supplier.company_name}')>"

class SupplierService(BaseModel):
    """
    خدمات المورد
    """
    __tablename__ = "supplier_services"
    
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    service_type = Column(String(50), nullable=False)  # flight, hotel, car, tour, etc.
    service_name = Column(String(100), nullable=False)
    service_code = Column(String(50), nullable=True)
    
    description = Column(Text, nullable=True)
    base_price = Column(Decimal(10, 2), nullable=True)
    currency = Column(String(3), default='USD')
    
    # توفر الخدمة
    is_available = Column(Boolean, default=True)
    availability_start = Column(DateTime, nullable=True)
    availability_end = Column(DateTime, nullable=True)
    
    # العلاقات
    supplier = relationship("Supplier")
    
    def __repr__(self):
        return f"<SupplierService(name='{self.service_name}', type='{self.service_type}')>"

class SupplierRating(BaseModel):
    """
    تقييمات الموردين
    """
    __tablename__ = "supplier_ratings"
    
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=True)
    
    # التقييم
    overall_rating = Column(Decimal(3, 2), nullable=False)  # من 0 إلى 5
    service_quality = Column(Decimal(3, 2), nullable=True)
    response_time = Column(Decimal(3, 2), nullable=True)
    reliability = Column(Decimal(3, 2), nullable=True)
    value_for_money = Column(Decimal(3, 2), nullable=True)
    
    # التعليقات
    comment = Column(Text, nullable=True)
    
    # معلومات المقيم
    rated_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # العلاقات
    supplier = relationship("Supplier")
    booking = relationship("Booking")
    user = relationship("User")
    
    def __repr__(self):
        return f"<SupplierRating(supplier_id={self.supplier_id}, rating={self.overall_rating})>"