import React, { useState, useEffect } from 'react';
import './DatabaseMonitor.css';

const DatabaseMonitor = () => {
  const [realTimeStats, setRealTimeStats] = useState({
    activeConnections: 12,
    queriesPerSecond: 45,
    avgResponseTime: 125,
    cpuUsage: 35,
    memoryUsage: 68,
    diskUsage: 42,
    cacheHitRatio: 94.5,
    slowQueries: 3
  });

  const [queryHistory, setQueryHistory] = useState([
    { time: '14:30:25', query: 'SELECT * FROM customers WHERE status = "active"', duration: 45, status: 'success' },
    { time: '14:30:22', query: 'UPDATE bookings SET status = "confirmed" WHERE id = 123', duration: 23, status: 'success' },
    { time: '14:30:18', query: 'INSERT INTO invoices (customer_id, amount) VALUES (456, 1500)', duration: 12, status: 'success' },
    { time: '14:30:15', query: 'SELECT COUNT(*) FROM payments WHERE date >= "2024-01-01"', duration: 156, status: 'slow' },
    { time: '14:30:10', query: 'DELETE FROM temp_data WHERE created_at < NOW() - INTERVAL 1 DAY', duration: 89, status: 'success' }
  ]);

  const [tableStats, setTableStats] = useState([
    { name: 'customers', records: 1247, size: '2.3 MB', lastUpdate: '2024-01-15 14:25:30' },
    { name: 'bookings', records: 3456, size: '5.7 MB', lastUpdate: '2024-01-15 14:30:15' },
    { name: 'invoices', records: 2189, size: '3.1 MB', lastUpdate: '2024-01-15 14:28:45' },
    { name: 'payments', records: 1876, size: '2.8 MB', lastUpdate: '2024-01-15 14:29:20' },
    { name: 'users', records: 45, size: '0.1 MB', lastUpdate: '2024-01-15 12:15:30' }
  ]);

  // محاكاة تحديث البيانات في الوقت الفعلي
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeStats(prev => ({
        ...prev,
        activeConnections: Math.max(5, prev.activeConnections + Math.floor(Math.random() * 6) - 3),
        queriesPerSecond: Math.max(10, prev.queriesPerSecond + Math.floor(Math.random() * 20) - 10),
        avgResponseTime: Math.max(50, prev.avgResponseTime + Math.floor(Math.random() * 40) - 20),
        cpuUsage: Math.max(10, Math.min(90, prev.cpuUsage + Math.floor(Math.random() * 10) - 5)),
        memoryUsage: Math.max(20, Math.min(95, prev.memoryUsage + Math.floor(Math.random() * 8) - 4)),
        cacheHitRatio: Math.max(80, Math.min(99, prev.cacheHitRatio + (Math.random() * 2) - 1))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (value, thresholds) => {
    if (value < thresholds.good) return '#27ae60';
    if (value < thresholds.warning) return '#f39c12';
    return '#e74c3c';
  };

  const getQueryStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'slow': return '⚠️';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  return (
    <div className="database-monitor">
      <h2>📊 مراقبة قاعدة البيانات المباشرة</h2>
      
      {/* الإحصائيات المباشرة */}
      <div className="real-time-stats">
        <h3>📈 الإحصائيات المباشرة</h3>
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-header">
              <span className="stat-icon">🔗</span>
              <span className="stat-title">الاتصالات النشطة</span>
            </div>
            <div className="stat-value">{realTimeStats.activeConnections}</div>
            <div className="stat-trend positive">+2 من آخر دقيقة</div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <span className="stat-icon">⚡</span>
              <span className="stat-title">استعلامات/ثانية</span>
            </div>
            <div className="stat-value">{realTimeStats.queriesPerSecond}</div>
            <div className="stat-trend negative">-5 من آخر دقيقة</div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <span className="stat-icon">⏱️</span>
              <span className="stat-title">متوسط وقت الاستجابة</span>
            </div>
            <div className="stat-value">{realTimeStats.avgResponseTime}ms</div>
            <div className="stat-trend positive">-12ms من آخر دقيقة</div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <span className="stat-icon">💾</span>
              <span className="stat-title">نسبة إصابة التخزين المؤقت</span>
            </div>
            <div className="stat-value">{realTimeStats.cacheHitRatio.toFixed(1)}%</div>
            <div className="stat-trend positive">+0.3% من آخر دقيقة</div>
          </div>
        </div>
      </div>

      {/* مؤشرات الأداء */}
      <div className="performance-indicators">
        <h3>🎯 مؤشرات الأداء</h3>
        <div className="indicators-grid">
          <div className="indicator">
            <div className="indicator-header">
              <span>🖥️ استخدام المعالج</span>
              <span className="indicator-value" style={{ color: getStatusColor(realTimeStats.cpuUsage, { good: 50, warning: 80 }) }}>
                {realTimeStats.cpuUsage}%
              </span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ 
                  width: `${realTimeStats.cpuUsage}%`,
                  backgroundColor: getStatusColor(realTimeStats.cpuUsage, { good: 50, warning: 80 })
                }}
              ></div>
            </div>
          </div>

          <div className="indicator">
            <div className="indicator-header">
              <span>🧠 استخدام الذاكرة</span>
              <span className="indicator-value" style={{ color: getStatusColor(realTimeStats.memoryUsage, { good: 60, warning: 85 }) }}>
                {realTimeStats.memoryUsage}%
              </span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ 
                  width: `${realTimeStats.memoryUsage}%`,
                  backgroundColor: getStatusColor(realTimeStats.memoryUsage, { good: 60, warning: 85 })
                }}
              ></div>
            </div>
          </div>

          <div className="indicator">
            <div className="indicator-header">
              <span>💽 استخدام القرص</span>
              <span className="indicator-value" style={{ color: getStatusColor(realTimeStats.diskUsage, { good: 70, warning: 90 }) }}>
                {realTimeStats.diskUsage}%
              </span>
            </div>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ 
                  width: `${realTimeStats.diskUsage}%`,
                  backgroundColor: getStatusColor(realTimeStats.diskUsage, { good: 70, warning: 90 })
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* آخر الاستعلامات */}
      <div className="recent-queries">
        <h3>📝 آخر الاستعلامات</h3>
        <div className="queries-table">
          <div className="table-header">
            <span>الوقت</span>
            <span>الاستعلام</span>
            <span>المدة</span>
            <span>الحالة</span>
          </div>
          {queryHistory.map((query, index) => (
            <div key={index} className={`table-row ${query.status}`}>
              <span className="query-time">{query.time}</span>
              <span className="query-text" title={query.query}>
                {query.query.length > 50 ? query.query.substring(0, 50) + '...' : query.query}
              </span>
              <span className="query-duration">{query.duration}ms</span>
              <span className="query-status">
                {getQueryStatusIcon(query.status)}
                {query.status === 'slow' ? 'بطيء' : query.status === 'error' ? 'خطأ' : 'نجح'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* إحصائيات الجداول */}
      <div className="table-statistics">
        <h3>🗂️ إحصائيات الجداول</h3>
        <div className="tables-grid">
          {tableStats.map((table, index) => (
            <div key={index} className="table-card">
              <div className="table-name">{table.name}</div>
              <div className="table-info">
                <div className="table-stat">
                  <span className="stat-label">السجلات:</span>
                  <span className="stat-value">{table.records.toLocaleString('ar-SA')}</span>
                </div>
                <div className="table-stat">
                  <span className="stat-label">الحجم:</span>
                  <span className="stat-value">{table.size}</span>
                </div>
                <div className="table-stat">
                  <span className="stat-label">آخر تحديث:</span>
                  <span className="stat-value">{new Date(table.lastUpdate).toLocaleString('ar-SA')}</span>
                </div>
              </div>
              <div className="table-actions">
                <button className="btn-table-action">📊 تحليل</button>
                <button className="btn-table-action">🔧 تحسين</button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* تنبيهات النظام */}
      <div className="system-alerts">
        <h3>🚨 تنبيهات النظام</h3>
        <div className="alerts-list">
          {realTimeStats.slowQueries > 0 && (
            <div className="alert warning">
              <span className="alert-icon">⚠️</span>
              <span className="alert-message">
                يوجد {realTimeStats.slowQueries} استعلامات بطيئة في آخر 5 دقائق
              </span>
              <span className="alert-time">منذ دقيقتين</span>
            </div>
          )}
          
          {realTimeStats.memoryUsage > 85 && (
            <div className="alert error">
              <span className="alert-icon">🔴</span>
              <span className="alert-message">
                استخدام الذاكرة مرتفع ({realTimeStats.memoryUsage}%)
              </span>
              <span className="alert-time">منذ 30 ثانية</span>
            </div>
          )}
          
          <div className="alert success">
            <span className="alert-icon">✅</span>
            <span className="alert-message">
              تم إنشاء النسخة الاحتياطية التلقائية بنجاح
            </span>
            <span className="alert-time">منذ ساعة</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatabaseMonitor;
