import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';

// 🏪 نظام إدارة الحالة المحسن

// 🎯 أنواع الإجراءات
export const ActionTypes = {
  // 📋 الحجوزات
  SET_BOOKINGS: 'SET_BOOKINGS',
  ADD_BOOKING: 'ADD_BOOKING',
  UPDATE_BOOKING: 'UPDATE_BOOKING',
  DELETE_BOOKING: 'DELETE_BOOKING',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  
  // 👥 العملاء
  SET_CUSTOMERS: 'SET_CUSTOMERS',
  ADD_CUSTOMER: 'ADD_CUSTOMER',
  UPDATE_CUSTOMER: 'UPDATE_CUSTOMER',
  
  // 🎨 واجهة المستخدم
  SET_THEME: 'SET_THEME',
  SET_SIDEBAR_OPEN: 'SET_SIDEBAR_OPEN',
  SET_NOTIFICATIONS: 'SET_NOTIFICATIONS',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  
  // 📊 التحليلات
  SET_ANALYTICS: 'SET_ANALYTICS',
  UPDATE_STATS: 'UPDATE_STATS',
  
  // ⚙️ الإعدادات
  SET_SETTINGS: 'SET_SETTINGS',
  UPDATE_SETTING: 'UPDATE_SETTING'
};

// 🏗️ الحالة الأولية
const initialState = {
  // 📋 بيانات الحجوزات
  bookings: {
    items: [],
    loading: false,
    error: null,
    filters: {
      status: 'all',
      type: 'all',
      dateRange: null
    },
    pagination: {
      page: 1,
      limit: 20,
      total: 0
    }
  },
  
  // 👥 بيانات العملاء
  customers: {
    items: [],
    loading: false,
    error: null
  },
  
  // 🎨 واجهة المستخدم
  ui: {
    theme: 'light',
    sidebarOpen: true,
    notifications: [],
    modals: {
      bookingDetails: { open: false, data: null },
      customerForm: { open: false, data: null }
    }
  },
  
  // 📊 التحليلات
  analytics: {
    stats: {},
    charts: {},
    loading: false
  },
  
  // ⚙️ الإعدادات
  settings: {
    language: 'ar',
    currency: 'SAR',
    dateFormat: 'DD/MM/YYYY',
    notifications: {
      email: true,
      push: true,
      sms: false
    },
    performance: {
      cacheEnabled: true,
      lazyLoading: true,
      virtualScrolling: true
    }
  }
};

// 🔄 مخفض الحالة المحسن
const optimizedReducer = (state, action) => {
  switch (action.type) {
    // 📋 إجراءات الحجوزات
    case ActionTypes.SET_BOOKINGS:
      return {
        ...state,
        bookings: {
          ...state.bookings,
          items: action.payload,
          loading: false,
          error: null
        }
      };
      
    case ActionTypes.ADD_BOOKING:
      return {
        ...state,
        bookings: {
          ...state.bookings,
          items: [action.payload, ...state.bookings.items]
        }
      };
      
    case ActionTypes.UPDATE_BOOKING:
      return {
        ...state,
        bookings: {
          ...state.bookings,
          items: state.bookings.items.map(booking =>
            booking.id === action.payload.id ? { ...booking, ...action.payload } : booking
          )
        }
      };
      
    case ActionTypes.DELETE_BOOKING:
      return {
        ...state,
        bookings: {
          ...state.bookings,
          items: state.bookings.items.filter(booking => booking.id !== action.payload)
        }
      };
      
    case ActionTypes.SET_LOADING:
      return {
        ...state,
        bookings: {
          ...state.bookings,
          loading: action.payload
        }
      };
      
    case ActionTypes.SET_ERROR:
      return {
        ...state,
        bookings: {
          ...state.bookings,
          error: action.payload,
          loading: false
        }
      };
      
    // 👥 إجراءات العملاء
    case ActionTypes.SET_CUSTOMERS:
      return {
        ...state,
        customers: {
          ...state.customers,
          items: action.payload,
          loading: false,
          error: null
        }
      };
      
    case ActionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: {
          ...state.customers,
          items: [action.payload, ...state.customers.items]
        }
      };
      
    // 🎨 إجراءات واجهة المستخدم
    case ActionTypes.SET_THEME:
      return {
        ...state,
        ui: {
          ...state.ui,
          theme: action.payload
        }
      };
      
    case ActionTypes.SET_SIDEBAR_OPEN:
      return {
        ...state,
        ui: {
          ...state.ui,
          sidebarOpen: action.payload
        }
      };
      
    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: [...state.ui.notifications, action.payload]
        }
      };
      
    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: state.ui.notifications.filter(n => n.id !== action.payload)
        }
      };
      
    // 📊 إجراءات التحليلات
    case ActionTypes.SET_ANALYTICS:
      return {
        ...state,
        analytics: {
          ...state.analytics,
          ...action.payload,
          loading: false
        }
      };
      
    // ⚙️ إجراءات الإعدادات
    case ActionTypes.SET_SETTINGS:
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload
        }
      };
      
    case ActionTypes.UPDATE_SETTING:
      return {
        ...state,
        settings: {
          ...state.settings,
          [action.payload.key]: action.payload.value
        }
      };
      
    default:
      return state;
  }
};

// 🏪 سياق المتجر
const StoreContext = createContext();

// 🎯 مزود المتجر المحسن
export const OptimizedStoreProvider = ({ children }) => {
  const [state, dispatch] = useReducer(optimizedReducer, initialState);
  
  // 💾 حفظ الحالة في localStorage
  useEffect(() => {
    const saveState = () => {
      try {
        const stateToSave = {
          ui: {
            theme: state.ui.theme,
            sidebarOpen: state.ui.sidebarOpen
          },
          settings: state.settings
        };
        localStorage.setItem('appState', JSON.stringify(stateToSave));
      } catch (error) {
        console.warn('فشل في حفظ الحالة:', error);
      }
    };
    
    const timeoutId = setTimeout(saveState, 1000); // حفظ مؤجل
    return () => clearTimeout(timeoutId);
  }, [state.ui.theme, state.ui.sidebarOpen, state.settings]);
  
  // 📖 تحميل الحالة من localStorage
  useEffect(() => {
    try {
      const savedState = localStorage.getItem('appState');
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: ActionTypes.SET_THEME, payload: parsedState.ui?.theme || 'light' });
        dispatch({ type: ActionTypes.SET_SIDEBAR_OPEN, payload: parsedState.ui?.sidebarOpen ?? true });
        dispatch({ type: ActionTypes.SET_SETTINGS, payload: parsedState.settings || {} });
      }
    } catch (error) {
      console.warn('فشل في تحميل الحالة:', error);
    }
  }, []);
  
  // 🎯 إجراءات محسنة
  const actions = {
    // 📋 إجراءات الحجوزات
    setBookings: useCallback((bookings) => {
      dispatch({ type: ActionTypes.SET_BOOKINGS, payload: bookings });
    }, []),
    
    addBooking: useCallback((booking) => {
      dispatch({ type: ActionTypes.ADD_BOOKING, payload: booking });
    }, []),
    
    updateBooking: useCallback((booking) => {
      dispatch({ type: ActionTypes.UPDATE_BOOKING, payload: booking });
    }, []),
    
    deleteBooking: useCallback((id) => {
      dispatch({ type: ActionTypes.DELETE_BOOKING, payload: id });
    }, []),
    
    setLoading: useCallback((loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    }, []),
    
    setError: useCallback((error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
    }, []),
    
    // 👥 إجراءات العملاء
    setCustomers: useCallback((customers) => {
      dispatch({ type: ActionTypes.SET_CUSTOMERS, payload: customers });
    }, []),
    
    addCustomer: useCallback((customer) => {
      dispatch({ type: ActionTypes.ADD_CUSTOMER, payload: customer });
    }, []),
    
    // 🎨 إجراءات واجهة المستخدم
    setTheme: useCallback((theme) => {
      dispatch({ type: ActionTypes.SET_THEME, payload: theme });
    }, []),
    
    toggleSidebar: useCallback(() => {
      dispatch({ type: ActionTypes.SET_SIDEBAR_OPEN, payload: !state.ui.sidebarOpen });
    }, [state.ui.sidebarOpen]),
    
    addNotification: useCallback((notification) => {
      const id = Date.now() + Math.random();
      dispatch({ 
        type: ActionTypes.ADD_NOTIFICATION, 
        payload: { ...notification, id } 
      });
      
      // إزالة تلقائية بعد مدة
      if (notification.duration !== 0) {
        setTimeout(() => {
          dispatch({ type: ActionTypes.REMOVE_NOTIFICATION, payload: id });
        }, notification.duration || 5000);
      }
    }, []),
    
    removeNotification: useCallback((id) => {
      dispatch({ type: ActionTypes.REMOVE_NOTIFICATION, payload: id });
    }, []),
    
    // 📊 إجراءات التحليلات
    setAnalytics: useCallback((analytics) => {
      dispatch({ type: ActionTypes.SET_ANALYTICS, payload: analytics });
    }, []),
    
    // ⚙️ إجراءات الإعدادات
    updateSetting: useCallback((key, value) => {
      dispatch({ type: ActionTypes.UPDATE_SETTING, payload: { key, value } });
    }, [])
  };
  
  // 🎯 محددات محسنة
  const selectors = {
    // 📋 محددات الحجوزات
    getBookings: () => state.bookings.items,
    getBookingById: (id) => state.bookings.items.find(b => b.id === id),
    getBookingsByStatus: (status) => state.bookings.items.filter(b => b.status === status),
    getBookingsByType: (type) => state.bookings.items.filter(b => b.type === type),
    isBookingsLoading: () => state.bookings.loading,
    getBookingsError: () => state.bookings.error,
    
    // 👥 محددات العملاء
    getCustomers: () => state.customers.items,
    getCustomerById: (id) => state.customers.items.find(c => c.id === id),
    
    // 🎨 محددات واجهة المستخدم
    getTheme: () => state.ui.theme,
    isSidebarOpen: () => state.ui.sidebarOpen,
    getNotifications: () => state.ui.notifications,
    
    // 📊 محددات التحليلات
    getAnalytics: () => state.analytics,
    
    // ⚙️ محددات الإعدادات
    getSettings: () => state.settings,
    getSetting: (key) => state.settings[key]
  };
  
  const value = {
    state,
    actions,
    selectors
  };
  
  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
};

// 🎣 هوك استخدام المتجر
export const useOptimizedStore = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error('useOptimizedStore must be used within OptimizedStoreProvider');
  }
  return context;
};

// 🎯 هوكس متخصصة
export const useBookings = () => {
  const { state, actions, selectors } = useOptimizedStore();
  return {
    bookings: selectors.getBookings(),
    loading: selectors.isBookingsLoading(),
    error: selectors.getBookingsError(),
    actions: {
      setBookings: actions.setBookings,
      addBooking: actions.addBooking,
      updateBooking: actions.updateBooking,
      deleteBooking: actions.deleteBooking,
      setLoading: actions.setLoading,
      setError: actions.setError
    }
  };
};

export const useUI = () => {
  const { state, actions, selectors } = useOptimizedStore();
  return {
    theme: selectors.getTheme(),
    sidebarOpen: selectors.isSidebarOpen(),
    notifications: selectors.getNotifications(),
    actions: {
      setTheme: actions.setTheme,
      toggleSidebar: actions.toggleSidebar,
      addNotification: actions.addNotification,
      removeNotification: actions.removeNotification
    }
  };
};

export const useSettings = () => {
  const { selectors, actions } = useOptimizedStore();
  return {
    settings: selectors.getSettings(),
    getSetting: selectors.getSetting,
    updateSetting: actions.updateSetting
  };
};

export default OptimizedStoreProvider;
