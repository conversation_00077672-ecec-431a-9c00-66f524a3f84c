# نافذة الإعدادات - دليل الاستخدام

## نظرة عامة
نافذة الإعدادات هي مركز التحكم الرئيسي في النظام، تتيح للمستخدمين إدارة جميع جوانب التطبيق من مكان واحد.

## الملفات الرئيسية

### 1. SettingsPage.js
الملف الرئيسي لنافذة الإعدادات الأساسية، يحتوي على:
- الإعدادات العامة
- معلومات الشركة
- الإعدادات المالية
- التنبيهات
- الأمان
- التكاملات

### 2. AdvancedSettingsPage.js
صفحة الإعدادات المتقدمة للمديرين، تشمل:
- إعدادات النظام
- الأداء والتحسين
- المراقبة والتنبيهات
- إدارة المستخدمين
- النسخ الاحتياطي
- إعدادات البريد الإلكتروني

### 3. SettingsPage.css
ملف الأنماط الشامل لجميع مكونات الإعدادات

### 4. SettingsComponents.js
مكونات مساعدة للإعدادات المتقدمة:
- `ConnectionTest`: اختبار الاتصال بالخدمات الخارجية
- `BackupManager`: إدارة النسخ الاحتياطية
- `UserManagement`: إدارة المستخدمين
- `EmailSettings`: إعدادات البريد الإلكتروني
- `SystemStats`: إحصائيات النظام

## الأقسام الرئيسية

### 📋 الإعدادات العامة
- اسم الشركة والشعار
- اللغة والعملة
- المنطقة الزمنية
- تنسيق التاريخ
- بداية السنة المالية

### 🏢 معلومات الشركة
- نوع النشاط التجاري
- أرقام التراخيص
- الرقم الضريبي
- السجل التجاري
- معلومات الاتصال

### 💰 الإعدادات المالية
- معدلات العمولة والضريبة
- شروط الدفع
- إعدادات الفواتير
- العملات المتعددة
- الموافقات المطلوبة

### 🔔 التنبيهات
- البريد الإلكتروني
- الرسائل النصية
- التنبيهات الفورية
- تأكيدات الحجز
- تذكيرات الدفع

### 🔒 الأمان
- المصادقة الثنائية
- انتهاء الجلسة
- سياسة كلمة المرور
- عناوين IP المسموحة
- تشفير البيانات

### 🔗 التكاملات
- بوابات الدفع (PayPal, Stripe, مدى)
- APIs خارجية (Amadeus, Sabre)
- خرائط جوجل
- وسائل التواصل الاجتماعي

## الإعدادات المتقدمة

### 🖥️ إعدادات النظام
- وضع الصيانة
- وضع التطوير
- مستوى السجلات
- النسخ الاحتياطي التلقائي

### ⚡ الأداء والتحسين
- التخزين المؤقت
- ضغط البيانات
- التحميل التدريجي
- مراقبة الموارد

### 📊 المراقبة والتنبيهات
- تتبع الأخطاء
- مراقبة الأداء
- تحليلات المستخدمين
- سجل الأحداث

### 👥 إدارة المستخدمين
- إضافة/حذف المستخدمين
- إدارة الصلاحيات
- تفعيل/إيقاف الحسابات
- تتبع النشاط

### 💾 النسخ الاحتياطي
- إنشاء نسخ احتياطية
- جدولة تلقائية
- تحميل واستعادة
- إدارة المساحة

## كيفية الاستخدام

### الوصول للإعدادات
```
http://localhost:3000/settings
```

### التنقل بين الأقسام
- استخدم التبويبات الجانبية للتنقل
- كل قسم منظم بشكل منطقي
- الحفظ تلقائي عند التغيير

### حفظ الإعدادات
- الحفظ يتم تلقائياً عند التغيير
- رسائل تأكيد للعمليات المهمة
- إمكانية التراجع عن التغييرات

### تصدير/استيراد الإعدادات
```javascript
// تصدير الإعدادات
const exportSettings = () => {
  const dataStr = JSON.stringify(settings, null, 2);
  const dataBlob = new Blob([dataStr], {type: 'application/json'});
  // تحميل الملف
};

// استيراد الإعدادات
const importSettings = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    const importedSettings = JSON.parse(e.target.result);
    setSettings(importedSettings);
  };
  reader.readAsText(file);
};
```

## التحقق من صحة البيانات

### استخدام أداة التحقق
```javascript
import { validateSettings } from '../utils/settingsValidator';

const validation = validateSettings(settings);
if (!validation.isValid) {
  console.log('أخطاء:', validation.errors);
}
```

### قواعد التحقق
- اسم الشركة مطلوب
- البريد الإلكتروني صحيح
- أرقام الهاتف بالتنسيق السعودي
- معدلات العمولة والضريبة بين 0-100%

## الاختبارات

### تشغيل الاختبارات
```javascript
import { runAllSettingsTests } from '../tests/settingsTest';

const testResults = runAllSettingsTests();
console.log('نتائج الاختبارات:', testResults);
```

### أنواع الاختبارات
- التحقق من صحة البيانات
- تنظيف البيانات
- الإعدادات الافتراضية
- حفظ واستعادة البيانات

## الأمان

### حماية البيانات
- تشفير كلمات المرور
- حماية المفاتيح السرية
- تسجيل العمليات الحساسة
- التحقق من الصلاحيات

### أفضل الممارسات
- تغيير كلمات المرور بانتظام
- تفعيل المصادقة الثنائية
- مراجعة سجل العمليات
- النسخ الاحتياطي المنتظم

## استكشاف الأخطاء

### مشاكل شائعة
1. **عدم حفظ الإعدادات**: تحقق من الاتصال بالخادم
2. **أخطاء التحقق**: راجع قواعد البيانات المطلوبة
3. **مشاكل التكاملات**: اختبر الاتصال بالخدمات الخارجية
4. **بطء الأداء**: تحقق من إعدادات التخزين المؤقت

### رسائل الخطأ
- `VALIDATION_ERROR`: خطأ في التحقق من البيانات
- `SAVE_ERROR`: فشل في حفظ الإعدادات
- `CONNECTION_ERROR`: مشكلة في الاتصال
- `PERMISSION_ERROR`: عدم وجود صلاحيات كافية

## التطوير المستقبلي

### مميزات مخططة
- إعدادات متقدمة للتقارير
- تكاملات إضافية
- إعدادات الذكاء الاصطناعي
- واجهة مخصصة للعملاء

### التحسينات
- أداء أفضل للتحميل
- واجهة مستخدم محسنة
- دعم المزيد من اللغات
- تكاملات أكثر مع الخدمات الخارجية