import React from 'react';

const DebugTest = () => {
  return (
    <div style={{ 
      padding: '2rem', 
      fontFamily: 'Arial, sans-serif',
      textAlign: 'center',
      backgroundColor: '#f0f9ff',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#1e40af', marginBottom: '2rem' }}>
        🔧 صفحة تشخيص المشاكل
      </h1>
      
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '1rem',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        maxWidth: '600px',
        margin: '0 auto'
      }}>
        <h2 style={{ color: '#059669', marginBottom: '1rem' }}>
          ✅ React يعمل بشكل صحيح!
        </h2>
        
        <p style={{ color: '#6b7280', lineHeight: '1.6', marginBottom: '2rem' }}>
          إذا كنت ترى هذه الرسالة، فهذا يعني أن React يعمل بشكل صحيح.
          المشكلة قد تكون في مكونات أخرى.
        </p>

        <div style={{
          display: 'grid',
          gap: '1rem',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          marginBottom: '2rem'
        }}>
          <button 
            onClick={() => alert('الزر يعمل!')}
            style={{
              padding: '1rem',
              backgroundColor: '#10b981',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            🎯 اختبار الزر
          </button>

          <button 
            onClick={() => console.log('تم النقر على الزر')}
            style={{
              padding: '1rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            📝 اختبار Console
          </button>
        </div>

        <div style={{
          backgroundColor: '#fef3c7',
          padding: '1rem',
          borderRadius: '0.5rem',
          border: '1px solid #f59e0b'
        }}>
          <h3 style={{ color: '#92400e', margin: '0 0 0.5rem 0' }}>
            🔍 خطوات التشخيص:
          </h3>
          <ol style={{ color: '#92400e', textAlign: 'right', margin: 0 }}>
            <li>تحقق من وحدة التحكم للأخطاء</li>
            <li>تأكد من أن جميع الملفات موجودة</li>
            <li>تحقق من صحة الاستيرادات</li>
            <li>اختبر المكونات واحداً تلو الآخر</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default DebugTest;
