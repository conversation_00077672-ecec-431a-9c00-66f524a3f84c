import React, { useState, useEffect } from 'react';
import './PermissionsMatrix.css';

const PermissionsMatrix = () => {
  const [users, setUsers] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterLevel, setFilterLevel] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('matrix'); // matrix, user-focused, permission-focused
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkUsers, setBulkUsers] = useState([]);
  const [bulkPermissions, setBulkPermissions] = useState([]);

  // نظام الأذونات الشامل
  const permissionsSystem = {
    // أذونات النظام
    system: {
      name: 'إدارة النظام',
      icon: '⚙️',
      color: '#e74c3c',
      description: 'أذونات التحكم في النظام العام',
      permissions: {
        'system.admin': {
          name: 'إدارة النظام الكاملة',
          description: 'تحكم كامل في جميع إعدادات النظام',
          level: 'super_admin',
          critical: true,
          category: 'system'
        },
        'system.settings': {
          name: 'إعدادات النظام',
          description: 'تعديل الإعدادات العامة للنظام',
          level: 'admin',
          critical: true,
          category: 'system'
        },
        'system.backup': {
          name: 'النسخ الاحتياطي',
          description: 'إنشاء واستعادة النسخ الاحتياطية',
          level: 'admin',
          critical: true,
          category: 'system'
        },
        'system.logs': {
          name: 'سجلات النظام',
          description: 'عرض وإدارة سجلات النظام',
          level: 'manager',
          critical: false,
          category: 'system'
        },
        'system.maintenance': {
          name: 'صيانة النظام',
          description: 'تشغيل عمليات الصيانة والتحديث',
          level: 'admin',
          critical: true,
          category: 'system'
        },
        'system.monitoring': {
          name: 'مراقبة النظام',
          description: 'مراقبة أداء النظام والخوادم',
          level: 'admin',
          critical: false,
          category: 'system'
        }
      }
    },

    // أذونات المستخدمين
    users: {
      name: 'إدارة المستخدمين',
      icon: '👥',
      color: '#3498db',
      description: 'أذونات التحكم في المستخدمين والحسابات',
      permissions: {
        'users.view': {
          name: 'عرض المستخدمين',
          description: 'عرض قائمة المستخدمين ومعلوماتهم الأساسية',
          level: 'employee',
          critical: false,
          category: 'users'
        },
        'users.create': {
          name: 'إضافة مستخدمين',
          description: 'إنشاء حسابات مستخدمين جديدة',
          level: 'admin',
          critical: true,
          category: 'users'
        },
        'users.edit': {
          name: 'تعديل المستخدمين',
          description: 'تعديل معلومات المستخدمين الموجودين',
          level: 'manager',
          critical: true,
          category: 'users'
        },
        'users.delete': {
          name: 'حذف المستخدمين',
          description: 'حذف حسابات المستخدمين نهائياً',
          level: 'admin',
          critical: true,
          category: 'users'
        },
        'users.permissions': {
          name: 'إدارة الأذونات',
          description: 'تعديل أذونات المستخدمين',
          level: 'admin',
          critical: true,
          category: 'users'
        },
        'users.roles': {
          name: 'إدارة الأدوار',
          description: 'إنشاء وتعديل أدوار المستخدمين',
          level: 'admin',
          critical: true,
          category: 'users'
        },
        'users.bulk': {
          name: 'العمليات المجمعة',
          description: 'تنفيذ عمليات على عدة مستخدمين',
          level: 'manager',
          critical: true,
          category: 'users'
        },
        'users.export': {
          name: 'تصدير البيانات',
          description: 'تصدير بيانات المستخدمين',
          level: 'manager',
          critical: false,
          category: 'users'
        },
        'users.import': {
          name: 'استيراد البيانات',
          description: 'استيراد بيانات المستخدمين من ملفات',
          level: 'admin',
          critical: true,
          category: 'users'
        },
        'users.audit': {
          name: 'مراجعة المستخدمين',
          description: 'عرض سجل نشاطات المستخدمين',
          level: 'admin',
          critical: false,
          category: 'users'
        }
      }
    },

    // أذونات المبيعات
    sales: {
      name: 'إدارة المبيعات',
      icon: '💰',
      color: '#f39c12',
      description: 'أذونات التحكم في عمليات المبيعات',
      permissions: {
        'sales.view': {
          name: 'عرض المبيعات',
          description: 'عرض قوائم المبيعات والصفقات',
          level: 'employee',
          critical: false,
          category: 'sales'
        },
        'sales.create': {
          name: 'إنشاء مبيعات',
          description: 'إنشاء صفقات وعروض أسعار جديدة',
          level: 'employee',
          critical: false,
          category: 'sales'
        },
        'sales.edit': {
          name: 'تعديل المبيعات',
          description: 'تعديل الصفقات والعروض الموجودة',
          level: 'manager',
          critical: true,
          category: 'sales'
        },
        'sales.delete': {
          name: 'حذف المبيعات',
          description: 'حذف الصفقات والعروض',
          level: 'admin',
          critical: true,
          category: 'sales'
        },
        'sales.approve': {
          name: 'اعتماد المبيعات',
          description: 'اعتماد الصفقات والعروض',
          level: 'manager',
          critical: true,
          category: 'sales'
        },
        'sales.reports': {
          name: 'تقارير المبيعات',
          description: 'عرض وإنشاء تقارير المبيعات',
          level: 'manager',
          critical: false,
          category: 'sales'
        },
        'sales.analytics': {
          name: 'تحليلات المبيعات',
          description: 'عرض التحليلات المتقدمة للمبيعات',
          level: 'manager',
          critical: false,
          category: 'sales'
        },
        'sales.commission': {
          name: 'إدارة العمولات',
          description: 'حساب وإدارة عمولات المبيعات',
          level: 'admin',
          critical: true,
          category: 'sales'
        },
        'sales.pricing': {
          name: 'إدارة الأسعار',
          description: 'تحديد وتعديل أسعار الخدمات',
          level: 'admin',
          critical: true,
          category: 'sales'
        },
        'sales.discounts': {
          name: 'إدارة الخصومات',
          description: 'منح وإدارة الخصومات',
          level: 'manager',
          critical: true,
          category: 'sales'
        }
      }
    },

    // أذونات العملاء
    customers: {
      name: 'إدارة العملاء',
      icon: '👥',
      color: '#27ae60',
      description: 'أذونات التحكم في بيانات العملاء',
      permissions: {
        'customers.view': {
          name: 'عرض العملاء',
          description: 'عرض قوائم العملاء ومعلوماتهم',
          level: 'employee',
          critical: false,
          category: 'customers'
        },
        'customers.create': {
          name: 'إضافة عملاء',
          description: 'إضافة عملاء جدد للنظام',
          level: 'employee',
          critical: false,
          category: 'customers'
        },
        'customers.edit': {
          name: 'تعديل العملاء',
          description: 'تعديل معلومات العملاء',
          level: 'employee',
          critical: false,
          category: 'customers'
        },
        'customers.delete': {
          name: 'حذف العملاء',
          description: 'حذف بيانات العملاء نهائياً',
          level: 'manager',
          critical: true,
          category: 'customers'
        },
        'customers.sensitive': {
          name: 'البيانات الحساسة',
          description: 'الوصول للبيانات الحساسة للعملاء',
          level: 'manager',
          critical: true,
          category: 'customers'
        },
        'customers.history': {
          name: 'تاريخ العملاء',
          description: 'عرض تاريخ تعاملات العملاء',
          level: 'employee',
          critical: false,
          category: 'customers'
        },
        'customers.communication': {
          name: 'التواصل مع العملاء',
          description: 'إرسال رسائل والتواصل مع العملاء',
          level: 'employee',
          critical: false,
          category: 'customers'
        },
        'customers.export': {
          name: 'تصدير بيانات العملاء',
          description: 'تصدير قوائم وبيانات العملاء',
          level: 'manager',
          critical: true,
          category: 'customers'
        },
        'customers.merge': {
          name: 'دمج العملاء',
          description: 'دمج حسابات العملاء المكررة',
          level: 'manager',
          critical: true,
          category: 'customers'
        },
        'customers.blacklist': {
          name: 'القائمة السوداء',
          description: 'إدارة قائمة العملاء المحظورين',
          level: 'manager',
          critical: true,
          category: 'customers'
        }
      }
    },

    // أذونات الحجوزات
    bookings: {
      name: 'إدارة الحجوزات',
      icon: '📋',
      color: '#9b59b6',
      description: 'أذونات التحكم في الحجوزات والرحلات',
      permissions: {
        'bookings.view': {
          name: 'عرض الحجوزات',
          description: 'عرض قوائم الحجوزات والرحلات',
          level: 'employee',
          critical: false,
          category: 'bookings'
        },
        'bookings.create': {
          name: 'إنشاء حجوزات',
          description: 'إنشاء حجوزات جديدة للعملاء',
          level: 'employee',
          critical: false,
          category: 'bookings'
        },
        'bookings.edit': {
          name: 'تعديل الحجوزات',
          description: 'تعديل تفاصيل الحجوزات الموجودة',
          level: 'employee',
          critical: false,
          category: 'bookings'
        },
        'bookings.cancel': {
          name: 'إلغاء الحجوزات',
          description: 'إلغاء الحجوزات وإدارة الإلغاءات',
          level: 'manager',
          critical: true,
          category: 'bookings'
        },
        'bookings.confirm': {
          name: 'تأكيد الحجوزات',
          description: 'تأكيد الحجوزات واعتمادها',
          level: 'manager',
          critical: true,
          category: 'bookings'
        },
        'bookings.refund': {
          name: 'استرداد الحجوزات',
          description: 'معالجة طلبات الاسترداد',
          level: 'admin',
          critical: true,
          category: 'bookings'
        },
        'bookings.modify': {
          name: 'تعديل متقدم',
          description: 'تعديلات متقدمة على الحجوزات',
          level: 'manager',
          critical: true,
          category: 'bookings'
        },
        'bookings.reports': {
          name: 'تقارير الحجوزات',
          description: 'عرض وإنشاء تقارير الحجوزات',
          level: 'manager',
          critical: false,
          category: 'bookings'
        },
        'bookings.bulk': {
          name: 'العمليات المجمعة',
          description: 'تنفيذ عمليات على عدة حجوزات',
          level: 'manager',
          critical: true,
          category: 'bookings'
        },
        'bookings.special': {
          name: 'الحجوزات الخاصة',
          description: 'إدارة الحجوزات الخاصة والمميزة',
          level: 'admin',
          critical: true,
          category: 'bookings'
        }
      }
    },

    // أذونات المالية
    finance: {
      name: 'إدارة المالية',
      icon: '💳',
      color: '#e67e22',
      description: 'أذونات التحكم في الشؤون المالية',
      permissions: {
        'finance.view': {
          name: 'عرض المالية',
          description: 'عرض البيانات والتقارير المالية',
          level: 'manager',
          critical: false,
          category: 'finance'
        },
        'finance.transactions': {
          name: 'المعاملات المالية',
          description: 'إدارة المعاملات المالية',
          level: 'manager',
          critical: true,
          category: 'finance'
        },
        'finance.invoices': {
          name: 'إدارة الفواتير',
          description: 'إنشاء وتعديل الفواتير',
          level: 'employee',
          critical: false,
          category: 'finance'
        },
        'finance.payments': {
          name: 'إدارة المدفوعات',
          description: 'معالجة وتتبع المدفوعات',
          level: 'manager',
          critical: true,
          category: 'finance'
        },
        'finance.refunds': {
          name: 'المبالغ المستردة',
          description: 'معالجة طلبات الاسترداد المالي',
          level: 'admin',
          critical: true,
          category: 'finance'
        },
        'finance.reports': {
          name: 'التقارير المالية',
          description: 'إنشاء وعرض التقارير المالية',
          level: 'manager',
          critical: false,
          category: 'finance'
        },
        'finance.audit': {
          name: 'المراجعة المالية',
          description: 'مراجعة وتدقيق العمليات المالية',
          level: 'admin',
          critical: true,
          category: 'finance'
        },
        'finance.budget': {
          name: 'إدارة الميزانية',
          description: 'تخطيط وإدارة الميزانيات',
          level: 'admin',
          critical: true,
          category: 'finance'
        },
        'finance.taxes': {
          name: 'إدارة الضرائب',
          description: 'حساب وإدارة الضرائب',
          level: 'admin',
          critical: true,
          category: 'finance'
        },
        'finance.banking': {
          name: 'العمليات المصرفية',
          description: 'إدارة الحسابات المصرفية',
          level: 'admin',
          critical: true,
          category: 'finance'
        }
      }
    },

    // أذونات التقارير
    reports: {
      name: 'التقارير والتحليلات',
      icon: '📊',
      color: '#1abc9c',
      description: 'أذونات التحكم في التقارير والتحليلات',
      permissions: {
        'reports.view': {
          name: 'عرض التقارير',
          description: 'عرض التقارير الأساسية',
          level: 'employee',
          critical: false,
          category: 'reports'
        },
        'reports.create': {
          name: 'إنشاء تقارير',
          description: 'إنشاء تقارير مخصصة',
          level: 'manager',
          critical: false,
          category: 'reports'
        },
        'reports.export': {
          name: 'تصدير التقارير',
          description: 'تصدير التقارير بصيغ مختلفة',
          level: 'manager',
          critical: false,
          category: 'reports'
        },
        'reports.advanced': {
          name: 'تقارير متقدمة',
          description: 'الوصول للتقارير المتقدمة',
          level: 'admin',
          critical: false,
          category: 'reports'
        },
        'reports.analytics': {
          name: 'التحليلات المتقدمة',
          description: 'استخدام أدوات التحليل المتقدمة',
          level: 'admin',
          critical: false,
          category: 'reports'
        },
        'reports.dashboard': {
          name: 'لوحة التحكم',
          description: 'الوصول للوحات التحكم التفاعلية',
          level: 'employee',
          critical: false,
          category: 'reports'
        },
        'reports.realtime': {
          name: 'التقارير الفورية',
          description: 'عرض التقارير والبيانات الفورية',
          level: 'manager',
          critical: false,
          category: 'reports'
        },
        'reports.schedule': {
          name: 'جدولة التقارير',
          description: 'جدولة التقارير التلقائية',
          level: 'manager',
          critical: false,
          category: 'reports'
        },
        'reports.share': {
          name: 'مشاركة التقارير',
          description: 'مشاركة التقارير مع الآخرين',
          level: 'manager',
          critical: false,
          category: 'reports'
        },
        'reports.sensitive': {
          name: 'التقارير الحساسة',
          description: 'الوصول للتقارير الحساسة والسرية',
          level: 'admin',
          critical: true,
          category: 'reports'
        }
      }
    }
  };

  // بيانات المستخدمين
  const initialUsers = [
    {
      id: 1,
      name: 'أحمد محمد الأحمد',
      email: '<EMAIL>',
      role: 'super_admin',
      department: 'الإدارة العامة',
      avatar: 'https://ui-avatars.com/api/?name=أحمد+محمد&background=667eea&color=fff&size=50',
      permissions: ['system.*', 'users.*', 'sales.*', 'customers.*', 'bookings.*', 'finance.*', 'reports.*']
    },
    {
      id: 2,
      name: 'فاطمة علي السالم',
      email: '<EMAIL>',
      role: 'sales_manager',
      department: 'المبيعات',
      avatar: 'https://ui-avatars.com/api/?name=فاطمة+علي&background=f39c12&color=fff&size=50',
      permissions: [
        'users.view', 'users.export',
        'sales.*',
        'customers.*',
        'bookings.view', 'bookings.create', 'bookings.edit', 'bookings.confirm',
        'finance.view', 'finance.invoices', 'finance.payments',
        'reports.view', 'reports.create', 'reports.export'
      ]
    },
    {
      id: 3,
      name: 'محمد عبدالله القحطاني',
      email: '<EMAIL>',
      role: 'booking_supervisor',
      department: 'الحجوزات',
      avatar: 'https://ui-avatars.com/api/?name=محمد+عبدالله&background=3498db&color=fff&size=50',
      permissions: [
        'users.view',
        'customers.view', 'customers.create', 'customers.edit', 'customers.history',
        'bookings.*',
        'finance.view', 'finance.invoices',
        'reports.view', 'reports.dashboard'
      ]
    },
    {
      id: 4,
      name: 'نورا سعد الغامدي',
      email: '<EMAIL>',
      role: 'customer_service',
      department: 'خدمة العملاء',
      avatar: 'https://ui-avatars.com/api/?name=نورا+سعد&background=27ae60&color=fff&size=50',
      permissions: [
        'customers.view', 'customers.create', 'customers.edit', 'customers.history', 'customers.communication',
        'bookings.view', 'bookings.create', 'bookings.edit',
        'finance.view', 'finance.invoices',
        'reports.view', 'reports.dashboard'
      ]
    },
    {
      id: 5,
      name: 'خالد أحمد البراك',
      email: '<EMAIL>',
      role: 'accountant',
      department: 'المحاسبة',
      avatar: 'https://ui-avatars.com/api/?name=خالد+أحمد&background=95a5a6&color=fff&size=50',
      permissions: [
        'customers.view',
        'bookings.view',
        'finance.*',
        'reports.view', 'reports.create', 'reports.export'
      ]
    },
    {
      id: 6,
      name: 'سارة محمد الزهراني',
      email: '<EMAIL>',
      role: 'marketing_manager',
      department: 'التسويق',
      avatar: 'https://ui-avatars.com/api/?name=سارة+محمد&background=9b59b6&color=fff&size=50',
      permissions: [
        'users.view',
        'customers.view', 'customers.create', 'customers.edit', 'customers.communication', 'customers.export',
        'sales.view', 'sales.reports', 'sales.analytics',
        'bookings.view',
        'reports.*'
      ]
    }
  ];

  useEffect(() => {
    setUsers(initialUsers);
    
    // تحويل نظام الأذونات إلى قائمة مسطحة
    const flatPermissions = [];
    Object.entries(permissionsSystem).forEach(([categoryKey, category]) => {
      Object.entries(category.permissions).forEach(([permKey, permission]) => {
        flatPermissions.push({
          key: permKey,
          ...permission,
          categoryKey,
          categoryName: category.name,
          categoryIcon: category.icon,
          categoryColor: category.color
        });
      });
    });
    setPermissions(flatPermissions);
  }, []);

  // تصفية الأذونات
  const filteredPermissions = permissions.filter(permission => {
    const matchesCategory = filterCategory === 'all' || permission.categoryKey === filterCategory;
    const matchesLevel = filterLevel === 'all' || permission.level === filterLevel;
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.key.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesLevel && matchesSearch;
  });

  // فحص وجود إذن للمستخدم
  const hasPermission = (user, permissionKey) => {
    return user.permissions.some(p => {
      if (p === permissionKey) return true;
      if (p.endsWith('.*')) {
        const prefix = p.slice(0, -2);
        return permissionKey.startsWith(prefix + '.');
      }
      return false;
    });
  };

  // تبديل إذن للمستخدم
  const togglePermission = (userId, permissionKey) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        const hasCurrentPermission = hasPermission(user, permissionKey);
        let newPermissions;
        
        if (hasCurrentPermission) {
          // إزالة الإذن
          newPermissions = user.permissions.filter(p => {
            if (p === permissionKey) return false;
            if (p.endsWith('.*')) {
              const prefix = p.slice(0, -2);
              return !permissionKey.startsWith(prefix + '.');
            }
            return true;
          });
        } else {
          // إضافة الإذن
          newPermissions = [...user.permissions, permissionKey];
        }
        
        return { ...user, permissions: newPermissions };
      }
      return user;
    }));
  };

  // العمليات المجمعة
  const handleBulkPermissions = (action) => {
    if (bulkUsers.length === 0 || bulkPermissions.length === 0) {
      alert('يرجى اختيار مستخدمين وأذونات');
      return;
    }

    setUsers(users.map(user => {
      if (bulkUsers.includes(user.id)) {
        let newPermissions = [...user.permissions];
        
        bulkPermissions.forEach(permissionKey => {
          const hasCurrentPermission = hasPermission(user, permissionKey);
          
          if (action === 'grant' && !hasCurrentPermission) {
            newPermissions.push(permissionKey);
          } else if (action === 'revoke' && hasCurrentPermission) {
            newPermissions = newPermissions.filter(p => {
              if (p === permissionKey) return false;
              if (p.endsWith('.*')) {
                const prefix = p.slice(0, -2);
                return !permissionKey.startsWith(prefix + '.');
              }
              return true;
            });
          }
        });
        
        return { ...user, permissions: newPermissions };
      }
      return user;
    }));

    setBulkUsers([]);
    setBulkPermissions([]);
    setShowBulkModal(false);
  };

  // إحصائيات الأذونات
  const getPermissionStats = () => {
    const stats = {};
    permissions.forEach(permission => {
      const usersWithPermission = users.filter(user => hasPermission(user, permission.key)).length;
      stats[permission.key] = {
        users: usersWithPermission,
        percentage: Math.round((usersWithPermission / users.length) * 100)
      };
    });
    return stats;
  };

  const permissionStats = getPermissionStats();

  return (
    <div className="permissions-matrix">
      {/* رأس الصفحة */}
      <div className="matrix-header">
        <div className="header-content">
          <div className="header-text">
            <h1 className="page-title">
              <span className="title-icon">🔐</span>
              مصفوفة الأذونات المتقدمة
            </h1>
            <p className="page-description">
              إدارة شاملة ومرئية لجميع أذونات المستخدمين في النظام
            </p>
          </div>
          <div className="header-actions">
            <button 
              className="btn-bulk-operations"
              onClick={() => setShowBulkModal(true)}
              disabled={bulkUsers.length === 0}
            >
              <span className="btn-icon">⚡</span>
              العمليات المجمعة ({bulkUsers.length})
            </button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="matrix-stats">
          <div className="stat-item">
            <span className="stat-icon">👥</span>
            <span className="stat-number">{users.length}</span>
            <span className="stat-label">مستخدم</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">🔐</span>
            <span className="stat-number">{permissions.length}</span>
            <span className="stat-label">إذن</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">📊</span>
            <span className="stat-number">{Object.keys(permissionsSystem).length}</span>
            <span className="stat-label">فئة</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">⚠️</span>
            <span className="stat-number">
              {permissions.filter(p => p.critical).length}
            </span>
            <span className="stat-label">إذن حساس</span>
          </div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="matrix-controls">
        <div className="controls-section">
          <div className="search-box">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              placeholder="البحث في الأذونات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filters">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="filter-select"
            >
              <option value="all">جميع الفئات</option>
              {Object.entries(permissionsSystem).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>

            <select
              value={filterLevel}
              onChange={(e) => setFilterLevel(e.target.value)}
              className="filter-select"
            >
              <option value="all">جميع المستويات</option>
              <option value="employee">موظف</option>
              <option value="manager">مدير</option>
              <option value="admin">مدير عام</option>
              <option value="super_admin">مدير النظام</option>
            </select>
          </div>

          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'matrix' ? 'active' : ''}`}
              onClick={() => setViewMode('matrix')}
            >
              <span className="mode-icon">⊞</span>
              مصفوفة
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'user-focused' ? 'active' : ''}`}
              onClick={() => setViewMode('user-focused')}
            >
              <span className="mode-icon">👤</span>
              حسب المستخدم
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'permission-focused' ? 'active' : ''}`}
              onClick={() => setViewMode('permission-focused')}
            >
              <span className="mode-icon">🔐</span>
              حسب الإذن
            </button>
          </div>
        </div>
      </div>

      {/* عرض المصفوفة */}
      <div className="matrix-content">
        {viewMode === 'matrix' && (
          <div className="matrix-table-container">
            <table className="permissions-matrix-table">
              <thead>
                <tr>
                  <th className="user-header">
                    <input
                      type="checkbox"
                      checked={bulkUsers.length === users.length}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setBulkUsers(users.map(u => u.id));
                        } else {
                          setBulkUsers([]);
                        }
                      }}
                    />
                    المستخدم
                  </th>
                  {filteredPermissions.map(permission => (
                    <th key={permission.key} className="permission-header">
                      <input
                        type="checkbox"
                        checked={bulkPermissions.includes(permission.key)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setBulkPermissions([...bulkPermissions, permission.key]);
                          } else {
                            setBulkPermissions(bulkPermissions.filter(p => p !== permission.key));
                          }
                        }}
                      />
                      <div className="permission-header-content">
                        <span 
                          className="permission-category-icon"
                          style={{ backgroundColor: permission.categoryColor }}
                        >
                          {permission.categoryIcon}
                        </span>
                        <div className="permission-info">
                          <div className="permission-name">{permission.name}</div>
                          <div className="permission-key">{permission.key}</div>
                          <div className="permission-stats">
                            {permissionStats[permission.key]?.users || 0} مستخدم
                            ({permissionStats[permission.key]?.percentage || 0}%)
                          </div>
                        </div>
                        {permission.critical && (
                          <span className="critical-indicator" title="إذن حساس">⚠️</span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {users.map(user => (
                  <tr key={user.id}>
                    <td className="user-cell">
                      <input
                        type="checkbox"
                        checked={bulkUsers.includes(user.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setBulkUsers([...bulkUsers, user.id]);
                          } else {
                            setBulkUsers(bulkUsers.filter(id => id !== user.id));
                          }
                        }}
                      />
                      <div className="user-info">
                        <img src={user.avatar} alt={user.name} className="user-avatar" />
                        <div className="user-details">
                          <div className="user-name">{user.name}</div>
                          <div className="user-role">{user.role}</div>
                          <div className="user-department">{user.department}</div>
                        </div>
                      </div>
                    </td>
                    {filteredPermissions.map(permission => {
                      const hasCurrentPermission = hasPermission(user, permission.key);
                      return (
                        <td key={permission.key} className="permission-cell">
                          <button
                            className={`permission-toggle ${hasCurrentPermission ? 'granted' : 'denied'} ${permission.critical ? 'critical' : ''}`}
                            onClick={() => togglePermission(user.id, permission.key)}
                            title={`${hasCurrentPermission ? 'إلغاء' : 'منح'} إذن: ${permission.name}`}
                          >
                            {hasCurrentPermission ? '✅' : '❌'}
                          </button>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {viewMode === 'user-focused' && (
          <div className="user-focused-view">
            {users.map(user => (
              <div key={user.id} className="user-permissions-card">
                <div className="user-card-header">
                  <div className="user-info">
                    <img src={user.avatar} alt={user.name} className="user-avatar" />
                    <div className="user-details">
                      <h3 className="user-name">{user.name}</h3>
                      <p className="user-role">{user.role}</p>
                      <p className="user-department">{user.department}</p>
                    </div>
                  </div>
                  <div className="user-stats">
                    <div className="stat">
                      <span className="stat-number">
                        {filteredPermissions.filter(p => hasPermission(user, p.key)).length}
                      </span>
                      <span className="stat-label">إذن مفعل</span>
                    </div>
                    <div className="stat">
                      <span className="stat-number">
                        {filteredPermissions.filter(p => hasPermission(user, p.key) && p.critical).length}
                      </span>
                      <span className="stat-label">إذن حساس</span>
                    </div>
                  </div>
                </div>

                <div className="user-permissions-grid">
                  {Object.entries(permissionsSystem).map(([categoryKey, category]) => {
                    const categoryPermissions = filteredPermissions.filter(p => p.categoryKey === categoryKey);
                    if (categoryPermissions.length === 0) return null;

                    return (
                      <div key={categoryKey} className="permission-category-section">
                        <div className="category-header">
                          <span 
                            className="category-icon"
                            style={{ backgroundColor: category.color }}
                          >
                            {category.icon}
                          </span>
                          <span className="category-name">{category.name}</span>
                          <span className="category-count">
                            {categoryPermissions.filter(p => hasPermission(user, p.key)).length} / {categoryPermissions.length}
                          </span>
                        </div>
                        <div className="permissions-list">
                          {categoryPermissions.map(permission => {
                            const hasCurrentPermission = hasPermission(user, permission.key);
                            return (
                              <div 
                                key={permission.key} 
                                className={`permission-item ${hasCurrentPermission ? 'granted' : 'denied'}`}
                              >
                                <button
                                  className="permission-toggle-btn"
                                  onClick={() => togglePermission(user.id, permission.key)}
                                >
                                  <span className="toggle-icon">
                                    {hasCurrentPermission ? '✅' : '❌'}
                                  </span>
                                  <div className="permission-details">
                                    <div className="permission-name">{permission.name}</div>
                                    <div className="permission-description">{permission.description}</div>
                                  </div>
                                  {permission.critical && (
                                    <span className="critical-badge">⚠️</span>
                                  )}
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}

        {viewMode === 'permission-focused' && (
          <div className="permission-focused-view">
            {Object.entries(permissionsSystem).map(([categoryKey, category]) => {
              const categoryPermissions = filteredPermissions.filter(p => p.categoryKey === categoryKey);
              if (categoryPermissions.length === 0) return null;

              return (
                <div key={categoryKey} className="permission-category-card">
                  <div className="category-header">
                    <div className="category-info">
                      <span 
                        className="category-icon"
                        style={{ backgroundColor: category.color }}
                      >
                        {category.icon}
                      </span>
                      <div className="category-details">
                        <h3 className="category-name">{category.name}</h3>
                        <p className="category-description">{category.description}</p>
                      </div>
                    </div>
                    <div className="category-stats">
                      <span className="permissions-count">{categoryPermissions.length} إذن</span>
                    </div>
                  </div>

                  <div className="permissions-grid">
                    {categoryPermissions.map(permission => (
                      <div key={permission.key} className="permission-card">
                        <div className="permission-header">
                          <div className="permission-info">
                            <h4 className="permission-name">{permission.name}</h4>
                            <p className="permission-description">{permission.description}</p>
                            <code className="permission-key">{permission.key}</code>
                          </div>
                          <div className="permission-badges">
                            <span className={`level-badge ${permission.level}`}>
                              {permission.level === 'super_admin' ? '👑' : 
                               permission.level === 'admin' ? '🛡️' :
                               permission.level === 'manager' ? '👨‍💼' : '👤'}
                            </span>
                            {permission.critical && (
                              <span className="critical-badge">⚠️</span>
                            )}
                          </div>
                        </div>

                        <div className="permission-users">
                          <div className="users-header">
                            <span className="users-count">
                              {users.filter(u => hasPermission(u, permission.key)).length} / {users.length} مستخدم
                            </span>
                            <div className="users-percentage">
                              <div 
                                className="percentage-bar"
                                style={{ 
                                  width: `${permissionStats[permission.key]?.percentage || 0}%` 
                                }}
                              ></div>
                            </div>
                          </div>
                          <div className="users-list">
                            {users.map(user => {
                              const hasCurrentPermission = hasPermission(user, permission.key);
                              return (
                                <button
                                  key={user.id}
                                  className={`user-permission-toggle ${hasCurrentPermission ? 'granted' : 'denied'}`}
                                  onClick={() => togglePermission(user.id, permission.key)}
                                  title={`${user.name} - ${hasCurrentPermission ? 'مفعل' : 'معطل'}`}
                                >
                                  <img src={user.avatar} alt={user.name} className="user-avatar-small" />
                                  <span className="permission-status">
                                    {hasCurrentPermission ? '✅' : '❌'}
                                  </span>
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* نافذة العمليات المجمعة */}
      {showBulkModal && (
        <div className="modal-overlay">
          <div className="modal bulk-modal">
            <div className="modal-header">
              <h3>العمليات المجمعة للأذونات</h3>
              <button 
                className="modal-close"
                onClick={() => setShowBulkModal(false)}
              >
                ✕
              </button>
            </div>
            
            <div className="modal-body">
              <div className="bulk-summary">
                <div className="summary-item">
                  <span className="summary-icon">👥</span>
                  <span className="summary-text">{bulkUsers.length} مستخدم محدد</span>
                </div>
                <div className="summary-item">
                  <span className="summary-icon">🔐</span>
                  <span className="summary-text">{bulkPermissions.length} إذن محدد</span>
                </div>
              </div>

              <div className="bulk-actions">
                <button 
                  className="bulk-action-btn grant"
                  onClick={() => handleBulkPermissions('grant')}
                  disabled={bulkUsers.length === 0 || bulkPermissions.length === 0}
                >
                  <span className="btn-icon">✅</span>
                  منح الأذونات المحددة
                </button>
                <button 
                  className="bulk-action-btn revoke"
                  onClick={() => handleBulkPermissions('revoke')}
                  disabled={bulkUsers.length === 0 || bulkPermissions.length === 0}
                >
                  <span className="btn-icon">❌</span>
                  إلغاء الأذونات المحددة
                </button>
              </div>

              <div className="bulk-warning">
                <span className="warning-icon">⚠️</span>
                <span className="warning-text">
                  ستؤثر هذه العملية على {bulkUsers.length} مستخدم و {bulkPermissions.length} إذن
                </span>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="btn-cancel"
                onClick={() => setShowBulkModal(false)}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionsMatrix;