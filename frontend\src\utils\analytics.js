// Analytics and monitoring utilities

class Analytics {
  constructor() {
    this.isEnabled = process.env.REACT_APP_ENV === 'production';
    this.userId = null;
    this.sessionId = this.generateSessionId();
  }

  // Initialize analytics
  init(userId = null) {
    this.userId = userId;
    
    if (this.isEnabled) {
      this.initGoogleAnalytics();
      this.initPerformanceMonitoring();
    }
    
    console.log('📊 Analytics initialized');
  }

  // Generate unique session ID
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Initialize Google Analytics
  initGoogleAnalytics() {
    const gaId = process.env.REACT_APP_GOOGLE_ANALYTICS_ID;
    
    if (!gaId) return;

    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;

    gtag('js', new Date());
    gtag('config', gaId, {
      user_id: this.userId,
      session_id: this.sessionId,
      send_page_view: false // We'll send manually
    });
  }

  // Initialize performance monitoring
  initPerformanceMonitoring() {
    // Monitor Core Web Vitals
    if ('web-vitals' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(this.sendToAnalytics);
        getFID(this.sendToAnalytics);
        getFCP(this.sendToAnalytics);
        getLCP(this.sendToAnalytics);
        getTTFB(this.sendToAnalytics);
      });
    }

    // Monitor errors
    window.addEventListener('error', this.handleError.bind(this));
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
  }

  // Track page views
  trackPageView(path, title = null) {
    if (!this.isEnabled) return;

    const pageData = {
      page_path: path,
      page_title: title || document.title,
      user_id: this.userId,
      session_id: this.sessionId,
      timestamp: new Date().toISOString()
    };

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('config', process.env.REACT_APP_GOOGLE_ANALYTICS_ID, {
        page_path: path,
        page_title: title
      });
    }

    // Send to custom analytics
    this.sendCustomEvent('page_view', pageData);
  }

  // Track events
  trackEvent(eventName, eventData = {}) {
    if (!this.isEnabled) return;

    const eventPayload = {
      event_name: eventName,
      user_id: this.userId,
      session_id: this.sessionId,
      timestamp: new Date().toISOString(),
      ...eventData
    };

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('event', eventName, eventData);
    }

    // Send to custom analytics
    this.sendCustomEvent(eventName, eventPayload);
  }

  // Track user interactions
  trackUserInteraction(action, element, value = null) {
    this.trackEvent('user_interaction', {
      action,
      element,
      value,
      page: window.location.pathname
    });
  }

  // Track business events
  trackBusinessEvent(eventType, data = {}) {
    this.trackEvent('business_event', {
      event_type: eventType,
      ...data
    });
  }

  // Track performance metrics
  sendToAnalytics = (metric) => {
    if (!this.isEnabled) return;

    const metricData = {
      metric_name: metric.name,
      metric_value: metric.value,
      metric_id: metric.id,
      user_id: this.userId,
      session_id: this.sessionId,
      page: window.location.pathname
    };

    this.sendCustomEvent('performance_metric', metricData);
  };

  // Handle JavaScript errors
  handleError(event) {
    if (!this.isEnabled) return;

    const errorData = {
      error_message: event.message,
      error_filename: event.filename,
      error_lineno: event.lineno,
      error_colno: event.colno,
      error_stack: event.error?.stack,
      user_id: this.userId,
      session_id: this.sessionId,
      page: window.location.pathname,
      user_agent: navigator.userAgent
    };

    this.sendCustomEvent('javascript_error', errorData);
  }

  // Handle promise rejections
  handlePromiseRejection(event) {
    if (!this.isEnabled) return;

    const errorData = {
      error_message: event.reason?.message || 'Unhandled Promise Rejection',
      error_stack: event.reason?.stack,
      user_id: this.userId,
      session_id: this.sessionId,
      page: window.location.pathname,
      user_agent: navigator.userAgent
    };

    this.sendCustomEvent('promise_rejection', errorData);
  }

  // Send custom events to backend
  sendCustomEvent(eventType, data) {
    if (!this.isEnabled) return;

    // Send to backend analytics endpoint
    fetch('/api/analytics/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        event_type: eventType,
        data,
        timestamp: new Date().toISOString()
      })
    }).catch(error => {
      console.warn('Failed to send analytics event:', error);
    });
  }

  // Set user properties
  setUserProperties(properties) {
    this.userId = properties.user_id || this.userId;

    if (window.gtag) {
      window.gtag('config', process.env.REACT_APP_GOOGLE_ANALYTICS_ID, {
        user_id: this.userId,
        custom_map: properties
      });
    }
  }

  // Track conversion events
  trackConversion(conversionType, value = null, currency = 'SAR') {
    this.trackEvent('conversion', {
      conversion_type: conversionType,
      value,
      currency
    });
  }

  // Track form submissions
  trackFormSubmission(formName, formData = {}) {
    this.trackEvent('form_submission', {
      form_name: formName,
      form_data: formData
    });
  }

  // Track search events
  trackSearch(searchTerm, searchResults = null) {
    this.trackEvent('search', {
      search_term: searchTerm,
      search_results: searchResults
    });
  }

  // Track booking events
  trackBooking(bookingType, bookingData = {}) {
    this.trackBusinessEvent('booking', {
      booking_type: bookingType,
      ...bookingData
    });
  }

  // Track payment events
  trackPayment(paymentMethod, amount, currency = 'SAR') {
    this.trackBusinessEvent('payment', {
      payment_method: paymentMethod,
      amount,
      currency
    });
  }
}

// Create singleton instance
const analytics = new Analytics();

export default analytics;

// Helper functions for common tracking scenarios
export const trackPageView = (path, title) => analytics.trackPageView(path, title);
export const trackEvent = (eventName, eventData) => analytics.trackEvent(eventName, eventData);
export const trackUserInteraction = (action, element, value) => analytics.trackUserInteraction(action, element, value);
export const trackBusinessEvent = (eventType, data) => analytics.trackBusinessEvent(eventType, data);
export const trackConversion = (conversionType, value, currency) => analytics.trackConversion(conversionType, value, currency);
export const trackFormSubmission = (formName, formData) => analytics.trackFormSubmission(formName, formData);
export const trackSearch = (searchTerm, searchResults) => analytics.trackSearch(searchTerm, searchResults);
export const trackBooking = (bookingType, bookingData) => analytics.trackBooking(bookingType, bookingData);
export const trackPayment = (paymentMethod, amount, currency) => analytics.trackPayment(paymentMethod, amount, currency);