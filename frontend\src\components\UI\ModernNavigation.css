/* Modern Navigation Components Styles */

/* ===== MODERN NAVBAR ===== */
.modern-navbar {
  background: var(--neutral-0);
  border-bottom: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  z-index: var(--z-navbar);
}

.modern-navbar--fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.modern-navbar--transparent {
  background: transparent;
  border-bottom: 1px solid transparent;
  box-shadow: none;
}

.modern-navbar--transparent.modern-navbar--scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom-color: var(--neutral-200);
  box-shadow: var(--shadow-lg);
}

.modern-navbar--glass {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-navbar-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--navbar-height, 70px);
}

.modern-navbar-brand {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--primary-600);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modern-navbar-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* ===== NAVIGATION MENU ===== */
.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-2);
}

.nav-menu--horizontal {
  flex-direction: row;
}

.nav-menu--vertical {
  flex-direction: column;
}

.nav-item {
  position: relative;
}

.nav-item-link,
.nav-item-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--neutral-700);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  border: none;
  background: transparent;
  cursor: pointer;
  white-space: nowrap;
}

.nav-item-button {
  width: 100%;
}

.nav-item-link:hover,
.nav-item-button:hover {
  background: var(--neutral-100);
  color: var(--primary-600);
  transform: translateY(-1px);
}

.nav-item--active .nav-item-link,
.nav-item--active .nav-item-button {
  background: var(--primary-100);
  color: var(--primary-700);
  font-weight: var(--font-semibold);
}

.nav-item--disabled .nav-item-link,
.nav-item--disabled .nav-item-button {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.nav-item-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.nav-item-text {
  flex: 1;
}

.nav-item-badge {
  background: var(--error-500);
  color: var(--neutral-0);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== DROPDOWN ===== */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  cursor: pointer;
}

.dropdown-menu {
  position: absolute;
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--space-2);
  min-width: 200px;
  z-index: var(--z-popover);
  animation: dropdownSlideIn 0.2s ease-out;
  backdrop-filter: blur(20px);
}

/* الأنماط الافتراضية للقوائم المنسدلة العادية (بدون تموضع مخصص) */
.dropdown:not(.more-menu-dropdown) .dropdown-menu--bottom-left {
  top: 100%;
  left: 0;
  margin-top: var(--space-2);
}

.dropdown:not(.more-menu-dropdown) .dropdown-menu--bottom-right {
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
}

.dropdown-menu--top-left {
  bottom: 100%;
  left: 0;
  margin-bottom: var(--space-2);
}

.dropdown-menu--top-right {
  bottom: 100%;
  right: 0;
  margin-bottom: var(--space-2);
}

.dropdown-menu--bottom-center {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: var(--space-2);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--neutral-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--transition-fast);
  border: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
  text-align: right;
}

.dropdown-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

.dropdown-item--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.dropdown-item-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.dropdown-item-text {
  flex: 1;
}

/* ===== BREADCRUMB ===== */
.breadcrumb {
  padding: var(--space-4) 0;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.breadcrumb-link {
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.breadcrumb-text {
  color: var(--neutral-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.breadcrumb-current {
  color: var(--neutral-800);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.breadcrumb-separator {
  color: var(--neutral-400);
  font-size: var(--text-sm);
  user-select: none;
}

/* ===== TABS ===== */
.tabs {
  display: flex;
  flex-direction: column;
}

.tabs-header {
  display: flex;
  border-bottom: 2px solid var(--neutral-200);
  background: var(--neutral-50);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  padding: var(--space-2);
  gap: var(--space-1);
}

.tabs--pills .tabs-header {
  background: transparent;
  border-bottom: none;
  padding: 0;
  gap: var(--space-2);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  background: transparent;
  color: var(--neutral-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
  white-space: nowrap;
}

.tab-button:hover {
  background: var(--neutral-100);
  color: var(--neutral-800);
}

.tab-button--active {
  background: var(--neutral-0);
  color: var(--primary-600);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-sm);
}

.tabs--pills .tab-button--active {
  background: var(--primary-500);
  color: var(--neutral-0);
}

.tab-button-icon {
  font-size: var(--text-base);
}

.tab-button-text {
  flex: 1;
}

.tab-button-badge {
  background: var(--error-500);
  color: var(--neutral-0);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabs-content {
  background: var(--neutral-0);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  min-height: 200px;
}

.tabs--pills .tabs-content {
  border-radius: var(--radius-lg);
  margin-top: var(--space-4);
}

.tab-panel {
  display: none;
  padding: var(--space-6);
  animation: fadeIn 0.3s ease-out;
}

.tab-panel--active {
  display: block;
}

/* ===== PAGINATION ===== */
.pagination {
  display: flex;
  justify-content: center;
  padding: var(--space-4) 0;
}

.pagination-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-1);
}

.pagination-item {
  /* Item styling */
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--neutral-300);
  background: var(--neutral-0);
  color: var(--neutral-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.pagination-button:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-1px);
}

.pagination-button--active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: var(--neutral-0);
  font-weight: var(--font-bold);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== STEPPER ===== */
.stepper {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding: var(--space-6);
}

.stepper--vertical {
  flex-direction: column;
}

.stepper--horizontal {
  flex-direction: row;
  align-items: center;
}

.stepper-step {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  flex: 1;
  position: relative;
}

.stepper--horizontal .stepper-step {
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stepper-step-button {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: right;
  width: 100%;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.stepper--horizontal .stepper-step-button {
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stepper-step-button:hover {
  background: var(--neutral-50);
}

.stepper-step-button:disabled {
  cursor: default;
}

.stepper-step-indicator {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  flex-shrink: 0;
  transition: all var(--transition-fast);
}

.stepper-step--pending .stepper-step-indicator {
  background: var(--neutral-200);
  color: var(--neutral-600);
  border: 2px solid var(--neutral-300);
}

.stepper-step--current .stepper-step-indicator {
  background: var(--primary-500);
  color: var(--neutral-0);
  border: 2px solid var(--primary-500);
  box-shadow: 0 0 0 4px var(--primary-100);
}

.stepper-step--completed .stepper-step-indicator {
  background: var(--success-500);
  color: var(--neutral-0);
  border: 2px solid var(--success-500);
}

.stepper-step-content {
  flex: 1;
  margin-top: var(--space-2);
}

.stepper--horizontal .stepper-step-content {
  margin-top: var(--space-3);
}

.stepper-step-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
  line-height: var(--leading-tight);
}

.stepper-step--pending .stepper-step-title {
  color: var(--neutral-600);
}

.stepper-step-description {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
}

.stepper-connector {
  position: absolute;
  background: var(--neutral-300);
  transition: background var(--transition-fast);
}

.stepper--vertical .stepper-connector {
  width: 2px;
  height: 100%;
  right: 19px;
  top: 50px;
}

.stepper--horizontal .stepper-connector {
  height: 2px;
  width: 100%;
  top: 19px;
  left: 50%;
}

.stepper-step--completed + .stepper-step .stepper-connector,
.stepper-step--current .stepper-connector {
  background: var(--primary-500);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .modern-navbar-container {
    padding: 0 var(--space-4);
    height: 60px;
  }
  
  .modern-navbar-brand {
    font-size: var(--text-lg);
  }
  
  .nav-menu--horizontal {
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--neutral-0);
    border: 1px solid var(--neutral-200);
    border-top: none;
    padding: var(--space-4);
    gap: var(--space-2);
  }
  
  .dropdown-menu {
    position: fixed;
    left: var(--space-4);
    right: var(--space-4);
    width: auto;
  }
  
  /* تحسين القائمة المنسدلة للمزيد في الشاشات الصغيرة */
  .more-menu-dropdown .dropdown-menu {
    left: var(--space-4) !important;
    right: var(--space-4) !important;
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
    transform: none !important;
  }
  
  .tabs-header {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .tabs-header::-webkit-scrollbar {
    display: none;
  }
  
  .tab-button {
    flex-shrink: 0;
  }
  
  .breadcrumb-list {
    gap: var(--space-1);
  }
  
  .breadcrumb-item {
    font-size: var(--text-xs);
  }
  
  .stepper {
    padding: var(--space-4);
  }
  
  .stepper--horizontal {
    flex-direction: column;
  }
  
  .stepper--horizontal .stepper-step {
    flex-direction: row;
    align-items: flex-start;
    text-align: right;
    width: 100%;
  }
  
  .stepper--horizontal .stepper-step-button {
    flex-direction: row;
    align-items: flex-start;
    text-align: right;
  }
  
  .stepper--horizontal .stepper-step-content {
    margin-top: var(--space-2);
  }
  
  .stepper--horizontal .stepper-connector {
    width: 2px;
    height: 100%;
    right: 19px;
    top: 50px;
    left: auto;
  }
}

@media (max-width: 480px) {
  .modern-navbar-container {
    padding: 0 var(--space-3);
  }
  
  .pagination-button {
    width: 36px;
    height: 36px;
    font-size: var(--text-xs);
  }
  
  .stepper-step-indicator {
    width: 32px;
    height: 32px;
    font-size: var(--text-xs);
  }
  
  .stepper--vertical .stepper-connector {
    right: 15px;
    top: 42px;
  }
}

/* ===== MORE MENU DROPDOWN SPECIFIC STYLES ===== */
.more-menu-dropdown {
  position: relative;
}

.more-menu-dropdown .nav-item-trigger {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--neutral-700);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  border: none;
  background: transparent;
  cursor: pointer;
  white-space: nowrap;
}

.more-menu-dropdown .nav-item-trigger:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  transform: translateY(-1px);
}

.more-menu-dropdown .nav-item-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.more-menu-dropdown .nav-item-text {
  flex: 1;
}

.more-menu-dropdown .nav-item-arrow {
  font-size: var(--text-xs);
  transition: transform var(--transition-fast);
}

.more-menu-dropdown[data-open="true"] .nav-item-arrow {
  transform: rotate(180deg);
}

/* تحسين محاذاة القائمة المنسدلة للمزيد */
.more-menu-dropdown .dropdown-menu,
.dropdown-menu--bottom-left {
  min-width: 220px;
  max-width: 280px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--neutral-200);
  background: var(--neutral-0);
  border-radius: var(--radius-xl);
  padding: var(--space-2);
  animation: dropdownSlideIn 0.2s ease-out;
  /* التأكد من أن القائمة تظهر فوق العناصر الأخرى */
  z-index: 9999 !important;
  /* إزالة أي تحويلات افتراضية قد تؤثر على المحاذاة */
  position: fixed !important;
}

/* إزالة أي margin أو transform افتراضي للقائمة المنسدلة "المزيد" */
.more-menu-dropdown .dropdown-menu {
  margin: 0 !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  position: fixed !important;
  transform: none !important;
}

/* تأكيد أن القائمة المنسدلة "المزيد" تستخدم التموضع المخصص */
.more-menu-dropdown .dropdown-menu--bottom-left,
.more-menu-dropdown .dropdown-menu--bottom-right,
.more-menu-dropdown .dropdown-menu--bottom-center {
  position: fixed !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  margin: 0 !important;
  transform: none !important;
}

.more-menu-dropdown .dropdown-item {
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-1);
  font-size: var(--text-sm);
  padding: var(--space-3) var(--space-4);
}

.more-menu-dropdown .dropdown-item:last-child {
  margin-bottom: 0;
}

.more-menu-dropdown .dropdown-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  transform: translateX(-2px);
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-navbar {
    background: var(--neutral-800);
    border-bottom-color: var(--neutral-600);
  }
  
  .modern-navbar--transparent.modern-navbar--scrolled {
    background: rgba(0, 0, 0, 0.95);
    border-bottom-color: var(--neutral-600);
  }
  
  .modern-navbar-brand {
    color: var(--primary-400);
  }
  
  .nav-item-link,
  .nav-item-button {
    color: var(--neutral-300);
  }
  
  .nav-item-link:hover,
  .nav-item-button:hover {
    background: var(--neutral-700);
    color: var(--primary-400);
  }
  
  .nav-item--active .nav-item-link,
  .nav-item--active .nav-item-button {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .dropdown-menu {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .dropdown-item {
    color: var(--neutral-300);
  }
  
  .dropdown-item:hover {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .breadcrumb-link {
    color: var(--primary-400);
  }
  
  .breadcrumb-text {
    color: var(--neutral-400);
  }
  
  .breadcrumb-current {
    color: var(--neutral-200);
  }
  
  .tabs-header {
    background: var(--neutral-700);
    border-bottom-color: var(--neutral-600);
  }
  
  .tab-button {
    color: var(--neutral-400);
  }
  
  .tab-button:hover {
    background: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .tab-button--active {
    background: var(--neutral-800);
    color: var(--primary-400);
  }
  
  .tabs-content {
    background: var(--neutral-800);
  }
  
  .pagination-button {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-300);
  }
  
  .pagination-button:hover {
    background: var(--primary-800);
    border-color: var(--primary-600);
    color: var(--primary-300);
  }
  
  .pagination-button--active {
    background: var(--primary-600);
    border-color: var(--primary-600);
  }
  
  .stepper-step-title {
    color: var(--neutral-200);
  }
  
  /* More Menu Dropdown Dark Mode */
  .more-menu-dropdown .nav-item-trigger {
    color: var(--neutral-300);
  }
  
  .more-menu-dropdown .nav-item-trigger:hover {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .more-menu-dropdown .dropdown-menu {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    box-shadow: var(--shadow-xl);
  }
  
  .more-menu-dropdown .dropdown-item {
    color: var(--neutral-300);
  }
  
  .more-menu-dropdown .dropdown-item:hover {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .stepper-step--pending .stepper-step-title {
    color: var(--neutral-400);
  }
  
  .stepper-step-description {
    color: var(--neutral-400);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-navbar,
  .dropdown-menu,
  .pagination {
    display: none !important;
  }
  
  .breadcrumb {
    border-bottom: 1px solid #000;
    padding-bottom: var(--space-2);
  }
  
  .tabs-header {
    display: none !important;
  }
  
  .tab-panel {
    display: block !important;
    padding: 0 !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .nav-item-link,
  .nav-item-button,
  .dropdown-item,
  .tab-button,
  .pagination-button,
  .stepper-step-indicator,
  .stepper-connector {
    transition: none !important;
  }
  
  .dropdown-menu,
  .tab-panel {
    animation: none !important;
  }
  
  .nav-item-link:hover,
  .nav-item-button:hover,
  .pagination-button:hover {
    transform: none !important;
  }
}

/* Focus styles for keyboard navigation */
.nav-item-link:focus,
.nav-item-button:focus,
.dropdown-item:focus,
.tab-button:focus,
.pagination-button:focus,
.stepper-step-button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-navbar,
  .dropdown-menu,
  .tabs-header,
  .tabs-content {
    border: 2px solid currentColor !important;
  }
  
  .nav-item--active .nav-item-link,
  .nav-item--active .nav-item-button,
  .tab-button--active,
  .pagination-button--active {
    background: currentColor !important;
    color: white !important;
  }
}