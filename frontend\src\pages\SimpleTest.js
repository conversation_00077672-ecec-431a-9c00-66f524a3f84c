import React, { useState, createContext, useContext } from 'react';

// إنشاء Context بسيط
const SimpleNotificationContext = createContext();

// مزود الإشعارات البسيط
const SimpleNotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = (message, type = 'info') => {
    const id = Date.now();
    const notification = { id, message, type };
    
    setNotifications(prev => [...prev, notification]);
    
    // إزالة تلقائية بعد 3 ثوانٍ
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 3000);
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <SimpleNotificationContext.Provider value={{ 
      notifications, 
      addNotification, 
      removeNotification 
    }}>
      {children}
      <NotificationContainer />
    </SimpleNotificationContext.Provider>
  );
};

// هوك استخدام الإشعارات
const useSimpleNotifications = () => {
  const context = useContext(SimpleNotificationContext);
  if (!context) {
    throw new Error('useSimpleNotifications must be used within SimpleNotificationProvider');
  }
  return context;
};

// حاوية الإشعارات
const NotificationContainer = () => {
  const { notifications } = useSimpleNotifications();

  return (
    <div style={{
      position: 'fixed',
      top: '1rem',
      right: '1rem',
      zIndex: 9999,
      display: 'flex',
      flexDirection: 'column',
      gap: '0.5rem',
      maxWidth: '300px'
    }}>
      {notifications.map(notification => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}
    </div>
  );
};

// عنصر الإشعار
const NotificationItem = ({ notification }) => {
  const { removeNotification } = useSimpleNotifications();

  const getBackgroundColor = (type) => {
    switch (type) {
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      default: return '#3b82f6';
    }
  };

  return (
    <div style={{
      backgroundColor: getBackgroundColor(notification.type),
      color: 'white',
      padding: '1rem',
      borderRadius: '0.5rem',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      transform: 'translateX(0)',
      opacity: 1
    }}
    onClick={() => removeNotification(notification.id)}
    >
      <div style={{ fontWeight: '500' }}>
        {notification.message}
      </div>
      <div style={{ fontSize: '0.75rem', opacity: 0.8, marginTop: '0.25rem' }}>
        اضغط للإغلاق
      </div>
    </div>
  );
};

// المكون الرئيسي للاختبار
const SimpleTestContent = () => {
  const { addNotification } = useSimpleNotifications();
  const [counter, setCounter] = useState(1);

  const testSuccess = () => {
    addNotification(`إشعار نجاح ${counter}`, 'success');
    setCounter(prev => prev + 1);
  };

  const testError = () => {
    addNotification(`إشعار خطأ ${counter}`, 'error');
    setCounter(prev => prev + 1);
  };

  const testWarning = () => {
    addNotification(`إشعار تحذير ${counter}`, 'warning');
    setCounter(prev => prev + 1);
  };

  const testInfo = () => {
    addNotification(`إشعار معلومات ${counter}`, 'info');
    setCounter(prev => prev + 1);
  };

  return (
    <div style={{ 
      padding: '2rem', 
      fontFamily: 'Arial, sans-serif',
      textAlign: 'center',
      backgroundColor: '#f8fafc',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#1e40af', marginBottom: '2rem' }}>
        🔔 اختبار نظام الإشعارات البسيط
      </h1>
      
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '1rem',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        maxWidth: '600px',
        margin: '0 auto'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <button 
            onClick={testSuccess}
            style={{
              padding: '1rem',
              backgroundColor: '#10b981',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            ✅ نجاح
          </button>

          <button 
            onClick={testError}
            style={{
              padding: '1rem',
              backgroundColor: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            ❌ خطأ
          </button>

          <button 
            onClick={testWarning}
            style={{
              padding: '1rem',
              backgroundColor: '#f59e0b',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            ⚠️ تحذير
          </button>

          <button 
            onClick={testInfo}
            style={{
              padding: '1rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            ℹ️ معلومات
          </button>
        </div>

        <div style={{
          backgroundColor: '#e0f2fe',
          padding: '1rem',
          borderRadius: '0.5rem',
          color: '#0277bd'
        }}>
          <p style={{ margin: 0 }}>
            💡 هذا اختبار بسيط لنظام الإشعارات. إذا كان يعمل، فالمشكلة في النظام المعقد.
          </p>
        </div>
      </div>
    </div>
  );
};

// المكون الرئيسي
const SimpleTest = () => {
  return (
    <SimpleNotificationProvider>
      <SimpleTestContent />
    </SimpleNotificationProvider>
  );
};

export default SimpleTest;
