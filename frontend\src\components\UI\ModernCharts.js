import React, { useEffect, useRef, useState } from 'react';
import './ModernCharts.css';

// Simple Chart Components (without external dependencies)

// Progress Circle Component
export const ProgressCircle = ({
  value = 0,
  max = 100,
  size = 120,
  strokeWidth = 8,
  color = 'var(--primary-500)',
  backgroundColor = 'var(--neutral-200)',
  showValue = true,
  label,
  className = '',
  ...props
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const progress = (value / max) * 100;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className={`progress-circle ${className}`} {...props}>
      <svg width={size} height={size} className="progress-circle-svg">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
          className="progress-circle-background"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="progress-circle-progress"
          style={{
            transition: 'stroke-dashoffset 1s ease-in-out',
            transform: 'rotate(-90deg)',
            transformOrigin: '50% 50%'
          }}
        />
      </svg>
      
      {(showValue || label) && (
        <div className="progress-circle-content">
          {showValue && (
            <div className="progress-circle-value">
              {Math.round(progress)}%
            </div>
          )}
          {label && (
            <div className="progress-circle-label">
              {label}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Progress Bar Component
export const ProgressBar = ({
  value = 0,
  max = 100,
  height = 8,
  color = 'var(--primary-500)',
  backgroundColor = 'var(--neutral-200)',
  showValue = false,
  label,
  striped = false,
  animated = false,
  className = '',
  ...props
}) => {
  const progress = Math.min(100, Math.max(0, (value / max) * 100));

  const barClasses = [
    'progress-bar',
    striped && 'progress-bar--striped',
    animated && 'progress-bar--animated',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={barClasses} {...props}>
      {label && (
        <div className="progress-bar-label">
          {label}
          {showValue && <span className="progress-bar-value">({Math.round(progress)}%)</span>}
        </div>
      )}
      
      <div 
        className="progress-bar-track"
        style={{ 
          height: `${height}px`,
          backgroundColor 
        }}
      >
        <div
          className="progress-bar-fill"
          style={{
            width: `${progress}%`,
            backgroundColor: color,
            transition: 'width 1s ease-in-out'
          }}
        />
      </div>
      
      {showValue && !label && (
        <div className="progress-bar-value-only">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
};

// Simple Bar Chart Component
export const BarChart = ({
  data = [],
  width = 400,
  height = 300,
  color = 'var(--primary-500)',
  showValues = true,
  showGrid = true,
  className = '',
  ...props
}) => {
  const maxValue = Math.max(...data.map(item => item.value));
  const barWidth = (width - 60) / data.length - 10;
  const chartHeight = height - 60;

  return (
    <div className={`bar-chart ${className}`} {...props}>
      <svg width={width} height={height} className="bar-chart-svg">
        {/* Grid lines */}
        {showGrid && (
          <g className="bar-chart-grid">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
              <line
                key={index}
                x1={40}
                y1={30 + chartHeight * ratio}
                x2={width - 20}
                y2={30 + chartHeight * ratio}
                stroke="var(--neutral-300)"
                strokeWidth={1}
                opacity={0.5}
              />
            ))}
          </g>
        )}
        
        {/* Bars */}
        {data.map((item, index) => {
          const barHeight = (item.value / maxValue) * chartHeight;
          const x = 50 + index * (barWidth + 10);
          const y = height - 30 - barHeight;
          
          return (
            <g key={index} className="bar-chart-bar">
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={item.color || color}
                rx={4}
                className="bar-chart-rect"
              />
              
              {showValues && (
                <text
                  x={x + barWidth / 2}
                  y={y - 5}
                  textAnchor="middle"
                  fontSize="12"
                  fill="var(--neutral-700)"
                  className="bar-chart-value"
                >
                  {item.value}
                </text>
              )}
              
              <text
                x={x + barWidth / 2}
                y={height - 10}
                textAnchor="middle"
                fontSize="12"
                fill="var(--neutral-600)"
                className="bar-chart-label"
              >
                {item.label}
              </text>
            </g>
          );
        })}
      </svg>
    </div>
  );
};

// Simple Line Chart Component
export const LineChart = ({
  data = [],
  width = 400,
  height = 300,
  color = 'var(--primary-500)',
  strokeWidth = 3,
  showPoints = true,
  showGrid = true,
  smooth = false,
  className = '',
  ...props
}) => {
  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const valueRange = maxValue - minValue || 1;
  const chartWidth = width - 80;
  const chartHeight = height - 60;

  const getPoint = (index, value) => {
    const x = 50 + (index / (data.length - 1)) * chartWidth;
    const y = 30 + ((maxValue - value) / valueRange) * chartHeight;
    return { x, y };
  };

  const pathData = data.map((item, index) => {
    const point = getPoint(index, item.value);
    return `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`;
  }).join(' ');

  return (
    <div className={`line-chart ${className}`} {...props}>
      <svg width={width} height={height} className="line-chart-svg">
        {/* Grid lines */}
        {showGrid && (
          <g className="line-chart-grid">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
              <line
                key={index}
                x1={40}
                y1={30 + chartHeight * ratio}
                x2={width - 20}
                y2={30 + chartHeight * ratio}
                stroke="var(--neutral-300)"
                strokeWidth={1}
                opacity={0.5}
              />
            ))}
          </g>
        )}
        
        {/* Line */}
        <path
          d={pathData}
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          className="line-chart-path"
        />
        
        {/* Points */}
        {showPoints && data.map((item, index) => {
          const point = getPoint(index, item.value);
          return (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r={4}
              fill={color}
              stroke="var(--neutral-0)"
              strokeWidth={2}
              className="line-chart-point"
            />
          );
        })}
        
        {/* Labels */}
        {data.map((item, index) => {
          const point = getPoint(index, item.value);
          return (
            <text
              key={index}
              x={point.x}
              y={height - 10}
              textAnchor="middle"
              fontSize="12"
              fill="var(--neutral-600)"
              className="line-chart-label"
            >
              {item.label}
            </text>
          );
        })}
      </svg>
    </div>
  );
};

// Donut Chart Component
export const DonutChart = ({
  data = [],
  size = 200,
  innerRadius = 0.6,
  showLabels = true,
  showValues = true,
  className = '',
  ...props
}) => {
  const radius = size / 2 - 10;
  const innerR = radius * innerRadius;
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  let currentAngle = 0;
  const segments = data.map((item, index) => {
    const percentage = (item.value / total) * 100;
    const angle = (item.value / total) * 360;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    
    currentAngle += angle;
    
    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);
    
    const x1 = size / 2 + radius * Math.cos(startAngleRad);
    const y1 = size / 2 + radius * Math.sin(startAngleRad);
    const x2 = size / 2 + radius * Math.cos(endAngleRad);
    const y2 = size / 2 + radius * Math.sin(endAngleRad);
    
    const x3 = size / 2 + innerR * Math.cos(endAngleRad);
    const y3 = size / 2 + innerR * Math.sin(endAngleRad);
    const x4 = size / 2 + innerR * Math.cos(startAngleRad);
    const y4 = size / 2 + innerR * Math.sin(startAngleRad);
    
    const largeArcFlag = angle > 180 ? 1 : 0;
    
    const pathData = [
      `M ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      `L ${x3} ${y3}`,
      `A ${innerR} ${innerR} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
      'Z'
    ].join(' ');
    
    return {
      ...item,
      pathData,
      percentage,
      startAngle,
      endAngle
    };
  });

  return (
    <div className={`donut-chart ${className}`} {...props}>
      <svg width={size} height={size} className="donut-chart-svg">
        {segments.map((segment, index) => (
          <path
            key={index}
            d={segment.pathData}
            fill={segment.color || `hsl(${index * 360 / data.length}, 70%, 60%)`}
            className="donut-chart-segment"
          />
        ))}
      </svg>
      
      {(showLabels || showValues) && (
        <div className="donut-chart-legend">
          {segments.map((segment, index) => (
            <div key={index} className="donut-chart-legend-item">
              <div 
                className="donut-chart-legend-color"
                style={{ 
                  backgroundColor: segment.color || `hsl(${index * 360 / data.length}, 70%, 60%)` 
                }}
              />
              {showLabels && (
                <span className="donut-chart-legend-label">{segment.label}</span>
              )}
              {showValues && (
                <span className="donut-chart-legend-value">
                  {segment.value} ({Math.round(segment.percentage)}%)
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Metric Display Component
export const MetricDisplay = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  color = 'var(--primary-500)',
  size = 'md',
  trend,
  className = '',
  ...props
}) => {
  const displayClasses = [
    'metric-display',
    `metric-display--${size}`,
    className
  ].filter(Boolean).join(' ');

  const changeClasses = [
    'metric-display-change',
    `metric-display-change--${changeType}`
  ].filter(Boolean).join(' ');

  return (
    <div className={displayClasses} {...props}>
      <div className="metric-display-header">
        {icon && (
          <div className="metric-display-icon" style={{ color }}>
            {icon}
          </div>
        )}
        <div className="metric-display-title">{title}</div>
      </div>
      
      <div className="metric-display-value" style={{ color }}>
        {value}
      </div>
      
      {change && (
        <div className={changeClasses}>
          <span className="metric-display-change-icon">
            {changeType === 'positive' ? '↗' : changeType === 'negative' ? '↘' : '→'}
          </span>
          <span className="metric-display-change-value">{change}</span>
        </div>
      )}
      
      {trend && (
        <div className="metric-display-trend">
          <svg width="60" height="20" className="metric-display-trend-svg">
            <polyline
              points={trend.map((value, index) => 
                `${(index / (trend.length - 1)) * 60},${20 - (value / Math.max(...trend)) * 15}`
              ).join(' ')}
              fill="none"
              stroke={color}
              strokeWidth="2"
              opacity="0.7"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

// Gauge Chart Component
export const GaugeChart = ({
  value = 0,
  min = 0,
  max = 100,
  size = 200,
  thickness = 20,
  color = 'var(--primary-500)',
  backgroundColor = 'var(--neutral-200)',
  showValue = true,
  label,
  className = '',
  ...props
}) => {
  const radius = (size - thickness) / 2;
  const circumference = Math.PI * radius;
  const progress = Math.min(max, Math.max(min, value));
  const progressRatio = (progress - min) / (max - min);
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progressRatio * circumference);

  return (
    <div className={`gauge-chart ${className}`} {...props}>
      <svg width={size} height={size / 2 + 40} className="gauge-chart-svg">
        {/* Background arc */}
        <path
          d={`M ${thickness / 2} ${size / 2} A ${radius} ${radius} 0 0 1 ${size - thickness / 2} ${size / 2}`}
          stroke={backgroundColor}
          strokeWidth={thickness}
          fill="transparent"
          strokeLinecap="round"
          className="gauge-chart-background"
        />
        
        {/* Progress arc */}
        <path
          d={`M ${thickness / 2} ${size / 2} A ${radius} ${radius} 0 0 1 ${size - thickness / 2} ${size / 2}`}
          stroke={color}
          strokeWidth={thickness}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="gauge-chart-progress"
          style={{
            transition: 'stroke-dashoffset 1s ease-in-out'
          }}
        />
      </svg>
      
      <div className="gauge-chart-content">
        {showValue && (
          <div className="gauge-chart-value" style={{ color }}>
            {Math.round(progress)}
          </div>
        )}
        {label && (
          <div className="gauge-chart-label">
            {label}
          </div>
        )}
      </div>
    </div>
  );
};

export default {
  ProgressCircle,
  ProgressBar,
  BarChart,
  LineChart,
  DonutChart,
  MetricDisplay,
  GaugeChart
};