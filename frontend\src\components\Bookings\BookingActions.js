import React from 'react';
import './BookingActions.css';

// مكون أزرار العمليات للحجوزات - نسخة مُصلحة
export const BookingActions = ({ 
  booking, 
  onView, 
  onEdit, 
  onDelete, 
  onPrint, 
  onSavePDF,
  showAll = true 
}) => {
  // التحقق من وجود البيانات
  if (!booking) {
    return <div>لا توجد بيانات</div>;
  }

  const handleAction = (action, actionFunction) => {
    try {
      if (typeof actionFunction === 'function') {
        actionFunction(booking);
      } else {
        console.warn(`الدالة ${action} غير معرفة`);
      }
    } catch (error) {
      console.error(`خطأ في تنفيذ ${action}:`, error);
    }
  };

  return (
    <div className="booking-actions">
      {showAll && (
        <>
          <button
            className="booking-action-button action-view"
            title="عرض التفاصيل"
            onClick={() => handleAction('عرض', onView)}
          >
            👁️
          </button>
          <button
            className="booking-action-button action-edit"
            title="تعديل"
            onClick={() => handleAction('تعديل', onEdit)}
          >
            ✏️
          </button>
          <button
            className="booking-action-button action-delete"
            title="حذف"
            onClick={() => handleAction('حذف', onDelete)}
          >
            🗑️
          </button>
        </>
      )}
      <button
        className="booking-action-button action-print"
        title="طباعة"
        onClick={() => handleAction('طباعة', onPrint)}
      >
        🖨️
      </button>
      <button
        className="booking-action-button action-pdf"
        title="حفظ PDF"
        onClick={() => handleAction('حفظ PDF', onSavePDF)}
      >
        📄
      </button>
    </div>
  );
};

// مكون أزرار العمليات المجمعة - نسخة مُصلحة
export const BulkActions = ({ 
  selectedBookings = [], 
  onBulkPrint, 
  onBulkSavePDF, 
  onBulkDelete,
  onSelectAll,
  onClearSelection 
}) => {
  const hasSelection = selectedBookings && selectedBookings.length > 0;

  const handleBulkAction = (action, actionFunction) => {
    try {
      if (typeof actionFunction === 'function') {
        actionFunction(selectedBookings);
      } else {
        console.warn(`الدالة ${action} غير معرفة`);
      }
    } catch (error) {
      console.error(`خطأ في تنفيذ ${action}:`, error);
    }
  };

  return (
    <div className="bulk-actions-container">
      <div className="bulk-actions-info">
        <button
          className="booking-action-button"
          onClick={() => {
            try {
              if (typeof onSelectAll === 'function') {
                onSelectAll();
              }
            } catch (error) {
              console.error('خطأ في تحديد الكل:', error);
            }
          }}
          style={{
            background: 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
            color: 'white'
          }}
        >
          ☑️ تحديد الكل
        </button>
        {hasSelection && (
          <button
            className="booking-action-button"
            onClick={() => {
              try {
                if (typeof onClearSelection === 'function') {
                  onClearSelection();
                }
              } catch (error) {
                console.error('خطأ في إلغاء التحديد:', error);
              }
            }}
            style={{
              background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
              color: 'white'
            }}
          >
            ❌ إلغاء التحديد
          </button>
        )}
      </div>
      
      {hasSelection && (
        <div className="bulk-actions-buttons">
          <span className="selection-count" style={{
            fontWeight: 'bold',
            color: '#2c3e50',
            marginLeft: '15px'
          }}>
            تم تحديد {selectedBookings.length} عنصر
          </span>
          <button
            className="booking-action-button action-print"
            onClick={() => handleBulkAction('طباعة مجمعة', onBulkPrint)}
          >
            🖨️ طباعة المحدد
          </button>
          <button
            className="booking-action-button action-pdf"
            onClick={() => handleBulkAction('PDF مجمع', onBulkSavePDF)}
          >
            📄 حفظ PDF
          </button>
          <button
            className="booking-action-button action-delete"
            onClick={() => handleBulkAction('حذف مجمع', onBulkDelete)}
          >
            🗑️ حذف المحدد
          </button>
        </div>
      )}
    </div>
  );
};

// مكون نافذة عرض التفاصيل - نسخة مُصلحة
export const BookingDetailsModal = ({ booking, isOpen, onClose, onPrint, onSavePDF }) => {
  if (!isOpen || !booking) return null;

  const handleModalAction = (action, actionFunction) => {
    try {
      if (typeof actionFunction === 'function') {
        actionFunction(booking);
      } else {
        console.warn(`الدالة ${action} غير معرفة`);
      }
    } catch (error) {
      console.error(`خطأ في تنفيذ ${action}:`, error);
    }
  };

  const safeValue = (value, defaultValue = '-') => {
    return value !== null && value !== undefined && value !== '' ? value : defaultValue;
  };

  return (
    <div className="booking-modal-overlay" onClick={onClose}>
      <div className="booking-modal-content" onClick={e => e.stopPropagation()}>
        <div className="booking-modal-header">
          <h3>تفاصيل الحجز #{safeValue(booking.id)}</h3>
          <div className="booking-modal-actions">
            <button
              className="booking-action-button action-print"
              title="طباعة"
              onClick={() => handleModalAction('طباعة', onPrint)}
            >
              🖨️
            </button>
            <button
              className="booking-action-button action-pdf"
              title="حفظ PDF"
              onClick={() => handleModalAction('حفظ PDF', onSavePDF)}
            >
              📄
            </button>
            <button
              className="booking-action-button"
              title="إغلاق"
              onClick={onClose}
              style={{
                background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
                color: 'white'
              }}
            >
              ✕
            </button>
          </div>
        </div>
        
        <div className="booking-modal-body">
          <div className="booking-details-grid">
            <div className="detail-item">
              <label>رقم الحجز:</label>
              <span>{safeValue(booking.id)}</span>
            </div>
            <div className="detail-item">
              <label>اسم العميل:</label>
              <span>{safeValue(booking.customerName)}</span>
            </div>
            <div className="detail-item">
              <label>رقم الهاتف:</label>
              <span>{safeValue(booking.customerPhone)}</span>
            </div>
            <div className="detail-item">
              <label>البريد الإلكتروني:</label>
              <span>{safeValue(booking.customerEmail)}</span>
            </div>
            <div className="detail-item">
              <label>تاريخ الحجز:</label>
              <span>{safeValue(booking.bookingDate || booking.date)}</span>
            </div>
            <div className="detail-item">
              <label>الحالة:</label>
              <span className={`status-badge status-${booking.status || 'unknown'}`}>
                {booking.status === 'confirmed' ? 'مؤكد' :
                 booking.status === 'pending' ? 'في الانتظار' :
                 booking.status === 'cancelled' ? 'ملغي' : 
                 safeValue(booking.status)}
              </span>
            </div>
            <div className="detail-item">
              <label>المبلغ:</label>
              <span>{safeValue(booking.amount)} {booking.currency || 'ريال'}</span>
            </div>
            <div className="detail-item">
              <label>حالة الدفع:</label>
              <span>{booking.paymentStatus === 'paid' ? 'مدفوع' :
                     booking.paymentStatus === 'partial' ? 'جزئي' :
                     booking.paymentStatus === 'unpaid' ? 'غير مدفوع' :
                     safeValue(booking.paymentStatus)}</span>
            </div>
            {booking.service && (
              <div className="detail-item">
                <label>الخدمة:</label>
                <span>{safeValue(booking.service)}</span>
              </div>
            )}
            {booking.destination && (
              <div className="detail-item">
                <label>الوجهة:</label>
                <span>{safeValue(booking.destination)}</span>
              </div>
            )}
            {booking.packageType && (
              <div className="detail-item">
                <label>نوع الباقة:</label>
                <span>{safeValue(booking.packageType)}</span>
              </div>
            )}
            {booking.departureDate && (
              <div className="detail-item">
                <label>تاريخ المغادرة:</label>
                <span>{safeValue(booking.departureDate)}</span>
              </div>
            )}
            {booking.returnDate && (
              <div className="detail-item">
                <label>تاريخ العودة:</label>
                <span>{safeValue(booking.returnDate)}</span>
              </div>
            )}
            {booking.notes && (
              <div className="detail-item full-width">
                <label>ملاحظات:</label>
                <span>{safeValue(booking.notes)}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="booking-modal-footer">
          <button
            className="booking-action-button action-print"
            onClick={() => handleModalAction('طباعة', onPrint)}
            style={{ marginLeft: '10px' }}
          >
            🖨️ طباعة
          </button>
          <button
            className="booking-action-button action-pdf"
            onClick={() => handleModalAction('حفظ PDF', onSavePDF)}
            style={{ marginLeft: '10px' }}
          >
            📄 حفظ PDF
          </button>
          <button
            className="booking-action-button"
            onClick={onClose}
            style={{
              background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
              color: 'white'
            }}
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default { BookingActions, BulkActions, BookingDetailsModal };