.accounting-periods {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.periods-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.periods-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.current-period-card {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 15px;
  padding: 25px;
  color: white;
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.current-period-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.current-period-card h3 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

.period-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.period-status.active {
  background: rgba(255,255,255,0.2);
  color: white;
}

.period-status.closed {
  background: #e74c3c20;
  color: #e74c3c;
}

.period-status.draft {
  background: #f39c1220;
  color: #f39c12;
}

.period-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.period-details h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.period-details p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.period-stats {
  flex: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  display: block;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.periods-list {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f2f6;
}

.list-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: bold;
}

.list-filters {
  display: flex;
  gap: 10px;
}

.filter-select {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.periods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.period-card {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.period-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.period-card.active {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db05, #2980b905);
}

.period-card.closed {
  border-color: #e74c3c;
  background: linear-gradient(135deg, #e74c3c05, #c0392b05);
}

.period-card.draft {
  border-color: #f39c12;
  background: linear-gradient(135deg, #f39c1205, #e67e2205);
}

.period-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.period-title {
  flex: 1;
}

.period-title h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.active-badge {
  background: #27ae60;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.period-details {
  margin-bottom: 20px;
}

.date-range {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.date-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.date-item .label {
  font-size: 11px;
  color: #7f8c8d;
  font-weight: bold;
  text-transform: uppercase;
}

.date-item .date {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
}

.period-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f1f2f6;
}

.meta-item:last-child {
  border-bottom: none;
}

.meta-item .label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.meta-item .value {
  font-size: 12px;
  color: #2c3e50;
}

.period-description {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 15px;
}

.period-description p {
  margin: 0;
  font-size: 13px;
  color: #2c3e50;
  line-height: 1.4;
}

.period-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.period-actions .btn {
  flex: 1;
  min-width: 80px;
  padding: 8px 12px;
  font-size: 12px;
}

/* نموذج الفترة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.period-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 2px solid #f1f2f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 25px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.modal-footer {
  padding: 25px;
  border-top: 2px solid #f1f2f6;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.btn-info {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .periods-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .period-info {
    flex-direction: column;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .periods-grid {
    grid-template-columns: 1fr;
  }
  
  .list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .date-range {
    flex-direction: column;
    gap: 10px;
  }
  
  .period-actions {
    justify-content: center;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .accounting-periods {
    padding: 10px;
  }
  
  .periods-header,
  .current-period-card,
  .periods-list,
  .modal-content {
    padding: 15px;
  }
  
  .period-modal {
    margin: 10px;
    max-width: none;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .period-actions .btn {
    min-width: 60px;
    padding: 6px 8px;
    font-size: 11px;
  }
}