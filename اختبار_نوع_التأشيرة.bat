@echo off
chcp 65001 >nul
title اختبار نوع التأشيرة (فردي/عادي)

echo.
echo ========================================
echo    🎫 اختبار نوع التأشيرة الجديد
echo      فردي / عادي
echo ========================================
echo.

echo 🌐 فتح النظام...
start http://localhost:3000/customers

echo.
echo 📋 قائمة اختبار نوع التأشيرة:
echo.
echo ✅ 1. اختبار عرض العمود الجديد:
echo    □ تحقق من وجود عمود "نوع التأشيرة" في الجدول
echo    □ تحقق من عرض القيم: "فردي" أو "عادي"
echo    □ تحقق من الألوان:
echo      • فردي: خلفية خضراء فاتحة، نص أخضر
echo      • عادي: خلفية صفراء فاتحة، نص برتقالي
echo    □ تحقق من موقع العمود بعد "نوع الفيزا"
echo.
echo ✅ 2. اختبار إدارة الأعمدة:
echo    □ اضغط زر "📋 إدارة الأعمدة"
echo    □ تحقق من وجود "نوع التأشيرة" في قائمة الأعمدة
echo    □ تحقق من أنه معروض افتراضياً (☑️)
echo    □ جرب إخفاء العمود وإظهاره
echo    □ تحقق من التحديث الفوري في الجدول
echo.
echo ✅ 3. اختبار إضافة عميل جديد:
echo    □ اضغط تبويب "إضافة عميل"
echo    □ ابحث عن حقل "نوع التأشيرة"
echo    □ تحقق من وجوده بعد حقل "نوع الفيزا"
echo    □ تحقق من الخيارات المتاحة:
echo      • عادي (افتراضي)
echo      • فردي
echo    □ تحقق من أن القيمة الافتراضية هي "عادي"
echo.
echo ✅ 4. اختبار إضافة عميل بنوع فردي:
echo    □ املأ بيانات عميل جديد
echo    □ اختر "فردي" في حقل نوع التأشيرة
echo    □ اضغط "إضافة العميل"
echo    □ ارجع لقائمة العملاء
echo    □ تحقق من ظهور "فردي" بالتنسيق الصحيح
echo.
echo ✅ 5. اختبار إضافة عميل بنوع عادي:
echo    □ أضف عميل آخر
echo    □ اختر "عادي" في حقل نوع التأشيرة
echo    □ اضغط "إضافة العميل"
echo    □ ارجع لقائمة العملاء
echo    □ تحقق من ظهور "عادي" بالتنسيق الصحيح
echo.
echo ✅ 6. اختبار فلتر نوع التأشيرة:
echo    □ تحقق من وجود فلتر "جميع أنواع التأشيرة" في شريط الفلاتر
echo    □ تحقق من موقعه بعد فلتر "نوع الفيزا"
echo    □ تحقق من الخيارات:
echo      • جميع أنواع التأشيرة (افتراضي)
echo      • فردي
echo      • عادي
echo.
echo ✅ 7. اختبار فلترة بنوع "فردي":
echo    □ اختر "فردي" من فلتر نوع التأشيرة
echo    □ تحقق من عرض العملاء ذوي التأشيرة الفردية فقط
echo    □ تحقق من ظهور مؤشر الفلتر النشط:
echo      "🔍 الفلاتر النشطة: نوع التأشيرة"
echo    □ تحقق من اللون البنفسجي للمؤشر
echo.
echo ✅ 8. اختبار فلترة بنوع "عادي":
echo    □ اختر "عادي" من فلتر نوع التأشيرة
echo    □ تحقق من عرض العملاء ذوي التأشيرة العادية فقط
echo    □ تحقق من تحديث مؤشر الفلتر النشط
echo    □ تحقق من دقة النتائج
echo.
echo ✅ 9. اختبار مسح الفلاتر:
echo    □ اضغط زر "🗑️ مسح الفلاتر"
echo    □ تحقق من إعادة تعيين فلتر نوع التأشيرة إلى "جميع أنواع التأشيرة"
echo    □ تحقق من عرض جميع العملاء
echo    □ تحقق من اختفاء مؤشر الفلتر النشط
echo.
echo ✅ 10. اختبار الطباعة:
echo    □ حدد أعمدة تشمل "نوع التأشيرة"
echo    □ اضغط زر "🖨️ طباعة"
echo    □ تحقق من ظهور عمود "نوع التأشيرة" في التقرير
echo    □ تحقق من عرض القيم بالنص العربي:
echo      • "فردي" للتأشيرات الفردية
echo      • "عادي" للتأشيرات العادية
echo.
echo ✅ 11. اختبار التصدير:
echo    □ حدد أعمدة تشمل "نوع التأشيرة"
echo    □ اضغط زر "📊 تصدير Excel"
echo    □ افتح الملف المُصدر
echo    □ تحقق من وجود عمود "نوع التأشيرة"
echo    □ تحقق من صحة البيانات المُصدرة
echo.
echo ✅ 12. اختبار البيانات التجريبية:
echo    □ تحقق من العميل الأول (أحمد محمد العلي):
echo      • نوع التأشيرة: فردي
echo      • التنسيق: خلفية خضراء، نص أخضر
echo    □ تحقق من العميل الثاني (سارة عبدالله الخالد):
echo      • نوع التأشيرة: عادي
echo      • التنسيق: خلفية صفراء، نص برتقالي
echo.
echo ========================================
echo.
echo 🎯 سيناريوهات اختبار متقدمة:
echo.
echo 📊 سيناريو 1: تقرير التأشيرات الفردية
echo    1. اختر فلتر "فردي" من نوع التأشيرة
echo    2. أخف الأعمدة غير المطلوبة
echo    3. أظهر: اسم العميل، نوع الفيزا، نوع التأشيرة، حالة المعاملة
echo    4. اطبع التقرير
echo    5. تحقق من التقرير المخصص للتأشيرات الفردية
echo.
echo 📊 سيناريو 2: تقرير التأشيرات العادية
echo    1. اختر فلتر "عادي" من نوع التأشيرة
echo    2. حدد الأعمدة المطلوبة
echo    3. صدر البيانات
echo    4. تحقق من ملف التصدير
echo.
echo 📊 سيناريو 3: مقارنة الأنواع
echo    1. أظهر عمود "نوع التأشيرة" فقط
echo    2. اعرض جميع العملاء
echo    3. راقب توزيع الأنواع (فردي/عادي)
echo    4. استخدم الفلاتر للتبديل بين الأنواع
echo.
echo 📊 سيناريو 4: إضافة عملاء متنوعين
echo    1. أضف 3 عملاء بتأشيرة فردية
echo    2. أضف 3 عملاء بتأشيرة عادية
echo    3. تحقق من التنسيق الصحيح لكل نوع
echo    4. جرب الفلترة والبحث
echo.
echo ========================================
echo.
echo 🔍 نقاط التحقق المهمة:
echo.
echo ✅ العرض:
echo    □ عمود "نوع التأشيرة" ظاهر في الجدول
echo    □ الألوان صحيحة (أخضر للفردي، برتقالي للعادي)
echo    □ النصوص عربية واضحة
echo    □ العرض 120px مناسب
echo.
echo ✅ الإدخال:
echo    □ حقل نوع التأشيرة في نموذج إضافة العميل
echo    □ القيمة الافتراضية "عادي"
echo    □ خيارات واضحة (فردي/عادي)
echo    □ الحفظ يعمل بشكل صحيح
echo.
echo ✅ الفلترة:
echo    □ فلتر نوع التأشيرة في شريط الفلاتر
echo    □ الفلترة تعمل بدقة
echo    □ مؤشر الفلتر النشط يظهر
echo    □ مسح الفلاتر يعمل
echo.
echo ✅ التقارير:
echo    □ الطباعة تشمل العمود الجديد
echo    □ التصدير يحتوي على البيانات الصحيحة
echo    □ النصوص العربية في التقارير
echo    □ التنسيق مناسب للطباعة
echo.
echo ========================================
echo.
echo 💡 نصائح للاختبار:
echo.
echo 🔍 للاختبار الفعال:
echo    - ابدأ بفحص العرض في الجدول
echo    - جرب إضافة عملاء بكلا النوعين
echo    - اختبر الفلترة بعناية
echo    - تحقق من التقارير والتصدير
echo.
echo 📊 للتحقق من الدقة:
echo    - قارن البيانات المدخلة مع المعروضة
echo    - تأكد من الألوان والتنسيق
echo    - اختبر الفلترة مع بيانات متنوعة
echo    - راجع ملفات التصدير
echo.
echo 🎨 للواجهة:
echo    - تحقق من التناسق مع باقي الأعمدة
echo    - راقب الألوان والخطوط
echo    - تأكد من وضوح النصوص
echo    - اختبر على شاشات مختلفة
echo.
echo ========================================
echo.
echo 🎯 معايير النجاح:
echo.
echo ✅ العمود الجديد يظهر في الجدول بشكل صحيح
echo ✅ حقل الإدخال يعمل في نموذج إضافة العميل
echo ✅ الفلتر الجديد يعمل بدقة
echo ✅ الألوان والتنسيق مناسبان
echo ✅ الطباعة والتصدير يشملان العمود الجديد
echo ✅ البيانات التجريبية تعرض بشكل صحيح
echo ✅ مؤشر الفلتر النشط يعمل
echo ✅ مسح الفلاتر يعيد تعيين الفلتر الجديد
echo ✅ إدارة الأعمدة تتضمن العمود الجديد
echo ✅ لا توجد أخطاء في الكونسول
echo.
echo ========================================
echo.
echo 🎉 إذا نجحت جميع الاختبارات:
echo    نوع التأشيرة (فردي/عادي) يعمل بنجاح!
echo    يمكنك الآن تصنيف العملاء حسب نوع التأشيرة!
echo.
echo 🔧 إذا فشل أي اختبار:
echo    تحقق من الكونسول للأخطاء
echo    راجع الكود المضاف
echo    أعد تحميل الصفحة وحاول مرة أخرى
echo.
echo 📊 للاستخدام العملي:
echo    استخدم "فردي" للتأشيرات الخاصة
echo    استخدم "عادي" للتأشيرات العامة
echo    استفد من الفلترة لإنشاء تقارير مخصصة
echo    راقب توزيع الأنواع في قاعدة البيانات
echo.
echo اضغط أي مفتاح للخروج...
pause >nul