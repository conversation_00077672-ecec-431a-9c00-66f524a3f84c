import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import BookingAnalytics from '../Analytics/BookingAnalytics';
import { useNotifications } from '../UI/EnhancedNotifications';
import { useShortcuts } from '../UI/KeyboardShortcuts';
import './BookingsDashboard.css';

// 📊 لوحة تحكم الحجوزات المتطورة
const BookingsDashboard = () => {
  const { success, info } = useNotifications();
  const { registerShortcut, unregisterShortcut } = useShortcuts();
  
  const [dashboardData, setDashboardData] = useState({
    recentBookings: [],
    quickStats: {},
    alerts: [],
    upcomingTasks: []
  });
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('today');

  // 📊 تحميل بيانات لوحة التحكم
  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      
      // محاكاة تحميل البيانات
      setTimeout(() => {
        const mockData = {
          recentBookings: [
            {
              id: 1,
              type: 'flight',
              customerName: 'أحمد محمد',
              destination: 'دبي',
              status: 'confirmed',
              amount: 2500,
              createdAt: new Date().toISOString(),
              icon: '✈️'
            },
            {
              id: 2,
              type: 'umrah',
              customerName: 'فاطمة علي',
              destination: 'مكة المكرمة',
              status: 'pending',
              amount: 4500,
              createdAt: new Date(Date.now() - 3600000).toISOString(),
              icon: '🕋'
            },
            {
              id: 3,
              type: 'passport',
              customerName: 'محمد سالم',
              destination: 'تجديد جواز',
              status: 'processing',
              amount: 300,
              createdAt: new Date(Date.now() - 7200000).toISOString(),
              icon: '📘'
            },
            {
              id: 4,
              type: 'hotel',
              customerName: 'سارة أحمد',
              destination: 'الرياض',
              status: 'confirmed',
              amount: 800,
              createdAt: new Date(Date.now() - 10800000).toISOString(),
              icon: '🏨'
            },
            {
              id: 5,
              type: 'visa',
              customerName: 'عبدالله خالد',
              destination: 'تركيا',
              status: 'pending',
              amount: 500,
              createdAt: new Date(Date.now() - 14400000).toISOString(),
              icon: '📋'
            }
          ],
          quickStats: {
            todayBookings: 12,
            todayRevenue: 15750,
            pendingApprovals: 8,
            completedToday: 5,
            totalCustomers: 1250,
            monthlyGrowth: 15.5
          },
          alerts: [
            {
              id: 1,
              type: 'warning',
              title: 'حجوزات تحتاج متابعة',
              message: '5 حجوزات معلقة منذ أكثر من 24 ساعة',
              action: 'مراجعة الحجوزات',
              link: '/bookings?status=pending'
            },
            {
              id: 2,
              type: 'info',
              title: 'تذكير مهم',
              message: 'موعد رحلة العمرة للمجموعة الثالثة غداً',
              action: 'عرض التفاصيل',
              link: '/bookings/umrah'
            },
            {
              id: 3,
              type: 'success',
              title: 'إنجاز ممتاز',
              message: 'تم تحقيق هدف المبيعات الشهري بنسبة 120%',
              action: 'عرض التقرير',
              link: '/reports/sales'
            }
          ],
          upcomingTasks: [
            {
              id: 1,
              title: 'متابعة حجز العمرة - فاطمة علي',
              dueDate: new Date(Date.now() + 3600000),
              priority: 'high',
              type: 'follow-up'
            },
            {
              id: 2,
              title: 'تأكيد حجز الطيران - أحمد محمد',
              dueDate: new Date(Date.now() + 7200000),
              priority: 'medium',
              type: 'confirmation'
            },
            {
              id: 3,
              title: 'إرسال تذاكر الطيران - سارة أحمد',
              dueDate: new Date(Date.now() + 10800000),
              priority: 'low',
              type: 'delivery'
            }
          ]
        };
        
        setDashboardData(mockData);
        setLoading(false);
        success('تم تحديث بيانات لوحة التحكم');
      }, 1500);
    };

    loadDashboardData();
  }, [selectedPeriod, success]);

  // ⌨️ تسجيل اختصارات لوحة المفاتيح
  useEffect(() => {
    registerShortcut('ctrl+d', 'refreshDashboard', 'تحديث لوحة التحكم', 'dashboard');
    registerShortcut('ctrl+1', 'quickNewFlight', 'حجز طيران سريع', 'dashboard');
    registerShortcut('ctrl+2', 'quickNewUmrah', 'حجز عمرة سريع', 'dashboard');

    const handleShortcutAction = (event) => {
      switch (event.detail.action) {
        case 'refreshDashboard':
          window.location.reload();
          break;
        case 'quickNewFlight':
          window.location.href = '/bookings/flight/new';
          break;
        case 'quickNewUmrah':
          window.location.href = '/bookings/umrah/new';
          break;
      }
    };

    document.addEventListener('shortcutAction', handleShortcutAction);

    return () => {
      unregisterShortcut('ctrl+d');
      unregisterShortcut('ctrl+1');
      unregisterShortcut('ctrl+2');
      document.removeEventListener('shortcutAction', handleShortcutAction);
    };
  }, [registerShortcut, unregisterShortcut]);

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner-advanced"></div>
        <p>جاري تحميل لوحة التحكم...</p>
      </div>
    );
  }

  return (
    <div className="bookings-dashboard">
      {/* 🎯 رأس لوحة التحكم */}
      <div className="dashboard-header">
        <div className="dashboard-title">
          <h1>📊 لوحة تحكم الحجوزات</h1>
          <p>نظرة شاملة على جميع الحجوزات والعمليات</p>
        </div>
        <div className="dashboard-actions">
          <select 
            value={selectedPeriod} 
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="period-selector"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="year">هذا العام</option>
          </select>
          <button 
            className="btn btn-primary"
            onClick={() => window.location.reload()}
          >
            🔄 تحديث
          </button>
        </div>
      </div>

      {/* 📊 الإحصائيات السريعة */}
      <div className="quick-stats">
        <div className="stat-card primary">
          <div className="stat-icon">📋</div>
          <div className="stat-content">
            <h3>حجوزات اليوم</h3>
            <div className="stat-value">{dashboardData.quickStats.todayBookings}</div>
            <div className="stat-change positive">+{dashboardData.quickStats.monthlyGrowth}%</div>
          </div>
        </div>

        <div className="stat-card success">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>إيرادات اليوم</h3>
            <div className="stat-value">{dashboardData.quickStats.todayRevenue?.toLocaleString()} ر.س</div>
            <div className="stat-subtitle">مكتمل: {dashboardData.quickStats.completedToday}</div>
          </div>
        </div>

        <div className="stat-card warning">
          <div className="stat-icon">⏳</div>
          <div className="stat-content">
            <h3>في الانتظار</h3>
            <div className="stat-value">{dashboardData.quickStats.pendingApprovals}</div>
            <div className="stat-subtitle">يحتاج مراجعة</div>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>إجمالي العملاء</h3>
            <div className="stat-value">{dashboardData.quickStats.totalCustomers?.toLocaleString()}</div>
            <div className="stat-subtitle">عميل نشط</div>
          </div>
        </div>
      </div>

      {/* 🚨 التنبيهات والإشعارات */}
      <div className="dashboard-alerts">
        <h2>🚨 التنبيهات المهمة</h2>
        <div className="alerts-grid">
          {dashboardData.alerts.map(alert => (
            <div key={alert.id} className={`alert-card ${alert.type}`}>
              <div className="alert-content">
                <h4>{alert.title}</h4>
                <p>{alert.message}</p>
              </div>
              <Link to={alert.link} className="alert-action">
                {alert.action}
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* 📋 الحجوزات الأخيرة */}
      <div className="recent-bookings">
        <div className="section-header">
          <h2>📋 الحجوزات الأخيرة</h2>
          <Link to="/bookings" className="view-all-link">
            عرض الكل
          </Link>
        </div>
        <div className="bookings-list">
          {dashboardData.recentBookings.map(booking => (
            <div key={booking.id} className="booking-item">
              <div className="booking-icon">{booking.icon}</div>
              <div className="booking-details">
                <h4>{booking.customerName}</h4>
                <p>{booking.destination}</p>
                <span className="booking-time">
                  {new Date(booking.createdAt).toLocaleTimeString('ar-SA')}
                </span>
              </div>
              <div className="booking-status">
                <span className={`status-badge ${booking.status}`}>
                  {booking.status === 'confirmed' && 'مؤكد'}
                  {booking.status === 'pending' && 'معلق'}
                  {booking.status === 'processing' && 'قيد المعالجة'}
                </span>
              </div>
              <div className="booking-amount">
                {booking.amount.toLocaleString()} ر.س
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* ✅ المهام القادمة */}
      <div className="upcoming-tasks">
        <h2>✅ المهام القادمة</h2>
        <div className="tasks-list">
          {dashboardData.upcomingTasks.map(task => (
            <div key={task.id} className={`task-item priority-${task.priority}`}>
              <div className="task-content">
                <h4>{task.title}</h4>
                <p>موعد الاستحقاق: {task.dueDate.toLocaleString('ar-SA')}</p>
              </div>
              <div className={`task-priority ${task.priority}`}>
                {task.priority === 'high' && 'عالي'}
                {task.priority === 'medium' && 'متوسط'}
                {task.priority === 'low' && 'منخفض'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 📊 التحليلات المدمجة */}
      <div className="embedded-analytics">
        <BookingAnalytics bookings={dashboardData.recentBookings} />
      </div>
    </div>
  );
};

export default BookingsDashboard;
