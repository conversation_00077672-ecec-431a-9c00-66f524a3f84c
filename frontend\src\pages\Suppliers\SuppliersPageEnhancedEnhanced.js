import React, { useState, useEffect } from 'react';
import ServiceManager from '../../components/Suppliers/ServiceManager';
import './SuppliersPage.css';

const SuppliersPage = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  const [viewMode, setViewMode] = useState('grid'); // grid or table

  // بيانات تجريبية محسنة للموردين
  const [suppliersData, setSuppliersData] = useState([
    {
      id: 1,
      name: 'شركة الطيران العربية',
      type: 'airline',
      contact: 'أحمد محمد',
      phone: '+************',
      email: '<EMAIL>',
      address: 'الرياض، المملكة العربية السعودية',
      website: 'https://arabair.com',
      taxNumber: '*********',
      bankAccount: '*********************',
      paymentTerms: '30',
      services: ['تذاكر طيران', 'حجوزات فنادق'],
      status: 'active',
      rating: 4.5,
      totalBookings: 150,
      lastBooking: '2024-01-15',
      notes: 'مورد موثوق مع خدمة عملاء ممتازة',
      createdDate: '2023-01-15',
      totalRevenue: 450000,
      contractStartDate: '2023-01-15',
      contractEndDate: '2024-12-31',
      commission: 5.5,
      priority: 'high'
    },
    {
      id: 2,
      name: 'فنادق الخليج الدولية',
      type: 'hotel',
      contact: 'فاطمة أحمد',
      phone: '+************',
      email: '<EMAIL>',
      address: 'دبي، الإمارات العربية المتحدة',
      website: 'https://gulfhotels.com',
      taxNumber: '*********',
      bankAccount: '*********************',
      paymentTerms: '15',
      services: ['حجوزات فنادق', 'خدمات النقل'],
      status: 'active',
      rating: 4.8,
      totalBookings: 200,
      lastBooking: '2024-01-20',
      notes: 'شبكة فنادق واسعة في منطقة الخليج',
      createdDate: '2023-03-10',
      totalRevenue: 680000,
      contractStartDate: '2023-03-10',
      contractEndDate: '2025-03-09',
      commission: 7.0,
      priority: 'high'
    },
    {
      id: 3,
      name: 'شركة النقل السياحي',
      type: 'transport',
      contact: 'محمد علي',
      phone: '+***********',
      email: '<EMAIL>',
      address: 'القاهرة، مصر',
      website: 'https://transport.com',
      taxNumber: '*********',
      bankAccount: '*********************',
      paymentTerms: '7',
      services: ['نقل المطار', 'رحلات سياحية'],
      status: 'inactive',
      rating: 4.2,
      totalBookings: 80,
      lastBooking: '2023-12-10',
      notes: 'خدمات نقل متنوعة ولكن تحتاج متابعة',
      createdDate: '2023-06-20',
      totalRevenue: 120000,
      contractStartDate: '2023-06-20',
      contractEndDate: '2024-06-19',
      commission: 4.5,
      priority: 'medium'
    },
    {
      id: 4,
      name: 'وكالة السفر الذهبية',
      type: 'tour',
      contact: 'سارة خالد',
      phone: '+************',
      email: '<EMAIL>',
      address: 'جدة، المملكة العربية السعودية',
      website: 'https://goldtravel.com',
      taxNumber: '*********',
      bankAccount: '*********************',
      paymentTerms: '30',
      services: ['رحلات سياحية', 'حج وعمرة', 'سياحة داخلية'],
      status: 'active',
      rating: 4.7,
      totalBookings: 320,
      lastBooking: '2024-01-18',
      notes: 'متخصصة في رحلات الحج والعمرة',
      createdDate: '2022-11-05',
      totalRevenue: 890000,
      contractStartDate: '2022-11-05',
      contractEndDate: '2024-11-04',
      commission: 6.5,
      priority: 'high'
    },
    {
      id: 5,
      name: 'مكتب التأشيرات السريع',
      type: 'visa',
      contact: 'عبدالله أحمد',
      phone: '+************',
      email: '<EMAIL>',
      address: 'أبوظبي، الإمارات العربية المتحدة',
      website: 'https://fastvisas.com',
      taxNumber: '*********',
      bankAccount: '*********************',
      paymentTerms: '0',
      services: ['تأشيرات سياحية', 'تأشيرات عمل', 'تأشيرات دراسة'],
      status: 'active',
      rating: 4.6,
      totalBookings: 180,
      lastBooking: '2024-01-22',
      notes: 'سرعة في إنجاز المعاملات',
      createdDate: '2023-08-12',
      totalRevenue: 340000,
      contractStartDate: '2023-08-12',
      contractEndDate: '2025-08-11',
      commission: 8.0,
      priority: 'medium'
    }
  ]);

  const [newSupplier, setNewSupplier] = useState({
    name: '',
    type: 'airline',
    contact: '',
    phone: '',
    email: '',
    address: '',
    website: '',
    taxNumber: '',
    bankAccount: '',
    paymentTerms: '30',
    services: [],
    status: 'active',
    notes: '',
    contractStartDate: '',
    contractEndDate: '',
    commission: 0,
    priority: 'medium'
  });

  useEffect(() => {
    filterAndSortSuppliers();
  }, [searchTerm, filterType, filterStatus, sortBy, sortOrder, suppliersData]); // eslint-disable-line react-hooks/exhaustive-deps

  const filterAndSortSuppliers = () => {
    let filtered = [...suppliersData];

    // تطبيق البحث
    if (searchTerm) {
      filtered = filtered.filter(supplier =>
        supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.phone.includes(searchTerm)
      );
    }

    // تطبيق فلتر النوع
    if (filterType !== 'all') {
      filtered = filtered.filter(supplier => supplier.type === filterType);
    }

    // تطبيق فلتر الحالة
    if (filterStatus !== 'all') {
      filtered = filtered.filter(supplier => supplier.status === filterStatus);
    }

    // تطبيق الترتيب
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'rating' || sortBy === 'totalBookings' || sortBy === 'totalRevenue' || sortBy === 'commission') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setSuppliers(filtered);
  };

  const handleAddSupplier = () => {
    const supplier = {
      ...newSupplier,
      id: Date.now(),
      rating: 0,
      totalBookings: 0,
      lastBooking: null,
      createdDate: new Date().toISOString().split('T')[0],
      totalRevenue: 0
    };
    setSuppliersData([...suppliersData, supplier]);
    resetForm();
    setShowAddModal(false);
  };

  const handleEditSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setNewSupplier({...supplier});
    setShowEditModal(true);
  };

  const handleUpdateSupplier = () => {
    setSuppliersData(suppliersData.map(supplier =>
      supplier.id === selectedSupplier.id ? newSupplier : supplier
    ));
    setShowEditModal(false);
    setSelectedSupplier(null);
    resetForm();
  };

  const handleDeleteSupplier = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      setSuppliersData(suppliersData.filter(supplier => supplier.id !== id));
    }
  };

  const handleViewDetails = (supplier) => {
    setSelectedSupplier(supplier);
    setShowDetailsModal(true);
  };

  const handleExportData = () => {
    const csvContent = "data:text/csv;charset=utf-8," 
      + "الاسم,النوع,جهة الاتصال,الهاتف,البريد الإلكتروني,الحالة,التقييم,الحجوزات,الإيرادات\n"
      + suppliers.map(s => 
          `${s.name},${getTypeLabel(s.type)},${s.contact},${s.phone},${s.email},${s.status === 'active' ? 'نشط' : 'غير نشط'},${s.rating},${s.totalBookings},${s.totalRevenue}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "suppliers_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetForm = () => {
    setNewSupplier({
      name: '',
      type: 'airline',
      contact: '',
      phone: '',
      email: '',
      address: '',
      website: '',
      taxNumber: '',
      bankAccount: '',
      paymentTerms: '30',
      services: [],
      status: 'active',
      notes: '',
      contractStartDate: '',
      contractEndDate: '',
      commission: 0,
      priority: 'medium'
    });
  };

  const getTypeLabel = (type) => {
    const types = {
      airline: 'شركة طيران',
      hotel: 'فندق',
      transport: 'نقل',
      tour: 'رحلات سياحية',
      visa: 'خدمات تأشيرات'
    };
    return types[type] || type;
  };

  const getStatusBadge = (status) => {
    return status === 'active' ? 
      <span className="status-badge active">نشط</span> : 
      <span className="status-badge inactive">غير نشط</span>;
  };

  const getPriorityBadge = (priority) => {
    const badges = {
      high: <span className="priority-badge high">عالية</span>,
      medium: <span className="priority-badge medium">متوسطة</span>,
      low: <span className="priority-badge low">منخفضة</span>
    };
    return badges[priority] || badges.medium;
  };

  const renderStars = (rating) => {
    return '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const isContractExpiring = (endDate) => {
    const today = new Date();
    const contractEnd = new Date(endDate);
    const daysUntilExpiry = Math.ceil((contractEnd - today) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  const isContractExpired = (endDate) => {
    const today = new Date();
    const contractEnd = new Date(endDate);
    return contractEnd < today;
  };

  // حساب الصفحات
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentSuppliers = suppliers.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(suppliers.length / itemsPerPage);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  return (
    <div className="suppliers-page">
      <div className="page-header">
        <div className="header-content">
          <h1>إدارة الموردين</h1>
          <div className="header-stats">
            <div className="stat-card">
              <span className="stat-number">{suppliersData.length}</span>
              <span className="stat-label">إجمالي الموردين</span>
            </div>
            <div className="stat-card">
              <span className="stat-number">{suppliersData.filter(s => s.status === 'active').length}</span>
              <span className="stat-label">موردين نشطين</span>
            </div>
            <div className="stat-card">
              <span className="stat-number">{suppliersData.filter(s => isContractExpiring(s.contractEndDate)).length}</span>
              <span className="stat-label">عقود منتهية قريباً</span>
            </div>
            <div className="stat-card">
              <span className="stat-number">{formatCurrency(suppliersData.reduce((sum, s) => sum + s.totalRevenue, 0))}</span>
              <span className="stat-label">إجمالي الإيرادات</span>
            </div>
          </div>
        </div>
        <div className="header-actions">
          <button 
            className="export-btn"
            onClick={handleExportData}
          >
            📊 تصدير البيانات
          </button>
          <button 
            className="import-btn"
            onClick={() => setShowImportModal(true)}
          >
            📥 استيراد البيانات
          </button>
          <button 
            className="add-btn"
            onClick={() => setShowAddModal(true)}
          >
            + إضافة مورد جديد
          </button>
        </div>
      </div>

      <div className="filters-section">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في الموردين..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="filter-group">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            <option value="all">جميع الأنواع</option>
            <option value="airline">شركات الطيران</option>
            <option value="hotel">الفنادق</option>
            <option value="transport">النقل</option>
            <option value="tour">الرحلات السياحية</option>
            <option value="visa">خدمات التأشيرات</option>
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="name">الاسم</option>
            <option value="rating">التقييم</option>
            <option value="totalBookings">عدد الحجوزات</option>
            <option value="totalRevenue">الإيرادات</option>
            <option value="commission">العمولة</option>
            <option value="createdDate">تاريخ الإضافة</option>
          </select>
          
          <button
            className="sort-order-btn"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
          
          <div className="view-mode-toggle">
            <button
              className={`view-mode-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              ⊞
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => setViewMode('table')}
            >
              ☰
            </button>
          </div>
        </div>
      </div>

      {viewMode === 'grid' ? (
        <div className="suppliers-grid">
          {currentSuppliers.map(supplier => (
            <div key={supplier.id} className="supplier-card">
              <div className="card-header">
                <h3>{supplier.name}</h3>
                <div className="card-badges">
                  {getStatusBadge(supplier.status)}
                  {getPriorityBadge(supplier.priority)}
                  {isContractExpiring(supplier.contractEndDate) && (
                    <span className="contract-warning">⚠️ عقد منتهي قريباً</span>
                  )}
                  {isContractExpired(supplier.contractEndDate) && (
                    <span className="contract-expired">❌ عقد منتهي</span>
                  )}
                </div>
              </div>
              
              <div className="card-body">
                <div className="supplier-info">
                  <p><strong>النوع:</strong> {getTypeLabel(supplier.type)}</p>
                  <p><strong>جهة الاتصال:</strong> {supplier.contact}</p>
                  <p><strong>الهاتف:</strong> {supplier.phone}</p>
                  <p><strong>البريد الإلكتروني:</strong> {supplier.email}</p>
                  <p><strong>العمولة:</strong> {supplier.commission}%</p>
                </div>
                
                <div className="supplier-services">
                  <strong>الخدمات:</strong>
                  <div className="services-tags">
                    {supplier.services.slice(0, 2).map((service, index) => (
                      <span key={index} className="service-tag">{service}</span>
                    ))}
                    {supplier.services.length > 2 && (
                      <span className="service-tag more">+{supplier.services.length - 2}</span>
                    )}
                  </div>
                </div>
                
                <div className="supplier-stats">
                  <div className="stat">
                    <span className="stat-label">التقييم:</span>
                    <span className="rating">{renderStars(supplier.rating)} ({supplier.rating})</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">الحجوزات:</span>
                    <span className="stat-value">{supplier.totalBookings}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">الإيرادات:</span>
                    <span className="stat-value">{formatCurrency(supplier.totalRevenue)}</span>
                  </div>
                </div>
              </div>
              
              <div className="card-actions">
                <button 
                  className="view-btn"
                  onClick={() => handleViewDetails(supplier)}
                >
                  عرض
                </button>
                <button 
                  className="edit-btn"
                  onClick={() => handleEditSupplier(supplier)}
                >
                  تعديل
                </button>
                <button 
                  className="delete-btn"
                  onClick={() => handleDeleteSupplier(supplier.id)}
                >
                  حذف
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="suppliers-table-container">
          <table className="suppliers-table">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>النوع</th>
                <th>جهة الاتصال</th>
                <th>الهاتف</th>
                <th>التقييم</th>
                <th>الحجوزات</th>
                <th>العمولة</th>
                <th>الحالة</th>
                <th>الأولوية</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {currentSuppliers.map(supplier => (
                <tr key={supplier.id}>
                  <td>
                    <div className="supplier-name">
                      {supplier.name}
                      {isContractExpiring(supplier.contractEndDate) && (
                        <span className="contract-warning-small">⚠️</span>
                      )}
                      {isContractExpired(supplier.contractEndDate) && (
                        <span className="contract-expired-small">❌</span>
                      )}
                    </div>
                  </td>
                  <td>{getTypeLabel(supplier.type)}</td>
                  <td>{supplier.contact}</td>
                  <td>{supplier.phone}</td>
                  <td>
                    <span className="rating">{renderStars(supplier.rating)} ({supplier.rating})</span>
                  </td>
                  <td>{supplier.totalBookings}</td>
                  <td>{supplier.commission}%</td>
                  <td>{getStatusBadge(supplier.status)}</td>
                  <td>{getPriorityBadge(supplier.priority)}</td>
                  <td>
                    <div className="table-actions">
                      <button 
                        className="view-btn small"
                        onClick={() => handleViewDetails(supplier)}
                      >
                        عرض
                      </button>
                      <button 
                        className="edit-btn small"
                        onClick={() => handleEditSupplier(supplier)}
                      >
                        تعديل
                      </button>
                      <button 
                        className="delete-btn small"
                        onClick={() => handleDeleteSupplier(supplier.id)}
                      >
                        حذف
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => paginate(currentPage - 1)}
            disabled={currentPage === 1}
            className="pagination-btn"
          >
            السابق
          </button>
          
          {[...Array(totalPages)].map((_, index) => (
            <button
              key={index + 1}
              onClick={() => paginate(index + 1)}
              className={`pagination-btn ${currentPage === index + 1 ? 'active' : ''}`}
            >
              {index + 1}
            </button>
          ))}
          
          <button
            onClick={() => paginate(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="pagination-btn"
          >
            التالي
          </button>
        </div>
      )}

      {suppliers.length === 0 && (
        <div className="no-results">
          <p>لا توجد موردين مطابقين لمعايير البحث</p>
        </div>
      )}

      {/* نافذة تفاصيل المورد */}
      {showDetailsModal && selectedSupplier && (
        <div className="modal-overlay">
          <div className="modal large">
            <div className="modal-header">
              <h2>تفاصيل المورد - {selectedSupplier.name}</h2>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="details-grid">
                <div className="details-section">
                  <h3>المعلومات الأساسية</h3>
                  <div className="detail-item">
                    <label>اسم المورد:</label>
                    <span>{selectedSupplier.name}</span>
                  </div>
                  <div className="detail-item">
                    <label>النوع:</label>
                    <span>{getTypeLabel(selectedSupplier.type)}</span>
                  </div>
                  <div className="detail-item">
                    <label>الحالة:</label>
                    {getStatusBadge(selectedSupplier.status)}
                  </div>
                  <div className="detail-item">
                    <label>الأولوية:</label>
                    {getPriorityBadge(selectedSupplier.priority)}
                  </div>
                  <div className="detail-item">
                    <label>تاريخ الإضافة:</label>
                    <span>{selectedSupplier.createdDate}</span>
                  </div>
                </div>

                <div className="details-section">
                  <h3>معلومات الاتصال</h3>
                  <div className="detail-item">
                    <label>جهة الاتصال:</label>
                    <span>{selectedSupplier.contact}</span>
                  </div>
                  <div className="detail-item">
                    <label>الهاتف:</label>
                    <span>{selectedSupplier.phone}</span>
                  </div>
                  <div className="detail-item">
                    <label>البريد الإلكتروني:</label>
                    <span>{selectedSupplier.email}</span>
                  </div>
                  <div className="detail-item">
                    <label>الموقع الإلكتروني:</label>
                    <span>{selectedSupplier.website || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>العنوان:</label>
                    <span>{selectedSupplier.address}</span>
                  </div>
                </div>

                <div className="details-section">
                  <h3>المعلومات المالية</h3>
                  <div className="detail-item">
                    <label>الرقم الضريبي:</label>
                    <span>{selectedSupplier.taxNumber || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>رقم الحساب البنكي:</label>
                    <span>{selectedSupplier.bankAccount || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>شروط الدفع:</label>
                    <span>{selectedSupplier.paymentTerms === '0' ? 'فوري' : `${selectedSupplier.paymentTerms} يوم`}</span>
                  </div>
                  <div className="detail-item">
                    <label>العمولة:</label>
                    <span>{selectedSupplier.commission}%</span>
                  </div>
                </div>

                <div className="details-section">
                  <h3>معلومات العقد</h3>
                  <div className="detail-item">
                    <label>تاريخ بداية العقد:</label>
                    <span>{selectedSupplier.contractStartDate || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>تاريخ انتهاء العقد:</label>
                    <span>
                      {selectedSupplier.contractEndDate || 'غير محدد'}
                      {isContractExpiring(selectedSupplier.contractEndDate) && (
                        <span className="contract-warning"> ⚠️ ينتهي قريباً</span>
                      )}
                      {isContractExpired(selectedSupplier.contractEndDate) && (
                        <span className="contract-expired"> ❌ منتهي</span>
                      )}
                    </span>
                  </div>
                </div>

                <div className="details-section">
                  <h3>الإحصائيات</h3>
                  <div className="detail-item">
                    <label>التقييم:</label>
                    <span>{renderStars(selectedSupplier.rating)} ({selectedSupplier.rating})</span>
                  </div>
                  <div className="detail-item">
                    <label>إجمالي الحجوزات:</label>
                    <span>{selectedSupplier.totalBookings}</span>
                  </div>
                  <div className="detail-item">
                    <label>آخر حجز:</label>
                    <span>{selectedSupplier.lastBooking || 'لا يوجد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>إجمالي الإيرادات:</label>
                    <span>{formatCurrency(selectedSupplier.totalRevenue)}</span>
                  </div>
                </div>
              </div>

              <div className="details-section full-width">
                <h3>الخدمات المقدمة</h3>
                <div className="services-tags">
                  {selectedSupplier.services.map((service, index) => (
                    <span key={index} className="service-tag">{service}</span>
                  ))}
                </div>
              </div>

              {selectedSupplier.notes && (
                <div className="details-section full-width">
                  <h3>ملاحظات</h3>
                  <p className="notes-text">{selectedSupplier.notes}</p>
                </div>
              )}
            </div>
            
            <div className="modal-footer">
              <button 
                className="edit-btn"
                onClick={() => {
                  setShowDetailsModal(false);
                  handleEditSupplier(selectedSupplier);
                }}
              >
                تعديل
              </button>
              <button 
                className="cancel-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة مورد جديد */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal large">
            <div className="modal-header">
              <h2>إضافة مورد جديد</h2>
              <button 
                className="close-btn"
                onClick={() => setShowAddModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-section">
                  <h3>المعلومات الأساسية</h3>
                  <div className="form-group">
                    <label>اسم المورد *</label>
                    <input
                      type="text"
                      value={newSupplier.name}
                      onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>نوع المورد *</label>
                    <select
                      value={newSupplier.type}
                      onChange={(e) => setNewSupplier({...newSupplier, type: e.target.value})}
                    >
                      <option value="airline">شركة طيران</option>
                      <option value="hotel">فندق</option>
                      <option value="transport">نقل</option>
                      <option value="tour">رحلات سياحية</option>
                      <option value="visa">خدمات تأشيرات</option>
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label>الحالة</label>
                    <select
                      value={newSupplier.status}
                      onChange={(e) => setNewSupplier({...newSupplier, status: e.target.value})}
                    >
                      <option value="active">نشط</option>
                      <option value="inactive">غير نشط</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>الأولوية</label>
                    <select
                      value={newSupplier.priority}
                      onChange={(e) => setNewSupplier({...newSupplier, priority: e.target.value})}
                    >
                      <option value="high">عالية</option>
                      <option value="medium">متوسطة</option>
                      <option value="low">منخفضة</option>
                    </select>
                  </div>
                </div>

                <div className="form-section">
                  <h3>معلومات الاتصال</h3>
                  <div className="form-group">
                    <label>جهة الاتصال *</label>
                    <input
                      type="text"
                      value={newSupplier.contact}
                      onChange={(e) => setNewSupplier({...newSupplier, contact: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>رقم الهاتف *</label>
                    <input
                      type="text"
                      value={newSupplier.phone}
                      onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>البريد الإلكتروني *</label>
                    <input
                      type="email"
                      value={newSupplier.email}
                      onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>الموقع الإلكتروني</label>
                    <input
                      type="url"
                      value={newSupplier.website}
                      onChange={(e) => setNewSupplier({...newSupplier, website: e.target.value})}
                      placeholder="https://example.com"
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>المعلومات المالية</h3>
                  <div className="form-group">
                    <label>الرقم الضريبي</label>
                    <input
                      type="text"
                      value={newSupplier.taxNumber}
                      onChange={(e) => setNewSupplier({...newSupplier, taxNumber: e.target.value})}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>رقم الحساب البنكي</label>
                    <input
                      type="text"
                      value={newSupplier.bankAccount}
                      onChange={(e) => setNewSupplier({...newSupplier, bankAccount: e.target.value})}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>شروط الدفع (بالأيام)</label>
                    <select
                      value={newSupplier.paymentTerms}
                      onChange={(e) => setNewSupplier({...newSupplier, paymentTerms: e.target.value})}
                    >
                      <option value="0">فوري</option>
                      <option value="7">7 أيام</option>
                      <option value="15">15 يوم</option>
                      <option value="30">30 يوم</option>
                      <option value="60">60 يوم</option>
                      <option value="90">90 يوم</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>العمولة (%)</label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={newSupplier.commission}
                      onChange={(e) => setNewSupplier({...newSupplier, commission: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>معلومات العقد</h3>
                  <div className="form-group">
                    <label>تاريخ بداية العقد</label>
                    <input
                      type="date"
                      value={newSupplier.contractStartDate}
                      onChange={(e) => setNewSupplier({...newSupplier, contractStartDate: e.target.value})}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>تاريخ انتهاء العقد</label>
                    <input
                      type="date"
                      value={newSupplier.contractEndDate}
                      onChange={(e) => setNewSupplier({...newSupplier, contractEndDate: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              <div className="form-section full-width">
                <div className="form-group">
                  <label>العنوان</label>
                  <textarea
                    value={newSupplier.address}
                    onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                    rows="2"
                  />
                </div>
                
                <div className="form-group">
                  <label>ملاحظات</label>
                  <textarea
                    value={newSupplier.notes}
                    onChange={(e) => setNewSupplier({...newSupplier, notes: e.target.value})}
                    rows="3"
                    placeholder="ملاحظات إضافية عن المورد..."
                  />
                </div>

                <ServiceManager
                  services={newSupplier.services}
                  onServicesChange={(services) => setNewSupplier({...newSupplier, services})}
                />
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="cancel-btn"
                onClick={() => setShowAddModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="save-btn"
                onClick={handleAddSupplier}
                disabled={!newSupplier.name || !newSupplier.contact || !newSupplier.phone || !newSupplier.email}
              >
                حفظ
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل المورد */}
      {showEditModal && (
        <div className="modal-overlay">
          <div className="modal large">
            <div className="modal-header">
              <h2>تعديل المورد</h2>
              <button 
                className="close-btn"
                onClick={() => setShowEditModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-section">
                  <h3>المعلومات الأساسية</h3>
                  <div className="form-group">
                    <label>اسم المورد *</label>
                    <input
                      type="text"
                      value={newSupplier.name}
                      onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>نوع المورد *</label>
                    <select
                      value={newSupplier.type}
                      onChange={(e) => setNewSupplier({...newSupplier, type: e.target.value})}
                    >
                      <option value="airline">شركة طيران</option>
                      <option value="hotel">فندق</option>
                      <option value="transport">نقل</option>
                      <option value="tour">رحلات سياحية</option>
                      <option value="visa">خدمات تأشيرات</option>
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label>الحالة</label>
                    <select
                      value={newSupplier.status}
                      onChange={(e) => setNewSupplier({...newSupplier, status: e.target.value})}
                    >
                      <option value="active">نشط</option>
                      <option value="inactive">غير نشط</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>الأولوية</label>
                    <select
                      value={newSupplier.priority}
                      onChange={(e) => setNewSupplier({...newSupplier, priority: e.target.value})}
                    >
                      <option value="high">عالية</option>
                      <option value="medium">متوسطة</option>
                      <option value="low">منخفضة</option>
                    </select>
                  </div>
                </div>

                <div className="form-section">
                  <h3>معلومات الاتصال</h3>
                  <div className="form-group">
                    <label>جهة الاتصال *</label>
                    <input
                      type="text"
                      value={newSupplier.contact}
                      onChange={(e) => setNewSupplier({...newSupplier, contact: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>رقم الهاتف *</label>
                    <input
                      type="text"
                      value={newSupplier.phone}
                      onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>البريد الإلكتروني *</label>
                    <input
                      type="email"
                      value={newSupplier.email}
                      onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>الموقع الإلكتروني</label>
                    <input
                      type="url"
                      value={newSupplier.website}
                      onChange={(e) => setNewSupplier({...newSupplier, website: e.target.value})}
                      placeholder="https://example.com"
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>المعلومات المالية</h3>
                  <div className="form-group">
                    <label>الرقم الضريبي</label>
                    <input
                      type="text"
                      value={newSupplier.taxNumber}
                      onChange={(e) => setNewSupplier({...newSupplier, taxNumber: e.target.value})}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>رقم الحساب البنكي</label>
                    <input
                      type="text"
                      value={newSupplier.bankAccount}
                      onChange={(e) => setNewSupplier({...newSupplier, bankAccount: e.target.value})}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>شروط الدفع (بالأيام)</label>
                    <select
                      value={newSupplier.paymentTerms}
                      onChange={(e) => setNewSupplier({...newSupplier, paymentTerms: e.target.value})}
                    >
                      <option value="0">فوري</option>
                      <option value="7">7 أيام</option>
                      <option value="15">15 يوم</option>
                      <option value="30">30 يوم</option>
                      <option value="60">60 يوم</option>
                      <option value="90">90 يوم</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>العمولة (%)</label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={newSupplier.commission}
                      onChange={(e) => setNewSupplier({...newSupplier, commission: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>معلومات العقد</h3>
                  <div className="form-group">
                    <label>تاريخ بداية العقد</label>
                    <input
                      type="date"
                      value={newSupplier.contractStartDate}
                      onChange={(e) => setNewSupplier({...newSupplier, contractStartDate: e.target.value})}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>تاريخ انتهاء العقد</label>
                    <input
                      type="date"
                      value={newSupplier.contractEndDate}
                      onChange={(e) => setNewSupplier({...newSupplier, contractEndDate: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              <div className="form-section full-width">
                <div className="form-group">
                  <label>العنوان</label>
                  <textarea
                    value={newSupplier.address}
                    onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                    rows="2"
                  />
                </div>
                
                <div className="form-group">
                  <label>ملاحظات</label>
                  <textarea
                    value={newSupplier.notes}
                    onChange={(e) => setNewSupplier({...newSupplier, notes: e.target.value})}
                    rows="3"
                    placeholder="ملاحظات إضافية عن المورد..."
                  />
                </div>

                <ServiceManager
                  services={newSupplier.services}
                  onServicesChange={(services) => setNewSupplier({...newSupplier, services})}
                />
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="cancel-btn"
                onClick={() => setShowEditModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="save-btn"
                onClick={handleUpdateSupplier}
                disabled={!newSupplier.name || !newSupplier.contact || !newSupplier.phone || !newSupplier.email}
              >
                تحديث
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة استيراد البيانات */}
      {showImportModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>استيراد بيانات الموردين</h2>
              <button 
                className="close-btn"
                onClick={() => setShowImportModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="import-section">
                <p>يمكنك استيراد بيانات الموردين من ملف CSV أو Excel</p>
                <div className="file-upload">
                  <input type="file" accept=".csv,.xlsx,.xls" />
                  <p>اسحب الملف هنا أو انقر للاختيار</p>
                </div>
                <div className="import-template">
                  <h4>تحميل نموذج الاستيراد:</h4>
                  <button className="template-btn">📄 تحميل نموذج CSV</button>
                  <button className="template-btn">📊 تحميل نموذج Excel</button>
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="cancel-btn"
                onClick={() => setShowImportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="save-btn"
                disabled
              >
                استيراد
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SuppliersPage;