import React, { useState } from 'react';
import { useTheme } from './ThemeProvider';
import './ThemeSettings.css';

// 🎛️ مكون إعدادات الثيم المتقدمة
const ThemeSettings = ({ isOpen, onClose }) => {
  const { current, customSettings, actions } = useTheme();
  const [activeTab, setActiveTab] = useState('appearance');

  if (!isOpen) return null;

  // 🎨 تحديث الإعدادات
  const handleSettingChange = (setting, value) => {
    actions.updateCustomSettings({ [setting]: value });
  };

  // 🔄 إعادة تعيين الإعدادات
  const handleReset = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      actions.resetToDefaults();
    }
  };

  return (
    <div className="theme-settings-overlay" onClick={onClose}>
      <div className="theme-settings-modal" onClick={(e) => e.stopPropagation()}>
        {/* 🎯 رأس المودال */}
        <div className="theme-settings-header">
          <h2>⚙️ إعدادات الثيم المتقدمة</h2>
          <button className="close-button" onClick={onClose}>
            ✕
          </button>
        </div>

        {/* 📑 التبويبات */}
        <div className="theme-settings-tabs">
          <button
            className={`tab ${activeTab === 'appearance' ? 'active' : ''}`}
            onClick={() => setActiveTab('appearance')}
          >
            🎨 المظهر
          </button>
          <button
            className={`tab ${activeTab === 'typography' ? 'active' : ''}`}
            onClick={() => setActiveTab('typography')}
          >
            📝 الخطوط
          </button>
          <button
            className={`tab ${activeTab === 'animations' ? 'active' : ''}`}
            onClick={() => setActiveTab('animations')}
          >
            🎬 الحركات
          </button>
          <button
            className={`tab ${activeTab === 'accessibility' ? 'active' : ''}`}
            onClick={() => setActiveTab('accessibility')}
          >
            ♿ إمكانية الوصول
          </button>
        </div>

        {/* 📄 محتوى التبويبات */}
        <div className="theme-settings-content">
          
          {/* 🎨 تبويب المظهر */}
          {activeTab === 'appearance' && (
            <div className="settings-section">
              <h3>🎨 إعدادات المظهر</h3>
              
              {/* نصف القطر */}
              <div className="setting-group">
                <label>📐 نصف قطر الحواف</label>
                <div className="radio-group">
                  {['none', 'small', 'medium', 'large', 'xlarge'].map((radius) => (
                    <label key={radius} className="radio-option">
                      <input
                        type="radio"
                        name="borderRadius"
                        value={radius}
                        checked={customSettings.borderRadius === radius}
                        onChange={(e) => handleSettingChange('borderRadius', e.target.value)}
                      />
                      <span className="radio-label">
                        {radius === 'none' && 'بدون'}
                        {radius === 'small' && 'صغير'}
                        {radius === 'medium' && 'متوسط'}
                        {radius === 'large' && 'كبير'}
                        {radius === 'xlarge' && 'كبير جداً'}
                      </span>
                      <div className={`radius-preview radius-${radius}`}></div>
                    </label>
                  ))}
                </div>
              </div>

              {/* معلومات الثيم الحالي */}
              <div className="current-theme-info">
                <h4>🎯 الثيم الحالي</h4>
                <div className="theme-card">
                  <div className="theme-icon-large">{current.icon}</div>
                  <div className="theme-details">
                    <h5>{current.name}</h5>
                    <p>{current.description}</p>
                  </div>
                  <div className="theme-colors-large">
                    {Object.entries(current.colors).map(([key, color]) => (
                      <div
                        key={key}
                        className="color-swatch"
                        style={{ backgroundColor: color }}
                        title={`${key}: ${color}`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 📝 تبويب الخطوط */}
          {activeTab === 'typography' && (
            <div className="settings-section">
              <h3>📝 إعدادات الخطوط</h3>
              
              {/* حجم الخط */}
              <div className="setting-group">
                <label>📏 حجم الخط</label>
                <div className="radio-group">
                  {['small', 'medium', 'large', 'xlarge'].map((size) => (
                    <label key={size} className="radio-option">
                      <input
                        type="radio"
                        name="fontSize"
                        value={size}
                        checked={customSettings.fontSize === size}
                        onChange={(e) => handleSettingChange('fontSize', e.target.value)}
                      />
                      <span className="radio-label">
                        {size === 'small' && 'صغير'}
                        {size === 'medium' && 'متوسط'}
                        {size === 'large' && 'كبير'}
                        {size === 'xlarge' && 'كبير جداً'}
                      </span>
                      <div className={`font-preview font-${size}`}>نص تجريبي</div>
                    </label>
                  ))}
                </div>
              </div>

              {/* معاينة الخط */}
              <div className="font-preview-section">
                <h4>👁️ معاينة الخط</h4>
                <div className="font-samples">
                  <h1>عنوان رئيسي</h1>
                  <h3>عنوان فرعي</h3>
                  <p>هذا نص عادي لمعاينة حجم الخط المختار. يمكنك رؤية كيف سيبدو النص في النظام.</p>
                  <small>نص صغير للتفاصيل</small>
                </div>
              </div>
            </div>
          )}

          {/* 🎬 تبويب الحركات */}
          {activeTab === 'animations' && (
            <div className="settings-section">
              <h3>🎬 إعدادات الحركات</h3>
              
              {/* تفعيل الحركات */}
              <div className="setting-group">
                <label className="toggle-setting">
                  <input
                    type="checkbox"
                    checked={customSettings.animations}
                    onChange={(e) => handleSettingChange('animations', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                  <span className="toggle-label">✨ تفعيل الحركات والتأثيرات</span>
                </label>
                <p className="setting-description">
                  تفعيل الحركات والانتقالات في واجهة المستخدم
                </p>
              </div>

              {/* تقليل الحركة */}
              <div className="setting-group">
                <label className="toggle-setting">
                  <input
                    type="checkbox"
                    checked={customSettings.reducedMotion}
                    onChange={(e) => handleSettingChange('reducedMotion', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                  <span className="toggle-label">🐌 تقليل الحركة</span>
                </label>
                <p className="setting-description">
                  تقليل الحركات للمستخدمين الذين يعانون من حساسية الحركة
                </p>
              </div>

              {/* معاينة الحركات */}
              <div className="animations-preview">
                <h4>👁️ معاينة الحركات</h4>
                <div className="animation-demos">
                  <div className="demo-card animate-pulse">نبضة</div>
                  <div className="demo-card animate-bounce">ارتداد</div>
                  <div className="demo-card animate-wobble">اهتزاز</div>
                </div>
              </div>
            </div>
          )}

          {/* ♿ تبويب إمكانية الوصول */}
          {activeTab === 'accessibility' && (
            <div className="settings-section">
              <h3>♿ إعدادات إمكانية الوصول</h3>
              
              {/* التباين العالي */}
              <div className="setting-group">
                <label className="toggle-setting">
                  <input
                    type="checkbox"
                    checked={customSettings.highContrast}
                    onChange={(e) => handleSettingChange('highContrast', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                  <span className="toggle-label">🔆 التباين العالي</span>
                </label>
                <p className="setting-description">
                  زيادة التباين لتحسين قابلية القراءة
                </p>
              </div>

              {/* التركيز المرئي */}
              <div className="setting-group">
                <label className="toggle-setting">
                  <input
                    type="checkbox"
                    checked={customSettings.focusVisible}
                    onChange={(e) => handleSettingChange('focusVisible', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                  <span className="toggle-label">🎯 التركيز المرئي</span>
                </label>
                <p className="setting-description">
                  إظهار حدود واضحة عند التنقل بلوحة المفاتيح
                </p>
              </div>

              {/* معلومات إمكانية الوصول */}
              <div className="accessibility-info">
                <h4>ℹ️ معلومات إمكانية الوصول</h4>
                <ul>
                  <li>✅ دعم كامل لقارئات الشاشة</li>
                  <li>✅ التنقل بلوحة المفاتيح</li>
                  <li>✅ ألوان متوافقة مع عمى الألوان</li>
                  <li>✅ نصوص بديلة للصور</li>
                  <li>✅ تباين ألوان محسن</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* 🔧 أزرار التحكم */}
        <div className="theme-settings-footer">
          <button className="btn btn-secondary" onClick={handleReset}>
            🔄 إعادة تعيين
          </button>
          <button className="btn btn-primary" onClick={onClose}>
            ✅ حفظ وإغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThemeSettings;
