import React, { useState, useEffect } from 'react';

const CustomersPage = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  // const [selectedCustomer, setSelectedCustomer] = useState(null); // غير مستخدم حالياً

  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    email: '',
    nationalId: '',
    address: '',
    city: '',
    country: 'السعودية',
    customerType: 'individual',
    companyName: '',
    taxNumber: '',
    creditLimit: '',
    notes: ''
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setCustomers([
        {
          id: 'CUST-001',
          name: 'أحم<PERSON> محمد العلي',
          phone: '+966501234567',
          email: '<EMAIL>',
          nationalId: '1234567890',
          address: 'الرياض، حي النخيل، شارع الملك فهد',
          city: 'الرياض',
          country: 'السعودية',
          customerType: 'individual',
          companyName: '',
          taxNumber: '',
          creditLimit: 10000,
          totalPurchases: 25000,
          totalInvoices: 8,
          lastPurchase: '2024-01-20',
          registrationDate: '2023-06-15',
          status: 'active',
          notes: 'عميل مميز - دفع سريع'
        },
        {
          id: 'CUST-002',
          name: 'شركة السفر الذهبي',
          phone: '+966112345678',
          email: '<EMAIL>',
          nationalId: '',
          address: 'جدة، حي الحمراء، برج التجارة',
          city: 'جدة',
          country: 'السعودية',
          customerType: 'company',
          companyName: 'شركة السفر الذهبي',
          taxNumber: '300123456789003',
          creditLimit: 50000,
          totalPurchases: 125000,
          totalInvoices: 25,
          lastPurchase: '2024-01-19',
          registrationDate: '2023-03-10',
          status: 'active',
          notes: 'شركة سياحة كبيرة'
        },
        {
          id: 'CUST-003',
          name: 'فاطمة سالم الأحمد',
          phone: '+966507654321',
          email: '<EMAIL>',
          nationalId: '0987654321',
          address: 'الدمام، حي الشاطئ، شارع الكورنيش',
          city: 'الدمام',
          country: 'السعودية',
          customerType: 'individual',
          companyName: '',
          taxNumber: '',
          creditLimit: 5000,
          totalPurchases: 8500,
          totalInvoices: 3,
          lastPurchase: '2024-01-18',
          registrationDate: '2023-11-20',
          status: 'active',
          notes: 'عميلة جديدة'
        },
        {
          id: 'CUST-004',
          name: 'خالد أحمد الزهراني',
          phone: '+966551234567',
          email: '<EMAIL>',
          nationalId: '1122334455',
          address: 'مكة المكرمة، حي العزيزية',
          city: 'مكة المكرمة',
          country: 'السعودية',
          customerType: 'individual',
          companyName: '',
          taxNumber: '',
          creditLimit: 3000,
          totalPurchases: 1200,
          totalInvoices: 2,
          lastPurchase: '2024-01-10',
          registrationDate: '2024-01-05',
          status: 'inactive',
          notes: 'عميل جديد'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount, currency = 'SAR') => {
    return `${amount.toLocaleString('ar-SA')} ر.س`;
  };

  const getCustomerTypeText = (type) => {
    switch (type) {
      case 'individual': return 'فرد';
      case 'company': return 'شركة';
      default: return type;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#27ae60';
      case 'inactive': return '#e74c3c';
      case 'suspended': return '#f39c12';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'suspended': return 'معلق';
      default: return status;
    }
  };

  const handleAddCustomer = (e) => {
    e.preventDefault();
    const customer = {
      id: `CUST-${String(customers.length + 1).padStart(3, '0')}`,
      ...newCustomer,
      creditLimit: parseFloat(newCustomer.creditLimit || 0),
      totalPurchases: 0,
      totalInvoices: 0,
      lastPurchase: null,
      registrationDate: new Date().toISOString().split('T')[0],
      status: 'active'
    };
    setCustomers([customer, ...customers]);
    setNewCustomer({
      name: '',
      phone: '',
      email: '',
      nationalId: '',
      address: '',
      city: '',
      country: 'السعودية',
      customerType: 'individual',
      companyName: '',
      taxNumber: '',
      creditLimit: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || customer.customerType === filterType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#666' }}>جاري تحميل بيانات العملاء...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          👥 إدارة العملاء
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          إدارة شاملة لقاعدة بيانات العملاء والشركات
        </p>
      </div>

      {/* الإحصائيات السريعة */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        {[
          { title: 'إجمالي العملاء', value: customers.length, color: '#3498db', icon: '👥' },
          { title: 'عملاء أفراد', value: customers.filter(c => c.customerType === 'individual').length, color: '#27ae60', icon: '👤' },
          { title: 'عملاء شركات', value: customers.filter(c => c.customerType === 'company').length, color: '#8e44ad', icon: '🏢' },
          { title: 'عملاء نشطون', value: customers.filter(c => c.status === 'active').length, color: '#e67e22', icon: '✅' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '12px',
            boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '10px' }}>{stat.icon}</div>
            <h3 style={{ color: stat.color, margin: '0 0 10px 0', fontSize: '14px' }}>{stat.title}</h3>
            <p style={{ fontSize: '20px', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>{stat.value}</p>
          </div>
        ))}
      </div>

      {/* أدوات التحكم */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '20px',
          flexWrap: 'wrap',
          gap: '10px'
        }}>
          <button
            onClick={() => setShowAddForm(true)}
            style={{
              background: '#27ae60',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'background 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.background = '#219a52'}
            onMouseLeave={(e) => e.target.style.background = '#27ae60'}
          >
            ➕ إضافة عميل جديد
          </button>

          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <button style={{
              background: '#3498db',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              📊 تقرير العملاء
            </button>
            <button style={{
              background: '#e67e22',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              📤 تصدير Excel
            </button>
          </div>
        </div>

        {/* البحث والفلترة */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '15px' 
        }}>
          <input
            type="text"
            placeholder="البحث في العملاء (الاسم، الهاتف، البريد الإلكتروني)..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          />
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          >
            <option value="all">جميع الأنواع</option>
            <option value="individual">أفراد</option>
            <option value="company">شركات</option>
          </select>
        </div>
      </div>

      {/* جدول العملاء */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ background: '#f8f9fa' }}>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>رقم العميل</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>الاسم</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>معلومات الاتصال</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>النوع</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>إجمالي المشتريات</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>عدد الفواتير</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>آخر شراء</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الحالة</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredCustomers.map((customer, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '15px', fontWeight: 'bold', color: '#3498db' }}>
                  {customer.id}
                </td>
                <td style={{ padding: '15px' }}>
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{customer.name}</div>
                    {customer.customerType === 'company' && customer.companyName && (
                      <div style={{ fontSize: '12px', color: '#666' }}>{customer.companyName}</div>
                    )}
                  </div>
                </td>
                <td style={{ padding: '15px' }}>
                  <div>
                    <div style={{ fontSize: '14px' }}>📞 {customer.phone}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>📧 {customer.email}</div>
                  </div>
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <span style={{
                    background: customer.customerType === 'individual' ? '#e8f5e8' : '#e8e8f5',
                    color: customer.customerType === 'individual' ? '#27ae60' : '#8e44ad',
                    padding: '5px 12px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    {getCustomerTypeText(customer.customerType)}
                  </span>
                </td>
                <td style={{ padding: '15px', fontWeight: 'bold', color: '#27ae60' }}>
                  {formatCurrency(customer.totalPurchases)}
                </td>
                <td style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>
                  {customer.totalInvoices}
                </td>
                <td style={{ padding: '15px' }}>
                  {customer.lastPurchase || 'لا يوجد'}
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <span style={{
                    background: getStatusColor(customer.status),
                    color: 'white',
                    padding: '5px 12px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    {getStatusText(customer.status)}
                  </span>
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                    <button style={{
                      background: '#3498db',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}>
                      عرض
                    </button>
                    <button style={{
                      background: '#27ae60',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}>
                      تعديل
                    </button>
                    <button style={{
                      background: '#e67e22',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}>
                      فواتير
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredCustomers.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '50px', 
            color: '#666',
            fontSize: '16px'
          }}>
            لا توجد عملاء تطابق معايير البحث
          </div>
        )}
      </div>

      {/* نموذج إضافة عميل جديد */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>👥 إضافة عميل جديد</h2>
            
            <form onSubmit={handleAddCustomer}>
              {/* نوع العميل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#3498db', marginBottom: '15px' }}>📋 نوع العميل</h3>
                <div style={{ display: 'flex', gap: '20px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="radio"
                      name="customerType"
                      value="individual"
                      checked={newCustomer.customerType === 'individual'}
                      onChange={(e) => setNewCustomer({...newCustomer, customerType: e.target.value})}
                    />
                    👤 فرد
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <input
                      type="radio"
                      name="customerType"
                      value="company"
                      checked={newCustomer.customerType === 'company'}
                      onChange={(e) => setNewCustomer({...newCustomer, customerType: e.target.value})}
                    />
                    🏢 شركة
                  </label>
                </div>
              </div>

              {/* المعلومات الأساسية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>📝 المعلومات الأساسية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                      {newCustomer.customerType === 'company' ? 'اسم الشركة' : 'الاسم الكامل'}
                    </label>
                    <input
                      type="text"
                      value={newCustomer.name}
                      onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  
                  {newCustomer.customerType === 'company' && (
                    <div>
                      <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الرقم الضريبي</label>
                      <input
                        type="text"
                        value={newCustomer.taxNumber}
                        onChange={(e) => setNewCustomer({...newCustomer, taxNumber: e.target.value})}
                        placeholder="300123456789003"
                        style={{
                          width: '100%',
                          padding: '12px',
                          border: '2px solid #e0e0e0',
                          borderRadius: '8px',
                          fontSize: '14px',
                          boxSizing: 'border-box'
                        }}
                      />
                    </div>
                  )}

                  {newCustomer.customerType === 'individual' && (
                    <div>
                      <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهوية</label>
                      <input
                        type="text"
                        value={newCustomer.nationalId}
                        onChange={(e) => setNewCustomer({...newCustomer, nationalId: e.target.value})}
                        placeholder="1234567890"
                        style={{
                          width: '100%',
                          padding: '12px',
                          border: '2px solid #e0e0e0',
                          borderRadius: '8px',
                          fontSize: '14px',
                          boxSizing: 'border-box'
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* معلومات الاتصال */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e67e22', marginBottom: '15px' }}>📞 معلومات الاتصال</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newCustomer.phone}
                      onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                      required
                      placeholder="+966501234567"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={newCustomer.email}
                      onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                      placeholder="<EMAIL>"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* العنوان */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>📍 العنوان</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div style={{ gridColumn: '1 / -1' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العنوان التفصيلي</label>
                    <input
                      type="text"
                      value={newCustomer.address}
                      onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                      placeholder="الحي، الشارع، رقم المبنى"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المدينة</label>
                    <input
                      type="text"
                      value={newCustomer.city}
                      onChange={(e) => setNewCustomer({...newCustomer, city: e.target.value})}
                      placeholder="الرياض"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البلد</label>
                    <select
                      value={newCustomer.country}
                      onChange={(e) => setNewCustomer({...newCustomer, country: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="السعودية">🇸🇦 السعودية</option>
                      <option value="اليمن">🇾🇪 اليمن</option>
                      <option value="الإمارات">🇦🇪 الإمارات</option>
                      <option value="الكويت">🇰🇼 الكويت</option>
                      <option value="قطر">🇶🇦 قطر</option>
                      <option value="البحرين">🇧🇭 البحرين</option>
                      <option value="عمان">🇴🇲 عمان</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* معلومات إضافية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#f39c12', marginBottom: '15px' }}>💰 معلومات إضافية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الحد الائتماني (ر.س)</label>
                    <input
                      type="number"
                      min="0"
                      value={newCustomer.creditLimit}
                      onChange={(e) => setNewCustomer({...newCustomer, creditLimit: e.target.value})}
                      placeholder="10000"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div style={{ gridColumn: '1 / -1' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                    <textarea
                      value={newCustomer.notes}
                      onChange={(e) => setNewCustomer({...newCustomer, notes: e.target.value})}
                      rows="3"
                      placeholder="ملاحظات إضافية عن العميل..."
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* أزرار التحكم */}
              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    background: '#95a5a6',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    background: '#27ae60',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة العميل
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* CSS للأنيميشن */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default CustomersPage;