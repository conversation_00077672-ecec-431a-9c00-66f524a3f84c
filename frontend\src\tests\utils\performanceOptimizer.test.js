import {
  PerformanceMonitor,
  CacheManager,
  RequestBatcher,
  optimizeImage,
  getDeviceInfo,
  useOptimizedRender
} from '../../utils/performanceOptimizer';

// 🧪 اختبارات نظام تحسين الأداء

// محاكاة performance API
const mockPerformance = {
  memory: {
    usedJSHeapSize: 10000000,
    totalJSHeapSize: 20000000,
    jsHeapSizeLimit: 100000000
  },
  getEntriesByType: jest.fn(() => [
    {
      loadEventEnd: 2000,
      loadEventStart: 1000
    }
  ]),
  now: jest.fn(() => Date.now())
};

// محاكاة navigator
const mockNavigator = {
  userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
  onLine: true,
  deviceMemory: 4,
  hardwareConcurrency: 4,
  connection: {
    effectiveType: '4g',
    downlink: 10
  }
};

describe('PerformanceOptimizer', () => {
  beforeEach(() => {
    // إعداد المحاكيات
    global.performance = mockPerformance;
    global.navigator = mockNavigator;
    jest.clearAllMocks();
  });

  describe('PerformanceMonitor', () => {
    test('يجب إنشاء مراقب الأداء بنجاح', () => {
      const monitor = new PerformanceMonitor();
      expect(monitor).toBeInstanceOf(PerformanceMonitor);
      expect(monitor.metrics).toBeDefined();
    });

    test('يجب تسجيل مراقب للأحداث', () => {
      const monitor = new PerformanceMonitor();
      const callback = jest.fn();
      
      monitor.subscribe(callback);
      expect(monitor.observers).toContain(callback);
    });

    test('يجب إلغاء تسجيل مراقب للأحداث', () => {
      const monitor = new PerformanceMonitor();
      const callback = jest.fn();
      
      monitor.subscribe(callback);
      monitor.unsubscribe(callback);
      expect(monitor.observers).not.toContain(callback);
    });

    test('يجب إشعار المراقبين عند تحديث المقاييس', () => {
      const monitor = new PerformanceMonitor();
      const callback = jest.fn();
      
      monitor.subscribe(callback);
      monitor.notifyObservers('loadTime', 1000);
      
      expect(callback).toHaveBeenCalledWith('loadTime', 1000);
    });

    test('يجب إرجاع المقاييس الحالية', () => {
      const monitor = new PerformanceMonitor();
      const metrics = monitor.getMetrics();
      
      expect(metrics).toHaveProperty('loadTime');
      expect(metrics).toHaveProperty('renderTime');
      expect(metrics).toHaveProperty('memoryUsage');
    });

    test('يجب إنشاء تقرير أداء', () => {
      const monitor = new PerformanceMonitor();
      const consoleSpy = jest.spyOn(console, 'group').mockImplementation();
      const consoleTableSpy = jest.spyOn(console, 'table').mockImplementation();
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleGroupEndSpy = jest.spyOn(console, 'groupEnd').mockImplementation();
      
      const report = monitor.generateReport();
      
      expect(report).toHaveProperty('timestamp');
      expect(report).toHaveProperty('metrics');
      expect(report).toHaveProperty('recommendations');
      
      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleTableSpy).toHaveBeenCalled();
      expect(consoleLogSpy).toHaveBeenCalled();
      expect(consoleGroupEndSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
      consoleTableSpy.mockRestore();
      consoleLogSpy.mockRestore();
      consoleGroupEndSpy.mockRestore();
    });
  });

  describe('CacheManager', () => {
    test('يجب إنشاء مدير التخزين المؤقت بنجاح', () => {
      const cache = new CacheManager();
      expect(cache).toBeInstanceOf(CacheManager);
    });

    test('يجب حفظ واسترجاع البيانات', () => {
      const cache = new CacheManager();
      const testData = { test: 'data' };
      
      cache.set('testKey', testData);
      const retrieved = cache.get('testKey');
      
      expect(retrieved).toEqual(testData);
    });

    test('يجب إرجاع null للمفاتيح غير الموجودة', () => {
      const cache = new CacheManager();
      const result = cache.get('nonExistentKey');
      
      expect(result).toBeNull();
    });

    test('يجب حذف البيانات المنتهية الصلاحية', () => {
      const cache = new CacheManager(100, 100); // TTL قصير
      
      cache.set('testKey', 'testData');
      
      // انتظار انتهاء الصلاحية
      setTimeout(() => {
        const result = cache.get('testKey');
        expect(result).toBeNull();
      }, 150);
    });

    test('يجب حذف أقدم عنصر عند الوصول للحد الأقصى', () => {
      const cache = new CacheManager(2); // حد أقصى 2 عناصر
      
      cache.set('key1', 'data1');
      cache.set('key2', 'data2');
      cache.set('key3', 'data3'); // يجب حذف key1
      
      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBe('data2');
      expect(cache.get('key3')).toBe('data3');
    });

    test('يجب مسح جميع البيانات', () => {
      const cache = new CacheManager();
      
      cache.set('key1', 'data1');
      cache.set('key2', 'data2');
      cache.clear();
      
      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBeNull();
    });

    test('يجب إرجاع إحصائيات صحيحة', () => {
      const cache = new CacheManager(10);
      
      cache.set('key1', 'data1');
      cache.set('key2', 'data2');
      
      const stats = cache.getStats();
      
      expect(stats.size).toBe(2);
      expect(stats.maxSize).toBe(10);
    });
  });

  describe('RequestBatcher', () => {
    test('يجب إنشاء مجمع الطلبات بنجاح', () => {
      const batcher = new RequestBatcher();
      expect(batcher).toBeInstanceOf(RequestBatcher);
    });

    test('يجب تجميع الطلبات وتنفيذها', async () => {
      const batcher = new RequestBatcher(2, 50);
      const mockRequest1 = jest.fn(() => Promise.resolve('result1'));
      const mockRequest2 = jest.fn(() => Promise.resolve('result2'));
      
      const promise1 = batcher.add(mockRequest1);
      const promise2 = batcher.add(mockRequest2);
      
      const results = await Promise.all([promise1, promise2]);
      
      expect(results).toEqual(['result1', 'result2']);
      expect(mockRequest1).toHaveBeenCalled();
      expect(mockRequest2).toHaveBeenCalled();
    });

    test('يجب تنفيذ الدفعة عند الوصول للحد الأقصى', async () => {
      const batcher = new RequestBatcher(2, 1000); // تأخير طويل
      const mockRequest1 = jest.fn(() => Promise.resolve('result1'));
      const mockRequest2 = jest.fn(() => Promise.resolve('result2'));
      
      const promise1 = batcher.add(mockRequest1);
      const promise2 = batcher.add(mockRequest2);
      
      // يجب تنفيذ الطلبات فوراً لأننا وصلنا للحد الأقصى
      const results = await Promise.all([promise1, promise2]);
      
      expect(results).toEqual(['result1', 'result2']);
    });

    test('يجب معالجة أخطاء الطلبات', async () => {
      const batcher = new RequestBatcher(1, 50);
      const mockRequest = jest.fn(() => Promise.reject(new Error('Test error')));
      
      await expect(batcher.add(mockRequest)).rejects.toThrow('Test error');
    });
  });

  describe('optimizeImage', () => {
    test('يجب إرجاع URL محسن للصورة', () => {
      const result = optimizeImage('test.jpg', {
        width: 300,
        height: 200,
        quality: 80,
        format: 'webp'
      });
      
      expect(result.src).toContain('w=300');
      expect(result.src).toContain('h=200');
      expect(result.src).toContain('q=80');
      expect(result.src).toContain('f=webp');
    });

    test('يجب استخدام القيم الافتراضية', () => {
      const result = optimizeImage('test.jpg');
      
      expect(result.src).toContain('q=80');
      expect(result.src).toContain('f=webp');
      expect(result.loading).toBe('lazy');
      expect(result.decoding).toBe('async');
    });

    test('يجب تعطيل التحميل البطيء عند الطلب', () => {
      const result = optimizeImage('test.jpg', { lazy: false });
      
      expect(result.loading).toBe('eager');
    });
  });

  describe('getDeviceInfo', () => {
    test('يجب إرجاع معلومات الجهاز', () => {
      const deviceInfo = getDeviceInfo();
      
      expect(deviceInfo).toHaveProperty('isMobile');
      expect(deviceInfo).toHaveProperty('isOnline');
      expect(deviceInfo).toHaveProperty('connection');
      expect(deviceInfo).toHaveProperty('memory');
      expect(deviceInfo).toHaveProperty('cores');
    });

    test('يجب كشف الأجهزة المحمولة', () => {
      const deviceInfo = getDeviceInfo();
      
      // بناءً على userAgent المحاكي (iPhone)
      expect(deviceInfo.isMobile).toBe(true);
    });

    test('يجب إرجاع معلومات الاتصال', () => {
      const deviceInfo = getDeviceInfo();
      
      expect(deviceInfo.isOnline).toBe(true);
      expect(deviceInfo.connection).toBeDefined();
    });
  });

  describe('معالجة الأخطاء', () => {
    test('يجب معالجة عدم توفر performance API', () => {
      const originalPerformance = global.performance;
      delete global.performance;
      
      expect(() => {
        new PerformanceMonitor();
      }).not.toThrow();
      
      global.performance = originalPerformance;
    });

    test('يجب معالجة عدم توفر memory API', () => {
      const originalMemory = global.performance.memory;
      delete global.performance.memory;
      
      expect(() => {
        new PerformanceMonitor();
      }).not.toThrow();
      
      global.performance.memory = originalMemory;
    });

    test('يجب معالجة أخطاء التخزين المؤقت', () => {
      const cache = new CacheManager();
      
      // محاولة حفظ قيمة غير صالحة
      expect(() => {
        cache.set(null, 'data');
      }).not.toThrow();
    });
  });

  describe('الأداء', () => {
    test('يجب أن يكون CacheManager سريعاً', () => {
      const cache = new CacheManager();
      const startTime = performance.now();
      
      // إجراء عدة عمليات
      for (let i = 0; i < 1000; i++) {
        cache.set(`key${i}`, `data${i}`);
      }
      
      for (let i = 0; i < 1000; i++) {
        cache.get(`key${i}`);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // يجب أن تكتمل العمليات في أقل من 100ms
      expect(duration).toBeLessThan(100);
    });

    test('يجب أن يكون RequestBatcher فعالاً', async () => {
      const batcher = new RequestBatcher(10, 10);
      const requests = [];
      
      // إنشاء عدة طلبات
      for (let i = 0; i < 50; i++) {
        requests.push(
          batcher.add(() => Promise.resolve(`result${i}`))
        );
      }
      
      const startTime = performance.now();
      await Promise.all(requests);
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      
      // يجب أن تكتمل جميع الطلبات في وقت معقول
      expect(duration).toBeLessThan(1000);
    });
  });
});
