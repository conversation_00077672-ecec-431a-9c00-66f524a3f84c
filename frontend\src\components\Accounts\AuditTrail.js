import React, { useState, useMemo } from 'react';
import './AuditTrail.css';

const AuditTrail = ({ transactions, accounts, currentUser }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterUser, setFilterUser] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // الحصول على قائمة المستخدمين الفريدة
  const uniqueUsers = useMemo(() => {
    const users = [...new Set(transactions.map(trans => trans.user).filter(Boolean))];
    return users.sort();
  }, [transactions]);

  // تصفية المعاملات
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const matchesSearch = 
        transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.entries?.some(entry => 
          entry.accountName?.toLowerCase().includes(searchTerm.toLowerCase())
        );

      const matchesUser = filterUser === 'all' || transaction.user === filterUser;
      const matchesType = filterType === 'all' || transaction.type === filterType;
      
      const transactionDate = new Date(transaction.date);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      const matchesDate = transactionDate >= startDate && transactionDate <= endDate;

      return matchesSearch && matchesUser && matchesType && matchesDate;
    }).sort((a, b) => new Date(b.createdAt || b.date) - new Date(a.createdAt || a.date));
  }, [transactions, searchTerm, filterUser, filterType, dateRange]);

  // حساب الإحصائيات
  const auditStats = useMemo(() => {
    const stats = {
      totalTransactions: filteredTransactions.length,
      totalAmount: 0,
      byType: {
        revenue: 0,
        expense: 0,
        transfer: 0,
        manual: 0
      },
      byUser: {},
      recentActivity: filteredTransactions.slice(0, 5)
    };

    filteredTransactions.forEach(transaction => {
      // حساب المبلغ الإجمالي
      const transactionAmount = transaction.entries?.reduce((sum, entry) => 
        sum + (entry.debit || 0), 0) || 0;
      stats.totalAmount += transactionAmount;

      // حساب حسب النوع
      if (stats.byType[transaction.type] !== undefined) {
        stats.byType[transaction.type]++;
      }

      // حساب حسب المستخدم
      if (transaction.user) {
        stats.byUser[transaction.user] = (stats.byUser[transaction.user] || 0) + 1;
      }
    });

    return stats;
  }, [filteredTransactions]);

  // عرض تفاصيل المعاملة
  const showTransactionDetails = (transaction) => {
    setSelectedTransaction(transaction);
    setShowDetails(true);
  };

  // تصدير التقرير
  const exportAuditReport = () => {
    const csvContent = [
      ['رقم المعاملة', 'التاريخ', 'الوصف', 'المرجع', 'النوع', 'المستخدم', 'المبلغ', 'تاريخ الإنشاء'],
      ...filteredTransactions.map(transaction => [
        transaction.id,
        new Date(transaction.date).toLocaleDateString('ar-SA'),
        transaction.description,
        transaction.reference,
        transaction.type === 'revenue' ? 'إيراد' : 
        transaction.type === 'expense' ? 'مصروف' : 
        transaction.type === 'transfer' ? 'تحويل' : 'يدوي',
        transaction.user,
        transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0,
        new Date(transaction.createdAt || transaction.date).toLocaleString('ar-SA')
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `audit-trail-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  return (
    <div className="audit-trail">
      <div className="audit-header">
        <div className="header-content">
          <h2>🔍 مسار التدقيق المحاسبي</h2>
          <p>مراجعة وتتبع جميع العمليات المحاسبية والمستخدمين</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-success"
            onClick={exportAuditReport}
          >
            📊 تصدير التقرير
          </button>
        </div>
      </div>

      <div className="audit-content">
        <div className="audit-sidebar">
          <div className="filters-section">
            <h3>المرشحات</h3>
            
            <div className="filter-group">
              <label>البحث:</label>
              <input
                type="text"
                placeholder="🔍 البحث في المعاملات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>المستخدم:</label>
              <select
                value={filterUser}
                onChange={(e) => setFilterUser(e.target.value)}
                className="filter-select"
              >
                <option value="all">جميع المستخدمين</option>
                {uniqueUsers.map(user => (
                  <option key={user} value={user}>{user}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>نوع المعاملة:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="all">جميع الأنواع</option>
                <option value="revenue">إيرادات</option>
                <option value="expense">مصروفات</option>
                <option value="transfer">تحويلات</option>
                <option value="manual">يدوي</option>
              </select>
            </div>

            <div className="filter-group">
              <label>الفترة الزمنية:</label>
              <div className="date-inputs">
                <input
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                  className="date-input"
                />
                <input
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                  className="date-input"
                />
              </div>
            </div>
          </div>

          <div className="stats-section">
            <h3>إحصائيات التدقيق</h3>
            
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-info">
                <div className="stat-value">{auditStats.totalTransactions}</div>
                <div className="stat-label">إجمالي المعاملات</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">💰</div>
              <div className="stat-info">
                <div className="stat-value">{auditStats.totalAmount.toLocaleString()}</div>
                <div className="stat-label">إجمالي المبالغ</div>
              </div>
            </div>

            <div className="type-stats">
              <h4>حسب النوع</h4>
              <div className="type-list">
                <div className="type-item revenue">
                  <span className="type-icon">📈</span>
                  <span className="type-name">إيرادات</span>
                  <span className="type-count">{auditStats.byType.revenue}</span>
                </div>
                <div className="type-item expense">
                  <span className="type-icon">📉</span>
                  <span className="type-name">مصروفات</span>
                  <span className="type-count">{auditStats.byType.expense}</span>
                </div>
                <div className="type-item transfer">
                  <span className="type-icon">🔄</span>
                  <span className="type-name">تحويلات</span>
                  <span className="type-count">{auditStats.byType.transfer}</span>
                </div>
                <div className="type-item manual">
                  <span className="type-icon">✏️</span>
                  <span className="type-name">يدوي</span>
                  <span className="type-count">{auditStats.byType.manual}</span>
                </div>
              </div>
            </div>

            <div className="user-stats">
              <h4>نشاط المستخدمين</h4>
              <div className="user-list">
                {Object.entries(auditStats.byUser)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([user, count]) => (
                    <div key={user} className="user-item">
                      <span className="user-name">{user}</span>
                      <span className="user-count">{count}</span>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>

        <div className="audit-main">
          <div className="transactions-header">
            <h3>سجل المعاملات ({filteredTransactions.length})</h3>
            <div className="view-options">
              <button className="view-btn active">📋 جدول</button>
              <button className="view-btn">📊 رسم بياني</button>
            </div>
          </div>

          <div className="transactions-table-container">
            {filteredTransactions.length === 0 ? (
              <div className="no-transactions">
                <span className="icon">🔍</span>
                <h4>لا توجد معاملات</h4>
                <p>لا توجد معاملات تطابق المرشحات المحددة</p>
              </div>
            ) : (
              <table className="transactions-table">
                <thead>
                  <tr>
                    <th>رقم المعاملة</th>
                    <th>التاريخ</th>
                    <th>الوصف</th>
                    <th>النوع</th>
                    <th>المستخدم</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTransactions.map(transaction => {
                    const totalAmount = transaction.entries?.reduce((sum, entry) => 
                      sum + (entry.debit || 0), 0) || 0;
                    
                    return (
                      <tr key={transaction.id} className={`transaction-row ${transaction.type}`}>
                        <td className="transaction-id">
                          <span className="id-text">{transaction.id}</span>
                          {transaction.isAutomated && (
                            <span className="auto-badge" title="معاملة تلقائية">🤖</span>
                          )}
                        </td>
                        <td>{new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                        <td className="description">
                          <div className="desc-main">{transaction.description}</div>
                          {transaction.reference && (
                            <div className="desc-ref">المرجع: {transaction.reference}</div>
                          )}
                        </td>
                        <td>
                          <span className={`type-badge ${transaction.type}`}>
                            {transaction.type === 'revenue' ? '📈 إيراد' : 
                             transaction.type === 'expense' ? '📉 مصروف' : 
                             transaction.type === 'transfer' ? '🔄 تحويل' : '✏️ يدوي'}
                          </span>
                        </td>
                        <td className="user-cell">
                          <div className="user-info">
                            <span className="user-name">{transaction.user}</span>
                            {transaction.user === currentUser?.name && (
                              <span className="current-user-badge">أنت</span>
                            )}
                          </div>
                        </td>
                        <td className="amount">{totalAmount.toLocaleString()} ر.س</td>
                        <td>
                          <span className="status-badge success">مكتمل</span>
                        </td>
                        <td className="created-date">
                          {new Date(transaction.createdAt || transaction.date).toLocaleString('ar-SA')}
                        </td>
                        <td className="actions">
                          <button
                            className="action-btn view-btn"
                            onClick={() => showTransactionDetails(transaction)}
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          <button
                            className="action-btn print-btn"
                            title="طباعة"
                          >
                            🖨️
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>

      {/* نموذج تفاصيل المعاملة */}
      {showDetails && selectedTransaction && (
        <div className="modal-overlay">
          <div className="transaction-details-modal">
            <div className="modal-header">
              <h3>🔍 تفاصيل المعاملة {selectedTransaction.id}</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDetails(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="transaction-info">
                <div className="info-grid">
                  <div className="info-section">
                    <h4>معلومات أساسية</h4>
                    <div className="info-items">
                      <div className="info-item">
                        <label>رقم المعاملة:</label>
                        <span>{selectedTransaction.id}</span>
                      </div>
                      <div className="info-item">
                        <label>التاريخ:</label>
                        <span>{new Date(selectedTransaction.date).toLocaleDateString('ar-SA')}</span>
                      </div>
                      <div className="info-item">
                        <label>النوع:</label>
                        <span className={`type-badge ${selectedTransaction.type}`}>
                          {selectedTransaction.type === 'revenue' ? 'إيراد' : 
                           selectedTransaction.type === 'expense' ? 'مصروف' : 
                           selectedTransaction.type === 'transfer' ? 'تحويل' : 'يدوي'}
                        </span>
                      </div>
                      <div className="info-item">
                        <label>المرجع:</label>
                        <span>{selectedTransaction.reference || 'غير محدد'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="info-section">
                    <h4>معلومات المستخدم</h4>
                    <div className="info-items">
                      <div className="info-item">
                        <label>المستخدم:</label>
                        <span>{selectedTransaction.user}</span>
                      </div>
                      <div className="info-item">
                        <label>تاريخ الإنشاء:</label>
                        <span>{new Date(selectedTransaction.createdAt || selectedTransaction.date).toLocaleString('ar-SA')}</span>
                      </div>
                      <div className="info-item">
                        <label>آخر تحديث:</label>
                        <span>{new Date(selectedTransaction.updatedAt || selectedTransaction.date).toLocaleString('ar-SA')}</span>
                      </div>
                      <div className="info-item">
                        <label>نوع الإدخال:</label>
                        <span>{selectedTransaction.isAutomated ? '🤖 تلقائي' : '👤 يدوي'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="description-section">
                  <h4>الوصف</h4>
                  <p>{selectedTransaction.description}</p>
                </div>

                <div className="entries-section">
                  <h4>تفاصيل القيود</h4>
                  <table className="entries-table">
                    <thead>
                      <tr>
                        <th>الحساب</th>
                        <th>البيان</th>
                        <th>مدين</th>
                        <th>دائن</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedTransaction.entries?.map((entry, index) => (
                        <tr key={index}>
                          <td>
                            <div className="account-info">
                              <div className="account-name">{entry.accountName}</div>
                              <div className="account-code">({entry.accountId})</div>
                            </div>
                          </td>
                          <td>{entry.description}</td>
                          <td className="debit">{entry.debit ? entry.debit.toLocaleString() : '-'}</td>
                          <td className="credit">{entry.credit ? entry.credit.toLocaleString() : '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colSpan="2"><strong>الإجمالي</strong></td>
                        <td className="total-debit">
                          <strong>
                            {selectedTransaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0).toLocaleString()}
                          </strong>
                        </td>
                        <td className="total-credit">
                          <strong>
                            {selectedTransaction.entries?.reduce((sum, entry) => sum + (entry.credit || 0), 0).toLocaleString()}
                          </strong>
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                {selectedTransaction.notes && (
                  <div className="notes-section">
                    <h4>ملاحظات</h4>
                    <p>{selectedTransaction.notes}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDetails(false)}
              >
                إغلاق
              </button>
              <button className="btn btn-primary">
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuditTrail;