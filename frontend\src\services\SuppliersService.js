// خدمة إدارة الموردين المشتركة
class SuppliersService {
  constructor() {
    this.suppliers = [];
    this.listeners = [];
    this.loadSuppliers();
  }

  // تحميل الموردين من localStorage أو البيانات التجريبية
  loadSuppliers() {
    const savedSuppliers = localStorage.getItem('suppliers');
    if (savedSuppliers) {
      this.suppliers = JSON.parse(savedSuppliers);
    } else {
      // البيانات التجريبية الافتراضية
      this.suppliers = [
        {
          id: 1,
          name: 'شركة الطيران العربية',
          type: 'airline',
          contact: 'أحمد محمد الطيار',
          phone: '+************',
          email: '<EMAIL>',
          address: 'الرياض، المملكة العربية السعودية',
          services: ['تذاكر طيران', 'حجوزات فنادق'],
          status: 'active',
          rating: 4.5,
          totalBookings: 150,
          lastBooking: '2024-01-15',
          website: 'www.arabair.com',
          taxNumber: '*********',
          bankAccount: 'SA*********0*********',
          paymentTerms: '30',
          notes: 'مورد موثوق للطيران',
          totalAmount: 45000,
          paidAmount: 40000,
          remainingAmount: 5000,
          createdDate: '2023-06-15'
        },
        {
          id: 2,
          name: 'فنادق الخليج الدولية',
          type: 'hotel',
          contact: 'فاطمة أحمد الفندقي',
          phone: '+************',
          email: '<EMAIL>',
          address: 'دبي، الإمارات العربية المتحدة',
          services: ['حجوزات فنادق', 'خدمات النقل'],
          status: 'active',
          rating: 4.8,
          totalBookings: 200,
          lastBooking: '2024-01-20',
          website: 'www.gulfhotels.com',
          taxNumber: '*********',
          bankAccount: 'AE*********0*********',
          paymentTerms: '15',
          notes: 'شريك استراتيجي للفنادق',
          totalAmount: 78000,
          paidAmount: 70000,
          remainingAmount: 8000,
          createdDate: '2023-08-10'
        },
        {
          id: 3,
          name: 'شركة النقل السياحي المتطور',
          type: 'transport',
          contact: 'محمد علي النقلي',
          phone: '+***********',
          email: '<EMAIL>',
          address: 'القاهرة، مصر',
          services: ['نقل المطار', 'رحلات سياحية'],
          status: 'active',
          rating: 4.2,
          totalBookings: 80,
          lastBooking: '2024-01-10',
          website: 'www.transport.com',
          taxNumber: '*********',
          bankAccount: 'EG*********0*********',
          paymentTerms: '45',
          notes: 'خدمات نقل متميزة',
          totalAmount: 32000,
          paidAmount: 32000,
          remainingAmount: 0,
          createdDate: '2023-09-05'
        },
        {
          id: 4,
          name: 'مكتب التأشيرات الدولي',
          type: 'visa',
          contact: 'سارة محمد التأشيري',
          phone: '+************',
          email: '<EMAIL>',
          address: 'جدة، المملكة العربية السعودية',
          services: ['تأشيرات سياحية', 'تأشيرات عمل'],
          status: 'active',
          rating: 4.6,
          totalBookings: 120,
          lastBooking: '2024-01-18',
          website: 'www.visaoffice.com',
          taxNumber: '*********',
          bankAccount: 'SA*********0*********',
          paymentTerms: '7',
          notes: 'متخصص في التأشيرات',
          totalAmount: 25000,
          paidAmount: 20000,
          remainingAmount: 5000,
          createdDate: '2023-07-20'
        },
        {
          id: 5,
          name: 'شركة التأمين السياحي',
          type: 'insurance',
          contact: 'خالد عبدالله التأميني',
          phone: '+************',
          email: '<EMAIL>',
          address: 'الدمام، المملكة العربية السعودية',
          services: ['تأمين سفر', 'تأمين صحي'],
          status: 'active',
          rating: 4.4,
          totalBookings: 95,
          lastBooking: '2024-01-12',
          website: 'www.insurance.com',
          taxNumber: '*********',
          bankAccount: '*********************',
          paymentTerms: '30',
          notes: 'تأمين شامل للمسافرين',
          totalAmount: 18000,
          paidAmount: 15000,
          remainingAmount: 3000,
          createdDate: '2023-10-01'
        },
        {
          id: 6,
          name: 'مطاعم الضيافة العربية',
          type: 'restaurant',
          contact: 'نورا عبدالرحمن الطباخي',
          phone: '+************',
          email: '<EMAIL>',
          address: 'مكة المكرمة، المملكة العربية السعودية',
          services: ['وجبات جماعية', 'خدمات ضيافة'],
          status: 'active',
          rating: 4.7,
          totalBookings: 60,
          lastBooking: '2024-01-08',
          website: 'www.hospitality.com',
          taxNumber: '*********',
          bankAccount: '*********************',
          paymentTerms: '15',
          notes: 'خدمات ضيافة راقية',
          totalAmount: 12000,
          paidAmount: 10000,
          remainingAmount: 2000,
          createdDate: '2023-11-15'
        }
      ];
      this.saveSuppliers();
    }
  }

  // حفظ الموردين في localStorage
  saveSuppliers() {
    localStorage.setItem('suppliers', JSON.stringify(this.suppliers));
    this.notifyListeners();
  }

  // الحصول على جميع الموردين
  getAllSuppliers() {
    return [...this.suppliers];
  }

  // الحصول على الموردين النشطين فقط
  getActiveSuppliers() {
    return this.suppliers.filter(supplier => supplier.status === 'active');
  }

  // الحصول على مورد بالمعرف
  getSupplierById(id) {
    return this.suppliers.find(supplier => supplier.id === id);
  }

  // الحصول على مورد بالاسم
  getSupplierByName(name) {
    return this.suppliers.find(supplier => supplier.name === name);
  }

  // البحث في الموردين
  searchSuppliers(searchTerm) {
    const term = searchTerm.toLowerCase();
    return this.suppliers.filter(supplier => 
      supplier.name.toLowerCase().includes(term) ||
      supplier.contact.toLowerCase().includes(term) ||
      supplier.email.toLowerCase().includes(term) ||
      supplier.phone.includes(term) ||
      (supplier.services && supplier.services.some(service => service.toLowerCase().includes(term)))
    );
  }

  // إضافة مورد جديد
  addSupplier(supplierData) {
    const newSupplier = {
      id: Math.max(...this.suppliers.map(s => s.id), 0) + 1,
      ...supplierData,
      createdDate: new Date().toISOString().split('T')[0],
      rating: 0,
      totalBookings: 0,
      lastBooking: null,
      totalAmount: supplierData.totalAmount || 0,
      paidAmount: supplierData.paidAmount || 0,
      remainingAmount: (supplierData.totalAmount || 0) - (supplierData.paidAmount || 0)
    };
    
    this.suppliers.push(newSupplier);
    this.saveSuppliers();
    return newSupplier;
  }

  // تحديث مورد
  updateSupplier(id, supplierData) {
    const index = this.suppliers.findIndex(supplier => supplier.id === id);
    if (index !== -1) {
      this.suppliers[index] = { 
        ...this.suppliers[index], 
        ...supplierData,
        remainingAmount: (supplierData.totalAmount || this.suppliers[index].totalAmount) - 
                        (supplierData.paidAmount || this.suppliers[index].paidAmount)
      };
      this.saveSuppliers();
      return this.suppliers[index];
    }
    return null;
  }

  // حذف مورد
  deleteSupplier(id) {
    const index = this.suppliers.findIndex(supplier => supplier.id === id);
    if (index !== -1) {
      const deletedSupplier = this.suppliers.splice(index, 1)[0];
      this.saveSuppliers();
      return deletedSupplier;
    }
    return null;
  }

  // الحصول على الموردين الذين لديهم ذمم دائنة
  getSuppliersWithPayables() {
    return this.suppliers.filter(supplier => supplier.remainingAmount > 0);
  }

  // الحصول على إجمالي الذمم الدائنة
  getTotalPayables() {
    return this.suppliers.reduce((total, supplier) => total + (supplier.remainingAmount || 0), 0);
  }

  // إضافة مستمع للتغييرات
  addListener(callback) {
    this.listeners.push(callback);
  }

  // إزالة مستمع
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // إشعار المستمعين بالتغييرات
  notifyListeners() {
    this.listeners.forEach(callback => callback(this.suppliers));
  }

  // تحويل الموردين إلى تنسيق مناسب للذمم الدائنة
  getSuppliersForPayables() {
    return this.suppliers.map(supplier => ({
      id: supplier.id,
      name: supplier.name,
      contact: supplier.contact,
      phone: supplier.phone,
      email: supplier.email,
      type: supplier.type,
      totalAmount: supplier.totalAmount || 0,
      paidAmount: supplier.paidAmount || 0,
      remainingAmount: supplier.remainingAmount || 0,
      lastTransactionDate: supplier.createdDate,
      status: supplier.status
    }));
  }

  // الحصول على إحصائيات الموردين
  getSuppliersStats() {
    const activeSuppliers = this.suppliers.filter(s => s.status === 'active').length;
    const topRated = this.suppliers.filter(s => s.rating >= 4.5).length;
    const newThisMonth = this.suppliers.filter(s => {
      const createdDate = new Date(s.createdDate);
      const now = new Date();
      return createdDate.getMonth() === now.getMonth() && createdDate.getFullYear() === now.getFullYear();
    }).length;
    const totalPayables = this.getTotalPayables();

    return {
      totalSuppliers: this.suppliers.length,
      activeSuppliers: activeSuppliers,
      topRated: topRated,
      newThisMonth: newThisMonth,
      totalPayables: totalPayables
    };
  }
}

// إنشاء مثيل واحد من الخدمة
const suppliersService = new SuppliersService();

export default suppliersService;
