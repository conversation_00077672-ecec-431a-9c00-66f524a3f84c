/* 🎨 أنماط زر تبديل الثيم المتطور */

/* 📦 حاوية زر الثيم */
.theme-toggle-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  position: relative;
}

.theme-toggle-container.small {
  gap: var(--space-1);
}

.theme-toggle-container.large {
  gap: var(--space-3);
}

/* 🎯 زر التبديل الرئيسي */
.theme-toggle-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-full);
  color: var(--neutral-700);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.theme-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 0;
}

.theme-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.theme-toggle-btn:hover::before {
  opacity: 1;
}

.theme-toggle-btn:hover .theme-icon,
.theme-toggle-btn:hover .theme-label,
.theme-toggle-btn:hover .auto-indicator {
  color: white;
  z-index: 1;
  position: relative;
}

/* 🎭 أيقونة الثيم */
.theme-icon {
  font-size: 1.25rem;
  transition: all var(--transition-fast);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 🏷️ تسمية الثيم */
.theme-label {
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
  transition: all var(--transition-fast);
}

/* 🤖 مؤشر الوضع التلقائي */
.auto-indicator {
  font-size: 0.75rem;
  background: var(--warning-500);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  font-weight: 700;
  animation: pulse 2s infinite;
}

/* 🎨 زر اختيار الثيمات */
.theme-selector-btn,
.theme-settings-btn {
  width: 40px;
  height: 40px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-full);
  color: var(--neutral-600);
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-selector-btn:hover,
.theme-settings-btn:hover {
  background: var(--primary-50);
  color: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 📋 قائمة الثيمات المنسدلة */
.theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--space-4);
  min-width: 400px;
  z-index: 1000;
  animation: slideInDown 0.3s ease-out;
}

.theme-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--glass-border);
}

.theme-dropdown-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.close-dropdown {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
  border-radius: var(--radius-full);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: bold;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-dropdown:hover {
  background: var(--danger-500);
  color: white;
}

/* 🌫️ خلفية القائمة المنسدلة */
.theme-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999;
}

/* 🌟 زر الثيم المبسط */
.simple-theme-toggle {
  width: 48px;
  height: 48px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-full);
  color: var(--neutral-700);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.simple-theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.simple-theme-toggle:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-300);
}

.simple-theme-toggle:hover::before {
  opacity: 1;
}

/* 🎭 عرض الثيم الحالي */
.theme-display {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.theme-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.theme-icon-display {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.theme-text {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.theme-name-display {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.auto-badge {
  font-size: 0.625rem;
  background: var(--warning-500);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  font-weight: 700;
  text-transform: uppercase;
}

.theme-colors-display {
  display: flex;
  gap: var(--space-1);
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-xs);
}

/* 📏 أحجام مختلفة */
.theme-toggle-container.small .theme-toggle-btn {
  padding: var(--space-2) var(--space-3);
  font-size: 0.75rem;
}

.theme-toggle-container.small .theme-icon {
  font-size: 1rem;
}

.theme-toggle-container.small .theme-selector-btn,
.theme-toggle-container.small .theme-settings-btn {
  width: 32px;
  height: 32px;
  font-size: 0.875rem;
}

.theme-toggle-container.large .theme-toggle-btn {
  padding: var(--space-4) var(--space-6);
  font-size: 1rem;
}

.theme-toggle-container.large .theme-icon {
  font-size: 1.5rem;
}

.theme-toggle-container.large .theme-selector-btn,
.theme-toggle-container.large .theme-settings-btn {
  width: 48px;
  height: 48px;
  font-size: 1.25rem;
}

/* 🎨 حالات الثيمات المختلفة */
.theme-toggle-btn.light {
  border-color: var(--primary-200);
  background: rgba(59, 130, 246, 0.05);
}

.theme-toggle-btn.dark {
  border-color: var(--warning-200);
  background: rgba(245, 158, 11, 0.05);
}

.theme-toggle-btn.ocean {
  border-color: rgba(14, 165, 233, 0.3);
  background: rgba(14, 165, 233, 0.05);
}

.theme-toggle-btn.sunset {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.05);
}

.theme-toggle-btn.forest {
  border-color: rgba(5, 150, 105, 0.3);
  background: rgba(5, 150, 105, 0.05);
}

.theme-toggle-btn.royal {
  border-color: rgba(124, 58, 237, 0.3);
  background: rgba(124, 58, 237, 0.05);
}

/* ✨ تأثيرات خاصة */
.theme-toggle-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.theme-toggle-btn:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .theme-toggle-btn,
.dark-mode .theme-toggle-btn {
  background: var(--glass-bg);
  border-color: var(--glass-border);
  color: var(--neutral-300);
}

[data-theme="dark"] .theme-selector-btn,
[data-theme="dark"] .theme-settings-btn,
.dark-mode .theme-selector-btn,
.dark-mode .theme-settings-btn {
  background: var(--glass-bg);
  border-color: var(--glass-border);
  color: var(--neutral-400);
}

[data-theme="dark"] .theme-selector-btn:hover,
[data-theme="dark"] .theme-settings-btn:hover,
.dark-mode .theme-selector-btn:hover,
.dark-mode .theme-settings-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  color: var(--primary-400);
}

[data-theme="dark"] .theme-dropdown,
.dark-mode .theme-dropdown {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .theme-dropdown-header h4,
.dark-mode .theme-dropdown-header h4 {
  color: var(--neutral-300);
}

[data-theme="dark"] .theme-name-display,
.dark-mode .theme-name-display {
  color: var(--neutral-300);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .theme-toggle-container {
    gap: var(--space-1);
  }

  .theme-toggle-btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
  }

  .theme-label {
    display: none;
  }

  .theme-selector-btn,
  .theme-settings-btn {
    width: 36px;
    height: 36px;
    font-size: 0.875rem;
  }

  .theme-dropdown {
    min-width: 300px;
    right: -50px;
  }

  .simple-theme-toggle {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .theme-dropdown {
    position: fixed;
    top: 50%;
    left: 50%;
    right: auto;
    transform: translate(-50%, -50%);
    min-width: 280px;
    max-width: 90vw;
  }

  .theme-toggle-container {
    flex-wrap: wrap;
  }
}

/* 🎬 حركات خاصة */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🎯 تحسينات الأداء */
.theme-toggle-btn,
.theme-selector-btn,
.theme-settings-btn,
.simple-theme-toggle {
  will-change: transform, box-shadow;
}

/* 🚫 تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-btn,
  .theme-selector-btn,
  .theme-settings-btn,
  .simple-theme-toggle {
    transition: none;
  }

  .theme-toggle-btn:hover,
  .theme-selector-btn:hover,
  .theme-settings-btn:hover,
  .simple-theme-toggle:hover {
    transform: none;
  }

  .auto-indicator {
    animation: none;
  }

  .theme-dropdown {
    animation: none;
  }
}

/* 🔧 تحسينات إمكانية الوصول */
.theme-toggle-btn:focus-visible,
.theme-selector-btn:focus-visible,
.theme-settings-btn:focus-visible,
.simple-theme-toggle:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 📋 تحسينات للقائمة المنسدلة المدمجة */
.theme-selector.compact {
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
}

.theme-selector.compact .theme-selector-header {
  display: none;
}

.theme-selector.compact .theme-grid {
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-2);
}

.theme-selector.compact .theme-option {
  padding: var(--space-3);
  font-size: 0.75rem;
}

.theme-selector.compact .theme-icon {
  font-size: 1.25rem;
  margin-bottom: var(--space-2);
}

.theme-selector.compact .theme-description {
  display: none;
}
