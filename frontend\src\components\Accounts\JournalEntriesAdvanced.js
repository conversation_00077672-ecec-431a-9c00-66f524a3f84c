import React, { useState, useEffect, useRef, useMemo } from 'react';
import './JournalEntries.css';

const JournalEntriesAdvanced = ({ transactions, accounts, currentUser, onAddTransaction, onUpdateTransaction, onDeleteTransaction }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [viewMode, setViewMode] = useState('table');
  const [sortConfig, setSortConfig] = useState({ key: 'date', direction: 'desc' });
  const [printOptions, setPrintOptions] = useState({
    includeDetails: true,
    includeAttachments: false,
    includeUserInfo: true,
    includeRunningBalance: true,
    pageSize: 'A4',
    orientation: 'landscape',
    groupByDate: false
  });
  const [exportFormat, setExportFormat] = useState('csv');
  const [advancedFilters, setAdvancedFilters] = useState({
    amountRange: { min: '', max: '' },
    entryTypes: [],
    hasAttachments: false,
    isBalanced: true,
    userFilter: '',
    accountFilter: ''
  });

  // بيانات القيد الجديد
  const [newEntry, setNewEntry] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    entries: [
      { accountId: '', accountName: '', description: '', debit: '', credit: '' },
      { accountId: '', accountName: '', description: '', debit: '', credit: '' }
    ],
    attachments: [],
    notes: '',
    tags: []
  });

  const printRef = useRef();

  // حساب إجمالي المدين والدائن للقيد
  const calculateTotals = (entries) => {
    const totalDebit = entries.reduce((sum, entry) => sum + (parseFloat(entry.debit) || 0), 0);
    const totalCredit = entries.reduce((sum, entry) => sum + (parseFloat(entry.credit) || 0), 0);
    return { totalDebit, totalCredit, isBalanced: Math.abs(totalDebit - totalCredit) < 0.01 };
  };

  // تصفية القيود
  const filteredEntries = useMemo(() => {
    return transactions.filter(entry => {
      const matchesSearch = 
        entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.id.toLowerCase().includes(searchTerm.toLowerCase());

      const entryDate = new Date(entry.date);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      const matchesDate = entryDate >= startDate && entryDate <= endDate;

      const { totalDebit, totalCredit } = calculateTotals(entry.entries || []);
      const matchesAmount = (!advancedFilters.amountRange.min || totalDebit >= parseFloat(advancedFilters.amountRange.min)) &&
                           (!advancedFilters.amountRange.max || totalDebit <= parseFloat(advancedFilters.amountRange.max));

      const matchesBalance = !advancedFilters.isBalanced || Math.abs(totalDebit - totalCredit) < 0.01;
      const matchesAttachments = !advancedFilters.hasAttachments || (entry.attachments && entry.attachments.length > 0);
      const matchesUser = !advancedFilters.userFilter || entry.user === advancedFilters.userFilter;

      return matchesSearch && matchesDate && matchesAmount && matchesBalance && matchesAttachments && matchesUser;
    }).sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (sortConfig.key === 'date') {
        return sortConfig.direction === 'asc' 
          ? new Date(aValue) - new Date(bValue)
          : new Date(bValue) - new Date(aValue);
      }
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [transactions, searchTerm, dateRange, advancedFilters, sortConfig]);

  // حساب الإحصائيات
  const entryStats = useMemo(() => {
    const totalEntries = filteredEntries.length;
    const totalDebit = filteredEntries.reduce((sum, entry) => {
      const { totalDebit } = calculateTotals(entry.entries || []);
      return sum + totalDebit;
    }, 0);
    const totalCredit = filteredEntries.reduce((sum, entry) => {
      const { totalCredit } = calculateTotals(entry.entries || []);
      return sum + totalCredit;
    }, 0);
    const balancedEntries = filteredEntries.filter(entry => {
      const { isBalanced } = calculateTotals(entry.entries || []);
      return isBalanced;
    }).length;
    const unbalancedEntries = totalEntries - balancedEntries;

    return {
      totalEntries,
      totalDebit,
      totalCredit,
      balancedEntries,
      unbalancedEntries,
      averageAmount: totalEntries > 0 ? totalDebit / totalEntries : 0
    };
  }, [filteredEntries]);

  // إضافة سطر جديد للقيد
  const addEntryLine = () => {
    setNewEntry(prev => ({
      ...prev,
      entries: [...prev.entries, { accountId: '', accountName: '', description: '', debit: '', credit: '' }]
    }));
  };

  // حذف سطر من القيد
  const removeEntryLine = (index) => {
    if (newEntry.entries.length > 2) {
      setNewEntry(prev => ({
        ...prev,
        entries: prev.entries.filter((_, i) => i !== index)
      }));
    }
  };

  // تحديث سطر في القيد
  const updateEntryLine = (index, field, value) => {
    setNewEntry(prev => ({
      ...prev,
      entries: prev.entries.map((entry, i) => 
        i === index ? { ...entry, [field]: value } : entry
      )
    }));
  };

  // حفظ القيد الجديد
  const handleSaveEntry = () => {
    const { totalDebit, totalCredit, isBalanced } = calculateTotals(newEntry.entries);
    
    if (!isBalanced) {
      alert('القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
      return;
    }

    if (!newEntry.description.trim()) {
      alert('يرجى إدخال وصف للقيد');
      return;
    }

    const entryData = {
      ...newEntry,
      id: `JE-${Date.now()}`,
      user: currentUser?.name || 'المستخدم الحالي',
      createdAt: new Date().toISOString(),
      totalDebit,
      totalCredit,
      isBalanced: true
    };

    if (onAddTransaction) {
      onAddTransaction(entryData);
    }

    // إعادة تعيين النموذج
    setNewEntry({
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      entries: [
        { accountId: '', accountName: '', description: '', debit: '', credit: '' },
        { accountId: '', accountName: '', description: '', debit: '', credit: '' }
      ],
      attachments: [],
      notes: '',
      tags: []
    });

    setShowAddModal(false);
    alert('تم حفظ القيد بنجاح');
  };

  // تعديل القيد
  const handleEditEntry = () => {
    const { totalDebit, totalCredit, isBalanced } = calculateTotals(selectedEntry.entries);
    
    if (!isBalanced) {
      alert('القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
      return;
    }

    const updatedEntry = {
      ...selectedEntry,
      totalDebit,
      totalCredit,
      isBalanced: true,
      updatedAt: new Date().toISOString(),
      updatedBy: currentUser?.name || 'المستخدم الحالي'
    };

    if (onUpdateTransaction) {
      onUpdateTransaction(updatedEntry);
    }

    setShowEditModal(false);
    setSelectedEntry(null);
    alert('تم تحديث القيد بنجاح');
  };

  // حذف القيد
  const handleDeleteEntry = () => {
    if (onDeleteTransaction) {
      onDeleteTransaction(selectedEntry.id);
    }
    setShowDeleteModal(false);
    setSelectedEntry(null);
    alert('تم حذف القيد بنجاح');
  };

  // وظائف الطباعة
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>القيود اليومية</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .entry-container { margin: 20px 0; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
          .entry-header { background: #f5f5f5; padding: 15px; border-bottom: 1px solid #ddd; }
          .entry-date { font-weight: bold; color: #333; }
          .entry-description { margin: 5px 0; }
          .entry-reference { font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .amount { font-weight: bold; text-align: left; }
          .debit { color: #27ae60; }
          .credit { color: #e74c3c; }
          .totals { background: #f8f9fa; font-weight: bold; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    const entriesToPrint = selectedEntry ? [selectedEntry] : filteredEntries;
    
    return `
      <div class="header">
        <div class="company-name">شركة شراء السياحية</div>
        <div class="report-title">القيود اليومية</div>
        <div class="date-range">
          من ${new Date(dateRange.startDate).toLocaleDateString('ar-SA')} 
          إلى ${new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
        </div>
      </div>
      
      ${entriesToPrint.map(entry => {
        const { totalDebit, totalCredit } = calculateTotals(entry.entries || []);
        return `
          <div class="entry-container">
            <div class="entry-header">
              <div class="entry-date">التاريخ: ${new Date(entry.date).toLocaleDateString('ar-SA')}</div>
              <div class="entry-description">البيان: ${entry.description}</div>
              ${entry.reference ? `<div class="entry-reference">المرجع: ${entry.reference}</div>` : ''}
              <div class="entry-reference">رقم القيد: ${entry.id}</div>
            </div>
            
            <table>
              <thead>
                <tr>
                  <th>اسم الحساب</th>
                  <th>البيان</th>
                  <th>مدين</th>
                  <th>دائن</th>
                </tr>
              </thead>
              <tbody>
                ${entry.entries?.map(entryLine => `
                  <tr>
                    <td>${entryLine.accountName || entryLine.accountId}</td>
                    <td>${entryLine.description}</td>
                    <td class="amount debit">${entryLine.debit ? parseFloat(entryLine.debit).toLocaleString() : '-'}</td>
                    <td class="amount credit">${entryLine.credit ? parseFloat(entryLine.credit).toLocaleString() : '-'}</td>
                  </tr>
                `).join('') || ''}
                <tr class="totals">
                  <td colspan="2"><strong>الإجمالي</strong></td>
                  <td class="amount debit"><strong>${totalDebit.toLocaleString()}</strong></td>
                  <td class="amount credit"><strong>${totalCredit.toLocaleString()}</strong></td>
                </tr>
              </tbody>
            </table>
          </div>
        `;
      }).join('')}
      
      <div class="footer">
        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
        <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
        <p>عدد القيود: ${entriesToPrint.length}</p>
        <p>إجمالي المدين: ${entryStats.totalDebit.toLocaleString()}</p>
        <p>إجمالي الدائن: ${entryStats.totalCredit.toLocaleString()}</p>
      </div>
    `;
  };

  // وظائف التصدير
  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    const headers = ['التاريخ', 'رقم القيد', 'البيان', 'المرجع', 'اسم الحساب', 'بيان السطر', 'مدين', 'دائن', 'المستخدم'];
    const rows = [];
    
    filteredEntries.forEach(entry => {
      entry.entries?.forEach(entryLine => {
        rows.push([
          new Date(entry.date).toLocaleDateString('ar-SA'),
          entry.id,
          entry.description,
          entry.reference || '',
          entryLine.accountName || entryLine.accountId,
          entryLine.description,
          entryLine.debit || 0,
          entryLine.credit || 0,
          entry.user || ''
        ]);
      });
    });

    const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `القيود-اليومية-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  // وظائف أخرى
  const resetFilters = () => {
    setSearchTerm('');
    setAdvancedFilters({
      amountRange: { min: '', max: '' },
      entryTypes: [],
      hasAttachments: false,
      isBalanced: true,
      userFilter: '',
      accountFilter: ''
    });
    setDateRange({
      startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    });
  };

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  return (
    <div className="journal-entries-advanced">
      <div className="entries-header">
        <div className="header-content">
          <h2>📝 القيود اليومية المتقدمة</h2>
          <p>إدارة شاملة للقيود المحاسبية مع وظائف متقدمة</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddModal(true)}
          >
            ➕ قيد جديد
          </button>
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-success"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowFilterModal(true)}
          >
            🔍 تصفية متقدمة
          </button>
          <button 
            className="btn btn-secondary"
            onClick={resetFilters}
          >
            🔄 إعادة تعيين
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="entries-stats">
        <div className="stat-card">
          <div className="stat-icon">📝</div>
          <div className="stat-info">
            <div className="stat-value">{entryStats.totalEntries}</div>
            <div className="stat-label">إجمالي القيود</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{entryStats.totalDebit.toLocaleString()}</div>
            <div className="stat-label">إجمالي المدين</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💳</div>
          <div className="stat-info">
            <div className="stat-value">{entryStats.totalCredit.toLocaleString()}</div>
            <div className="stat-label">إجمالي الدائن</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <div className="stat-value">{entryStats.balancedEntries}</div>
            <div className="stat-label">القيود المتوازنة</div>
          </div>
        </div>
        
        <div className="stat-card error">
          <div className="stat-icon">⚠️</div>
          <div className="stat-info">
            <div className="stat-value">{entryStats.unbalancedEntries}</div>
            <div className="stat-label">القيود غير المتوازنة</div>
          </div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="entries-controls">
        <div className="controls-row">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث في القيود..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="date-range">
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
          
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => setViewMode('table')}
            >
              📋 جدول
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'cards' ? 'active' : ''}`}
              onClick={() => setViewMode('cards')}
            >
              🗃️ بطاقات
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'timeline' ? 'active' : ''}`}
              onClick={() => setViewMode('timeline')}
            >
              📅 خط زمني
            </button>
          </div>
        </div>
      </div>

      {/* عرض البيانات */}
      <div className="entries-content">
        {viewMode === 'table' && (
          <div className="table-view">
            {filteredEntries.length === 0 ? (
              <div className="no-data">
                <span className="icon">📝</span>
                <h3>لا توجد قيود</h3>
                <p>لا توجد قيود تطابق المعايير المحددة</p>
                <button 
                  className="btn btn-primary"
                  onClick={() => setShowAddModal(true)}
                >
                  ➕ إضافة قيد جديد
                </button>
              </div>
            ) : (
              <div className="table-container">
                <table className="entries-table">
                  <thead>
                    <tr>
                      <th onClick={() => handleSort('date')}>
                        التاريخ {sortConfig.key === 'date' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('id')}>
                        رقم القيد {sortConfig.key === 'id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th>البيان</th>
                      <th>المرجع</th>
                      <th>إجمالي المدين</th>
                      <th>إجمالي الدائن</th>
                      <th>الحالة</th>
                      <th>المستخدم</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEntries.map(entry => {
                      const { totalDebit, totalCredit, isBalanced } = calculateTotals(entry.entries || []);
                      return (
                        <tr key={entry.id} className={!isBalanced ? 'unbalanced' : ''}>
                          <td className="date">
                            {new Date(entry.date).toLocaleDateString('ar-SA')}
                          </td>
                          <td className="entry-id">{entry.id}</td>
                          <td className="description">{entry.description}</td>
                          <td className="reference">{entry.reference || '-'}</td>
                          <td className="amount debit">{totalDebit.toLocaleString()}</td>
                          <td className="amount credit">{totalCredit.toLocaleString()}</td>
                          <td className="status">
                            <span className={`status-badge ${isBalanced ? 'balanced' : 'unbalanced'}`}>
                              {isBalanced ? '✅ متوازن' : '⚠️ غير متوازن'}
                            </span>
                          </td>
                          <td className="user">{entry.user || '-'}</td>
                          <td className="actions">
                            <button
                              className="action-btn view-btn"
                              onClick={() => {
                                setSelectedEntry(entry);
                                setShowDetailsModal(true);
                              }}
                              title="عرض التفاصيل"
                            >
                              👁️
                            </button>
                            <button
                              className="action-btn edit-btn"
                              onClick={() => {
                                setSelectedEntry(entry);
                                setShowEditModal(true);
                              }}
                              title="تعديل"
                            >
                              ✏️
                            </button>
                            <button
                              className="action-btn print-btn"
                              onClick={() => {
                                setSelectedEntry(entry);
                                handlePrint();
                              }}
                              title="طباعة"
                            >
                              🖨️
                            </button>
                            <button
                              className="action-btn delete-btn"
                              onClick={() => {
                                setSelectedEntry(entry);
                                setShowDeleteModal(true);
                              }}
                              title="حذف"
                            >
                              🗑️
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {viewMode === 'cards' && (
          <div className="cards-view">
            {filteredEntries.map(entry => {
              const { totalDebit, totalCredit, isBalanced } = calculateTotals(entry.entries || []);
              return (
                <div key={entry.id} className={`entry-card ${!isBalanced ? 'unbalanced' : ''}`}>
                  <div className="card-header">
                    <div className="card-title">
                      <span className="entry-id">{entry.id}</span>
                      <span className="entry-date">
                        {new Date(entry.date).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="card-status">
                      <span className={`status-badge ${isBalanced ? 'balanced' : 'unbalanced'}`}>
                        {isBalanced ? '✅' : '⚠️'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="card-content">
                    <div className="description">{entry.description}</div>
                    {entry.reference && <div className="reference">المرجع: {entry.reference}</div>}
                    <div className="amounts">
                      <div className="amount-item debit">
                        <span className="label">مدين:</span>
                        <span className="value">{totalDebit.toLocaleString()}</span>
                      </div>
                      <div className="amount-item credit">
                        <span className="label">دائن:</span>
                        <span className="value">{totalCredit.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="user">👤 {entry.user || 'غير محدد'}</div>
                    <div className="entries-count">📝 {entry.entries?.length || 0} سطر</div>
                  </div>
                  
                  <div className="card-actions">
                    <button
                      className="action-btn view-btn"
                      onClick={() => {
                        setSelectedEntry(entry);
                        setShowDetailsModal(true);
                      }}
                    >
                      👁️ عرض
                    </button>
                    <button
                      className="action-btn edit-btn"
                      onClick={() => {
                        setSelectedEntry(entry);
                        setShowEditModal(true);
                      }}
                    >
                      ✏️ تعديل
                    </button>
                    <button
                      className="action-btn print-btn"
                      onClick={() => {
                        setSelectedEntry(entry);
                        handlePrint();
                      }}
                    >
                      🖨️ طباعة
                    </button>
                    <button
                      className="action-btn delete-btn"
                      onClick={() => {
                        setSelectedEntry(entry);
                        setShowDeleteModal(true);
                      }}
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {viewMode === 'timeline' && (
          <div className="timeline-view">
            {filteredEntries.map((entry, index) => {
              const { totalDebit, isBalanced } = calculateTotals(entry.entries || []);
              return (
                <div key={entry.id} className="timeline-item">
                  <div className={`timeline-marker ${isBalanced ? 'balanced' : 'unbalanced'}`}></div>
                  <div className="timeline-content">
                    <div className="timeline-header">
                      <span className="timeline-date">
                        {new Date(entry.date).toLocaleDateString('ar-SA')}
                      </span>
                      <span className="timeline-id">{entry.id}</span>
                    </div>
                    <div className="timeline-description">{entry.description}</div>
                    <div className="timeline-details">
                      <span>المبلغ: {totalDebit.toLocaleString()}</span>
                      <span>الأسطر: {entry.entries?.length || 0}</span>
                      <span className={isBalanced ? 'balanced' : 'unbalanced'}>
                        {isBalanced ? '✅ متوازن' : '⚠️ غير متوازن'}
                      </span>
                    </div>
                    <div className="timeline-actions">
                      <button
                        className="action-btn view-btn"
                        onClick={() => {
                          setSelectedEntry(entry);
                          setShowDetailsModal(true);
                        }}
                      >
                        👁️ عرض
                      </button>
                      <button
                        className="action-btn edit-btn"
                        onClick={() => {
                          setSelectedEntry(entry);
                          setShowEditModal(true);
                        }}
                      >
                        ✏️ تعديل
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* نموذج إضافة قيد جديد */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="add-entry-modal">
            <div className="modal-header">
              <h3>➕ إضافة قيد جديد</h3>
              <button 
                className="close-btn"
                onClick={() => setShowAddModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="entry-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>التاريخ:</label>
                    <input
                      type="date"
                      value={newEntry.date}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, date: e.target.value }))}
                      className="form-control"
                    />
                  </div>
                  <div className="form-group">
                    <label>المرجع:</label>
                    <input
                      type="text"
                      placeholder="رقم المرجع (اختياري)"
                      value={newEntry.reference}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, reference: e.target.value }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>البيان:</label>
                  <textarea
                    placeholder="وصف القيد"
                    value={newEntry.description}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="2"
                  />
                </div>

                <div className="entries-section">
                  <div className="section-header">
                    <h4>أسطر القيد</h4>
                    <button
                      type="button"
                      className="btn btn-sm btn-secondary"
                      onClick={addEntryLine}
                    >
                      ➕ إضافة سطر
                    </button>
                  </div>

                  <div className="entries-table-container">
                    <table className="entries-form-table">
                      <thead>
                        <tr>
                          <th>الحساب</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                          <th>إجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {newEntry.entries.map((entry, index) => (
                          <tr key={index}>
                            <td>
                              <select
                                value={entry.accountId}
                                onChange={(e) => {
                                  const selectedAccount = accounts.find(acc => acc.id === e.target.value);
                                  updateEntryLine(index, 'accountId', e.target.value);
                                  updateEntryLine(index, 'accountName', selectedAccount?.name || '');
                                }}
                                className="form-control"
                              >
                                <option value="">اختر الحساب</option>
                                {accounts.filter(acc => acc.type === 'detail').map(account => (
                                  <option key={account.id} value={account.id}>
                                    {account.name} ({account.id})
                                  </option>
                                ))}
                              </select>
                            </td>
                            <td>
                              <input
                                type="text"
                                placeholder="بيان السطر"
                                value={entry.description}
                                onChange={(e) => updateEntryLine(index, 'description', e.target.value)}
                                className="form-control"
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                placeholder="0.00"
                                value={entry.debit}
                                onChange={(e) => updateEntryLine(index, 'debit', e.target.value)}
                                className="form-control amount-input"
                                step="0.01"
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                placeholder="0.00"
                                value={entry.credit}
                                onChange={(e) => updateEntryLine(index, 'credit', e.target.value)}
                                className="form-control amount-input"
                                step="0.01"
                              />
                            </td>
                            <td>
                              <button
                                type="button"
                                className="btn btn-sm btn-danger"
                                onClick={() => removeEntryLine(index)}
                                disabled={newEntry.entries.length <= 2}
                                title="حذف السطر"
                              >
                                🗑️
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="totals-summary">
                    {(() => {
                      const { totalDebit, totalCredit, isBalanced } = calculateTotals(newEntry.entries);
                      return (
                        <div className={`totals-row ${isBalanced ? 'balanced' : 'unbalanced'}`}>
                          <div className="total-item">
                            <span className="label">إجمالي المدين:</span>
                            <span className="value debit">{totalDebit.toLocaleString()}</span>
                          </div>
                          <div className="total-item">
                            <span className="label">إجمالي الدائن:</span>
                            <span className="value credit">{totalCredit.toLocaleString()}</span>
                          </div>
                          <div className="balance-status">
                            <span className={`status ${isBalanced ? 'balanced' : 'unbalanced'}`}>
                              {isBalanced ? '✅ متوازن' : '⚠️ غير متوازن'}
                            </span>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </div>

                <div className="form-group">
                  <label>ملاحظات:</label>
                  <textarea
                    placeholder="ملاحظات إضافية (اختياري)"
                    value={newEntry.notes}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, notes: e.target.value }))}
                    className="form-control"
                    rows="2"
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowAddModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleSaveEntry}
              >
                💾 حفظ القيد
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تعديل القيد */}
      {showEditModal && selectedEntry && (
        <div className="modal-overlay">
          <div className="edit-entry-modal">
            <div className="modal-header">
              <h3>✏️ تعديل القيد - {selectedEntry.id}</h3>
              <button 
                className="close-btn"
                onClick={() => setShowEditModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="entry-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>التاريخ:</label>
                    <input
                      type="date"
                      value={selectedEntry.date}
                      onChange={(e) => setSelectedEntry(prev => ({ ...prev, date: e.target.value }))}
                      className="form-control"
                    />
                  </div>
                  <div className="form-group">
                    <label>المرجع:</label>
                    <input
                      type="text"
                      placeholder="رقم المرجع (اختياري)"
                      value={selectedEntry.reference || ''}
                      onChange={(e) => setSelectedEntry(prev => ({ ...prev, reference: e.target.value }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>البيان:</label>
                  <textarea
                    placeholder="وصف القيد"
                    value={selectedEntry.description}
                    onChange={(e) => setSelectedEntry(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="2"
                  />
                </div>

                <div className="entries-section">
                  <div className="section-header">
                    <h4>أسطر القيد</h4>
                    <button
                      type="button"
                      className="btn btn-sm btn-secondary"
                      onClick={() => setSelectedEntry(prev => ({
                        ...prev,
                        entries: [...prev.entries, { accountId: '', accountName: '', description: '', debit: '', credit: '' }]
                      }))}
                    >
                      ➕ إضافة سطر
                    </button>
                  </div>

                  <div className="entries-table-container">
                    <table className="entries-form-table">
                      <thead>
                        <tr>
                          <th>الحساب</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                          <th>إجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedEntry.entries?.map((entry, index) => (
                          <tr key={index}>
                            <td>
                              <select
                                value={entry.accountId}
                                onChange={(e) => {
                                  const selectedAccount = accounts.find(acc => acc.id === e.target.value);
                                  setSelectedEntry(prev => ({
                                    ...prev,
                                    entries: prev.entries.map((ent, i) => 
                                      i === index ? { 
                                        ...ent, 
                                        accountId: e.target.value,
                                        accountName: selectedAccount?.name || ''
                                      } : ent
                                    )
                                  }));
                                }}
                                className="form-control"
                              >
                                <option value="">اختر الحساب</option>
                                {accounts.filter(acc => acc.type === 'detail').map(account => (
                                  <option key={account.id} value={account.id}>
                                    {account.name} ({account.id})
                                  </option>
                                ))}
                              </select>
                            </td>
                            <td>
                              <input
                                type="text"
                                placeholder="بيان السطر"
                                value={entry.description}
                                onChange={(e) => setSelectedEntry(prev => ({
                                  ...prev,
                                  entries: prev.entries.map((ent, i) => 
                                    i === index ? { ...ent, description: e.target.value } : ent
                                  )
                                }))}
                                className="form-control"
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                placeholder="0.00"
                                value={entry.debit}
                                onChange={(e) => setSelectedEntry(prev => ({
                                  ...prev,
                                  entries: prev.entries.map((ent, i) => 
                                    i === index ? { ...ent, debit: e.target.value } : ent
                                  )
                                }))}
                                className="form-control amount-input"
                                step="0.01"
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                placeholder="0.00"
                                value={entry.credit}
                                onChange={(e) => setSelectedEntry(prev => ({
                                  ...prev,
                                  entries: prev.entries.map((ent, i) => 
                                    i === index ? { ...ent, credit: e.target.value } : ent
                                  )
                                }))}
                                className="form-control amount-input"
                                step="0.01"
                              />
                            </td>
                            <td>
                              <button
                                type="button"
                                className="btn btn-sm btn-danger"
                                onClick={() => setSelectedEntry(prev => ({
                                  ...prev,
                                  entries: prev.entries.filter((_, i) => i !== index)
                                }))}
                                disabled={selectedEntry.entries.length <= 2}
                                title="حذف السطر"
                              >
                                🗑️
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="totals-summary">
                    {(() => {
                      const { totalDebit, totalCredit, isBalanced } = calculateTotals(selectedEntry.entries || []);
                      return (
                        <div className={`totals-row ${isBalanced ? 'balanced' : 'unbalanced'}`}>
                          <div className="total-item">
                            <span className="label">إجمالي المدين:</span>
                            <span className="value debit">{totalDebit.toLocaleString()}</span>
                          </div>
                          <div className="total-item">
                            <span className="label">إجمالي الدائن:</span>
                            <span className="value credit">{totalCredit.toLocaleString()}</span>
                          </div>
                          <div className="balance-status">
                            <span className={`status ${isBalanced ? 'balanced' : 'unbalanced'}`}>
                              {isBalanced ? '✅ متوازن' : '⚠️ غير متوازن'}
                            </span>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </div>

                <div className="form-group">
                  <label>ملاحظات:</label>
                  <textarea
                    placeholder="ملاحظات إضافية (اختياري)"
                    value={selectedEntry.notes || ''}
                    onChange={(e) => setSelectedEntry(prev => ({ ...prev, notes: e.target.value }))}
                    className="form-control"
                    rows="2"
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowEditModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleEditEntry}
              >
                💾 حفظ التعديلات
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج عرض تفاصيل القيد */}
      {showDetailsModal && selectedEntry && (
        <div className="modal-overlay">
          <div className="details-modal">
            <div className="modal-header">
              <h3>👁️ تفاصيل القيد - {selectedEntry.id}</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="entry-details">
                <div className="details-header">
                  <div className="detail-item">
                    <label>رقم القيد:</label>
                    <span>{selectedEntry.id}</span>
                  </div>
                  <div className="detail-item">
                    <label>التاريخ:</label>
                    <span>{new Date(selectedEntry.date).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>المرجع:</label>
                    <span>{selectedEntry.reference || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>المستخدم:</label>
                    <span>{selectedEntry.user || 'غير محدد'}</span>
                  </div>
                </div>

                <div className="description-section">
                  <label>البيان:</label>
                  <p>{selectedEntry.description}</p>
                </div>

                <div className="entries-details">
                  <h4>أسطر القيد:</h4>
                  <table className="details-table">
                    <thead>
                      <tr>
                        <th>الحساب</th>
                        <th>البيان</th>
                        <th>مدين</th>
                        <th>دائن</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedEntry.entries?.map((entry, index) => (
                        <tr key={index}>
                          <td>
                            <div className="account-info">
                              <div className="account-name">{entry.accountName || entry.accountId}</div>
                              <div className="account-code">{entry.accountId}</div>
                            </div>
                          </td>
                          <td>{entry.description}</td>
                          <td className="amount debit">
                            {entry.debit ? parseFloat(entry.debit).toLocaleString() : '-'}
                          </td>
                          <td className="amount credit">
                            {entry.credit ? parseFloat(entry.credit).toLocaleString() : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      {(() => {
                        const { totalDebit, totalCredit, isBalanced } = calculateTotals(selectedEntry.entries || []);
                        return (
                          <tr className="totals">
                            <td colSpan="2"><strong>الإجمالي</strong></td>
                            <td className="amount debit"><strong>{totalDebit.toLocaleString()}</strong></td>
                            <td className="amount credit"><strong>{totalCredit.toLocaleString()}</strong></td>
                          </tr>
                        );
                      })()}
                    </tfoot>
                  </table>
                </div>

                {selectedEntry.notes && (
                  <div className="notes-section">
                    <label>الملاحظات:</label>
                    <p>{selectedEntry.notes}</p>
                  </div>
                )}

                <div className="audit-info">
                  <h4>معلومات التدقيق:</h4>
                  <div className="audit-grid">
                    <div className="audit-item">
                      <label>تاريخ الإنشاء:</label>
                      <span>{selectedEntry.createdAt ? new Date(selectedEntry.createdAt).toLocaleString('ar-SA') : 'غير محدد'}</span>
                    </div>
                    <div className="audit-item">
                      <label>آخر تعديل:</label>
                      <span>{selectedEntry.updatedAt ? new Date(selectedEntry.updatedAt).toLocaleString('ar-SA') : 'لم يتم التعديل'}</span>
                    </div>
                    <div className="audit-item">
                      <label>عدل بواسطة:</label>
                      <span>{selectedEntry.updatedBy || 'لا يوجد'}</span>
                    </div>
                    <div className="audit-item">
                      <label>حالة القيد:</label>
                      <span className={calculateTotals(selectedEntry.entries || []).isBalanced ? 'balanced' : 'unbalanced'}>
                        {calculateTotals(selectedEntry.entries || []).isBalanced ? '✅ متوازن' : '⚠️ غير متوازن'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDetailsModal(false)}
              >
                إغلاق
              </button>
              <button 
                className="btn btn-warning"
                onClick={() => {
                  setShowDetailsModal(false);
                  setShowEditModal(true);
                }}
              >
                ✏️ تعديل
              </button>
              <button 
                className="btn btn-info"
                onClick={() => {
                  handlePrint();
                }}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تأكيد الحذف */}
      {showDeleteModal && selectedEntry && (
        <div className="modal-overlay">
          <div className="delete-modal">
            <div className="modal-header">
              <h3>🗑️ تأكيد الحذف</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDeleteModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="delete-warning">
                <div className="warning-icon">⚠️</div>
                <div className="warning-text">
                  <h4>هل أنت متأكد من حذف هذا القيد؟</h4>
                  <p>سيتم حذف القيد رقم <strong>{selectedEntry.id}</strong> نهائياً ولن يمكن استرداده.</p>
                  <div className="entry-summary">
                    <div className="summary-item">
                      <span className="label">التاريخ:</span>
                      <span>{new Date(selectedEntry.date).toLocaleDateString('ar-SA')}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">البيان:</span>
                      <span>{selectedEntry.description}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">المبلغ:</span>
                      <span>{calculateTotals(selectedEntry.entries || []).totalDebit.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-danger"
                onClick={handleDeleteEntry}
              >
                🗑️ حذف نهائياً
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeDetails}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                    />
                    تضمين تفاصيل الأسطر
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeUserInfo}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeUserInfo: e.target.checked }))}
                    />
                    تضمين معلومات المستخدم
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeRunningBalance}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeRunningBalance: e.target.checked }))}
                    />
                    تضمين الأرصدة الجارية
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.groupByDate}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, groupByDate: e.target.checked }))}
                    />
                    تجميع حسب التاريخ
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>

                <div className="print-preview">
                  <h4>معاينة الطباعة:</h4>
                  <div className="preview-info">
                    <div className="preview-item">
                      <span className="label">عدد القيود:</span>
                      <span className="value">{selectedEntry ? 1 : filteredEntries.length}</span>
                    </div>
                    <div className="preview-item">
                      <span className="label">إجمالي المدين:</span>
                      <span className="value">{entryStats.totalDebit.toLocaleString()}</span>
                    </div>
                    <div className="preview-item">
                      <span className="label">إجمالي الدائن:</span>
                      <span className="value">{entryStats.totalCredit.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">عدد القيود:</span>
                      <span className="value">{filteredEntries.length}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">عدد الأسطر:</span>
                      <span className="value">
                        {filteredEntries.reduce((sum, entry) => sum + (entry.entries?.length || 0), 0)}
                      </span>
                    </div>
                    <div className="summary-item">
                      <span className="label">الفترة:</span>
                      <span className="value">
                        {new Date(dateRange.startDate).toLocaleDateString('ar-SA')} - 
                        {new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="summary-item">
                      <span className="label">إجمالي المبلغ:</span>
                      <span className="value">{entryStats.totalDebit.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصفية المتقدمة */}
      {showFilterModal && (
        <div className="modal-overlay">
          <div className="filter-modal">
            <div className="modal-header">
              <h3>🔍 تصفية متقدمة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowFilterModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="filter-form">
                <div className="filter-group">
                  <label>نطاق المبلغ:</label>
                  <div className="range-inputs">
                    <input
                      type="number"
                      placeholder="من"
                      value={advancedFilters.amountRange.min}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        amountRange: { ...prev.amountRange, min: e.target.value }
                      }))}
                      className="form-control"
                    />
                    <span>إلى</span>
                    <input
                      type="number"
                      placeholder="إلى"
                      value={advancedFilters.amountRange.max}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        amountRange: { ...prev.amountRange, max: e.target.value }
                      }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="filter-group">
                  <label>المستخدم:</label>
                  <select
                    value={advancedFilters.userFilter}
                    onChange={(e) => setAdvancedFilters(prev => ({
                      ...prev,
                      userFilter: e.target.value
                    }))}
                    className="form-control"
                  >
                    <option value="">جميع المستخدمين</option>
                    {[...new Set(transactions.map(t => t.user).filter(Boolean))].map(user => (
                      <option key={user} value={user}>{user}</option>
                    ))}
                  </select>
                </div>

                <div className="filter-group">
                  <label>الحساب:</label>
                  <select
                    value={advancedFilters.accountFilter}
                    onChange={(e) => setAdvancedFilters(prev => ({
                      ...prev,
                      accountFilter: e.target.value
                    }))}
                    className="form-control"
                  >
                    <option value="">جميع الحسابات</option>
                    {accounts.filter(acc => acc.type === 'detail').map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.id})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="filter-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={advancedFilters.isBalanced}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        isBalanced: e.target.checked
                      }))}
                    />
                    القيود المتوازنة فقط
                  </label>
                </div>

                <div className="filter-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={advancedFilters.hasAttachments}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        hasAttachments: e.target.checked
                      }))}
                    />
                    القيود التي تحتوي على مرفقات فقط
                  </label>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowFilterModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-warning"
                onClick={resetFilters}
              >
                🔄 إعادة تعيين
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => setShowFilterModal(false)}
              >
                ✅ تطبيق المرشحات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JournalEntriesAdvanced;