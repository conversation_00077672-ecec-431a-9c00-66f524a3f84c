# 🔌 مرجع API - نظام إدارة السفر

## 🎯 نظرة عامة

هذا المرجع يوثق جميع واجهات برمجة التطبيقات (APIs) المستخدمة في نظام إدارة السفر، بما في ذلك المكونات والهوكس والأدوات المساعدة.

## 📋 فهرس المحتويات

- [مكونات واجهة المستخدم](#مكونات-واجهة-المستخدم)
- [مكونات الحجوزات](#مكونات-الحجوزات)
- [إدارة الحالة](#إدارة-الحالة)
- [أدوات تحسين الأداء](#أدوات-تحسين-الأداء)
- [أدوات الشبكة](#أدوات-الشبكة)
- [أدوات الذاكرة](#أدوات-الذاكرة)

## 🎨 مكونات واجهة المستخدم

### EnhancedNotifications

نظام إشعارات متقدم مع دعم الإجراءات والرسوم المتحركة.

#### الاستخدام

```jsx
import { EnhancedNotificationsProvider, useNotifications } from './components/UI/EnhancedNotifications';

// في المكون الجذر
<EnhancedNotificationsProvider>
  <App />
</EnhancedNotificationsProvider>

// في المكونات الفرعية
const { success, error, warning, info, loading } = useNotifications();
```

#### API

##### `useNotifications()`

يرجع كائن يحتوي على دوال الإشعارات:

```typescript
interface NotificationHook {
  success: (message: string, options?: NotificationOptions) => void;
  error: (message: string, options?: NotificationOptions) => void;
  warning: (message: string, options?: NotificationOptions) => void;
  info: (message: string, options?: NotificationOptions) => void;
  loading: (message: string, options?: NotificationOptions) => void;
}
```

##### `NotificationOptions`

```typescript
interface NotificationOptions {
  duration?: number;           // مدة العرض بالميلي ثانية (افتراضي: 5000)
  title?: string;             // عنوان الإشعار
  icon?: string;              // أيقونة مخصصة
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  actions?: NotificationAction[];  // أزرار الإجراءات
  persistent?: boolean;       // عدم الإخفاء التلقائي
}

interface NotificationAction {
  label: string;              // نص الزر
  onClick: () => void;        // دالة التنفيذ
  variant?: 'primary' | 'secondary';  // نوع الزر
}
```

#### أمثلة

```jsx
// إشعار بسيط
success('تم الحفظ بنجاح!');

// إشعار مع خيارات
error('حدث خطأ!', {
  duration: 10000,
  title: 'خطأ في النظام',
  icon: '⚠️'
});

// إشعار مع إجراءات
success('تم إنشاء الحجز', {
  actions: [
    {
      label: 'عرض',
      onClick: () => navigate('/booking/123')
    },
    {
      label: 'طباعة',
      onClick: () => printBooking(booking)
    }
  ]
});
```

### KeyboardShortcuts

نظام إدارة اختصارات لوحة المفاتيح مع دعم التجميع والمساعدة.

#### الاستخدام

```jsx
import { KeyboardShortcutsProvider, useShortcuts } from './components/UI/KeyboardShortcuts';

// في المكون الجذر
<KeyboardShortcutsProvider>
  <App />
</KeyboardShortcutsProvider>

// في المكونات الفرعية
const { registerShortcut, unregisterShortcut, getShortcuts } = useShortcuts();
```

#### API

##### `useShortcuts()`

```typescript
interface ShortcutsHook {
  registerShortcut: (combination: string, action: string, description: string, category?: string) => void;
  unregisterShortcut: (combination: string) => void;
  getShortcuts: () => Shortcut[];
  getShortcutsByCategory: (category: string) => Shortcut[];
  showHelp: () => void;
}

interface Shortcut {
  combination: string;        // مجموعة المفاتيح (مثل: 'ctrl+s')
  action: string;            // اسم الإجراء
  description: string;       // وصف الإجراء
  category: string;          // فئة الاختصار
}
```

#### أمثلة

```jsx
// تسجيل اختصار
registerShortcut('ctrl+s', 'save', 'حفظ الملف', 'general');

// إلغاء تسجيل اختصار
unregisterShortcut('ctrl+s');

// الحصول على جميع الاختصارات
const shortcuts = getShortcuts();

// الحصول على اختصارات فئة معينة
const fileShortcuts = getShortcutsByCategory('file');
```

### QuickSearch

مكون البحث السريع مع دعم الفلترة والتجميع.

#### الاستخدام

```jsx
import QuickSearch from './components/UI/QuickSearch';

<QuickSearch
  isOpen={isSearchOpen}
  onClose={() => setIsSearchOpen(false)}
  onSelect={(item) => handleSelect(item)}
  placeholder="البحث في النظام..."
/>
```

#### Props

```typescript
interface QuickSearchProps {
  isOpen: boolean;                    // حالة فتح/إغلاق البحث
  onClose: () => void;               // دالة الإغلاق
  onSelect: (item: SearchItem) => void;  // دالة اختيار العنصر
  placeholder?: string;              // نص المساعدة
  categories?: string[];             // فئات البحث
  maxResults?: number;               // عدد النتائج الأقصى
}

interface SearchItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  icon?: string;
  url?: string;
  data?: any;
}
```

## 📋 مكونات الحجوزات

### BookingActions

مكون إجراءات الحجوزات مع دعم العمليات المختلفة.

#### الاستخدام

```jsx
import BookingActions from './components/Bookings/BookingActions';

<BookingActions
  booking={booking}
  onView={handleView}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onPrint={handlePrint}
  onSavePDF={handleSavePDF}
  onStatusChange={handleStatusChange}
/>
```

#### Props

```typescript
interface BookingActionsProps {
  booking: Booking;                  // بيانات الحجز
  onView?: (booking: Booking) => void;
  onEdit?: (booking: Booking) => void;
  onDelete?: (booking: Booking) => void;
  onPrint?: (booking: Booking) => void;
  onSavePDF?: (booking: Booking) => void;
  onStatusChange?: (id: string, status: string) => void;
  showStatusChange?: boolean;        // إظهار قائمة تغيير الحالة
  hideEdit?: boolean;               // إخفاء زر التعديل
  hideDelete?: boolean;             // إخفاء زر الحذف
  loading?: boolean;                // حالة التحميل
  customActions?: CustomAction[];   // إجراءات مخصصة
}

interface CustomAction {
  label: string;
  icon: string;
  onClick: (booking: Booking) => void;
  className?: string;
}
```

### BookingAnalytics

مكون تحليلات الحجوزات مع الرسوم البيانية والإحصائيات.

#### الاستخدام

```jsx
import BookingAnalytics from './components/Analytics/BookingAnalytics';

<BookingAnalytics
  bookings={bookings}
  type="flight"
  timeRange="month"
/>
```

#### Props

```typescript
interface BookingAnalyticsProps {
  bookings: Booking[];              // قائمة الحجوزات
  type?: 'all' | 'flight' | 'umrah' | 'passport';  // نوع الحجوزات
  timeRange?: 'week' | 'month' | 'quarter' | 'year';  // الفترة الزمنية
  showInsights?: boolean;           // إظهار الرؤى والتوصيات
}
```

## 🏪 إدارة الحالة

### OptimizedStore

نظام إدارة الحالة المحسن مع دعم التخزين المؤقت والأداء.

#### الاستخدام

```jsx
import { OptimizedStoreProvider, useOptimizedStore } from './store/optimizedStore';

// في المكون الجذر
<OptimizedStoreProvider>
  <App />
</OptimizedStoreProvider>

// في المكونات الفرعية
const { state, actions, selectors } = useOptimizedStore();
```

#### API

##### `useOptimizedStore()`

```typescript
interface OptimizedStoreHook {
  state: AppState;
  actions: StoreActions;
  selectors: StoreSelectors;
}
```

##### هوكس متخصصة

```jsx
// للحجوزات
const { bookings, loading, actions } = useBookings();

// لواجهة المستخدم
const { theme, sidebarOpen, notifications, actions } = useUI();

// للإعدادات
const { settings, getSetting, updateSetting } = useSettings();
```

## ⚡ أدوات تحسين الأداء

### PerformanceMonitor

مراقب الأداء مع تتبع المقاييس والتقارير.

#### الاستخدام

```jsx
import { PerformanceMonitor } from './utils/performanceOptimizer';

const monitor = new PerformanceMonitor();

// تسجيل مراقب
monitor.subscribe((metric, value) => {
  console.log(`${metric}: ${value}`);
});

// الحصول على المقاييس
const metrics = monitor.getMetrics();

// إنشاء تقرير
const report = monitor.generateReport();
```

#### API

```typescript
class PerformanceMonitor {
  subscribe(callback: (metric: string, value: any) => void): void;
  unsubscribe(callback: Function): void;
  getMetrics(): PerformanceMetrics;
  generateReport(): PerformanceReport;
}

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  networkRequests: NetworkRequest[];
  errors: ErrorInfo[];
}
```

### CacheManager

مدير التخزين المؤقت مع دعم TTL والحد الأقصى للحجم.

#### الاستخدام

```jsx
import { CacheManager } from './utils/performanceOptimizer';

const cache = new CacheManager(100, 300000); // 100 عنصر، 5 دقائق

// حفظ البيانات
cache.set('key', data, 60000); // TTL مخصص

// استرجاع البيانات
const data = cache.get('key');

// مسح التخزين المؤقت
cache.clear();
```

#### API

```typescript
class CacheManager {
  constructor(maxSize?: number, ttl?: number);
  set(key: string, value: any, customTTL?: number): void;
  get(key: string): any | null;
  delete(key: string): void;
  clear(): void;
  getStats(): CacheStats;
}
```

## 🌐 أدوات الشبكة

### OptimizedApiClient

عميل API محسن مع إعادة المحاولة والتخزين المؤقت.

#### الاستخدام

```jsx
import { OptimizedApiClient } from './utils/networkOptimizer';

const apiClient = new OptimizedApiClient('/api', {
  timeout: 10000,
  retries: 3,
  cache: true
});

// طلب GET
const data = await apiClient.request('/bookings');

// طلب POST
const result = await apiClient.request('/bookings', {
  method: 'POST',
  body: JSON.stringify(bookingData)
});
```

#### API

```typescript
class OptimizedApiClient {
  constructor(baseURL: string, options?: ApiClientOptions);
  request(endpoint: string, options?: RequestOptions): Promise<any>;
  clearCache(): void;
  getStats(): ApiStats;
}

interface ApiClientOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
}
```

### NetworkQualityMonitor

مراقب جودة الشبكة مع قياس السرعة وزمن الاستجابة.

#### الاستخدام

```jsx
import { NetworkQualityMonitor } from './utils/networkOptimizer';

const monitor = new NetworkQualityMonitor();

// تسجيل مراقب
monitor.subscribe((quality) => {
  console.log('جودة الشبكة:', quality);
});

// الحصول على الجودة الحالية
const quality = monitor.getQuality();
```

## 🧠 أدوات الذاكرة

### MemoryMonitor

مراقب استخدام الذاكرة مع تنبيهات الضغط.

#### الاستخدام

```jsx
import { MemoryMonitor } from './utils/memoryOptimizer';

const monitor = new MemoryMonitor();

// تسجيل مراقب
monitor.subscribe((metrics) => {
  console.log('استخدام الذاكرة:', monitor.formatBytes(metrics.usedJSHeapSize));
});
```

### OptimizedImage

مكون صورة محسن مع التحميل البطيء والتخزين المؤقت.

#### الاستخدام

```jsx
import { OptimizedImage } from './utils/memoryOptimizer';

<OptimizedImage
  src="/path/to/image.jpg"
  alt="وصف الصورة"
  placeholder={<div>جاري التحميل...</div>}
  className="my-image"
/>
```

#### Props

```typescript
interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: React.ReactNode;
  onLoad?: () => void;
  onError?: () => void;
}
```

## 🎣 هوكس مخصصة

### useMemoryOptimization

هوك لتحسين استخدام الذاكرة مع تنظيف تلقائي.

```jsx
import { useMemoryOptimization } from './utils/memoryOptimizer';

function MyComponent() {
  const { addCleanup, removeCleanup } = useMemoryOptimization();
  
  useEffect(() => {
    const timer = setInterval(() => {
      // عمل دوري
    }, 1000);
    
    // إضافة دالة التنظيف
    addCleanup(() => clearInterval(timer));
    
    return () => {
      removeCleanup(() => clearInterval(timer));
    };
  }, []);
}
```

### useOptimizedMemo

هوك memo محسن مع TTL وحد أقصى للحجم.

```jsx
import { useOptimizedMemo } from './utils/memoryOptimizer';

function MyComponent({ data }) {
  const processedData = useOptimizedMemo(() => {
    return expensiveCalculation(data);
  }, [data], {
    maxAge: 60000,  // دقيقة واحدة
    maxSize: 50     // 50 عنصر
  });
}
```

### useMobileOptimization

هوك لكشف الأجهزة المحمولة وتحسين الأداء.

```jsx
import { useMobileOptimization } from './utils/memoryOptimizer';

function MyComponent() {
  const { isMobile, isLowMemory, shouldOptimize } = useMobileOptimization();
  
  return (
    <div>
      {shouldOptimize ? (
        <SimplifiedView />
      ) : (
        <FullFeaturedView />
      )}
    </div>
  );
}
```

## 📊 أنواع البيانات

### Booking

```typescript
interface Booking {
  id: string;
  type: 'flight' | 'umrah' | 'hajj' | 'passport' | 'visa' | 'hotel';
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  amount: number;
  paidAmount: number;
  remainingAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  // خصائص إضافية حسب النوع
}
```

### Customer

```typescript
interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  idNumber?: string;
  passportNumber?: string;
  address?: Address;
  bookings: Booking[];
  totalSpent: number;
  createdAt: string;
}
```

### Address

```typescript
interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode?: string;
}
```

---

📝 **ملاحظة**: هذا المرجع يتم تحديثه مع كل إصدار جديد. للحصول على أحدث المعلومات، راجع الكود المصدري أو الوثائق المضمنة.
