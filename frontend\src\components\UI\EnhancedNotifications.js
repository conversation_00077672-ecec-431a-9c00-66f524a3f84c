import React, { useState, useEffect, createContext, useContext } from 'react';
import './EnhancedNotifications.css';

// 🔔 سياق الإشعارات
const NotificationContext = createContext();

// 🎯 أنواع الإشعارات
const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading'
};

// 🎨 أيقونات الإشعارات
const NOTIFICATION_ICONS = {
  success: '✅',
  error: '❌',
  warning: '⚠️',
  info: 'ℹ️',
  loading: '⏳'
};

// 📱 مزود الإشعارات
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);

  // 🆔 إنشاء معرف فريد
  const generateId = () => Date.now() + Math.random();

  // 🧹 تنظيف الإشعارات القديمة
  const cleanupOldNotifications = () => {
    setNotifications(prev => {
      const now = new Date();
      const filtered = prev.filter(notification => {
        const age = now - new Date(notification.timestamp);
        // إزالة الإشعارات التي انتهت مدتها
        if (notification.duration > 0 && age > notification.duration) {
          return false;
        }
        // إزالة الإشعارات القديمة جداً (أكثر من 20 ثانية)
        if (age > 20000) {
          return false;
        }
        return true;
      });

      // 🔢 الحد الأقصى للإشعارات المعروضة (3 فقط)
      return filtered.slice(-3);
    });
  };

  // ➕ إضافة إشعار جديد
  const addNotification = (notification) => {
    const id = generateId();
    const newNotification = {
      id,
      type: NOTIFICATION_TYPES.INFO,
      duration: 4000, // تقليل المدة الافتراضية
      dismissible: true,
      showProgress: true,
      ...notification,
      timestamp: new Date()
    };

    setNotifications(prev => {
      // 🚫 منع تكرار الإشعارات المتشابهة
      const isDuplicate = prev.some(existing =>
        existing.message === newNotification.message &&
        existing.type === newNotification.type &&
        (new Date() - new Date(existing.timestamp)) < 2000 // خلال ثانيتين
      );

      if (isDuplicate) {
        return prev;
      }

      // 📊 الحد الأقصى للإشعارات المعروضة
      const maxNotifications = 5;
      const updatedNotifications = [...prev, newNotification];

      if (updatedNotifications.length > maxNotifications) {
        return updatedNotifications.slice(-maxNotifications);
      }

      return updatedNotifications;
    });

    // 🕐 إزالة تلقائية
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    // 🧹 تنظيف دوري
    setTimeout(cleanupOldNotifications, 1000);

    return id;
  };

  // ❌ إزالة إشعار
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // 🧹 مسح جميع الإشعارات
  const clearAll = () => {
    setNotifications([]);
  };

  // 🔄 تنظيف دوري كل 5 ثوانٍ
  React.useEffect(() => {
    const interval = setInterval(cleanupOldNotifications, 5000);
    return () => clearInterval(interval);
  }, []);

  // 🔄 تنظيف فوري عند تغيير الإشعارات
  React.useEffect(() => {
    if (notifications.length > 3) {
      const timer = setTimeout(cleanupOldNotifications, 500);
      return () => clearTimeout(timer);
    }
  }, [notifications.length]);

  // 🔄 تحديث إشعار
  const updateNotification = (id, updates) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, ...updates } : n)
    );
  };

  // 🎯 دوال مساعدة سريعة
  const success = (message, options = {}) => {
    // تنظيف الإشعارات القديمة قبل إضافة الجديد
    cleanupOldNotifications();
    return addNotification({ type: NOTIFICATION_TYPES.SUCCESS, message, duration: 3000, ...options });
  };

  const error = (message, options = {}) => {
    cleanupOldNotifications();
    return addNotification({ type: NOTIFICATION_TYPES.ERROR, message, duration: 6000, ...options });
  };

  const warning = (message, options = {}) => {
    cleanupOldNotifications();
    return addNotification({ type: NOTIFICATION_TYPES.WARNING, message, duration: 4000, ...options });
  };

  const info = (message, options = {}) => {
    cleanupOldNotifications();
    return addNotification({ type: NOTIFICATION_TYPES.INFO, message, duration: 3000, ...options });
  };

  const loading = (message, options = {}) => {
    cleanupOldNotifications();
    return addNotification({ type: NOTIFICATION_TYPES.LOADING, message, duration: 0, dismissible: false, ...options });
  };

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications: clearAll,
    updateNotification,
    success,
    error,
    warning,
    info,
    loading
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  );
};

// 🎣 هوك استخدام الإشعارات
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within NotificationProvider');
  }
  return context;
};

// 📦 حاوية الإشعارات
const NotificationContainer = () => {
  const { notifications, clearAllNotifications } = useNotifications();

  return (
    <div className="notification-container">
      {/* 🧹 زر مسح الكل */}
      {notifications.length > 2 && (
        <div className="clear-all-notifications">
          <button
            onClick={clearAllNotifications}
            className="clear-all-btn"
            title="مسح جميع الإشعارات"
          >
            🗑️ مسح الكل
          </button>
        </div>
      )}

      {notifications.map(notification => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}
    </div>
  );
};

// 🎴 عنصر الإشعار
const NotificationItem = ({ notification }) => {
  const { removeNotification } = useNotifications();
  const [progress, setProgress] = useState(100);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 🎬 حركة الدخول
    setTimeout(() => setIsVisible(true), 10);

    // 📊 شريط التقدم
    if (notification.duration > 0 && notification.showProgress) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev - (100 / (notification.duration / 100));
          if (newProgress <= 0) {
            handleClose();
            return 0;
          }
          return newProgress;
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [notification.duration, notification.showProgress]);

  // 🎯 إغلاق الإشعار
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => removeNotification(notification.id), 300);
  };

  // ⏰ إزالة تلقائية بعد انتهاء المدة
  useEffect(() => {
    if (notification.duration > 0) {
      const timer = setTimeout(handleClose, notification.duration);
      return () => clearTimeout(timer);
    }
  }, [notification.duration]);

  return (
    <div
      className={`notification-item ${notification.type} ${isVisible ? 'visible' : ''}`}
      onClick={notification.onClick}
    >
      {/* 🎭 أيقونة الإشعار */}
      <div className="notification-icon">
        {notification.icon || NOTIFICATION_ICONS[notification.type]}
      </div>

      {/* 📝 محتوى الإشعار */}
      <div className="notification-content">
        {notification.title && (
          <div className="notification-title">{notification.title}</div>
        )}
        <div className="notification-message">{notification.message}</div>
        {notification.description && (
          <div className="notification-description">{notification.description}</div>
        )}
      </div>

      {/* 🎬 أزرار الإجراءات */}
      {notification.actions && (
        <div className="notification-actions">
          {notification.actions.map((action, index) => (
            <button
              key={index}
              className={`notification-action ${action.variant || 'primary'}`}
              onClick={(e) => {
                e.stopPropagation();
                action.onClick();
                if (action.closeOnClick !== false) {
                  handleClose();
                }
              }}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}

      {/* ❌ زر الإغلاق */}
      {notification.dismissible && (
        <button
          className="notification-close"
          onClick={(e) => {
            e.stopPropagation();
            handleClose();
          }}
        >
          ✕
        </button>
      )}

      {/* 📊 شريط التقدم */}
      {notification.showProgress && notification.duration > 0 && (
        <div className="notification-progress">
          <div
            className="notification-progress-bar"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
};

// 🎯 مكون إشعار سريع
export const QuickNotification = ({ type, message, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000);
    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <div className={`quick-notification ${type}`}>
      <span className="quick-notification-icon">
        {NOTIFICATION_ICONS[type]}
      </span>
      <span className="quick-notification-message">{message}</span>
      <button className="quick-notification-close" onClick={onClose}>
        ✕
      </button>
    </div>
  );
};

// 🔔 مكون مركز الإشعارات
export const NotificationCenter = ({ isOpen, onClose }) => {
  const { notifications, clearAll, removeNotification } = useNotifications();

  if (!isOpen) return null;

  return (
    <div className="notification-center-overlay" onClick={onClose}>
      <div className="notification-center" onClick={(e) => e.stopPropagation()}>
        <div className="notification-center-header">
          <h3>🔔 مركز الإشعارات</h3>
          <div className="notification-center-actions">
            <button className="btn btn-ghost btn-sm" onClick={clearAll}>
              مسح الكل
            </button>
            <button className="btn btn-ghost btn-sm" onClick={onClose}>
              ✕
            </button>
          </div>
        </div>

        <div className="notification-center-content">
          {notifications.length === 0 ? (
            <div className="notification-center-empty">
              <div className="empty-icon">🔕</div>
              <div className="empty-message">لا توجد إشعارات</div>
            </div>
          ) : (
            <div className="notification-center-list">
              {notifications.map(notification => (
                <div key={notification.id} className="notification-center-item">
                  <div className="notification-center-item-icon">
                    {notification.icon || NOTIFICATION_ICONS[notification.type]}
                  </div>
                  <div className="notification-center-item-content">
                    <div className="notification-center-item-message">
                      {notification.message}
                    </div>
                    <div className="notification-center-item-time">
                      {notification.timestamp.toLocaleTimeString('ar-SA')}
                    </div>
                  </div>
                  <button
                    className="notification-center-item-close"
                    onClick={() => removeNotification(notification.id)}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// تصدير مع أسماء مختلفة للمرونة
export const EnhancedNotificationsProvider = NotificationProvider;
export default NotificationProvider;
