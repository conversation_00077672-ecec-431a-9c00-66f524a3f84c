/* Components Demo Styles */

.components-demo {
  padding: var(--space-6);
  max-width: 1400px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-3xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.demo-header h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
}

.demo-header p {
  font-size: var(--text-lg);
  color: var(--neutral-600);
  margin: 0;
}

.demo-tabs {
  background: var(--neutral-0);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
}

.demo-section {
  padding: var(--space-8);
}

.demo-section h2 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-3);
  border-bottom: 2px solid var(--primary-200);
  position: relative;
}

.demo-section h2::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--primary-500);
}

.demo-section h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
  margin-bottom: var(--space-3);
}

.demo-section h4 {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
  margin-bottom: var(--space-3);
}

/* Progress Demo */
.progress-demo {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

/* Alerts Demo */
.alerts-demo {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

/* Status Demo */
.status-demo {
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-200);
}

.status-demo h4 {
  margin-bottom: var(--space-3);
  color: var(--neutral-800);
  font-weight: var(--font-semibold);
}

/* Chart containers */
.demo-section .modern-card {
  transition: all var(--transition-base);
}

.demo-section .modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

/* Button demo spacing */
.demo-section .modern-flex {
  margin-bottom: var(--space-6);
}

/* Form demo styling */
.demo-section .modern-form {
  background: var(--neutral-50);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--neutral-200);
}

/* Dropdown demo */
.dropdown-divider {
  height: 1px;
  background: var(--neutral-200);
  margin: var(--space-2) 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .components-demo {
    padding: var(--space-4);
  }
  
  .demo-header {
    padding: var(--space-6);
  }
  
  .demo-header h1 {
    font-size: var(--text-3xl);
  }
  
  .demo-header p {
    font-size: var(--text-base);
  }
  
  .demo-section {
    padding: var(--space-6);
  }
  
  .demo-section h2 {
    font-size: var(--text-xl);
  }
  
  .status-demo {
    padding: var(--space-3);
  }
}

@media (max-width: 480px) {
  .components-demo {
    padding: var(--space-3);
  }
  
  .demo-header {
    padding: var(--space-4);
  }
  
  .demo-header h1 {
    font-size: var(--text-2xl);
  }
  
  .demo-section {
    padding: var(--space-4);
  }
  
  .demo-section h2 {
    font-size: var(--text-lg);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .demo-header {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .demo-header h1 {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .demo-header p {
    color: var(--neutral-400);
  }
  
  .demo-tabs {
    background: var(--neutral-800);
  }
  
  .demo-section h2,
  .demo-section h3,
  .demo-section h4,
  .status-demo h4 {
    color: var(--neutral-200);
  }
  
  .demo-section h2 {
    border-bottom-color: var(--primary-600);
  }
  
  .status-demo {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .demo-section .modern-form {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .dropdown-divider {
    background: var(--neutral-600);
  }
}

/* Print styles */
@media print {
  .components-demo {
    padding: 0;
  }
  
  .demo-header {
    background: white !important;
    border: 1px solid #000 !important;
  }
  
  .demo-header h1 {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }
  
  .demo-header p {
    color: #666 !important;
  }
  
  .demo-tabs {
    background: white !important;
    box-shadow: none !important;
  }
  
  .demo-section h2,
  .demo-section h3,
  .demo-section h4 {
    color: black !important;
  }
  
  .status-demo {
    background: white !important;
    border: 1px solid #000 !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .demo-section .modern-card {
    transition: none !important;
  }
  
  .demo-section .modern-card:hover {
    transform: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .demo-header,
  .demo-tabs,
  .status-demo,
  .demo-section .modern-form {
    border: 2px solid currentColor !important;
  }
  
  .demo-section h2 {
    border-bottom: 2px solid currentColor !important;
  }
  
  .dropdown-divider {
    background: currentColor !important;
    height: 2px;
  }
}