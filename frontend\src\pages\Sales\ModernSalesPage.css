/* Modern Sales Page Styles */

.modern-sales-page {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  padding: var(--space-6);
  background: transparent;
  min-height: 100vh;
}

/* ===== PAGE HEADER ===== */
.sales-page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-6);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  animation: slideInDown 0.6s ease-out;
}

.sales-page-title h1 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: var(--leading-tight);
}

.sales-page-title p {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
  line-height: var(--leading-relaxed);
}

.sales-page-actions {
  display: flex;
  gap: var(--space-3);
  flex-shrink: 0;
}

/* ===== STATS SECTION ===== */
.sales-stats {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

/* ===== TABLE SECTION ===== */
.modern-sales-page .modern-card {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

/* ===== TABLE CUSTOM STYLES ===== */
.invoice-number {
  font-family: var(--font-family-mono);
  font-weight: var(--font-bold);
  color: var(--primary-600);
  background: var(--primary-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
}

.amount {
  font-family: var(--font-family-mono);
  font-weight: var(--font-bold);
  color: var(--success-600);
  font-size: var(--text-sm);
}

/* ===== QUICK ACTIONS ===== */
.quick-actions {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

.quick-action-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-2);
  opacity: 0.8;
  transition: all var(--transition-base);
}

.quick-actions .modern-card:hover .quick-action-icon {
  transform: scale(1.1);
  opacity: 1;
}

.quick-actions .modern-card h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  text-align: center;
}

.quick-actions .modern-card p {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-sm);
  color: var(--neutral-600);
  text-align: center;
  line-height: var(--leading-relaxed);
}

/* ===== ANIMATIONS ===== */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== FILTERS SECTION ===== */
.sales-filters {
  display: flex;
  gap: var(--space-4);
  align-items: flex-end;
  flex-wrap: wrap;
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--neutral-200);
}

/* ===== STATUS INDICATORS ===== */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator--paid {
  background: var(--success-100);
  color: var(--success-700);
}

.status-indicator--pending {
  background: var(--warning-100);
  color: var(--warning-700);
}

.status-indicator--overdue {
  background: var(--error-100);
  color: var(--error-700);
}

.status-indicator--draft {
  background: var(--neutral-100);
  color: var(--neutral-700);
}

/* ===== BULK ACTIONS ===== */
.bulk-actions {
  display: flex;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--primary-50);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  animation: slideInDown 0.3s ease-out;
}

.bulk-actions-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--primary-700);
}

.bulk-actions-buttons {
  display: flex;
  gap: var(--space-2);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .sales-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .modern-sales-page {
    padding: var(--space-4);
    gap: var(--space-6);
  }
  
  .sales-page-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
    text-align: center;
    padding: var(--space-4);
  }
  
  .sales-page-title h1 {
    font-size: var(--text-3xl);
  }
  
  .sales-page-title p {
    font-size: var(--text-base);
  }
  
  .sales-page-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .sales-stats {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .sales-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .bulk-actions {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .bulk-actions-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .modern-sales-page {
    padding: var(--space-3);
    gap: var(--space-4);
  }
  
  .sales-page-header {
    padding: var(--space-3);
  }
  
  .sales-page-title h1 {
    font-size: var(--text-2xl);
  }
  
  .sales-page-title p {
    font-size: var(--text-sm);
  }
  
  .sales-page-actions {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .quick-action-icon {
    font-size: var(--text-4xl);
  }
  
  .quick-actions .modern-card h3 {
    font-size: var(--text-base);
  }
  
  .quick-actions .modern-card p {
    font-size: var(--text-xs);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .sales-page-header {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .sales-page-title h1 {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .sales-page-title p {
    color: var(--neutral-400);
  }
  
  .invoice-number {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .amount {
    color: var(--success-400);
  }
  
  .quick-actions .modern-card h3 {
    color: var(--neutral-200);
  }
  
  .quick-actions .modern-card p {
    color: var(--neutral-400);
  }
  
  .sales-filters {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .bulk-actions {
    background: var(--primary-800);
    border-color: var(--primary-600);
  }
  
  .bulk-actions-info {
    color: var(--primary-300);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-sales-page {
    padding: 0;
    background: white !important;
  }
  
  .sales-page-header {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .sales-page-actions,
  .quick-actions,
  .bulk-actions {
    display: none !important;
  }
  
  .sales-page-title h1 {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }
  
  .sales-page-title p {
    color: #666 !important;
  }
  
  .invoice-number {
    background: #f0f0f0 !important;
    color: black !important;
  }
  
  .amount {
    color: black !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-sales-page,
  .sales-page-header,
  .sales-stats,
  .modern-card,
  .quick-actions,
  .bulk-actions {
    animation: none !important;
  }
  
  .quick-action-icon {
    transition: none !important;
  }
  
  .quick-actions .modern-card:hover .quick-action-icon {
    transform: none !important;
  }
}

/* Focus styles for keyboard navigation */
.sales-page-actions button:focus,
.quick-actions .modern-card:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .sales-page-header,
  .sales-filters,
  .bulk-actions {
    border: 2px solid currentColor !important;
  }
  
  .invoice-number,
  .status-indicator {
    border: 1px solid currentColor;
  }
}