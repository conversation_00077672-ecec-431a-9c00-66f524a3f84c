# ===================================================================
# 🚀 سكربت تشغيل نظام شراء للسفر والسياحة المتطور
# Sharau Travel & Tourism System Launcher
# ===================================================================

# إعداد الألوان والتنسيق
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

# رسم شعار النظام
Write-Host "
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║    ✈️  نظام شراء للسفر والسياحة المتطور  ✈️                    ║
║         Sharau Travel & Tourism System                          ║
║                                                                  ║
║    🎨 نظام القوالب المذهل | 💰 إدارة السندات المتطورة          ║
║    📊 التقارير التحليلية | 🌍 إدارة الحجوزات الشاملة          ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
" -ForegroundColor Cyan

Write-Host "🔄 جاري تشغيل النظام..." -ForegroundColor Yellow
Write-Host ""

# متغيرات النظام
$PROJECT_PATH = "c:\Users\<USER>\Desktop\sharaubtravelsoft"
$FRONTEND_PATH = "$PROJECT_PATH\frontend"
$BACKEND_PATH = "$PROJECT_PATH\backend"
$LOG_FILE = "$PROJECT_PATH\system.log"
$PID_FILE = "$PROJECT_PATH\system.pid"

# دالة كتابة السجلات
function Write-Log {
    param($Message, $Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Add-Content -Path $LOG_FILE -Value $logEntry
    
    switch ($Level) {
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "⚠️ $Message" -ForegroundColor Yellow }
        default { Write-Host "ℹ️ $Message" -ForegroundColor White }
    }
}

# دالة فحص المتطلبات
function Test-Requirements {
    Write-Log "فحص متطلبات النظام..." "INFO"
    
    # فحص Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Log "Node.js متوفر: $nodeVersion" "SUCCESS"
        } else {
            throw "Node.js غير مثبت"
        }
    } catch {
        Write-Log "Node.js غير مثبت. يرجى تثبيت Node.js أولاً" "ERROR"
        Write-Host "📥 تحميل Node.js من: https://nodejs.org" -ForegroundColor Cyan
        return $false
    }
    
    # فحص npm
    try {
        $npmVersion = npm --version 2>$null
        if ($npmVersion) {
            Write-Log "npm متوفر: $npmVersion" "SUCCESS"
        } else {
            throw "npm غير متوفر"
        }
    } catch {
        Write-Log "npm غير متوفر" "ERROR"
        return $false
    }
    
    # فحص مجلدات المشروع
    if (-not (Test-Path $PROJECT_PATH)) {
        Write-Log "مجلد المشروع غير موجود: $PROJECT_PATH" "ERROR"
        return $false
    }
    
    if (-not (Test-Path $FRONTEND_PATH)) {
        Write-Log "مجلد Frontend غير موجود: $FRONTEND_PATH" "ERROR"
        return $false
    }
    
    if (-not (Test-Path $BACKEND_PATH)) {
        Write-Log "مجلد Backend غير موجود: $BACKEND_PATH" "ERROR"
        return $false
    }
    
    Write-Log "جميع المتطلبات متوفرة ✅" "SUCCESS"
    return $true
}

# دالة تثبيت التبعيات
function Install-Dependencies {
    Write-Log "تثبيت تبعيات النظام..." "INFO"
    
    # تثبيت تبعيات Frontend
    Write-Log "تثبيت تبعيات Frontend..." "INFO"
    Set-Location $FRONTEND_PATH
    
    if (-not (Test-Path "node_modules")) {
        Write-Log "تثبيت حزم Frontend..." "INFO"
        npm install --silent 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "تم تثبيت تبعيات Frontend بنجاح" "SUCCESS"
        } else {
            Write-Log "فشل في تثبيت تبعيات Frontend" "ERROR"
            return $false
        }
    } else {
        Write-Log "تبعيات Frontend مثبتة مسبقاً" "SUCCESS"
    }
    
    # تثبيت تبعيات Backend
    Write-Log "تثبيت تبعيات Backend..." "INFO"
    Set-Location $BACKEND_PATH
    
    if (-not (Test-Path "node_modules")) {
        Write-Log "تثبيت حزم Backend..." "INFO"
        npm install --silent 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "تم تثبيت تبعيات Backend بنجاح" "SUCCESS"
        } else {
            Write-Log "فشل في تثبيت تبعيات Backend" "ERROR"
            return $false
        }
    } else {
        Write-Log "تبعيات Backend مثبتة مسبقاً" "SUCCESS"
    }
    
    return $true
}

# دالة إيقاف العمليات السابقة
function Stop-PreviousProcesses {
    Write-Log "إيقاف العمليات السابقة..." "INFO"
    
    # قراءة ملف PIDs إذا كان موجوداً
    if (Test-Path $PID_FILE) {
        $pids = Get-Content $PID_FILE
        foreach ($pid in $pids) {
            if ($pid -and $pid -ne "") {
                try {
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process) {
                        Stop-Process -Id $pid -Force
                        Write-Log "تم إيقاف العملية: $pid" "SUCCESS"
                    }
                } catch {
                    Write-Log "لا يمكن إيقاف العملية: $pid" "WARNING"
                }
            }
        }
        Remove-Item $PID_FILE -Force
    }
    
    # إيقاف عمليات Node.js التي تستخدم المنافذ المطلوبة
    $ports = @(3001, 5000)
    foreach ($port in $ports) {
        try {
            $processes = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue | 
                        Select-Object -ExpandProperty OwningProcess
            foreach ($processId in $processes) {
                $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                if ($process -and $process.ProcessName -eq "node") {
                    Stop-Process -Id $processId -Force
                    Write-Log "تم إيقاف عملية Node.js على المنفذ $port" "SUCCESS"
                }
            }
        } catch {
            # تجاهل الأخطاء
        }
    }
    
    Start-Sleep -Seconds 2
}

# دالة تشغيل Backend
function Start-Backend {
    Write-Log "تشغيل Backend Server..." "INFO"
    Set-Location $BACKEND_PATH
    
    # إنشاء ملف package.json إذا لم يكن موجوداً
    if (-not (Test-Path "package.json")) {
        Write-Log "إنشاء ملف package.json للـ Backend..." "INFO"
        $packageJson = @{
            name = "sharau-backend"
            version = "1.0.0"
            description = "Sharau Travel System Backend"
            main = "server.js"
            scripts = @{
                start = "node server.js"
                dev = "nodemon server.js"
            }
            dependencies = @{
                express = "^4.18.2"
                cors = "^2.8.5"
                dotenv = "^16.3.1"
                mongoose = "^7.5.0"
                bcryptjs = "^2.4.3"
                jsonwebtoken = "^9.0.2"
                multer = "^1.4.5"
                nodemailer = "^6.9.4"
            }
            devDependencies = @{
                nodemon = "^3.0.1"
            }
        } | ConvertTo-Json -Depth 3
        
        Set-Content -Path "package.json" -Value $packageJson -Encoding UTF8
    }
    
    # إنشاء ملف server.js إذا لم يكن موجوداً
    if (-not (Test-Path "server.js")) {
        Write-Log "إنشاء ملف server.js..." "INFO"
        $serverJs = @"
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
    res.json({ 
        message: 'Sharau Travel System Backend is running!',
        version: '1.0.0',
        status: 'active'
    });
});

// API Routes
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Backend Server running on port ${PORT}`);
    console.log(`📡 API available at http://localhost:${PORT}`);
});
"@
        Set-Content -Path "server.js" -Value $serverJs -Encoding UTF8
    }
    
    # تشغيل Backend
    $backendProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -PassThru -WindowStyle Hidden
    
    if ($backendProcess) {
        Write-Log "تم تشغيل Backend بنجاح - PID: $($backendProcess.Id)" "SUCCESS"
        Add-Content -Path $PID_FILE -Value $backendProcess.Id
        
        # انتظار تشغيل الخادم
        Start-Sleep -Seconds 3
        
        # فحص حالة الخادم
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -TimeoutSec 5
            Write-Log "Backend Server جاهز ويعمل بشكل صحيح" "SUCCESS"
            return $true
        } catch {
            Write-Log "Backend Server لا يستجيب" "WARNING"
            return $true # نكمل حتى لو لم يستجب
        }
    } else {
        Write-Log "فشل في تشغيل Backend" "ERROR"
        return $false
    }
}

# دالة تشغيل Frontend
function Start-Frontend {
    Write-Log "تشغيل Frontend Application..." "INFO"
    Set-Location $FRONTEND_PATH
    
    # فحص ملف package.json
    if (-not (Test-Path "package.json")) {
        Write-Log "ملف package.json غير موجود في Frontend" "ERROR"
        return $false
    }
    
    # تشغيل Frontend
    $env:BROWSER = "none"  # منع فتح المتصفح تلقائياً
    $frontendProcess = Start-Process -FilePath "npm" -ArgumentList "start" -PassThru -WindowStyle Hidden
    
    if ($frontendProcess) {
        Write-Log "تم تشغيل Frontend بنجاح - PID: $($frontendProcess.Id)" "SUCCESS"
        Add-Content -Path $PID_FILE -Value $frontendProcess.Id
        
        # انتظار تشغيل التطبيق
        Write-Log "انتظار تحميل التطبيق..." "INFO"
        $maxAttempts = 30
        $attempt = 0
        
        do {
            Start-Sleep -Seconds 2
            $attempt++
            Write-Progress -Activity "تحميل التطبيق" -Status "المحاولة $attempt من $maxAttempts" -PercentComplete (($attempt / $maxAttempts) * 100)
            
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 3 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-Progress -Completed -Activity "تحميل التطبيق"
                    Write-Log "Frontend Application جاهز ويعمل بشكل صحيح" "SUCCESS"
                    return $true
                }
            } catch {
                # استمرار المحاولة
            }
        } while ($attempt -lt $maxAttempts)
        
        Write-Progress -Completed -Activity "تحميل التطبيق"
        Write-Log "Frontend Application قيد التحميل..." "WARNING"
        return $true
    } else {
        Write-Log "فشل في تشغيل Frontend" "ERROR"
        return $false
    }
}

# دالة فتح المتصفح
function Open-Browser {
    Write-Log "فتح التطبيق في المتصفح..." "INFO"
    
    $url = "http://localhost:3001"
    
    # محاولة فتح المتصفح
    try {
        Start-Process $url
        Write-Log "تم فتح التطبيق في المتصفح" "SUCCESS"
    } catch {
        Write-Log "لا يمكن فتح المتصفح تلقائياً" "WARNING"
        Write-Host ""
        Write-Host "🌐 افتح المتصفح يدوياً واذهب إلى:" -ForegroundColor Cyan
        Write-Host "   $url" -ForegroundColor Yellow
        Write-Host ""
    }
}

# دالة عرض معلومات النظام
function Show-SystemInfo {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
    Write-Host "║                    🎉 النظام جاهز للاستخدام! 🎉                ║" -ForegroundColor Green
    Write-Host "╠══════════════════════════════════════════════════════════════════╣" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "║  🌐 Frontend:  http://localhost:3001                            ║" -ForegroundColor Green
    Write-Host "║  📡 Backend:   http://localhost:5000                            ║" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "║  📄 القوالب المذهلة:    /templates                             ║" -ForegroundColor Green
    Write-Host "║  💰 السندات المتطورة:   /vouchers                              ║" -ForegroundColor Green
    Write-Host "║  📊 التقارير التحليلية: /reports                               ║" -ForegroundColor Green
    Write-Host "║  🎯 لوحة التحكم:       /dashboard                              ║" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "║  📝 السجلات: $LOG_FILE" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "╚══════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
    Write-Host ""
    Write-Host "💡 نصائح للاستخدام:" -ForegroundColor Cyan
    Write-Host "   • استخدم Ctrl+C لإيقاف النظام" -ForegroundColor White
    Write-Host "   • تحقق من السجلات في حالة وجود مشاكل" -ForegroundColor White
    Write-Host "   • النظام يعمل في الخلفية حتى إغلاق هذه النافذة" -ForegroundColor White
    Write-Host ""
}

# دالة مراقبة النظام
function Monitor-System {
    Write-Log "بدء مراقبة النظام..." "INFO"
    
    try {
        while ($true) {
            Start-Sleep -Seconds 30
            
            # فحص Frontend
            try {
                $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3001" -TimeoutSec 5 -UseBasicParsing
                if ($frontendResponse.StatusCode -ne 200) {
                    Write-Log "Frontend لا يستجيب بشكل صحيح" "WARNING"
                }
            } catch {
                Write-Log "Frontend غير متاح" "ERROR"
            }
            
            # فحص Backend
            try {
                $backendResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -TimeoutSec 5
                if (-not $backendResponse.status) {
                    Write-Log "Backend لا يستجيب بشكل صحيح" "WARNING"
                }
            } catch {
                Write-Log "Backend غير متاح" "WARNING"
            }
        }
    } catch {
        Write-Log "تم إيقاف مراقبة النظام" "INFO"
    }
}

# دالة التنظيف عند الإغلاق
function Cleanup {
    Write-Log "تنظيف النظام..." "INFO"
    
    if (Test-Path $PID_FILE) {
        $pids = Get-Content $PID_FILE
        foreach ($pid in $pids) {
            if ($pid -and $pid -ne "") {
                try {
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process) {
                        Stop-Process -Id $pid -Force
                        Write-Log "تم إيقاف العملية: $pid" "SUCCESS"
                    }
                } catch {
                    # تجاهل الأخطاء
                }
            }
        }
        Remove-Item $PID_FILE -Force
    }
    
    Write-Log "تم تنظيف النظام بنجاح" "SUCCESS"
}

# معالج إشارة الإغلاق
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Cleanup
}

# ===================================================================
# 🚀 تشغيل النظام الرئيسي
# ===================================================================

try {
    # تنظيف ملف السجل
    if (Test-Path $LOG_FILE) {
        Remove-Item $LOG_FILE -Force
    }
    
    Write-Log "بدء تشغيل نظام شراء للسفر والسياحة" "INFO"
    
    # فحص المتطلبات
    if (-not (Test-Requirements)) {
        Write-Log "فشل في فحص المتطلبات" "ERROR"
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # إيقاف العمليات السابقة
    Stop-PreviousProcesses
    
    # تثبيت التبعيات
    if (-not (Install-Dependencies)) {
        Write-Log "فشل في تثبيت التبعيات" "ERROR"
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # تشغيل Backend
    if (-not (Start-Backend)) {
        Write-Log "فشل في تشغيل Backend" "ERROR"
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # تشغيل Frontend
    if (-not (Start-Frontend)) {
        Write-Log "فشل في تشغيل Frontend" "ERROR"
        Cleanup
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # فتح المتصفح
    Open-Browser
    
    # عرض معلومات النظام
    Show-SystemInfo
    
    # بدء مراقبة النظام
    Monitor-System
    
} catch {
    Write-Log "خطأ في تشغيل النظام: $($_.Exception.Message)" "ERROR"
    Cleanup
    Read-Host "اضغط Enter للخروج"
    exit 1
} finally {
    Cleanup
}