/* Accounts Page Styles */

.accounts-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* Page Header */
.page-header {
  background: white;
  border-radius: 15px;
  padding: 25px 30px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
  font-weight: 400;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.period-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 10px;
  border: 2px solid #e1e8ed;
  font-size: 14px;
  font-weight: 500;
}

.period-selector label {
  color: #495057;
  font-weight: 600;
  white-space: nowrap;
}

.period-selector input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  transition: border-color 0.3s ease;
}

.period-selector input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.period-selector span {
  color: #6c757d;
  font-weight: 500;
}

/* Accounts Container */
.accounts-container {
  display: flex;
  gap: 25px;
  min-height: calc(100vh - 200px);
}

/* Accounts Sidebar */
.accounts-sidebar {
  width: 280px;
  background: white;
  border-radius: 15px;
  padding: 25px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.accounts-tabs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 20px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  text-align: right;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
  transform: translateX(-3px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transform: translateX(-3px);
}

.tab-btn.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
  border-radius: 0 2px 2px 0;
}

.tab-icon {
  font-size: 18px;
  min-width: 20px;
  text-align: center;
}

/* Accounts Content */
.accounts-content {
  flex: 1;
  background: transparent;
  border-radius: 15px;
  overflow: hidden;
}

/* Quick Stats Cards */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 32px;
  margin-bottom: 10px;
  opacity: 0.8;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: white;
  color: #495057;
  border: 2px solid #e1e8ed;
}

.action-btn.secondary:hover {
  background: #f8f9fa;
  border-color: #ced4da;
  transform: translateY(-1px);
}

.action-btn.success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.action-btn.success:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.action-btn.warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
}

.action-btn.warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #6c757d;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-item:not(:last-child)::after {
  content: '←';
  color: #ced4da;
  font-weight: 600;
}

.breadcrumb-item.active {
  color: #495057;
  font-weight: 600;
}

/* Search and Filter Bar */
.search-filter-bar {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 250px;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

/* Data Table */
.data-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  font-weight: 600;
  font-size: 14px;
  display: grid;
  gap: 15px;
  align-items: center;
}

.table-row {
  padding: 15px 20px;
  border-bottom: 1px solid #f1f3f4;
  display: grid;
  gap: 15px;
  align-items: center;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 80px;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.posted {
  background: #d1ecf1;
  color: #0c5460;
}

.status-badge.draft {
  background: #e2e3e5;
  color: #383d41;
}

/* Currency Display */
.currency {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  text-align: right;
  direction: ltr;
}

.currency.positive {
  color: #28a745;
}

.currency.negative {
  color: #dc3545;
}

.currency.zero {
  color: #6c757d;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 10;
}

.loading-content {
  text-align: center;
  color: #495057;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 20px;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 25px 0;
  font-size: 14px;
  line-height: 1.5;
}

.empty-state-action {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.empty-state-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .accounts-container {
    flex-direction: column;
  }
  
  .accounts-sidebar {
    width: 100%;
    position: static;
  }
  
  .accounts-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
  }
  
  .quick-stats {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .accounts-page {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .header-controls {
    justify-content: center;
  }
  
  .period-selector {
    flex-direction: column;
    gap: 8px;
  }
  
  .accounts-tabs {
    grid-template-columns: 1fr;
  }
  
  .tab-btn {
    justify-content: center;
  }
  
  .quick-stats {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .search-filter-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input,
  .filter-select {
    min-width: auto;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .table-header > div,
  .table-row > div {
    padding: 8px;
    border-bottom: 1px solid #e1e8ed;
  }
  
  .table-header > div:last-child,
  .table-row > div:last-child {
    border-bottom: none;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .header-content p {
    font-size: 14px;
  }
  
  .accounts-sidebar {
    padding: 20px 0;
  }
  
  .tab-btn {
    padding: 12px 15px;
    font-size: 13px;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .action-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .search-filter-bar {
    padding: 15px;
  }
  
  .data-table {
    font-size: 13px;
  }
  
  .table-header,
  .table-row {
    padding: 12px 15px;
  }
}

/* Print Styles */
@media print {
  .accounts-page {
    background: white;
    padding: 0;
  }
  
  .page-header {
    box-shadow: none;
    border-bottom: 2px solid #000;
    margin-bottom: 20px;
  }
  
  .header-controls,
  .accounts-sidebar,
  .action-buttons,
  .search-filter-bar {
    display: none !important;
  }
  
  .accounts-container {
    flex-direction: column;
    gap: 0;
  }
  
  .accounts-content {
    background: white;
  }
  
  .data-table {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .table-header {
    background: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .quick-stats {
    display: none;
  }
  
  .stat-card {
    break-inside: avoid;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .accounts-page {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: #e2e8f0;
  }
  
  .page-header,
  .accounts-sidebar,
  .stat-card,
  .data-table,
  .search-filter-bar {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  
  .header-content h1 {
    color: #e2e8f0;
  }
  
  .tab-btn {
    color: #a0aec0;
  }
  
  .tab-btn:hover {
    background: #4a5568;
    color: #e2e8f0;
  }
  
  .search-input,
  .filter-select {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  
  .table-row:hover {
    background: #4a5568;
  }
}