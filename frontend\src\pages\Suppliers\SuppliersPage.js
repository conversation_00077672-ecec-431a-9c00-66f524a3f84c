import React, { useState, useEffect } from 'react';
import suppliersService from '../../services/SuppliersService';
import './SuppliersPage.css';

const SuppliersPage = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // تحميل الموردين من الخدمة المشتركة
  const [suppliersData, setSuppliersData] = useState([]);

  // تحميل البيانات من الخدمة
  useEffect(() => {
    const loadSuppliers = () => {
      const suppliers = suppliersService.getAllSuppliers();
      setSuppliersData(suppliers);
    };

    loadSuppliers();

    // إضافة مستمع للتحديثات المباشرة
    const handleSuppliersUpdate = (updatedSuppliers) => {
      setSuppliersData(updatedSuppliers);
    };

    suppliersService.addListener(handleSuppliersUpdate);

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      suppliersService.removeListener(handleSuppliersUpdate);
    };
  }, []);

  const [newSupplier, setNewSupplier] = useState({
    name: '',
    type: 'airline',
    contact: '',
    phone: '',
    email: '',
    address: '',
    services: [],
    status: 'active',
    website: '',
    taxNumber: '',
    bankAccount: '',
    paymentTerms: '30',
    notes: ''
  });

  useEffect(() => {
    filterSuppliers();
  }, [searchTerm, filterType, suppliersData]);

  const filterSuppliers = () => {
    let filtered = suppliersData;

    if (searchTerm) {
      filtered = filtered.filter(supplier =>
        supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(supplier => supplier.type === filterType);
    }

    setSuppliers(filtered);
  };

  const handleAddSupplier = () => {
    const supplier = suppliersService.addSupplier(newSupplier);
    setSuppliersData(suppliersService.getAllSuppliers());
    setNewSupplier({
      name: '',
      type: 'airline',
      contact: '',
      phone: '',
      email: '',
      address: '',
      services: [],
      status: 'active',
      website: '',
      taxNumber: '',
      bankAccount: '',
      paymentTerms: '30',
      notes: ''
    });
    setShowAddModal(false);
  };

  const handleEditSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setNewSupplier(supplier);
    setShowEditModal(true);
  };

  const handleUpdateSupplier = () => {
    setSuppliersData(suppliersData.map(supplier =>
      supplier.id === selectedSupplier.id ? newSupplier : supplier
    ));
    setShowEditModal(false);
    setSelectedSupplier(null);
    setNewSupplier({
      name: '',
      type: 'airline',
      contact: '',
      phone: '',
      email: '',
      address: '',
      services: [],
      status: 'active',
      website: '',
      taxNumber: '',
      bankAccount: '',
      paymentTerms: '30',
      notes: ''
    });
  };

  const handleDeleteSupplier = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      setSuppliersData(suppliersData.filter(supplier => supplier.id !== id));
    }
  };

  const getTypeLabel = (type) => {
    const types = {
      airline: 'شركة طيران',
      hotel: 'فندق',
      transport: 'نقل',
      tour: 'رحلات سياحية',
      visa: 'خدمات تأشيرات'
    };
    return types[type] || type;
  };

  const getStatusBadge = (status) => {
    return status === 'active' ? 
      <span className="status-badge active">نشط</span> : 
      <span className="status-badge inactive">غير نشط</span>;
  };

  const renderStars = (rating) => {
    return '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
  };

  return (
    <div className="suppliers-page">
      <div className="page-header">
        <h1>إدارة الموردين</h1>
        <button 
          className="add-btn"
          onClick={() => setShowAddModal(true)}
        >
          + إضافة مورد جديد
        </button>
      </div>

      <div className="filters-section">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في الموردين..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="filter-dropdown">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            <option value="all">جميع الأنواع</option>
            <option value="airline">شركات الطيران</option>
            <option value="hotel">الفنادق</option>
            <option value="transport">النقل</option>
            <option value="tour">الرحلات السياحية</option>
            <option value="visa">خدمات التأشيرات</option>
          </select>
        </div>
      </div>

      <div className="suppliers-grid">
        {suppliers.map(supplier => (
          <div key={supplier.id} className="supplier-card">
            <div className="card-header">
              <h3>{supplier.name}</h3>
              {getStatusBadge(supplier.status)}
            </div>
            
            <div className="card-body">
              <div className="supplier-info">
                <p><strong>النوع:</strong> {getTypeLabel(supplier.type)}</p>
                <p><strong>جهة الاتصال:</strong> {supplier.contact}</p>
                <p><strong>الهاتف:</strong> {supplier.phone}</p>
                <p><strong>البريد الإلكتروني:</strong> {supplier.email}</p>
                <p><strong>العنوان:</strong> {supplier.address}</p>
              </div>
              
              <div className="supplier-services">
                <strong>الخدمات:</strong>
                <div className="services-tags">
                  {supplier.services.map((service, index) => (
                    <span key={index} className="service-tag">{service}</span>
                  ))}
                </div>
              </div>
              
              <div className="supplier-stats">
                <div className="stat">
                  <span className="stat-label">التقييم:</span>
                  <span className="rating">{renderStars(supplier.rating)} ({supplier.rating})</span>
                </div>
                <div className="stat">
                  <span className="stat-label">إجمالي الحجوزات:</span>
                  <span className="stat-value">{supplier.totalBookings}</span>
                </div>
                <div className="stat">
                  <span className="stat-label">آخر حجز:</span>
                  <span className="stat-value">{supplier.lastBooking || 'لا يوجد'}</span>
                </div>
              </div>
            </div>
            
            <div className="card-actions">
              <button 
                className="edit-btn"
                onClick={() => handleEditSupplier(supplier)}
              >
                تعديل
              </button>
              <button 
                className="delete-btn"
                onClick={() => handleDeleteSupplier(supplier.id)}
              >
                حذف
              </button>
            </div>
          </div>
        ))}
      </div>

      {suppliers.length === 0 && (
        <div className="no-results">
          <p>لا توجد موردين مطابقين لمعايير البحث</p>
        </div>
      )}

      {/* نافذة إضافة مورد جديد */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>إضافة مورد جديد</h2>
              <button 
                className="close-btn"
                onClick={() => setShowAddModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>اسم المورد</label>
                <input
                  type="text"
                  value={newSupplier.name}
                  onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>نوع المورد</label>
                <select
                  value={newSupplier.type}
                  onChange={(e) => setNewSupplier({...newSupplier, type: e.target.value})}
                >
                  <option value="airline">شركة طيران</option>
                  <option value="hotel">فندق</option>
                  <option value="transport">نقل</option>
                  <option value="tour">رحلات سياحية</option>
                  <option value="visa">خدمات تأشيرات</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>جهة الاتصال</label>
                <input
                  type="text"
                  value={newSupplier.contact}
                  onChange={(e) => setNewSupplier({...newSupplier, contact: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>رقم الهاتف</label>
                <input
                  type="text"
                  value={newSupplier.phone}
                  onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  value={newSupplier.email}
                  onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>العنوان</label>
                <textarea
                  value={newSupplier.address}
                  onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>الحالة</label>
                <select
                  value={newSupplier.status}
                  onChange={(e) => setNewSupplier({...newSupplier, status: e.target.value})}
                >
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                </select>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="cancel-btn"
                onClick={() => setShowAddModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="save-btn"
                onClick={handleAddSupplier}
              >
                حفظ
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل المورد */}
      {showEditModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>تعديل المورد</h2>
              <button 
                className="close-btn"
                onClick={() => setShowEditModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>اسم المورد</label>
                <input
                  type="text"
                  value={newSupplier.name}
                  onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>نوع المورد</label>
                <select
                  value={newSupplier.type}
                  onChange={(e) => setNewSupplier({...newSupplier, type: e.target.value})}
                >
                  <option value="airline">شركة طيران</option>
                  <option value="hotel">فندق</option>
                  <option value="transport">نقل</option>
                  <option value="tour">رحلات سياحية</option>
                  <option value="visa">خدمات تأشيرات</option>
                </select>
              </div>
              
              <div className="form-group">
                <label>جهة الاتصال</label>
                <input
                  type="text"
                  value={newSupplier.contact}
                  onChange={(e) => setNewSupplier({...newSupplier, contact: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>رقم الهاتف</label>
                <input
                  type="text"
                  value={newSupplier.phone}
                  onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  value={newSupplier.email}
                  onChange={(e) => setNewSupplier({...newSupplier, email: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>العنوان</label>
                <textarea
                  value={newSupplier.address}
                  onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                />
              </div>
              
              <div className="form-group">
                <label>الحالة</label>
                <select
                  value={newSupplier.status}
                  onChange={(e) => setNewSupplier({...newSupplier, status: e.target.value})}
                >
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                </select>
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                className="cancel-btn"
                onClick={() => setShowEditModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="save-btn"
                onClick={handleUpdateSupplier}
              >
                تحديث
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SuppliersPage;