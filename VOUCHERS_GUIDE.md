# دليل سندات القبض وسندات الصرف
# Receipt & Payment Vouchers Guide

## المكونات المُضافة ✅

### 1. سندات القبض (Receipt Vouchers)
**المسار:** المالية → سندات القبض
**الملفات:**
- `frontend/src/components/Finance/ReceiptVouchers.js`
- `frontend/src/components/Finance/ReceiptVouchers.css`

**المميزات:**
- ✅ إنشاء سندات قبض جديدة
- ✅ تعديل السندات الموجودة
- ✅ إلغاء السندات
- ✅ البحث والفلترة المتقدمة
- ✅ إحصائيات شاملة
- ✅ طباعة السندات
- ✅ تصميم متجاوب

### 2. سندات الصرف (Payment Vouchers)
**المسار:** المالية → سندات الصرف
**الملفات:**
- `frontend/src/components/Finance/PaymentVouchers.js`
- `frontend/src/components/Finance/PaymentVouchers.css`

**المميزات:**
- ✅ إنشاء سندات صرف جديدة
- ✅ تعديل السندات الموجودة
- ✅ إلغاء السندات
- ✅ نظام الاعتماد
- ✅ البحث والفلترة المتقدمة
- ✅ إحصائيات شاملة
- ✅ طباعة السندات
- ✅ تصميم متجاوب

## كيفية الوصول 🎯

### الطريقة الأولى - من القائمة الرئيسية:
1. سجل الدخول إلى النظام
2. اذهب إلى قسم "المالية" من القائمة الجانبية
3. ستجد تبويبين جديدين:
   - **💰 سندات القبض**
   - **💸 سندات الصرف**

### الطريقة الثانية - الرابط المباشر:
- سندات القبض: `http://localhost:3001/finance` ثم اختر تبويب "سندات القبض"
- سندات الصرف: `http://localhost:3001/finance` ثم اختر تبويب "سندات الصرف"

## المميزات الرئيسية 🚀

### سندات القبض:
#### 📊 الإحصائيات:
- إجمالي المبالغ المستلمة
- عدد السندات النشطة
- عدد السندات الملغية
- إجمالي السندات

#### 📝 إدارة السندات:
- **إنشاء سند جديد:** رقم تلقائي، تاريخ، عميل، مبلغ، طريقة دفع
- **طرق الدفع:** نقدي، تحويل بنكي، بطاقة ائتمان، شيك
- **الحسابات:** الصندوق، البنوك المختلفة
- **المرجع:** ربط بالفواتير أو الحجوزات

#### 🔍 البحث والفلترة:
- البحث بالاسم أو رقم السند
- فلترة حسب الحالة (نشط/ملغي)
- فلترة حسب التاريخ (من - إلى)
- عرض النتائج في جدول منظم

### سندات الصرف:
#### 📊 الإحصائيات:
- إجمالي المبالغ المصروفة
- عدد السندات النشطة
- عدد السندات الملغية
- إجمالي السندات

#### 📝 إدارة السندات:
- **إنشاء سند جديد:** رقم تلقائي، تاريخ، مستفيد، مبلغ، طريقة دفع
- **نظام الاعتماد:** تسجيل من اعتمد السند
- **طرق الدفع:** نقدي، تحويل بنكي، بطاقة ائتمان، شيك
- **الحسابات:** الصندوق، البنوك المختلفة
- **المرجع:** ربط بالفواتير أو الطلبات

#### 🔍 البحث والفلترة:
- البحث بالاسم أو رقم السند
- فلترة حسب الحالة (نشط/ملغي)
- فلترة حسب التاريخ (من - إلى)
- عرض النتائج في جدول منظم

## البيانات التجريبية 📋

### سندات القبض:
```
RV-2024-001 | أحمد محمد العلي | 5,000 ر.س | نقدي | دفعة مقدمة لحجز عمرة
RV-2024-002 | فاطمة سالم | 3,500 ر.س | تحويل بنكي | سداد فاتورة رحلة حج
RV-2024-003 | عبدالله خالد | 2,800 ر.س | بطاقة ائتمان | دفع تذاكر طيران (ملغي)
RV-2024-004 | نورا عبدالرحمن | 7,200 ر.س | شيك | سداد باقة سياحية كاملة
```

### سندات الصرف:
```
PV-2024-001 | شركة الطيران السعودي | 8,500 ر.س | تحويل بنكي | دفع تذاكر طيران للعملاء
PV-2024-002 | فندق الحرمين | 12,000 ر.س | شيك | دفع حجوزات فندقية
PV-2024-003 | شركة النقل المتميز | 3,500 ر.س | نقدي | أجرة نقل المعتمرين (ملغي)
PV-2024-004 | مكتب خدمات العمرة | 5,800 ر.س | بطاقة ائتمان | رسوم خدمات العمرة
PV-2024-005 | مؤسسة الخدمات اللوجستية | 2,200 ر.س | تحويل بنكي | خدمات شحن ولوجستية
```

## كيفية الاختبار 🧪

### 1. اختبار سندات القبض

#### إنشاء سند جديد:
1. اذهب إلى "المالية" → "سندات القبض"
2. اضغط "سند قبض جديد"
3. املأ البيانات:
   - **العميل:** اسم العميل
   - **المبلغ:** مبلغ رقمي
   - **طريقة الدفع:** اختر من القائمة
   - **البيان:** وصف السبب
   - **المرجع:** رقم الفاتورة (اختياري)
4. اضغط "حفظ"
5. **النتيجة المتوقعة:** ظهور رسالة نجاح وإضافة السند للقائمة

#### تعديل سند موجود:
1. في قائمة السندات، اضغط أيقونة التعديل ✏️
2. غيّر البيانات المطلوبة
3. اضغط "تحديث"
4. **النتيجة المتوقعة:** تحديث البيانات وظهور رسالة نجاح

#### البحث والفلترة:
1. استخدم مربع البحث للبحث بالاسم
2. اختر حالة معينة من قائمة الحالة
3. حدد نطاق تاريخ
4. **النتيجة المتوقعة:** تحديث القائمة حسب المعايير

### 2. اختبار سندات الصرف

#### إنشاء سند جديد:
1. اذهب إلى "المالية" → "سندات الصرف"
2. اضغط "سند صرف جديد"
3. املأ البيانات:
   - **المستفيد:** اسم المستفيد
   - **المبلغ:** مبلغ رقمي
   - **طريقة الدفع:** اختر من القائمة
   - **البيان:** وصف السبب
   - **معتمد من:** اسم المعتمد
4. اضغط "حفظ"
5. **النتيجة المتوقعة:** ظهور رسالة نجاح وإضافة السند للقائمة

#### إلغاء سند:
1. في قائمة السندات، اضغط أيقونة الإلغاء ❌
2. أكد الإلغاء
3. **النتيجة المتوقعة:** تغيير حالة السند إلى "ملغي"

### 3. اختبار الطباعة
1. في أي سند، اضغط أيقونة الطباعة 🖨️
2. **النتيجة المتوقعة:** فتح نافذة الطباعة

### 4. اختبار الاستجابة
1. اختبر الصفحات على أحجام شاشة مختلفة
2. **النتيجة المتوقعة:** تكيف التصميم مع الشاشة

## الحسابات المتاحة 🏦

### حسابات الصندوق والبنوك:
```
1110 - الصندوق
1120 - البنك الأهلي
1121 - بنك الراجحي
1122 - بنك الرياض
```

### طرق الدفع:
- **نقدي:** للمعاملات النقدية
- **تحويل بنكي:** للتحويلات البنكية
- **بطاقة ائتمان:** للدفع بالبطاقات
- **شيك:** للدفع بالشيكات

## المشاكل المحتملة وحلولها 🔧

### المشكلة 1: التبويبات لا تظهر
**السبب:** خطأ في التحديث أو عدم تحميل المكونات
**الحل:**
1. تأكد من حفظ جميع الملفات
2. أعد تشغيل الخادم
3. امسح cache المتصفح

### المشكلة 2: النموذج لا يحفظ
**السبب:** خطأ في التحقق من البيانات
**الحل:**
1. تأكد من ملء الحقول المطلوبة (*)
2. تحقق من صحة المبلغ (رقم موجب)
3. تحقق من Console للأخطاء

### المشكلة 3: البحث لا يعمل
**السبب:** خطأ في دالة الفلترة
**الحل:**
1. تحقق من Console للأخطاء
2. جرب إعادة تحميل الصفحة
3. امسح مربع البحث وجرب مرة أخرى

### المشكلة 4: التصميم لا يظهر بشكل صحيح
**السبب:** ملفات CSS لم تحمل
**الحل:**
1. تأكد من استيراد ملفات CSS
2. امسح cache المتصفح (Ctrl+F5)
3. تحقق من Network tab في Developer Tools

## التطوير المستقبلي 🚀

### تحسينات مقترحة:
- [ ] ربط بقاعدة بيانات حقيقية
- [ ] تصدير السندات إلى PDF
- [ ] إرسال السندات بالبريد الإلكتروني
- [ ] نظام موافقات متعدد المستويات
- [ ] تقارير مالية متقدمة
- [ ] ربط مع النظام المحاسبي
- [ ] نظام أرشفة السندات
- [ ] تنبيهات للسندات المعلقة

### تكامل مع أنظمة أخرى:
- [ ] ربط مع نظام الحجوزات
- [ ] ربط مع نظام الفواتير
- [ ] ربط مع نظام العملاء
- [ ] ربط مع النظام المصرفي
- [ ] تكامل مع أنظمة الدفع الإلكتروني

## الملفات المُحدثة 📁

```
frontend/src/
├── components/
│   └── Finance/
│       ├── ReceiptVouchers.js      ← جديد
│       ├── ReceiptVouchers.css     ← جديد
│       ├── PaymentVouchers.js      ← جديد
│       └── PaymentVouchers.css     ← جديد
├── pages/
│   └── Finance/
│       └── FinancePage.js          ← محدث (إضافة التبويبات)
└── ...
```

---

**الحالة:** ✅ مكتمل وجاهز للاختبار  
**التاريخ:** اليوم  
**المطور:** AI Assistant

**ملاحظة:** جميع المكونات تعمل بالبيانات التجريبية. للإنتاج، يجب ربط النظام بقاعدة بيانات حقيقية وAPI للمعاملات المالية.