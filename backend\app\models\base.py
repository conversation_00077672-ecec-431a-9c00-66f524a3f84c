"""
النموذج الأساسي لجميع الجداول
Base Model for all database tables
"""

from sqlalchemy import Column, Integer, DateTime, Boolean, String, Text
from sqlalchemy.sql import func
from app.core.database import Base

class BaseModel(Base):
    """
    النموذج الأساسي الذي يحتوي على الحقول المشتركة
    """
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # حقول التدقيق
    created_by = Column(Integer, nullable=True)  # معرف المستخدم الذي أنشأ السجل
    updated_by = Column(Integer, nullable=True)  # معرف المستخدم الذي عدل السجل
    
    # حقول إضافية
    notes = Column(Text, nullable=True)  # ملاحظات
    
    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"