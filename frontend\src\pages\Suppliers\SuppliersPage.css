.suppliers-page {
  padding: 20px;
  font-family: 'Cairo', Arial, sans-serif;
  direction: rtl;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.header-stats {
  display: flex;
  gap: 20px;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  min-width: 120px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.export-btn, .import-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.export-btn:hover, .import-btn:hover {
  background: #218838;
}

.import-btn {
  background: #17a2b8;
}

.import-btn:hover {
  background: #138496;
}

.page-header h1 {
  color: #2c3e50;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.add-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.filters-section {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  align-items: center;
}

.filter-group {
  display: flex;
  gap: 15px;
  align-items: center;
}

.sort-order-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background 0.3s ease;
}

.sort-order-btn:hover {
  background: #5a67d8;
}

.view-mode-toggle {
  display: flex;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
}

.view-mode-btn {
  background: white;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background: #667eea;
  color: white;
}

.view-mode-btn:hover {
  background: #f8f9fa;
}

.view-mode-btn.active:hover {
  background: #5a67d8;
}

.search-box {
  flex: 1;
}

.search-box input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
}

.filter-dropdown select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  cursor: pointer;
  min-width: 200px;
}

.suppliers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.supplier-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.supplier-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.card-badges {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-end;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.card-body {
  padding: 20px;
}

.supplier-info {
  margin-bottom: 20px;
}

.supplier-info p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
}

.supplier-info strong {
  color: #2c3e50;
  font-weight: 600;
}

.supplier-services {
  margin-bottom: 20px;
}

.supplier-services strong {
  color: #2c3e50;
  font-weight: 600;
  display: block;
  margin-bottom: 10px;
}

.services-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.service-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.supplier-stats {
  border-top: 1px solid #e1e8ed;
  padding-top: 15px;
}

.stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}

.rating {
  color: #ffc107;
  font-size: 14px;
}

.card-actions {
  padding: 15px 20px;
  background: #f8f9fa;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.view-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.view-btn:hover {
  background: #138496;
}

.edit-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.edit-btn:hover {
  background: #218838;
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.delete-btn:hover {
  background: #c82333;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.no-results p {
  color: #666;
  font-size: 18px;
  margin: 0;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255,255,255,0.2);
}

.modal-body {
  padding: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  padding: 20px 30px;
  background: #f8f9fa;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease;
}

.cancel-btn:hover {
  background: #5a6268;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .suppliers-page {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .filters-section {
    flex-direction: column;
  }
  
  .suppliers-grid {
    grid-template-columns: 1fr;
  }
  
  .modal {
    width: 95%;
    margin: 20px;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-footer {
    padding: 15px 20px;
    flex-direction: column;
  }
  
  .card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 24px;
  }
  
  .add-btn {
    width: 100%;
  }
  
  .supplier-card {
    margin-bottom: 15px;
  }
  
  .services-tags {
    flex-direction: column;
  }
  
  .service-tag {
    text-align: center;
  }
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin: 30px 0;
}

.pagination-btn {
  background: white;
  border: 2px solid #e1e8ed;
  color: #2c3e50;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.pagination-btn:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.pagination-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.pagination-btn:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  border-color: #e1e8ed;
}

/* Enhanced Modal Styles */
.modal.large {
  max-width: 900px;
  width: 95%;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.details-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.details-section.full-width {
  grid-column: 1 / -1;
}

.details-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 600;
  color: #2c3e50;
  min-width: 120px;
}

.detail-item span {
  color: #555;
  text-align: left;
}

.notes-text {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

/* Enhanced Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.form-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.form-section.full-width {
  grid-column: 1 / -1;
}

.form-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.form-group input:required:invalid {
  border-color: #dc3545;
}

.form-group input:required:valid {
  border-color: #28a745;
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.save-btn:disabled:hover {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

/* Service Tags Enhancements */
.service-tag.more {
  background: #6c757d;
  color: white;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .header-stats {
    flex-wrap: wrap;
  }
  
  .stat-card {
    min-width: 100px;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content {
    align-items: center;
    text-align: center;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .filter-group {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
  
  .pagination-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .stat-card {
    min-width: 80px;
    padding: 8px 10px;
  }
  
  .stat-number {
    font-size: 18px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .detail-item label {
    min-width: auto;
  }
}

/* Priority Badges */
.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.priority-badge.high {
  background: #dc3545;
  color: white;
}

.priority-badge.medium {
  background: #ffc107;
  color: #212529;
}

.priority-badge.low {
  background: #28a745;
  color: white;
}

/* Contract Status */
.contract-warning {
  background: #fff3cd;
  color: #856404;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.contract-expired {
  background: #f8d7da;
  color: #721c24;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.contract-warning-small, .contract-expired-small {
  margin-right: 5px;
  font-size: 12px;
}

/* Table Styles */
.suppliers-table-container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow-x: auto;
}

.suppliers-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.suppliers-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 10px;
  text-align: right;
  font-weight: 600;
  border-bottom: 2px solid #5a67d8;
}

.suppliers-table td {
  padding: 12px 10px;
  border-bottom: 1px solid #e1e8ed;
  vertical-align: middle;
}

.suppliers-table tr:hover {
  background: #f8f9fa;
}

.supplier-name {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.table-actions {
  display: flex;
  gap: 5px;
}

.table-actions .small {
  padding: 4px 8px;
  font-size: 12px;
}

/* Import Modal */
.import-section {
  text-align: center;
  padding: 20px;
}

.file-upload {
  border: 2px dashed #667eea;
  border-radius: 8px;
  padding: 40px 20px;
  margin: 20px 0;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload:hover {
  background: #e3f2fd;
  border-color: #5a67d8;
}

.file-upload input {
  display: none;
}

.file-upload p {
  margin: 10px 0 0 0;
  color: #666;
}

.import-template {
  margin-top: 20px;
}

.import-template h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.template-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  color: #2c3e50;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.template-btn:hover {
  background: #e3f2fd;
  border-color: #667eea;
  color: #667eea;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .header-actions {
    flex-wrap: wrap;
  }
  
  .suppliers-table {
    font-size: 12px;
  }
  
  .suppliers-table th,
  .suppliers-table td {
    padding: 8px 6px;
  }
}

@media (max-width: 768px) {
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .view-mode-toggle {
    order: -1;
    margin-bottom: 10px;
  }
  
  .suppliers-table-container {
    overflow-x: scroll;
  }
  
  .suppliers-table {
    min-width: 800px;
  }
  
  .card-badges {
    align-items: center;
  }
  
  .import-section {
    padding: 15px;
  }
  
  .file-upload {
    padding: 30px 15px;
  }
}

@media (max-width: 480px) {
  .header-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group select,
  .sort-order-btn {
    width: 100%;
  }
  
  .view-mode-toggle {
    width: 100%;
  }
  
  .view-mode-btn {
    flex: 1;
  }
  
  .template-btn {
    display: block;
    width: 100%;
    margin: 5px 0;
  }
}