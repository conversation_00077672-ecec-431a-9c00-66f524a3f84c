import React, { useState } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from './index';

// مكون اختبار للتأكد من عمل المكونات
const TestBookingComponents = () => {
  const [selectedBookings, setSelectedBookings] = useState([1, 2]);
  const [showModal, setShowModal] = useState(false);
  
  const testBooking = {
    id: 1,
    customerName: 'أحمد محمد',
    customerPhone: '+966501234567',
    bookingReference: 'TEST001',
    amount: 1500,
    currency: 'SAR',
    status: 'confirmed'
  };

  const handleTest = (action) => {
    console.log(`تم اختبار: ${action}`);
    alert(`تم اختبار: ${action}`);
  };

  return (
    <div style={{ padding: '20px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h2>اختبار مكونات الحجوزات</h2>
      
      <div style={{ marginBottom: '30px', background: 'white', padding: '20px', borderRadius: '10px' }}>
        <h3>مكون أزرار العمليات:</h3>
        <BookingActions
          booking={testBooking}
          onView={() => handleTest('عرض التفاصيل')}
          onEdit={() => handleTest('تعديل')}
          onDelete={() => handleTest('حذف')}
          onPrint={() => handleTest('طباعة')}
          onSavePDF={() => handleTest('حفظ PDF')}
        />
      </div>

      <div style={{ marginBottom: '30px', background: 'white', padding: '20px', borderRadius: '10px' }}>
        <h3>مكون العمليات المجمعة:</h3>
        <BulkActions
          selectedBookings={selectedBookings}
          onBulkPrint={() => handleTest('طباعة مجمعة')}
          onBulkSavePDF={() => handleTest('حفظ PDF مجمع')}
          onBulkDelete={() => handleTest('حذف مجمع')}
          onSelectAll={() => handleTest('تحديد الكل')}
          onClearSelection={() => handleTest('إلغاء التحديد')}
        />
      </div>

      <div style={{ marginBottom: '30px', background: 'white', padding: '20px', borderRadius: '10px' }}>
        <h3>نافذة عرض التفاصيل:</h3>
        <button 
          onClick={() => setShowModal(true)}
          style={{
            padding: '10px 20px',
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          فتح نافذة التفاصيل
        </button>
        
        <BookingDetailsModal
          booking={testBooking}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          onPrint={() => handleTest('طباعة من النافذة')}
          onSavePDF={() => handleTest('حفظ PDF من النافذة')}
        />
      </div>

      <div style={{ background: 'white', padding: '20px', borderRadius: '10px' }}>
        <h3>حالة الاختبار:</h3>
        <p>✅ تم تحميل جميع المكونات بنجاح</p>
        <p>✅ المكونات جاهزة للاستخدام</p>
        <p>✅ يمكن الآن استخدامها في صفحات الحجوزات</p>
      </div>
    </div>
  );
};

export default TestBookingComponents;