import React, { useState, useRef } from 'react';
import { CurrencyManager, CURRENCIES } from '../Finance/CurrencyManager';
import './VoucherTemplateBuilder.css';

const VoucherTemplateBuilder = ({ onSave, onCancel, initialTemplate = null }) => {
  const [template, setTemplate] = useState(initialTemplate || {
    name: '',
    type: 'receipt', // receipt, payment
    description: '',
    layout: 'modern', // modern, classic, minimal, luxury
    colorScheme: 'blue', // blue, red, green, purple, orange
    settings: {
      showLogo: true,
      showWatermark: true,
      showQRCode: true,
      showSignatures: true,
      showCurrency: true,
      showAmountInWords: true,
      pageSize: 'A4',
      orientation: 'portrait',
      margins: 'normal',
      fontSize: 'medium'
    },
    customFields: [],
    branding: {
      companyName: 'شركة شراء للسفر والسياحة',
      companyNameEn: 'Sharau Travel & Tourism Company',
      logo: '✈️',
      address: 'الرياض، المملكة العربية السعودية',
      phone: '+966 11 234 5678',
      email: '<EMAIL>',
      website: 'www.sharaubtravelsoft.com'
    }
  });

  const [activeTab, setActiveTab] = useState('design');
  const [previewData, setPreviewData] = useState({
    voucher_number: 'RV-2024-001',
    date: new Date().toLocaleDateString('ar-SA'),
    customer_name: 'أحمد محمد العلي',
    customer_phone: '+966501234567',
    amount: '5000',
    currency: 'SAR',
    amount_words: 'خمسة آلاف ريال سعودي فقط لا غير',
    payment_method: 'نقدي',
    description: 'دفعة مقدمة لحجز عمرة',
    reference: 'BK-2024-001',
    created_by: 'محمد أحمد'
  });

  const previewRef = useRef(null);

  // تخطيطات القوالب
  const layouts = {
    modern: {
      name: 'عصري',
      description: 'تصميم عصري مع تدرجات وتأثيرات',
      preview: '🎨'
    },
    classic: {
      name: 'كلاسيكي',
      description: 'تصميم تقليدي أنيق ومحترف',
      preview: '📋'
    },
    minimal: {
      name: 'بسيط',
      description: 'تصميم بسيط ونظيف',
      preview: '📄'
    },
    luxury: {
      name: 'فاخر',
      description: 'تصميم فاخر مع تفاصيل ذهبية',
      preview: '👑'
    }
  };

  // أنظمة الألوان
  const colorSchemes = {
    blue: {
      name: 'أزرق',
      primary: '#2563eb',
      secondary: '#3b82f6',
      accent: '#1d4ed8',
      preview: '🔵'
    },
    red: {
      name: 'أحمر',
      primary: '#dc2626',
      secondary: '#ef4444',
      accent: '#b91c1c',
      preview: '🔴'
    },
    green: {
      name: 'أخضر',
      primary: '#16a34a',
      secondary: '#22c55e',
      accent: '#15803d',
      preview: '🟢'
    },
    purple: {
      name: 'بنفسجي',
      primary: '#9333ea',
      secondary: '#a855f7',
      accent: '#7c3aed',
      preview: '🟣'
    },
    orange: {
      name: 'برتقالي',
      primary: '#ea580c',
      secondary: '#f97316',
      accent: '#c2410c',
      preview: '🟠'
    }
  };

  // أحجام الخط
  const fontSizes = {
    small: { name: 'صغير', value: '12px' },
    medium: { name: 'متوسط', value: '14px' },
    large: { name: 'كبير', value: '16px' }
  };

  // الهوامش
  const margins = {
    narrow: { name: 'ضيق', value: '10mm' },
    normal: { name: 'عادي', value: '20mm' },
    wide: { name: 'واسع', value: '30mm' }
  };

  const handleTemplateChange = (field, value) => {
    setTemplate(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSettingsChange = (setting, value) => {
    setTemplate(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [setting]: value
      }
    }));
  };

  const handleBrandingChange = (field, value) => {
    setTemplate(prev => ({
      ...prev,
      branding: {
        ...prev.branding,
        [field]: value
      }
    }));
  };

  const addCustomField = () => {
    const newField = {
      id: Date.now(),
      name: '',
      label: '',
      type: 'text',
      required: false,
      position: 'body'
    };
    
    setTemplate(prev => ({
      ...prev,
      customFields: [...prev.customFields, newField]
    }));
  };

  const updateCustomField = (id, field, value) => {
    setTemplate(prev => ({
      ...prev,
      customFields: prev.customFields.map(f => 
        f.id === id ? { ...f, [field]: value } : f
      )
    }));
  };

  const removeCustomField = (id) => {
    setTemplate(prev => ({
      ...prev,
      customFields: prev.customFields.filter(f => f.id !== id)
    }));
  };

  const generatePreviewHTML = () => {
    const scheme = colorSchemes[template.colorScheme];
    const isPayment = template.type === 'payment';
    
    return `
      <div class="voucher-preview ${template.layout}" style="
        --primary-color: ${scheme.primary};
        --secondary-color: ${scheme.secondary};
        --accent-color: ${scheme.accent};
        font-size: ${fontSizes[template.settings.fontSize].value};
      ">
        ${template.settings.showWatermark ? `
          <div class="watermark">${isPayment ? 'سند صرف' : 'سند قبض'}</div>
        ` : ''}
        
        <div class="voucher-header">
          <div class="company-section">
            ${template.settings.showLogo ? `
              <div class="company-logo">
                <div class="logo">${template.branding.logo}</div>
                <div class="company-info">
                  <h1>${template.branding.companyName}</h1>
                  <p class="company-name-en">${template.branding.companyNameEn}</p>
                </div>
              </div>
            ` : ''}
            <div class="company-details">
              <p>📍 ${template.branding.address}</p>
              <p>📞 ${template.branding.phone}</p>
              <p>📧 ${template.branding.email}</p>
              <p>🌐 ${template.branding.website}</p>
            </div>
          </div>
          
          <div class="voucher-type-section ${isPayment ? 'payment' : 'receipt'}">
            <h2>${isPayment ? '💸 سند صرف' : '💰 سند قبض'}</h2>
            <div class="voucher-number">${previewData.voucher_number}</div>
          </div>
        </div>
        
        <div class="voucher-body">
          <div class="voucher-info">
            <div class="info-section">
              <h3>📅 معلومات التاريخ</h3>
              <p><strong>التاريخ:</strong> ${previewData.date}</p>
              <p><strong>المرجع:</strong> ${previewData.reference}</p>
            </div>
            <div class="info-section">
              <h3>${isPayment ? '🏢 معلومات المستفيد' : '👤 معلومات العميل'}</h3>
              <p><strong>الاسم:</strong> ${previewData.customer_name}</p>
              <p><strong>الهاتف:</strong> ${previewData.customer_phone}</p>
              <p><strong>طريقة الدفع:</strong> ${previewData.payment_method}</p>
            </div>
          </div>
          
          <div class="amount-section ${isPayment ? 'payment' : 'receipt'}">
            <h3>${isPayment ? 'المبلغ المدفوع' : 'المبلغ المستلم'}</h3>
            <div class="amount-value">
              ${previewData.amount} ${template.settings.showCurrency ? previewData.currency : ''}
            </div>
            ${template.settings.showAmountInWords ? `
              <div class="amount-words">${previewData.amount_words}</div>
            ` : ''}
          </div>
          
          <div class="description-section">
            <h3>📝 البيان</h3>
            <p>${previewData.description}</p>
          </div>
          
          ${template.customFields.filter(f => f.position === 'body').map(field => `
            <div class="custom-field">
              <h3>${field.label}</h3>
              <p>[${field.name}]</p>
            </div>
          `).join('')}
          
          ${template.settings.showQRCode ? `
            <div class="qr-section">
              <div class="qr-code">[QR Code]</div>
              <p>رمز الاستجابة السريعة للتحقق</p>
            </div>
          ` : ''}
        </div>
        
        ${template.settings.showSignatures ? `
          <div class="voucher-footer">
            <div class="signatures">
              <div class="signature-box">
                <p>${isPayment ? 'توقيع المستفيد' : 'توقيع المستلم'}</p>
                <div class="signature-line"></div>
              </div>
              <div class="signature-box">
                <p>توقيع المحاسب</p>
                <div class="signature-line"></div>
                <small>${previewData.created_by}</small>
              </div>
              ${isPayment ? `
                <div class="signature-box">
                  <p>توقيع المعتمد</p>
                  <div class="signature-line"></div>
                </div>
              ` : ''}
            </div>
          </div>
        ` : ''}
      </div>
    `;
  };

  const handleSave = () => {
    const templateData = {
      ...template,
      content: generatePreviewHTML(),
      variables: [
        'voucher_number', 'date', 'customer_name', 'customer_phone',
        'amount', 'currency', 'amount_words', 'payment_method',
        'description', 'reference', 'created_by',
        ...template.customFields.map(f => f.name)
      ],
      createdDate: new Date().toISOString().split('T')[0],
      isActive: true
    };
    
    onSave(templateData);
  };

  return (
    <div className="voucher-template-builder">
      <div className="builder-header">
        <h2>🎨 منشئ قوالب السندات المتطور</h2>
        <div className="header-actions">
          <button className="save-btn" onClick={handleSave}>
            💾 حفظ القالب
          </button>
          <button className="cancel-btn" onClick={onCancel}>
            ❌ إلغاء
          </button>
        </div>
      </div>

      <div className="builder-content">
        <div className="builder-sidebar">
          <div className="tabs">
            <button 
              className={`tab ${activeTab === 'design' ? 'active' : ''}`}
              onClick={() => setActiveTab('design')}
            >
              🎨 التصميم
            </button>
            <button 
              className={`tab ${activeTab === 'content' ? 'active' : ''}`}
              onClick={() => setActiveTab('content')}
            >
              📝 المحتوى
            </button>
            <button 
              className={`tab ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              ⚙️ الإعدادات
            </button>
            <button 
              className={`tab ${activeTab === 'branding' ? 'active' : ''}`}
              onClick={() => setActiveTab('branding')}
            >
              🏢 العلامة التجارية
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'design' && (
              <div className="design-panel">
                <div className="form-group">
                  <label>اسم القالب</label>
                  <input
                    type="text"
                    value={template.name}
                    onChange={(e) => handleTemplateChange('name', e.target.value)}
                    placeholder="أدخل اسم القالب"
                  />
                </div>

                <div className="form-group">
                  <label>نوع السند</label>
                  <select
                    value={template.type}
                    onChange={(e) => handleTemplateChange('type', e.target.value)}
                  >
                    <option value="receipt">💰 سند قبض</option>
                    <option value="payment">💸 سند صرف</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>التخطيط</label>
                  <div className="layout-grid">
                    {Object.entries(layouts).map(([key, layout]) => (
                      <div
                        key={key}
                        className={`layout-option ${template.layout === key ? 'selected' : ''}`}
                        onClick={() => handleTemplateChange('layout', key)}
                      >
                        <div className="layout-preview">{layout.preview}</div>
                        <div className="layout-info">
                          <h4>{layout.name}</h4>
                          <p>{layout.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="form-group">
                  <label>نظام الألوان</label>
                  <div className="color-grid">
                    {Object.entries(colorSchemes).map(([key, scheme]) => (
                      <div
                        key={key}
                        className={`color-option ${template.colorScheme === key ? 'selected' : ''}`}
                        onClick={() => handleTemplateChange('colorScheme', key)}
                        style={{ '--scheme-color': scheme.primary }}
                      >
                        <div className="color-preview">{scheme.preview}</div>
                        <span>{scheme.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'content' && (
              <div className="content-panel">
                <div className="form-group">
                  <label>وصف القالب</label>
                  <textarea
                    value={template.description}
                    onChange={(e) => handleTemplateChange('description', e.target.value)}
                    placeholder="وصف مختصر للقالب"
                    rows="3"
                  />
                </div>

                <div className="custom-fields">
                  <div className="section-header">
                    <h3>الحقول المخصصة</h3>
                    <button className="add-field-btn" onClick={addCustomField}>
                      ➕ إضافة حقل
                    </button>
                  </div>

                  {template.customFields.map(field => (
                    <div key={field.id} className="custom-field-editor">
                      <div className="field-controls">
                        <input
                          type="text"
                          placeholder="اسم الحقل"
                          value={field.name}
                          onChange={(e) => updateCustomField(field.id, 'name', e.target.value)}
                        />
                        <input
                          type="text"
                          placeholder="التسمية"
                          value={field.label}
                          onChange={(e) => updateCustomField(field.id, 'label', e.target.value)}
                        />
                        <select
                          value={field.type}
                          onChange={(e) => updateCustomField(field.id, 'type', e.target.value)}
                        >
                          <option value="text">نص</option>
                          <option value="number">رقم</option>
                          <option value="date">تاريخ</option>
                        </select>
                        <button
                          className="remove-field-btn"
                          onClick={() => removeCustomField(field.id)}
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="settings-panel">
                <div className="settings-group">
                  <h3>عناصر السند</h3>
                  <div className="checkbox-group">
                    <label>
                      <input
                        type="checkbox"
                        checked={template.settings.showLogo}
                        onChange={(e) => handleSettingsChange('showLogo', e.target.checked)}
                      />
                      عرض الشعار
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={template.settings.showWatermark}
                        onChange={(e) => handleSettingsChange('showWatermark', e.target.checked)}
                      />
                      العلامة المائية
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={template.settings.showQRCode}
                        onChange={(e) => handleSettingsChange('showQRCode', e.target.checked)}
                      />
                      رمز QR
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={template.settings.showSignatures}
                        onChange={(e) => handleSettingsChange('showSignatures', e.target.checked)}
                      />
                      التوقيعات
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={template.settings.showCurrency}
                        onChange={(e) => handleSettingsChange('showCurrency', e.target.checked)}
                      />
                      رمز العملة
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={template.settings.showAmountInWords}
                        onChange={(e) => handleSettingsChange('showAmountInWords', e.target.checked)}
                      />
                      المبلغ بالكلمات
                    </label>
                  </div>
                </div>

                <div className="settings-group">
                  <h3>إعدادات الطباعة</h3>
                  <div className="form-group">
                    <label>حجم الصفحة</label>
                    <select
                      value={template.settings.pageSize}
                      onChange={(e) => handleSettingsChange('pageSize', e.target.value)}
                    >
                      <option value="A4">A4</option>
                      <option value="A5">A5</option>
                      <option value="Letter">Letter</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>الاتجاه</label>
                    <select
                      value={template.settings.orientation}
                      onChange={(e) => handleSettingsChange('orientation', e.target.value)}
                    >
                      <option value="portrait">عمودي</option>
                      <option value="landscape">أفقي</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>حجم الخط</label>
                    <select
                      value={template.settings.fontSize}
                      onChange={(e) => handleSettingsChange('fontSize', e.target.value)}
                    >
                      {Object.entries(fontSizes).map(([key, size]) => (
                        <option key={key} value={key}>{size.name}</option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label>الهوامش</label>
                    <select
                      value={template.settings.margins}
                      onChange={(e) => handleSettingsChange('margins', e.target.value)}
                    >
                      {Object.entries(margins).map(([key, margin]) => (
                        <option key={key} value={key}>{margin.name}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'branding' && (
              <div className="branding-panel">
                <div className="form-group">
                  <label>اسم الشركة (عربي)</label>
                  <input
                    type="text"
                    value={template.branding.companyName}
                    onChange={(e) => handleBrandingChange('companyName', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label>اسم الشركة (إنجليزي)</label>
                  <input
                    type="text"
                    value={template.branding.companyNameEn}
                    onChange={(e) => handleBrandingChange('companyNameEn', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label>الشعار (إيموجي أو نص)</label>
                  <input
                    type="text"
                    value={template.branding.logo}
                    onChange={(e) => handleBrandingChange('logo', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label>العنوان</label>
                  <input
                    type="text"
                    value={template.branding.address}
                    onChange={(e) => handleBrandingChange('address', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label>رقم الهاتف</label>
                  <input
                    type="text"
                    value={template.branding.phone}
                    onChange={(e) => handleBrandingChange('phone', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={template.branding.email}
                    onChange={(e) => handleBrandingChange('email', e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label>الموقع الإلكتروني</label>
                  <input
                    type="text"
                    value={template.branding.website}
                    onChange={(e) => handleBrandingChange('website', e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="builder-preview">
          <div className="preview-header">
            <h3>👁️ معاينة مباشرة</h3>
            <div className="preview-controls">
              <button className="zoom-btn">🔍 تكبير</button>
              <button className="print-btn">🖨️ طباعة تجريبية</button>
            </div>
          </div>
          
          <div className="preview-container">
            <div 
              ref={previewRef}
              className="preview-content"
              dangerouslySetInnerHTML={{ __html: generatePreviewHTML() }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoucherTemplateBuilder;