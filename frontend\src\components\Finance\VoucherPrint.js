import React, { useRef, useEffect } from 'react';
import { CurrencyManager, CURRENCIES } from './CurrencyManager';
import './VoucherPrint.css';

const VoucherPrint = ({ voucher, type = 'receipt', onClose, companyInfo }) => {
  const printRef = useRef();

  const defaultCompanyInfo = {
    name: 'شركة شراء للسفر والسياحة',
    nameEn: 'Sharau Travel & Tourism Company',
    logo: '✈️',
    address: 'الرياض، المملكة العربية السعودية',
    addressEn: 'Riyadh, Saudi Arabia',
    phone: '+966 11 234 5678',
    email: '<EMAIL>',
    website: 'www.sharaubtravelsoft.com',
    cr: '**********',
    vatNumber: '300123456789003',
    license: 'ترخيص رقم: 12345-ت'
  };

  const company = { ...defaultCompanyInfo, ...companyInfo };
  const currency = CURRENCIES[voucher.currency] || CURRENCIES.SAR;
  const isReceipt = type === 'receipt';

  useEffect(() => {
    // تطبيق أنماط الطباعة عند تحميل المكون
    document.body.classList.add('printing-mode');
    
    return () => {
      document.body.classList.remove('printing-mode');
    };
  }, []);

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = printRef.current.innerHTML;
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${isReceipt ? 'سند قبض' : 'سند صرف'} - ${voucher.voucherNumber}</title>
        <style>
          ${getInlinePrintStyles()}
        </style>
      </head>
      <body>
        <div class="print-container">
          ${printContent}
        </div>
        <script>
          window.onload = function() {
            window.print();
            window.onafterprint = function() {
              window.close();
            };
          };
        </script>
      </body>
      </html>
    `);
    
    printWindow.document.close();
  };

  const getInlinePrintStyles = () => {
    return `
      @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        background: white;
        color: #000;
        line-height: 1.6;
      }
      
      .print-container {
        max-width: 210mm;
        margin: 0 auto;
        padding: 20mm;
        background: white;
        min-height: 297mm;
        position: relative;
      }
      
      .voucher-print {
        width: 100%;
        height: 100%;
        position: relative;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border: 3px solid #2563eb;
        border-radius: 15px;
        overflow: hidden;
      }
      
      .voucher-watermark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 120px;
        color: rgba(37, 99, 235, 0.05);
        font-weight: bold;
        z-index: 1;
        pointer-events: none;
      }
      
      .voucher-content {
        position: relative;
        z-index: 2;
        padding: 30px;
      }
      
      .voucher-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 3px solid #2563eb;
      }
      
      .company-section {
        flex: 1;
      }
      
      .company-logo {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
      }
      
      .logo-icon {
        font-size: 48px;
        background: linear-gradient(135deg, #2563eb, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .company-name {
        font-size: 28px;
        font-weight: 700;
        color: #1e40af;
        margin: 0;
      }
      
      .company-name-en {
        font-size: 16px;
        color: #64748b;
        font-weight: 500;
        margin-top: 5px;
      }
      
      .company-details {
        color: #475569;
        font-size: 14px;
        line-height: 1.8;
      }
      
      .voucher-type-section {
        text-align: center;
        background: linear-gradient(135deg, #2563eb, #3b82f6);
        color: white;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
      }
      
      .voucher-type {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
      }
      
      .voucher-number {
        font-size: 18px;
        font-weight: 600;
        font-family: 'Courier New', monospace;
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 8px;
        display: inline-block;
      }
      
      .voucher-body {
        margin: 40px 0;
      }
      
      .voucher-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 40px;
      }
      
      .info-section {
        background: #f8fafc;
        padding: 25px;
        border-radius: 12px;
        border-right: 4px solid #2563eb;
      }
      
      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #1e40af;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
      }
      
      .info-row:last-child {
        border-bottom: none;
      }
      
      .info-label {
        font-weight: 600;
        color: #374151;
      }
      
      .info-value {
        color: #1f2937;
        font-weight: 500;
      }
      
      .amount-section {
        background: linear-gradient(135deg, #fef3c7, #fbbf24);
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        margin: 30px 0;
        border: 2px solid #f59e0b;
      }
      
      .amount-title {
        font-size: 20px;
        font-weight: 600;
        color: #92400e;
        margin-bottom: 15px;
      }
      
      .amount-value {
        font-size: 36px;
        font-weight: 700;
        color: #78350f;
        margin-bottom: 15px;
        font-family: 'Courier New', monospace;
      }
      
      .amount-words {
        font-size: 16px;
        color: #a16207;
        font-weight: 500;
        font-style: italic;
        background: rgba(255, 255, 255, 0.5);
        padding: 10px 20px;
        border-radius: 8px;
        display: inline-block;
      }
      
      .description-section {
        background: #f1f5f9;
        padding: 25px;
        border-radius: 12px;
        margin: 30px 0;
        border-right: 4px solid #10b981;
      }
      
      .description-title {
        font-size: 18px;
        font-weight: 600;
        color: #059669;
        margin-bottom: 15px;
      }
      
      .description-text {
        font-size: 16px;
        color: #374151;
        line-height: 1.8;
      }
      
      .voucher-footer {
        margin-top: 50px;
        padding-top: 30px;
        border-top: 2px solid #e5e7eb;
      }
      
      .signatures {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 40px;
        margin-bottom: 30px;
      }
      
      .signature-box {
        text-align: center;
        padding: 20px;
        border: 2px dashed #9ca3af;
        border-radius: 10px;
        min-height: 80px;
      }
      
      .signature-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 10px;
      }
      
      .signature-line {
        border-bottom: 2px solid #9ca3af;
        margin: 20px 0 10px 0;
      }
      
      .footer-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #6b7280;
        margin-top: 30px;
      }
      
      .print-date {
        font-weight: 500;
      }
      
      .qr-section {
        text-align: center;
        padding: 20px;
        background: #f9fafb;
        border-radius: 10px;
        margin: 20px 0;
      }
      
      .qr-placeholder {
        width: 100px;
        height: 100px;
        background: #e5e7eb;
        border: 2px dashed #9ca3af;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px;
        font-size: 12px;
        color: #6b7280;
      }
      
      .currency-info {
        background: #ecfdf5;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        border-right: 4px solid #10b981;
      }
      
      .currency-rate {
        font-size: 14px;
        color: #065f46;
        font-weight: 500;
      }
      
      @media print {
        body {
          margin: 0;
          padding: 0;
        }
        
        .print-container {
          margin: 0;
          padding: 0;
          max-width: none;
          min-height: auto;
        }
        
        .voucher-print {
          border: none;
          border-radius: 0;
          box-shadow: none;
        }
        
        .voucher-content {
          padding: 20px;
        }
      }
    `;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="voucher-print-modal">
      <div className="print-modal-overlay" onClick={onClose}>
        <div className="print-modal-content" onClick={(e) => e.stopPropagation()}>
          <div className="print-modal-header">
            <h2>معاينة {isReceipt ? 'سند القبض' : 'سند الصرف'}</h2>
            <div className="print-modal-actions">
              <button className="print-btn" onClick={handlePrint}>
                🖨️ طباعة
              </button>
              <button className="close-btn" onClick={onClose}>
                ✕ إغلاق
              </button>
            </div>
          </div>
          
          <div className="print-preview" ref={printRef}>
            <div className="voucher-print">
              {/* العلامة المائية */}
              <div className="voucher-watermark">
                {isReceipt ? 'سند قبض' : 'سند صرف'}
              </div>
              
              <div className="voucher-content">
                {/* رأس السند */}
                <div className="voucher-header">
                  <div className="company-section">
                    <div className="company-logo">
                      <div className="logo-icon">{company.logo}</div>
                      <div>
                        <h1 className="company-name">{company.name}</h1>
                        <div className="company-name-en">{company.nameEn}</div>
                      </div>
                    </div>
                    <div className="company-details">
                      <div>📍 {company.address}</div>
                      <div>📞 {company.phone}</div>
                      <div>📧 {company.email}</div>
                      <div>🌐 {company.website}</div>
                      <div>📋 س.ت: {company.cr}</div>
                      <div>🏛️ ض.ق.م: {company.vatNumber}</div>
                      <div>📜 {company.license}</div>
                    </div>
                  </div>
                  
                  <div className="voucher-type-section">
                    <div className="voucher-type">
                      {isReceipt ? '💰 سند قبض' : '💸 سند صرف'}
                    </div>
                    <div className="voucher-number">
                      {voucher.voucherNumber}
                    </div>
                  </div>
                </div>

                {/* معلومات السند */}
                <div className="voucher-body">
                  <div className="voucher-info">
                    <div className="info-section">
                      <div className="section-title">
                        📅 معلومات التاريخ والوقت
                      </div>
                      <div className="info-row">
                        <span className="info-label">التاريخ:</span>
                        <span className="info-value">{formatDate(voucher.date)}</span>
                      </div>
                      <div className="info-row">
                        <span className="info-label">الوقت:</span>
                        <span className="info-value">{formatTime(voucher.createdAt || new Date())}</span>
                      </div>
                      <div className="info-row">
                        <span className="info-label">المرجع:</span>
                        <span className="info-value">{voucher.reference || 'غير محدد'}</span>
                      </div>
                    </div>

                    <div className="info-section">
                      <div className="section-title">
                        {isReceipt ? '👤 معلومات العميل' : '🏢 معلومات المستفيد'}
                      </div>
                      <div className="info-row">
                        <span className="info-label">الاسم:</span>
                        <span className="info-value">
                          {isReceipt ? voucher.customerName : voucher.beneficiaryName}
                        </span>
                      </div>
                      <div className="info-row">
                        <span className="info-label">الهاتف:</span>
                        <span className="info-value">
                          {isReceipt ? voucher.customerPhone : voucher.beneficiaryPhone}
                        </span>
                      </div>
                      <div className="info-row">
                        <span className="info-label">طريقة الدفع:</span>
                        <span className="info-value">
                          {voucher.paymentMethod === 'cash' && '💵 نقدي'}
                          {voucher.paymentMethod === 'bank_transfer' && '🏦 تحويل بنكي'}
                          {voucher.paymentMethod === 'credit_card' && '💳 بطاقة ائتمان'}
                          {voucher.paymentMethod === 'check' && '📝 شيك'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* قسم المبلغ */}
                  <div className="amount-section">
                    <div className="amount-title">
                      {isReceipt ? 'المبلغ المستلم' : 'المبلغ المدفوع'}
                    </div>
                    <div className="amount-value">
                      {CurrencyManager.formatAmount(voucher.amount, voucher.currency)}
                    </div>
                    <div className="amount-words">
                      {CurrencyManager.formatAmountInWords(voucher.amount, voucher.currency)}
                    </div>
                  </div>

                  {/* معلومات العملة */}
                  {voucher.currency !== 'SAR' && (
                    <div className="currency-info">
                      <div className="currency-rate">
                        💱 سعر الصرف: 1 {currency.symbol} = {CurrencyManager.getExchangeRate(voucher.currency, 'SAR').toFixed(4)} ر.س
                      </div>
                      <div className="currency-rate">
                        💰 المبلغ بالريال السعودي: {CurrencyManager.formatAmount(
                          CurrencyManager.convertCurrency(voucher.amount, voucher.currency, 'SAR'),
                          'SAR'
                        )}
                      </div>
                    </div>
                  )}

                  {/* وصف السند */}
                  <div className="description-section">
                    <div className="description-title">
                      📝 البيان والوصف
                    </div>
                    <div className="description-text">
                      {voucher.description}
                    </div>
                    {voucher.notes && (
                      <div style={{ marginTop: '15px', fontSize: '14px', color: '#6b7280' }}>
                        <strong>ملاحظات:</strong> {voucher.notes}
                      </div>
                    )}
                  </div>

                  {/* QR Code */}
                  <div className="qr-section">
                    <div className="qr-placeholder">
                      QR Code
                    </div>
                    <div style={{ fontSize: '12px', color: '#6b7280' }}>
                      رمز الاستجابة السريعة للتحقق من صحة السند
                    </div>
                  </div>
                </div>

                {/* تذييل السند */}
                <div className="voucher-footer">
                  <div className="signatures">
                    <div className="signature-box">
                      <div className="signature-label">
                        {isReceipt ? 'توقيع المستلم' : 'توقيع المستفيد'}
                      </div>
                      <div className="signature-line"></div>
                    </div>
                    <div className="signature-box">
                      <div className="signature-label">توقيع المحاسب</div>
                      <div className="signature-line"></div>
                      <div style={{ fontSize: '12px', marginTop: '5px' }}>
                        {voucher.createdBy}
                      </div>
                    </div>
                    <div className="signature-box">
                      <div className="signature-label">
                        {isReceipt ? 'توقيع أمين الصندوق' : 'توقيع المعتمد'}
                      </div>
                      <div className="signature-line"></div>
                      <div style={{ fontSize: '12px', marginTop: '5px' }}>
                        {voucher.approvedBy || 'غير محدد'}
                      </div>
                    </div>
                  </div>

                  <div className="footer-info">
                    <div className="print-date">
                      تاريخ الطباعة: {new Date().toLocaleDateString('ar-SA')} - {new Date().toLocaleTimeString('ar-SA')}
                    </div>
                    <div>
                      تم إنشاؤه بواسطة نظام شراء للسفر والسياحة
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoucherPrint;