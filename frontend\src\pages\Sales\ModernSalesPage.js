import React, { useState, useEffect } from 'react';
import ModernCard, { StatsCard, CardBody, CardHeader, CardTitle } from '../../components/UI/ModernCard';
import ModernButton, { IconButton } from '../../components/UI/ModernButton';
import { ModernGrid, ModernFlex } from '../../components/UI/ModernLayout';
import { DataTable } from '../../components/UI/ModernTable';
import { ModernInput, ModernSelect } from '../../components/UI/ModernForm';
import { ModernAlert, StatusBadge } from '../../components/UI/ModernAlert';
import { ModalWithFooter, FormModal } from '../../components/UI/ModernModal';
import './ModernSalesPage.css';

const ModernSalesPage = () => {
  const [salesData, setSalesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSales, setSelectedSales] = useState([]);
  const [showNewSaleModal, setShowNewSaleModal] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    dateRange: 'all',
    customer: ''
  });

  // Sample data
  useEffect(() => {
    setTimeout(() => {
      setSalesData([
        {
          id: 1,
          invoiceNumber: 'INV-2024-001',
          customer: 'أحمد محمد العلي',
          service: 'رحلة الرياض - دبي',
          amount: 2500,
          status: 'paid',
          date: '2024-01-15',
          dueDate: '2024-01-20',
          paymentMethod: 'credit_card'
        },
        {
          id: 2,
          invoiceNumber: 'INV-2024-002',
          customer: 'فاطمة سالم أحمد',
          service: 'عمرة - 7 أيام',
          amount: 1800,
          status: 'pending',
          date: '2024-01-14',
          dueDate: '2024-01-25',
          paymentMethod: 'bank_transfer'
        },
        {
          id: 3,
          invoiceNumber: 'INV-2024-003',
          customer: 'محمد عبدالله الزهراني',
          service: 'تأشيرة سياحية - تركيا',
          amount: 450,
          status: 'overdue',
          date: '2024-01-10',
          dueDate: '2024-01-15',
          paymentMethod: 'cash'
        },
        {
          id: 4,
          invoiceNumber: 'INV-2024-004',
          customer: 'نورا خالد المطيري',
          service: 'حجز فندق - 5 ليالي',
          amount: 3200,
          status: 'paid',
          date: '2024-01-12',
          dueDate: '2024-01-18',
          paymentMethod: 'credit_card'
        },
        {
          id: 5,
          invoiceNumber: 'INV-2024-005',
          customer: 'خالد يوسف الغامدي',
          service: 'رحلة باريس - 10 أيام',
          amount: 18000,
          status: 'draft',
          date: '2024-01-13',
          dueDate: '2024-01-28',
          paymentMethod: 'bank_transfer'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusLabel = (status) => {
    const labels = {
      paid: 'مدفوع',
      pending: 'قيد الانتظار',
      overdue: 'متأخر',
      draft: 'مسودة',
      cancelled: 'ملغي'
    };
    return labels[status] || status;
  };

  const getStatusVariant = (status) => {
    const variants = {
      paid: 'success',
      pending: 'warning',
      overdue: 'error',
      draft: 'info',
      cancelled: 'error'
    };
    return variants[status] || 'default';
  };

  const getPaymentMethodLabel = (method) => {
    const labels = {
      credit_card: 'بطاقة ائتمان',
      bank_transfer: 'تحويل بنكي',
      cash: 'نقداً',
      check: 'شيك'
    };
    return labels[method] || method;
  };

  const columns = [
    {
      key: 'invoiceNumber',
      title: 'رقم الفاتورة',
      width: '120px',
      render: (value) => (
        <span className="invoice-number">{value}</span>
      )
    },
    {
      key: 'customer',
      title: 'العميل',
      width: '200px'
    },
    {
      key: 'service',
      title: 'الخدمة',
      width: '250px'
    },
    {
      key: 'amount',
      title: 'المبلغ',
      width: '120px',
      align: 'center',
      render: (value) => (
        <span className="amount">{formatCurrency(value)}</span>
      )
    },
    {
      key: 'status',
      title: 'الحالة',
      width: '100px',
      align: 'center',
      render: (value) => (
        <StatusBadge variant={getStatusVariant(value)}>
          {getStatusLabel(value)}
        </StatusBadge>
      )
    },
    {
      key: 'date',
      title: 'تاريخ الإنشاء',
      width: '120px',
      align: 'center'
    },
    {
      key: 'dueDate',
      title: 'تاريخ الاستحقاق',
      width: '120px',
      align: 'center'
    },
    {
      key: 'paymentMethod',
      title: 'طريقة الدفع',
      width: '120px',
      align: 'center',
      render: (value) => getPaymentMethodLabel(value)
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      width: '150px',
      align: 'center',
      sortable: false,
      render: (_, row) => (
        <ModernFlex gap="sm" justify="center">
          <IconButton
            icon="👁️"
            variant="ghost"
            size="sm"
            tooltip="عرض"
            onClick={() => handleViewSale(row)}
          />
          <IconButton
            icon="✏️"
            variant="ghost"
            size="sm"
            tooltip="تعديل"
            onClick={() => handleEditSale(row)}
          />
          <IconButton
            icon="🗑️"
            variant="ghost"
            size="sm"
            tooltip="حذف"
            onClick={() => handleDeleteSale(row)}
          />
        </ModernFlex>
      )
    }
  ];

  const handleViewSale = (sale) => {
    console.log('View sale:', sale);
  };

  const handleEditSale = (sale) => {
    console.log('Edit sale:', sale);
  };

  const handleDeleteSale = (sale) => {
    console.log('Delete sale:', sale);
  };

  const handleNewSale = () => {
    setShowNewSaleModal(true);
  };

  const handleExportSales = () => {
    console.log('Export sales data');
  };

  const handleBulkAction = (action) => {
    console.log('Bulk action:', action, 'on:', selectedSales);
  };

  // Calculate stats
  const stats = {
    totalSales: salesData.reduce((sum, sale) => sum + sale.amount, 0),
    paidSales: salesData.filter(sale => sale.status === 'paid').length,
    pendingSales: salesData.filter(sale => sale.status === 'pending').length,
    overdueSales: salesData.filter(sale => sale.status === 'overdue').length
  };

  return (
    <div className="modern-sales-page">
      {/* Page Header */}
      <div className="sales-page-header">
        <div className="sales-page-title">
          <h1>إدارة المبيعات</h1>
          <p>إدارة شاملة لجميع المبيعات والفواتير</p>
        </div>
        
        <div className="sales-page-actions">
          <ModernButton
            variant="outline"
            icon="📥"
            onClick={handleExportSales}
          >
            تصدير البيانات
          </ModernButton>
          <ModernButton
            variant="primary"
            icon="➕"
            onClick={handleNewSale}
          >
            فاتورة جديدة
          </ModernButton>
        </div>
      </div>

      {/* Stats Cards */}
      <ModernGrid cols={4} gap="lg" responsive className="sales-stats">
        <StatsCard
          title="إجمالي المبيعات"
          value={formatCurrency(stats.totalSales)}
          change="+12.5%"
          changeType="positive"
          icon="💰"
          color="success"
        />
        
        <StatsCard
          title="الفواتير المدفوعة"
          value={stats.paidSales.toString()}
          change="+8.2%"
          changeType="positive"
          icon="✅"
          color="primary"
        />
        
        <StatsCard
          title="الفواتير المعلقة"
          value={stats.pendingSales.toString()}
          change="+3.1%"
          changeType="positive"
          icon="⏳"
          color="warning"
        />
        
        <StatsCard
          title="الفواتير المتأخرة"
          value={stats.overdueSales.toString()}
          change="-2.3%"
          changeType="negative"
          icon="⚠️"
          color="error"
        />
      </ModernGrid>

      {/* Alerts */}
      {stats.overdueSales > 0 && (
        <ModernAlert
          variant="warning"
          title="تنبيه"
          dismissible
          actions={
            <ModernButton variant="warning" size="sm">
              عرض الفواتير المتأخرة
            </ModernButton>
          }
        >
          لديك {stats.overdueSales} فاتورة متأخرة تحتاج إلى متابعة
        </ModernAlert>
      )}

      {/* Sales Table */}
      <ModernCard>
        <CardHeader>
          <CardTitle>قائمة المبيعات</CardTitle>
        </CardHeader>
        <CardBody>
          <DataTable
            data={salesData}
            columns={columns}
            loading={loading}
            selectable
            searchable
            pagination
            pageSize={10}
            onSelectionChange={setSelectedSales}
            filters={
              <ModernFlex gap="md" align="end">
                <ModernSelect
                  label="الحالة"
                  value={filters.status}
                  onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                  options={[
                    { value: '', label: 'جميع الحالات' },
                    { value: 'paid', label: 'مدفوع' },
                    { value: 'pending', label: 'قيد الانتظار' },
                    { value: 'overdue', label: 'متأخر' },
                    { value: 'draft', label: 'مسودة' }
                  ]}
                  size="sm"
                />
                
                <ModernSelect
                  label="الفترة الزمنية"
                  value={filters.dateRange}
                  onChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
                  options={[
                    { value: 'all', label: 'جميع الفترات' },
                    { value: 'today', label: 'اليوم' },
                    { value: 'week', label: 'هذا الأسبوع' },
                    { value: 'month', label: 'هذا الشهر' },
                    { value: 'year', label: 'هذا العام' }
                  ]}
                  size="sm"
                />
                
                <ModernButton
                  variant="outline"
                  size="sm"
                  icon="🔄"
                  onClick={() => setFilters({ status: '', dateRange: 'all', customer: '' })}
                >
                  إعادة تعيين
                </ModernButton>
              </ModernFlex>
            }
            actions={
              selectedSales.length > 0 && (
                <ModernFlex gap="sm">
                  <ModernButton
                    variant="primary"
                    size="sm"
                    icon="📧"
                    onClick={() => handleBulkAction('send_reminder')}
                  >
                    إرسال تذكير
                  </ModernButton>
                  <ModernButton
                    variant="success"
                    size="sm"
                    icon="✅"
                    onClick={() => handleBulkAction('mark_paid')}
                  >
                    تحديد كمدفوع
                  </ModernButton>
                  <ModernButton
                    variant="error"
                    size="sm"
                    icon="🗑️"
                    onClick={() => handleBulkAction('delete')}
                  >
                    حذف
                  </ModernButton>
                </ModernFlex>
              )
            }
          />
        </CardBody>
      </ModernCard>

      {/* Quick Actions */}
      <ModernGrid cols={3} gap="lg" responsive className="quick-actions">
        <ModernCard hover>
          <CardBody>
            <ModernFlex direction="column" align="center" gap="md">
              <div className="quick-action-icon">📊</div>
              <h3>تقارير المبيعات</h3>
              <p>عرض تقارير مفصلة عن أداء المبيعات</p>
              <ModernButton variant="primary" size="sm">
                عرض التقارير
              </ModernButton>
            </ModernFlex>
          </CardBody>
        </ModernCard>
        
        <ModernCard hover>
          <CardBody>
            <ModernFlex direction="column" align="center" gap="md">
              <div className="quick-action-icon">👥</div>
              <h3>إدارة العملاء</h3>
              <p>إضافة وإدارة بيانات العملاء</p>
              <ModernButton variant="secondary" size="sm">
                إدارة العملاء
              </ModernButton>
            </ModernFlex>
          </CardBody>
        </ModernCard>
        
        <ModernCard hover>
          <CardBody>
            <ModernFlex direction="column" align="center" gap="md">
              <div className="quick-action-icon">⚙️</div>
              <h3>إعدادات المبيعات</h3>
              <p>تخصيص إعدادات النظام والفواتير</p>
              <ModernButton variant="info" size="sm">
                الإعدادات
              </ModernButton>
            </ModernFlex>
          </CardBody>
        </ModernCard>
      </ModernGrid>

      {/* New Sale Modal */}
      <FormModal
        isOpen={showNewSaleModal}
        onClose={() => setShowNewSaleModal(false)}
        title="إنشاء فاتورة جديدة"
        size="lg"
        onSubmit={(e) => {
          e.preventDefault();
          console.log('Create new sale');
          setShowNewSaleModal(false);
        }}
      >
        <ModernGrid cols={2} gap="md">
          <ModernInput
            label="العميل"
            placeholder="اختر العميل"
            required
          />
          <ModernInput
            label="رقم الفاتورة"
            placeholder="INV-2024-006"
            required
          />
          <ModernSelect
            label="نوع الخدمة"
            placeholder="اختر نوع الخدمة"
            options={[
              { value: 'flight', label: 'حجز طيران' },
              { value: 'hotel', label: 'حجز فندق' },
              { value: 'visa', label: 'تأشيرة' },
              { value: 'package', label: 'باقة سياحية' },
              { value: 'umrah', label: 'عمرة' },
              { value: 'hajj', label: 'حج' }
            ]}
            required
          />
          <ModernInput
            label="المبلغ"
            type="number"
            placeholder="0.00"
            required
          />
          <ModernInput
            label="تاريخ الإنشاء"
            type="date"
            required
          />
          <ModernInput
            label="تاريخ الاستحقاق"
            type="date"
            required
          />
          <ModernSelect
            label="طريقة الدفع"
            placeholder="اختر طريقة الدفع"
            options={[
              { value: 'credit_card', label: 'بطاقة ائتمان' },
              { value: 'bank_transfer', label: 'تحويل بنكي' },
              { value: 'cash', label: 'نقداً' },
              { value: 'check', label: 'شيك' }
            ]}
            required
          />
          <ModernSelect
            label="الحالة"
            placeholder="اختر الحالة"
            options={[
              { value: 'draft', label: 'مسودة' },
              { value: 'pending', label: 'قيد الانتظار' },
              { value: 'paid', label: 'مدفوع' }
            ]}
            required
          />
        </ModernGrid>
      </FormModal>
    </div>
  );
};

export default ModernSalesPage;