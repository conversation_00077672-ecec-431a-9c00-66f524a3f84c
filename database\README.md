# قاعدة بيانات نظام شراء السياحة
# Sharau Travel System Database

## نظرة عامة
هذا المجلد يحتوي على ملفات قاعدة البيانات الكاملة لنظام شراء السياحة المحاسبي.

## الملفات المتوفرة

### 1. `complete_database.sql`
ملف SQL شامل يحتوي على:
- إنشاء قاعدة البيانات `travel_accounting`
- إنشاء جميع الجداول المطلوبة
- إعداد المستخدمين والصلاحيات
- إدراج البيانات الأولية
- إنشاء الفهارس والـ Views

### 2. `setup_database.bat`
ملف تشغيل سريع لإعداد قاعدة البيانات تلقائياً

### 3. `init.sql`
ملف الإعداد الأساسي (للمرجع)

## كيفية الاستخدام

### الطريقة الأولى: استخدام ملف التشغيل السريع
```bash
# تشغيل الملف
setup_database.bat
```

### الطريقة الثانية: استخدام MySQL مباشرة
```bash
# الاتصال بـ MySQL
mysql -u root -p

# تنفيذ ملف قاعدة البيانات
source complete_database.sql
```

### الطريقة الثالثة: استخدام phpMyAdmin أو أدوات أخرى
1. افتح phpMyAdmin أو أي أداة إدارة قواعد بيانات
2. استورد ملف `complete_database.sql`

## معلومات قاعدة البيانات

### معلومات الاتصال
- **اسم قاعدة البيانات**: `travel_accounting`
- **المستخدم**: `travel_user`
- **كلمة المرور**: `travel_password_2024`
- **الترميز**: `utf8mb4_unicode_ci`

### المستخدم الافتراضي للنظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: مدير عام (جميع الصلاحيات)

## هيكل قاعدة البيانات

### جداول إدارة المستخدمين
- `users` - المستخدمين
- `roles` - الأدوار
- `permissions` - الصلاحيات
- `user_roles` - ربط المستخدمين بالأدوار
- `role_permissions` - ربط الأدوار بالصلاحيات

### جداول العملاء
- `customers` - العملاء الرئيسي
- `customer_contacts` - جهات اتصال العملاء
- `customer_documents` - مستندات العملاء
- `customer_notes` - ملاحظات العملاء

### جداول الموردين
- `suppliers` - الموردين
- `supplier_contacts` - جهات اتصال الموردين

### جداول الوكلاء
- `agents` - الوكلاء

### جداول الحجوزات
- `bookings` - الحجوزات الرئيسي
- `booking_passengers` - ركاب الحجز
- `booking_services` - خدمات الحجز
- `booking_payments` - مدفوعات الحجز
- `booking_documents` - مستندات الحجز
- `booking_history` - تاريخ تغييرات الحجز

### جداول المحاسبة
- `accounts` - دليل الحسابات
- `journal_entries` - القيود المحاسبية
- `journal_entry_lines` - تفاصيل القيود المحاسبية

### جداول الفواتير والمدفوعات
- `invoices` - الفواتير
- `invoice_items` - بنود الفواتير
- `payments` - المدفوعات

### جداول المخزون
- `visa_types` - أنواع التأشيرات
- `visa_inventory` - مخزون التأشيرات

### جداول النظام
- `system_settings` - إعدادات النظام
- `activity_logs` - سجل النشاطات
- `saved_reports` - التقارير المحفوظة

## Views المتوفرة

### `customer_summary`
ملخص العملاء مع إحصائيات الحجوزات والمبيعات

### `booking_summary`
ملخص الحجوزات مع معلومات العملاء والموردين والوكلاء

### `financial_summary`
ملخص مالي يومي حسب نوع الحساب

## الفهارس المُحسَّنة

تم إنشاء فهارس محسنة للأداء على:
- جميع المفاتيح الأساسية والخارجية
- حقول البحث الرئيسية
- حقول التواريخ
- حقول المبالغ المالية
- فهارس النص الكامل للبحث

## الأمان

### المستخدمين المُنشأين
1. **travel_user**: مستخدم كامل الصلاحيات لقاعدة البيانات
2. **travel_readonly**: مستخدم للقراءة فقط (للتقارير)

### إعدادات الأمان
- تشفير كلمات المرور باستخدام bcrypt
- تسجيل جميع النشاطات في `activity_logs`
- إعدادات انتهاء الجلسة
- حد أقصى لمحاولات تسجيل الدخول

## البيانات الأولية

### الأدوار الافتراضية
- مدير عام (super_admin)
- مدير (admin)
- مدير قسم (manager)
- محاسب (accountant)
- موظف (employee)
- وكيل (agent)

### دليل الحسابات الأساسي
- الأصول (المتداولة والثابتة)
- الخصوم (المتداولة وطويلة الأجل)
- حقوق الملكية
- الإيرادات
- المصروفات

### إعدادات النظام الافتراضية
- معلومات الشركة
- الإعدادات المالية
- إعدادات الأمان
- إعدادات الإشعارات

## متطلبات النظام

### قاعدة البيانات
- MySQL 5.7+ أو MariaDB 10.2+
- دعم UTF8MB4
- InnoDB Engine

### الذاكرة والأداء
- الحد الأدنى: 256MB RAM مخصصة لـ MySQL
- مساحة القرص: 1GB للبيانات الأولية
- معدل الاتصالات: 200 اتصال متزامن

## النسخ الاحتياطي

### النسخ الاحتياطي اليدوي
```bash
mysqldump -u travel_user -p travel_accounting > backup_$(date +%Y%m%d_%H%M%S).sql
```

### استعادة النسخة الاحتياطية
```bash
mysql -u travel_user -p travel_accounting < backup_file.sql
```

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال
```
ERROR 1045 (28000): Access denied for user
```
**الحل**: تأكد من صحة اسم المستخدم وكلمة المرور

#### خطأ في الترميز
```
ERROR 1366: Incorrect string value
```
**الحل**: تأكد من استخدام UTF8MB4

#### خطأ في الصلاحيات
```
ERROR 1142: SELECT command denied
```
**الحل**: تأكد من منح الصلاحيات المناسبة للمستخدم

## الدعم والمساعدة

للحصول على المساعدة:
1. راجع ملف الأخطاء في MySQL
2. تحقق من إعدادات الاتصال
3. تأكد من تشغيل خدمة MySQL
4. راجع سجل النشاطات في `activity_logs`

## التحديثات المستقبلية

عند إضافة ميزات جديدة:
1. أنشئ ملف migration منفصل
2. احتفظ بنسخة احتياطية قبل التحديث
3. اختبر التحديث في بيئة التطوير أولاً

---

**ملاحظة**: هذه قاعدة بيانات شاملة جاهزة للإنتاج. تأكد من تغيير كلمات المرور الافتراضية في بيئة الإنتاج.