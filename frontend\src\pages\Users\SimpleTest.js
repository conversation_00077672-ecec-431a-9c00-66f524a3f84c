import React from 'react';

const SimpleTest = () => {
  return (
    <div style={{
      padding: '40px',
      textAlign: 'center',
      background: 'linear-gradient(135deg, #667eea, #764ba2)',
      color: 'white',
      minHeight: '100vh',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <h1 style={{ fontSize: '3rem', marginBottom: '20px' }}>
        🎉 النظام المتقدم يعمل!
      </h1>
      
      <p style={{ fontSize: '1.5rem', marginBottom: '40px' }}>
        تم إنشاء نظام إدارة المستخدمين المتقدم بنجاح
      </p>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '30px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)'
        }}>
          <h2>🚀 المستخدمين المتقدم</h2>
          <p>نظام إدارة مستخدمين متطور مع واجهة حديثة</p>
          <a href="/advanced-users" style={{
            background: 'rgba(255,255,255,0.2)',
            color: 'white',
            padding: '10px 20px',
            borderRadius: '10px',
            textDecoration: 'none',
            display: 'inline-block',
            marginTop: '10px',
            border: '2px solid rgba(255,255,255,0.3)'
          }}>
            انتقل للصفحة
          </a>
        </div>
        
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '30px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)'
        }}>
          <h2>🔐 مصفوفة الأذونات</h2>
          <p>تحكم تفاعلي في جميع أذونات النظام</p>
          <a href="/permissions-matrix" style={{
            background: 'rgba(255,255,255,0.2)',
            color: 'white',
            padding: '10px 20px',
            borderRadius: '10px',
            textDecoration: 'none',
            display: 'inline-block',
            marginTop: '10px',
            border: '2px solid rgba(255,255,255,0.3)'
          }}>
            انتقل للصفحة
          </a>
        </div>
        
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          padding: '30px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)'
        }}>
          <h2>👥 المستخدمين الأساسي</h2>
          <p>النظام التقليدي لإدارة المستخدمين</p>
          <a href="/users" style={{
            background: 'rgba(255,255,255,0.2)',
            color: 'white',
            padding: '10px 20px',
            borderRadius: '10px',
            textDecoration: 'none',
            display: 'inline-block',
            marginTop: '10px',
            border: '2px solid rgba(255,255,255,0.3)'
          }}>
            انتقل للصفحة
          </a>
        </div>
      </div>
      
      <div style={{
        marginTop: '40px',
        padding: '20px',
        background: 'rgba(39, 174, 96, 0.2)',
        borderRadius: '15px',
        border: '2px solid rgba(39, 174, 96, 0.3)',
        maxWidth: '800px',
        margin: '40px auto 0'
      }}>
        <h3>✅ المميزات المتقدمة:</h3>
        <ul style={{ textAlign: 'right', listStyle: 'none', padding: 0 }}>
          <li style={{ margin: '10px 0' }}>✅ 66 إذن مفصل في 7 فئات</li>
          <li style={{ margin: '10px 0' }}>✅ 4 أنماط عرض تفاعلية</li>
          <li style={{ margin: '10px 0' }}>✅ عمليات مجمعة متقدمة</li>
          <li style={{ margin: '10px 0' }}>✅ أمان متعدد المستويات</li>
          <li style={{ margin: '10px 0' }}>✅ واجهة مستخدم حديثة</li>
          <li style={{ margin: '10px 0' }}>✅ تصميم متجاوب</li>
        </ul>
      </div>
    </div>
  );
};

export default SimpleTest;