import React, { useState, useEffect } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from './BookingActions';

// مكون تشخيص الأخطاء
const ErrorDiagnosis = () => {
  const [errors, setErrors] = useState([]);
  const [showModal, setShowModal] = useState(false);

  // بيانات تجريبية للاختبار
  const testBooking = {
    id: 1,
    customerName: 'أحمد محمد',
    customerPhone: '0501234567',
    customerEmail: '<EMAIL>',
    date: '2024-01-15',
    bookingDate: '2024-01-15',
    status: 'confirmed',
    paymentStatus: 'paid',
    amount: 1500,
    currency: 'SAR',
    service: 'حجز طيران',
    destination: 'الرياض',
    packageType: 'اقتصادي',
    departureDate: '2024-02-01',
    returnDate: '2024-02-10',
    notes: 'حجز تجريبي للاختبار'
  };

  // التقاط الأخطاء
  useEffect(() => {
    const originalError = console.error;
    console.error = (...args) => {
      setErrors(prev => [...prev, {
        time: new Date().toLocaleTimeString(),
        message: args.join(' ')
      }]);
      originalError(...args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  // دوال الاختبار
  const testFunctions = {
    handleView: (booking) => {
      console.log('✅ دالة العرض تعمل:', booking.id);
      setShowModal(true);
    },
    handleEdit: (booking) => {
      console.log('✅ دالة التعديل تعمل:', booking.id);
    },
    handleDelete: (booking) => {
      console.log('✅ دالة الحذف تعمل:', booking.id);
    },
    handlePrint: (booking) => {
      console.log('✅ دالة الطباعة تعمل:', booking.id);
    },
    handleSavePDF: (booking) => {
      console.log('✅ دالة PDF تعمل:', booking.id);
    },
    handleBulkPrint: (ids) => {
      console.log('✅ الطباعة المجمعة تعمل:', ids);
    },
    handleBulkSavePDF: (ids) => {
      console.log('✅ PDF المجمع يعمل:', ids);
    },
    handleBulkDelete: (ids) => {
      console.log('✅ الحذف المجمع يعمل:', ids);
    },
    handleSelectAll: () => {
      console.log('✅ تحديد الكل يعمل');
    },
    handleClearSelection: () => {
      console.log('✅ إلغاء التحديد يعمل');
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#2c3e50', marginBottom: '30px' }}>
        🔍 تشخيص أخطاء نوافذ الحجوزات
      </h1>

      {/* عرض الأخطاء */}
      {errors.length > 0 && (
        <div style={{
          background: '#ffe6e6',
          border: '1px solid #ff9999',
          borderRadius: '8px',
          padding: '15px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#d32f2f', margin: '0 0 10px 0' }}>
            ❌ الأخطاء المكتشفة ({errors.length}):
          </h3>
          {errors.map((error, index) => (
            <div key={index} style={{
              background: 'white',
              padding: '8px',
              margin: '5px 0',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              <strong>{error.time}:</strong> {error.message}
            </div>
          ))}
          <button 
            onClick={() => setErrors([])}
            style={{
              background: '#f44336',
              color: 'white',
              border: 'none',
              padding: '5px 10px',
              borderRadius: '4px',
              marginTop: '10px',
              cursor: 'pointer'
            }}
          >
            مسح الأخطاء
          </button>
        </div>
      )}

      {/* اختبار BookingActions */}
      <div style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '12px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '15px' }}>
          🔧 اختبار BookingActions:
        </h3>
        <BookingActions
          booking={testBooking}
          onView={testFunctions.handleView}
          onEdit={testFunctions.handleEdit}
          onDelete={testFunctions.handleDelete}
          onPrint={testFunctions.handlePrint}
          onSavePDF={testFunctions.handleSavePDF}
        />
      </div>

      {/* اختبار BulkActions */}
      <div style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '12px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '15px' }}>
          📊 اختبار BulkActions:
        </h3>
        <BulkActions
          selectedBookings={[1, 2, 3]}
          onBulkPrint={testFunctions.handleBulkPrint}
          onBulkSavePDF={testFunctions.handleBulkSavePDF}
          onBulkDelete={testFunctions.handleBulkDelete}
          onSelectAll={testFunctions.handleSelectAll}
          onClearSelection={testFunctions.handleClearSelection}
        />
      </div>

      {/* اختبار النافذة المنبثقة */}
      <div style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '12px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '15px' }}>
          🪟 اختبار النافذة المنبثقة:
        </h3>
        <button
          onClick={() => setShowModal(true)}
          style={{
            background: 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 20px',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          فتح النافذة المنبثقة
        </button>
      </div>

      {/* معلومات النظام */}
      <div style={{
        background: '#e8f5e8',
        border: '1px solid #4caf50',
        borderRadius: '8px',
        padding: '15px'
      }}>
        <h3 style={{ color: '#2e7d32', margin: '0 0 10px 0' }}>
          ✅ معلومات النظام:
        </h3>
        <ul style={{ margin: 0, paddingRight: '20px' }}>
          <li>React Version: {React.version}</li>
          <li>Browser: {navigator.userAgent.split(' ')[0]}</li>
          <li>Platform: {navigator.platform}</li>
          <li>Language: {navigator.language}</li>
          <li>Online: {navigator.onLine ? 'متصل' : 'غير متصل'}</li>
        </ul>
      </div>

      {/* النافذة المنبثقة */}
      <BookingDetailsModal
        booking={testBooking}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onPrint={testFunctions.handlePrint}
        onSavePDF={testFunctions.handleSavePDF}
      />
    </div>
  );
};

export default ErrorDiagnosis;