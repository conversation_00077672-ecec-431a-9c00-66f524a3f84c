import React, { useState, useEffect } from 'react';
import './GeneralLedger.css';

const GeneralLedger = ({ accounts, transactions, onAddTransaction, currentUser }) => {
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [ledgerEntries, setLedgerEntries] = useState([]);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [transactionType, setTransactionType] = useState('manual');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  const [newTransaction, setNewTransaction] = useState({
    type: 'manual', // manual, revenue, expense, transfer
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    amount: 0,
    fromAccount: '',
    toAccount: '',
    category: '',
    notes: '',
    attachments: []
  });

  // حساب أرصدة الحسابات مع تفاصيل الحركات
  const calculateAccountBalance = (accountId) => {
    const accountTransactions = transactions.filter(trans => 
      trans.entries?.some(entry => entry.accountId === accountId) &&
      new Date(trans.date) >= new Date(dateRange.startDate) &&
      new Date(trans.date) <= new Date(dateRange.endDate)
    );

    let balance = 0;
    let debitTotal = 0;
    let creditTotal = 0;
    const movements = [];

    accountTransactions.forEach(trans => {
      trans.entries?.forEach(entry => {
        if (entry.accountId === accountId) {
          debitTotal += entry.debit || 0;
          creditTotal += entry.credit || 0;
          balance += (entry.debit || 0) - (entry.credit || 0);
          
          movements.push({
            id: trans.id,
            date: trans.date,
            description: trans.description,
            reference: trans.reference,
            debit: entry.debit || 0,
            credit: entry.credit || 0,
            balance: balance,
            user: trans.user || currentUser?.name || 'النظام',
            type: trans.type || 'manual',
            notes: trans.notes || ''
          });
        }
      });
    });

    return {
      balance,
      debitTotal,
      creditTotal,
      movements: movements.sort((a, b) => new Date(a.date) - new Date(b.date))
    };
  };

  // تصنيف الحسابات
  const categorizeAccounts = () => {
    const categories = {
      assets: { name: 'الأصول', accounts: [], color: '#27ae60', icon: '💰' },
      liabilities: { name: 'الخصوم', accounts: [], color: '#e74c3c', icon: '📉' },
      equity: { name: 'حقوق الملكية', accounts: [], color: '#3498db', icon: '🏛️' },
      revenue: { name: 'الإيرادات', accounts: [], color: '#f39c12', icon: '📈' },
      expenses: { name: 'المصروفات', accounts: [], color: '#9b59b6', icon: '💸' }
    };

    accounts.forEach(account => {
      if (account.type === 'detail' && categories[account.category]) {
        const accountData = calculateAccountBalance(account.id);
        categories[account.category].accounts.push({
          ...account,
          ...accountData
        });
      }
    });

    return categories;
  };

  // إنشاء قيد محاسبي تلقائي
  const createAutomaticEntry = (type, data) => {
    const entryId = `AUTO-${Date.now()}`;
    let entries = [];
    let description = '';

    switch (type) {
      case 'revenue':
        // إيراد: مدين النقدية/البنك، دائن الإيرادات
        entries = [
          { accountId: data.fromAccount, debit: data.amount, credit: 0 },
          { accountId: data.toAccount, debit: 0, credit: data.amount }
        ];
        description = `إيراد: ${data.description}`;
        break;

      case 'expense':
        // مصروف: مدين المصروفات، دائن النقدية/البنك
        entries = [
          { accountId: data.toAccount, debit: data.amount, credit: 0 },
          { accountId: data.fromAccount, debit: 0, credit: data.amount }
        ];
        description = `مصروف: ${data.description}`;
        break;

      case 'transfer':
        // تحويل: مدين الحساب المستقبل، دائن الحساب المرسل
        entries = [
          { accountId: data.toAccount, debit: data.amount, credit: 0 },
          { accountId: data.fromAccount, debit: 0, credit: data.amount }
        ];
        description = `تحويل: ${data.description}`;
        break;

      default:
        // قيد يدوي
        entries = [
          { accountId: data.fromAccount, debit: data.amount, credit: 0 },
          { accountId: data.toAccount, debit: 0, credit: data.amount }
        ];
        description = data.description;
    }

    const transaction = {
      id: entryId,
      date: data.date,
      description,
      reference: data.reference || entryId,
      type: type,
      user: currentUser?.name || 'النظام',
      notes: data.notes,
      entries: entries.map(entry => ({
        ...entry,
        accountName: accounts.find(acc => acc.id === entry.accountId)?.name || 'حساب غير معروف'
      })),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return transaction;
  };

  // حفظ المعاملة
  const handleSaveTransaction = () => {
    if (!newTransaction.fromAccount || !newTransaction.toAccount || !newTransaction.amount) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (newTransaction.fromAccount === newTransaction.toAccount) {
      alert('لا يمكن أن يكون الحساب المدين والدائن نفس الحساب');
      return;
    }

    const transaction = createAutomaticEntry(newTransaction.type, newTransaction);
    onAddTransaction(transaction);

    // إعادة تعيين النموذج
    setNewTransaction({
      type: 'manual',
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      amount: 0,
      fromAccount: '',
      toAccount: '',
      category: '',
      notes: '',
      attachments: []
    });

    setShowTransactionModal(false);
    alert('تم حفظ المعاملة بنجاح');
  };

  // تصفية الحسابات حسب البحث
  const filteredCategories = () => {
    const categories = categorizeAccounts();
    
    if (!searchTerm) return categories;

    Object.keys(categories).forEach(key => {
      categories[key].accounts = categories[key].accounts.filter(account =>
        account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.id.includes(searchTerm)
      );
    });

    return categories;
  };

  return (
    <div className="general-ledger">
      <div className="ledger-header">
        <div className="header-content">
          <h2>📚 دفتر الأستاذ العام</h2>
          <p>عرض تفصيلي لجميع حركات الحسابات والأرصدة</p>
        </div>
        
        <div className="header-controls">
          <div className="date-range">
            <label>من:</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
            />
            <label>إلى:</label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
            />
          </div>
          
          <button 
            className="btn btn-primary"
            onClick={() => setShowTransactionModal(true)}
          >
            ➕ معاملة جديدة
          </button>
        </div>
      </div>

      <div className="ledger-content">
        <div className="accounts-sidebar">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث في الحسابات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="accounts-tree">
            {Object.entries(filteredCategories()).map(([categoryKey, category]) => (
              <div key={categoryKey} className="account-category">
                <div 
                  className="category-header"
                  style={{ borderLeft: `4px solid ${category.color}` }}
                >
                  <span className="category-icon">{category.icon}</span>
                  <span className="category-name">{category.name}</span>
                  <span className="accounts-count">({category.accounts.length})</span>
                </div>

                <div className="category-accounts">
                  {category.accounts.map(account => (
                    <div
                      key={account.id}
                      className={`account-item ${selectedAccount?.id === account.id ? 'selected' : ''}`}
                      onClick={() => setSelectedAccount(account)}
                    >
                      <div className="account-info">
                        <div className="account-name">{account.name}</div>
                        <div className="account-code">{account.id}</div>
                      </div>
                      <div className="account-balance" style={{ color: category.color }}>
                        {account.balance.toLocaleString()} ر.س
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="ledger-details">
          {selectedAccount ? (
            <div className="account-ledger">
              <div className="account-header">
                <div className="account-title">
                  <h3>{selectedAccount.name}</h3>
                  <span className="account-code">رمز الحساب: {selectedAccount.id}</span>
                </div>
                
                <div className="account-summary">
                  <div className="summary-item debit">
                    <span className="label">إجمالي المدين</span>
                    <span className="value">{selectedAccount.debitTotal.toLocaleString()} ر.س</span>
                  </div>
                  <div className="summary-item credit">
                    <span className="label">إجمالي الدائن</span>
                    <span className="value">{selectedAccount.creditTotal.toLocaleString()} ر.س</span>
                  </div>
                  <div className="summary-item balance">
                    <span className="label">الرصيد النهائي</span>
                    <span className="value">{selectedAccount.balance.toLocaleString()} ر.س</span>
                  </div>
                </div>
              </div>

              <div className="movements-table">
                <table>
                  <thead>
                    <tr>
                      <th>التاريخ</th>
                      <th>رقم القيد</th>
                      <th>الوصف</th>
                      <th>المرجع</th>
                      <th>مدين</th>
                      <th>دائن</th>
                      <th>الرصيد</th>
                      <th>المستخدم</th>
                      <th>النوع</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedAccount.movements.map((movement, index) => (
                      <tr key={index} className={`movement-row ${movement.type}`}>
                        <td>{new Date(movement.date).toLocaleDateString('ar-SA')}</td>
                        <td className="entry-id">{movement.id}</td>
                        <td>{movement.description}</td>
                        <td>{movement.reference}</td>
                        <td className="debit">{movement.debit ? movement.debit.toLocaleString() : '-'}</td>
                        <td className="credit">{movement.credit ? movement.credit.toLocaleString() : '-'}</td>
                        <td className="balance">{movement.balance.toLocaleString()}</td>
                        <td className="user">{movement.user}</td>
                        <td className="type">
                          <span className={`type-badge ${movement.type}`}>
                            {movement.type === 'revenue' ? 'إيراد' : 
                             movement.type === 'expense' ? 'مصروف' :
                             movement.type === 'transfer' ? 'تحويل' : 'يدوي'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="no-selection">
              <div className="no-selection-content">
                <span className="icon">📊</span>
                <h3>اختر حساباً لعرض تفاصيله</h3>
                <p>انقر على أي حساب من القائمة الجانبية لعرض حركاته وأرصدته</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* نموذج إضافة معاملة جديدة */}
      {showTransactionModal && (
        <div className="modal-overlay">
          <div className="transaction-modal">
            <div className="modal-header">
              <h3>➕ معاملة مالية جديدة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowTransactionModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="transaction-type-selector">
                <label>نوع المعاملة:</label>
                <div className="type-buttons">
                  {[
                    { value: 'revenue', label: '📈 إيراد', color: '#27ae60' },
                    { value: 'expense', label: '💸 مصروف', color: '#e74c3c' },
                    { value: 'transfer', label: '🔄 تحويل', color: '#3498db' },
                    { value: 'manual', label: '✏️ قيد يدوي', color: '#9b59b6' }
                  ].map(type => (
                    <button
                      key={type.value}
                      className={`type-btn ${newTransaction.type === type.value ? 'active' : ''}`}
                      style={{ 
                        borderColor: type.color,
                        backgroundColor: newTransaction.type === type.value ? type.color : 'transparent',
                        color: newTransaction.type === type.value ? 'white' : type.color
                      }}
                      onClick={() => setNewTransaction(prev => ({ ...prev, type: type.value }))}
                    >
                      {type.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="form-grid">
                <div className="form-group">
                  <label>التاريخ *</label>
                  <input
                    type="date"
                    value={newTransaction.date}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, date: e.target.value }))}
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label>المبلغ *</label>
                  <input
                    type="number"
                    step="0.01"
                    value={newTransaction.amount}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    className="form-control"
                    placeholder="0.00"
                  />
                </div>

                <div className="form-group">
                  <label>
                    {newTransaction.type === 'revenue' ? 'حساب الاستلام *' :
                     newTransaction.type === 'expense' ? 'حساب الدفع *' :
                     'الحساب المدين *'}
                  </label>
                  <select
                    value={newTransaction.fromAccount}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, fromAccount: e.target.value }))}
                    className="form-control"
                  >
                    <option value="">اختر الحساب</option>
                    {accounts.filter(acc => acc.type === 'detail').map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.id})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label>
                    {newTransaction.type === 'revenue' ? 'حساب الإيراد *' :
                     newTransaction.type === 'expense' ? 'حساب المصروف *' :
                     'الحساب الدائن *'}
                  </label>
                  <select
                    value={newTransaction.toAccount}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, toAccount: e.target.value }))}
                    className="form-control"
                  >
                    <option value="">اختر الحساب</option>
                    {accounts.filter(acc => acc.type === 'detail').map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.id})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>الوصف *</label>
                  <input
                    type="text"
                    value={newTransaction.description}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    placeholder="وصف المعاملة"
                  />
                </div>

                <div className="form-group">
                  <label>المرجع</label>
                  <input
                    type="text"
                    value={newTransaction.reference}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, reference: e.target.value }))}
                    className="form-control"
                    placeholder="رقم الفاتورة أو المرجع"
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={newTransaction.notes}
                    onChange={(e) => setNewTransaction(prev => ({ ...prev, notes: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowTransactionModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleSaveTransaction}
              >
                💾 حفظ المعاملة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralLedger;