/* 🎨 أنماط مختار الثيم */

.theme-selector {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
}

.theme-selector:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

/* 🎯 رأس المختار */
.theme-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--glass-border);
}

.theme-selector-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 🔄 مفتاح الوضع التلقائي */
.auto-mode-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.auto-mode-toggle label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-600);
  transition: color var(--transition-fast);
}

.auto-mode-toggle label:hover {
  color: var(--primary-600);
}

.auto-mode-toggle input[type="checkbox"] {
  width: 18px;
  height: 18px;
  border: 2px solid var(--primary-300);
  border-radius: var(--radius-sm);
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  appearance: none;
  position: relative;
}

.auto-mode-toggle input[type="checkbox"]:checked {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.auto-mode-toggle input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 🌈 شبكة الثيمات */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

/* 🎨 خيار الثيم */
.theme-option {
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.theme-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.theme-option:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-300);
}

.theme-option:hover::before {
  opacity: 1;
}

.theme-option.active {
  border-color: var(--primary-500);
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.theme-option.active::after {
  content: '✓';
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 24px;
  height: 24px;
  background: var(--primary-500);
  color: white;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.theme-option:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 🎭 أيقونة الثيم */
.theme-icon {
  font-size: 2rem;
  margin-bottom: var(--space-3);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 📝 اسم الثيم */
.theme-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
  transition: color var(--transition-fast);
}

.theme-option:hover .theme-name {
  color: var(--primary-700);
}

/* 📄 وصف الثيم */
.theme-description {
  font-size: 0.75rem;
  color: var(--neutral-500);
  margin-bottom: var(--space-3);
  line-height: 1.4;
}

/* 🌈 ألوان الثيم */
.theme-colors {
  display: flex;
  justify-content: center;
  gap: var(--space-1);
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.theme-option:hover .color-dot {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .theme-selector {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .theme-option {
  background: rgba(15, 23, 42, 0.6);
  border-color: var(--glass-border);
}

[data-theme="dark"] .theme-name {
  color: var(--neutral-200);
}

[data-theme="dark"] .theme-description {
  color: var(--neutral-400);
}

[data-theme="dark"] .auto-mode-toggle label {
  color: var(--neutral-300);
}

[data-theme="dark"] .auto-mode-toggle label:hover {
  color: var(--primary-400);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .theme-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }
  
  .theme-option {
    padding: var(--space-4);
  }
  
  .theme-icon {
    font-size: 1.5rem;
    margin-bottom: var(--space-2);
  }
  
  .theme-name {
    font-size: 0.875rem;
  }
  
  .theme-description {
    font-size: 0.625rem;
  }
  
  .theme-selector-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .theme-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .theme-selector {
    padding: var(--space-4);
  }
}

/* 🎬 حركات خاصة */
.theme-option {
  animation: fadeInUp 0.6s ease-out;
}

.theme-option:nth-child(1) { animation-delay: 0ms; }
.theme-option:nth-child(2) { animation-delay: 100ms; }
.theme-option:nth-child(3) { animation-delay: 200ms; }
.theme-option:nth-child(4) { animation-delay: 300ms; }
.theme-option:nth-child(5) { animation-delay: 400ms; }
.theme-option:nth-child(6) { animation-delay: 500ms; }

/* 🔄 تأثير التبديل */
.theme-switching {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ✨ تأثير البريق عند التغيير */
.theme-changed {
  animation: themeChangeGlow 1s ease-out;
}

@keyframes themeChangeGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 🎯 تحسينات الأداء */
.theme-option {
  will-change: transform, box-shadow;
}

.color-dot {
  will-change: transform;
}

/* 🚫 تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  .theme-option {
    animation: none;
    transition: none;
  }
  
  .theme-option:hover {
    transform: none;
  }
  
  .color-dot {
    transition: none;
  }
  
  .theme-option:hover .color-dot {
    transform: none;
  }
}
