import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ModernNavbar, NavMenu, NavItem, Dropdown, DropdownItem } from '../UI/ModernNavigation';
import ModernButton, { IconButton } from '../UI/ModernButton';
import { ModernAlert, Toast, ToastContainer } from '../UI/ModernAlert';
import ThemeToggle, { SimpleThemeToggle } from '../UI/ThemeToggle';
import './ModernSystemLayout.css';

const ModernSystemLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [user] = useState({
    name: 'أحمد محمد',
    email: '<EMAIL>',
    avatar: '👤',
    role: 'مدير النظام'
  });
  
  const location = useLocation();
  const navigate = useNavigate();

  // Navigation items
  const navigationItems = [
    {
      path: '/dashboard',
      label: 'لوحة التحكم',
      icon: '🏠',
      badge: null
    },
    {
      path: '/customers',
      label: 'العملاء',
      icon: '👥'
    },
    {
      path: '/bookings',
      label: 'الحجوزات',
      icon: '📋',
      badge: '12'
    },
    {
      path: '/sales',
      label: 'المبيعات',
      icon: '💰',
      submenu: [
        { path: '/sales', label: 'نظرة عامة' },
        { path: '/sales/management', label: 'إدارة المبيعات' },
        { path: '/sales/invoices', label: 'الفواتير' },
        { path: '/sales/customers', label: 'العملاء' },
        { path: '/sales/reports', label: 'التقارير' },
        { path: '/sales/payments', label: 'المدفوعات' },
        { path: '/sales/settings', label: 'الإعدادات' }
      ]
    },
    {
      path: '/purchases',
      label: 'المشتريات',
      icon: '🛒'
    },
    {
      path: '/inventory',
      label: 'المخزون',
      icon: '📦',
      submenu: [
        { path: '/inventory', label: 'نظرة عامة' },
        { path: '/inventory/visas', label: 'مخزون التأشيرات' }
      ]
    },
    {
      path: '/finance',
      label: 'المالية',
      icon: '💳'
    },
    {
      path: '/suppliers',
      label: 'الموردين',
      icon: '🏢'
    },
    {
      path: '/agents',
      label: 'الوكلاء',
      icon: '🤝'
    },
    {
      path: '/templates',
      label: 'القوالب',
      icon: '📄'
    },
    {
      path: '/reports',
      label: 'التقارير',
      icon: '📊'
    },
    {
      path: '/accounts',
      label: 'الحسابات',
      icon: '🏦'
    },
    {
      path: '/users',
      label: 'إدارة المستخدمين',
      icon: '👥',
      submenu: [
        { path: '/users', label: 'المستخدمين الأساسي' },
        { path: '/advanced-users', label: 'المستخدمين المتقدم' },
        { path: '/roles-permissions', label: 'الأدوار والصلاحيات' },
        { path: '/permissions-matrix', label: 'مصفوفة الأذونات' },
        { path: '/test-advanced', label: '🧪 اختبار النظام المتقدم' },
        { path: '/simple-test-users', label: '🎉 عرض النظام المتقدم' }
      ]
    },
    {
      path: '/settings',
      label: 'الإعدادات',
      icon: '⚙️'
    }
  ];

  // Sample notifications
  useEffect(() => {
    setNotifications([
      {
        id: 1,
        type: 'info',
        title: 'حجز جديد',
        message: 'تم استلام حجز جديد من العميل أحمد محمد',
        time: '5 دقائق'
      },
      {
        id: 2,
        type: 'warning',
        title: 'دفعة معلقة',
        message: 'هناك دفعة معلقة تحتاج إلى مراجعة',
        time: '15 دقيقة'
      },
      {
        id: 3,
        type: 'success',
        title: 'تم التحديث',
        message: 'تم تحديث النظام بنجاح',
        time: '1 ساعة'
      }
    ]);
  }, []);

  const handleLogout = () => {
    // مسح بيانات تسجيل الدخول
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    
    // التوجه إلى صفحة تسجيل الدخول
    navigate('/login');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const isActivePath = (path) => {
    if (path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  const renderNavItem = (item) => {
    if (item.submenu) {
      return (
        <Dropdown
          key={item.path}
          trigger={
            <div className={`nav-item-trigger ${isActivePath(item.path) ? 'active' : ''}`}>
              <span className="nav-item-icon">{item.icon}</span>
              <span className="nav-item-text">{item.label}</span>
              <span className="nav-item-arrow">▼</span>
              {item.badge && <span className="nav-item-badge">{item.badge}</span>}
            </div>
          }
          position="bottom-right"
        >
          {item.submenu.map(subItem => (
            <DropdownItem
              key={subItem.path}
              to={subItem.path}
              onClick={() => setMobileMenuOpen(false)}
            >
              {subItem.label}
            </DropdownItem>
          ))}
        </Dropdown>
      );
    }

    return (
      <NavItem
        key={item.path}
        to={item.path}
        active={isActivePath(item.path)}
        icon={item.icon}
        badge={item.badge}
        onClick={() => setMobileMenuOpen(false)}
      >
        {item.label}
      </NavItem>
    );
  };

  return (
    <div className="modern-system-layout">
      {/* Top Navigation */}
      <ModernNavbar
        variant="glass"
        fixed
        className="system-navbar"
        brand={
          <Link to="/dashboard" className="system-brand">
            <div className="brand-logo">✈️</div>
            <div className="brand-text">
              <div className="brand-name">شراء للسفر</div>
              <div className="brand-subtitle">نظام إدارة السفر</div>
            </div>
          </Link>
        }
      >
        <div className="navbar-content">
          {/* Desktop Navigation */}
          <div className="desktop-nav">
            <NavMenu direction="horizontal">
              {navigationItems.slice(0, 6).map(renderNavItem)}
              
              <Dropdown
                className="more-menu-dropdown"
                position="bottom-left"
                trigger={
                  <div className="nav-item-trigger">
                    <span className="nav-item-icon">⋯</span>
                    <span className="nav-item-text">المزيد</span>
                    <span className="nav-item-arrow">▼</span>
                  </div>
                }
              >
                {navigationItems.slice(6).map(item => (
                  <DropdownItem key={item.path} to={item.path} icon={item.icon}>
                    {item.label}
                  </DropdownItem>
                ))}
              </Dropdown>
            </NavMenu>
          </div>

          {/* Right Side Actions */}
          <div className="navbar-actions">
            {/* Search */}
            <IconButton
              icon="🔍"
              variant="ghost"
              size="sm"
              tooltip="البحث"
              onClick={() => console.log('Search')}
            />

            {/* Theme Toggle */}
            <div className="theme-toggle-wrapper">
              <ThemeToggle showLabel={false} size="small" />
            </div>

            {/* Notifications */}
            <Dropdown
              trigger={
                <div className="notification-trigger">
                  <IconButton
                    icon="🔔"
                    variant="ghost"
                    size="sm"
                    tooltip="الإشعارات"
                  />
                  {notifications.length > 0 && (
                    <span className="notification-badge">{notifications.length}</span>
                  )}
                </div>
              }
              position="bottom-right"
            >
              <div className="notifications-dropdown">
                <div className="notifications-header">
                  <h4>الإشعارات</h4>
                  <ModernButton variant="ghost" size="xs">
                    تحديد الكل كمقروء
                  </ModernButton>
                </div>
                
                <div className="notifications-list">
                  {notifications.map(notification => (
                    <div key={notification.id} className="notification-item">
                      <div className={`notification-icon notification-icon--${notification.type}`}>
                        {notification.type === 'info' ? 'ℹ️' : 
                         notification.type === 'warning' ? '⚠️' : '✅'}
                      </div>
                      <div className="notification-content">
                        <div className="notification-title">{notification.title}</div>
                        <div className="notification-message">{notification.message}</div>
                        <div className="notification-time">{notification.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="notifications-footer">
                  <Link to="/notifications" className="view-all-notifications">
                    عرض جميع الإشعارات
                  </Link>
                </div>
              </div>
            </Dropdown>

            {/* User Menu */}
            <Dropdown
              trigger={
                <div className="user-trigger">
                  <div className="user-avatar">{user.avatar}</div>
                  <div className="user-info">
                    <div className="user-name">{user.name}</div>
                    <div className="user-role">{user.role}</div>
                  </div>
                  <span className="user-arrow">▼</span>
                </div>
              }
              position="bottom-right"
            >
              <div className="user-dropdown">
                <div className="user-dropdown-header">
                  <div className="user-avatar-large">{user.avatar}</div>
                  <div className="user-details">
                    <div className="user-name-large">{user.name}</div>
                    <div className="user-email">{user.email}</div>
                  </div>
                </div>
                
                <div className="user-dropdown-menu">
                  <DropdownItem to="/profile" icon="👤">
                    الملف الشخصي
                  </DropdownItem>
                  <DropdownItem to="/settings" icon="⚙️">
                    الإعدادات
                  </DropdownItem>
                  <DropdownItem to="/help" icon="❓">
                    المساعدة
                  </DropdownItem>
                  <div className="dropdown-divider" />
                  <DropdownItem onClick={handleLogout} icon="🚪">
                    تسجيل الخروج
                  </DropdownItem>
                </div>
              </div>
            </Dropdown>

            {/* Mobile Menu Toggle */}
            <IconButton
              icon={mobileMenuOpen ? "✕" : "☰"}
              variant="ghost"
              size="sm"
              className="mobile-menu-toggle"
              onClick={toggleMobileMenu}
            />
          </div>
        </div>
      </ModernNavbar>

      {/* Mobile Navigation Overlay */}
      {mobileMenuOpen && (
        <div className="mobile-nav-overlay" onClick={() => setMobileMenuOpen(false)}>
          <div className="mobile-nav-content" onClick={(e) => e.stopPropagation()}>
            <div className="mobile-nav-header">
              <h3>القائمة الرئيسية</h3>
              <IconButton
                icon="✕"
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(false)}
              />
            </div>
            
            <NavMenu direction="vertical" className="mobile-nav-menu">
              {navigationItems.map(renderNavItem)}
            </NavMenu>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="system-main">
        <div className="main-content">
          {children}
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer position="top-right" />

      {/* Footer */}
      <footer className="system-footer">
        <div className="footer-content">
          <div className="footer-left">
            <p>&copy; 2024 شراء للسفر والسياحة. جميع الحقوق محفوظة.</p>
          </div>
          <div className="footer-right">
            <Link to="/privacy">سياسة الخصوصية</Link>
            <Link to="/terms">شروط الاستخدام</Link>
            <Link to="/support">الدعم الفني</Link>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ModernSystemLayout;