// سكريبت لتحديث جميع صفحات الحجوزات
const fs = require('fs');
const path = require('path');

const bookingPages = [
  'UmrahBookingPage.js',
  'PassportBookingPage.js', 
  'BusBookingPage.js',
  'CarBookingPage.js',
  'DocumentAuthenticationPage.js'
];

const bookingTypes = {
  'UmrahBookingPage.js': 'العمرة',
  'PassportBookingPage.js': 'الجوازات',
  'BusBookingPage.js': 'الباصات', 
  'CarBookingPage.js': 'السيارات',
  'DocumentAuthenticationPage.js': 'تعميد الوثائق'
};

const basePath = './src/pages/Bookings/';

// إضافة الاستيرادات المطلوبة
const importsToAdd = `import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';`;

// إضافة المتغيرات المطلوبة
const stateVariablesToAdd = `  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);`;

// دوال العمليات الجديدة
const functionsToAdd = (bookingType) => `
  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    handleDeleteBooking(booking.id);
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, '${bookingType}');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, '${bookingType}');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, '${bookingType}');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, '${bookingType}');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(\`هل أنت متأكد من حذف \${selectedIds.length} حجز؟\`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };`;

// مكون العمليات المجمعة
const bulkActionsComponent = `      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

`;

// نافذة التفاصيل
const detailsModal = `
      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />`;

console.log('تحديث صفحات الحجوزات...');
console.log('يرجى تطبيق هذه التغييرات يدوياً على كل صفحة:');
console.log('');

bookingPages.forEach(page => {
  const bookingType = bookingTypes[page];
  console.log(`=== ${page} (${bookingType}) ===`);
  console.log('1. إضافة الاستيرادات بعد السطر الأول:');
  console.log(importsToAdd);
  console.log('');
  console.log('2. إضافة المتغيرات بعد متغيرات الحالة الموجودة:');
  console.log(stateVariablesToAdd);
  console.log('');
  console.log('3. إضافة الدوال بعد handleDeleteBooking:');
  console.log(functionsToAdd(bookingType));
  console.log('');
  console.log('4. إضافة عمود التحديد في رأس الجدول:');
  console.log(`                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>`);
  console.log('');
  console.log('5. إضافة عمود التحديد في كل صف:');
  console.log(`                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedBookings.includes(booking.id)}
                      onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>`);
  console.log('');
  console.log('6. استبدال أزرار الإجراءات بـ:');
  console.log(`                    <BookingActions
                      booking={booking}
                      onView={handleViewBooking}
                      onEdit={handleEditBooking}
                      onDelete={handleDeleteSingleBooking}
                      onPrint={handlePrintBooking}
                      onSavePDF={handleSavePDFBooking}
                    />`);
  console.log('');
  console.log('7. إضافة مكون العمليات المجمعة قبل الجدول:');
  console.log(bulkActionsComponent);
  console.log('');
  console.log('8. إضافة نافذة التفاصيل قبل export default:');
  console.log(detailsModal);
  console.log('');
  console.log('=====================================');
  console.log('');
});

console.log('تم إنشاء التعليمات لتحديث جميع الصفحات!');