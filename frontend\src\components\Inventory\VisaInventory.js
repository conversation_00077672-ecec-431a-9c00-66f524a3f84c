import React, { useState, useEffect } from 'react';
import { useNotifications } from '../UI/EnhancedNotifications';
import { useShortcuts } from '../UI/KeyboardShortcuts';
import './VisaInventory.css';

// 📋 مكون إدارة مخزون التأشيرات
const VisaInventory = () => {
  const { success, error, warning, info } = useNotifications();
  const { registerShortcut, unregisterShortcut } = useShortcuts();

  // 🏪 حالة المخزون
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedItems, setSelectedItems] = useState([]);

  // 📝 بيانات النموذج
  const [newVisa, setNewVisa] = useState({
    issueNumber: '',      // رقم الصادر
    registryNumber: '',   // رقم السجل
    visaType: 'work',     // نوع التأشيرة
    nationality: '',      // الجنسية
    profession: '',       // المهنة
    duration: '90',       // مدة الإقامة
    entries: 'single',    // عدد الدخول
    issueDate: '',        // تاريخ الإصدار
    expiryDate: '',       // تاريخ الانتهاء
    sponsorName: '',      // اسم الكفيل
    sponsorId: '',        // هوية الكفيل
    cost: '',             // التكلفة
    sellingPrice: '',     // سعر البيع
    status: 'available',  // الحالة
    notes: ''             // ملاحظات
  });

  // 📊 تحميل بيانات المخزون
  useEffect(() => {
    loadInventory();
  }, []);

  // ⌨️ تسجيل اختصارات لوحة المفاتيح
  useEffect(() => {
    registerShortcut('ctrl+shift+v', 'newVisa', 'إضافة تأشيرة جديدة للمخزون', 'inventory');
    registerShortcut('ctrl+shift+f', 'searchVisa', 'البحث في مخزون التأشيرات', 'inventory');

    const handleShortcutAction = (event) => {
      switch (event.detail.action) {
        case 'newVisa':
          setShowAddForm(true);
          break;
        case 'searchVisa':
          document.querySelector('.visa-search-input')?.focus();
          break;
      }
    };

    document.addEventListener('shortcutAction', handleShortcutAction);

    return () => {
      unregisterShortcut('ctrl+shift+v');
      unregisterShortcut('ctrl+shift+f');
      document.removeEventListener('shortcutAction', handleShortcutAction);
    };
  }, [registerShortcut, unregisterShortcut]);

  // 📊 تحميل المخزون
  const loadInventory = async () => {
    setLoading(true);
    try {
      // محاكاة تحميل البيانات
      setTimeout(() => {
        const mockInventory = [
          {
            id: 1,
            issueNumber: '********',
            registryNumber: '********',
            visaType: 'work',
            nationality: 'بنغلاديش',
            profession: 'عامل',
            duration: '90',
            entries: 'single',
            issueDate: '2024-01-15',
            expiryDate: '2024-04-15',
            sponsorName: 'شركة الرياض للتجارة',
            sponsorId: '1234567890',
            cost: 500,
            sellingPrice: 800,
            status: 'available',
            notes: 'تأشيرة عمل عادية',
            createdAt: new Date().toISOString()
          },
          {
            id: 2,
            issueNumber: 'V2024002',
            registryNumber: 'R2024002',
            visaType: 'visit',
            nationality: 'مصر',
            profession: 'زيارة عائلية',
            duration: '30',
            entries: 'single',
            issueDate: '2024-01-20',
            expiryDate: '2024-02-20',
            sponsorName: 'أحمد محمد علي',
            sponsorId: '9876543210',
            cost: 200,
            sellingPrice: 350,
            status: 'reserved',
            notes: 'محجوزة للعميل أحمد',
            createdAt: new Date().toISOString()
          }
        ];
        
        setInventory(mockInventory);
        setLoading(false);
        success('تم تحميل مخزون التأشيرات بنجاح');
      }, 1000);
    } catch (err) {
      error('فشل في تحميل مخزون التأشيرات');
      setLoading(false);
    }
  };

  // ➕ إضافة تأشيرة جديدة للمخزون
  const handleAddVisa = async (e) => {
    e.preventDefault();
    
    // التحقق من البيانات
    if (!newVisa.issueNumber || !newVisa.registryNumber) {
      warning('رقم الصادر ورقم السجل مطلوبان');
      return;
    }

    // التحقق من عدم التكرار
    const existingVisa = inventory.find(v => 
      v.issueNumber === newVisa.issueNumber || 
      v.registryNumber === newVisa.registryNumber
    );

    if (existingVisa) {
      error('رقم الصادر أو رقم السجل موجود مسبقاً');
      return;
    }

    try {
      const visa = {
        ...newVisa,
        id: Date.now(),
        cost: parseFloat(newVisa.cost) || 0,
        sellingPrice: parseFloat(newVisa.sellingPrice) || 0,
        createdAt: new Date().toISOString()
      };

      setInventory([visa, ...inventory]);
      
      // إعادة تعيين النموذج
      setNewVisa({
        issueNumber: '',
        registryNumber: '',
        visaType: 'work',
        nationality: '',
        profession: '',
        duration: '90',
        entries: 'single',
        issueDate: '',
        expiryDate: '',
        sponsorName: '',
        sponsorId: '',
        cost: '',
        sellingPrice: '',
        status: 'available',
        notes: ''
      });

      setShowAddForm(false);
      
      success(`تم إضافة التأشيرة ${visa.issueNumber} للمخزون بنجاح`, {
        actions: [
          {
            label: 'عرض التفاصيل',
            onClick: () => handleViewVisa(visa)
          }
        ]
      });

      // 🔗 إشعار نظام العملاء بالتأشيرة الجديدة
      window.dispatchEvent(new CustomEvent('visaAdded', {
        detail: { visa }
      }));

    } catch (err) {
      error('فشل في إضافة التأشيرة للمخزون');
    }
  };

  // 🔍 البحث والفلترة
  const filteredInventory = inventory.filter(visa => {
    const matchesSearch = 
      visa.issueNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      visa.registryNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      visa.nationality.toLowerCase().includes(searchTerm.toLowerCase()) ||
      visa.sponsorName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' || visa.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // 📊 إحصائيات المخزون
  const stats = {
    total: inventory.length,
    available: inventory.filter(v => v.status === 'available').length,
    reserved: inventory.filter(v => v.status === 'reserved').length,
    used: inventory.filter(v => v.status === 'used').length,
    expired: inventory.filter(v => new Date(v.expiryDate) < new Date()).length
  };

  // 🎯 تغيير حالة التأشيرة
  const handleStatusChange = (id, newStatus) => {
    setInventory(inventory.map(visa => 
      visa.id === id ? { ...visa, status: newStatus } : visa
    ));
    
    const visa = inventory.find(v => v.id === id);
    info(`تم تغيير حالة التأشيرة ${visa?.issueNumber} إلى ${getStatusText(newStatus)}`);
  };

  // 📝 الحصول على نص الحالة
  const getStatusText = (status) => {
    const statusMap = {
      available: 'متاحة',
      reserved: 'محجوزة',
      used: 'مستخدمة',
      expired: 'منتهية الصلاحية'
    };
    return statusMap[status] || status;
  };

  // 👁️ عرض تفاصيل التأشيرة
  const handleViewVisa = (visa) => {
    // فتح مودال التفاصيل
    console.log('عرض تفاصيل التأشيرة:', visa);
  };

  if (loading) {
    return (
      <div className="visa-inventory-loading">
        <div className="loading-spinner-advanced"></div>
        <p>جاري تحميل مخزون التأشيرات...</p>
      </div>
    );
  }

  return (
    <div className="visa-inventory">
      {/* 🎯 رأس الصفحة */}
      <div className="visa-inventory-header">
        <div className="header-title">
          <h1>📋 مخزون التأشيرات</h1>
          <p>إدارة وتتبع جميع التأشيرات المتاحة</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إضافة تأشيرة جديدة
          </button>
        </div>
      </div>

      {/* 📊 إحصائيات سريعة */}
      <div className="visa-stats">
        <div className="stat-card total">
          <div className="stat-icon">📋</div>
          <div className="stat-content">
            <h3>إجمالي التأشيرات</h3>
            <div className="stat-value">{stats.total}</div>
          </div>
        </div>
        <div className="stat-card available">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h3>متاحة</h3>
            <div className="stat-value">{stats.available}</div>
          </div>
        </div>
        <div className="stat-card reserved">
          <div className="stat-icon">🔒</div>
          <div className="stat-content">
            <h3>محجوزة</h3>
            <div className="stat-value">{stats.reserved}</div>
          </div>
        </div>
        <div className="stat-card used">
          <div className="stat-icon">✔️</div>
          <div className="stat-content">
            <h3>مستخدمة</h3>
            <div className="stat-value">{stats.used}</div>
          </div>
        </div>
      </div>

      {/* 🔍 البحث والفلاتر */}
      <div className="visa-filters">
        <div className="search-container">
          <input
            type="text"
            className="visa-search-input"
            placeholder="البحث برقم الصادر، رقم السجل، الجنسية، أو اسم الكفيل..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="status-filter"
        >
          <option value="all">جميع الحالات</option>
          <option value="available">متاحة</option>
          <option value="reserved">محجوزة</option>
          <option value="used">مستخدمة</option>
          <option value="expired">منتهية الصلاحية</option>
        </select>
      </div>

      {/* 📋 جدول المخزون */}
      <div className="visa-inventory-table">
        <table>
          <thead>
            <tr>
              <th>رقم الصادر</th>
              <th>رقم السجل</th>
              <th>النوع</th>
              <th>الجنسية</th>
              <th>المهنة</th>
              <th>اسم الكفيل</th>
              <th>تاريخ الانتهاء</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredInventory.map(visa => (
              <tr key={visa.id} className={`visa-row ${visa.status}`}>
                <td className="issue-number">{visa.issueNumber}</td>
                <td className="registry-number">{visa.registryNumber}</td>
                <td>{visa.visaType === 'work' ? 'عمل' : 'زيارة'}</td>
                <td>{visa.nationality}</td>
                <td>{visa.profession}</td>
                <td>{visa.sponsorName}</td>
                <td>{new Date(visa.expiryDate).toLocaleDateString('ar-SA')}</td>
                <td>
                  <select
                    value={visa.status}
                    onChange={(e) => handleStatusChange(visa.id, e.target.value)}
                    className={`status-select ${visa.status}`}
                  >
                    <option value="available">متاحة</option>
                    <option value="reserved">محجوزة</option>
                    <option value="used">مستخدمة</option>
                    <option value="expired">منتهية</option>
                  </select>
                </td>
                <td>
                  <div className="visa-actions">
                    <button
                      className="action-btn view"
                      onClick={() => handleViewVisa(visa)}
                      title="عرض التفاصيل"
                    >
                      👁️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* ➕ نموذج إضافة تأشيرة جديدة */}
      {showAddForm && (
        <div className="visa-form-overlay">
          <div className="visa-form-modal">
            <div className="form-header">
              <h2>➕ إضافة تأشيرة جديدة للمخزون</h2>
              <button 
                className="close-btn"
                onClick={() => setShowAddForm(false)}
              >
                ✕
              </button>
            </div>
            
            <form onSubmit={handleAddVisa} className="visa-form">
              <div className="form-grid">
                {/* معلومات أساسية */}
                <div className="form-section">
                  <h3>📋 المعلومات الأساسية</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>رقم الصادر *</label>
                      <input
                        type="text"
                        value={newVisa.issueNumber}
                        onChange={(e) => setNewVisa({...newVisa, issueNumber: e.target.value})}
                        placeholder="********"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label>رقم السجل *</label>
                      <input
                        type="text"
                        value={newVisa.registryNumber}
                        onChange={(e) => setNewVisa({...newVisa, registryNumber: e.target.value})}
                        placeholder="********"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label>نوع التأشيرة</label>
                      <select
                        value={newVisa.visaType}
                        onChange={(e) => setNewVisa({...newVisa, visaType: e.target.value})}
                      >
                        <option value="work">عمل</option>
                        <option value="visit">زيارة</option>
                        <option value="business">تجارية</option>
                        <option value="family">عائلية</option>
                      </select>
                    </div>
                    <div className="form-group">
                      <label>الجنسية</label>
                      <input
                        type="text"
                        value={newVisa.nationality}
                        onChange={(e) => setNewVisa({...newVisa, nationality: e.target.value})}
                        placeholder="بنغلاديش"
                      />
                    </div>
                  </div>
                </div>

                {/* معلومات الكفيل */}
                <div className="form-section">
                  <h3>👤 معلومات الكفيل</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>اسم الكفيل</label>
                      <input
                        type="text"
                        value={newVisa.sponsorName}
                        onChange={(e) => setNewVisa({...newVisa, sponsorName: e.target.value})}
                        placeholder="شركة الرياض للتجارة"
                      />
                    </div>
                    <div className="form-group">
                      <label>هوية الكفيل</label>
                      <input
                        type="text"
                        value={newVisa.sponsorId}
                        onChange={(e) => setNewVisa({...newVisa, sponsorId: e.target.value})}
                        placeholder="1234567890"
                      />
                    </div>
                  </div>
                </div>

                {/* التواريخ والتكلفة */}
                <div className="form-section">
                  <h3>📅 التواريخ والتكلفة</h3>
                  <div className="form-row">
                    <div className="form-group">
                      <label>تاريخ الإصدار</label>
                      <input
                        type="date"
                        value={newVisa.issueDate}
                        onChange={(e) => setNewVisa({...newVisa, issueDate: e.target.value})}
                      />
                    </div>
                    <div className="form-group">
                      <label>تاريخ الانتهاء</label>
                      <input
                        type="date"
                        value={newVisa.expiryDate}
                        onChange={(e) => setNewVisa({...newVisa, expiryDate: e.target.value})}
                      />
                    </div>
                  </div>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label>التكلفة (ريال)</label>
                      <input
                        type="number"
                        value={newVisa.cost}
                        onChange={(e) => setNewVisa({...newVisa, cost: e.target.value})}
                        placeholder="500"
                      />
                    </div>
                    <div className="form-group">
                      <label>سعر البيع (ريال)</label>
                      <input
                        type="number"
                        value={newVisa.sellingPrice}
                        onChange={(e) => setNewVisa({...newVisa, sellingPrice: e.target.value})}
                        placeholder="800"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="form-actions">
                <button type="submit" className="btn btn-primary">
                  💾 حفظ التأشيرة
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowAddForm(false)}
                >
                  ❌ إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisaInventory;
