@echo off
chcp 65001 >nul
title نظام شراء للسفر والسياحة
cd /d "c:\Users\<USER>\Desktop\sharaubtravelsoft"

REM تفضيل PowerShell 7 إن وجد
where pwsh >nul 2>nul
if %errorlevel%==0 (
    pwsh -NoLogo -NoProfile -ExecutionPolicy Bypass -File "c:\Users\<USER>\Desktop\sharaubtravelsoft\start-sharau-app.ps1"
) else (
    powershell.exe -NoLogo -NoProfile -ExecutionPolicy Bypass -File "c:\Users\<USER>\Desktop\sharaubtravelsoft\start-sharau-app.ps1"
)

pause
