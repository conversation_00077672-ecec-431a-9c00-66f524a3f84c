import React, { useState } from 'react';
import { 
  ModernGrid, 
  ModernFlex,
  ModernCard, 
  StatsCard, 
  MetricCard,
  ModernButton, 
  IconButton, 
  ButtonGroup,
  ModernAlert, 
  StatusBadge, 
  Toast,
  Tabs, 
  TabPanel, 
  Dropdown, 
  DropdownItem,
  ProgressCircle, 
  ProgressBar, 
  BarChart, 
  LineChart, 
  DonutChart, 
  MetricDisplay,
  GaugeChart,
  ModernForm, 
  ModernInput, 
  ModernSelect, 
  ModernTextarea 
} from '../../components/UI';
import './ComponentsDemo.css';

const ComponentsDemo = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [showToast, setShowToast] = useState(false);

  // Sample data for charts
  const salesData = [
    { label: 'يناير', value: 180000 },
    { label: 'فبراير', value: 220000 },
    { label: 'مارس', value: 195000 },
    { label: 'أبريل', value: 280000 },
    { label: 'مايو', value: 310000 },
    { label: 'يونيو', value: 290000 }
  ];

  const servicesData = [
    { label: 'حجز طيران', value: 45, color: '#3B82F6' },
    { label: 'حجز فنادق', value: 30, color: '#10B981' },
    { label: 'تأشيرات', value: 15, color: '#F59E0B' },
    { label: 'باقات سياحية', value: 10, color: '#EF4444' }
  ];

  const trendData = [65, 72, 68, 85, 92, 88, 95, 89, 94, 98, 85, 92];

  return (
    <div className="components-demo">
      <div className="demo-header">
        <h1>معرض المكونات الحديثة</h1>
        <p>استعراض شامل لجميع مكونات واجهة المستخدم الحديثة</p>
      </div>

      <Tabs defaultTab={0} variant="pills" className="demo-tabs">
        {/* Cards Tab */}
        <TabPanel label="البطاقات" icon="🃏">
          <div className="demo-section">
            <h2>البطاقات الأساسية</h2>
            <ModernGrid cols={3} gap="lg" responsive>
              <ModernCard>
                <h3>بطاقة أساسية</h3>
                <p>هذه بطاقة أساسية تحتوي على محتوى نصي بسيط.</p>
                <ModernButton variant="primary" size="sm">
                  إجراء
                </ModernButton>
              </ModernCard>

              <ModernCard variant="glass">
                <h3>بطاقة زجاجية</h3>
                <p>بطاقة بتأثير زجاجي شفاف وحديث.</p>
                <ModernButton variant="outline" size="sm">
                  تفاصيل
                </ModernButton>
              </ModernCard>

              <ModernCard variant="gradient">
                <h3>بطاقة متدرجة</h3>
                <p>بطاقة بخلفية متدرجة جميلة.</p>
                <ModernButton variant="white" size="sm">
                  المزيد
                </ModernButton>
              </ModernCard>
            </ModernGrid>

            <h2>بطاقات الإحصائيات</h2>
            <ModernGrid cols={4} gap="lg" responsive>
              <StatsCard
                title="إجمالي الإيرادات"
                value="2,450,000 ر.س"
                change="+12.5%"
                changeType="positive"
                icon="💰"
                color="success"
                trend={trendData}
              />
              
              <StatsCard
                title="إجمالي الحجوزات"
                value="1,234"
                change="+8.2%"
                changeType="positive"
                icon="📋"
                color="primary"
              />
              
              <StatsCard
                title="العملاء النشطون"
                value="856"
                change="+15.3%"
                changeType="positive"
                icon="👥"
                color="info"
              />
              
              <StatsCard
                title="المدفوعات المعلقة"
                value="45,000 ر.س"
                change="-5.1%"
                changeType="negative"
                icon="⏳"
                color="warning"
              />
            </ModernGrid>

            <h2>بطاقات المقاييس</h2>
            <ModernGrid cols={2} gap="lg" responsive>
              <MetricCard
                title="معدل التحويل"
                value="68%"
                description="نسبة تحويل الزوار إلى عملاء"
                trend="up"
                color="success"
              />
              
              <MetricCard
                title="متوسط وقت الاستجابة"
                value="2.3 ثانية"
                description="متوسط وقت استجابة النظام"
                trend="down"
                color="info"
              />
            </ModernGrid>
          </div>
        </TabPanel>

        {/* Buttons Tab */}
        <TabPanel label="الأزرار" icon="🔘">
          <div className="demo-section">
            <h2>الأزرار الأساسية</h2>
            <ModernFlex gap="md" wrap>
              <ModernButton variant="primary">أساسي</ModernButton>
              <ModernButton variant="secondary">ثانوي</ModernButton>
              <ModernButton variant="success">نجاح</ModernButton>
              <ModernButton variant="warning">تحذير</ModernButton>
              <ModernButton variant="error">خطأ</ModernButton>
              <ModernButton variant="info">معلومات</ModernButton>
              <ModernButton variant="outline">محدد</ModernButton>
              <ModernButton variant="ghost">شبح</ModernButton>
            </ModernFlex>

            <h2>أحجام الأزرار</h2>
            <ModernFlex gap="md" wrap align="center">
              <ModernButton size="xs" variant="primary">صغير جداً</ModernButton>
              <ModernButton size="sm" variant="primary">صغير</ModernButton>
              <ModernButton size="md" variant="primary">متوسط</ModernButton>
              <ModernButton size="lg" variant="primary">كبير</ModernButton>
              <ModernButton size="xl" variant="primary">كبير جداً</ModernButton>
            </ModernFlex>

            <h2>أزرار الأيقونات</h2>
            <ModernFlex gap="md" wrap>
              <IconButton icon="🏠" variant="primary" tooltip="الرئيسية" />
              <IconButton icon="⚙️" variant="secondary" tooltip="الإعدادات" />
              <IconButton icon="🔍" variant="outline" tooltip="البحث" />
              <IconButton icon="🔔" variant="ghost" tooltip="الإشعارات" />
              <IconButton icon="❤️" variant="error" tooltip="المفضلة" />
            </ModernFlex>

            <h2>مجموعات الأزرار</h2>
            <ButtonGroup>
              <ModernButton variant="outline">الأول</ModernButton>
              <ModernButton variant="outline">الثاني</ModernButton>
              <ModernButton variant="outline">الثالث</ModernButton>
            </ButtonGroup>

            <h2>أزرار بأيقونات</h2>
            <ModernFlex gap="md" wrap>
              <ModernButton variant="primary" icon="➕">إضافة جديد</ModernButton>
              <ModernButton variant="success" icon="💾">حفظ</ModernButton>
              <ModernButton variant="warning" icon="✏️">تعديل</ModernButton>
              <ModernButton variant="error" icon="🗑️">حذف</ModernButton>
            </ModernFlex>
          </div>
        </TabPanel>

        {/* Charts Tab */}
        <TabPanel label="الرسوم البيانية" icon="📊">
          <div className="demo-section">
            <h2>دوائر التقدم</h2>
            <ModernFlex gap="lg" wrap justify="center">
              <ProgressCircle
                value={75}
                size={120}
                color="var(--primary-500)"
                label="التقدم"
              />
              <ProgressCircle
                value={60}
                size={120}
                color="var(--success-500)"
                label="الإنجاز"
              />
              <ProgressCircle
                value={90}
                size={120}
                color="var(--warning-500)"
                label="الأداء"
              />
            </ModernFlex>

            <h2>أشرطة التقدم</h2>
            <div className="progress-demo">
              <ProgressBar
                value={75}
                label="التحميل"
                showValue
                color="var(--primary-500)"
              />
              <ProgressBar
                value={60}
                label="المعالجة"
                showValue
                color="var(--success-500)"
                striped
                animated
              />
              <ProgressBar
                value={90}
                label="الرفع"
                showValue
                color="var(--info-500)"
              />
            </div>

            <h2>الرسوم البيانية</h2>
            <ModernGrid cols={2} gap="lg" responsive>
              <ModernCard>
                <h3>مخطط خطي - اتجاه المبيعات</h3>
                <LineChart
                  data={salesData}
                  width={400}
                  height={250}
                  color="var(--primary-500)"
                  showGrid
                  showPoints
                />
              </ModernCard>

              <ModernCard>
                <h3>مخطط أعمدة - أداء الخدمات</h3>
                <BarChart
                  data={servicesData}
                  width={400}
                  height={250}
                  showValues
                  showGrid
                />
              </ModernCard>

              <ModernCard>
                <h3>مخطط دائري - توزيع الخدمات</h3>
                <DonutChart
                  data={servicesData}
                  size={200}
                  showLabels
                  showValues
                />
              </ModernCard>

              <ModernCard>
                <h3>مقياس الأداء</h3>
                <GaugeChart
                  value={85}
                  size={200}
                  color="var(--success-500)"
                  label="من 100"
                />
              </ModernCard>
            </ModernGrid>

            <h2>عرض المقاييس</h2>
            <ModernGrid cols={2} gap="lg" responsive>
              <MetricDisplay
                title="متوسط قيمة الحجز"
                value="1,987 ر.س"
                change="+5.2%"
                changeType="positive"
                icon="💳"
                color="var(--success-500)"
              />
              
              <MetricDisplay
                title="معدل التحويل"
                value="68%"
                change="+2.1%"
                changeType="positive"
                icon="🎯"
                color="var(--primary-500)"
              />
            </ModernGrid>
          </div>
        </TabPanel>

        {/* Forms Tab */}
        <TabPanel label="النماذج" icon="📝">
          <div className="demo-section">
            <h2>عناصر النماذج</h2>
            <ModernCard>
              <ModernForm>
                <ModernGrid cols={2} gap="md" responsive>
                  <ModernInput
                    label="الاسم الكامل"
                    placeholder="أدخل اسمك الكامل"
                    required
                  />
                  
                  <ModernInput
                    label="البريد الإلكتروني"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                  />
                  
                  <ModernInput
                    label="رقم الهاتف"
                    type="tel"
                    placeholder="+966 50 123 4567"
                  />
                  
                  <ModernSelect
                    label="نوع الخدمة"
                    options={[
                      { value: 'flight', label: 'حجز طيران' },
                      { value: 'hotel', label: 'حجز فندق' },
                      { value: 'visa', label: 'تأشيرة' },
                      { value: 'package', label: 'باقة سياحية' }
                    ]}
                    placeholder="اختر نوع الخدمة"
                  />
                </ModernGrid>
                
                <ModernTextarea
                  label="ملاحظات إضافية"
                  placeholder="أدخل أي ملاحظات أو متطلبات خاصة"
                  rows={4}
                />
                
                <ModernFlex gap="md" justify="end">
                  <ModernButton variant="outline">إلغاء</ModernButton>
                  <ModernButton variant="primary">حفظ</ModernButton>
                </ModernFlex>
              </ModernForm>
            </ModernCard>
          </div>
        </TabPanel>

        {/* Alerts Tab */}
        <TabPanel label="التنبيهات" icon="🚨">
          <div className="demo-section">
            <h2>التنبيهات</h2>
            <div className="alerts-demo">
              <ModernAlert
                variant="success"
                title="تم بنجاح"
                dismissible
                icon="✅"
              >
                تم حفظ البيانات بنجاح. يمكنك الآن المتابعة إلى الخطوة التالية.
              </ModernAlert>

              <ModernAlert
                variant="warning"
                title="تحذير"
                dismissible
                icon="⚠️"
              >
                يرجى مراجعة البيانات المدخلة قبل المتابعة.
              </ModernAlert>

              <ModernAlert
                variant="error"
                title="خطأ"
                dismissible
                icon="❌"
              >
                حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.
              </ModernAlert>

              <ModernAlert
                variant="info"
                title="معلومات"
                dismissible
                icon="ℹ️"
              >
                هذه معلومات مهمة يجب أن تعرفها قبل المتابعة.
              </ModernAlert>
            </div>

            <h2>شارات الحالة</h2>
            <ModernFlex gap="md" wrap>
              <StatusBadge variant="success">نشط</StatusBadge>
              <StatusBadge variant="warning">قيد الانتظار</StatusBadge>
              <StatusBadge variant="error">ملغي</StatusBadge>
              <StatusBadge variant="info">قيد المراجعة</StatusBadge>
              <StatusBadge variant="default">افتراضي</StatusBadge>
            </ModernFlex>

            <h2>إشعارات Toast</h2>
            <ModernButton
              variant="primary"
              onClick={() => setShowToast(true)}
            >
              عرض إشعار
            </ModernButton>

            {showToast && (
              <Toast
                variant="success"
                title="نجح الإجراء"
                message="تم تنفيذ العملية بنجاح"
                onClose={() => setShowToast(false)}
                autoClose={3000}
              />
            )}
          </div>
        </TabPanel>

        {/* Navigation Tab */}
        <TabPanel label="التنقل" icon="🧭">
          <div className="demo-section">
            <h2>القوائم المنسدلة</h2>
            <ModernFlex gap="md" wrap>
              <Dropdown
                trigger={
                  <ModernButton variant="outline">
                    القائمة الأساسية ▼
                  </ModernButton>
                }
              >
                <DropdownItem icon="🏠">الرئيسية</DropdownItem>
                <DropdownItem icon="📋">الحجوزات</DropdownItem>
                <DropdownItem icon="💰">المبيعات</DropdownItem>
                <DropdownItem icon="👥">العملاء</DropdownItem>
              </Dropdown>

              <Dropdown
                trigger={
                  <ModernButton variant="primary">
                    الإجراءات ▼
                  </ModernButton>
                }
              >
                <DropdownItem icon="➕">إضافة جديد</DropdownItem>
                <DropdownItem icon="✏️">تعديل</DropdownItem>
                <DropdownItem icon="📄">تصدير</DropdownItem>
                <div className="dropdown-divider" />
                <DropdownItem icon="🗑️" className="text-error">حذف</DropdownItem>
              </Dropdown>
            </ModernFlex>

            <h2>شارات الحالة المتقدمة</h2>
            <ModernGrid cols={3} gap="md" responsive>
              <div className="status-demo">
                <h4>حالات الحجز</h4>
                <ModernFlex direction="column" gap="sm">
                  <StatusBadge variant="success" size="lg">مؤكد</StatusBadge>
                  <StatusBadge variant="warning" size="lg">قيد الانتظار</StatusBadge>
                  <StatusBadge variant="info" size="lg">قيد المعالجة</StatusBadge>
                  <StatusBadge variant="error" size="lg">ملغي</StatusBadge>
                </ModernFlex>
              </div>

              <div className="status-demo">
                <h4>حالات الدفع</h4>
                <ModernFlex direction="column" gap="sm">
                  <StatusBadge variant="success" size="lg">مدفوع</StatusBadge>
                  <StatusBadge variant="warning" size="lg">جزئي</StatusBadge>
                  <StatusBadge variant="error" size="lg">غير مدفوع</StatusBadge>
                  <StatusBadge variant="info" size="lg">مسترد</StatusBadge>
                </ModernFlex>
              </div>

              <div className="status-demo">
                <h4>حالات التأشيرة</h4>
                <ModernFlex direction="column" gap="sm">
                  <StatusBadge variant="success" size="lg">موافق عليها</StatusBadge>
                  <StatusBadge variant="warning" size="lg">قيد المراجعة</StatusBadge>
                  <StatusBadge variant="info" size="lg">مرسلة</StatusBadge>
                  <StatusBadge variant="error" size="lg">مرفوضة</StatusBadge>
                </ModernFlex>
              </div>
            </ModernGrid>
          </div>
        </TabPanel>
      </Tabs>
    </div>
  );
};

export default ComponentsDemo;