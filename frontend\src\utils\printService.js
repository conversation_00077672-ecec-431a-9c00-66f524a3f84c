// خدمات الطباعة وحفظ PDF للحجوزات

// دالة طباعة حجز واحد
export const printBooking = (booking, bookingType = 'عام') => {
  try {
    if (!booking) {
      alert('لا توجد بيانات للطباعة');
      return;
    }

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
      return;
    }

    const printContent = generateBookingHTML(booking, bookingType);
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    
    // انتظار تحميل المحتوى قبل الطباعة
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
    
  } catch (error) {
    console.error('خطأ في الطباعة:', error);
    alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
  }
};

// دالة طباعة عدة حجوزات
export const printMultipleBookings = (bookings, bookingType = 'عام') => {
  try {
    if (!bookings || bookings.length === 0) {
      alert('لا توجد حجوزات للطباعة');
      return;
    }

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
      return;
    }

    const printContent = generateMultipleBookingsHTML(bookings, bookingType);
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    
    // انتظار تحميل المحتوى قبل الطباعة
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
    
  } catch (error) {
    console.error('خطأ في الطباعة المجمعة:', error);
    alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
  }
};

// دالة حفظ PDF لحجز واحد
export const savePDF = async (booking, bookingType = 'عام') => {
  try {
    if (!booking) {
      alert('لا توجد بيانات لحفظها');
      return false;
    }

    // فتح نافذة طباعة جديدة مع إعدادات PDF
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
      return false;
    }

    const printContent = generateBookingHTML(booking, bookingType, true);
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    
    // تعليمات للمستخدم
    alert('سيتم فتح نافذة الطباعة. اختر "حفظ كـ PDF" من خيارات الطباعة.');
    
    setTimeout(() => {
      printWindow.print();
    }, 500);
    
    return true;
  } catch (error) {
    console.error('خطأ في حفظ PDF:', error);
    alert('حدث خطأ أثناء حفظ الملف. يرجى المحاولة مرة أخرى.');
    return false;
  }
};

// دالة حفظ PDF لعدة حجوزات
export const saveMultiplePDF = async (bookings, bookingType = 'عام') => {
  try {
    const printElement = document.createElement('div');
    printElement.innerHTML = generateMultipleBookingsHTML(bookings, bookingType, true);
    printElement.style.position = 'absolute';
    printElement.style.left = '-9999px';
    document.body.appendChild(printElement);

    const originalTitle = document.title;
    document.title = `حجوزات_${bookingType}_${new Date().toISOString().split('T')[0]}`;
    
    window.print();
    
    document.title = originalTitle;
    document.body.removeChild(printElement);
    
    return true;
  } catch (error) {
    console.error('خطأ في حفظ PDF:', error);
    alert('حدث خطأ أثناء حفظ الملف. يرجى المحاولة مرة أخرى.');
    return false;
  }
};

// دالة إنشاء HTML للحجز الواحد
const generateBookingHTML = (booking, bookingType, forPDF = false) => {
  const currentDate = new Date().toLocaleDateString('ar-SA');
  const currentTime = new Date().toLocaleTimeString('ar-SA');
  
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>حجز ${bookingType} - ${booking.id}</title>
      <style>
        ${getPrintStyles()}
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-header">
          <div class="company-info">
            <h1>✈️ شراء للسفر والسياحة</h1>
            <p>نظام إدارة الحجوزات المتطور</p>
          </div>
          <div class="print-date">
            <p>تاريخ الطباعة: ${currentDate}</p>
            <p>وقت الطباعة: ${currentTime}</p>
          </div>
        </div>
        
        <div class="booking-details">
          <h2>تفاصيل حجز ${bookingType}</h2>
          <div class="details-grid">
            <div class="detail-row">
              <span class="label">رقم الحجز:</span>
              <span class="value">${booking.id}</span>
            </div>
            <div class="detail-row">
              <span class="label">اسم العميل:</span>
              <span class="value">${booking.customerName || booking.customer}</span>
            </div>
            <div class="detail-row">
              <span class="label">تاريخ الحجز:</span>
              <span class="value">${booking.date}</span>
            </div>
            <div class="detail-row">
              <span class="label">الحالة:</span>
              <span class="value status-${booking.status}">
                ${booking.status === 'confirmed' ? 'مؤكد' :
                  booking.status === 'pending' ? 'في الانتظار' :
                  booking.status === 'cancelled' ? 'ملغي' : booking.status}
              </span>
            </div>
            <div class="detail-row">
              <span class="label">المبلغ:</span>
              <span class="value">${booking.amount} ريال سعودي</span>
            </div>
            ${booking.service ? `
            <div class="detail-row">
              <span class="label">الخدمة:</span>
              <span class="value">${booking.service}</span>
            </div>` : ''}
            ${booking.destination ? `
            <div class="detail-row">
              <span class="label">الوجهة:</span>
              <span class="value">${booking.destination}</span>
            </div>` : ''}
            ${booking.from ? `
            <div class="detail-row">
              <span class="label">من:</span>
              <span class="value">${booking.from}</span>
            </div>` : ''}
            ${booking.to ? `
            <div class="detail-row">
              <span class="label">إلى:</span>
              <span class="value">${booking.to}</span>
            </div>` : ''}
            ${booking.departureDate ? `
            <div class="detail-row">
              <span class="label">تاريخ المغادرة:</span>
              <span class="value">${booking.departureDate}</span>
            </div>` : ''}
            ${booking.returnDate ? `
            <div class="detail-row">
              <span class="label">تاريخ العودة:</span>
              <span class="value">${booking.returnDate}</span>
            </div>` : ''}
            ${booking.passengers ? `
            <div class="detail-row">
              <span class="label">عدد المسافرين:</span>
              <span class="value">${booking.passengers}</span>
            </div>` : ''}
            ${booking.notes ? `
            <div class="detail-row full-width">
              <span class="label">ملاحظات:</span>
              <span class="value">${booking.notes}</span>
            </div>` : ''}
          </div>
        </div>
        
        <div class="print-footer">
          <div class="footer-info">
            <p>شراء للسفر والسياحة - نظام إدارة الحجوزات</p>
            <p>تم إنشاء هذا التقرير تلقائياً بواسطة النظام</p>
          </div>
          <div class="qr-placeholder">
            <div class="qr-code">QR</div>
            <p>رمز الاستجابة السريعة</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

// دالة إنشاء HTML لعدة حجوزات
const generateMultipleBookingsHTML = (bookings, bookingType, forPDF = false) => {
  const currentDate = new Date().toLocaleDateString('ar-SA');
  const currentTime = new Date().toLocaleTimeString('ar-SA');
  
  const bookingsHTML = bookings.map(booking => `
    <tr>
      <td>${booking.id}</td>
      <td>${booking.customerName || booking.customer}</td>
      <td>${booking.date}</td>
      <td class="status-${booking.status}">
        ${booking.status === 'confirmed' ? 'مؤكد' :
          booking.status === 'pending' ? 'في الانتظار' :
          booking.status === 'cancelled' ? 'ملغي' : booking.status}
      </td>
      <td>${booking.amount} ريال</td>
      <td>${booking.service || booking.destination || '-'}</td>
    </tr>
  `).join('');
  
  const totalAmount = bookings.reduce((sum, booking) => sum + (parseFloat(booking.amount) || 0), 0);
  
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>تقرير حجوزات ${bookingType}</title>
      <style>
        ${getPrintStyles()}
      </style>
    </head>
    <body>
      <div class="print-container">
        <div class="print-header">
          <div class="company-info">
            <h1>✈️ شراء للسفر والسياحة</h1>
            <p>تقرير حجوزات ${bookingType}</p>
          </div>
          <div class="print-date">
            <p>تاريخ الطباعة: ${currentDate}</p>
            <p>وقت الطباعة: ${currentTime}</p>
          </div>
        </div>
        
        <div class="bookings-summary">
          <h2>ملخص الحجوزات</h2>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">إجمالي الحجوزات:</span>
              <span class="stat-value">${bookings.length}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">إجمالي المبلغ:</span>
              <span class="stat-value">${totalAmount.toLocaleString()} ريال سعودي</span>
            </div>
          </div>
        </div>
        
        <div class="bookings-table">
          <table>
            <thead>
              <tr>
                <th>رقم الحجز</th>
                <th>اسم العميل</th>
                <th>تاريخ الحجز</th>
                <th>الحالة</th>
                <th>المبلغ</th>
                <th>التفاصيل</th>
              </tr>
            </thead>
            <tbody>
              ${bookingsHTML}
            </tbody>
          </table>
        </div>
        
        <div class="print-footer">
          <div class="footer-info">
            <p>شراء للسفر والسياحة - تقرير حجوزات ${bookingType}</p>
            <p>تم إنشاء هذا التقرير تلقائياً بواسطة النظام</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

// أنماط الطباعة
const getPrintStyles = () => `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Arial', sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    direction: rtl;
  }
  
  .print-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }
  
  .print-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #333;
    padding-bottom: 15px;
    margin-bottom: 20px;
  }
  
  .company-info h1 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 5px;
  }
  
  .company-info p {
    color: #7f8c8d;
    font-size: 14px;
  }
  
  .print-date {
    text-align: left;
    font-size: 11px;
    color: #7f8c8d;
  }
  
  .booking-details h2,
  .bookings-summary h2 {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 15px;
    border-bottom: 1px solid #bdc3c7;
    padding-bottom: 5px;
  }
  
  .details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    border: 1px solid #ecf0f1;
    border-radius: 4px;
  }
  
  .detail-row.full-width {
    grid-column: 1 / -1;
  }
  
  .label {
    font-weight: bold;
    color: #34495e;
  }
  
  .value {
    color: #2c3e50;
  }
  
  .status-confirmed {
    color: #27ae60 !important;
    font-weight: bold;
  }
  
  .status-pending {
    color: #f39c12 !important;
    font-weight: bold;
  }
  
  .status-cancelled {
    color: #e74c3c !important;
    font-weight: bold;
  }
  
  .summary-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .stat-item {
    display: flex;
    flex-direction: column;
    padding: 10px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    min-width: 150px;
  }
  
  .stat-label {
    font-size: 11px;
    color: #7f8c8d;
    margin-bottom: 5px;
  }
  
  .stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
  }
  
  .bookings-table table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }
  
  .bookings-table th,
  .bookings-table td {
    border: 1px solid #bdc3c7;
    padding: 8px;
    text-align: center;
  }
  
  .bookings-table th {
    background-color: #ecf0f1;
    font-weight: bold;
    color: #2c3e50;
  }
  
  .bookings-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
  }
  
  .print-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #bdc3c7;
    padding-top: 15px;
    margin-top: 30px;
  }
  
  .footer-info {
    font-size: 11px;
    color: #7f8c8d;
  }
  
  .qr-placeholder {
    text-align: center;
  }
  
  .qr-code {
    width: 50px;
    height: 50px;
    border: 2px solid #bdc3c7;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 5px;
    font-size: 10px;
    color: #7f8c8d;
  }
  
  .qr-placeholder p {
    font-size: 10px;
    color: #7f8c8d;
  }
  
  @media print {
    body {
      font-size: 11px;
    }
    
    .print-container {
      padding: 10px;
    }
    
    .print-header {
      margin-bottom: 15px;
    }
    
    .booking-details,
    .bookings-summary {
      page-break-inside: avoid;
    }
  }
`;

export default {
  printBooking,
  printMultipleBookings,
  savePDF,
  saveMultiplePDF
};