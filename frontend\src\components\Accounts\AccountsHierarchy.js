import React, { useState, useMemo } from 'react';
import './AccountsHierarchy.css';

const AccountsHierarchy = ({ accounts, onAddAccount, onUpdateAccount, onDeleteAccount, transactions }) => {
  const [expandedNodes, setExpandedNodes] = useState(new Set(['1000', '2000', '3000', '4000', '5000']));
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const [accountForm, setAccountForm] = useState({
    id: '',
    name: '',
    type: 'detail',
    category: 'assets',
    parent: null,
    description: '',
    isActive: true
  });

  // حساب أرصدة الحسابات من المعاملات
  const calculateAccountBalance = (accountId) => {
    const accountTransactions = transactions.filter(trans => 
      trans.entries?.some(entry => entry.accountId === accountId)
    );

    let balance = 0;
    accountTransactions.forEach(trans => {
      trans.entries?.forEach(entry => {
        if (entry.accountId === accountId) {
          balance += (entry.debit || 0) - (entry.credit || 0);
        }
      });
    });

    return balance;
  };

  // إنشاء هيكل شجري للحسابات
  const accountTree = useMemo(() => {
    const accountsWithBalances = accounts.map(account => ({
      ...account,
      balance: calculateAccountBalance(account.id)
    }));

    // تصفية الحسابات
    const filtered = accountsWithBalances.filter(account => {
      const matchesSearch = account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           account.id.includes(searchTerm);
      const matchesType = filterType === 'all' || account.type === filterType;
      return matchesSearch && matchesType;
    });

    const tree = {};
    const rootAccounts = [];

    // إنشاء خريطة للحسابات
    filtered.forEach(account => {
      tree[account.id] = { ...account, children: [] };
    });

    // بناء الهيكل الشجري
    filtered.forEach(account => {
      if (account.parent && tree[account.parent]) {
        tree[account.parent].children.push(tree[account.id]);
      } else {
        rootAccounts.push(tree[account.id]);
      }
    });

    // ترتيب الحسابات
    const sortAccounts = (accounts) => {
      return accounts.sort((a, b) => a.id.localeCompare(b.id)).map(account => ({
        ...account,
        children: sortAccounts(account.children)
      }));
    };

    return sortAccounts(rootAccounts);
  }, [accounts, transactions, searchTerm, filterType]);

  // تبديل توسيع/طي العقدة
  const toggleNode = (nodeId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  // فتح نموذج إضافة حساب
  const openAddModal = (parentAccount = null) => {
    setAccountForm({
      id: '',
      name: '',
      type: 'detail',
      category: parentAccount?.category || 'assets',
      parent: parentAccount?.id || null,
      description: '',
      isActive: true
    });
    setShowAddModal(true);
  };

  // فتح نموذج تعديل حساب
  const openEditModal = (account) => {
    setAccountForm({ ...account });
    setShowEditModal(true);
  };

  // حفظ الحساب الجديد
  const handleSaveAccount = () => {
    if (!accountForm.name.trim()) {
      alert('يرجى إدخال اسم الحساب');
      return;
    }

    if (!accountForm.id.trim()) {
      alert('يرجى إدخال رمز الحساب');
      return;
    }

    // التحقق من عدم تكرار رمز الحساب
    if (accounts.some(acc => acc.id === accountForm.id && acc.id !== selectedAccount?.id)) {
      alert('رمز الحساب موجود مسبقاً');
      return;
    }

    if (showEditModal) {
      onUpdateAccount(selectedAccount.id, accountForm);
      setShowEditModal(false);
    } else {
      onAddAccount(accountForm);
      setShowAddModal(false);
    }

    setAccountForm({
      id: '',
      name: '',
      type: 'detail',
      category: 'assets',
      parent: null,
      description: '',
      isActive: true
    });
  };

  // حذف الحساب
  const handleDeleteAccount = (account) => {
    // التحقق من وجود حسابات فرعية
    const hasChildren = accounts.some(acc => acc.parent === account.id);
    if (hasChildren) {
      alert('لا يمكن حذف حساب يحتوي على حسابات فرعية');
      return;
    }

    // التحقق من وجود معاملات
    const hasTransactions = transactions.some(trans => 
      trans.entries?.some(entry => entry.accountId === account.id)
    );
    
    if (hasTransactions) {
      if (!window.confirm('هذا الحساب يحتوي على معاملات. هل أنت متأكد من الحذف؟')) {
        return;
      }
    }

    if (window.confirm(`هل أنت متأكد من حذف الحساب "${account.name}"؟`)) {
      onDeleteAccount(account.id);
    }
  };

  // عرض الحساب في الشجرة
  const renderAccountNode = (account, level = 0) => {
    const hasChildren = account.children && account.children.length > 0;
    const isExpanded = expandedNodes.has(account.id);
    const isSelected = selectedAccount?.id === account.id;

    const categoryColors = {
      assets: '#27ae60',
      liabilities: '#e74c3c',
      equity: '#3498db',
      revenue: '#f39c12',
      expenses: '#9b59b6'
    };

    const categoryIcons = {
      assets: '💰',
      liabilities: '📉',
      equity: '🏛️',
      revenue: '📈',
      expenses: '💸'
    };

    return (
      <div key={account.id} className="account-node">
        <div 
          className={`account-item ${isSelected ? 'selected' : ''} ${account.type}`}
          style={{ 
            paddingRight: `${level * 20 + 15}px`,
            borderRight: `3px solid ${categoryColors[account.category]}20`
          }}
          onClick={() => setSelectedAccount(account)}
        >
          <div className="account-toggle">
            {hasChildren && (
              <button
                className={`toggle-btn ${isExpanded ? 'expanded' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleNode(account.id);
                }}
              >
                {isExpanded ? '▼' : '▶'}
              </button>
            )}
          </div>

          <div className="account-icon" style={{ color: categoryColors[account.category] }}>
            {account.type === 'header' ? '📁' : categoryIcons[account.category]}
          </div>

          <div className="account-info">
            <div className="account-name">
              {account.name}
              {account.type === 'header' && (
                <span className="header-badge">رئيسي</span>
              )}
            </div>
            <div className="account-code">{account.id}</div>
          </div>

          <div className="account-balance" style={{ color: categoryColors[account.category] }}>
            {account.type === 'detail' && (
              <span>{account.balance.toLocaleString()} ر.س</span>
            )}
          </div>

          <div className="account-actions">
            <button
              className="action-btn add-btn"
              onClick={(e) => {
                e.stopPropagation();
                openAddModal(account);
              }}
              title="إضافة حساب فرعي"
            >
              ➕
            </button>
            <button
              className="action-btn edit-btn"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedAccount(account);
                openEditModal(account);
              }}
              title="تعديل الحساب"
            >
              ✏️
            </button>
            <button
              className="action-btn delete-btn"
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteAccount(account);
              }}
              title="حذف الحساب"
            >
              🗑️
            </button>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="account-children">
            {account.children.map(child => renderAccountNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // حساب إحصائيات الحسابات
  const accountStats = useMemo(() => {
    const stats = {
      total: accounts.length,
      header: accounts.filter(acc => acc.type === 'header').length,
      detail: accounts.filter(acc => acc.type === 'detail').length,
      assets: accounts.filter(acc => acc.category === 'assets').length,
      liabilities: accounts.filter(acc => acc.category === 'liabilities').length,
      equity: accounts.filter(acc => acc.category === 'equity').length,
      revenue: accounts.filter(acc => acc.category === 'revenue').length,
      expenses: accounts.filter(acc => acc.category === 'expenses').length
    };

    return stats;
  }, [accounts]);

  return (
    <div className="accounts-hierarchy">
      <div className="hierarchy-header">
        <div className="header-content">
          <h2>🏗️ هيكل الحسابات</h2>
          <p>إدارة الحسابات الرئيسية والفرعية</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => openAddModal()}
          >
            ➕ حساب رئيسي جديد
          </button>
        </div>
      </div>

      <div className="hierarchy-content">
        <div className="hierarchy-sidebar">
          <div className="search-section">
            <div className="search-box">
              <input
                type="text"
                placeholder="🔍 البحث في الحسابات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>

            <div className="filter-section">
              <label>تصفية حسب النوع:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="all">جميع الحسابات</option>
                <option value="header">الحسابات الرئيسية</option>
                <option value="detail">الحسابات التفصيلية</option>
              </select>
            </div>
          </div>

          <div className="stats-section">
            <h3>إحصائيات الحسابات</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-value">{accountStats.total}</span>
                <span className="stat-label">إجمالي الحسابات</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{accountStats.header}</span>
                <span className="stat-label">حسابات رئيسية</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{accountStats.detail}</span>
                <span className="stat-label">حسابات تفصيلية</span>
              </div>
            </div>

            <div className="category-stats">
              <h4>حسب الفئة</h4>
              <div className="category-list">
                {[
                  { key: 'assets', name: 'الأصول', color: '#27ae60', icon: '💰' },
                  { key: 'liabilities', name: 'الخصوم', color: '#e74c3c', icon: '📉' },
                  { key: 'equity', name: 'حقوق الملكية', color: '#3498db', icon: '🏛️' },
                  { key: 'revenue', name: 'الإيرادات', color: '#f39c12', icon: '📈' },
                  { key: 'expenses', name: 'المصروفات', color: '#9b59b6', icon: '💸' }
                ].map(category => (
                  <div key={category.key} className="category-stat">
                    <span className="category-icon" style={{ color: category.color }}>
                      {category.icon}
                    </span>
                    <span className="category-name">{category.name}</span>
                    <span className="category-count">{accountStats[category.key]}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="hierarchy-main">
          <div className="accounts-tree">
            {accountTree.length === 0 ? (
              <div className="no-accounts">
                <span className="icon">📊</span>
                <h3>لا توجد حسابات</h3>
                <p>ابدأ بإضافة حساب رئيسي جديد</p>
              </div>
            ) : (
              accountTree.map(account => renderAccountNode(account))
            )}
          </div>
        </div>

        {selectedAccount && (
          <div className="account-details">
            <div className="details-header">
              <h3>تفاصيل الحساب</h3>
              <button
                className="close-details"
                onClick={() => setSelectedAccount(null)}
              >
                ✕
              </button>
            </div>

            <div className="details-content">
              <div className="detail-item">
                <label>رمز الحساب:</label>
                <span>{selectedAccount.id}</span>
              </div>
              <div className="detail-item">
                <label>اسم الحساب:</label>
                <span>{selectedAccount.name}</span>
              </div>
              <div className="detail-item">
                <label>النوع:</label>
                <span>{selectedAccount.type === 'header' ? 'رئيسي' : 'تفصيلي'}</span>
              </div>
              <div className="detail-item">
                <label>الفئة:</label>
                <span>
                  {selectedAccount.category === 'assets' ? 'الأصول' :
                   selectedAccount.category === 'liabilities' ? 'الخصوم' :
                   selectedAccount.category === 'equity' ? 'حقوق الملكية' :
                   selectedAccount.category === 'revenue' ? 'الإيرادات' : 'المصروفات'}
                </span>
              </div>
              {selectedAccount.parent && (
                <div className="detail-item">
                  <label>الحساب الأب:</label>
                  <span>{accounts.find(acc => acc.id === selectedAccount.parent)?.name}</span>
                </div>
              )}
              {selectedAccount.type === 'detail' && (
                <div className="detail-item">
                  <label>الرصيد الحالي:</label>
                  <span className="balance">{selectedAccount.balance.toLocaleString()} ر.س</span>
                </div>
              )}
              {selectedAccount.description && (
                <div className="detail-item">
                  <label>الوصف:</label>
                  <span>{selectedAccount.description}</span>
                </div>
              )}
            </div>

            <div className="details-actions">
              <button
                className="btn btn-primary"
                onClick={() => openEditModal(selectedAccount)}
              >
                ✏️ تعديل
              </button>
              <button
                className="btn btn-success"
                onClick={() => openAddModal(selectedAccount)}
              >
                ➕ إضافة فرعي
              </button>
              <button
                className="btn btn-danger"
                onClick={() => handleDeleteAccount(selectedAccount)}
              >
                🗑️ حذف
              </button>
            </div>
          </div>
        )}
      </div>

      {/* نموذج إضافة/تعديل حساب */}
      {(showAddModal || showEditModal) && (
        <div className="modal-overlay">
          <div className="account-modal">
            <div className="modal-header">
              <h3>{showEditModal ? '✏️ تعديل الحساب' : '➕ حساب جديد'}</h3>
              <button 
                className="close-btn"
                onClick={() => {
                  setShowAddModal(false);
                  setShowEditModal(false);
                }}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>رمز الحساب *</label>
                  <input
                    type="text"
                    value={accountForm.id}
                    onChange={(e) => setAccountForm(prev => ({ ...prev, id: e.target.value }))}
                    className="form-control"
                    placeholder="مثال: 1001"
                    disabled={showEditModal}
                  />
                </div>

                <div className="form-group">
                  <label>اسم الحساب *</label>
                  <input
                    type="text"
                    value={accountForm.name}
                    onChange={(e) => setAccountForm(prev => ({ ...prev, name: e.target.value }))}
                    className="form-control"
                    placeholder="مثال: النقدية في الصندوق"
                  />
                </div>

                <div className="form-group">
                  <label>نوع الحساب *</label>
                  <select
                    value={accountForm.type}
                    onChange={(e) => setAccountForm(prev => ({ ...prev, type: e.target.value }))}
                    className="form-control"
                  >
                    <option value="header">رئيسي</option>
                    <option value="detail">تفصيلي</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>الفئة *</label>
                  <select
                    value={accountForm.category}
                    onChange={(e) => setAccountForm(prev => ({ ...prev, category: e.target.value }))}
                    className="form-control"
                  >
                    <option value="assets">الأصول</option>
                    <option value="liabilities">الخصوم</option>
                    <option value="equity">حقوق الملكية</option>
                    <option value="revenue">الإيرادات</option>
                    <option value="expenses">المصروفات</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>الحساب الأب</label>
                  <select
                    value={accountForm.parent || ''}
                    onChange={(e) => setAccountForm(prev => ({ ...prev, parent: e.target.value || null }))}
                    className="form-control"
                  >
                    <option value="">لا يوجد (حساب رئيسي)</option>
                    {accounts
                      .filter(acc => acc.type === 'header' && acc.category === accountForm.category)
                      .map(account => (
                        <option key={account.id} value={account.id}>
                          {account.name} ({account.id})
                        </option>
                      ))}
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={accountForm.description}
                    onChange={(e) => setAccountForm(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="وصف مختصر للحساب..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  setShowAddModal(false);
                  setShowEditModal(false);
                }}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleSaveAccount}
              >
                💾 حفظ
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountsHierarchy;