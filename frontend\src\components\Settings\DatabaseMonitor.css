/* مراقب قاعدة البيانات */
.database-monitor {
  padding: 20px;
  background: var(--bg-primary);
  color: var(--text-primary);
  direction: rtl;
  font-family: 'Cairo', Arial, sans-serif;
}

.database-monitor h2 {
  margin: 0 0 30px 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 10px;
}

.database-monitor h3 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

/* الإحصائيات المباشرة */
.real-time-stats {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.stat-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
}

.stat-title {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.stat-trend {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
}

.stat-trend.positive {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.stat-trend.negative {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* مؤشرات الأداء */
.performance-indicators {
  margin-bottom: 30px;
}

.indicators-grid {
  display: grid;
  gap: 15px;
}

.indicator {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.indicator-value {
  font-weight: 700;
  font-size: 1.1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: all 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* آخر الاستعلامات */
.recent-queries {
  margin-bottom: 30px;
}

.queries-table {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-header {
  display: grid;
  grid-template-columns: 100px 1fr 80px 80px;
  gap: 15px;
  padding: 15px 20px;
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}

.table-row {
  display: grid;
  grid-template-columns: 100px 1fr 80px 80px;
  gap: 15px;
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-light);
  transition: background-color 0.2s ease;
  align-items: center;
}

.table-row:hover {
  background: var(--bg-secondary);
}

.table-row.slow {
  border-right: 4px solid #f39c12;
}

.table-row.error {
  border-right: 4px solid #e74c3c;
}

.query-time {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.query-text {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.query-duration {
  font-weight: 600;
  text-align: center;
}

.query-status {
  text-align: center;
  font-size: 0.8rem;
  font-weight: 600;
}

/* إحصائيات الجداول */
.table-statistics {
  margin-bottom: 30px;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.table-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.table-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-light);
}

.table-info {
  margin-bottom: 15px;
}

.table-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

.table-actions {
  display: flex;
  gap: 10px;
}

.btn-table-action {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-table-action:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* تنبيهات النظام */
.system-alerts {
  margin-bottom: 30px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.alert {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border-radius: 8px;
  border-right: 4px solid;
  font-weight: 600;
}

.alert.success {
  background: rgba(39, 174, 96, 0.1);
  border-color: #27ae60;
  color: #27ae60;
}

.alert.warning {
  background: rgba(243, 156, 18, 0.1);
  border-color: #f39c12;
  color: #f39c12;
}

.alert.error {
  background: rgba(231, 76, 60, 0.1);
  border-color: #e74c3c;
  color: #e74c3c;
}

.alert-icon {
  font-size: 1.2rem;
}

.alert-message {
  flex: 1;
}

.alert-time {
  font-size: 0.8rem;
  opacity: 0.7;
}

/* تحسينات للوضع المظلم */
.dark-mode .stat-card::before {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.dark-mode .progress-fill::after {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.dark-mode .alert.success {
  background: rgba(39, 174, 96, 0.2);
}

.dark-mode .alert.warning {
  background: rgba(243, 156, 18, 0.2);
}

.dark-mode .alert.error {
  background: rgba(231, 76, 60, 0.2);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 80px 1fr 60px 60px;
    gap: 10px;
    padding: 10px 15px;
  }
  
  .query-text {
    font-size: 0.7rem;
  }
  
  .table-actions {
    flex-direction: column;
  }
  
  .alert {
    padding: 12px 15px;
  }
}
