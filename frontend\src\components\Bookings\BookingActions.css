/* Booking Actions Styles */

.booking-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.booking-action-button {
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  min-width: 36px;
  justify-content: center;
}

.booking-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.booking-action-button:active {
  transform: translateY(0);
}

.action-view {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.action-edit {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
}

.action-delete {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

.action-print {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
  color: white;
}

.action-pdf {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  color: white;
}

/* Bulk Actions Styles */
.bulk-actions-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.bulk-actions-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.bulk-actions-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
}

.selection-count {
  font-weight: bold;
  color: #2c3e50;
  margin-left: 15px;
}

/* Modal Styles */
.booking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.booking-modal-content {
  background: white;
  border-radius: 20px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  animation: modalSlideIn 0.3s ease-out;
  padding: 20px;
  min-width: 500px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.booking-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.booking-modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.booking-modal-actions {
  display: flex;
  gap: 8px;
}

.booking-modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

.booking-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item label {
  font-weight: bold;
  color: #7f8c8d;
  font-size: 12px;
}

.detail-item span {
  color: #2c3e50;
  font-size: 14px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.booking-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f8f9fa;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-confirmed {
  background: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.status-pending {
  background: rgba(243, 156, 18, 0.2);
  color: #f39c12;
}

.status-cancelled {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-modal-content {
    min-width: 95vw;
    margin: 10px;
  }
  
  .bulk-actions-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bulk-actions-buttons {
    justify-content: center;
  }
  
  .booking-details-grid {
    grid-template-columns: 1fr;
  }
}

.booking-actions .action-print:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

.booking-actions .action-pdf {
  color: var(--success-600);
}

.booking-actions .action-pdf:hover {
  background: var(--success-50);
  color: var(--success-700);
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  border: 1px solid var(--neutral-200);
}

.bulk-actions-left {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.bulk-actions-right {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.selection-count {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
  padding: var(--space-2) var(--space-3);
  background: var(--neutral-100);
  border-radius: var(--radius-md);
}

.bulk-delete {
  color: var(--error-600) !important;
  border-color: var(--error-200) !important;
}

.bulk-delete:hover {
  background: var(--error-50) !important;
  border-color: var(--error-300) !important;
  color: var(--error-700) !important;
}

/* Booking Details Modal */
.booking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.booking-modal-content {
  background: var(--neutral-0);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.booking-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--gradient-glass);
}

.booking-modal-header h3 {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.booking-modal-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.booking-modal-body {
  padding: var(--space-6);
  max-height: 400px;
  overflow-y: auto;
}

.booking-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-600);
}

.detail-item span {
  font-size: var(--text-base);
  color: var(--neutral-800);
  font-weight: var(--font-medium);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-confirmed {
  background: var(--success-100);
  color: var(--success-800);
}

.status-pending {
  background: var(--warning-100);
  color: var(--warning-800);
}

.status-cancelled {
  background: var(--error-100);
  color: var(--error-800);
}

.booking-modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
  padding: var(--space-6);
  border-top: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-actions {
    flex-wrap: wrap;
    gap: var(--space-1);
  }
  
  .bulk-actions {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }
  
  .bulk-actions-left,
  .bulk-actions-right {
    justify-content: center;
  }
  
  .booking-details-grid {
    grid-template-columns: 1fr;
  }
  
  .booking-modal-content {
    margin: var(--space-4);
    max-height: 90vh;
  }
  
  .booking-modal-header,
  .booking-modal-body,
  .booking-modal-footer {
    padding: var(--space-4);
  }
  
  .booking-modal-footer {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .booking-modal-footer button {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .booking-actions,
  .bulk-actions,
  .booking-modal-overlay {
    display: none !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .bulk-actions {
    background: var(--neutral-800);
    border-color: var(--neutral-700);
  }
  
  .selection-count {
    background: var(--neutral-700);
    color: var(--neutral-300);
  }
  
  .booking-modal-content {
    background: var(--neutral-800);
  }
  
  .booking-modal-header {
    border-bottom-color: var(--neutral-700);
    background: rgba(0, 0, 0, 0.3);
  }
  
  .booking-modal-header h3 {
    color: var(--neutral-200);
  }
  
  .booking-modal-footer {
    background: var(--neutral-900);
    border-top-color: var(--neutral-700);
  }
  
  .detail-item label {
    color: var(--neutral-400);
  }
  
  .detail-item span {
    color: var(--neutral-200);
  }
}