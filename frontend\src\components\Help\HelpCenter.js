import React, { useState } from 'react';
import ModernCard, { <PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '../UI/ModernCard';
import ModernButton from '../UI/ModernButton';
import { ModernGrid, ModernFlex } from '../UI/ModernLayout';
import { ModernAlert } from '../UI/ModernAlert';
import './HelpCenter.css';

const HelpCenter = () => {
  const [activeTab, setActiveTab] = useState('faq');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    priority: 'medium'
  });
  const [showAlert, setShowAlert] = useState(false);

  // الأسئلة الشائعة
  const faqData = [
    {
      id: 1,
      category: 'bookings',
      question: 'كيف يمكنني إنشاء حجز جديد؟',
      answer: 'لإنشاء حجز جديد، اذهب إلى قسم "الحجوزات" من القائمة الرئيسية، ثم اضغط على "حجز جديد". املأ جميع البيانات المطلوبة للعميل والرحلة، ثم احفظ الحجز.'
    },
    {
      id: 2,
      category: 'bookings',
      question: 'كيف يمكنني تعديل حجز موجود؟',
      answer: 'يمكنك تعديل الحجز من خلال البحث عن الحجز في قائمة الحجوزات، ثم الضغط على "تعديل". تأكد من حفظ التغييرات بعد التعديل.'
    },
    {
      id: 3,
      category: 'customers',
      question: 'كيف أضيف عميل جديد؟',
      answer: 'اذهب إلى قسم "العملاء" واضغط على "عميل جديد". املأ جميع البيانات الشخصية ومعلومات الاتصال، ثم احفظ البيانات.'
    },
    {
      id: 4,
      category: 'payments',
      question: 'كيف أسجل دفعة جديدة؟',
      answer: 'يمكنك تسجيل الدفعات من خلال قسم "المدفوعات" أو مباشرة من صفحة الحجز. اختر طريقة الدفع والمبلغ، ثم احفظ العملية.'
    },
    {
      id: 5,
      category: 'reports',
      question: 'كيف أطبع التقارير؟',
      answer: 'اذهب إلى قسم "التقارير"، اختر نوع التقرير والفترة الزمنية، ثم اضغط على "طباعة" أو "تصدير PDF".'
    },
    {
      id: 6,
      category: 'system',
      question: 'كيف أغير كلمة المرور؟',
      answer: 'اذهب إلى "الملف الشخصي" من قائمة المستخدم، ثم اضغط على "تغيير كلمة المرور". أدخل كلمة المرور الحالية والجديدة.'
    },
    {
      id: 7,
      category: 'system',
      question: 'ماذا أفعل إذا نسيت كلمة المرور؟',
      answer: 'اتصل بمدير النظام لإعادة تعيين كلمة المرور. سيتم إرسال كلمة مرور مؤقتة إلى بريدك الإلكتروني.'
    },
    {
      id: 8,
      category: 'invoices',
      question: 'كيف أصدر فاتورة؟',
      answer: 'اذهب إلى قسم "المبيعات" ثم "الفواتير"، اضغط على "فاتورة جديدة"، اختر العميل والخدمات، ثم احفظ الفاتورة.'
    }
  ];

  // فئات المساعدة
  const categories = [
    { id: 'all', name: 'جميع الفئات', icon: '📋' },
    { id: 'bookings', name: 'الحجوزات', icon: '✈️' },
    { id: 'customers', name: 'العملاء', icon: '👥' },
    { id: 'payments', name: 'المدفوعات', icon: '💳' },
    { id: 'invoices', name: 'الفواتير', icon: '🧾' },
    { id: 'reports', name: 'التقارير', icon: '📊' },
    { id: 'system', name: 'النظام', icon: '⚙️' }
  ];

  // أدلة المستخدم
  const userGuides = [
    {
      id: 1,
      title: 'دليل البدء السريع',
      description: 'تعلم أساسيات استخدام النظام في 10 دقائق',
      icon: '🚀',
      duration: '10 دقائق',
      level: 'مبتدئ'
    },
    {
      id: 2,
      title: 'إدارة الحجوزات',
      description: 'دليل شامل لإدارة جميع أنواع الحجوزات',
      icon: '📖',
      duration: '25 دقيقة',
      level: 'متوسط'
    },
    {
      id: 3,
      title: 'النظام المحاسبي',
      description: 'كيفية استخدام النظام المحاسبي والتقارير المالية',
      icon: '💰',
      duration: '30 دقيقة',
      level: 'متقدم'
    },
    {
      id: 4,
      title: 'إدارة العملاء',
      description: 'أفضل الممارسات في إدارة قاعدة بيانات العملاء',
      icon: '👤',
      duration: '15 دقيقة',
      level: 'مبتدئ'
    }
  ];

  // فلترة الأسئلة الشائعة
  const filteredFAQ = faqData.filter(item => {
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesSearch = item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const handleContactSubmit = (e) => {
    e.preventDefault();
    // محاكاة إرسال النموذج
    console.log('Contact form submitted:', contactForm);
    setShowAlert(true);
    setContactForm({
      name: '',
      email: '',
      subject: '',
      message: '',
      priority: 'medium'
    });
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleInputChange = (field, value) => {
    setContactForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="help-center">
      {/* Alert */}
      {showAlert && (
        <ModernAlert
          type="success"
          title="تم الإرسال بنجاح"
          message="تم إرسال رسالتك بنجاح. سنتواصل معك قريباً."
          onClose={() => setShowAlert(false)}
        />
      )}

      {/* Header */}
      <div className="help-header">
        <div className="help-header-content">
          <h1>مركز المساعدة</h1>
          <p>نحن هنا لمساعدتك في استخدام النظام بأفضل طريقة ممكنة</p>
          
          {/* Search */}
          <div className="help-search">
            <div className="search-input-wrapper">
              <span className="search-icon">🔍</span>
              <input
                type="text"
                placeholder="ابحث في المساعدة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="help-tabs">
        <ModernFlex gap="sm" className="tabs-container">
          <button
            className={`help-tab ${activeTab === 'faq' ? 'active' : ''}`}
            onClick={() => setActiveTab('faq')}
          >
            <span className="tab-icon">❓</span>
            الأسئلة الشائعة
          </button>
          <button
            className={`help-tab ${activeTab === 'guides' ? 'active' : ''}`}
            onClick={() => setActiveTab('guides')}
          >
            <span className="tab-icon">📚</span>
            أدلة المستخدم
          </button>
          <button
            className={`help-tab ${activeTab === 'contact' ? 'active' : ''}`}
            onClick={() => setActiveTab('contact')}
          >
            <span className="tab-icon">📞</span>
            اتصل بنا
          </button>
          <button
            className={`help-tab ${activeTab === 'shortcuts' ? 'active' : ''}`}
            onClick={() => setActiveTab('shortcuts')}
          >
            <span className="tab-icon">⌨️</span>
            اختصارات لوحة المفاتيح
          </button>
        </ModernFlex>
      </div>

      {/* Content */}
      <div className="help-content">
        
        {/* FAQ Tab */}
        {activeTab === 'faq' && (
          <div className="faq-section">
            <ModernGrid cols={4} gap="lg" responsive>
              
              {/* Categories Sidebar */}
              <div className="categories-sidebar">
                <ModernCard>
                  <CardHeader>
                    <CardTitle>الفئات</CardTitle>
                  </CardHeader>
                  <CardBody>
                    <div className="categories-list">
                      {categories.map(category => (
                        <button
                          key={category.id}
                          className={`category-item ${selectedCategory === category.id ? 'active' : ''}`}
                          onClick={() => setSelectedCategory(category.id)}
                        >
                          <span className="category-icon">{category.icon}</span>
                          <span className="category-name">{category.name}</span>
                        </button>
                      ))}
                    </div>
                  </CardBody>
                </ModernCard>
              </div>

              {/* FAQ List */}
              <div className="faq-list" style={{ gridColumn: 'span 3' }}>
                {filteredFAQ.length > 0 ? (
                  filteredFAQ.map(item => (
                    <ModernCard key={item.id} className="faq-item">
                      <CardBody>
                        <div className="faq-question">
                          <h3>{item.question}</h3>
                        </div>
                        <div className="faq-answer">
                          <p>{item.answer}</p>
                        </div>
                      </CardBody>
                    </ModernCard>
                  ))
                ) : (
                  <div className="no-results">
                    <div className="no-results-icon">🔍</div>
                    <h3>لا توجد نتائج</h3>
                    <p>لم نجد أي أسئلة تطابق بحثك. جرب كلمات مختلفة أو تصفح الفئات.</p>
                  </div>
                )}
              </div>
            </ModernGrid>
          </div>
        )}

        {/* User Guides Tab */}
        {activeTab === 'guides' && (
          <div className="guides-section">
            <ModernGrid cols={2} gap="lg" responsive>
              {userGuides.map(guide => (
                <ModernCard key={guide.id} className="guide-card">
                  <CardBody>
                    <div className="guide-header">
                      <div className="guide-icon">{guide.icon}</div>
                      <div className="guide-info">
                        <h3>{guide.title}</h3>
                        <p>{guide.description}</p>
                      </div>
                    </div>
                    
                    <div className="guide-meta">
                      <span className="guide-duration">⏱️ {guide.duration}</span>
                      <span className={`guide-level guide-level--${guide.level}`}>
                        {guide.level}
                      </span>
                    </div>
                    
                    <div className="guide-actions">
                      <ModernButton variant="primary" fullWidth>
                        بدء الدليل
                      </ModernButton>
                    </div>
                  </CardBody>
                </ModernCard>
              ))}
            </ModernGrid>
          </div>
        )}

        {/* Contact Tab */}
        {activeTab === 'contact' && (
          <div className="contact-section">
            <ModernGrid cols={2} gap="lg" responsive>
              
              {/* Contact Form */}
              <ModernCard>
                <CardHeader>
                  <CardTitle>أرسل رسالة</CardTitle>
                </CardHeader>
                <CardBody>
                  <form onSubmit={handleContactSubmit} className="contact-form">
                    <div className="form-group">
                      <label>الاسم</label>
                      <input
                        type="text"
                        value={contactForm.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        required
                        className="form-input"
                      />
                    </div>

                    <div className="form-group">
                      <label>البريد الإلكتروني</label>
                      <input
                        type="email"
                        value={contactForm.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                        className="form-input"
                      />
                    </div>

                    <div className="form-group">
                      <label>الموضوع</label>
                      <input
                        type="text"
                        value={contactForm.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        required
                        className="form-input"
                      />
                    </div>

                    <div className="form-group">
                      <label>الأولوية</label>
                      <select
                        value={contactForm.priority}
                        onChange={(e) => handleInputChange('priority', e.target.value)}
                        className="form-input"
                      >
                        <option value="low">منخفضة</option>
                        <option value="medium">متوسطة</option>
                        <option value="high">عالية</option>
                        <option value="urgent">عاجلة</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label>الرسالة</label>
                      <textarea
                        value={contactForm.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        required
                        rows="5"
                        className="form-input"
                        placeholder="اشرح مشكلتك أو استفسارك بالتفصيل..."
                      />
                    </div>

                    <ModernButton type="submit" variant="primary" fullWidth>
                      إرسال الرسالة
                    </ModernButton>
                  </form>
                </CardBody>
              </ModernCard>

              {/* Contact Info */}
              <div className="contact-info">
                <ModernCard>
                  <CardHeader>
                    <CardTitle>معلومات الاتصال</CardTitle>
                  </CardHeader>
                  <CardBody>
                    <div className="contact-methods">
                      <div className="contact-method">
                        <div className="contact-icon">📞</div>
                        <div className="contact-details">
                          <h4>الهاتف</h4>
                          <p>+966 11 123 4567</p>
                          <small>الأحد - الخميس: 8:00 ص - 6:00 م</small>
                        </div>
                      </div>

                      <div className="contact-method">
                        <div className="contact-icon">📧</div>
                        <div className="contact-details">
                          <h4>البريد الإلكتروني</h4>
                          <p><EMAIL></p>
                          <small>نرد خلال 24 ساعة</small>
                        </div>
                      </div>

                      <div className="contact-method">
                        <div className="contact-icon">💬</div>
                        <div className="contact-details">
                          <h4>الدردشة المباشرة</h4>
                          <p>متاحة الآن</p>
                          <ModernButton variant="outline" size="sm">
                            بدء المحادثة
                          </ModernButton>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </ModernCard>

                <ModernCard className="emergency-card">
                  <CardHeader>
                    <CardTitle>الدعم الطارئ</CardTitle>
                  </CardHeader>
                  <CardBody>
                    <div className="emergency-info">
                      <div className="emergency-icon">🚨</div>
                      <h4>للمشاكل العاجلة</h4>
                      <p>اتصل على الرقم المباشر</p>
                      <div className="emergency-number">+966 50 123 4567</div>
                      <small>متاح 24/7 للمشاكل الحرجة</small>
                    </div>
                  </CardBody>
                </ModernCard>
              </div>
            </ModernGrid>
          </div>
        )}

        {/* Keyboard Shortcuts Tab */}
        {activeTab === 'shortcuts' && (
          <div className="shortcuts-section">
            <ModernGrid cols={2} gap="lg" responsive>
              <ModernCard>
                <CardHeader>
                  <CardTitle>اختصارات عامة</CardTitle>
                </CardHeader>
                <CardBody>
                  <div className="shortcuts-list">
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Ctrl</kbd> + <kbd>N</kbd>
                      </div>
                      <div className="shortcut-description">حجز جديد</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Ctrl</kbd> + <kbd>S</kbd>
                      </div>
                      <div className="shortcut-description">حفظ</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Ctrl</kbd> + <kbd>F</kbd>
                      </div>
                      <div className="shortcut-description">بحث</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Ctrl</kbd> + <kbd>P</kbd>
                      </div>
                      <div className="shortcut-description">طباعة</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>F5</kbd>
                      </div>
                      <div className="shortcut-description">تحديث الصفحة</div>
                    </div>
                  </div>
                </CardBody>
              </ModernCard>

              <ModernCard>
                <CardHeader>
                  <CardTitle>اختصارات التنقل</CardTitle>
                </CardHeader>
                <CardBody>
                  <div className="shortcuts-list">
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Alt</kbd> + <kbd>D</kbd>
                      </div>
                      <div className="shortcut-description">لوحة التحكم</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Alt</kbd> + <kbd>C</kbd>
                      </div>
                      <div className="shortcut-description">العملاء</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Alt</kbd> + <kbd>B</kbd>
                      </div>
                      <div className="shortcut-description">الحجوزات</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Alt</kbd> + <kbd>R</kbd>
                      </div>
                      <div className="shortcut-description">التقارير</div>
                    </div>
                    <div className="shortcut-item">
                      <div className="shortcut-keys">
                        <kbd>Esc</kbd>
                      </div>
                      <div className="shortcut-description">إغلاق النافذة</div>
                    </div>
                  </div>
                </CardBody>
              </ModernCard>
            </ModernGrid>
          </div>
        )}
      </div>
    </div>
  );
};

export default HelpCenter;