/* Voucher Template Builder - منشئ قوالب السندات المتطور */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

/* ===== MAIN CONTAINER ===== */
.voucher-template-builder {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.voucher-template-builder::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* ===== HEADER ===== */
.builder-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.builder-header h2 {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.save-btn, .cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.save-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.cancel-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.cancel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* ===== CONTENT ===== */
.builder-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

/* ===== SIDEBAR ===== */
.builder-sidebar {
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 20px 20px 0 0;
  padding: 5px;
  gap: 2px;
}

.tab {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #666;
  font-family: inherit;
  text-align: center;
}

.tab.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab:hover:not(.active) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab-content {
  flex: 1;
  padding: 25px;
  overflow-y: auto;
}

/* ===== FORM ELEMENTS ===== */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e6ed;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  outline: none;
  font-family: inherit;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* ===== LAYOUT GRID ===== */
.layout-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: 10px;
}

.layout-option {
  padding: 15px;
  border: 2px solid #e0e6ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background: white;
}

.layout-option:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.layout-option.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.layout-preview {
  font-size: 32px;
  margin-bottom: 10px;
}

.layout-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.layout-info p {
  font-size: 12px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

/* ===== COLOR GRID ===== */
.color-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.color-option {
  padding: 12px;
  border: 2px solid #e0e6ed;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.color-option:hover {
  border-color: var(--scheme-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.color-option.selected {
  border-color: var(--scheme-color);
  background: linear-gradient(135deg, var(--scheme-color), transparent);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.color-preview {
  font-size: 24px;
}

.color-option span {
  font-size: 12px;
  font-weight: 600;
  color: #2c3e50;
}

/* ===== CHECKBOX GROUP ===== */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin: 0;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
  cursor: pointer;
}

/* ===== SETTINGS GROUP ===== */
.settings-group {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e6ed;
}

.settings-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.settings-group h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* ===== CUSTOM FIELDS ===== */
.custom-fields {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.add-field-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.add-field-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.custom-field-editor {
  background: #f8f9fa;
  border: 1px solid #e0e6ed;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
}

.field-controls {
  display: grid;
  grid-template-columns: 1fr 1fr auto auto;
  gap: 10px;
  align-items: center;
}

.field-controls input,
.field-controls select {
  margin: 0;
}

.remove-field-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.remove-field-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* ===== PREVIEW ===== */
.builder-preview {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-header {
  background: #f8f9fa;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e6ed;
}

.preview-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.preview-controls {
  display: flex;
  gap: 10px;
}

.zoom-btn, .print-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.zoom-btn:hover, .print-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.preview-container {
  flex: 1;
  padding: 25px;
  overflow: auto;
  background: #f8f9fa;
}

.preview-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transform-origin: top center;
  transition: transform 0.3s ease;
}

/* ===== VOUCHER PREVIEW STYLES ===== */
.voucher-preview {
  max-width: 210mm;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border: 3px solid var(--primary-color, #2563eb);
  border-radius: 15px;
  overflow: hidden;
  font-family: 'Cairo', Arial, sans-serif;
  direction: rtl;
  position: relative;
  min-height: 297mm;
}

.voucher-preview.luxury {
  background: linear-gradient(135deg, #fef7cd 0%, #ffffff 100%);
  border-color: #d4af37;
}

.voucher-preview.minimal {
  background: white;
  border: 2px solid #e5e7eb;
}

.voucher-preview.classic {
  background: #ffffff;
  border: 4px double var(--primary-color, #2563eb);
}

.watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 120px;
  color: rgba(0, 0, 0, 0.05);
  font-weight: bold;
  z-index: 1;
  pointer-events: none;
  user-select: none;
}

.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30px;
  border-bottom: 3px solid var(--primary-color, #2563eb);
  position: relative;
  z-index: 2;
}

.luxury .voucher-header {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), transparent);
}

.company-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  font-size: 48px;
  background: linear-gradient(135deg, var(--primary-color, #2563eb), var(--secondary-color, #3b82f6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.company-info h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color, #1e40af);
  margin: 0 0 5px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.company-name-en {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  font-style: italic;
}

.company-details {
  margin-top: 15px;
  color: #475569;
  font-size: 14px;
  line-height: 1.8;
}

.company-details p {
  margin: 2px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.voucher-type-section {
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color, #2563eb), var(--secondary-color, #3b82f6));
  color: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.voucher-type-section.payment {
  background: linear-gradient(135deg, #dc2626, #ef4444);
}

.voucher-type-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.voucher-type-section h2 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.voucher-number {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 15px;
  border-radius: 8px;
  display: inline-block;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.voucher-body {
  padding: 30px;
  position: relative;
  z-index: 2;
}

.voucher-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.info-section {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 20px;
  border-radius: 12px;
  border-right: 4px solid var(--primary-color, #2563eb);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.info-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color, #1e40af);
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-section p {
  margin: 8px 0;
  font-size: 14px;
  color: #374151;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-section {
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  margin: 25px 0;
  border: 2px solid #f59e0b;
  box-shadow: 0 15px 35px rgba(245, 158, 11, 0.2);
  position: relative;
  overflow: hidden;
}

.amount-section.payment {
  background: linear-gradient(135deg, #fee2e2, #f87171);
  border-color: #dc2626;
  box-shadow: 0 15px 35px rgba(220, 38, 38, 0.2);
}

.amount-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.amount-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 15px 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.amount-section.payment h3 {
  color: #7f1d1d;
}

.amount-value {
  font-size: 28px;
  font-weight: 700;
  color: #78350f;
  margin: 0 0 10px 0;
  font-family: 'Courier New', monospace;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.amount-section.payment .amount-value {
  color: #7f1d1d;
}

.amount-words {
  font-size: 14px;
  color: #a16207;
  font-weight: 500;
  font-style: italic;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px 15px;
  border-radius: 8px;
  display: inline-block;
  border: 1px solid rgba(161, 98, 7, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.amount-section.payment .amount-words {
  color: #991b1b;
  border-color: rgba(153, 27, 27, 0.3);
}

.description-section {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  padding: 20px;
  border-radius: 12px;
  margin: 20px 0;
  border-right: 4px solid #10b981;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
}

.description-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.description-section p {
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

.custom-field {
  background: #f8fafc;
  padding: 15px;
  border-radius: 10px;
  margin: 15px 0;
  border-right: 3px solid #6366f1;
}

.custom-field h3 {
  font-size: 14px;
  font-weight: 600;
  color: #4338ca;
  margin: 0 0 8px 0;
}

.custom-field p {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  font-family: 'Courier New', monospace;
}

.qr-section {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  border-radius: 12px;
  margin: 20px 0;
  border: 2px dashed #9ca3af;
}

.qr-code {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  border: 2px dashed #9ca3af;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.qr-code::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(156, 163, 175, 0.3), transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.qr-section p {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.voucher-footer {
  padding: 20px 30px;
  border-top: 2px solid #e5e7eb;
  position: relative;
  z-index: 2;
}

.voucher-footer::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #e5e7eb, #9ca3af, #e5e7eb);
}

.signatures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.signature-box {
  text-align: center;
  padding: 15px;
  border: 2px dashed #9ca3af;
  border-radius: 10px;
  min-height: 80px;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  position: relative;
  transition: all 0.3s ease;
}

.signature-box:hover {
  border-color: var(--primary-color, #2563eb);
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.signature-box p {
  font-weight: 600;
  color: #374151;
  margin: 0 0 10px 0;
  font-size: 14px;
}

.signature-line {
  border-bottom: 2px solid #9ca3af;
  margin: 15px 0 10px 0;
  position: relative;
}

.signature-line::before {
  content: '✍️';
  position: absolute;
  right: -15px;
  top: -15px;
  font-size: 16px;
  opacity: 0.5;
}

.signature-box small {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1400px) {
  .builder-content {
    flex-direction: column;
    gap: 15px;
  }
  
  .builder-sidebar {
    width: 100%;
    max-height: 400px;
  }
  
  .tabs {
    justify-content: center;
  }
  
  .tab {
    flex: none;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .voucher-template-builder {
    padding: 0;
  }
  
  .builder-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .builder-content {
    padding: 15px;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .layout-grid {
    grid-template-columns: 1fr;
  }
  
  .color-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .field-controls {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .voucher-info {
    grid-template-columns: 1fr;
  }
  
  .signatures {
    grid-template-columns: 1fr;
  }
  
  .preview-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .preview-container {
    padding: 15px;
  }
}

/* ===== SCROLLBAR STYLING ===== */
.tab-content::-webkit-scrollbar,
.preview-container::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track,
.preview-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb,
.preview-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover,
.preview-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}