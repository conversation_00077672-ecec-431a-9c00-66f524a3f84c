import React, { useState, useEffect } from 'react';
import agentsService from '../../services/AgentsService';

const AgentsPage = () => {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('list');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSpecialty, setFilterSpecialty] = useState('all');
  const [filterOffice, setFilterOffice] = useState('all');
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showAgentDetails, setShowAgentDetails] = useState(false);
  const [showAgentCustomers, setShowAgentCustomers] = useState(false);
  const [agentCustomers, setAgentCustomers] = useState([]);
  
  // إحصائيات الوكلاء
  const [agentStats, setAgentStats] = useState({
    totalAgents: 0,
    activeAgents: 0,
    topPerformers: 0,
    newThisMonth: 0,
    totalTransactions: 0
  });

  // بيانات الوكيل الجديد
  const [newAgent, setNewAgent] = useState({
    // المعلومات الأساسية
    agentName: '',
    agentCode: '',
    phone: '',
    email: '',
    nationalId: '',
    address: '',
    
    // معلومات الأداء
    totalTransactions: 0,
    currentMonthTransactions: 0,
    rating: 5,
    
    // معلومات الحالة
    status: 'active',
    lastActivity: '',
    notes: '',
    
    // معلومات إضافية
    emergencyContact: '',
    emergencyPhone: '',
    documents: []
  });

  // تحميل البيانات
  useEffect(() => {
    setTimeout(() => {
      const mockAgents = [
        {
          id: 1,
          agentName: 'أحمد محمد السالم',
          agentCode: 'AGT001',
          phone: '+966501234567',
          email: '<EMAIL>',
          nationalId: '1234567890',
          address: 'الرياض، حي النخيل، شارع الملك فهد',
          
          joinDate: '2023-01-15',
          
          totalTransactions: 245,
          currentMonthTransactions: 28,
          rating: 4.8,
          
          status: 'active',
          lastActivity: '2024-01-20',
          notes: 'وكيل متميز في تأشيرات الإمارات',
          
          emergencyContact: 'فاطمة السالم',
          emergencyPhone: '+966507654321',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة الخبرة']
        },
        {
          id: 2,
          agentName: 'فاطمة أحمد الزهراني',
          agentCode: 'AGT002',
          phone: '+966502345678',
          email: '<EMAIL>',
          nationalId: '2345678901',
          address: 'جدة، حي الروضة، شارع التحلية',
          
          joinDate: '2023-03-10',
          
          totalTransactions: 189,
          currentMonthTransactions: 22,
          rating: 4.6,
          
          status: 'active',
          lastActivity: '2024-01-19',
          notes: 'متخصصة في تأشيرات تركيا والدول الأوروبية',
          
          emergencyContact: 'محمد الزهراني',
          emergencyPhone: '+966508765432',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة التدريب']
        },
        {
          id: 3,
          agentName: 'محمد علي حسن',
          agentCode: 'AGT003',
          phone: '+966503456789',
          email: '<EMAIL>',
          nationalId: '3456789012',
          address: 'الدمام، حي الفيصلية، شارع الأمير محمد',
          
          joinDate: '2022-11-20',
          
          totalTransactions: 156,
          currentMonthTransactions: 18,
          rating: 4.4,
          
          status: 'active',
          lastActivity: '2024-01-18',
          notes: 'خبرة واسعة في تأشيرات الدول العربية',
          
          emergencyContact: 'سارة حسن',
          emergencyPhone: '+966509876543',
          documents: ['الهوية الوطنية', 'عقد العمل']
        },
        {
          id: 4,
          agentName: 'سارة محمد الأحمد',
          agentCode: 'AGT004',
          phone: '+966504567890',
          email: '<EMAIL>',
          nationalId: '4567890123',
          address: 'مكة المكرمة، حي العزيزية، شارع إبراهيم الخليل',
          
          joinDate: '2023-06-01',
          
          totalTransactions: 98,
          currentMonthTransactions: 15,
          rating: 4.7,
          
          status: 'active',
          lastActivity: '2024-01-20',
          notes: 'وكيلة جديدة ومتحمسة',
          
          emergencyContact: 'خالد الأحمد',
          emergencyPhone: '+966500987654',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة التأهيل']
        },
        {
          id: 5,
          agentName: 'خالد عبدالله المطيري',
          agentCode: 'AGT005',
          phone: '+966505678901',
          email: '<EMAIL>',
          nationalId: '5678901234',
          address: 'المدينة المنورة، حي قباء، شارع قباء',
          
          joinDate: '2022-08-15',
          
          totalTransactions: 67,
          currentMonthTransactions: 8,
          rating: 4.2,
          
          status: 'inactive',
          lastActivity: '2024-01-10',
          notes: 'في إجازة مؤقتة',
          
          emergencyContact: 'نورا المطيري',
          emergencyPhone: '+966501098765',
          documents: ['الهوية الوطنية', 'عقد العمل', 'شهادة اللغة الإنجليزية']
        }
      ];

      // تحميل الوكلاء من الخدمة المشتركة
      const agentsData = agentsService.getAllAgents();
      setAgents(agentsData);

      // حساب الإحصائيات من الخدمة
      const stats = agentsService.getAgentsStats();
      setAgentStats(stats);

      setLoading(false);
    }, 1000);
  }, []);

  // دالة إضافة وكيل جديد
  const handleAddAgent = (e) => {
    e.preventDefault();
    const agent = agentsService.addAgent(newAgent);
    setAgents(agentsService.getAllAgents());

    // تحديث الإحصائيات
    setAgentStats(agentsService.getAgentsStats());
    
    // إعادة تعيين النموذج
    setNewAgent({
      agentName: '',
      agentCode: '',
      phone: '',
      email: '',
      nationalId: '',
      address: '',
      totalTransactions: 0,
      currentMonthTransactions: 0,
      rating: 5,
      status: 'active',
      lastActivity: '',
      notes: '',
      emergencyContact: '',
      emergencyPhone: '',
      documents: []
    });
    
    setActiveTab('list');
  };

  // دالة عرض تفاصيل الوكيل
  const handleViewAgent = (agent) => {
    setSelectedAgent(agent);
    setShowAgentDetails(true);
  };

  // دالة تعديل الوكيل
  const handleEditAgent = (agent) => {
    setSelectedAgent(agent);
    setNewAgent({
      agentName: agent.agentName,
      agentCode: agent.agentCode,
      phone: agent.phone,
      email: agent.email,
      nationalId: agent.nationalId,
      address: agent.address,
      joinDate: agent.joinDate,
      totalTransactions: agent.totalTransactions,
      currentMonthTransactions: agent.currentMonthTransactions,
      rating: agent.rating,
      status: agent.status,
      lastActivity: agent.lastActivity,
      notes: agent.notes,
      emergencyContact: agent.emergencyContact,
      emergencyPhone: agent.emergencyPhone,
      documents: agent.documents
    });
    setActiveTab('edit');
  };

  // دالة طباعة تفاصيل الوكيل
  const handlePrintAgent = (agent) => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تفاصيل الوكيل - ${agent.agentName}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
          }
          .report-title {
            font-size: 18px;
            color: #333;
          }
          .agent-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
          }
          .info-section {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
          }
          .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
          }
          .info-label {
            font-weight: bold;
            color: #555;
          }
          .info-value {
            color: #333;
          }
          .performance-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">شركة شراء السفر</div>
          <div class="report-title">تقرير تفاصيل الوكيل</div>
          <div style="font-size: 14px; color: #666; margin-top: 10px;">
            تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}
          </div>
        </div>

        <div class="agent-info">
          <div class="info-section">
            <div class="section-title">المعلومات الأساسية</div>
            <div class="info-row">
              <span class="info-label">اسم الوكيل:</span>
              <span class="info-value">${agent.agentName}</span>
            </div>
            <div class="info-row">
              <span class="info-label">كود الوكيل:</span>
              <span class="info-value">${agent.agentCode}</span>
            </div>
            <div class="info-row">
              <span class="info-label">رقم الهاتف:</span>
              <span class="info-value">${agent.phone}</span>
            </div>
            <div class="info-row">
              <span class="info-label">البريد الإلكتروني:</span>
              <span class="info-value">${agent.email}</span>
            </div>
            <div class="info-row">
              <span class="info-label">رقم الهوية:</span>
              <span class="info-value">${agent.nationalId}</span>
            </div>
            <div class="info-row">
              <span class="info-label">العنوان:</span>
              <span class="info-value">${agent.address}</span>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">معلومات العمل</div>
            <div class="info-row">
              <span class="info-label">المكتب:</span>
              <span class="info-value">${agent.office}</span>
            </div>
            <div class="info-row">
              <span class="info-label">التخصص:</span>
              <span class="info-value">${agent.specialty}</span>
            </div>
            <div class="info-row">
              <span class="info-label">تاريخ الانضمام:</span>
              <span class="info-value">${agent.joinDate}</span>
            </div>
            <div class="info-row">
              <span class="info-label">نوع العقد:</span>
              <span class="info-value">${agent.contractType}</span>
            </div>
            <div class="info-row">
              <span class="info-label">معدل العمولة:</span>
              <span class="info-value">${agent.commissionRate}%</span>
            </div>
            <div class="info-row">
              <span class="info-label">الهدف الشهري:</span>
              <span class="info-value">${agent.targetMonthly.toLocaleString()} ريال</span>
            </div>
          </div>
        </div>

        <div class="performance-section">
          <div class="section-title">الأداء والإحصائيات</div>
          <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; text-align: center;">
            <div>
              <div style="font-size: 24px; font-weight: bold; color: #007bff;">${agent.totalTransactions}</div>
              <div style="color: #666;">إجمالي المعاملات</div>
            </div>

            <div>
              <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">${agent.currentMonthTransactions}</div>
              <div style="color: #666;">معاملات هذا الشهر</div>
            </div>
            <div>
              <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${agent.rating}/5</div>
              <div style="color: #666;">التقييم</div>
            </div>
          </div>
        </div>

        <div class="info-section">
          <div class="section-title">معلومات إضافية</div>
          <div class="info-row">
            <span class="info-label">الحالة:</span>
            <span class="info-value">${agent.status === 'active' ? 'نشط' : 'غير نشط'}</span>
          </div>
          <div class="info-row">
            <span class="info-label">آخر نشاط:</span>
            <span class="info-value">${agent.lastActivity}</span>
          </div>

          <div class="info-row">
            <span class="info-label">جهة الاتصال الطارئ:</span>
            <span class="info-value">${agent.emergencyContact} - ${agent.emergencyPhone}</span>
          </div>
          ${agent.notes ? `
          <div class="info-row">
            <span class="info-label">ملاحظات:</span>
            <span class="info-value">${agent.notes}</span>
          </div>
          ` : ''}
        </div>

        <div class="footer">
          <p>هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة الوكلاء - شركة شراء السفر</p>
          <p>للاستفسارات: <EMAIL> | +966-11-1234567</p>
        </div>
      </body>
      </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
    }, 250);
  };

  // دالة طباعة قائمة عملاء الوكيل
  const handlePrintAgentCustomers = (agent, customers) => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>قائمة عملاء الوكيل - ${agent.agentName}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
          }
          .report-title {
            font-size: 18px;
            color: #333;
          }
          .agent-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
          }
          .customers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .customers-table th,
          .customers-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
          }
          .customers-table th {
            background: #007bff;
            color: white;
            font-weight: bold;
          }
          .customers-table tr:nth-child(even) {
            background: #f8f9fa;
          }
          .status-completed { color: #28a745; font-weight: bold; }
          .status-in_progress { color: #ffc107; font-weight: bold; }
          .status-pending { color: #dc3545; font-weight: bold; }
          .status-embassy_execution { color: #17a2b8; font-weight: bold; }
          .summary-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
          }
          .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            text-align: center;
          }
          .summary-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
          }
          .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
          }
          .summary-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">شركة شراء السفر</div>
          <div class="report-title">قائمة عملاء الوكيل</div>
          <div style="font-size: 14px; color: #666; margin-top: 10px;">
            تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}
          </div>
        </div>

        <div class="agent-info">
          <h3 style="margin: 0 0 15px 0; color: #007bff;">معلومات الوكيل</h3>
          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
            <div><strong>اسم الوكيل:</strong> ${agent.agentName}</div>
            <div><strong>كود الوكيل:</strong> ${agent.agentCode}</div>
            <div><strong>التخصص:</strong> ${agent.specialty}</div>
            <div><strong>المكتب:</strong> ${agent.office}</div>
            <div><strong>الهاتف:</strong> ${agent.phone}</div>
            <div><strong>معدل العمولة:</strong> ${agent.commissionRate}%</div>
          </div>
        </div>

        <div class="summary-section">
          <h3 style="margin: 0 0 20px 0; color: #333; text-align: center;">ملخص العملاء</h3>
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-number">${customers.length}</div>
              <div class="summary-label">إجمالي العملاء</div>
            </div>
            <div class="summary-item">
              <div class="summary-number">${customers.filter(c => c.transactionStatus === 'completed').length}</div>
              <div class="summary-label">معاملات مكتملة</div>
            </div>
            <div class="summary-item">
              <div class="summary-number">${customers.reduce((sum, c) => sum + c.totalSpent, 0).toLocaleString()} ريال</div>
              <div class="summary-label">إجمالي المبيعات</div>
            </div>
            <div class="summary-item">
              <div class="summary-number">${(customers.reduce((sum, c) => sum + c.totalSpent, 0) * (agent.commissionRate / 100)).toLocaleString()} ريال</div>
              <div class="summary-label">العمولة المحققة</div>
            </div>
          </div>
        </div>

        ${customers.length > 0 ? `
        <table class="customers-table">
          <thead>
            <tr>
              <th>اسم العميل</th>
              <th>رقم الهاتف</th>
              <th>رقم الجواز</th>
              <th>نوع التأشيرة</th>
              <th>الدولة</th>
              <th>حالة المعاملة</th>
              <th>تاريخ التسجيل</th>
              <th>المبلغ المدفوع</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            ${customers.map(customer => `
              <tr>
                <td>${customer.customerName}</td>
                <td>${customer.phone}</td>
                <td>${customer.passportNumber}</td>
                <td>${customer.visaType === 'tourist' ? 'سياحية' : 
                     customer.visaType === 'business' ? 'عمل' : 
                     customer.visaType === 'medical' ? 'علاج' : customer.visaType}</td>
                <td>${customer.country}</td>
                <td class="status-${customer.transactionStatus}">
                  ${customer.transactionStatus === 'completed' ? 'مكتملة' :
                    customer.transactionStatus === 'in_progress' ? 'قيد المعالجة' :
                    customer.transactionStatus === 'pending' ? 'في الانتظار' :
                    customer.transactionStatus === 'embassy_execution' ? 'تنفيذ السفارة' : customer.transactionStatus}
                </td>
                <td>${customer.registrationDate}</td>
                <td>${customer.totalSpent.toLocaleString()} ريال</td>
                <td>${customer.notes}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        ` : `
        <div style="text-align: center; padding: 40px; color: #666;">
          <h3>لا يوجد عملاء</h3>
          <p>لم يتم تسجيل أي عملاء لهذا الوكيل بعد</p>
        </div>
        `}

        <div class="footer">
          <p>هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة الوكلاء - شركة شراء السفر</p>
          <p>للاستفسارات: <EMAIL> | +966-11-1234567</p>
        </div>
      </body>
      </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
    }, 250);
  };

  // دالة حفظ التعديلات
  const handleSaveEdit = (e) => {
    e.preventDefault();
    const updatedAgent = agentsService.updateAgent(selectedAgent.id, newAgent);
    setAgents(agentsService.getAllAgents());

    // تحديث الإحصائيات
    setAgentStats(agentsService.getAgentsStats());
    setActiveTab('list');
    setSelectedAgent(null);
    
    // إعادة تعيين النموذج
    setNewAgent({
      agentName: '',
      agentCode: '',
      phone: '',
      email: '',
      nationalId: '',
      address: '',
      joinDate: '',
      totalTransactions: 0,
      currentMonthTransactions: 0,
      rating: 5,
      status: 'active',
      lastActivity: '',
      notes: '',
      emergencyContact: '',
      emergencyPhone: '',
      documents: []
    });
  };

  // دالة عرض عملاء الوكيل
  const handleViewAgentCustomers = (agent) => {
    setSelectedAgent(agent);
    
    // بيانات تجريبية لعملاء كل وكيل
    const mockCustomersByAgent = {
      'AGT001': [ // أحمد محمد السالم - تأشيرات الإمارات
        {
          id: 1,
          customerName: 'محمد أحمد الشهري',
          phone: '+966501234567',
          passportNumber: '*********',
          visaType: 'tourist',
          country: 'الإمارات العربية المتحدة',
          transactionStatus: 'completed',
          registrationDate: '2024-01-15',
          totalSpent: 1200,
          notes: 'عميل متميز - تأشيرة سياحية'
        },
        {
          id: 2,
          customerName: 'فاطمة علي القحطاني',
          phone: '+966502345678',
          passportNumber: '*********',
          visaType: 'business',
          country: 'الإمارات العربية المتحدة',
          transactionStatus: 'in_progress',
          registrationDate: '2024-01-18',
          totalSpent: 1800,
          notes: 'تأشيرة عمل - قيد المعالجة'
        },
        {
          id: 3,
          customerName: 'عبدالله محمد النعيمي',
          phone: '+966503456789',
          passportNumber: 'C34567890',
          visaType: 'tourist',
          country: 'الإمارات العربية المتحدة',
          transactionStatus: 'completed',
          registrationDate: '2024-01-20',
          totalSpent: 1100,
          notes: 'تأشيرة سياحية عائلية'
        }
      ],
      'AGT002': [ // فاطمة أحمد الزهراني - تأشيرات تركيا
        {
          id: 4,
          customerName: 'سارة خالد المالكي',
          phone: '+966504567890',
          passportNumber: 'D45678901',
          visaType: 'tourist',
          country: 'تركيا',
          transactionStatus: 'completed',
          registrationDate: '2024-01-12',
          totalSpent: 900,
          notes: 'تأشيرة سياحية - إسطنبول'
        },
        {
          id: 5,
          customerName: 'أحمد سعد الغامدي',
          phone: '+966505678901',
          passportNumber: 'E56789012',
          visaType: 'business',
          country: 'تركيا',
          transactionStatus: 'embassy_execution',
          registrationDate: '2024-01-16',
          totalSpent: 1300,
          notes: 'تأشيرة عمل - أنقرة'
        }
      ],
      'AGT003': [ // محمد علي حسن - تأشيرات مصر
        {
          id: 6,
          customerName: 'نورا عبدالرحمن الدوسري',
          phone: '+966506789012',
          passportNumber: 'F67890123',
          visaType: 'tourist',
          country: 'مصر',
          transactionStatus: 'completed',
          registrationDate: '2024-01-14',
          totalSpent: 500,
          notes: 'تأشيرة سياحية - القاهرة'
        },
        {
          id: 7,
          customerName: 'خالد محمد العتيبي',
          phone: '+966507890123',
          passportNumber: 'G78901234',
          visaType: 'medical',
          country: 'مصر',
          transactionStatus: 'in_progress',
          registrationDate: '2024-01-19',
          totalSpent: 800,
          notes: 'تأشيرة علاج - القاهرة'
        }
      ],
      'AGT004': [ // سارة محمد الأحمد - تأشيرات الهند
        {
          id: 8,
          customerName: 'عبدالعزيز أحمد الحربي',
          phone: '+966508901234',
          passportNumber: 'H89012345',
          visaType: 'business',
          country: 'الهند',
          transactionStatus: 'completed',
          registrationDate: '2024-01-17',
          totalSpent: 700,
          notes: 'تأشيرة عمل - نيودلهي'
        }
      ],
      'AGT005': [ // خالد عبدالله المطيري - تأشيرات أمريكا
        {
          id: 9,
          customerName: 'ريم سعود الرشيد',
          phone: '+966509012345',
          passportNumber: 'I90123456',
          visaType: 'tourist',
          country: 'الولايات المتحدة الأمريكية',
          transactionStatus: 'pending',
          registrationDate: '2024-01-21',
          totalSpent: 2500,
          notes: 'تأشيرة سياحية - نيويورك'
        }
      ]
    };

    const customers = mockCustomersByAgent[agent.agentCode] || [];
    setAgentCustomers(customers);
    setShowAgentCustomers(true);
  };

  // دالة تصفية الوكلاء
  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.agentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.agentCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.phone.includes(searchTerm) ||
                         agent.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || agent.status === filterStatus;
    const matchesSpecialty = filterSpecialty === 'all' || agent.specialty.includes(filterSpecialty);
    const matchesOffice = filterOffice === 'all' || agent.office.includes(filterOffice);
    
    return matchesSearch && matchesStatus && matchesSpecialty && matchesOffice;
  });

  // دوال مساعدة
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#28a745';
      case 'inactive': return '#6c757d';
      case 'suspended': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'suspended': return 'موقوف';
      default: return 'غير محدد';
    }
  };

  const getPerformanceColor = (rating) => {
    if (rating >= 4.5) return '#28a745';
    if (rating >= 4.0) return '#ffc107';
    if (rating >= 3.5) return '#fd7e14';
    return '#dc3545';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '60vh',
        fontSize: '18px',
        color: '#666'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>⏳</div>
          جاري تحميل بيانات الوكلاء...
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Cairo, Arial, sans-serif', direction: 'rtl' }}>
      {/* العنوان الرئيسي */}
      <div style={{ marginBottom: '30px' }}>
        <h1 style={{ 
          color: '#2c3e50', 
          marginBottom: '10px',
          display: 'flex',
          alignItems: 'center',
          gap: '15px'
        }}>
          🤝 إدارة الوكلاء
        </h1>
        <p style={{ color: '#7f8c8d', margin: 0 }}>
          إدارة شاملة لجميع الوكلاء ومتابعة أدائهم وعمولاتهم
        </p>
      </div>

      {/* الإحصائيات */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '25px',
          borderRadius: '15px',
          textAlign: 'center',
          boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)'
        }}>
          <div style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '5px' }}>
            {agentStats.totalAgents}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.9 }}>إجمالي الوكلاء</div>
        </div>

        <div style={{
          background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
          color: 'white',
          padding: '25px',
          borderRadius: '15px',
          textAlign: 'center',
          boxShadow: '0 4px 15px rgba(40, 167, 69, 0.4)'
        }}>
          <div style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '5px' }}>
            {agentStats.activeAgents}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.9 }}>الوكلاء النشطون</div>
        </div>

        <div style={{
          background: 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
          color: 'white',
          padding: '25px',
          borderRadius: '15px',
          textAlign: 'center',
          boxShadow: '0 4px 15px rgba(255, 193, 7, 0.4)'
        }}>
          <div style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '5px' }}>
            {agentStats.topPerformers}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.9 }}>الوكلاء المتميزون</div>
        </div>



        <div style={{
          background: 'linear-gradient(135deg, #e83e8c 0%, #d63384 100%)',
          color: 'white',
          padding: '25px',
          borderRadius: '15px',
          textAlign: 'center',
          boxShadow: '0 4px 15px rgba(232, 62, 140, 0.4)'
        }}>
          <div style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '5px' }}>
            {agentStats.totalTransactions}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.9 }}>إجمالي المعاملات</div>
        </div>

        <div style={{
          background: 'linear-gradient(135deg, #6f42c1 0%, #563d7c 100%)',
          color: 'white',
          padding: '25px',
          borderRadius: '15px',
          textAlign: 'center',
          boxShadow: '0 4px 15px rgba(111, 66, 193, 0.4)'
        }}>
          <div style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '5px' }}>
            {agentStats.newThisMonth}
          </div>
          <div style={{ fontSize: '14px', opacity: 0.9 }}>جدد هذا الشهر</div>
        </div>
      </div>

      {/* التبويبات */}
      <div style={{ marginBottom: '30px' }}>
        <div style={{ 
          display: 'flex', 
          gap: '10px', 
          borderBottom: '2px solid #e9ecef',
          marginBottom: '20px'
        }}>
          {[
            { id: 'list', label: 'قائمة الوكلاء', icon: '📋' },
            { id: 'add', label: 'إضافة وكيل جديد', icon: '➕' },
            ...(activeTab === 'edit' ? [{ id: 'edit', label: `تعديل الوكيل: ${selectedAgent?.agentName}`, icon: '✏️' }] : [])
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                padding: '12px 20px',
                border: 'none',
                background: activeTab === tab.id ? '#007bff' : 'transparent',
                color: activeTab === tab.id ? 'white' : '#666',
                borderRadius: '8px 8px 0 0',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              <span>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* قائمة الوكلاء */}
      {activeTab === 'list' && (
        <div style={{
          background: 'white',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          padding: '30px'
        }}>
          <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>📋 قائمة الوكلاء</h2>
          
          {/* أدوات البحث والتصفية */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '15px', 
            marginBottom: '25px',
            padding: '20px',
            background: '#f8f9fa',
            borderRadius: '10px'
          }}>
            <div>
              <input
                type="text"
                placeholder="🔍 البحث في الوكلاء..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '14px',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            <div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '14px',
                  boxSizing: 'border-box'
                }}
              >
                <option value="all">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
                <option value="suspended">موقوف</option>
              </select>
            </div>
            <div>
              <select
                value={filterSpecialty}
                onChange={(e) => setFilterSpecialty(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '14px',
                  boxSizing: 'border-box'
                }}
              >
                <option value="all">جميع التخصصات</option>
                <option value="الإمارات">تأشيرات الإمارات</option>
                <option value="تركيا">تأشيرات تركيا</option>
                <option value="مصر">تأشيرات مصر</option>
                <option value="الهند">تأشيرات الهند</option>
                <option value="أمريكا">تأشيرات أمريكا</option>
              </select>
            </div>
            <div>
              <select
                value={filterOffice}
                onChange={(e) => setFilterOffice(e.target.value)}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '14px',
                  boxSizing: 'border-box'
                }}
              >
                <option value="all">جميع المكاتب</option>
                <option value="الرياض">مكتب الرياض</option>
                <option value="جدة">مكتب جدة</option>
                <option value="الدمام">مكتب الدمام</option>
                <option value="مكة">مكتب مكة</option>
                <option value="المدينة">مكتب المدينة</option>
              </select>
            </div>
          </div>
          
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ background: '#f8f9fa' }}>
                  <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الوكيل</th>
                  <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الأداء</th>
                  <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الحالة</th>
                  <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredAgents.map((agent) => (
                  <tr key={agent.id} style={{ borderBottom: '1px solid #dee2e6' }}>
                    <td style={{ padding: '15px' }}>
                      <div style={{ fontWeight: 'bold', color: '#2c3e50', marginBottom: '5px' }}>
                        {agent.agentName}
                      </div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '3px' }}>
                        كود: {agent.agentCode}
                      </div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '3px' }}>
                        📱 {agent.phone}
                      </div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                        📧 {agent.email}
                      </div>
                    </td>
                    <td style={{ padding: '15px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '5px' }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold' }}>⭐</span>
                        <span style={{ 
                          color: getPerformanceColor(agent.rating), 
                          fontWeight: 'bold' 
                        }}>
                          {agent.rating}
                        </span>
                      </div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '3px' }}>
                        📊 {agent.totalTransactions} معاملة
                      </div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                        📈 {agent.currentMonthTransactions} هذا الشهر
                      </div>
                    </td>
                    <td style={{ padding: '15px' }}>
                      <span style={{
                        background: getStatusColor(agent.status),
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}>
                        {getStatusText(agent.status)}
                      </span>
                      <div style={{ fontSize: '11px', color: '#7f8c8d', marginTop: '5px' }}>
                        آخر نشاط: {formatDate(agent.lastActivity)}
                      </div>
                    </td>
                    <td style={{ padding: '15px', textAlign: 'center' }}>
                      <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', flexWrap: 'wrap' }}>
                        <button
                          onClick={() => handleViewAgent(agent)}
                          style={{
                            background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                            color: 'white',
                            border: 'none',
                            padding: '6px 10px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '11px',
                            fontWeight: 'bold',
                            minWidth: '60px'
                          }}
                        >
                          👁️ عرض
                        </button>
                        <button
                          onClick={() => handleViewAgentCustomers(agent)}
                          style={{
                            background: 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)',
                            color: 'white',
                            border: 'none',
                            padding: '6px 10px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '11px',
                            fontWeight: 'bold',
                            minWidth: '60px'
                          }}
                        >
                          👥 العملاء
                        </button>
                        <button
                          onClick={() => handleEditAgent(agent)}
                          style={{
                            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                            color: 'white',
                            border: 'none',
                            padding: '6px 10px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '11px',
                            fontWeight: 'bold',
                            minWidth: '60px'
                          }}
                        >
                          ✏️ تعديل
                        </button>
                        <button
                          onClick={() => handlePrintAgent(agent)}
                          style={{
                            background: 'linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%)',
                            color: 'white',
                            border: 'none',
                            padding: '6px 10px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '11px',
                            fontWeight: 'bold',
                            minWidth: '60px'
                          }}
                        >
                          🖨️ طباعة
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredAgents.length === 0 && (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#7f8c8d'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '15px' }}>🔍</div>
              <h3>لا توجد نتائج</h3>
              <p>لم يتم العثور على وكلاء مطابقين لمعايير البحث</p>
            </div>
          )}
        </div>
      )}

      {/* نموذج إضافة وكيل جديد */}
      {activeTab === 'add' && (
        <div style={{
          background: 'white',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          padding: '30px'
        }}>
          <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>➕ إضافة وكيل جديد</h2>
          
          <form onSubmit={handleAddAgent}>
            {/* المعلومات الأساسية */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#3498db', marginBottom: '20px', borderBottom: '2px solid #3498db20', paddingBottom: '10px' }}>
                👤 المعلومات الأساسية
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم الوكيل</label>
                  <input
                    type="text"
                    value={newAgent.agentName}
                    onChange={(e) => setNewAgent({...newAgent, agentName: e.target.value})}
                    required
                    placeholder="مثال: أحمد محمد السالم"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>كود الوكيل</label>
                  <input
                    type="text"
                    value={newAgent.agentCode}
                    onChange={(e) => setNewAgent({...newAgent, agentCode: e.target.value})}
                    required
                    placeholder="مثال: AGT006"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الجوال</label>
                  <input
                    type="tel"
                    value={newAgent.phone}
                    onChange={(e) => setNewAgent({...newAgent, phone: e.target.value})}
                    required
                    placeholder="مثال: +966501234567"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={newAgent.email}
                    onChange={(e) => setNewAgent({...newAgent, email: e.target.value})}
                    required
                    placeholder="مثال: <EMAIL>"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهوية الوطنية</label>
                  <input
                    type="text"
                    value={newAgent.nationalId}
                    onChange={(e) => setNewAgent({...newAgent, nationalId: e.target.value})}
                    required
                    placeholder="مثال: 1234567890"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
              
              <div style={{ marginTop: '15px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العنوان</label>
                <textarea
                  value={newAgent.address}
                  onChange={(e) => setNewAgent({...newAgent, address: e.target.value})}
                  required
                  rows="2"
                  placeholder="مثال: الرياض، حي النخيل، شارع الملك فهد"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>
            </div>



            {/* معلومات إضافية */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#e67e22', marginBottom: '20px', borderBottom: '2px solid #e67e2220', paddingBottom: '10px' }}>
                📋 معلومات إضافية
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>جهة الاتصال الطارئ</label>
                  <input
                    type="text"
                    value={newAgent.emergencyContact}
                    onChange={(e) => setNewAgent({...newAgent, emergencyContact: e.target.value})}
                    placeholder="مثال: فاطمة السالم"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الاتصال الطارئ</label>
                  <input
                    type="tel"
                    value={newAgent.emergencyPhone}
                    onChange={(e) => setNewAgent({...newAgent, emergencyPhone: e.target.value})}
                    placeholder="مثال: +966507654321"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>
              
              <div style={{ marginTop: '15px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newAgent.notes}
                  onChange={(e) => setNewAgent({...newAgent, notes: e.target.value})}
                  rows="3"
                  placeholder="ملاحظات إضافية حول الوكيل..."
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
              <button
                type="button"
                onClick={() => setActiveTab('list')}
                style={{
                  padding: '12px 25px',
                  border: '2px solid #e0e0e0',
                  background: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إلغاء
              </button>
              <button
                type="submit"
                style={{
                  padding: '12px 25px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                إضافة الوكيل
              </button>
            </div>
          </form>
        </div>
      )}

      {/* نافذة تفاصيل الوكيل */}
      {showAgentDetails && selectedAgent && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '15px',
            padding: '30px',
            maxWidth: '800px',
            width: '90%',
            maxHeight: '90vh',
            overflow: 'auto',
            boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '25px' }}>
              <h2 style={{ color: '#2c3e50', margin: 0 }}>🤝 تفاصيل الوكيل</h2>
              <button
                onClick={() => setShowAgentDetails(false)}
                style={{
                  background: '#e74c3c',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '35px',
                  height: '35px',
                  cursor: 'pointer',
                  fontSize: '18px'
                }}
              >
                ×
              </button>
            </div>

            {/* معلومات الوكيل الأساسية */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ color: '#34495e', margin: '0 0 15px 0' }}>👤 المعلومات الأساسية</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div><strong>الاسم:</strong> {selectedAgent.agentName}</div>
                <div><strong>كود الوكيل:</strong> {selectedAgent.agentCode}</div>
                <div><strong>الجوال:</strong> {selectedAgent.phone}</div>
                <div><strong>البريد الإلكتروني:</strong> {selectedAgent.email}</div>
                <div><strong>رقم الهوية:</strong> {selectedAgent.nationalId}</div>
                <div><strong>تاريخ الانضمام:</strong> {formatDate(selectedAgent.joinDate)}</div>
              </div>
              <div style={{ marginTop: '15px' }}>
                <strong>العنوان:</strong> {selectedAgent.address}
              </div>
            </div>

            {/* الحالة */}
            <div style={{ 
              background: '#e8f5e8', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#27ae60' }}>📊 الحالة</h4>
              <div>
                <strong>الحالة:</strong> 
                <span style={{
                  background: getStatusColor(selectedAgent.status),
                  color: 'white',
                  padding: '3px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  marginRight: '8px'
                }}>
                  {getStatusText(selectedAgent.status)}
                </span>
              </div>
            </div>

            {/* الأداء والإحصائيات */}
            <div style={{ 
              background: '#fff3cd', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#856404' }}>📊 الأداء والإحصائيات</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                <div>
                  <strong>التقييم:</strong>
                  <div style={{ 
                    color: getPerformanceColor(selectedAgent.rating), 
                    fontWeight: 'bold', 
                    fontSize: '18px' 
                  }}>
                    ⭐ {selectedAgent.rating}
                  </div>
                </div>
                <div>
                  <strong>إجمالي المعاملات:</strong>
                  <div style={{ color: '#3498db', fontWeight: 'bold', fontSize: '18px' }}>
                    {selectedAgent.totalTransactions}
                  </div>
                </div>

                <div>
                  <strong>معاملات هذا الشهر:</strong>
                  <div style={{ color: '#e67e22', fontWeight: 'bold', fontSize: '16px' }}>
                    {selectedAgent.currentMonthTransactions}
                  </div>
                </div>

                <div>
                  <strong>آخر نشاط:</strong>
                  <div style={{ color: '#7f8c8d', fontSize: '14px' }}>
                    {formatDate(selectedAgent.lastActivity)}
                  </div>
                </div>
              </div>
            </div>

            {/* معلومات إضافية */}
            <div style={{ 
              background: '#f0f8ff', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#0066cc' }}>📋 معلومات إضافية</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>

                <div><strong>جهة الاتصال الطارئ:</strong> {selectedAgent.emergencyContact}</div>
                <div><strong>رقم الاتصال الطارئ:</strong> {selectedAgent.emergencyPhone}</div>
                <div>
                  <strong>المستندات:</strong>
                  <div style={{ fontSize: '12px', color: '#7f8c8d', marginTop: '5px' }}>
                    {selectedAgent.documents.join(', ')}
                  </div>
                </div>
              </div>
              {selectedAgent.notes && (
                <div style={{ marginTop: '15px' }}>
                  <strong>الملاحظات:</strong>
                  <div style={{ 
                    background: 'white', 
                    padding: '10px', 
                    borderRadius: '5px', 
                    marginTop: '5px',
                    fontSize: '14px',
                    color: '#495057'
                  }}>
                    {selectedAgent.notes}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* نافذة عرض عملاء الوكيل */}
      {showAgentCustomers && selectedAgent && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '15px',
            padding: '30px',
            maxWidth: '1000px',
            width: '95%',
            maxHeight: '90vh',
            overflow: 'auto',
            boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '25px' }}>
              <div>
                <h2 style={{ color: '#2c3e50', margin: 0 }}>👥 عملاء الوكيل</h2>
                <p style={{ color: '#7f8c8d', margin: '5px 0 0 0', fontSize: '14px' }}>
                  عملاء الوكيل: <strong>{selectedAgent.agentName}</strong> ({selectedAgent.agentCode})
                </p>
              </div>
              <div style={{ display: 'flex', gap: '10px' }}>
                <button
                  onClick={() => handlePrintAgentCustomers(selectedAgent, agentCustomers)}
                  style={{
                    background: '#6f42c1',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '8px 15px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                  }}
                >
                  🖨️ طباعة القائمة
                </button>
                <button
                  onClick={() => setShowAgentCustomers(false)}
                  style={{
                    background: '#e74c3c',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '35px',
                    height: '35px',
                    cursor: 'pointer',
                    fontSize: '18px'
                  }}
                >
                  ×
                </button>
              </div>
            </div>

            {/* معلومات الوكيل المختصرة */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '15px', 
              borderRadius: '10px',
              marginBottom: '20px',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '15px'
            }}>
              <div><strong>المكتب:</strong> {selectedAgent.office}</div>
              <div><strong>التخصص:</strong> {selectedAgent.specialty}</div>
              <div><strong>عدد العملاء:</strong> {agentCustomers.length} عميل</div>
              <div><strong>التقييم:</strong> ⭐ {selectedAgent.rating}</div>
            </div>

            {agentCustomers.length > 0 ? (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ background: '#f8f9fa' }}>
                      <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>العميل</th>
                      <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>التأشيرة</th>
                      <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الحالة</th>
                      <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>المبلغ</th>
                      <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>التاريخ</th>
                      <th style={{ padding: '12px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>الملاحظات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agentCustomers.map((customer) => (
                      <tr key={customer.id} style={{ borderBottom: '1px solid #dee2e6' }}>
                        <td style={{ padding: '12px' }}>
                          <div style={{ fontWeight: 'bold', color: '#2c3e50', marginBottom: '3px' }}>
                            {customer.customerName}
                          </div>
                          <div style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '2px' }}>
                            📱 {customer.phone}
                          </div>
                          <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                            🛂 {customer.passportNumber}
                          </div>
                        </td>
                        <td style={{ padding: '12px' }}>
                          <div style={{ fontWeight: 'bold', color: '#495057', marginBottom: '3px' }}>
                            {customer.country}
                          </div>
                          <div style={{ fontSize: '12px', color: '#7f8c8d' }}>
                            {customer.visaType === 'tourist' ? '🏖️ سياحية' :
                             customer.visaType === 'business' ? '💼 عمل' :
                             customer.visaType === 'medical' ? '🏥 علاج' : '📋 أخرى'}
                          </div>
                        </td>
                        <td style={{ padding: '12px' }}>
                          <span style={{
                            background: customer.transactionStatus === 'completed' ? '#28a745' :
                                       customer.transactionStatus === 'in_progress' ? '#ffc107' :
                                       customer.transactionStatus === 'embassy_execution' ? '#17a2b8' :
                                       customer.transactionStatus === 'pending' ? '#6c757d' : '#dc3545',
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: 'bold'
                          }}>
                            {customer.transactionStatus === 'completed' ? '✅ مكتملة' :
                             customer.transactionStatus === 'in_progress' ? '⏳ قيد المعالجة' :
                             customer.transactionStatus === 'embassy_execution' ? '🏛️ في السفارة' :
                             customer.transactionStatus === 'pending' ? '⏸️ معلقة' : '❌ ملغية'}
                          </span>
                        </td>
                        <td style={{ padding: '12px' }}>
                          <div style={{ fontWeight: 'bold', color: '#28a745', fontSize: '14px' }}>
                            {formatCurrency(customer.totalSpent)}
                          </div>
                        </td>
                        <td style={{ padding: '12px' }}>
                          <div style={{ fontSize: '13px', color: '#495057' }}>
                            {formatDate(customer.registrationDate)}
                          </div>
                        </td>
                        <td style={{ padding: '12px' }}>
                          <div style={{ fontSize: '12px', color: '#7f8c8d', maxWidth: '200px' }}>
                            {customer.notes}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* إحصائيات العملاء */}
                <div style={{ 
                  marginTop: '20px',
                  padding: '15px',
                  background: '#e8f5e8',
                  borderRadius: '10px',
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '15px'
                }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>
                      {agentCustomers.length}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6c757d' }}>إجمالي العملاء</div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>
                      {agentCustomers.filter(c => c.transactionStatus === 'completed').length}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6c757d' }}>معاملات مكتملة</div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>
                      {formatCurrency(agentCustomers.reduce((sum, c) => sum + c.totalSpent, 0))}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6c757d' }}>إجمالي المبيعات</div>
                  </div>

                </div>
              </div>
            ) : (
              <div style={{
                textAlign: 'center',
                padding: '40px',
                color: '#7f8c8d'
              }}>
                <div style={{ fontSize: '48px', marginBottom: '15px' }}>👥</div>
                <h3>لا يوجد عملاء</h3>
                <p>لم يتم تسجيل أي عملاء لهذا الوكيل بعد</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* نموذج تعديل الوكيل */}
      {activeTab === 'edit' && selectedAgent && (
        <div style={{
          background: 'white',
          padding: '30px',
          borderRadius: '15px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>✏️ تعديل الوكيل: {selectedAgent.agentName}</h2>
          
          <form onSubmit={handleSaveEdit}>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
              gap: '20px',
              marginBottom: '30px'
            }}>
              {/* المعلومات الأساسية */}
              <div style={{
                background: '#f8f9fa',
                padding: '20px',
                borderRadius: '10px',
                border: '1px solid #e9ecef'
              }}>
                <h3 style={{ margin: '0 0 15px 0', color: '#495057' }}>المعلومات الأساسية</h3>
                
                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    اسم الوكيل *
                  </label>
                  <input
                    type="text"
                    value={newAgent.agentName}
                    onChange={(e) => setNewAgent({...newAgent, agentName: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    كود الوكيل *
                  </label>
                  <input
                    type="text"
                    value={newAgent.agentCode}
                    onChange={(e) => setNewAgent({...newAgent, agentCode: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    رقم الهاتف *
                  </label>
                  <input
                    type="tel"
                    value={newAgent.phone}
                    onChange={(e) => setNewAgent({...newAgent, phone: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    value={newAgent.email}
                    onChange={(e) => setNewAgent({...newAgent, email: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    رقم الهوية الوطنية *
                  </label>
                  <input
                    type="text"
                    value={newAgent.nationalId}
                    onChange={(e) => setNewAgent({...newAgent, nationalId: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    العنوان
                  </label>
                  <textarea
                    value={newAgent.address}
                    onChange={(e) => setNewAgent({...newAgent, address: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px',
                      minHeight: '80px',
                      resize: 'vertical'
                    }}
                  />
                </div>
              </div>

              {/* معلومات العمل */}
              <div style={{
                background: '#f8f9fa',
                padding: '20px',
                borderRadius: '10px',
                border: '1px solid #e9ecef'
              }}>
                <h3 style={{ margin: '0 0 15px 0', color: '#495057' }}>معلومات العمل</h3>
                
                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    المكتب *
                  </label>
                  <select
                    value={newAgent.office}
                    onChange={(e) => setNewAgent({...newAgent, office: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  >
                    <option value="">اختر المكتب</option>
                    <option value="مكتب الرياض الرئيسي">مكتب الرياض الرئيسي</option>
                    <option value="مكتب جدة">مكتب جدة</option>
                    <option value="مكتب الدمام">مكتب الدمام</option>
                    <option value="مكتب مكة">مكتب مكة</option>
                    <option value="مكتب المدينة">مكتب المدينة</option>
                  </select>
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    التخصص *
                  </label>
                  <select
                    value={newAgent.specialty}
                    onChange={(e) => setNewAgent({...newAgent, specialty: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  >
                    <option value="">اختر التخصص</option>
                    <option value="تأشيرات الإمارات">تأشيرات الإمارات</option>
                    <option value="تأشيرات تركيا">تأشيرات تركيا</option>
                    <option value="تأشيرات مصر">تأشيرات مصر</option>
                    <option value="تأشيرات الهند">تأشيرات الهند</option>
                    <option value="تأشيرات أمريكا">تأشيرات أمريكا</option>
                    <option value="تأشيرات أوروبا">تأشيرات أوروبا</option>
                    <option value="تأشيرات آسيا">تأشيرات آسيا</option>
                  </select>
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    نوع العقد *
                  </label>
                  <select
                    value={newAgent.contractType}
                    onChange={(e) => setNewAgent({...newAgent, contractType: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  >
                    <option value="">اختر نوع العقد</option>
                    <option value="دوام كامل">دوام كامل</option>
                    <option value="دوام جزئي">دوام جزئي</option>
                    <option value="عقد مؤقت">عقد مؤقت</option>
                    <option value="مستقل">مستقل</option>
                  </select>
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    معدل العمولة (%) *
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    value={newAgent.commissionRate}
                    onChange={(e) => setNewAgent({...newAgent, commissionRate: parseFloat(e.target.value)})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    الهدف الشهري (ريال) *
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={newAgent.targetMonthly}
                    onChange={(e) => setNewAgent({...newAgent, targetMonthly: parseInt(e.target.value)})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    الحالة *
                  </label>
                  <select
                    value={newAgent.status}
                    onChange={(e) => setNewAgent({...newAgent, status: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                    required
                  >
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                  </select>
                </div>
              </div>

              {/* معلومات إضافية */}
              <div style={{
                background: '#f8f9fa',
                padding: '20px',
                borderRadius: '10px',
                border: '1px solid #e9ecef'
              }}>
                <h3 style={{ margin: '0 0 15px 0', color: '#495057' }}>معلومات إضافية</h3>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    جهة الاتصال الطارئ
                  </label>
                  <input
                    type="text"
                    value={newAgent.emergencyContact}
                    onChange={(e) => setNewAgent({...newAgent, emergencyContact: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    رقم هاتف الطوارئ
                  </label>
                  <input
                    type="tel"
                    value={newAgent.emergencyPhone}
                    onChange={(e) => setNewAgent({...newAgent, emergencyPhone: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    ملاحظات
                  </label>
                  <textarea
                    value={newAgent.notes}
                    onChange={(e) => setNewAgent({...newAgent, notes: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '1px solid #ddd',
                      borderRadius: '5px',
                      fontSize: '14px',
                      minHeight: '100px',
                      resize: 'vertical'
                    }}
                    placeholder="أضف أي ملاحظات حول الوكيل..."
                  />
                </div>
              </div>
            </div>

            {/* أزرار الحفظ والإلغاء */}
            <div style={{ 
              display: 'flex', 
              gap: '15px', 
              justifyContent: 'flex-end',
              paddingTop: '20px',
              borderTop: '1px solid #e9ecef'
            }}>
              <button
                type="button"
                onClick={() => {
                  setActiveTab('list');
                  setSelectedAgent(null);
                }}
                style={{
                  padding: '12px 25px',
                  border: '1px solid #6c757d',
                  background: 'white',
                  color: '#6c757d',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                ❌ إلغاء
              </button>
              <button
                type="submit"
                style={{
                  padding: '12px 25px',
                  border: 'none',
                  background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                  color: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                💾 حفظ التعديلات
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default AgentsPage;