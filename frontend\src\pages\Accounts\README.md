# نظام الحسابات المحترف - دليل شامل

## نظرة عامة
نظام الحسابات المحترف هو نظام محاسبي متكامل مصمم خصيصاً لشركات السفر والسياحة، يوفر إدارة شاملة للحسابات والمعاملات المالية وفقاً للمعايير المحاسبية الدولية والسعودية.

## المميزات الرئيسية

### 📊 دليل الحسابات (Chart of Accounts)
- **هيكل شجري منظم**: تصنيف الحسابات حسب الطبيعة (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- **ترقيم تلقائي**: نظام ترقيم منطقي للحسابات
- **حسابات رئيسية وفرعية**: إمكانية إنشاء مستويات متعددة من الحسابات
- **بحث وفلترة متقدمة**: البحث السريع في الحسابات بالاسم أو الرقم
- **إدارة كاملة**: إضافة، تعديل، حذف الحسابات مع التحقق من القيود

### 📝 قيود اليومية (Journal Entries)
- **إدخال قيود محاسبية**: واجهة سهلة لإدخال القيود المحاسبية
- **التحقق من التوازن**: التأكد من توازن القيود تلقائياً
- **حالات متعددة**: مسودة، مرحل، معتمد
- **مرجعية كاملة**: ربط القيود بالمستندات والمراجع
- **تعديل وحذف**: إمكانية تعديل القيود قبل الترحيل

### ⚖️ ميزان المراجعة (Trial Balance)
- **عرض شامل**: جميع الحسابات مع أرصدتها
- **فترات مالية**: تحديد الفترة المالية للتقرير
- **تفاصيل الحركات**: عرض الحركات المدينة والدائنة
- **التحقق من التوازن**: التأكد من توازن الميزان
- **تصدير البيانات**: تصدير إلى Excel وPDF

### 📋 القوائم المالية (Financial Statements)
- **قائمة الدخل**: عرض الإيرادات والمصروفات وصافي الدخل
- **الميزانية العمومية**: الأصول والخصوم وحقوق الملكية
- **قائمة التدفقات النقدية**: التدفقات التشغيلية والاستثمارية والتمويلية
- **تقارير مقارنة**: مقارنة الفترات المالية المختلفة

### 💰 الذمم المدينة (Accounts Receivable)
- **متابعة العملاء**: قائمة شاملة بالعملاء والمبالغ المستحقة
- **تواريخ الاستحقاق**: تتبع تواريخ استحقاق الفواتير
- **المتأخرات**: تحديد الفواتير المتأخرة وعدد الأيام
- **تذكيرات تلقائية**: إرسال تذكيرات للعملاء
- **تسجيل المدفوعات**: تسجيل المدفوعات الجزئية والكاملة

### 💳 الذمم الدائنة (Accounts Payable)
- **متابعة الموردين**: إدارة المبالغ المستحقة للموردين
- **جدولة المدفوعات**: تخطيط المدفوعات المستقبلية
- **موافقات الدفع**: نظام موافقات متدرج للمدفوعات
- **تقارير الاستحقاق**: تقارير المبالغ المستحقة بالتواريخ

### 🏦 تسوية البنوك (Bank Reconciliation)
- **مطابقة الحسابات**: مطابقة كشوف البنك مع السجلات
- **العمليات المعلقة**: تتبع العمليات غير المطابقة
- **تسوية تلقائية**: مطابقة تلقائية للعمليات المتشابهة
- **تقارير التسوية**: تقارير مفصلة لعمليات التسوية

### 📄 التقارير الضريبية (Tax Reports)
- **ضريبة القيمة المضافة**: حساب وتقارير ضريبة القيمة المضافة
- **الإقرارات الضريبية**: إعداد الإقرارات الضريبية
- **سجلات ضريبية**: الاحتفاظ بالسجلات الضريبية المطلوبة
- **تصدير للهيئات**: تصدير التقارير بالتنسيقات المطلوبة

## الهيكل التقني

### الملفات الرئيسية

#### 1. AccountsPage.js
```javascript
// الصفحة الرئيسية لنظام الحسابات
- إدارة التبويبات والتنقل
- تحميل البيانات وإدارة الحالة
- التكامل مع جميع المكونات الفرعية
```

#### 2. AccountsComponents.js
```javascript
// المكونات المتخصصة للحسابات
- ChartOfAccounts: دليل الحسابات
- JournalEntries: قيود اليومية
- TrialBalance: ميزان المراجعة
- FinancialStatements: القوائم المالية
- AccountsReceivable: الذمم المدينة
- AccountsPayable: الذمم الدائنة
- BankReconciliation: تسوية البنوك
- TaxReports: التقارير الضريبية
```

#### 3. AccountsPage.css & AccountsComponents.css
```css
/* تصميم متجاوب وأنيق */
- ألوان متدرجة وتأثيرات بصرية
- تصميم متجاوب لجميع الأجهزة
- طباعة محسنة للتقارير
- دعم الوضع المظلم
```

## البيانات والهيكل

### هيكل الحسابات
```javascript
{
  id: '1110',              // رقم الحساب
  name: 'النقدية والبنوك', // اسم الحساب
  type: 'detail',          // نوع الحساب (header/detail)
  category: 'assets',      // فئة الحساب
  balance: 500000,         // الرصيد الحالي
  parent: '1100',          // الحساب الأب
  description: '...'       // وصف الحساب
}
```

### هيكل المعاملات
```javascript
{
  id: 'JE-001',
  date: '2024-01-15',
  description: 'وصف المعاملة',
  reference: 'مرجع المعاملة',
  entries: [
    {
      accountId: '1112',
      accountName: 'البنك الأهلي',
      debit: 500000,
      credit: 0
    }
  ],
  status: 'posted',        // draft, posted
  createdBy: 'اسم المستخدم'
}
```

## الاستخدام والتشغيل

### الوصول للنظام
```
URL: http://localhost:3000/accounts
```

### التنقل بين الأقسام
- **دليل الحسابات**: إدارة هيكل الحسابات
- **قيود اليومية**: إدخال المعاملات المحاسبية
- **ميزان المراجعة**: مراجعة أرصدة الحسابات
- **القوائم المالية**: عرض التقارير المالية
- **الذمم المدينة**: متابعة مستحقات العملاء
- **الذمم الدائنة**: متابعة مستحقات الموردين
- **تسوية البنوك**: مطابقة كشوف البنك
- **التقارير الضريبية**: إعداد التقارير الضريبية

### العمليات الأساسية

#### إضافة حساب جديد
1. انتقل إلى "دليل الحسابات"
2. اضغط "إضافة حساب"
3. أدخل بيانات الحساب
4. حدد الفئة والحساب الأب
5. احفظ الحساب

#### إدخال قيد محاسبي
1. انتقل إلى "قيود اليومية"
2. اضغط "إضافة قيد"
3. أدخل تاريخ ووصف المعاملة
4. أضف القيود التفصيلية
5. تأكد من التوازن
6. احفظ أو رحل القيد

#### عرض ميزان المراجعة
1. انتقل إلى "ميزان المراجعة"
2. حدد الفترة المالية
3. اختر فئة الحسابات (اختياري)
4. عرض النتائج
5. تصدير التقرير

## المعايير المحاسبية

### المعايير المطبقة
- **المعايير الدولية للتقارير المالية (IFRS)**
- **المعايير المحاسبية السعودية**
- **متطلبات هيئة الزكاة والضريبة والجمارك**

### مبادئ المحاسبة
- **مبدأ القيد المزدوج**: كل معاملة تؤثر على حسابين على الأقل
- **مبدأ الاستحقاق**: تسجيل المعاملات عند حدوثها
- **مبدأ الثبات**: استخدام نفس الطرق المحاسبية
- **مبدأ الحيطة والحذر**: عدم المبالغة في الأصول والإيرادات

## الأمان والحماية

### حماية البيانات
- **تشفير البيانات**: تشفير جميع البيانات المالية الحساسة
- **نسخ احتياطية**: نسخ احتياطية منتظمة للبيانات
- **سجل المراجعة**: تسجيل جميع العمليات والتغييرات
- **صلاحيات المستخدمين**: تحديد صلاحيات الوصول والتعديل

### التحكم في الوصول
- **مستويات الصلاحيات**: مدير، محاسب، مستخدم عادي
- **موافقات متدرجة**: نظام موافقات للعمليات المالية الكبيرة
- **تسجيل العمليات**: تسجيل جميع عمليات الدخول والتعديل

## التقارير والتحليلات

### التقارير المالية
- **قائمة الدخل**: شهرية، ربع سنوية، سنوية
- **الميزانية العمومية**: في تواريخ محددة
- **قائمة التدفقات النقدية**: تحليل التدفقات
- **تقارير مقارنة**: مقارنة الفترات والسنوات

### التقارير التحليلية
- **تحليل الربحية**: تحليل ربحية الخدمات والعملاء
- **تحليل التكاليف**: توزيع التكاليف على المراكز
- **تحليل الاتجاهات**: اتجاهات الإيرادات والمصروفات
- **مؤشرات الأداء**: نسب مالية ومؤشرات الأداء

## التكامل مع الأنظمة الأخرى

### التكامل الداخلي
- **نظام المبيعات**: ربط الفواتير بالحسابات
- **نظام المشتريات**: ربط فواتير الموردين
- **نظام المخزون**: تقييم المخزون وتكلفة البضاعة
- **نظام الرواتب**: تسجيل مصروفات الرواتب

### التكامل الخارجي
- **البنوك**: استيراد كشوف الحساب
- **هيئة الزكاة والضريبة**: تصدير التقارير الضريبية
- **أنظمة ERP**: التكامل مع أنظمة تخطيط الموارد
- **برامج المحاسبة**: استيراد وتصدير البيانات

## الصيانة والتطوير

### النسخ الاحتياطية
```javascript
// جدولة النسخ الاحتياطية
- نسخة يومية للمعاملات
- نسخة أسبوعية شاملة
- نسخة شهرية للأرشيف
- اختبار استعادة البيانات
```

### التحديثات والتطوير
- **تحديثات دورية**: تحديث المعايير المحاسبية
- **مميزات جديدة**: إضافة مميزات حسب الاحتياجات
- **تحسين الأداء**: تحسين سرعة التقارير والاستعلامات
- **دعم فني**: دعم فني مستمر للمستخدمين

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### عدم توازن القيود
```
المشكلة: القيد غير متوازن
الحل: تأكد من أن مجموع المدين = مجموع الدائن
```

#### خطأ في ترحيل القيود
```
المشكلة: فشل في ترحيل القيد
الحل: تحقق من صحة أرقام الحسابات والمبالغ
```

#### بطء في التقارير
```
المشكلة: بطء في تحميل التقارير
الحل: تحديد فترة زمنية أقصر أو تحسين الفهارس
```

### رسائل الخطأ الشائعة
- `ACCOUNT_NOT_FOUND`: الحساب غير موجود
- `UNBALANCED_ENTRY`: القيد غير متوازن
- `INVALID_DATE`: تاريخ غير صحيح
- `INSUFFICIENT_PERMISSIONS`: صلاحيات غير كافية

## أفضل الممارسات

### إدخال البيانات
1. **تحقق من البيانات**: راجع جميع البيانات قبل الحفظ
2. **استخدم المراجع**: أضف مراجع واضحة لكل معاملة
3. **وصف مفصل**: اكتب وصفاً واضحاً لكل قيد
4. **مراجعة دورية**: راجع القيود بانتظام

### إدارة الحسابات
1. **هيكل منطقي**: استخدم هيكلاً منطقياً للحسابات
2. **ترقيم متسق**: حافظ على نظام ترقيم متسق
3. **تجنب الحذف**: تجنب حذف الحسابات المستخدمة
4. **نسخ احتياطية**: احتفظ بنسخ احتياطية منتظمة

### التقارير المالية
1. **مراجعة شهرية**: أعد التقارير المالية شهرياً
2. **مقارنة الفترات**: قارن الأداء مع الفترات السابقة
3. **تحليل الانحرافات**: حلل أي انحرافات كبيرة
4. **توثيق القرارات**: وثق القرارات المحاسبية المهمة

## الدعم والمساعدة

### الحصول على المساعدة
- **دليل المستخدم**: راجع هذا الدليل أولاً
- **الدعم الفني**: اتصل بفريق الدعم الفني
- **التدريب**: احضر دورات التدريب المتاحة
- **المجتمع**: شارك في منتديات المستخدمين

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-1234567
- **الموقع الإلكتروني**: www.sharaubtravelsoft.com
- **ساعات العمل**: الأحد - الخميس، 8:00 ص - 5:00 م

---

**ملاحظة**: هذا النظام مطور خصيصاً لشركات السفر والسياحة ويتوافق مع المتطلبات المحاسبية والضريبية في المملكة العربية السعودية.