/* 🎨 النظام التصميمي المتطور - شراء للسفريات */

/* ===== متغيرات الألوان المتطورة ===== */
:root {
  /* 🎨 نظام الألوان الأساسي - مستوحى من أحدث اتجاهات 2024 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* 🌟 ألوان ثانوية متدرجة */
  --secondary-50: #f0fdf4;
  --secondary-100: #dcfce7;
  --secondary-200: #bbf7d0;
  --secondary-300: #86efac;
  --secondary-400: #4ade80;
  --secondary-500: #22c55e;
  --secondary-600: #16a34a;
  --secondary-700: #15803d;
  --secondary-800: #166534;
  --secondary-900: #14532d;

  /* 🔥 ألوان التحذير والخطر */
  --danger-50: #fef2f2;
  --danger-100: #fee2e2;
  --danger-200: #fecaca;
  --danger-300: #fca5a5;
  --danger-400: #f87171;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  --danger-700: #b91c1c;
  --danger-800: #991b1b;
  --danger-900: #7f1d1d;

  /* ⚠️ ألوان التحذير */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* 💜 ألوان المعلومات */
  --info-50: #faf5ff;
  --info-100: #f3e8ff;
  --info-200: #e9d5ff;
  --info-300: #d8b4fe;
  --info-400: #c084fc;
  --info-500: #a855f7;
  --info-600: #9333ea;
  --info-700: #7c3aed;
  --info-800: #6b21a8;
  --info-900: #581c87;

  /* 🌫️ ألوان محايدة متطورة */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;

  /* 🌈 خلفيات متدرجة خرافية */
  --bg-primary: #ffffff;
  --bg-secondary: var(--neutral-50);
  --bg-tertiary: var(--neutral-100);
  --bg-glass: rgba(255, 255, 255, 0.25);
  --bg-glass-dark: rgba(255, 255, 255, 0.1);
  --bg-blur: rgba(255, 255, 255, 0.8);

  /* 🎭 خلفيات متدرجة مذهلة */
  --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-700) 100%);
  --gradient-sunset: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-forest: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
  --gradient-royal: linear-gradient(135deg, #667db6 0%, #0082c8 25%, #0082c8 75%, #667db6 100%);
  --gradient-aurora: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-cosmic: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);

  /* ✨ تأثيرات Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);

  /* 🎯 تأثيرات Neumorphism */
  --neuro-light: #ffffff;
  --neuro-dark: #d1d9e6;
  --neuro-shadow-light: 20px 20px 60px #bebebe;
  --neuro-shadow-dark: -20px -20px 60px #ffffff;
  --neuro-inset-light: inset 20px 20px 60px #bebebe;
  --neuro-inset-dark: inset -20px -20px 60px #ffffff;

  /* 🌊 ظلال متطورة */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.5);
  --shadow-colored: 0 10px 25px -5px rgba(59, 130, 246, 0.4);

  /* 🎬 انتقالات وحركات متطورة */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* 📐 نصف أقطار متطورة */
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* 📏 مساحات متدرجة */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
}

/* 🌙 الوضع المظلم المتطور */
[data-theme="dark"],
.dark-mode {
  /* 🎨 ألوان أساسية محسنة للوضع المظلم */
  --primary-50: #1e293b;
  --primary-100: #334155;
  --primary-200: #475569;
  --primary-300: #64748b;
  --primary-400: #94a3b8;
  --primary-500: #cbd5e1;
  --primary-600: #e2e8f0;
  --primary-700: #f1f5f9;
  --primary-800: #f8fafc;
  --primary-900: #ffffff;

  /* 🌫️ خلفيات مظلمة أنيقة */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-glass: rgba(15, 23, 42, 0.25);
  --bg-glass-dark: rgba(15, 23, 42, 0.4);
  --bg-blur: rgba(15, 23, 42, 0.8);

  /* 🌌 تدرجات مظلمة خرافية */
  --gradient-primary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-secondary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --gradient-sunset: linear-gradient(135deg, #7c3aed 0%, #3b82f6 50%, #06b6d4 100%);
  --gradient-ocean: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  --gradient-forest: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
  --gradient-royal: linear-gradient(135deg, #312e81 0%, #3730a3 25%, #4338ca 75%, #6366f1 100%);
  --gradient-aurora: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  --gradient-cosmic: linear-gradient(135deg, #581c87 0%, #7c3aed 100%);

  /* ✨ تأثيرات Glassmorphism للوضع المظلم */
  --glass-bg: rgba(15, 23, 42, 0.4);
  --glass-border: rgba(148, 163, 184, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);

  /* 🎯 تأثيرات Neumorphism للوضع المظلم */
  --neuro-light: #1e293b;
  --neuro-dark: #0f172a;
  --neuro-shadow-light: 20px 20px 60px #0a0f1a;
  --neuro-shadow-dark: -20px -20px 60px #2a3441;
  --neuro-inset-light: inset 20px 20px 60px #0a0f1a;
  --neuro-inset-dark: inset -20px -20px 60px #2a3441;

  /* 🌊 ظلال محسنة للوضع المظلم */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.4);
  --shadow-colored: 0 10px 25px -5px rgba(99, 102, 241, 0.3);

  /* 📝 ألوان النصوص المحسنة */
  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-300);
  --text-muted: var(--neutral-500);
  --text-inverse: var(--neutral-900);

  /* 🔲 ألوان الحدود */
  --border-color: var(--neutral-700);
  --border-light: var(--neutral-800);
  --border-dark: var(--neutral-600);
}

/* ===== الأنماط الأساسية المتطورة ===== */

/* 🔄 إعادة تعيين شاملة */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 📱 تحسينات HTML الأساسية */
html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
  transition: all var(--transition-normal);
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* 🎨 تصميم الجسم المتطور */
body {
  font-family: 'Cairo', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--neutral-800);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  transition: all var(--transition-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 🌙 تحسينات الوضع المظلم للجسم */
[data-theme="dark"] body,
.dark-mode body {
  background: var(--gradient-cosmic);
  color: var(--neutral-100);
}

/* 📏 تحسين العناصر الأساسية */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

input,
button,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* 🎯 إزالة الخطوط التحتية الافتراضية */
button {
  border: none;
  background: none;
  cursor: pointer;
}

/* 📝 تحسين النصوص */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 🔗 تحسين الروابط */
a {
  color: inherit;
  text-decoration: none;
  transition: all var(--transition-fast);
}

/* 📋 تحسين القوائم */
ul,
ol {
  list-style: none;
}

/* 🎬 تحسين الحركات للمستخدمين الذين يفضلون تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== الحاويات والبطاقات المتطورة ===== */

/* 📦 حاوية أساسية محسنة */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  transition: all var(--transition-normal);
}

/* 🎴 بطاقة Glassmorphism أساسية */
.card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(255, 255, 255, 0.3);
}

.card:hover::before {
  opacity: 1;
}

/* 🎯 بطاقة Neumorphism */
.card-neuro {
  background: var(--neuro-light);
  border-radius: var(--radius-2xl);
  box-shadow: var(--neuro-shadow-light), var(--neuro-shadow-dark);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  border: none;
}

.card-neuro:hover {
  box-shadow: var(--neuro-inset-light), var(--neuro-inset-dark);
}

/* 🌟 بطاقة متدرجة */
.card-gradient {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-colored);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card-gradient:hover::before {
  opacity: 1;
}

/* 💎 بطاقة فاخرة */
.card-luxury {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: var(--space-8);
  transition: all var(--transition-normal);
  position: relative;
}

.card-luxury:hover {
  transform: translateY(-2px);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 🌙 أنماط الوضع المظلم للبطاقات */
[data-theme="dark"] .card,
.dark-mode .card {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .card-neuro,
.dark-mode .card-neuro {
  background: var(--neuro-light);
  box-shadow: var(--neuro-shadow-light), var(--neuro-shadow-dark);
}

[data-theme="dark"] .card-luxury,
.dark-mode .card-luxury {
  background: linear-gradient(145deg, var(--neutral-800) 0%, var(--neutral-900) 100%);
  border-color: var(--neutral-700);
}

/* ===== الأزرار المتطورة ===== */

/* 🔘 زر أساسي محسن */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 🎨 زر أساسي */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover::before {
  opacity: 1;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* 🌟 زر ثانوي */
.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 🔥 زر خطر */
.btn-danger {
  background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.4);
}

/* ⚠️ زر تحذير */
.btn-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(245, 158, 11, 0.4);
}

/* 💎 زر Glassmorphism */
.btn-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  color: var(--neutral-800);
  box-shadow: var(--glass-shadow);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* 🎯 زر Neumorphism */
.btn-neuro {
  background: var(--neuro-light);
  color: var(--neutral-700);
  box-shadow: var(--neuro-shadow-light), var(--neuro-shadow-dark);
  border: none;
}

.btn-neuro:hover {
  box-shadow: var(--neuro-inset-light), var(--neuro-inset-dark);
}

/* 👻 زر شبح */
.btn-ghost {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-200);
}

.btn-ghost:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 📏 أحجام الأزرار */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: 1rem;
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: 1.125rem;
  border-radius: var(--radius-2xl);
}

/* 🌙 أنماط الوضع المظلم للأزرار */
[data-theme="dark"] .btn-glass,
.dark-mode .btn-glass {
  background: var(--glass-bg);
  color: var(--neutral-200);
  border-color: var(--glass-border);
}

[data-theme="dark"] .btn-neuro,
.dark-mode .btn-neuro {
  background: var(--neuro-light);
  color: var(--neutral-300);
}

[data-theme="dark"] .btn-ghost,
.dark-mode .btn-ghost {
  color: var(--primary-400);
  border-color: var(--primary-800);
}

[data-theme="dark"] .btn-ghost:hover,
.dark-mode .btn-ghost:hover {
  background: var(--primary-950);
  border-color: var(--primary-700);
}

/* ===== حقول الإدخال والنماذج المتطورة ===== */

/* 📝 حقل إدخال أساسي محسن */
.form-control {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--neutral-800);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  appearance: none;
}

.form-control::placeholder {
  color: var(--neutral-400);
  opacity: 1;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.1),
    var(--shadow-md);
  background: rgba(255, 255, 255, 0.9);
}

.form-control:hover {
  border-color: var(--primary-300);
}

/* ✨ حقل إدخال Glassmorphism */
.form-control-glass {
  background: var(--glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-4) var(--space-5);
  color: var(--neutral-800);
  transition: all var(--transition-normal);
}

.form-control-glass:focus {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: var(--shadow-glow);
}

/* 🎯 حقل إدخال Neumorphism */
.form-control-neuro {
  background: var(--neuro-light);
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--space-4) var(--space-5);
  box-shadow: var(--neuro-inset-light), var(--neuro-inset-dark);
  color: var(--neutral-700);
  transition: all var(--transition-normal);
}

.form-control-neuro:focus {
  box-shadow:
    var(--neuro-shadow-light),
    var(--neuro-shadow-dark),
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 🏷️ تسميات النماذج */
.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
  transition: color var(--transition-fast);
}

/* 📦 مجموعة النماذج */
.form-group {
  margin-bottom: var(--space-6);
}

/* ✅ حالات التحقق */
.form-control.is-valid {
  border-color: var(--secondary-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-control.is-invalid {
  border-color: var(--danger-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 💬 رسائل التحقق */
.form-feedback {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  font-weight: 500;
}

.form-feedback.valid {
  color: var(--secondary-600);
}

.form-feedback.invalid {
  color: var(--danger-600);
}

/* 🔘 أزرار الراديو والتحديد */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.form-check-input {
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  background: white;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.form-check-input:checked {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.form-check-input[type="radio"] {
  border-radius: var(--radius-full);
}

.form-check-label {
  font-size: 0.875rem;
  color: var(--neutral-700);
  cursor: pointer;
  user-select: none;
}

/* 📋 قوائم الاختيار */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-left: 2.5rem;
}

/* 🌙 أنماط الوضع المظلم للنماذج */
[data-theme="dark"] .form-control,
.dark-mode .form-control {
  color: var(--neutral-200);
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .form-control::placeholder,
.dark-mode .form-control::placeholder {
  color: var(--neutral-500);
}

[data-theme="dark"] .form-control:focus,
.dark-mode .form-control:focus {
  background: rgba(15, 23, 42, 0.6);
  border-color: var(--primary-400);
}

[data-theme="dark"] .form-label,
.dark-mode .form-label {
  color: var(--neutral-300);
}

[data-theme="dark"] .form-check-label,
.dark-mode .form-check-label {
  color: var(--neutral-300);
}

/* ===== النصوص والعناوين المتطورة ===== */

/* 📝 العناوين الأساسية */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', 'Segoe UI', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  color: var(--neutral-900);
  margin-bottom: var(--space-4);
  transition: all var(--transition-normal);
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: var(--space-6);
}

h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--neutral-800);
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-700);
}

h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-700);
}

h5 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-600);
}

h6 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-600);
}

/* 📄 النصوص العادية */
p {
  font-size: 1rem;
  line-height: 1.7;
  color: var(--neutral-600);
  margin-bottom: var(--space-4);
  transition: color var(--transition-normal);
}

/* 🎨 أنماط النصوص المتنوعة */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* 🎯 أوزان الخطوط */
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* 🌈 ألوان النصوص */
.text-primary { color: var(--primary-600); }
.text-secondary { color: var(--secondary-600); }
.text-danger { color: var(--danger-600); }
.text-warning { color: var(--warning-600); }
.text-info { color: var(--info-600); }
.text-success { color: var(--secondary-600); }
.text-muted { color: var(--neutral-500); }
.text-light { color: var(--neutral-400); }
.text-dark { color: var(--neutral-800); }

/* ✨ نصوص متدرجة */
.text-gradient {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.text-gradient-sunset {
  background: var(--gradient-sunset);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

/* 🔗 الروابط المحسنة */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--primary-700);
}

.link-underline {
  position: relative;
}

.link-underline::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: width var(--transition-normal);
}

.link-underline:hover::after {
  width: 100%;
}

/* 📏 محاذاة النصوص */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 🌙 أنماط الوضع المظلم للنصوص */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6 {
  color: var(--neutral-100);
}

[data-theme="dark"] p,
.dark-mode p {
  color: var(--neutral-300);
}

[data-theme="dark"] .text-muted,
.dark-mode .text-muted {
  color: var(--neutral-500);
}

[data-theme="dark"] a,
.dark-mode a {
  color: var(--primary-400);
}

[data-theme="dark"] a:hover,
.dark-mode a:hover {
  color: var(--primary-300);
}

/* ===== الجداول المتطورة ===== */

/* 📊 جدول أساسي محسن */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
}

.table th,
.table td {
  padding: var(--space-4) var(--space-5);
  text-align: right;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  transition: all var(--transition-fast);
  vertical-align: middle;
}

.table th {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  font-weight: 600;
  color: var(--neutral-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  opacity: 0.3;
}

.table td {
  color: var(--neutral-600);
  font-size: 0.875rem;
  font-weight: 500;
}

.table tbody tr {
  transition: all var(--transition-fast);
}

.table tbody tr:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== القوائم المنسدلة المتطورة ===== */

/* 📋 قائمة منسدلة Glassmorphism */
.dropdown {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  padding: var(--space-2);
  min-width: 200px;
  transition: all var(--transition-normal);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  color: var(--neutral-700);
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  text-decoration: none;
}

.dropdown-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-700);
  transform: translateX(-2px);
}

.dropdown-item.active {
  background: var(--gradient-primary);
  color: white;
}

.dropdown-divider {
  height: 1px;
  background: var(--glass-border);
  margin: var(--space-2) 0;
}

/* 🌙 أنماط الوضع المظلم للجداول والقوائم */
[data-theme="dark"] .table,
.dark-mode .table {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .table th,
.dark-mode .table th {
  color: var(--neutral-300);
  background: rgba(15, 23, 42, 0.3);
}

[data-theme="dark"] .table td,
.dark-mode .table td {
  color: var(--neutral-400);
}

[data-theme="dark"] .table tbody tr:hover,
.dark-mode .table tbody tr:hover {
  background: rgba(99, 102, 241, 0.1);
}

[data-theme="dark"] .dropdown,
.dark-mode .dropdown {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .dropdown-item,
.dark-mode .dropdown-item {
  color: var(--neutral-300);
}

[data-theme="dark"] .dropdown-item:hover,
.dark-mode .dropdown-item:hover {
  background: rgba(99, 102, 241, 0.2);
  color: var(--primary-400);
}

/* ===== الشريط الجانبي المتطور ===== */

/* 📱 شريط جانبي Glassmorphism */
.sidebar {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-left: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  opacity: 0.6;
}

/* 🎯 عناصر الشريط الجانبي */
.sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  margin: var(--space-1) var(--space-2);
  color: var(--neutral-600);
  font-weight: 500;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
  background: var(--gradient-primary);
  transition: width var(--transition-normal);
  z-index: -1;
}

.sidebar-item:hover {
  color: var(--primary-700);
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(-4px);
}

.sidebar-item:hover::before {
  width: 4px;
}

.sidebar-item.active {
  color: var(--primary-700);
  background: rgba(59, 130, 246, 0.15);
  font-weight: 600;
}

.sidebar-item.active::before {
  width: 4px;
}

/* ===== الهيدر المتطور ===== */

/* 🎨 هيدر Glassmorphism */
.header {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--neutral-800);
  text-decoration: none;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.header-nav-item {
  color: var(--neutral-600);
  font-weight: 500;
  text-decoration: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
}

.header-nav-item::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.header-nav-item:hover {
  color: var(--primary-700);
  background: rgba(59, 130, 246, 0.1);
}

.header-nav-item:hover::after {
  width: 100%;
}

.header-nav-item.active {
  color: var(--primary-700);
  font-weight: 600;
}

.header-nav-item.active::after {
  width: 100%;
}

/* 🌙 أنماط الوضع المظلم للشريط الجانبي والهيدر */
[data-theme="dark"] .sidebar,
.dark-mode .sidebar {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .sidebar-item,
.dark-mode .sidebar-item {
  color: var(--neutral-400);
}

[data-theme="dark"] .sidebar-item:hover,
.dark-mode .sidebar-item:hover {
  color: var(--primary-400);
  background: rgba(99, 102, 241, 0.2);
}

[data-theme="dark"] .sidebar-item.active,
.dark-mode .sidebar-item.active {
  color: var(--primary-400);
  background: rgba(99, 102, 241, 0.3);
}

[data-theme="dark"] .header,
.dark-mode .header {
  background: var(--glass-bg);
  border-color: var(--glass-border);
}

[data-theme="dark"] .header-logo,
.dark-mode .header-logo {
  color: var(--neutral-200);
}

[data-theme="dark"] .header-nav-item,
.dark-mode .header-nav-item {
  color: var(--neutral-400);
}

[data-theme="dark"] .header-nav-item:hover,
.dark-mode .header-nav-item:hover {
  color: var(--primary-400);
  background: rgba(99, 102, 241, 0.2);
}

/* الفوتر */
.footer {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border-top: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

/* الإشعارات */
.notification {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.notification.success {
  border-left: 4px solid var(--secondary-color);
}

.notification.error {
  border-left: 4px solid var(--accent-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

.notification.info {
  border-left: 4px solid var(--info-color);
}

/* المودال */
.modal {
  background-color: var(--bg-overlay);
  backdrop-filter: blur(10px);
}

.modal-content {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-xl);
}

/* شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* تحميل الثيم */
.theme-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.theme-loading .loading-spinner {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.theme-loading .spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.theme-loading p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

/* إشعار تغيير الثيم */
.theme-notification {
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 600;
  direction: rtl;
}

.theme-notification .notification-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.theme-notification .notification-icon {
  font-size: 1.2rem;
}

/* تحسينات إضافية */
.fade-in {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* ===== الأدوات المساعدة المتطورة ===== */

/* 📐 العرض والارتفاع */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-fit { width: fit-content; }
.w-min { width: min-content; }
.w-max { width: max-content; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-fit { height: fit-content; }
.h-min { height: min-content; }
.h-max { height: max-content; }

/* 🎯 Flexbox المتقدم */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* 📏 المساحات */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.space-x-1 > * + * { margin-right: var(--space-1); }
.space-x-2 > * + * { margin-right: var(--space-2); }
.space-x-3 > * + * { margin-right: var(--space-3); }
.space-x-4 > * + * { margin-right: var(--space-4); }
.space-x-6 > * + * { margin-right: var(--space-6); }

.space-y-1 > * + * { margin-top: var(--space-1); }
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }
.space-y-4 > * + * { margin-top: var(--space-4); }
.space-y-6 > * + * { margin-top: var(--space-6); }

/* 📍 المواضع */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* 🎨 الخلفيات والحدود */
.bg-transparent { background-color: transparent; }
.bg-white { background-color: white; }
.bg-black { background-color: black; }

.border-0 { border-width: 0; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 🌊 الظلال */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* 👁️ الرؤية والعرض */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 🌊 الفيض */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* 🖱️ المؤشر */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* 👆 التفاعل */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* 🎬 الانتقالات */
.transition-none { transition: none; }
.transition-all { transition: all var(--transition-normal); }
.transition { transition: all var(--transition-fast); }
.transition-colors { transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast); }
.transition-transform { transition: transform var(--transition-fast); }

.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }

/* 🔄 التحويلات */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.rotate-0 { transform: rotate(0deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }

/* تحسينات للطباعة */
@media print {
  :root {
    --bg-primary: #ffffff !important;
    --bg-secondary: #ffffff !important;
    --bg-card: #ffffff !important;
    --text-primary: #000000 !important;
    --text-secondary: #333333 !important;
    --border-color: #cccccc !important;
  }

  .dark-mode {
    --bg-primary: #ffffff !important;
    --bg-secondary: #ffffff !important;
    --bg-card: #ffffff !important;
    --text-primary: #000000 !important;
    --text-secondary: #333333 !important;
    --border-color: #cccccc !important;
  }
}
