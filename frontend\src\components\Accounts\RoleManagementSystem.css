.role-management-system {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
}

.role-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.role-management-header h2 {
  margin: 0;
  color: #495057;
  font-size: 1.8rem;
  font-weight: 600;
}

.create-role-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.create-role-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.role-management-content {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  height: calc(100vh - 140px);
}

.roles-sidebar {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.roles-sidebar h3 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  margin: 0;
  padding: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.roles-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 10px;
}

.role-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
}

.role-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
  transform: translateY(-2px);
}

.role-item.active {
  border-color: #007bff;
  background: #e3f2fd;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.role-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 10px;
}

.role-icon {
  font-size: 1.5rem;
  width: 30px;
  text-align: center;
}

.role-info {
  flex: 1;
}

.role-info h4 {
  margin: 0 0 5px 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}

.role-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
}

.system-badge {
  background: #17a2b8;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.role-stats {
  color: #6c757d;
  font-size: 0.8rem;
  margin-bottom: 10px;
}

.role-actions {
  display: flex;
  gap: 5px;
  position: absolute;
  top: 10px;
  left: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.role-item:hover .role-actions {
  opacity: 1;
}

.edit-btn, .delete-btn {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background: #e3f2fd;
}

.delete-btn:hover {
  background: #ffebee;
}

.role-details {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.role-details-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 20px;
}

.role-details-header h3 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.role-details-header p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.no-role-selected {
  padding: 60px 20px;
  text-align: center;
  color: #6c757d;
}

.no-role-selected h3 {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
  color: #495057;
}

.no-role-selected p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.modal-header button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.modal-header button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #495057;
  font-weight: 600;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.modal-footer {
  background: #f8f9fa;
  padding: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  border-top: 1px solid #dee2e6;
}

.modal-footer button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.modal-footer button:first-child {
  background: #6c757d;
  color: white;
}

.modal-footer button:first-child:hover {
  background: #5a6268;
}

.modal-footer button.primary {
  background: #007bff;
  color: white;
}

.modal-footer button.primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .role-management-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .roles-sidebar {
    max-height: 300px;
  }
  
  .role-management-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .modal {
    width: 95%;
    margin: 10px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Scrollbar Styling */
.roles-list::-webkit-scrollbar,
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.roles-list::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.roles-list::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.roles-list::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
