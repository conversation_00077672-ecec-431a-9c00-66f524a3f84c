import React, { useState, useEffect } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';
import { useNotifications } from '../../components/UI/EnhancedNotifications';
import { useShortcuts } from '../../components/UI/KeyboardShortcuts';
import './BookingPages.css';

const PassportBookingPage = () => {
  // 🔔 استخدام نظام الإشعارات المحسن
  const { success, error, warning, info, loading: showLoading } = useNotifications();

  // ⌨️ استخدام اختصارات لوحة المفاتيح
  const { registerShortcut, unregisterShortcut } = useShortcuts();

  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterService, setFilterService] = useState('all');
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);

  const [newBooking, setNewBooking] = useState({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    nationalId: '',
    serviceType: 'new-passport', // new-passport, renewal, replacement, urgent
    passportType: 'regular', // regular, diplomatic, special
    urgentService: false,
    deliveryMethod: 'pickup', // pickup, delivery
    deliveryAddress: '',
    amount: '',
    paidAmount: 0,
    remainingAmount: 0,
    currency: 'SAR',
    status: 'pending', // pending, in-progress, ready, delivered, cancelled
    paymentStatus: 'unpaid',
    bookingReference: '',
    expectedDelivery: '',
    notes: '',
    // الحقول الجديدة
    representativeName: '',
    registrationDate: new Date().toISOString().split('T')[0],
    attachments: [],
    transactionLocation: '',
    transactionType: 'in-person' // in-person, remote
  });

  useEffect(() => {
    setTimeout(() => {
      setBookings([
        {
          id: 1,
          customerName: 'سعد محمد الأحمد',
          customerPhone: '+966501234567',
          customerEmail: '<EMAIL>',
          nationalId: '1234567890',
          serviceType: 'new-passport',
          passportType: 'regular',
          urgentService: false,
          deliveryMethod: 'pickup',
          amount: 300,
          paidAmount: 300,
          remainingAmount: 0,
          currency: 'SAR',
          status: 'ready',
          paymentStatus: 'paid',
          bookingReference: '*********',
          expectedDelivery: '2024-02-20',
          bookingDate: '2024-01-15',
          notes: 'جواز سفر جديد',
          representativeName: 'أحمد المندوب',
          registrationDate: '2024-01-15',
          attachments: ['صورة الهوية', 'صورة شخصية', 'شهادة الميلاد'],
          transactionLocation: 'مكتب الجوازات - الرياض',
          transactionType: 'in-person'
        },
        {
          id: 2,
          customerName: 'نورا علي السالم',
          customerPhone: '+966507654321',
          customerEmail: '<EMAIL>',
          nationalId: '0987654321',
          serviceType: 'renewal',
          passportType: 'regular',
          urgentService: true,
          deliveryMethod: 'delivery',
          deliveryAddress: 'الرياض، حي النخيل',
          amount: 120,
          paidAmount: 60,
          remainingAmount: 60,
          currency: 'USD',
          status: 'in-progress',
          paymentStatus: 'partial',
          bookingReference: '*********',
          expectedDelivery: '2024-02-10',
          bookingDate: '2024-01-14',
          notes: 'تجديد عاجل',
          representativeName: 'فاطمة المندوبة',
          registrationDate: '2024-01-14',
          attachments: ['صورة الجواز القديم', 'صورة شخصية حديثة'],
          transactionLocation: 'منصة أبشر الإلكترونية',
          transactionType: 'remote'
        },
        {
          id: 3,
          customerName: 'محمد أحمد الزبيدي',
          customerPhone: '+967771234567',
          customerEmail: '<EMAIL>',
          nationalId: '1122334455',
          serviceType: 'replacement',
          passportType: 'regular',
          urgentService: false,
          deliveryMethod: 'pickup',
          amount: 75000,
          paidAmount: 0,
          remainingAmount: 75000,
          currency: 'YER',
          status: 'pending',
          paymentStatus: 'unpaid',
          bookingReference: '*********',
          expectedDelivery: '2024-02-25',
          bookingDate: '2024-01-12',
          notes: 'بدل فاقد - جواز سفر يمني',
          representativeName: 'علي المندوب',
          registrationDate: '2024-01-12',
          attachments: ['صورة الهوية', 'صورة شخصية', 'بلاغ فقدان'],
          transactionLocation: 'القنصلية اليمنية - الرياض',
          transactionType: 'in-person'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddBooking = (e) => {
    e.preventDefault();
    const booking = {
      id: bookings.length + 1,
      ...newBooking,
      amount: parseFloat(newBooking.amount),
      paidAmount: parseFloat(newBooking.paidAmount),
      remainingAmount: parseFloat(newBooking.amount) - parseFloat(newBooking.paidAmount),
      bookingDate: new Date().toISOString().split('T')[0],
      bookingReference: `PP${new Date().getFullYear()}${String(bookings.length + 1).padStart(3, '0')}`
    };
    setBookings([booking, ...bookings]);
    setNewBooking({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      nationalId: '',
      serviceType: 'new-passport',
      passportType: 'regular',
      urgentService: false,
      deliveryMethod: 'pickup',
      deliveryAddress: '',
      amount: '',
      paidAmount: 0,
      remainingAmount: 0,
      currency: 'SAR',
      status: 'pending',
      paymentStatus: 'unpaid',
      bookingReference: '',
      expectedDelivery: '',
      notes: '',
      // إعادة تعيين الحقول الجديدة
      representativeName: '',
      registrationDate: new Date().toISOString().split('T')[0],
      attachments: [],
      transactionLocation: '',
      transactionType: 'in-person'
    });
    setShowAddForm(false);
  };

  const handleStatusChange = (id, newStatus) => {
    setBookings(bookings.map(booking => 
      booking.id === id ? { ...booking, status: newStatus } : booking
    ));
  };

  const handleDeleteBooking = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
      setBookings(bookings.filter(booking => booking.id !== id));
    }
  };

  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    handleDeleteBooking(booking.id);
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, 'الجوازات');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, 'الجوازات');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, 'الجوازات');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, 'الجوازات');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedIds.length} حجز؟`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };

  const getServiceText = (serviceType) => {
    switch (serviceType) {
      case 'new-passport': return 'جواز جديد';
      case 'renewal': return 'تجديد';
      case 'replacement': return 'بدل فاقد';
      case 'urgent': return 'عاجل';
      default: return serviceType;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'معلق';
      case 'in-progress': return 'قيد التنفيذ';
      case 'ready': return 'جاهز';
      case 'delivered': return 'مسلم';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ready': return '#27ae60';
      case 'delivered': return '#8e44ad';
      case 'in-progress': return '#f39c12';
      case 'pending': return '#3498db';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.bookingReference.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || booking.status === filterStatus;
    const matchesService = filterService === 'all' || booking.serviceType === filterService;
    return matchesSearch && matchesStatus && matchesService;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #e74c3c',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل حجوزات الجوازات...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h2 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>📘 حجوزات الجوازات</h2>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة وتتبع طلبات الجوازات</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ طلب جواز جديد
        </button>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في طلبات الجوازات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="pending">معلق</option>
          <option value="in-progress">قيد التنفيذ</option>
          <option value="ready">جاهز</option>
          <option value="delivered">مسلم</option>
          <option value="cancelled">ملغي</option>
        </select>
        <select
          value={filterService}
          onChange={(e) => setFilterService(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الخدمات</option>
          <option value="new-passport">جواز جديد</option>
          <option value="renewal">تجديد</option>
          <option value="replacement">بدل فاقد</option>
          <option value="urgent">عاجل</option>
        </select>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي الطلبات', value: bookings.length, color: '#e74c3c', icon: '📘' },
          { title: 'جاهز للتسليم', value: bookings.filter(b => b.status === 'ready').length, color: '#27ae60', icon: '✅' },
          { title: 'قيد التنفيذ', value: bookings.filter(b => b.status === 'in-progress').length, color: '#f39c12', icon: '⏳' },
          { 
            title: 'إجمالي المبيعات', 
            value: (() => {
              const totals = bookings.reduce((acc, b) => {
                const currency = b.currency || 'SAR';
                acc[currency] = (acc[currency] || 0) + (b.amount || 0);
                return acc;
              }, {});
              
              return Object.entries(totals)
                .map(([currency, amount]) => formatCurrency(amount, currency))
                .join(' | ');
            })(),
            color: '#8e44ad', 
            icon: '💰' 
          }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {stat.value}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

      {/* Bookings Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>العميل</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>نوع الخدمة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>المندوب والمعاملة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>التسليم المتوقع</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>المبلغ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookings.map((booking) => (
                <tr key={booking.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedBookings.includes(booking.id)}
                      onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{booking.customerName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{booking.customerPhone}</div>
                      <div style={{ fontSize: '11px', color: '#95a5a6' }}>{booking.bookingReference}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      background: '#e74c3c20',
                      color: '#e74c3c'
                    }}>
                      {getServiceText(booking.serviceType)}
                    </span>
                    {booking.urgentService && (
                      <div style={{ fontSize: '10px', color: '#e74c3c', marginTop: '2px' }}>
                        ⚡ عاجل
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>
                      👤 {booking.representativeName || 'غير محدد'}
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d', marginBottom: '2px' }}>
                      📍 {booking.transactionLocation || 'غير محدد'}
                    </div>
                    <span style={{
                      padding: '2px 6px',
                      borderRadius: '8px',
                      fontSize: '10px',
                      fontWeight: 'bold',
                      background: booking.transactionType === 'in-person' ? '#27ae6020' : '#3498db20',
                      color: booking.transactionType === 'in-person' ? '#27ae60' : '#3498db'
                    }}>
                      {booking.transactionType === 'in-person' ? '🏢 حضوري' : '💻 عن بعد'}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div>📅 {booking.expectedDelivery}</div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      {booking.deliveryMethod === 'pickup' ? '🏢 استلام' : '🚚 توصيل'}
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold', color: '#27ae60' }}>
                      {formatCurrency(booking.amount, booking.currency)}
                    </div>
                    {booking.remainingAmount > 0 && (
                      <div style={{ fontSize: '11px', color: '#e74c3c' }}>
                        متبقي: {formatCurrency(booking.remainingAmount, booking.currency)}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      background: `${getStatusColor(booking.status)}20`,
                      color: getStatusColor(booking.status)
                    }}>
                      {getStatusText(booking.status)}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <BookingActions
                      booking={booking}
                      onView={handleViewBooking}
                      onEdit={handleEditBooking}
                      onDelete={handleDeleteSingleBooking}
                      onPrint={handlePrintBooking}
                      onSavePDF={handleSavePDFBooking}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Booking Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>📘 طلب جواز سفر جديد</h2>
            
            <form onSubmit={handleAddBooking}>
              {/* معلومات العميل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>👤 معلومات العميل</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({...newBooking, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({...newBooking, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهوية</label>
                    <input
                      type="text"
                      value={newBooking.nationalId}
                      onChange={(e) => setNewBooking({...newBooking, nationalId: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={newBooking.customerEmail}
                      onChange={(e) => setNewBooking({...newBooking, customerEmail: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* تفاصيل الخدمة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>📘 تفاصيل الخدمة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الخدمة</label>
                    <select
                      value={newBooking.serviceType}
                      onChange={(e) => setNewBooking({...newBooking, serviceType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="new-passport">جواز جديد</option>
                      <option value="renewal">تجديد</option>
                      <option value="replacement">بدل فاقد</option>
                      <option value="urgent">عاجل</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الجواز</label>
                    <select
                      value={newBooking.passportType}
                      onChange={(e) => setNewBooking({...newBooking, passportType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="regular">عادي</option>
                      <option value="diplomatic">دبلوماسي</option>
                      <option value="special">خاص</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>طريقة التسليم</label>
                    <select
                      value={newBooking.deliveryMethod}
                      onChange={(e) => setNewBooking({...newBooking, deliveryMethod: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="pickup">استلام من المكتب</option>
                      <option value="delivery">توصيل للمنزل</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التسليم المتوقع</label>
                    <input
                      type="date"
                      value={newBooking.expectedDelivery}
                      onChange={(e) => setNewBooking({...newBooking, expectedDelivery: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* معلومات المندوب والمعاملة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>👨‍💼 معلومات المندوب والمعاملة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم المندوب</label>
                    <input
                      type="text"
                      value={newBooking.representativeName}
                      onChange={(e) => setNewBooking({...newBooking, representativeName: e.target.value})}
                      placeholder="أدخل اسم المندوب"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ التسجيل</label>
                    <input
                      type="date"
                      value={newBooking.registrationDate}
                      onChange={(e) => setNewBooking({...newBooking, registrationDate: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مكان المعاملة</label>
                    <input
                      type="text"
                      value={newBooking.transactionLocation}
                      onChange={(e) => setNewBooking({...newBooking, transactionLocation: e.target.value})}
                      placeholder="مثال: مكتب الجوازات - الرياض"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع المعاملة</label>
                    <select
                      value={newBooking.transactionType}
                      onChange={(e) => setNewBooking({...newBooking, transactionType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="in-person">حضوري</option>
                      <option value="remote">عن بعد</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* المرفقات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>📎 المرفقات</h3>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إضافة مرفقات</label>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      const fileNames = files.map(file => file.name);
                      setNewBooking({...newBooking, attachments: [...newBooking.attachments, ...fileNames]});
                    }}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                  {newBooking.attachments.length > 0 && (
                    <div style={{ marginTop: '10px' }}>
                      <p style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>المرفقات المضافة:</p>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
                        {newBooking.attachments.map((attachment, index) => (
                          <span key={index} style={{
                            padding: '4px 8px',
                            background: '#e74c3c20',
                            color: '#e74c3c',
                            borderRadius: '12px',
                            fontSize: '11px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px'
                          }}>
                            📎 {attachment}
                            <button
                              type="button"
                              onClick={() => {
                                const newAttachments = newBooking.attachments.filter((_, i) => i !== index);
                                setNewBooking({...newBooking, attachments: newAttachments});
                              }}
                              style={{
                                background: 'none',
                                border: 'none',
                                color: '#e74c3c',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              ✕
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* عنوان التوصيل */}
              {newBooking.deliveryMethod === 'delivery' && (
                <div style={{ marginBottom: '25px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>عنوان التوصيل</label>
                  <textarea
                    value={newBooking.deliveryAddress}
                    onChange={(e) => setNewBooking({...newBooking, deliveryAddress: e.target.value})}
                    rows="3"
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      resize: 'vertical'
                    }}
                  />
                </div>
              )}

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إجمالي المبلغ</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.amount}
                      onChange={(e) => setNewBooking({...newBooking, amount: e.target.value})}
                      required
                      placeholder="0.00"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ المدفوع</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.paidAmount}
                      onChange={(e) => setNewBooking({...newBooking, paidAmount: e.target.value})}
                      placeholder="0.00"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع العملة</label>
                    <select
                      value={newBooking.currency}
                      onChange={(e) => setNewBooking({...newBooking, currency: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                      <option value="YER">🇾🇪 ريال يمني (YER)</option>
                      <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة الطلب
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />
    </div>
  );
};

export default PassportBookingPage;