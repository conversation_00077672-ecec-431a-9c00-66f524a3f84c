<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Frontend</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .test-frame {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 15px;
            background: white;
            margin: 20px 0;
        }
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid white;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: rgba(255,255,255,0.2);
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار Frontend - نظام محاسبي لوكالات السفريات</h1>
        
        <div class="status" id="status">
            <h3>📊 حالة الاختبار</h3>
            <p id="statusText">جاري التحميل...</p>
        </div>

        <div>
            <button class="btn" onclick="testDirect()">🔗 اختبار مباشر</button>
            <button class="btn" onclick="testInFrame()">🖼️ اختبار في إطار</button>
            <button class="btn" onclick="openNewWindow()">🪟 فتح نافذة جديدة</button>
            <button class="btn" onclick="checkErrors()">🔍 فحص الأخطاء</button>
        </div>

        <div id="testArea"></div>

        <div class="status">
            <h3>🛠️ معلومات التشخيص</h3>
            <p><strong>Frontend URL:</strong> http://localhost:3000</p>
            <p><strong>حالة الخادم:</strong> <span id="serverStatus">جاري الفحص...</span></p>
            <p><strong>آخر اختبار:</strong> <span id="lastTest">لم يتم بعد</span></p>
        </div>

        <div class="status">
            <h3>📝 سجل الأخطاء</h3>
            <div id="errorLog" style="text-align: right; font-family: monospace; font-size: 12px;">
                لا توجد أخطاء حتى الآن...
            </div>
        </div>
    </div>

    <script>
        let errorLog = [];

        // التقاط الأخطاء
        window.addEventListener('error', function(e) {
            const error = `❌ ${new Date().toLocaleTimeString()}: ${e.message} في ${e.filename}:${e.lineno}`;
            errorLog.push(error);
            updateErrorLog();
        });

        function updateErrorLog() {
            document.getElementById('errorLog').innerHTML = errorLog.length > 0 
                ? errorLog.slice(-10).join('<br>') 
                : 'لا توجد أخطاء حتى الآن...';
        }

        // اختبار حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:3000', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                document.getElementById('serverStatus').textContent = '✅ يعمل';
                document.getElementById('serverStatus').style.color = '#4CAF50';
                return true;
            } catch (error) {
                document.getElementById('serverStatus').textContent = '❌ لا يعمل - ' + error.message;
                document.getElementById('serverStatus').style.color = '#f44336';
                return false;
            }
        }

        // اختبار مباشر
        async function testDirect() {
            document.getElementById('statusText').textContent = 'جاري الاختبار المباشر...';
            document.getElementById('lastTest').textContent = new Date().toLocaleTimeString();
            
            try {
                const response = await fetch('http://localhost:3000');
                const html = await response.text();
                
                if (html.includes('root')) {
                    document.getElementById('status').className = 'status success';
                    document.getElementById('statusText').innerHTML = '✅ Frontend يعمل بشكل طبيعي<br>تم العثور على عنصر root في HTML';
                } else {
                    document.getElementById('status').className = 'status error';
                    document.getElementById('statusText').innerHTML = '⚠️ Frontend يستجيب لكن قد يكون هناك مشكلة في التحميل';
                }
            } catch (error) {
                document.getElementById('status').className = 'status error';
                document.getElementById('statusText').innerHTML = '❌ لا يمكن الوصول للـ Frontend<br>' + error.message;
            }
        }

        // اختبار في إطار
        function testInFrame() {
            document.getElementById('statusText').textContent = 'جاري الاختبار في إطار...';
            document.getElementById('lastTest').textContent = new Date().toLocaleTimeString();
            
            const testArea = document.getElementById('testArea');
            testArea.innerHTML = `
                <h3>🖼️ اختبار Frontend في إطار</h3>
                <iframe src="http://localhost:3000" class="test-frame" onload="frameLoaded()" onerror="frameError()"></iframe>
            `;
        }

        function frameLoaded() {
            document.getElementById('status').className = 'status success';
            document.getElementById('statusText').innerHTML = '✅ تم تحميل Frontend في الإطار بنجاح';
        }

        function frameError() {
            document.getElementById('status').className = 'status error';
            document.getElementById('statusText').innerHTML = '❌ فشل في تحميل Frontend في الإطار';
        }

        // فتح نافذة جديدة
        function openNewWindow() {
            document.getElementById('lastTest').textContent = new Date().toLocaleTimeString();
            const newWindow = window.open('http://localhost:3000', '_blank', 'width=1200,height=800');
            
            if (newWindow) {
                document.getElementById('statusText').textContent = '✅ تم فتح Frontend في نافذة جديدة';
                document.getElementById('status').className = 'status success';
            } else {
                document.getElementById('statusText').textContent = '❌ فشل في فتح نافذة جديدة - قد يكون محجوب بواسطة المتصفح';
                document.getElementById('status').className = 'status error';
            }
        }

        // فحص الأخطاء
        function checkErrors() {
            document.getElementById('lastTest').textContent = new Date().toLocaleTimeString();
            
            if (errorLog.length === 0) {
                document.getElementById('statusText').textContent = '✅ لا توجد أخطاء JavaScript';
                document.getElementById('status').className = 'status success';
            } else {
                document.getElementById('statusText').textContent = `⚠️ تم العثور على ${errorLog.length} خطأ`;
                document.getElementById('status').className = 'status error';
            }
            
            updateErrorLog();
        }

        // اختبار تلقائي عند التحميل
        window.addEventListener('load', function() {
            checkServerStatus();
            setTimeout(testDirect, 1000);
        });

        // تحديث دوري لحالة الخادم
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>