import React, { useState } from 'react';
import { EnhancedNotificationsProvider, useNotifications } from '../components/UI/EnhancedNotifications';
import '../components/UI/EnhancedNotifications.css';

const NotificationTestContent = () => {
  const { success, error, warning, info, loading, clearAllNotifications } = useNotifications();
  const [counter, setCounter] = useState(1);

  const testSuccess = () => {
    success(`تم تحميل البيانات بنجاح ${counter}`, {
      title: 'نجح العملية',
      description: 'تم تحديث قاعدة البيانات'
    });
    setCounter(prev => prev + 1);
  };

  const testError = () => {
    error(`خطأ في الاتصال ${counter}`, {
      title: 'خطأ في النظام',
      description: 'يرجى المحاولة مرة أخرى'
    });
    setCounter(prev => prev + 1);
  };

  const testWarning = () => {
    warning(`تحذير: مساحة منخفضة ${counter}`, {
      title: 'تحذير',
      description: 'يرجى تنظيف الملفات'
    });
    setCounter(prev => prev + 1);
  };

  const testInfo = () => {
    info(`معلومة: تحديث متوفر ${counter}`, {
      title: 'معلومات',
      description: 'إصدار جديد متوفر'
    });
    setCounter(prev => prev + 1);
  };

  const testLoading = () => {
    loading(`جاري التحميل ${counter}...`, {
      title: 'جاري التحميل'
    });
    
    setTimeout(() => {
      success(`تم التحميل بنجاح ${counter}!`);
    }, 3000);
    
    setCounter(prev => prev + 1);
  };

  const testMultiple = () => {
    success('إشعار نجاح 1');
    setTimeout(() => error('إشعار خطأ 1'), 500);
    setTimeout(() => warning('إشعار تحذير 1'), 1000);
    setTimeout(() => info('إشعار معلومات 1'), 1500);
    setTimeout(() => success('إشعار نجاح 2'), 2000);
  };

  const testFlood = () => {
    for (let i = 1; i <= 10; i++) {
      setTimeout(() => {
        success(`إشعار متتالي ${i}`);
      }, i * 200);
    }
  };

  return (
    <div style={{ 
      padding: '2rem', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ 
        textAlign: 'center', 
        marginBottom: '2rem', 
        color: '#1f2937',
        fontSize: '2rem'
      }}>
        🔔 اختبار نظام الإشعارات المحسن
      </h1>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <button 
          onClick={testSuccess}
          style={{
            padding: '1rem',
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500',
            transition: 'all 0.2s'
          }}
        >
          ✅ إشعار نجاح
        </button>

        <button 
          onClick={testError}
          style={{
            padding: '1rem',
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          ❌ إشعار خطأ
        </button>

        <button 
          onClick={testWarning}
          style={{
            padding: '1rem',
            backgroundColor: '#f59e0b',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          ⚠️ إشعار تحذير
        </button>

        <button 
          onClick={testInfo}
          style={{
            padding: '1rem',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          ℹ️ إشعار معلومات
        </button>

        <button 
          onClick={testLoading}
          style={{
            padding: '1rem',
            backgroundColor: '#8b5cf6',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          ⏳ إشعار تحميل
        </button>

        <button 
          onClick={testMultiple}
          style={{
            padding: '1rem',
            backgroundColor: '#06b6d4',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          🎯 إشعارات متعددة
        </button>

        <button 
          onClick={testFlood}
          style={{
            padding: '1rem',
            backgroundColor: '#f97316',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          🌊 اختبار الفيضان
        </button>

        <button 
          onClick={clearAllNotifications}
          style={{
            padding: '1rem',
            backgroundColor: '#6b7280',
            color: 'white',
            border: 'none',
            borderRadius: '0.5rem',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '500'
          }}
        >
          🗑️ مسح الكل
        </button>
      </div>

      <div style={{
        backgroundColor: '#f9fafb',
        padding: '1.5rem',
        borderRadius: '0.5rem',
        border: '1px solid #e5e7eb'
      }}>
        <h3 style={{ marginTop: 0, color: '#374151' }}>📋 ميزات النظام المحسن:</h3>
        <ul style={{ color: '#6b7280', lineHeight: '1.6' }}>
          <li>✅ منع تكرار الإشعارات المتشابهة</li>
          <li>🔢 حد أقصى 5 إشعارات معروضة</li>
          <li>⏰ إزالة تلقائية بعد 4 ثوانٍ</li>
          <li>🧹 تنظيف دوري كل 10 ثوانٍ</li>
          <li>🗑️ زر مسح الكل عند وجود أكثر من إشعارين</li>
          <li>📱 تصميم متجاوب للشاشات الصغيرة</li>
          <li>🎭 تأثيرات حركية سلسة</li>
          <li>🎯 تحسينات الأداء</li>
        </ul>
      </div>
    </div>
  );
};

const SimpleNotificationTest = () => {
  return (
    <EnhancedNotificationsProvider>
      <NotificationTestContent />
    </EnhancedNotificationsProvider>
  );
};

export default SimpleNotificationTest;
