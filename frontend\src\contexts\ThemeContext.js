import React, { createContext, useContext, useState, useEffect } from 'react';

// إنشاء Context للثيم
const ThemeContext = createContext();

// Hook لاستخدام الثيم
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// مزود الثيم العالمي
export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل الثيم المحفوظ عند بدء التطبيق
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme) {
      setIsDarkMode(savedTheme === 'dark');
    } else {
      setIsDarkMode(systemPrefersDark);
    }
    
    setIsLoading(false);
  }, []);

  // تطبيق الثيم على الـ body عند التغيير
  useEffect(() => {
    if (!isLoading) {
      const body = document.body;
      const html = document.documentElement;
      
      if (isDarkMode) {
        body.classList.add('dark-mode');
        html.classList.add('dark-mode');
        body.setAttribute('data-theme', 'dark');
        html.setAttribute('data-theme', 'dark');
      } else {
        body.classList.remove('dark-mode');
        html.classList.remove('dark-mode');
        body.setAttribute('data-theme', 'light');
        html.setAttribute('data-theme', 'light');
      }
      
      // حفظ الثيم في localStorage
      localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
      
      // إرسال حدث مخصص لإعلام المكونات الأخرى
      window.dispatchEvent(new CustomEvent('themeChange', {
        detail: { isDarkMode }
      }));
    }
  }, [isDarkMode, isLoading]);

  // تبديل الثيم
  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
    
    // إضافة تأثير انتقال سلس
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);
    
    // إظهار إشعار
    showThemeNotification(!isDarkMode);
  };

  // إظهار إشعار تغيير الثيم
  const showThemeNotification = (newDarkMode) => {
    const notification = document.createElement('div');
    notification.className = `theme-notification ${newDarkMode ? 'dark' : 'light'}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${newDarkMode ? '🌙' : '☀️'}</span>
        <span class="notification-text">
          تم تفعيل ${newDarkMode ? 'الوضع المظلم' : 'الوضع العادي'}
        </span>
      </div>
    `;
    
    // إضافة الأنماط
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${newDarkMode ? '#2c3e50' : '#ffffff'};
      color: ${newDarkMode ? '#ecf0f1' : '#2c3e50'};
      padding: 15px 20px;
      border-radius: 10px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
      z-index: 10000;
      transform: translateX(400px);
      opacity: 0;
      transition: all 0.3s ease;
      border: 2px solid ${newDarkMode ? '#34495e' : '#ecf0f1'};
      direction: rtl;
      font-family: 'Cairo', Arial, sans-serif;
      font-weight: 600;
    `;
    
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
      notification.style.opacity = '1';
    }, 100);
    
    // إخفاء الإشعار
    setTimeout(() => {
      notification.style.transform = 'translateX(400px)';
      notification.style.opacity = '0';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  };

  // تعيين الثيم مباشرة
  const setTheme = (theme) => {
    setIsDarkMode(theme === 'dark');
  };

  // الحصول على الثيم الحالي
  const getCurrentTheme = () => {
    return isDarkMode ? 'dark' : 'light';
  };

  // مراقبة تفضيلات النظام
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e) => {
      // تطبيق تفضيلات النظام فقط إذا لم يكن هناك ثيم محفوظ
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        setIsDarkMode(e.matches);
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  const value = {
    isDarkMode,
    toggleTheme,
    setTheme,
    getCurrentTheme,
    isLoading
  };

  if (isLoading) {
    return (
      <div className="theme-loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>جاري تحميل الثيم...</p>
        </div>
      </div>
    );
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
