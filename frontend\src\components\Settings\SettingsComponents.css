/* Settings Components Styles */

/* Connection Test */
.connection-test {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.test-btn {
  padding: 8px 16px;
  border: 1px solid #667eea;
  background: #667eea;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.test-btn:hover {
  background: #5a67d8;
}

.test-btn:disabled {
  background: #e1e8ed;
  color: #6c757d;
  cursor: not-allowed;
}

.test-btn.testing {
  background: #ffc107;
  color: #212529;
}

.test-result {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
}

.test-result.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.test-result.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Backup Manager */
.backup-manager {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.backup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.backup-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.create-backup-btn {
  padding: 10px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.create-backup-btn:hover {
  background: #218838;
}

.create-backup-btn.creating {
  background: #ffc107;
  color: #212529;
  cursor: not-allowed;
}

.backup-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.backup-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.backup-item:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.backup-info {
  flex: 1;
}

.backup-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.backup-type-icon {
  font-size: 16px;
}

.backup-details {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #6c757d;
}

.backup-actions {
  display: flex;
  gap: 10px;
}

.download-btn,
.delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.download-btn {
  background: #007bff;
  color: white;
}

.download-btn:hover {
  background: #0056b3;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

/* User Management */
.user-management {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.user-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.add-user-btn {
  padding: 10px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-user-btn:hover {
  background: #5a67d8;
}

.add-user-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.form-grid input,
.form-grid select {
  padding: 10px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
}

.save-user-btn {
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.save-user-btn:hover {
  background: #218838;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.user-item:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 3px;
}

.user-email {
  color: #667eea;
  font-size: 14px;
  margin-bottom: 3px;
}

.user-role {
  color: #6c757d;
  font-size: 13px;
  margin-bottom: 3px;
}

.user-last-login {
  color: #6c757d;
  font-size: 12px;
}

.user-status {
  margin: 0 15px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.toggle-status-btn,
.delete-user-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.toggle-status-btn {
  background: #ffc107;
  color: #212529;
}

.toggle-status-btn:hover {
  background: #e0a800;
}

.delete-user-btn {
  background: #dc3545;
  color: white;
}

.delete-user-btn:hover {
  background: #c82333;
}

/* Email Settings */
.email-settings {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.email-settings h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.email-config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-item label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.config-item input,
.config-item select {
  padding: 10px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.email-test {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #e1e8ed;
}

.test-email-btn {
  padding: 12px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-email-btn:hover {
  background: #5a67d8;
}

.test-email-btn.testing {
  background: #ffc107;
  color: #212529;
  cursor: not-allowed;
}

/* System Stats */
.system-stats {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.system-stats h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .backup-header,
  .user-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .backup-item,
  .user-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .backup-actions,
  .user-actions {
    justify-content: center;
  }
  
  .email-config-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .backup-manager,
  .user-management,
  .email-settings,
  .system-stats {
    padding: 15px;
  }
  
  .backup-item,
  .user-item {
    padding: 12px;
  }
  
  .stat-card {
    padding: 15px;
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .stat-icon {
    font-size: 28px;
  }
  
  .stat-value {
    font-size: 18px;
  }
}

/* Animation Effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.backup-manager,
.user-management,
.email-settings,
.system-stats {
  animation: fadeInUp 0.5s ease-out;
}

.backup-item,
.user-item,
.stat-card {
  animation: fadeInUp 0.3s ease-out;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
}

.loading-spinner-small {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}