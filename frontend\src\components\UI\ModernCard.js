import React from 'react';
import './ModernCard.css';

const ModernCard = ({ 
  children, 
  variant = 'default', 
  size = 'md',
  hover = true,
  glass = false,
  gradient = false,
  className = '',
  onClick,
  ...props 
}) => {
  const cardClasses = [
    'modern-card',
    `modern-card--${variant}`,
    `modern-card--${size}`,
    hover && 'modern-card--hover',
    glass && 'modern-card--glass',
    gradient && 'modern-card--gradient',
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={cardClasses}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

// Card Header Component
export const CardHeader = ({ children, className = '', ...props }) => (
  <div className={`modern-card__header ${className}`} {...props}>
    {children}
  </div>
);

// Card Body Component
export const CardBody = ({ children, className = '', ...props }) => (
  <div className={`modern-card__body ${className}`} {...props}>
    {children}
  </div>
);

// Card Footer Component
export const CardFooter = ({ children, className = '', ...props }) => (
  <div className={`modern-card__footer ${className}`} {...props}>
    {children}
  </div>
);

// Card Title Component
export const CardTitle = ({ children, level = 3, className = '', ...props }) => {
  const Tag = `h${level}`;
  return (
    <Tag className={`modern-card__title ${className}`} {...props}>
      {children}
    </Tag>
  );
};

// Card Subtitle Component
export const CardSubtitle = ({ children, className = '', ...props }) => (
  <p className={`modern-card__subtitle ${className}`} {...props}>
    {children}
  </p>
);

// Card Actions Component
export const CardActions = ({ children, align = 'right', className = '', ...props }) => (
  <div className={`modern-card__actions modern-card__actions--${align} ${className}`} {...props}>
    {children}
  </div>
);

// Stats Card Component
export const StatsCard = ({ 
  title, 
  value, 
  change, 
  changeType = 'positive',
  icon,
  color = 'primary',
  className = '',
  ...props 
}) => (
  <ModernCard 
    variant="stats" 
    className={`stats-card stats-card--${color} ${className}`}
    {...props}
  >
    <CardBody>
      <div className="stats-card__content">
        <div className="stats-card__info">
          <CardSubtitle>{title}</CardSubtitle>
          <CardTitle level={2} className="stats-card__value">
            {value}
          </CardTitle>
          {change && (
            <div className={`stats-card__change stats-card__change--${changeType}`}>
              <span className="stats-card__change-icon">
                {changeType === 'positive' ? '↗️' : '↘️'}
              </span>
              {change}
            </div>
          )}
        </div>
        {icon && (
          <div className="stats-card__icon">
            {icon}
          </div>
        )}
      </div>
    </CardBody>
  </ModernCard>
);

// Feature Card Component
export const FeatureCard = ({ 
  title, 
  description, 
  icon, 
  action,
  className = '',
  ...props 
}) => (
  <ModernCard 
    variant="feature" 
    className={`feature-card ${className}`}
    hover
    {...props}
  >
    <CardBody>
      {icon && (
        <div className="feature-card__icon">
          {icon}
        </div>
      )}
      <CardTitle level={4}>{title}</CardTitle>
      <CardSubtitle>{description}</CardSubtitle>
      {action && (
        <CardActions>
          {action}
        </CardActions>
      )}
    </CardBody>
  </ModernCard>
);

// Metric Card Component
export const MetricCard = ({ 
  label, 
  value, 
  unit, 
  trend,
  trendValue,
  color = 'primary',
  size = 'md',
  className = '',
  ...props 
}) => (
  <ModernCard 
    variant="metric" 
    size={size}
    className={`metric-card metric-card--${color} ${className}`}
    {...props}
  >
    <CardBody>
      <div className="metric-card__label">{label}</div>
      <div className="metric-card__value">
        {value}
        {unit && <span className="metric-card__unit">{unit}</span>}
      </div>
      {trend && (
        <div className={`metric-card__trend metric-card__trend--${trend}`}>
          <span className="metric-card__trend-icon">
            {trend === 'up' ? '📈' : trend === 'down' ? '📉' : '➡️'}
          </span>
          {trendValue}
        </div>
      )}
    </CardBody>
  </ModernCard>
);

export default ModernCard;