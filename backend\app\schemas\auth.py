"""
مخططات المصادقة
Authentication Schemas
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, validator

class UserLogin(BaseModel):
    """مخطط تسجيل الدخول"""
    username: str
    password: str

class UserRegister(BaseModel):
    """مخطط تسجيل مستخدم جديد"""
    username: str
    email: EmailStr
    full_name: str
    password: str
    phone: Optional[str] = None
    
    @validator('username')
    def username_alphanumeric(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط')
        if len(v) < 3:
            raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        return v
    
    @validator('password')
    def password_strength(cls, v):
        if len(v) < 8:
            raise ValueError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
        return v

class Token(BaseModel):
    """مخطط الرمز المميز"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Optional[dict] = None

class TokenData(BaseModel):
    """بيانات الرمز المميز"""
    user_id: Optional[int] = None
    username: Optional[str] = None