/* أنماط ميزان المراجعة المتقدم */
.trial-balance-advanced {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* رأس الصفحة */
.trial-balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  gap: 10px;
}

.header-controls .btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
  transform: translateY(-2px);
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* الإحصائيات */
.trial-balance-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stat-card.balanced {
  border-left: 4px solid #27ae60;
}

.stat-card.error {
  border-left: 4px solid #e74c3c;
}

.stat-icon {
  font-size: 28px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

/* أدوات التحكم */
.trial-balance-controls {
  margin-bottom: 20px;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-range label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.date-input {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
}

.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.view-mode-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-mode-btn:hover:not(.active) {
  background: #e9ecef;
}

/* عرض البيانات */
.trial-balance-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

.trial-balance-table {
  width: 100%;
  border-collapse: collapse;
}

.trial-balance-table th {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.trial-balance-table th:hover {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.trial-balance-table td {
  padding: 12px 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.trial-balance-table tr:hover {
  background: #f8f9fa;
}

.trial-balance-table tfoot tr {
  background: #f8f9fa;
  font-weight: bold;
}

.trial-balance-table tfoot td {
  border-top: 2px solid #2c3e50;
  border-bottom: 2px solid #2c3e50;
}

.account-code {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.account-name {
  text-align: right;
  font-weight: 500;
  color: #2c3e50;
}

.amount {
  font-weight: bold;
  text-align: left;
  font-family: monospace;
}

.amount.debit {
  color: #27ae60;
}

.amount.credit {
  color: #e74c3c;
}

.actions {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: scale(1.1);
}

.view-btn:hover {
  background: #3498db;
  color: white;
}

/* العرض المجمع */
.category-section {
  margin-bottom: 30px;
}

.category-title {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  padding: 15px 20px;
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px 8px 0 0;
}

.category-section .trial-balance-table {
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

/* النماذج المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.print-modal,
.export-modal,
.filter-modal,
.details-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.print-modal,
.export-modal,
.filter-modal {
  max-width: 500px;
}

.details-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.modal-content {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

/* نموذج خيارات الطباعة */
.print-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #2c3e50;
  cursor: pointer;
}

.option-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.form-control {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.print-preview {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.print-preview h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.preview-item .label {
  color: #7f8c8d;
}

.preview-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.preview-item .value.balanced {
  color: #27ae60;
}

.preview-item .value.unbalanced {
  color: #e74c3c;
}

/* نموذج خيارات التصدير */
.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.format-selection h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-option:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.format-option input[type="radio"] {
  width: 16px;
  height: 16px;
}

.format-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.format-icon {
  font-size: 24px;
}

.format-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.format-desc {
  font-size: 12px;
  color: #7f8c8d;
}

.export-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.export-summary h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.summary-item .label {
  color: #7f8c8d;
}

.summary-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.summary-item .value.balanced {
  color: #27ae60;
}

.summary-item .value.unbalanced {
  color: #e74c3c;
}

/* نموذج التصفية المتقدمة */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group > label {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #2c3e50;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-inputs span {
  color: #7f8c8d;
  font-size: 14px;
}

/* نموذج تفاصيل الحساب */
.account-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.details-header {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.detail-item span {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.balance-summary,
.movement-analysis {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.balance-summary h4,
.movement-analysis h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.balance-grid,
.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.balance-item,
.analysis-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.balance-item label,
.analysis-item label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.balance-item span,
.analysis-item span {
  font-size: 14px;
  font-weight: bold;
}

.balance-item span.debit,
.analysis-item span.debit {
  color: #27ae60;
}

.balance-item span.credit,
.analysis-item span.credit {
  color: #e74c3c;
}

/* حالة عدم وجود بيانات */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.no-data .icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-data h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.no-data p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .trial-balance-advanced {
    padding: 10px;
  }
  
  .trial-balance-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .trial-balance-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .controls-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-box {
    max-width: none;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-modes {
    flex-direction: column;
  }
  
  .trial-balance-table {
    font-size: 12px;
  }
  
  .trial-balance-table th,
  .trial-balance-table td {
    padding: 8px 4px;
  }
  
  .details-header {
    grid-template-columns: 1fr;
  }
  
  .balance-grid,
  .analysis-grid {
    grid-template-columns: 1fr;
  }
  
  .format-options {
    gap: 8px;
  }
  
  .format-option {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .trial-balance-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .header-controls {
    grid-template-columns: 1fr;
  }
  
  .trial-balance-table th,
  .trial-balance-table td {
    padding: 6px 2px;
    font-size: 10px;
  }
  
  .actions {
    flex-direction: column;
    gap: 2px;
  }
  
  .action-btn {
    padding: 4px 6px;
    font-size: 10px;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    padding: 15px;
  }
}