import { initPerformanceOptimization } from './performanceOptimizer';
import { initNetworkOptimization } from './networkOptimizer';
import { initMemoryOptimization } from './memoryOptimizer';

// 🚀 مدير الأداء الشامل

export class PerformanceManager {
  constructor() {
    this.isInitialized = false;
    this.modules = {};
    this.config = {
      enableMonitoring: true,
      enableCaching: true,
      enableOptimizations: true,
      reportInterval: 60000, // دقيقة واحدة
      thresholds: {
        loadTime: 3000,
        memoryUsage: 50 * 1024 * 1024, // 50MB
        networkLatency: 2000
      }
    };
    
    this.metrics = {
      pageLoadTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      networkRequests: 0,
      errors: 0,
      userInteractions: 0
    };
    
    this.init();
  }

  async init() {
    if (this.isInitialized) return;
    
    try {
      console.log('🚀 بدء تهيئة مدير الأداء...');
      
      // تهيئة الوحدات
      await this.initializeModules();
      
      // بدء المراقبة
      this.startMonitoring();
      
      // إعداد التقارير الدورية
      this.setupReporting();
      
      // إعداد معالجات الأحداث
      this.setupEventHandlers();
      
      this.isInitialized = true;
      console.log('✅ تم تهيئة مدير الأداء بنجاح');
      
    } catch (error) {
      console.error('❌ فشل في تهيئة مدير الأداء:', error);
    }
  }

  async initializeModules() {
    // تهيئة وحدة تحسين الأداء
    this.modules.performance = initPerformanceOptimization();
    
    // تهيئة وحدة تحسين الشبكة
    this.modules.network = initNetworkOptimization('/api');
    
    // تهيئة وحدة تحسين الذاكرة
    this.modules.memory = initMemoryOptimization();
    
    console.log('📦 تم تهيئة جميع وحدات الأداء');
  }

  startMonitoring() {
    if (!this.config.enableMonitoring) return;
    
    // مراقبة أداء التحميل
    this.monitorPageLoad();
    
    // مراقبة استخدام الذاكرة
    this.monitorMemoryUsage();
    
    // مراقبة طلبات الشبكة
    this.monitorNetworkRequests();
    
    // مراقبة تفاعلات المستخدم
    this.monitorUserInteractions();
    
    console.log('👁️ بدأت مراقبة الأداء');
  }

  monitorPageLoad() {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0];
        this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.loadEventStart;
        
        // تحليل أداء التحميل
        this.analyzeLoadPerformance();
      });
    }
  }

  monitorMemoryUsage() {
    if (this.modules.memory?.monitor) {
      this.modules.memory.monitor.subscribe((metrics) => {
        this.metrics.memoryUsage = metrics.usedJSHeapSize;
        
        // تحليل استخدام الذاكرة
        this.analyzeMemoryUsage(metrics);
      });
    }
  }

  monitorNetworkRequests() {
    if (this.modules.network?.apiClient) {
      // مراقبة إحصائيات الشبكة
      setInterval(() => {
        const stats = this.modules.network.apiClient.getStats();
        this.metrics.networkRequests = stats.pendingRequests;
      }, 5000);
    }
  }

  monitorUserInteractions() {
    const events = ['click', 'scroll', 'keypress', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.metrics.userInteractions++;
      }, { passive: true });
    });
  }

  analyzeLoadPerformance() {
    const { pageLoadTime } = this.metrics;
    const { loadTime } = this.config.thresholds;
    
    if (pageLoadTime > loadTime) {
      this.reportIssue('slow-load', {
        actual: pageLoadTime,
        threshold: loadTime,
        severity: 'warning'
      });
    }
  }

  analyzeMemoryUsage(metrics) {
    const { memoryUsage } = this.config.thresholds;
    
    if (metrics.usedJSHeapSize > memoryUsage) {
      this.reportIssue('high-memory', {
        actual: metrics.usedJSHeapSize,
        threshold: memoryUsage,
        severity: 'warning'
      });
    }
  }

  reportIssue(type, details) {
    const issue = {
      type,
      details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    console.warn(`⚠️ مشكلة في الأداء: ${type}`, issue);
    
    // إرسال التقرير للخادم (اختياري)
    if (this.config.enableReporting) {
      this.sendIssueReport(issue);
    }
  }

  async sendIssueReport(issue) {
    try {
      await this.modules.network?.apiClient?.request('/performance/issues', {
        method: 'POST',
        body: JSON.stringify(issue)
      });
    } catch (error) {
      console.error('فشل في إرسال تقرير المشكلة:', error);
    }
  }

  setupReporting() {
    setInterval(() => {
      this.generatePerformanceReport();
    }, this.config.reportInterval);
  }

  generatePerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: { ...this.metrics },
      moduleStats: this.getModuleStats(),
      recommendations: this.generateRecommendations()
    };
    
    console.group('📊 تقرير الأداء الدوري');
    console.table(report.metrics);
    console.log('📈 إحصائيات الوحدات:', report.moduleStats);
    console.log('💡 التوصيات:', report.recommendations);
    console.groupEnd();
    
    return report;
  }

  getModuleStats() {
    const stats = {};
    
    if (this.modules.performance?.monitor) {
      stats.performance = this.modules.performance.monitor.getMetrics();
    }
    
    if (this.modules.network?.apiClient) {
      stats.network = this.modules.network.apiClient.getStats();
    }
    
    if (this.modules.memory?.monitor) {
      stats.memory = this.modules.memory.monitor.getMetrics();
    }
    
    return stats;
  }

  generateRecommendations() {
    const recommendations = [];
    const { metrics, config } = this;
    
    // توصيات الأداء
    if (metrics.pageLoadTime > config.thresholds.loadTime) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        message: 'وقت التحميل بطيء - فكر في تحسين الصور وتقليل حجم الملفات'
      });
    }
    
    // توصيات الذاكرة
    if (metrics.memoryUsage > config.thresholds.memoryUsage) {
      recommendations.push({
        type: 'memory',
        priority: 'medium',
        message: 'استخدام ذاكرة عالي - تحقق من تسريبات الذاكرة'
      });
    }
    
    // توصيات الشبكة
    if (metrics.networkRequests > 10) {
      recommendations.push({
        type: 'network',
        priority: 'low',
        message: 'عدد كبير من طلبات الشبكة المعلقة - فكر في التجميع'
      });
    }
    
    return recommendations;
  }

  setupEventHandlers() {
    // معالج أخطاء JavaScript
    window.addEventListener('error', (event) => {
      this.metrics.errors++;
      this.reportIssue('javascript-error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });
    
    // معالج الأخطاء غير المعالجة
    window.addEventListener('unhandledrejection', (event) => {
      this.metrics.errors++;
      this.reportIssue('unhandled-promise', {
        reason: event.reason
      });
    });
    
    // معالج تغيير حالة الشبكة
    window.addEventListener('online', () => {
      console.log('🌐 تم الاتصال بالإنترنت');
    });
    
    window.addEventListener('offline', () => {
      console.log('📱 تم قطع الاتصال بالإنترنت');
    });
  }

  // 🎯 واجهة برمجة التطبيقات العامة
  
  getMetrics() {
    return { ...this.metrics };
  }
  
  getConfig() {
    return { ...this.config };
  }
  
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
  
  clearCache() {
    if (this.modules.network?.apiClient) {
      this.modules.network.apiClient.clearCache();
    }
    
    if (this.modules.memory?.imageManager) {
      this.modules.memory.imageManager.clearCache();
    }
  }
  
  optimizeForMobile() {
    this.updateConfig({
      enableCaching: true,
      thresholds: {
        loadTime: 5000, // أكثر تساهلاً للأجهزة المحمولة
        memoryUsage: 30 * 1024 * 1024, // 30MB
        networkLatency: 3000
      }
    });
    
    console.log('📱 تم تحسين الإعدادات للأجهزة المحمولة');
  }
  
  optimizeForDesktop() {
    this.updateConfig({
      enableCaching: true,
      thresholds: {
        loadTime: 2000,
        memoryUsage: 100 * 1024 * 1024, // 100MB
        networkLatency: 1000
      }
    });
    
    console.log('🖥️ تم تحسين الإعدادات لأجهزة سطح المكتب');
  }
  
  async runDiagnostics() {
    console.log('🔍 بدء تشخيص الأداء...');
    
    const diagnostics = {
      timestamp: new Date().toISOString(),
      browser: this.getBrowserInfo(),
      device: this.getDeviceInfo(),
      performance: await this.runPerformanceTests(),
      network: await this.runNetworkTests(),
      memory: this.runMemoryTests()
    };
    
    console.log('📋 نتائج التشخيص:', diagnostics);
    return diagnostics;
  }
  
  getBrowserInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };
  }
  
  getDeviceInfo() {
    return {
      deviceMemory: navigator.deviceMemory,
      hardwareConcurrency: navigator.hardwareConcurrency,
      connection: navigator.connection?.effectiveType,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      }
    };
  }
  
  async runPerformanceTests() {
    const tests = {};
    
    // اختبار سرعة الرسم
    const start = performance.now();
    await new Promise(resolve => requestAnimationFrame(resolve));
    tests.renderTime = performance.now() - start;
    
    return tests;
  }
  
  async runNetworkTests() {
    const tests = {};
    
    try {
      const start = performance.now();
      await fetch('/api/ping', { method: 'HEAD' });
      tests.latency = performance.now() - start;
    } catch (error) {
      tests.latency = -1;
      tests.error = error.message;
    }
    
    return tests;
  }
  
  runMemoryTests() {
    const tests = {};
    
    if ('memory' in performance) {
      const memory = performance.memory;
      tests.usedJSHeapSize = memory.usedJSHeapSize;
      tests.totalJSHeapSize = memory.totalJSHeapSize;
      tests.jsHeapSizeLimit = memory.jsHeapSizeLimit;
      tests.usagePercentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
    }
    
    return tests;
  }
}

// 🚀 إنشاء مثيل مدير الأداء العام
export const performanceManager = new PerformanceManager();

// 🎯 دوال مساعدة
export const initializePerformance = async () => {
  await performanceManager.init();
  return performanceManager;
};

export const getPerformanceMetrics = () => {
  return performanceManager.getMetrics();
};

export const optimizeForDevice = () => {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
  
  if (isMobile) {
    performanceManager.optimizeForMobile();
  } else {
    performanceManager.optimizeForDesktop();
  }
};

export default performanceManager;
