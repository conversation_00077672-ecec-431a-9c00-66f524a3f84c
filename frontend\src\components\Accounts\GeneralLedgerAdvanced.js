import React, { useState, useEffect, useRef, useMemo } from 'react';
import './GeneralLedger.css';

const GeneralLedgerAdvanced = ({ transactions, accounts, currentUser }) => {
  const [selectedAccount, setSelectedAccount] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [viewMode, setViewMode] = useState('detailed');
  const [sortConfig, setSortConfig] = useState({ key: 'date', direction: 'desc' });
  const [printOptions, setPrintOptions] = useState({
    includeOpeningBalance: true,
    includeRunningBalance: true,
    includeTransactionDetails: true,
    includeSummary: true,
    pageSize: 'A4',
    orientation: 'landscape'
  });
  const [exportFormat, setExportFormat] = useState('csv');
  const [advancedFilters, setAdvancedFilters] = useState({
    amountRange: { min: '', max: '' },
    transactionTypes: [],
    balanceType: 'all', // all, debit, credit
    includeZeroBalance: true
  });

  const printRef = useRef();

  // حساب أرصدة الحسابات
  const accountBalances = useMemo(() => {
    const balances = {};
    
    accounts.forEach(account => {
      if (account.type === 'detail') {
        balances[account.id] = {
          account,
          openingBalance: account.openingBalance || 0,
          debitTotal: 0,
          creditTotal: 0,
          currentBalance: account.openingBalance || 0,
          transactionCount: 0,
          lastTransactionDate: null,
          movements: []
        };
      }
    });

    // حساب الحركات والأرصدة
    transactions.forEach(transaction => {
      if (transaction.entries) {
        transaction.entries.forEach(entry => {
          if (balances[entry.accountId]) {
            balances[entry.accountId].debitTotal += entry.debit || 0;
            balances[entry.accountId].creditTotal += entry.credit || 0;
            balances[entry.accountId].transactionCount++;
            
            const transactionDate = new Date(transaction.date);
            if (!balances[entry.accountId].lastTransactionDate || 
                transactionDate > new Date(balances[entry.accountId].lastTransactionDate)) {
              balances[entry.accountId].lastTransactionDate = transaction.date;
            }

            // إضافة الحركة
            balances[entry.accountId].movements.push({
              date: transaction.date,
              transactionId: transaction.id,
              description: transaction.description,
              reference: transaction.reference,
              debit: entry.debit || 0,
              credit: entry.credit || 0,
              entryDescription: entry.description
            });
          }
        });
      }
    });

    // حساب الرصيد الحالي وترتيب الحركات
    Object.keys(balances).forEach(accountId => {
      const balance = balances[accountId];
      const account = balance.account;
      
      // حساب الرصيد حسب طبيعة الحساب
      if (account.category === 'assets' || account.category === 'expenses') {
        balance.currentBalance = balance.openingBalance + balance.debitTotal - balance.creditTotal;
      } else {
        balance.currentBalance = balance.openingBalance + balance.creditTotal - balance.debitTotal;
      }

      // ترتيب الحركات حسب التاريخ
      balance.movements.sort((a, b) => new Date(a.date) - new Date(b.date));

      // حساب الرصيد الجاري لكل حركة
      let runningBalance = balance.openingBalance;
      balance.movements.forEach(movement => {
        if (account.category === 'assets' || account.category === 'expenses') {
          runningBalance += movement.debit - movement.credit;
        } else {
          runningBalance += movement.credit - movement.debit;
        }
        movement.runningBalance = runningBalance;
      });
    });

    return balances;
  }, [transactions, accounts]);

  // تصفية الحسابات
  const filteredAccounts = useMemo(() => {
    return Object.values(accountBalances).filter(balance => {
      const matchesSearch = 
        balance.account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        balance.account.id.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesAmount = (!advancedFilters.amountRange.min || Math.abs(balance.currentBalance) >= parseFloat(advancedFilters.amountRange.min)) &&
                           (!advancedFilters.amountRange.max || Math.abs(balance.currentBalance) <= parseFloat(advancedFilters.amountRange.max));

      const matchesBalanceType = advancedFilters.balanceType === 'all' ||
                                (advancedFilters.balanceType === 'debit' && balance.currentBalance > 0) ||
                                (advancedFilters.balanceType === 'credit' && balance.currentBalance < 0);

      const matchesZeroBalance = advancedFilters.includeZeroBalance || balance.currentBalance !== 0;

      return matchesSearch && matchesAmount && matchesBalanceType && matchesZeroBalance;
    }).sort((a, b) => {
      const aValue = a[sortConfig.key] || a.account[sortConfig.key];
      const bValue = b[sortConfig.key] || b.account[sortConfig.key];
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [accountBalances, searchTerm, advancedFilters, sortConfig]);

  // تصفية حركات الحساب المحدد
  const selectedAccountMovements = useMemo(() => {
    if (!selectedAccount || !accountBalances[selectedAccount]) return [];

    return accountBalances[selectedAccount].movements.filter(movement => {
      const movementDate = new Date(movement.date);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      
      return movementDate >= startDate && movementDate <= endDate;
    });
  }, [selectedAccount, accountBalances, dateRange]);

  // حساب الإحصائيات
  const ledgerStats = useMemo(() => {
    const totalAccounts = filteredAccounts.length;
    const activeAccounts = filteredAccounts.filter(balance => balance.transactionCount > 0).length;
    const totalDebit = filteredAccounts.reduce((sum, balance) => sum + balance.debitTotal, 0);
    const totalCredit = filteredAccounts.reduce((sum, balance) => sum + balance.creditTotal, 0);
    const accountsWithBalance = filteredAccounts.filter(balance => balance.currentBalance !== 0).length;

    return {
      totalAccounts,
      activeAccounts,
      totalDebit,
      totalCredit,
      accountsWithBalance,
      balanceDifference: totalDebit - totalCredit
    };
  }, [filteredAccounts]);

  // وظائف الطباعة
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>دفتر الأستاذ العام</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .account-header { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
          .account-name { font-size: 18px; font-weight: bold; }
          .account-code { font-size: 14px; color: #666; }
          .balance-info { display: flex; justify-content: space-between; margin-top: 10px; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 12px; }
          th, td { border: 1px solid #ddd; padding: 6px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .amount { font-weight: bold; text-align: left; }
          .debit { color: #27ae60; }
          .credit { color: #e74c3c; }
          .running-balance { background: #f8f9fa; font-weight: bold; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    if (selectedAccount && accountBalances[selectedAccount]) {
      // طباعة حساب محدد
      const balance = accountBalances[selectedAccount];
      return `
        <div class="header">
          <div class="company-name">شركة شراء السياحية</div>
          <div class="report-title">دفتر الأستاذ - ${balance.account.name}</div>
          <div class="date-range">
            من ${new Date(dateRange.startDate).toLocaleDateString('ar-SA')} 
            إلى ${new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
          </div>
        </div>
        
        <div class="account-header">
          <div class="account-name">${balance.account.name}</div>
          <div class="account-code">رقم الحساب: ${balance.account.id}</div>
          <div class="balance-info">
            <span>الرصيد الافتتاحي: ${balance.openingBalance.toLocaleString()}</span>
            <span>الرصيد الحالي: ${balance.currentBalance.toLocaleString()}</span>
          </div>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>رقم المعاملة</th>
              <th>البيان</th>
              <th>المرجع</th>
              <th>مدين</th>
              <th>دائن</th>
              ${printOptions.includeRunningBalance ? '<th>الرصيد الجاري</th>' : ''}
            </tr>
          </thead>
          <tbody>
            ${printOptions.includeOpeningBalance ? `
              <tr>
                <td colspan="${printOptions.includeRunningBalance ? '6' : '5'}"><strong>الرصيد الافتتاحي</strong></td>
                <td class="amount running-balance">${balance.openingBalance.toLocaleString()}</td>
              </tr>
            ` : ''}
            ${selectedAccountMovements.map(movement => `
              <tr>
                <td>${new Date(movement.date).toLocaleDateString('ar-SA')}</td>
                <td>${movement.transactionId}</td>
                <td>${movement.description}</td>
                <td>${movement.reference || '-'}</td>
                <td class="amount debit">${movement.debit ? movement.debit.toLocaleString() : '-'}</td>
                <td class="amount credit">${movement.credit ? movement.credit.toLocaleString() : '-'}</td>
                ${printOptions.includeRunningBalance ? `<td class="amount running-balance">${movement.runningBalance.toLocaleString()}</td>` : ''}
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="footer">
          <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
          <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
        </div>
      `;
    } else {
      // طباعة ملخص جميع الحسابات
      return `
        <div class="header">
          <div class="company-name">شركة شراء السياحية</div>
          <div class="report-title">دفتر الأستاذ العام - ملخص الحسابات</div>
          <div class="date-range">كما في ${new Date().toLocaleDateString('ar-SA')}</div>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>رقم الحساب</th>
              <th>اسم الحساب</th>
              <th>الرصيد الافتتاحي</th>
              <th>إجمالي المدين</th>
              <th>إجمالي الدائن</th>
              <th>الرصيد الحالي</th>
              <th>عدد الحركات</th>
            </tr>
          </thead>
          <tbody>
            ${filteredAccounts.map(balance => `
              <tr>
                <td>${balance.account.id}</td>
                <td>${balance.account.name}</td>
                <td class="amount">${balance.openingBalance.toLocaleString()}</td>
                <td class="amount debit">${balance.debitTotal.toLocaleString()}</td>
                <td class="amount credit">${balance.creditTotal.toLocaleString()}</td>
                <td class="amount ${balance.currentBalance >= 0 ? 'debit' : 'credit'}">${balance.currentBalance.toLocaleString()}</td>
                <td>${balance.transactionCount}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="footer">
          <p>إجمالي الحسابات: ${ledgerStats.totalAccounts}</p>
          <p>الحسابات النشطة: ${ledgerStats.activeAccounts}</p>
          <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
        </div>
      `;
    }
  };

  // وظائف التصدير
  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    if (selectedAccount && accountBalances[selectedAccount]) {
      // تصدير حساب محدد
      const headers = ['التاريخ', 'رقم المعاملة', 'البيان', 'المرجع', 'مدين', 'دائن', 'الرصيد الجاري'];
      const rows = selectedAccountMovements.map(movement => [
        new Date(movement.date).toLocaleDateString('ar-SA'),
        movement.transactionId,
        movement.description,
        movement.reference || '',
        movement.debit || 0,
        movement.credit || 0,
        movement.runningBalance
      ]);

      const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `دفتر-الأستاذ-${accountBalances[selectedAccount].account.name}-${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
    } else {
      // تصدير ملخص الحسابات
      const headers = ['رقم الحساب', 'اسم الحساب', 'الرصيد الافتتاحي', 'إجمالي المدين', 'إجمالي الدائن', 'الرصيد الحالي', 'عدد الحركات'];
      const rows = filteredAccounts.map(balance => [
        balance.account.id,
        balance.account.name,
        balance.openingBalance,
        balance.debitTotal,
        balance.creditTotal,
        balance.currentBalance,
        balance.transactionCount
      ]);

      const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `دفتر-الأستاذ-العام-${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
    }
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  // وظائف أخرى
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedAccount('');
    setAdvancedFilters({
      amountRange: { min: '', max: '' },
      transactionTypes: [],
      balanceType: 'all',
      includeZeroBalance: true
    });
  };

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  return (
    <div className="general-ledger-advanced">
      <div className="ledger-header">
        <div className="header-content">
          <h2>📊 دفتر الأستاذ العام المتقدم</h2>
          <p>عرض تفصيلي لأرصدة وحركات جميع الحسابات</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAccountModal(true)}
          >
            📋 عرض حساب
          </button>
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-success"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowFilterModal(true)}
          >
            🔍 تصفية متقدمة
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="ledger-stats">
        <div className="stat-card">
          <div className="stat-icon">🏦</div>
          <div className="stat-info">
            <div className="stat-value">{ledgerStats.totalAccounts}</div>
            <div className="stat-label">إجمالي الحسابات</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⚡</div>
          <div className="stat-info">
            <div className="stat-value">{ledgerStats.activeAccounts}</div>
            <div className="stat-label">الحسابات النشطة</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{ledgerStats.totalDebit.toLocaleString()}</div>
            <div className="stat-label">إجمالي المدين</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💳</div>
          <div className="stat-info">
            <div className="stat-value">{ledgerStats.totalCredit.toLocaleString()}</div>
            <div className="stat-label">إجمالي الدائن</div>
          </div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="ledger-controls">
        <div className="controls-row">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث في الحسابات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="account-selector">
            <select
              value={selectedAccount}
              onChange={(e) => setSelectedAccount(e.target.value)}
              className="account-select"
            >
              <option value="">جميع الحسابات</option>
              {accounts.filter(acc => acc.type === 'detail').map(account => (
                <option key={account.id} value={account.id}>
                  {account.name} ({account.id})
                </option>
              ))}
            </select>
          </div>
          
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'summary' ? 'active' : ''}`}
              onClick={() => setViewMode('summary')}
            >
              📋 ملخص
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'detailed' ? 'active' : ''}`}
              onClick={() => setViewMode('detailed')}
            >
              📊 تفصيلي
            </button>
          </div>
          
          <button className="reset-btn" onClick={resetFilters}>
            🔄 إعادة تعيين
          </button>
        </div>

        {selectedAccount && (
          <div className="date-range-row">
            <label>فترة العرض:</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
        )}
      </div>

      {/* عرض البيانات */}
      <div className="ledger-content">
        {selectedAccount && accountBalances[selectedAccount] ? (
          // عرض تفاصيل حساب محدد
          <div className="account-details">
            <div className="account-header">
              <div className="account-info">
                <h3>{accountBalances[selectedAccount].account.name}</h3>
                <p>رقم الحساب: {accountBalances[selectedAccount].account.id}</p>
                <p>التصنيف: {accountBalances[selectedAccount].account.category}</p>
              </div>
              <div className="account-balances">
                <div className="balance-item">
                  <span className="label">الرصيد الافتتاحي:</span>
                  <span className="value">{accountBalances[selectedAccount].openingBalance.toLocaleString()}</span>
                </div>
                <div className="balance-item">
                  <span className="label">إجمالي المدين:</span>
                  <span className="value debit">{accountBalances[selectedAccount].debitTotal.toLocaleString()}</span>
                </div>
                <div className="balance-item">
                  <span className="label">إجمالي الدائن:</span>
                  <span className="value credit">{accountBalances[selectedAccount].creditTotal.toLocaleString()}</span>
                </div>
                <div className="balance-item current">
                  <span className="label">الرصيد الحالي:</span>
                  <span className={`value ${accountBalances[selectedAccount].currentBalance >= 0 ? 'debit' : 'credit'}`}>
                    {accountBalances[selectedAccount].currentBalance.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="movements-section">
              <h4>حركات الحساب</h4>
              {selectedAccountMovements.length === 0 ? (
                <div className="no-data">
                  <span className="icon">📊</span>
                  <h3>لا توجد حركات</h3>
                  <p>لا توجد حركات لهذا الحساب في الفترة المحددة</p>
                </div>
              ) : (
                <div className="movements-table">
                  <table className="ledger-table">
                    <thead>
                      <tr>
                        <th>التاريخ</th>
                        <th>رقم المعاملة</th>
                        <th>البيان</th>
                        <th>المرجع</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>الرصيد الجاري</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedAccountMovements.map((movement, index) => (
                        <tr key={index}>
                          <td>{new Date(movement.date).toLocaleDateString('ar-SA')}</td>
                          <td className="transaction-id">{movement.transactionId}</td>
                          <td className="description">{movement.description}</td>
                          <td>{movement.reference || '-'}</td>
                          <td className="amount debit">
                            {movement.debit ? movement.debit.toLocaleString() : '-'}
                          </td>
                          <td className="amount credit">
                            {movement.credit ? movement.credit.toLocaleString() : '-'}
                          </td>
                          <td className={`amount running-balance ${movement.runningBalance >= 0 ? 'debit' : 'credit'}`}>
                            {movement.runningBalance.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        ) : (
          // عرض ملخص جميع الحسابات
          <div className="accounts-summary">
            {viewMode === 'summary' ? (
              <div className="summary-cards">
                {filteredAccounts.map(balance => (
                  <div key={balance.account.id} className="account-card">
                    <div className="card-header">
                      <div className="account-name">{balance.account.name}</div>
                      <div className="account-code">{balance.account.id}</div>
                    </div>
                    <div className="card-content">
                      <div className="balance-info">
                        <div className="balance-item">
                          <span className="label">الرصيد الحالي:</span>
                          <span className={`value ${balance.currentBalance >= 0 ? 'debit' : 'credit'}`}>
                            {balance.currentBalance.toLocaleString()}
                          </span>
                        </div>
                        <div className="balance-item">
                          <span className="label">عدد الحركات:</span>
                          <span className="value">{balance.transactionCount}</span>
                        </div>
                      </div>
                      <div className="totals">
                        <div className="total-item debit">
                          <span className="label">مدين:</span>
                          <span className="value">{balance.debitTotal.toLocaleString()}</span>
                        </div>
                        <div className="total-item credit">
                          <span className="label">دائن:</span>
                          <span className="value">{balance.creditTotal.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="card-actions">
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => setSelectedAccount(balance.account.id)}
                      >
                        📊 عرض التفاصيل
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="detailed-table">
                <table className="ledger-table">
                  <thead>
                    <tr>
                      <th onClick={() => handleSort('id')}>
                        رقم الحساب {sortConfig.key === 'id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('name')}>
                        اسم الحساب {sortConfig.key === 'name' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('openingBalance')}>
                        الرصيد الافتتاحي {sortConfig.key === 'openingBalance' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('debitTotal')}>
                        إجمالي المدين {sortConfig.key === 'debitTotal' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('creditTotal')}>
                        إجمالي الدائن {sortConfig.key === 'creditTotal' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('currentBalance')}>
                        الرصيد الحالي {sortConfig.key === 'currentBalance' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('transactionCount')}>
                        عدد الحركات {sortConfig.key === 'transactionCount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAccounts.map(balance => (
                      <tr key={balance.account.id}>
                        <td className="account-code">{balance.account.id}</td>
                        <td className="account-name">{balance.account.name}</td>
                        <td className="amount">{balance.openingBalance.toLocaleString()}</td>
                        <td className="amount debit">{balance.debitTotal.toLocaleString()}</td>
                        <td className="amount credit">{balance.creditTotal.toLocaleString()}</td>
                        <td className={`amount ${balance.currentBalance >= 0 ? 'debit' : 'credit'}`}>
                          {balance.currentBalance.toLocaleString()}
                        </td>
                        <td className="transaction-count">{balance.transactionCount}</td>
                        <td className="actions">
                          <button
                            className="action-btn view-btn"
                            onClick={() => setSelectedAccount(balance.account.id)}
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          <button className="action-btn print-btn" title="طباعة">🖨️</button>
                          <button className="action-btn export-btn" title="تصدير">📊</button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>

      {/* النماذج المنبثقة - سأضيفها في التعليق التالي */}
    </div>
  );
};

export default GeneralLedgerAdvanced;