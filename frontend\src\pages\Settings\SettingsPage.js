import React, { useState, useEffect } from 'react';
import SystemSettings from '../../components/Settings/SystemSettings';
import './SettingsPage.css';

const SettingsPage = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل الإعدادات
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>جاري تحميل إعدادات النظام...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="settings-page">
      <SystemSettings />
    </div>
  );
};

export default SettingsPage;