#!/usr/bin/env python3
"""
خادم بسيط لنظام شراء السياحة
Simple server for Travel Accounting System
"""

import json
import sqlite3
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import os
from datetime import datetime

class TravelSystemHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            response = {
                "message": "🧳 نظام شراء السياحة - خادم API",
                "status": "running",
                "version": "1.0.0",
                "endpoints": [
                    "/customers",
                    "/bookings", 
                    "/invoices",
                    "/settings",
                    "/dashboard"
                ]
            }
        elif path == '/customers':
            response = {
                "customers": [
                    {
                        "id": 1,
                        "name": "أحمد محمد",
                        "phone": "+966501234567",
                        "email": "<EMAIL>",
                        "passport": "A123456789",
                        "status": "نشط"
                    },
                    {
                        "id": 2,
                        "name": "فاطمة علي",
                        "phone": "+966507654321",
                        "email": "<EMAIL>",
                        "passport": "B987654321",
                        "status": "نشط"
                    }
                ],
                "total": 2
            }
        elif path == '/bookings':
            response = {
                "bookings": [
                    {
                        "id": 1,
                        "type": "طيران",
                        "customer": "أحمد محمد",
                        "destination": "دبي",
                        "date": "2024-01-15",
                        "amount": 1500,
                        "status": "مؤكد"
                    },
                    {
                        "id": 2,
                        "type": "عمرة",
                        "customer": "فاطمة علي",
                        "destination": "مكة المكرمة",
                        "date": "2024-02-01",
                        "amount": 3000,
                        "status": "قيد المعالجة"
                    }
                ],
                "total": 2
            }
        elif path == '/dashboard':
            response = {
                "stats": {
                    "total_customers": 150,
                    "total_bookings": 89,
                    "total_revenue": 450000,
                    "pending_bookings": 12
                },
                "recent_bookings": [
                    {
                        "id": 1,
                        "customer": "أحمد محمد",
                        "type": "طيران",
                        "amount": 1500,
                        "date": "2024-01-15"
                    }
                ]
            }
        elif path == '/settings':
            response = {
                "settings": {
                    "company_name": "شركة شراء السياحة",
                    "company_phone": "+966-11-123-4567",
                    "company_email": "<EMAIL>",
                    "currency": "SAR",
                    "language": "ar",
                    "theme": "light"
                }
            }
        else:
            response = {"error": "Endpoint not found", "path": path}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_POST(self):
        """Handle POST requests"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
        
        response = {
            "message": "تم استلام البيانات بنجاح",
            "status": "success",
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def log_message(self, format, *args):
        """Override to customize logging"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server():
    """تشغيل الخادم"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, TravelSystemHandler)
    
    print("🚀 بدء تشغيل خادم نظام شراء السياحة...")
    print(f"🌐 الخادم يعمل على: http://localhost:8000")
    print("📊 نقاط النهاية المتاحة:")
    print("   • http://localhost:8000/ - معلومات الخادم")
    print("   • http://localhost:8000/customers - العملاء")
    print("   • http://localhost:8000/bookings - الحجوزات")
    print("   • http://localhost:8000/dashboard - لوحة التحكم")
    print("   • http://localhost:8000/settings - الإعدادات")
    print("\n🔧 لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
