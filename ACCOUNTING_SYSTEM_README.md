# نظام الحسابات المتكامل - شركة شراء السياحية

## نظرة عامة

تم تطوير نظام حسابات متكامل وشامل لشركة شراء السياحية يتضمن جميع الوظائف المحاسبية الأساسية والمتقدمة مع واجهة مستخدم عربية حديثة ومتجاوبة.

## المكونات الرئيسية

### 1. هيكل الحسابات (AccountsHierarchy)
- **الوصف**: إدارة شاملة لهيكل الحسابات الرئيسية والفرعية
- **الميزات**:
  - عرض شجري تفاعلي للحسابات
  - إضافة وتعديل وحذف الحسابات
  - تصنيف الحسابات (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
  - حساب الأرصدة التلقائي
  - البحث والتصفية المتقدمة
  - إحصائيات شاملة

### 2. دفتر الأستاذ العام (GeneralLedger)
- **الوصف**: عرض تفصيلي لجميع حركات الحسابات
- **الميزات**:
  - عرض حركات كل حساب بالتفصيل
  - حساب الأرصدة الجارية
  - تصفية بالتاريخ والحساب
  - تصدير التقارير
  - عرض القيود المرتبطة

### 3. دفاتر اليومية (JournalBooks)
- **الوصف**: إدارة أنواع مختلفة من دفاتر اليومية
- **الميزات**:
  - دفتر اليومية العامة
  - دفتر يومية المبيعات
  - دفتر يومية المشتريات
  - دفتر يومية النقدية
  - دفتر يومية البنك
  - إضافة قيود محاسبية جديدة
  - التحقق من توازن القيود
  - عرض تفاصيل القيود

### 4. العمليات التلقائية (AutomatedTransactions)
- **الوصف**: أتمتة العمليات المحاسبية المتكررة
- **الميزات**:
  - قواعد تلقائية محددة مسبقاً
  - معاملات سريعة (إيرادات، مصروفات، تحويلات)
  - تنفيذ فوري للعمليات
  - سجل المعاملات التلقائية
  - إحصائيات الأتمتة

### 5. مسار التدقيق (AuditTrail)
- **الوصف**: مراجعة وتتبع جميع العمليات المحاسبية
- **الميزات**:
  - عرض جميع المعاملات مع تفاصيل المستخدم
  - تصفية متقدمة (مستخدم، نوع، تاريخ)
  - تتبع تواريخ الإنشاء والتعديل
  - تصدير تقارير التدقيق
  - عرض تفاصيل كاملة لكل معاملة
  - إحصائيات شاملة

### 6. الفترات المحاسبية (AccountingPeriods)
- **الوصف**: إدارة الفترات المالية للشركة
- **الميزات**:
  - إنشاء فترات محاسبية جديدة
  - تفعيل وإغلاق الفترات
  - عرض إحصائيات الفترة الحالية
  - تصدير تقارير الفترات
  - إدارة حالات الفترات (نشطة، مغلقة، مسودة)

## الميزات التقنية

### التصميم والواجهة
- **تصميم متجاوب**: يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- **واجهة عربية**: دعم كامل للغة العربية مع اتجاه RTL
- **ألوان متناسقة**: نظام ألوان موحد ومريح للعين
- **أيقونات تعبيرية**: استخدام الإيموجي لسهولة التعرف على الوظائف
- **تأثيرات بصرية**: انتقالات سلسة وتأثيرات hover

### الوظائف المتقدمة
- **البحث الذكي**: بحث سريع في جميع البيانات
- **التصفية المتعددة**: تصفية بمعايير متعددة
- **التصدير**: تصدير البيانات بصيغة CSV
- **التحقق من البيانات**: التحقق من صحة القيود المحاسبية
- **الحفظ التلقائي**: حفظ تلقائي للبيانات
- **النسخ الاحتياطي**: إمكانية عمل نسخ احتياطية

## هيكل الملفات

```
src/components/Accounts/
├── AccountsHierarchy.js          # هيكل الحسابات
├── AccountsHierarchy.css         # تنسيق هيكل الحسابات
├── GeneralLedger.js              # دفتر الأستاذ العام
├── GeneralLedger.css             # تنسيق دفتر الأستاذ
├── JournalBooks.js               # دفاتر اليومية
├── JournalBooks.css              # تنسيق دفاتر اليومية
├── AutomatedTransactions.js      # العمليات التلقائية
├── AutomatedTransactions.css     # تنسيق العمليات التلقائية
├── AuditTrail.js                 # مسار التدقيق
├── AuditTrail.css                # تنسيق مسار التدقيق
├── AccountingPeriods.js          # الفترات المحاسبية
├── AccountingPeriods.css         # تنسيق الفترات المحاسبية
└── AccountsComponents.js         # ملف التصدير الرئيسي
```

## كيفية الاستخدام

### 1. إعداد هيكل الحسابات
1. انتقل إلى تبويب "هيكل الحسابات"
2. أضف الحسابات الرئيسية (أصول، خصوم، إيرادات، مصروفات)
3. أضف الحسابات الفرعية تحت كل حساب رئيسي
4. حدد نوع كل حساب (رئيسي أو تفصيلي)

### 2. تسجيل القيود المحاسبية
1. استخدم تبويب "دفاتر اليومية"
2. اختر نوع الدفتر المناسب
3. أضف قيد جديد مع التأكد من التوازن
4. احفظ القيد

### 3. العمليات التلقائية
1. انتقل إلى تبويب "العمليات التلقائية"
2. استخدم المعاملات السريعة للعمليات الشائعة
3. أو استخدم القواعد المحددة مسبقاً
4. أدخل المبلغ والوصف وقم بالتنفيذ

### 4. مراجعة العمليات
1. استخدم تبويب "مسار التدقيق"
2. صفي البيانات حسب المستخدم أو التاريخ
3. اعرض تفاصيل أي معاملة
4. صدر تقارير التدقيق

### 5. إدارة الفترات المحاسبية
1. انتقل إلى تبويب "الفترات المحاسبية"
2. أنشئ فترة محاسبية جديدة
3. فعل الفترة المطلوبة
4. اعرض إحصائيات الفترة الحالية

## المعايير المحاسبية

النظام يتبع المعايير المحاسبية السعودية والدولية:
- **مبدأ القيد المزدوج**: كل قيد يجب أن يكون متوازن
- **تصنيف الحسابات**: حسب المعايير المحاسبية المعتمدة
- **الفترات المحاسبية**: إدارة منفصلة لكل فترة مالية
- **مسار التدقيق**: تتبع كامل لجميع العمليات

## الأمان والصلاحيات

- **تتبع المستخدمين**: كل عملية مرتبطة بمستخدم محدد
- **تواريخ الإنشاء والتعديل**: تسجيل تلقائي لجميع التواريخ
- **عدم إمكانية الحذف**: الحسابات التي تحتوي على معاملات محمية
- **التحقق من البيانات**: فحص صحة البيانات قبل الحفظ

## التطوير المستقبلي

### الميزات المخطط إضافتها:
- **التقارير المالية**: قائمة الدخل، الميزانية العمومية
- **التكامل البنكي**: ربط مع البنوك لاستيراد الكشوفات
- **الفواتير الإلكترونية**: ربط مع منصة فاتورة
- **التقارير الضريبية**: تقارير ضريبة القيمة المضافة
- **النسخ الاحتياطي التلقائي**: نسخ احتياطية مجدولة
- **التنبيهات**: تنبيهات للمواعيد المهمة

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع الإلكتروني**: www.sharaubtravelsoft.com

## الترخيص

هذا النظام مطور خصيصاً لشركة شراء السياحية وهو محمي بحقوق الطبع والنشر.

---

**تم التطوير بواسطة**: فريق التطوير - شركة شراء السياحية  
**تاريخ آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0