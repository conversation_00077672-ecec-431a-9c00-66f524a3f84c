import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const InvoicesPage = () => {
  const location = useLocation();
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterService, setFilterService] = useState('all');
  const [selectedInvoices, setSelectedInvoices] = useState([]);

  const [newInvoice, setNewInvoice] = useState({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    serviceType: 'work_visa',
    serviceDetails: '',
    baseAmount: '',
    discountAmount: '',
    totalAmount: '',
    currency: 'SAR',
    paymentMethod: 'cash',
    paymentStatus: 'pending',
    dueDate: '',
    notes: ''
  });

  useEffect(() => {
    // فتح نموذج الإضافة تلقائياً إذا كان المسار /new
    if (location.pathname.includes('/new')) {
      setShowAddForm(true);
    }
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setInvoices([
        {
          id: 'INV-2024-001',
          customerName: 'أحمد محمد العلي',
          customerPhone: '+966501234567',
          customerEmail: '<EMAIL>',
          serviceType: 'flight_ticket',
          serviceDetails: 'الرياض → دبي - درجة اقتصادية',
          baseAmount: 2000,
          discountAmount: 0,
          totalAmount: 2000,
          currency: 'SAR',
          paymentMethod: 'cash',
          paymentStatus: 'paid',
          issueDate: '2024-01-20',
          dueDate: '2024-01-25',
          paidDate: '2024-01-20',
          notes: 'دفع نقدي كامل'
        },
        {
          id: 'INV-2024-002',
          customerName: 'فاطمة سالم الأحمد',
          customerPhone: '+************',
          customerEmail: '<EMAIL>',
          serviceType: 'umrah_visa',
          serviceDetails: 'باقة عمرة فاخرة - 7 أيام',
          baseAmount: 7500,
          discountAmount: 500,
          totalAmount: 7000,
          currency: 'SAR',
          paymentMethod: 'bank_transfer',
          paymentStatus: 'pending',
          issueDate: '2024-01-19',
          dueDate: '2024-01-26',
          paidDate: null,
          notes: 'في انتظار التحويل البنكي'
        },
        {
          id: 'INV-2024-003',
          customerName: 'خالد أحمد الزهراني',
          customerPhone: '+************',
          customerEmail: '<EMAIL>',
          serviceType: 'car_booking',
          serviceDetails: 'تأجير سيارة كامري - 3 أيام',
          baseAmount: 400,
          discountAmount: 50,
          totalAmount: 350,
          currency: 'USD',
          paymentMethod: 'cash',
          paymentStatus: 'partial',
          issueDate: '2024-01-18',
          dueDate: '2024-01-23',
          paidDate: null,
          notes: 'دفع جزئي 200 دولار'
        },
        {
          id: 'INV-2024-004',
          customerName: 'نورا علي السالم',
          customerPhone: '+************',
          customerEmail: '<EMAIL>',
          serviceType: 'passport',
          serviceDetails: 'تجديد جواز سفر عاجل',
          baseAmount: 120000,
          discountAmount: 0,
          totalAmount: 120000,
          currency: 'YER',
          paymentMethod: 'cash',
          paymentStatus: 'overdue',
          issueDate: '2024-01-15',
          dueDate: '2024-01-20',
          paidDate: null,
          notes: 'متأخر عن الدفع'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, [location.pathname]);

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'partial': return '#e67e22';
      case 'overdue': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'partial': return 'جزئي';
      case 'overdue': return 'متأخر';
      default: return status;
    }
  };

  const getServiceText = (service) => {
    switch (service) {
      case 'work_visa': return 'معاملة تأشيرة عمل';
      case 'visit_visa': return 'معاملة تأشيرة زيارة';
      case 'umrah_visa': return 'تأشيرة عمرة';
      case 'hajj_visa': return 'تأشيرة حج';
      case 'three_month_visa': return 'فيزه أبو ثلاثة أشهر';
      case 'individual_visa_residence': return 'فيزه فردي مع الإقامة سنة';
      case 'passport': return 'جوازات';
      case 'bus_ticket': return 'تذكرة باص';
      case 'flight_ticket': return 'تذكرة طيران';
      case 'car_booking': return 'حجز سيارة';
      case 'document_attestation': return 'تعميد وثائق';
      case 'other': return 'أخرى';
      default: return service;
    }
  };

  const handleAddInvoice = (e) => {
    e.preventDefault();
    const invoice = {
      id: `INV-${new Date().getFullYear()}-${String(invoices.length + 1).padStart(3, '0')}`,
      ...newInvoice,
      baseAmount: parseFloat(newInvoice.baseAmount),
      discountAmount: parseFloat(newInvoice.discountAmount || 0),
      totalAmount: parseFloat(newInvoice.baseAmount) - parseFloat(newInvoice.discountAmount || 0),
      issueDate: new Date().toISOString().split('T')[0],
      paidDate: null
    };
    setInvoices([invoice, ...invoices]);
    setNewInvoice({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      serviceType: 'work_visa',
      serviceDetails: '',
      baseAmount: '',
      discountAmount: '',
      totalAmount: '',
      currency: 'SAR',
      paymentMethod: 'cash',
      paymentStatus: 'pending',
      dueDate: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.serviceDetails.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || invoice.paymentStatus === filterStatus;
    const matchesService = filterService === 'all' || invoice.serviceType === filterService;
    return matchesSearch && matchesStatus && matchesService;
  });

  const handleSelectInvoice = (invoiceId) => {
    setSelectedInvoices(prev => 
      prev.includes(invoiceId) 
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    );
  };

  const handleSelectAll = () => {
    setSelectedInvoices(
      selectedInvoices.length === filteredInvoices.length 
        ? [] 
        : filteredInvoices.map(inv => inv.id)
    );
  };

  // وظائف الأزرار الجديدة
  const handleViewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowViewModal(true);
  };

  const handleEditInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setNewInvoice({
      customerName: invoice.customerName,
      customerPhone: invoice.customerPhone,
      customerEmail: invoice.customerEmail,
      serviceType: invoice.serviceType,
      serviceDetails: invoice.serviceDetails,
      baseAmount: invoice.baseAmount.toString(),

      discountAmount: invoice.discountAmount.toString(),
      totalAmount: invoice.totalAmount.toString(),
      currency: invoice.currency,
      paymentMethod: invoice.paymentMethod,
      paymentStatus: invoice.paymentStatus,
      dueDate: invoice.dueDate,
      notes: invoice.notes
    });
    setShowEditModal(true);
  };

  const handleUpdateInvoice = (e) => {
    e.preventDefault();
    const updatedInvoice = {
      ...selectedInvoice,
      ...newInvoice,
      baseAmount: parseFloat(newInvoice.baseAmount),
      discountAmount: parseFloat(newInvoice.discountAmount || 0),
      totalAmount: parseFloat(newInvoice.baseAmount) - parseFloat(newInvoice.discountAmount || 0)
    };
    
    setInvoices(invoices.map(inv => 
      inv.id === selectedInvoice.id ? updatedInvoice : inv
    ));
    
    setShowEditModal(false);
    setSelectedInvoice(null);
    setNewInvoice({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      serviceType: 'work_visa',
      serviceDetails: '',
      baseAmount: '',
      discountAmount: '',
      totalAmount: '',
      currency: 'SAR',
      paymentMethod: 'cash',
      paymentStatus: 'pending',
      dueDate: '',
      notes: ''
    });
  };

  const handlePrintInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowPrintModal(true);
  };

  const handleDeleteInvoice = (invoice) => {
    if (window.confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.id}؟`)) {
      setInvoices(invoices.filter(inv => inv.id !== invoice.id));
    }
  };

  const handlePrint = () => {
    window.print();
    setShowPrintModal(false);
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#666' }}>جاري تحميل الفواتير...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          📄 إدارة الفواتير
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          إنشاء وإدارة ومتابعة جميع الفواتير
        </p>
      </div>

      {/* الإحصائيات السريعة */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px', 
        marginBottom: '30px' 
      }}>
        {[
          { title: 'إجمالي الفواتير', value: invoices.length, color: '#3498db', icon: '📄' },
          { title: 'فواتير مدفوعة', value: invoices.filter(inv => inv.paymentStatus === 'paid').length, color: '#27ae60', icon: '✅' },
          { title: 'فواتير معلقة', value: invoices.filter(inv => inv.paymentStatus === 'pending').length, color: '#f39c12', icon: '⏳' },
          { title: 'فواتير متأخرة', value: invoices.filter(inv => inv.paymentStatus === 'overdue').length, color: '#e74c3c', icon: '⚠️' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '12px',
            boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '10px' }}>{stat.icon}</div>
            <h3 style={{ color: stat.color, margin: '0 0 10px 0', fontSize: '14px' }}>{stat.title}</h3>
            <p style={{ fontSize: '20px', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>{stat.value}</p>
          </div>
        ))}
      </div>

      {/* أدوات التحكم */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '20px',
          flexWrap: 'wrap',
          gap: '10px'
        }}>
          <button
            onClick={() => setShowAddForm(true)}
            style={{
              background: '#27ae60',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'background 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.background = '#219a52'}
            onMouseLeave={(e) => e.target.style.background = '#27ae60'}
          >
            ➕ إنشاء فاتورة جديدة
          </button>

          <div style={{ display: 'flex', gap: '10px', alignItems: 'center', flexWrap: 'wrap' }}>
            {selectedInvoices.length > 0 && (
              <div style={{ display: 'flex', gap: '10px' }}>
                <button style={{
                  background: '#3498db',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}>
                  طباعة المحدد ({selectedInvoices.length})
                </button>
                <button style={{
                  background: '#e67e22',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}>
                  تصدير PDF
                </button>
              </div>
            )}
          </div>
        </div>

        {/* البحث والفلترة */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '15px' 
        }}>
          <input
            type="text"
            placeholder="البحث في الفواتير..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          />
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          >
            <option value="all">جميع الحالات</option>
            <option value="paid">مدفوع</option>
            <option value="pending">معلق</option>
            <option value="partial">جزئي</option>
            <option value="overdue">متأخر</option>
          </select>

          <select
            value={filterService}
            onChange={(e) => setFilterService(e.target.value)}
            style={{
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          >
            <option value="all">جميع الخدمات</option>
            <option value="work_visa">معاملة تأشيرة عمل</option>
            <option value="visit_visa">معاملة تأشيرة زيارة</option>
            <option value="umrah_visa">تأشيرة عمرة</option>
            <option value="hajj_visa">تأشيرة حج</option>
            <option value="three_month_visa">فيزه أبو ثلاثة أشهر</option>
            <option value="individual_visa_residence">فيزه فردي مع الإقامة سنة</option>
            <option value="passport">جوازات</option>
            <option value="bus_ticket">تذكرة باص</option>
            <option value="flight_ticket">تذكرة طيران</option>
            <option value="car_booking">حجز سيارة</option>
            <option value="document_attestation">تعميد وثائق</option>
            <option value="other">أخرى</option>
          </select>
        </div>
      </div>

      {/* جدول الفواتير */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ background: '#f8f9fa' }}>
              <th style={{ padding: '15px', textAlign: 'center' }}>
                <input
                  type="checkbox"
                  checked={selectedInvoices.length === filteredInvoices.length && filteredInvoices.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>رقم الفاتورة</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>العميل</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>الخدمة</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>المبلغ الإجمالي</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الحالة</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>تاريخ الإصدار</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>تاريخ الاستحقاق</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredInvoices.map((invoice, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <input
                    type="checkbox"
                    checked={selectedInvoices.includes(invoice.id)}
                    onChange={() => handleSelectInvoice(invoice.id)}
                  />
                </td>
                <td style={{ padding: '15px', fontWeight: 'bold', color: '#3498db' }}>
                  {invoice.id}
                </td>
                <td style={{ padding: '15px' }}>
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{invoice.customerName}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>{invoice.customerPhone}</div>
                  </div>
                </td>
                <td style={{ padding: '15px' }}>
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{getServiceText(invoice.serviceType)}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>{invoice.serviceDetails}</div>
                  </div>
                </td>
                <td style={{ padding: '15px', fontWeight: 'bold', color: '#27ae60' }}>
                  {formatCurrency(invoice.totalAmount, invoice.currency)}
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <span style={{
                    background: getStatusColor(invoice.paymentStatus),
                    color: 'white',
                    padding: '5px 12px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    {getStatusText(invoice.paymentStatus)}
                  </span>
                </td>
                <td style={{ padding: '15px' }}>{invoice.issueDate}</td>
                <td style={{ padding: '15px' }}>{invoice.dueDate}</td>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', flexWrap: 'wrap' }}>
                    <button 
                      onClick={() => handleViewInvoice(invoice)}
                      style={{
                        background: '#3498db',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      👁️ عرض
                    </button>
                    <button 
                      onClick={() => handleEditInvoice(invoice)}
                      style={{
                        background: '#e67e22',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      ✏️ تعديل
                    </button>
                    <button 
                      onClick={() => handlePrintInvoice(invoice)}
                      style={{
                        background: '#27ae60',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      🖨️ طباعة
                    </button>
                    <button 
                      onClick={() => handleDeleteInvoice(invoice)}
                      style={{
                        background: '#e74c3c',
                        color: 'white',
                        border: 'none',
                        padding: '5px 10px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredInvoices.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '50px', 
            color: '#666',
            fontSize: '16px'
          }}>
            لا توجد فواتير تطابق معايير البحث
          </div>
        )}
      </div>

      {/* نموذج إضافة فاتورة جديدة */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>📝 إنشاء فاتورة جديدة</h2>
            
            <form onSubmit={handleAddInvoice}>
              {/* بيانات العميل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#3498db', marginBottom: '15px' }}>👤 بيانات العميل</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newInvoice.customerName}
                      onChange={(e) => setNewInvoice({...newInvoice, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newInvoice.customerPhone}
                      onChange={(e) => setNewInvoice({...newInvoice, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={newInvoice.customerEmail}
                      onChange={(e) => setNewInvoice({...newInvoice, customerEmail: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* تفاصيل الخدمة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>🛎️ تفاصيل الخدمة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الخدمة</label>
                    <select
                      value={newInvoice.serviceType}
                      onChange={(e) => setNewInvoice({...newInvoice, serviceType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="work_visa">معاملة تأشيرة عمل</option>
                      <option value="visit_visa">معاملة تأشيرة زيارة</option>
                      <option value="umrah_visa">تأشيرة عمرة</option>
                      <option value="hajj_visa">تأشيرة حج</option>
                      <option value="three_month_visa">فيزه أبو ثلاثة أشهر</option>
                      <option value="individual_visa_residence">فيزه فردي مع الإقامة سنة</option>
                      <option value="passport">جوازات</option>
                      <option value="bus_ticket">تذكرة باص</option>
                      <option value="flight_ticket">تذكرة طيران</option>
                      <option value="car_booking">حجز سيارة</option>
                      <option value="document_attestation">تعميد وثائق</option>
                      <option value="other">أخرى</option>
                    </select>
                  </div>
                  <div style={{ gridColumn: 'span 2' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تفاصيل الخدمة</label>
                    <input
                      type="text"
                      value={newInvoice.serviceDetails}
                      onChange={(e) => setNewInvoice({...newInvoice, serviceDetails: e.target.value})}
                      required
                      placeholder="مثال: الرياض → دبي - درجة اقتصادية"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#e67e22', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ الأساسي</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newInvoice.baseAmount}
                      onChange={(e) => setNewInvoice({...newInvoice, baseAmount: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الخصم</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newInvoice.discountAmount}
                      onChange={(e) => setNewInvoice({...newInvoice, discountAmount: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>العملة</label>
                    <select
                      value={newInvoice.currency}
                      onChange={(e) => setNewInvoice({...newInvoice, currency: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                      <option value="YER">🇾🇪 ريال يمني (YER)</option>
                      <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>طريقة الدفع</label>
                    <select
                      value={newInvoice.paymentMethod}
                      onChange={(e) => setNewInvoice({...newInvoice, paymentMethod: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="cash">نقدي</option>
                      <option value="bank_transfer">تحويل بنكي</option>
                      <option value="check">شيك</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ الاستحقاق</label>
                    <input
                      type="date"
                      value={newInvoice.dueDate}
                      onChange={(e) => setNewInvoice({...newInvoice, dueDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>📝 ملاحظات</h3>
                <textarea
                  value={newInvoice.notes}
                  onChange={(e) => setNewInvoice({...newInvoice, notes: e.target.value})}
                  rows="3"
                  placeholder="ملاحظات إضافية..."
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              {/* أزرار التحكم */}
              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    background: '#95a5a6',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    background: '#27ae60',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إنشاء الفاتورة
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة عرض الفاتورة */}
      {showViewModal && selectedInvoice && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{ textAlign: 'center', marginBottom: '25px' }}>
              <h2 style={{ color: '#2c3e50', margin: 0 }}>📄 عرض الفاتورة</h2>
              <p style={{ color: '#7f8c8d', margin: '5px 0 0 0' }}>{selectedInvoice.id}</p>
            </div>

            <div style={{ 
              background: '#f8f9fa', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <strong>العميل:</strong> {selectedInvoice.customerName}
                </div>
                <div>
                  <strong>الهاتف:</strong> {selectedInvoice.customerPhone}
                </div>
                <div>
                  <strong>البريد:</strong> {selectedInvoice.customerEmail}
                </div>
                <div>
                  <strong>نوع الخدمة:</strong> {selectedInvoice.serviceType === 'flight' ? '✈️ طيران' : 
                    selectedInvoice.serviceType === 'hotel' ? '🏨 فندق' : 
                    selectedInvoice.serviceType === 'visa' ? '📋 تأشيرة' : 
                    selectedInvoice.serviceType === 'package' ? '📦 باقة سياحية' : selectedInvoice.serviceType}
                </div>
                <div style={{ gridColumn: '1 / -1' }}>
                  <strong>تفاصيل الخدمة:</strong> {selectedInvoice.serviceDetails}
                </div>
              </div>
            </div>

            <div style={{ 
              background: '#e8f5e8', 
              padding: '20px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 15px 0', color: '#27ae60' }}>💰 تفاصيل المبلغ</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
                <div>المبلغ الأساسي: {formatCurrency(selectedInvoice.baseAmount, selectedInvoice.currency)}</div>
                <div>الخصم: {formatCurrency(selectedInvoice.discountAmount, selectedInvoice.currency)}</div>
                <div style={{ 
                  gridColumn: '1 / -1', 
                  fontSize: '18px', 
                  fontWeight: 'bold', 
                  color: '#27ae60',
                  borderTop: '2px solid #27ae60',
                  paddingTop: '10px',
                  marginTop: '10px'
                }}>
                  المجموع: {formatCurrency(selectedInvoice.totalAmount, selectedInvoice.currency)}
                </div>
              </div>
            </div>

            <div style={{ 
              background: '#fff3cd', 
              padding: '15px', 
              borderRadius: '10px',
              marginBottom: '20px'
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
                <div><strong>تاريخ الإصدار:</strong> {selectedInvoice.issueDate}</div>
                <div><strong>تاريخ الاستحقاق:</strong> {selectedInvoice.dueDate}</div>
                <div><strong>طريقة الدفع:</strong> {selectedInvoice.paymentMethod === 'cash' ? 'نقدي' : 
                  selectedInvoice.paymentMethod === 'bank_transfer' ? 'تحويل بنكي' : 'شيك'}</div>
                <div>
                  <strong>حالة الدفع:</strong> 
                  <span style={{
                    background: getStatusColor(selectedInvoice.paymentStatus),
                    color: 'white',
                    padding: '3px 8px',
                    borderRadius: '12px',
                    fontSize: '12px',
                    marginRight: '8px'
                  }}>
                    {getStatusText(selectedInvoice.paymentStatus)}
                  </span>
                </div>
              </div>
            </div>

            {selectedInvoice.notes && (
              <div style={{ 
                background: '#f0f0f0', 
                padding: '15px', 
                borderRadius: '10px',
                marginBottom: '20px'
              }}>
                <strong>ملاحظات:</strong> {selectedInvoice.notes}
              </div>
            )}

            <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
              <button
                onClick={() => setShowViewModal(false)}
                style={{
                  background: '#95a5a6',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إغلاق
              </button>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  handlePrintInvoice(selectedInvoice);
                }}
                style={{
                  background: '#27ae60',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل الفاتورة */}
      {showEditModal && selectedInvoice && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '20px', textAlign: 'center' }}>
              ✏️ تعديل الفاتورة {selectedInvoice.id}
            </h2>
            
            <form onSubmit={handleUpdateInvoice}>
              {/* نفس نموذج الإضافة ولكن للتعديل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#3498db', marginBottom: '15px' }}>👤 معلومات العميل</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newInvoice.customerName}
                      onChange={(e) => setNewInvoice({...newInvoice, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newInvoice.customerPhone}
                      onChange={(e) => setNewInvoice({...newInvoice, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={newInvoice.customerEmail}
                      onChange={(e) => setNewInvoice({...newInvoice, customerEmail: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>🛍️ تفاصيل الخدمة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الخدمة</label>
                    <select
                      value={newInvoice.serviceType}
                      onChange={(e) => setNewInvoice({...newInvoice, serviceType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="work_visa">🔧 معاملة تأشيرة عمل</option>
                      <option value="visit_visa">👥 معاملة تأشيرة زيارة</option>
                      <option value="umrah_visa">🕋 تأشيرة عمرة</option>
                      <option value="hajj_visa">🕋 تأشيرة حج</option>
                      <option value="three_month_visa">📋 فيزه أبو ثلاثة أشهر</option>
                      <option value="individual_visa_residence">🏠 فيزه فردي مع الإقامة سنة</option>
                      <option value="passport">📘 جوازات</option>
                      <option value="bus_ticket">🚌 تذكرة باص</option>
                      <option value="flight_ticket">✈️ تذكرة طيران</option>
                      <option value="car_booking">🚗 حجز سيارة</option>
                      <option value="document_attestation">📄 تعميد وثائق</option>
                      <option value="other">🔧 أخرى</option>
                    </select>
                  </div>
                  <div style={{ gridColumn: '1 / -1' }}>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تفاصيل الخدمة</label>
                    <textarea
                      value={newInvoice.serviceDetails}
                      onChange={(e) => setNewInvoice({...newInvoice, serviceDetails: e.target.value})}
                      required
                      rows="3"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedInvoice(null);
                  }}
                  style={{
                    background: '#95a5a6',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    background: '#e67e22',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  ✏️ حفظ التعديلات
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة الطباعة */}
      {showPrintModal && selectedInvoice && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '40px',
            borderRadius: '15px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            {/* تصميم الفاتورة للطباعة */}
            <div id="invoice-print" style={{ fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
              {/* رأس الفاتورة */}
              <div style={{ 
                textAlign: 'center', 
                borderBottom: '3px solid #3498db', 
                paddingBottom: '20px',
                marginBottom: '30px'
              }}>
                <h1 style={{ color: '#2c3e50', margin: '0 0 10px 0', fontSize: '28px' }}>
                  🏢 شركة شراء السفر والسياحة
                </h1>
                <p style={{ color: '#7f8c8d', margin: 0, fontSize: '16px' }}>
                  الرياض - المملكة العربية السعودية | هاتف: +966112345678
                </p>
                <h2 style={{ 
                  background: '#3498db', 
                  color: 'white', 
                  padding: '10px 20px', 
                  borderRadius: '25px',
                  display: 'inline-block',
                  margin: '20px 0 0 0',
                  fontSize: '20px'
                }}>
                  📄 فاتورة رقم: {selectedInvoice.id}
                </h2>
              </div>

              {/* معلومات العميل والفاتورة */}
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: '1fr 1fr', 
                gap: '30px',
                marginBottom: '30px'
              }}>
                <div style={{ 
                  background: '#f8f9fa', 
                  padding: '20px', 
                  borderRadius: '10px',
                  border: '2px solid #e9ecef'
                }}>
                  <h3 style={{ color: '#495057', margin: '0 0 15px 0' }}>👤 بيانات العميل</h3>
                  <p><strong>الاسم:</strong> {selectedInvoice.customerName}</p>
                  <p><strong>الهاتف:</strong> {selectedInvoice.customerPhone}</p>
                  <p><strong>البريد:</strong> {selectedInvoice.customerEmail}</p>
                </div>
                
                <div style={{ 
                  background: '#f8f9fa', 
                  padding: '20px', 
                  borderRadius: '10px',
                  border: '2px solid #e9ecef'
                }}>
                  <h3 style={{ color: '#495057', margin: '0 0 15px 0' }}>📅 بيانات الفاتورة</h3>
                  <p><strong>تاريخ الإصدار:</strong> {selectedInvoice.issueDate}</p>
                  <p><strong>تاريخ الاستحقاق:</strong> {selectedInvoice.dueDate}</p>
                  <p><strong>طريقة الدفع:</strong> {selectedInvoice.paymentMethod === 'cash' ? 'نقدي' : 
                    selectedInvoice.paymentMethod === 'bank_transfer' ? 'تحويل بنكي' : 'شيك'}</p>
                </div>
              </div>

              {/* تفاصيل الخدمة */}
              <div style={{ 
                background: '#e8f5e8', 
                padding: '20px', 
                borderRadius: '10px',
                marginBottom: '30px',
                border: '2px solid #27ae60'
              }}>
                <h3 style={{ color: '#27ae60', margin: '0 0 15px 0' }}>🛍️ تفاصيل الخدمة</h3>
                <p><strong>نوع الخدمة:</strong> {selectedInvoice.serviceType === 'flight' ? '✈️ طيران' : 
                  selectedInvoice.serviceType === 'hotel' ? '🏨 فندق' : 
                  selectedInvoice.serviceType === 'visa' ? '📋 تأشيرة' : 
                  selectedInvoice.serviceType === 'package' ? '📦 باقة سياحية' : selectedInvoice.serviceType}</p>
                <p><strong>التفاصيل:</strong> {selectedInvoice.serviceDetails}</p>
              </div>

              {/* جدول المبالغ */}
              <table style={{ 
                width: '100%', 
                borderCollapse: 'collapse',
                marginBottom: '30px',
                border: '2px solid #dee2e6'
              }}>
                <thead>
                  <tr style={{ background: '#343a40', color: 'white' }}>
                    <th style={{ padding: '15px', textAlign: 'right', border: '1px solid #dee2e6' }}>البيان</th>
                    <th style={{ padding: '15px', textAlign: 'center', border: '1px solid #dee2e6' }}>المبلغ</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>المبلغ الأساسي</td>
                    <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                      {formatCurrency(selectedInvoice.baseAmount, selectedInvoice.currency)}
                    </td>
                  </tr>

                  {selectedInvoice.discountAmount > 0 && (
                    <tr>
                      <td style={{ padding: '12px', border: '1px solid #dee2e6' }}>الخصم</td>
                      <td style={{ padding: '12px', textAlign: 'center', border: '1px solid #dee2e6', color: '#e74c3c' }}>
                        -{formatCurrency(selectedInvoice.discountAmount, selectedInvoice.currency)}
                      </td>
                    </tr>
                  )}
                  <tr style={{ background: '#28a745', color: 'white', fontWeight: 'bold', fontSize: '18px' }}>
                    <td style={{ padding: '15px', border: '1px solid #dee2e6' }}>المجموع الإجمالي</td>
                    <td style={{ padding: '15px', textAlign: 'center', border: '1px solid #dee2e6' }}>
                      {formatCurrency(selectedInvoice.totalAmount, selectedInvoice.currency)}
                    </td>
                  </tr>
                </tbody>
              </table>

              {/* ملاحظات */}
              {selectedInvoice.notes && (
                <div style={{ 
                  background: '#fff3cd', 
                  padding: '15px', 
                  borderRadius: '10px',
                  marginBottom: '30px',
                  border: '2px solid #ffeaa7'
                }}>
                  <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>📝 ملاحظات:</h4>
                  <p style={{ margin: 0 }}>{selectedInvoice.notes}</p>
                </div>
              )}

              {/* تذييل الفاتورة */}
              <div style={{ 
                textAlign: 'center', 
                borderTop: '2px solid #dee2e6', 
                paddingTop: '20px',
                color: '#6c757d'
              }}>
                <p style={{ margin: '0 0 10px 0', fontSize: '16px' }}>
                  🙏 شكراً لتعاملكم معنا - نتطلع لخدمتكم مرة أخرى
                </p>
                <p style={{ margin: 0, fontSize: '14px' }}>
                  الرقم الضريبي: 300123456789003 | السجل التجاري: 1010123456
                </p>
              </div>
            </div>

            {/* أزرار الطباعة */}
            <div style={{ 
              display: 'flex', 
              gap: '15px', 
              justifyContent: 'center',
              marginTop: '30px',
              borderTop: '2px solid #dee2e6',
              paddingTop: '20px'
            }}>
              <button
                onClick={() => setShowPrintModal(false)}
                style={{
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                إغلاق
              </button>
              <button
                onClick={handlePrint}
                style={{
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                🖨️ طباعة الفاتورة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS للأنيميشن */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default InvoicesPage;
