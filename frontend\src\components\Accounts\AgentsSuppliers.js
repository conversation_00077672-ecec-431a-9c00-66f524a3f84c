import React, { useState, useMemo, useEffect } from 'react';
import './AgentsSuppliers.css';

// مكون نموذج الكيان
const EntityForm = ({ entity, activeTab, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: activeTab === 'agents' ? 'airline_agent' : 'transport_supplier',
    contactPerson: '',
    phone: '',
    email: '',
    address: '',
    creditLimit: '',
    status: 'active'
  });

  useEffect(() => {
    if (entity) {
      setFormData({
        name: entity.name || '',
        type: entity.type || (activeTab === 'agents' ? 'airline_agent' : 'transport_supplier'),
        contactPerson: entity.contactPerson || '',
        phone: entity.phone || '',
        email: entity.email || '',
        address: entity.address || '',
        creditLimit: entity.creditLimit || '',
        status: entity.status || 'active'
      });
    }
  }, [entity, activeTab]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.name || !formData.contactPerson || !formData.phone) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }
    onSave(formData);
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="modal-body">
      <form onSubmit={handleSubmit} className="entity-form">
        <div className="form-group">
          <label>اسم {activeTab === 'agents' ? 'الوكيل' : 'المورد'} *</label>
          <input 
            type="text" 
            placeholder="أدخل الاسم" 
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
        </div>

        <div className="form-group">
          <label>النوع *</label>
          <select 
            value={formData.type}
            onChange={(e) => handleChange('type', e.target.value)}
          >
            {activeTab === 'agents' ? (
              <>
                <option value="airline_agent">وكيل طيران</option>
                <option value="hotel_agent">وكيل فنادق</option>
              </>
            ) : (
              <>
                <option value="transport_supplier">مورد نقل</option>
                <option value="tourism_supplier">مورد خدمات سياحية</option>
              </>
            )}
          </select>
        </div>

        <div className="form-group">
          <label>الشخص المسؤول *</label>
          <input 
            type="text" 
            placeholder="أدخل اسم الشخص المسؤول" 
            value={formData.contactPerson}
            onChange={(e) => handleChange('contactPerson', e.target.value)}
            required
          />
        </div>

        <div className="form-group">
          <label>رقم الهاتف *</label>
          <input 
            type="tel" 
            placeholder="أدخل رقم الهاتف" 
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            required
          />
        </div>

        <div className="form-group">
          <label>البريد الإلكتروني</label>
          <input 
            type="email" 
            placeholder="أدخل البريد الإلكتروني" 
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label>العنوان</label>
          <textarea 
            placeholder="أدخل العنوان"
            value={formData.address}
            onChange={(e) => handleChange('address', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label>الحد الائتماني</label>
          <input 
            type="number" 
            placeholder="أدخل الحد الائتماني" 
            value={formData.creditLimit}
            onChange={(e) => handleChange('creditLimit', parseFloat(e.target.value) || 0)}
          />
        </div>

        <div className="form-group">
          <label>الحالة</label>
          <select 
            value={formData.status}
            onChange={(e) => handleChange('status', e.target.value)}
          >
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
        </div>

        <div className="modal-footer">
          <button type="submit" className="save-btn">
            💾 {entity ? 'تحديث' : 'حفظ'}
          </button>
          <button type="button" className="cancel-btn" onClick={onCancel}>
            ❌ إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

// مكون نموذج الحساب
const AccountForm = ({ account, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    accountNumber: '',
    accountName: '',
    accountType: 'payable',
    currency: 'SAR',
    balance: 0
  });

  useEffect(() => {
    if (account) {
      setFormData({
        accountNumber: account.accountNumber || '',
        accountName: account.accountName || '',
        accountType: account.accountType || 'payable',
        currency: account.currency || 'SAR',
        balance: account.balance || 0
      });
    }
  }, [account]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.accountNumber || !formData.accountName) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }
    onSave(formData);
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="modal-body">
      <form onSubmit={handleSubmit} className="account-form">
        <div className="form-group">
          <label>رقم الحساب *</label>
          <input 
            type="text" 
            placeholder="أدخل رقم الحساب" 
            value={formData.accountNumber}
            onChange={(e) => handleChange('accountNumber', e.target.value)}
            required
          />
        </div>

        <div className="form-group">
          <label>اسم الحساب *</label>
          <input 
            type="text" 
            placeholder="أدخل اسم الحساب" 
            value={formData.accountName}
            onChange={(e) => handleChange('accountName', e.target.value)}
            required
          />
        </div>

        <div className="form-group">
          <label>نوع الحساب</label>
          <select 
            value={formData.accountType}
            onChange={(e) => handleChange('accountType', e.target.value)}
          >
            <option value="payable">حساب دائن</option>
            <option value="receivable">حساب مدين</option>
            <option value="prepaid">مصروفات مقدمة</option>
            <option value="asset">حساب أصل</option>
          </select>
        </div>

        <div className="form-group">
          <label>العملة</label>
          <select 
            value={formData.currency}
            onChange={(e) => handleChange('currency', e.target.value)}
          >
            <option value="SAR">ريال سعودي</option>
            <option value="USD">دولار أمريكي</option>
            <option value="EUR">يورو</option>
            <option value="GBP">جنيه إسترليني</option>
          </select>
        </div>

        <div className="form-group">
          <label>الرصيد الافتتاحي</label>
          <input 
            type="number" 
            placeholder="أدخل الرصيد الافتتاحي" 
            value={formData.balance}
            onChange={(e) => handleChange('balance', parseFloat(e.target.value) || 0)}
            step="0.01"
          />
        </div>

        <div className="modal-footer">
          <button type="submit" className="save-btn">
            💾 {account ? 'تحديث' : 'حفظ'} الحساب
          </button>
          <button type="button" className="cancel-btn" onClick={onCancel}>
            ❌ إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

// مكون نموذج المعاملة
const TransactionForm = ({ onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    description: '',
    debit: 0,
    credit: 0,
    reference: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.description || (formData.debit === 0 && formData.credit === 0)) {
      alert('يرجى ملء الوصف وإدخال مبلغ في المدين أو الدائن');
      return;
    }
    if (formData.debit > 0 && formData.credit > 0) {
      alert('لا يمكن إدخال مبلغ في المدين والدائن معاً');
      return;
    }
    onSave(formData);
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="modal-body">
      <form onSubmit={handleSubmit} className="transaction-form">
        <div className="form-group">
          <label>وصف المعاملة *</label>
          <input 
            type="text" 
            placeholder="أدخل وصف المعاملة" 
            value={formData.description}
            onChange={(e) => handleChange('description', e.target.value)}
            required
          />
        </div>

        <div className="form-group">
          <label>مبلغ مدين</label>
          <input 
            type="number" 
            placeholder="0.00" 
            value={formData.debit}
            onChange={(e) => handleChange('debit', parseFloat(e.target.value) || 0)}
            step="0.01"
            min="0"
          />
        </div>

        <div className="form-group">
          <label>مبلغ دائن</label>
          <input 
            type="number" 
            placeholder="0.00" 
            value={formData.credit}
            onChange={(e) => handleChange('credit', parseFloat(e.target.value) || 0)}
            step="0.01"
            min="0"
          />
        </div>

        <div className="form-group">
          <label>المرجع</label>
          <input 
            type="text" 
            placeholder="رقم الفاتورة أو المرجع" 
            value={formData.reference}
            onChange={(e) => handleChange('reference', e.target.value)}
          />
        </div>

        <div className="transaction-summary">
          <div className="summary-item">
            <span>نوع المعاملة:</span>
            <span className={formData.debit > 0 ? 'debit' : formData.credit > 0 ? 'credit' : 'neutral'}>
              {formData.debit > 0 ? 'مدين' : formData.credit > 0 ? 'دائن' : 'غير محدد'}
            </span>
          </div>
          <div className="summary-item">
            <span>المبلغ:</span>
            <span>{(formData.debit || formData.credit).toLocaleString()} ريال</span>
          </div>
        </div>

        <div className="modal-footer">
          <button type="submit" className="save-btn">
            💾 حفظ المعاملة
          </button>
          <button type="button" className="cancel-btn" onClick={onCancel}>
            ❌ إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

// مكون حسابات الوكلاء والموردين
export const AgentsSuppliers = ({ isLoading }) => {
  const [activeTab, setActiveTab] = useState('agents');
  const [selectedEntity, setSelectedEntity] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAccountForm, setShowAccountForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showTransactionForm, setShowTransactionForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [editingEntity, setEditingEntity] = useState(null);
  const [deletingEntity, setDeletingEntity] = useState(null);
  const [editingAccount, setEditingAccount] = useState(null);
  const [agentsData, setAgentsData] = useState([]);
  const [suppliersData, setSuppliersData] = useState([]);

  // تحميل البيانات التجريبية
  useEffect(() => {
    // بيانات تجريبية للوكلاء
    const initialAgentsData = [
    {
      id: 'AGT-001',
      name: 'وكالة الطيران الذهبي',
      type: 'airline_agent',
      contactPerson: 'أحمد محمد العلي',
      phone: '+************',
      email: '<EMAIL>',
      address: 'الرياض، حي الملك فهد',
      status: 'active',
      creditLimit: 500000,
      currentBalance: 125000,
      totalTransactions: 45,
      lastTransaction: '2024-02-10',
      accounts: [
        {
          id: 'ACC-AGT-001-001',
          accountNumber: '2110-001',
          accountName: 'وكالة الطيران الذهبي - حساب جاري',
          accountType: 'payable',
          balance: 125000,
          currency: 'SAR',
          isActive: true
        },
        {
          id: 'ACC-AGT-001-002',
          accountNumber: '1120-001',
          accountName: 'وكالة الطيران الذهبي - مدين',
          accountType: 'receivable',
          balance: 25000,
          currency: 'SAR',
          isActive: true
        }
      ],
      transactions: [
        {
          id: 'TXN-001',
          date: '2024-02-10',
          description: 'فاتورة تذاكر طيران - رحلة الرياض-دبي',
          debit: 0,
          credit: 45000,
          balance: 125000,
          reference: 'INV-2024-001'
        },
        {
          id: 'TXN-002',
          date: '2024-02-08',
          description: 'دفعة نقدية',
          debit: 25000,
          credit: 0,
          balance: 80000,
          reference: 'PAY-2024-001'
        }
      ]
    },
    {
      id: 'AGT-002',
      name: 'شركة الفنادق المتميزة',
      type: 'hotel_agent',
      contactPerson: 'فاطمة سالم أحمد',
      phone: '+************',
      email: '<EMAIL>',
      address: 'جدة، حي الروضة',
      status: 'active',
      creditLimit: 300000,
      currentBalance: 85000,
      totalTransactions: 32,
      lastTransaction: '2024-02-09',
      accounts: [
        {
          id: 'ACC-AGT-002-001',
          accountNumber: '2110-002',
          accountName: 'شركة الفنادق المتميزة - حساب جاري',
          accountType: 'payable',
          balance: 85000,
          currency: 'SAR',
          isActive: true
        }
      ],
      transactions: [
        {
          id: 'TXN-003',
          date: '2024-02-09',
          description: 'فاتورة حجوزات فندقية',
          debit: 0,
          credit: 35000,
          balance: 85000,
          reference: 'INV-2024-002'
        }
      ]
    }
    ];

    // بيانات تجريبية للموردين
    const initialSuppliersData = [
    {
      id: 'SUP-001',
      name: 'شركة النقل السريع',
      type: 'transport_supplier',
      contactPerson: 'محمد عبدالله الزهراني',
      phone: '+************',
      email: '<EMAIL>',
      address: 'الدمام، حي الفيصلية',
      status: 'active',
      creditLimit: 200000,
      currentBalance: 65000,
      totalTransactions: 28,
      lastTransaction: '2024-02-10',
      accounts: [
        {
          id: 'ACC-SUP-001-001',
          accountNumber: '2120-001',
          accountName: 'شركة النقل السريع - حساب جاري',
          accountType: 'payable',
          balance: 65000,
          currency: 'SAR',
          isActive: true
        },
        {
          id: 'ACC-SUP-001-002',
          accountNumber: '1140-001',
          accountName: 'شركة النقل السريع - مصروفات مقدمة',
          accountType: 'prepaid',
          balance: 15000,
          currency: 'SAR',
          isActive: true
        }
      ],
      transactions: [
        {
          id: 'TXN-004',
          date: '2024-02-10',
          description: 'فاتورة خدمات نقل',
          debit: 0,
          credit: 25000,
          balance: 65000,
          reference: 'INV-2024-003'
        }
      ]
    },
    {
      id: 'SUP-002',
      name: 'مكتب الخدمات السياحية',
      type: 'tourism_supplier',
      contactPerson: 'نورا خالد المطيري',
      phone: '+************',
      email: '<EMAIL>',
      address: 'مكة المكرمة، حي العزيزية',
      status: 'active',
      creditLimit: 150000,
      currentBalance: 42000,
      totalTransactions: 18,
      lastTransaction: '2024-02-08',
      accounts: [
        {
          id: 'ACC-SUP-002-001',
          accountNumber: '2120-002',
          accountName: 'مكتب الخدمات السياحية - حساب جاري',
          accountType: 'payable',
          balance: 42000,
          currency: 'SAR',
          isActive: true
        }
      ],
      transactions: [
        {
          id: 'TXN-005',
          date: '2024-02-08',
          description: 'فاتورة خدمات سياحية',
          debit: 0,
          credit: 18000,
          balance: 42000,
          reference: 'INV-2024-004'
        }
      ]
    }
    ];

    // تعيين البيانات
    setAgentsData(initialAgentsData);
    setSuppliersData(initialSuppliersData);
  }, []);

  const currentData = activeTab === 'agents' ? agentsData : suppliersData;

  const filteredData = useMemo(() => {
    return currentData.filter(entity => {
      const matchesSearch = entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           entity.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || entity.status === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [currentData, searchTerm, filterStatus]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusLabel = (status) => {
    return status === 'active' ? 'نشط' : 'غير نشط';
  };

  const getStatusColor = (status) => {
    return status === 'active' ? '#28a745' : '#dc3545';
  };

  const getTypeLabel = (type) => {
    const types = {
      airline_agent: 'وكيل طيران',
      hotel_agent: 'وكيل فنادق',
      transport_supplier: 'مورد نقل',
      tourism_supplier: 'مورد خدمات سياحية'
    };
    return types[type] || type;
  };

  const handleViewDetails = (entity) => {
    setSelectedEntity(entity);
  };

  const handleAddEntity = () => {
    setShowAddForm(true);
    setEditingEntity(null);
  };

  const handleEditEntity = (entity) => {
    setEditingEntity(entity);
    setShowEditForm(true);
  };

  const handleDeleteEntity = (entity) => {
    setDeletingEntity(entity);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteEntity = () => {
    if (deletingEntity) {
      if (activeTab === 'agents') {
        setAgentsData(prev => prev.filter(agent => agent.id !== deletingEntity.id));
      } else {
        setSuppliersData(prev => prev.filter(supplier => supplier.id !== deletingEntity.id));
      }
      setShowDeleteConfirm(false);
      setDeletingEntity(null);
      if (selectedEntity && selectedEntity.id === deletingEntity.id) {
        setSelectedEntity(null);
      }
    }
  };

  const handleSaveEntity = (entityData) => {
    if (editingEntity) {
      // تحديث كيان موجود
      if (activeTab === 'agents') {
        setAgentsData(prev => prev.map(agent => 
          agent.id === editingEntity.id ? { ...agent, ...entityData } : agent
        ));
      } else {
        setSuppliersData(prev => prev.map(supplier => 
          supplier.id === editingEntity.id ? { ...supplier, ...entityData } : supplier
        ));
      }
    } else {
      // إضافة كيان جديد
      const newEntity = {
        id: activeTab === 'agents' ? `AGT-${Date.now()}` : `SUP-${Date.now()}`,
        ...entityData,
        currentBalance: 0,
        totalTransactions: 0,
        lastTransaction: new Date().toISOString().split('T')[0],
        accounts: [],
        transactions: []
      };
      
      if (activeTab === 'agents') {
        setAgentsData(prev => [...prev, newEntity]);
      } else {
        setSuppliersData(prev => [...prev, newEntity]);
      }
    }
    setShowAddForm(false);
    setShowEditForm(false);
    setEditingEntity(null);
  };

  const handleAddAccount = (entityId) => {
    setShowAccountForm(true);
    setEditingAccount(null);
  };

  const handleEditAccount = (account) => {
    setEditingAccount(account);
    setShowAccountForm(true);
  };

  const handleDeleteAccount = (accountId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
      if (selectedEntity) {
        const updatedEntity = {
          ...selectedEntity,
          accounts: selectedEntity.accounts.filter(acc => acc.id !== accountId)
        };
        setSelectedEntity(updatedEntity);
        
        // تحديث البيانات الأساسية
        if (activeTab === 'agents') {
          setAgentsData(prev => prev.map(agent => 
            agent.id === selectedEntity.id ? updatedEntity : agent
          ));
        } else {
          setSuppliersData(prev => prev.map(supplier => 
            supplier.id === selectedEntity.id ? updatedEntity : supplier
          ));
        }
      }
    }
  };

  const handleSaveAccount = (accountData) => {
    if (selectedEntity) {
      let updatedAccounts;
      
      if (editingAccount) {
        // تحديث حساب موجود
        updatedAccounts = selectedEntity.accounts.map(acc => 
          acc.id === editingAccount.id ? { ...acc, ...accountData } : acc
        );
      } else {
        // إضافة حساب جديد
        const newAccount = {
          id: `ACC-${selectedEntity.id}-${Date.now()}`,
          ...accountData,
          isActive: true
        };
        updatedAccounts = [...selectedEntity.accounts, newAccount];
      }
      
      const updatedEntity = {
        ...selectedEntity,
        accounts: updatedAccounts
      };
      
      setSelectedEntity(updatedEntity);
      
      // تحديث البيانات الأساسية
      if (activeTab === 'agents') {
        setAgentsData(prev => prev.map(agent => 
          agent.id === selectedEntity.id ? updatedEntity : agent
        ));
      } else {
        setSuppliersData(prev => prev.map(supplier => 
          supplier.id === selectedEntity.id ? updatedEntity : supplier
        ));
      }
    }
    
    setShowAccountForm(false);
    setEditingAccount(null);
  };

  const handleAddTransaction = () => {
    setShowTransactionForm(true);
  };

  const handleSaveTransaction = (transactionData) => {
    if (selectedEntity) {
      const newTransaction = {
        id: `TXN-${Date.now()}`,
        date: new Date().toISOString().split('T')[0],
        ...transactionData
      };
      
      const updatedEntity = {
        ...selectedEntity,
        transactions: [newTransaction, ...selectedEntity.transactions],
        totalTransactions: selectedEntity.totalTransactions + 1,
        lastTransaction: newTransaction.date,
        currentBalance: selectedEntity.currentBalance + (transactionData.credit || 0) - (transactionData.debit || 0)
      };
      
      setSelectedEntity(updatedEntity);
      
      // تحديث البيانات الأساسية
      if (activeTab === 'agents') {
        setAgentsData(prev => prev.map(agent => 
          agent.id === selectedEntity.id ? updatedEntity : agent
        ));
      } else {
        setSuppliersData(prev => prev.map(supplier => 
          supplier.id === selectedEntity.id ? updatedEntity : supplier
        ));
      }
    }
    
    setShowTransactionForm(false);
  };

  // مكون قائمة الكيانات (الوكلاء أو الموردين)
  const EntitiesList = () => (
    <div className="entities-list">
      <div className="list-header">
        <h4>{activeTab === 'agents' ? 'قائمة الوكلاء' : 'قائمة الموردين'}</h4>
        <button 
          className="add-entity-btn"
          onClick={handleAddEntity}
        >
          ➕ إضافة {activeTab === 'agents' ? 'وكيل' : 'مورد'}
        </button>
      </div>

      {/* فلاتر البحث */}
      <div className="entities-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder={`البحث في ${activeTab === 'agents' ? 'الوكلاء' : 'الموردين'}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="status-filter">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
        </div>
      </div>

      {/* جدول الكيانات */}
      <div className="entities-table">
        <div className="table-header">
          <div>الاسم</div>
          <div>النوع</div>
          <div>الشخص المسؤول</div>
          <div>الرصيد الحالي</div>
          <div>الحد الائتماني</div>
          <div>الحالة</div>
          <div>إجراءات</div>
        </div>

        {filteredData.map(entity => (
          <div key={entity.id} className={`table-row ${entity.status}`}>
            <div className="entity-info">
              <div className="entity-name">{entity.name}</div>
              <div className="entity-contact">{entity.phone}</div>
              <div className="entity-id">ID: {entity.id}</div>
            </div>
            
            <div className="entity-type">
              <span className="type-badge">
                {getTypeLabel(entity.type)}
              </span>
            </div>
            
            <div className="contact-person">
              <div className="person-name">{entity.contactPerson}</div>
              <div className="person-email">{entity.email}</div>
            </div>
            
            <div className="current-balance">
              <span className={entity.currentBalance > 0 ? 'positive' : 'zero'}>
                {formatCurrency(entity.currentBalance)}
              </span>
            </div>
            
            <div className="credit-limit">
              {formatCurrency(entity.creditLimit)}
            </div>
            
            <div className="entity-status">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(entity.status) }}
              >
                {getStatusLabel(entity.status)}
              </span>
            </div>
            
            <div className="entity-actions">
              <button 
                className="action-btn view"
                title="عرض التفاصيل"
                onClick={() => handleViewDetails(entity)}
              >
                👁️
              </button>
              <button 
                className="action-btn accounts"
                title="إدارة الحسابات"
                onClick={() => handleViewDetails(entity)}
              >
                📊
              </button>
              <button 
                className="action-btn edit"
                title="تعديل"
                onClick={() => handleEditEntity(entity)}
              >
                ✏️
              </button>
              <button 
                className="action-btn delete"
                title="حذف"
                onClick={() => handleDeleteEntity(entity)}
              >
                🗑️
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // مكون تفاصيل الكيان المحدد
  const EntityDetails = () => {
    if (!selectedEntity) return null;

    return (
      <div className="entity-details">
        <div className="details-header">
          <div className="header-info">
            <h4>{selectedEntity.name}</h4>
            <span className="entity-type-badge">
              {getTypeLabel(selectedEntity.type)}
            </span>
          </div>
          <div className="header-actions">
            <button 
              className="add-account-btn"
              onClick={() => handleAddAccount(selectedEntity.id)}
            >
              ➕ إضافة حساب
            </button>
            <button 
              className="back-btn"
              onClick={() => setSelectedEntity(null)}
            >
              ← العودة للقائمة
            </button>
          </div>
        </div>

        {/* معلومات الكيان */}
        <div className="entity-info-card">
          <div className="info-grid">
            <div className="info-item">
              <label>الشخص المسؤول:</label>
              <span>{selectedEntity.contactPerson}</span>
            </div>
            <div className="info-item">
              <label>الهاتف:</label>
              <span>{selectedEntity.phone}</span>
            </div>
            <div className="info-item">
              <label>البريد الإلكتروني:</label>
              <span>{selectedEntity.email}</span>
            </div>
            <div className="info-item">
              <label>العنوان:</label>
              <span>{selectedEntity.address}</span>
            </div>
            <div className="info-item">
              <label>الرصيد الحالي:</label>
              <span className={selectedEntity.currentBalance > 0 ? 'positive' : 'zero'}>
                {formatCurrency(selectedEntity.currentBalance)}
              </span>
            </div>
            <div className="info-item">
              <label>الحد الائتماني:</label>
              <span>{formatCurrency(selectedEntity.creditLimit)}</span>
            </div>
            <div className="info-item">
              <label>عدد المعاملات:</label>
              <span>{selectedEntity.totalTransactions}</span>
            </div>
            <div className="info-item">
              <label>آخر معاملة:</label>
              <span>{selectedEntity.lastTransaction}</span>
            </div>
          </div>
        </div>

        {/* حسابات الكيان */}
        <div className="entity-accounts">
          <h5>الحسابات المحاسبية</h5>
          <div className="accounts-grid">
            {selectedEntity.accounts.map(account => (
              <div key={account.id} className="account-card">
                <div className="account-header">
                  <div className="account-number">{account.accountNumber}</div>
                  <div className="account-actions">
                    <button 
                      className="action-btn edit"
                      onClick={() => handleEditAccount(account)}
                      title="تعديل الحساب"
                    >
                      ✏️
                    </button>
                    <button 
                      className="action-btn delete"
                      onClick={() => handleDeleteAccount(account.id)}
                      title="حذف الحساب"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
                
                <div className="account-name">{account.accountName}</div>
                
                <div className="account-details">
                  <div className="detail-item">
                    <label>نوع الحساب:</label>
                    <span>{account.accountType === 'payable' ? 'دائن' : 
                           account.accountType === 'receivable' ? 'مدين' : 'أصل'}</span>
                  </div>
                  <div className="detail-item">
                    <label>الرصيد:</label>
                    <span className={account.balance > 0 ? 'positive' : 'zero'}>
                      {formatCurrency(account.balance)}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>العملة:</label>
                    <span>{account.currency}</span>
                  </div>
                  <div className="detail-item">
                    <label>آخر نشاط:</label>
                    <span>{account.lastActivity}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* معاملات الكيان */}
        <div className="entity-transactions">
          <div className="transactions-header">
            <h5>آخر المعاملات</h5>
            <button 
              className="add-transaction-btn"
              onClick={handleAddTransaction}
            >
              ➕ إضافة معاملة
            </button>
          </div>
          <div className="transactions-table">
            <div className="table-header">
              <div>التاريخ</div>
              <div>الوصف</div>
              <div>مدين</div>
              <div>دائن</div>
              <div>الرصيد</div>
              <div>المرجع</div>
            </div>

            {selectedEntity.transactions.map(transaction => (
              <div key={transaction.id} className="table-row">
                <div className="transaction-date">{transaction.date}</div>
                <div className="transaction-description">{transaction.description}</div>
                <div className="transaction-debit">
                  {transaction.debit > 0 ? formatCurrency(transaction.debit) : '-'}
                </div>
                <div className="transaction-credit">
                  {transaction.credit > 0 ? formatCurrency(transaction.credit) : '-'}
                </div>
                <div className="transaction-balance">
                  {formatCurrency(transaction.balance)}
                </div>
                <div className="transaction-reference">{transaction.reference}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل حسابات {activeTab === 'agents' ? 'الوكلاء' : 'الموردين'}...</p>
      </div>
    );
  }

  return (
    <div className="agents-suppliers">
      <div className="page-header">
        <h3>حسابات الوكلاء والموردين</h3>
      </div>

      {/* تبويبات الوكلاء والموردين */}
      <div className="main-tabs">
        <button 
          className={`tab-btn ${activeTab === 'agents' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('agents');
            setSelectedEntity(null);
          }}
        >
          🤝 الوكلاء
        </button>
        <button 
          className={`tab-btn ${activeTab === 'suppliers' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('suppliers');
            setSelectedEntity(null);
          }}
        >
          🏢 الموردين
        </button>
      </div>

      {/* محتوى الصفحة */}
      <div className="page-content">
        {selectedEntity ? <EntityDetails /> : <EntitiesList />}
      </div>

      {/* نموذج إضافة/تعديل كيان */}
      {(showAddForm || showEditForm) && (
        <div className="entity-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>
                {editingEntity ? 'تعديل' : 'إضافة'} {activeTab === 'agents' ? 'وكيل' : 'مورد'} 
                {editingEntity ? '' : ' جديد'}
              </h4>
              <button 
                className="close-btn"
                onClick={() => {
                  setShowAddForm(false);
                  setShowEditForm(false);
                  setEditingEntity(null);
                }}
              >
                ❌
              </button>
            </div>
            
            <EntityForm 
              entity={editingEntity}
              activeTab={activeTab}
              onSave={handleSaveEntity}
              onCancel={() => {
                setShowAddForm(false);
                setShowEditForm(false);
                setEditingEntity(null);
              }}
            />


          </div>
        </div>
      )}

      {/* نموذج إضافة/تعديل حساب */}
      {showAccountForm && (
        <div className="entity-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>{editingAccount ? 'تعديل' : 'إضافة'} حساب محاسبي</h4>
              <button 
                className="close-btn"
                onClick={() => {
                  setShowAccountForm(false);
                  setEditingAccount(null);
                }}
              >
                ❌
              </button>
            </div>
            
            <AccountForm 
              account={editingAccount}
              onSave={handleSaveAccount}
              onCancel={() => {
                setShowAccountForm(false);
                setEditingAccount(null);
              }}
            />
          </div>
        </div>
      )}

      {/* نموذج تأكيد الحذف */}
      {showDeleteConfirm && deletingEntity && (
        <div className="entity-modal">
          <div className="modal-content delete-confirm">
            <div className="modal-header">
              <h4>تأكيد الحذف</h4>
              <button 
                className="close-btn"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeletingEntity(null);
                }}
              >
                ❌
              </button>
            </div>
            
            <div className="modal-body">
              <div className="delete-warning">
                <div className="warning-icon">⚠️</div>
                <div className="warning-text">
                  <p>هل أنت متأكد من حذف {activeTab === 'agents' ? 'الوكيل' : 'المورد'}:</p>
                  <strong>{deletingEntity.name}</strong>
                  <p className="warning-note">
                    سيتم حذف جميع الحسابات والمعاملات المرتبطة بهذا {activeTab === 'agents' ? 'الوكيل' : 'المورد'}.
                    هذا الإجراء لا يمكن التراجع عنه.
                  </p>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="delete-btn" onClick={confirmDeleteEntity}>
                🗑️ تأكيد الحذف
              </button>
              <button 
                className="cancel-btn"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeletingEntity(null);
                }}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج إضافة معاملة */}
      {showTransactionForm && (
        <div className="entity-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>إضافة معاملة جديدة</h4>
              <button 
                className="close-btn"
                onClick={() => setShowTransactionForm(false)}
              >
                ❌
              </button>
            </div>
            
            <TransactionForm 
              onSave={handleSaveTransaction}
              onCancel={() => setShowTransactionForm(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentsSuppliers;