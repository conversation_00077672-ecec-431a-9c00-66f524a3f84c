import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './components/UI/ThemeProvider';
import { EnhancedNotificationsProvider } from './components/UI/EnhancedNotifications';
import { KeyboardShortcutsProvider } from './components/UI/KeyboardShortcuts';
import './styles/GlobalTheme.css';
import LoginPage from './pages/Auth/LoginPage';

import Layout from './components/Layout/Layout';
import ModernLayout from './components/Layout/ModernLayout';
import ModernSystemLayout from './components/Layout/ModernSystemLayout';
import Dashboard from './pages/Dashboard/Dashboard';
import BookingsPage from './pages/Bookings/BookingsPage';
import SalesPage from './pages/Sales/SalesPage';
import SalesManagementPage from './pages/Sales/SalesManagementPage';
import InvoicesPage from './pages/Sales/InvoicesPage';
import SalesCustomersPage from './pages/Sales/CustomersPage';
import SalesReportsPage from './pages/Sales/ReportsPage';
import PaymentsPage from './pages/Sales/PaymentsPage';
import SalesSettingsPage from './pages/Sales/SettingsPage';
import PurchasesPage from './pages/Purchases/PurchasesPage';
import InventoryPage from './pages/Inventory/InventoryPage';
import VisaInventoryPage from './pages/Inventory/VisaInventoryPage';
import FinancePage from './pages/Finance/FinancePage';
import TemplatesPage from './pages/Templates/TemplatesPage';
import CustomersPage from './pages/Customers/CustomersPage';
import AgentsPage from './pages/Agents/AgentsPage';
import SuppliersPage from './pages/Suppliers/SuppliersPageEnhancedEnhanced';
import ReportsPage from './pages/Reports/ReportsPage';
import SettingsPage from './pages/Settings/SettingsPage';
import AccountsPage from './pages/Accounts/AccountsPage';
import ComponentsDemo from './pages/Demo/ComponentsDemo';
import TestBookingComponents from './components/Bookings/TestBookingComponents';
import SimpleTest from './components/Bookings/SimpleTest';
import ErrorDiagnosis from './components/Bookings/ErrorDiagnosis';
import LandingPage from './pages/LandingPage/LandingPage';
import UsersManagementPage from './pages/Users/<USER>';
import RolesPermissionsPage from './pages/Users/<USER>';
import AdvancedUsersManagement from './pages/Users/<USER>';
import PermissionsMatrix from './pages/Users/<USER>';
import TestAdvanced from './pages/Users/<USER>';
import DiagnosticComponent from './DiagnosticComponent';
import UserSimpleTest from './pages/Users/<USER>';
import TestPage from './TestPage';
import TestNotifications from './components/Test/TestNotifications';
import TestVisaCustomerLink from './pages/Test/TestVisaCustomerLink';
import TestNotificationsPage from './pages/TestNotifications';
import SimpleNotificationTest from './pages/SimpleNotificationTest';
import DebugTest from './pages/DebugTest';
import NotificationSimpleTest from './pages/SimpleTest';
import UserProfile from './components/Profile/UserProfile';
import HelpCenter from './components/Help/HelpCenter';

// مكون للحماية - يتحقق من تسجيل الدخول
const ProtectedRoute = ({ children }) => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  
  return isAuthenticated ? (
    <ModernSystemLayout>{children}</ModernSystemLayout>
  ) : (
    <Navigate to="/login" replace />
  );
};

// صفحات مؤقتة للأقسام الأخرى (غير مستخدمة حالياً)
// const ComingSoonPage = ({ title, icon }) => (
//   <div style={{
//     display: 'flex',
//     flexDirection: 'column',
//     alignItems: 'center',
//     justifyContent: 'center',
//     height: '60vh',
//     textAlign: 'center',
//     fontFamily: 'Cairo, Arial, sans-serif'
//   }}>
//     <div style={{ fontSize: '80px', marginBottom: '20px' }}>{icon}</div>
//     <h1 style={{ color: '#2c3e50', marginBottom: '15px' }}>{title}</h1>
//     <p style={{ color: '#7f8c8d', fontSize: '18px', marginBottom: '30px' }}>
//       هذا القسم قيد التطوير وسيكون متاحاً قريباً
//     </p>
//     <div style={{
//       background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
//       color: 'white',
//       padding: '15px 30px',
//       borderRadius: '25px',
//       fontSize: '16px'
//     }}>
//       🚧 قيد التطوير
//     </div>
//   </div>
// );

function App() {
  return (
    <ThemeProvider>
      <EnhancedNotificationsProvider>
        <KeyboardShortcutsProvider>
          <div className="App">
            <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />
        <Route path="/test" element={<TestPage />} />
        <Route path="/login" element={<LoginPage />} />
        
        {/* Protected Routes */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        
        <Route path="/bookings" element={
          <ProtectedRoute>
            <BookingsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales" element={
          <ProtectedRoute>
            <SalesPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/management" element={
          <ProtectedRoute>
            <SalesManagementPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/invoices" element={
          <ProtectedRoute>
            <InvoicesPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/invoices/new" element={
          <ProtectedRoute>
            <InvoicesPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/customers" element={
          <ProtectedRoute>
            <SalesCustomersPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/reports" element={
          <ProtectedRoute>
            <SalesReportsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/payments" element={
          <ProtectedRoute>
            <PaymentsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/sales/settings" element={
          <ProtectedRoute>
            <SalesSettingsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/purchases" element={
          <ProtectedRoute>
            <PurchasesPage />
          </ProtectedRoute>
        } />
        
        <Route path="/inventory" element={
          <ProtectedRoute>
            <InventoryPage />
          </ProtectedRoute>
        } />
        
        <Route path="/inventory/visas" element={
          <ProtectedRoute>
            <VisaInventoryPage />
          </ProtectedRoute>
        } />
        
        <Route path="/finance" element={
          <ProtectedRoute>
            <FinancePage />
          </ProtectedRoute>
        } />
        
        <Route path="/templates" element={
          <ProtectedRoute>
            <TemplatesPage />
          </ProtectedRoute>
        } />
        
        <Route path="/customers" element={
          <ProtectedRoute>
            <CustomersPage />
          </ProtectedRoute>
        } />
        
        <Route path="/suppliers" element={
          <ProtectedRoute>
            <SuppliersPage />
          </ProtectedRoute>
        } />
        
        <Route path="/agents" element={
          <ProtectedRoute>
            <AgentsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/reports" element={
          <ProtectedRoute>
            <ReportsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/settings" element={
          <ProtectedRoute>
            <SettingsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/accounts" element={
          <ProtectedRoute>
            <AccountsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/users" element={
          <ProtectedRoute>
            <UsersManagementPage />
          </ProtectedRoute>
        } />
        
        <Route path="/roles-permissions" element={
          <ProtectedRoute>
            <RolesPermissionsPage />
          </ProtectedRoute>
        } />
        
        <Route path="/advanced-users" element={
          <ProtectedRoute>
            <AdvancedUsersManagement />
          </ProtectedRoute>
        } />
        
        <Route path="/permissions-matrix" element={
          <ProtectedRoute>
            <PermissionsMatrix />
          </ProtectedRoute>
        } />
        
        <Route path="/test-advanced" element={
          <ProtectedRoute>
            <TestAdvanced />
          </ProtectedRoute>
        } />
        
        <Route path="/diagnostic" element={
          <ProtectedRoute>
            <DiagnosticComponent />
          </ProtectedRoute>
        } />
        
        <Route path="/simple-test-users" element={
          <ProtectedRoute>
            <UserSimpleTest />
          </ProtectedRoute>
        } />
        
        <Route path="/demo" element={
          <ProtectedRoute>
            <ComponentsDemo />
          </ProtectedRoute>
        } />
        
        <Route path="/test-booking" element={
          <ProtectedRoute>
            <TestBookingComponents />
          </ProtectedRoute>
        } />
        
        <Route path="/simple-test" element={
          <ProtectedRoute>
            <SimpleTest />
          </ProtectedRoute>
        } />
        
        <Route path="/error-diagnosis" element={
          <ProtectedRoute>
            <ErrorDiagnosis />
          </ProtectedRoute>
        } />

        <Route path="/test-notifications" element={
          <ProtectedRoute>
            <TestNotifications />
          </ProtectedRoute>
        } />

        <Route path="/test-visa-customer-link" element={
          <ProtectedRoute>
            <TestVisaCustomerLink />
          </ProtectedRoute>
        } />

        <Route path="/test-notifications-enhanced" element={
          <ProtectedRoute>
            <TestNotificationsPage />
          </ProtectedRoute>
        } />

        {/* صفحة اختبار بسيطة بدون حماية */}
        <Route path="/simple-notification-test" element={<SimpleNotificationTest />} />

        {/* صفحة تشخيص المشاكل */}
        <Route path="/debug-test" element={<DebugTest />} />

        {/* اختبار بسيط للإشعارات */}
        <Route path="/notification-simple-test" element={<NotificationSimpleTest />} />

        {/* Profile and Help Pages */}
        <Route path="/profile" element={
          <ProtectedRoute>
            <UserProfile />
          </ProtectedRoute>
        } />
        
        <Route path="/help" element={
          <ProtectedRoute>
            <HelpCenter />
          </ProtectedRoute>
        } />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </KeyboardShortcutsProvider>
      </EnhancedNotificationsProvider>
    </ThemeProvider>
  );
}

export default App;