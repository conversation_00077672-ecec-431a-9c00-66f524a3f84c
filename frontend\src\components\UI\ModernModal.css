/* Modern Modal Components Styles */

/* ===== MODAL OVERLAY ===== */
.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

.modern-modal-overlay--open {
  opacity: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* ===== MODERN MODAL ===== */
.modern-modal {
  background: var(--neutral-0);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--neutral-200);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.95) translateY(20px);
  animation: modalSlideIn 0.3s ease-out forwards;
  outline: none;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.95) translateY(20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Modal Sizes */
.modern-modal--xs {
  width: 100%;
  max-width: 320px;
}

.modern-modal--sm {
  width: 100%;
  max-width: 480px;
}

.modern-modal--md {
  width: 100%;
  max-width: 640px;
}

.modern-modal--lg {
  width: 100%;
  max-width: 800px;
}

.modern-modal--xl {
  width: 100%;
  max-width: 1024px;
}

.modern-modal--2xl {
  width: 100%;
  max-width: 1280px;
}

.modern-modal--fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* Modal Variants */
.modern-modal--default {
  /* Default styling already applied */
}

.modern-modal--glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-modal--minimal {
  background: transparent;
  border: none;
  box-shadow: none;
}

.modern-modal--card {
  box-shadow: var(--shadow-2xl);
  border: none;
}

/* Modal Positioning */
.modern-modal--centered {
  /* Centered by flexbox in overlay */
}

/* ===== MODAL HEADER ===== */
.modern-modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--neutral-50);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.modern-modal-title-section {
  flex: 1;
  margin-left: var(--space-4);
}

.modern-modal-title {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  line-height: var(--leading-tight);
}

.modern-modal-subtitle {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
}

.modern-modal-close {
  color: var(--neutral-500);
  flex-shrink: 0;
}

.modern-modal-close:hover {
  color: var(--neutral-700);
}

/* ===== MODAL BODY ===== */
.modern-modal-body {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
  overflow-x: hidden;
}

/* ===== MODAL WITH FOOTER ===== */
.modern-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modern-modal-main {
  flex: 1;
  overflow-y: auto;
}

.modern-modal-footer {
  padding: var(--space-4) var(--space-6) var(--space-6) var(--space-6);
  border-top: 1px solid var(--neutral-200);
  background: var(--neutral-50);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
}

.modern-modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* ===== CONFIRMATION MODAL ===== */
.confirmation-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--space-4);
  padding: var(--space-4) 0;
}

.confirmation-modal-icon {
  font-size: var(--text-6xl);
  opacity: 0.8;
}

.confirmation-modal-message {
  font-size: var(--text-base);
  color: var(--neutral-700);
  line-height: var(--leading-relaxed);
  max-width: 400px;
}

/* ===== FORM MODAL ===== */
.form-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-2) 0;
}

.form-modal-footer {
  padding-top: var(--space-4);
  border-top: 1px solid var(--neutral-200);
  margin-top: var(--space-4);
}

.form-modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* ===== IMAGE MODAL ===== */
.image-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.image-modal-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.image-modal-description {
  text-align: center;
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
}

/* ===== DRAWER ===== */
.modern-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease-out;
}

.modern-drawer {
  position: fixed;
  background: var(--neutral-0);
  box-shadow: var(--shadow-2xl);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-out;
}

.modern-drawer--open {
  transform: translateX(0);
}

/* Drawer Positions */
.modern-drawer--right {
  top: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-2xl) 0 0 var(--radius-2xl);
}

.modern-drawer--left {
  top: 0;
  left: 0;
  bottom: 0;
  border-radius: 0 var(--radius-2xl) var(--radius-2xl) 0;
  transform: translateX(-100%);
}

.modern-drawer--left.modern-drawer--open {
  transform: translateX(0);
}

/* Drawer Sizes */
.modern-drawer--xs {
  width: 280px;
}

.modern-drawer--sm {
  width: 320px;
}

.modern-drawer--md {
  width: 400px;
}

.modern-drawer--lg {
  width: 500px;
}

.modern-drawer--xl {
  width: 600px;
}

.modern-drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

.modern-drawer-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.modern-drawer-close {
  color: var(--neutral-500);
}

.modern-drawer-body {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* ===== BOTTOM SHEET ===== */
.modern-bottom-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  z-index: var(--z-modal);
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease-out;
}

.modern-bottom-sheet {
  width: 100%;
  background: var(--neutral-0);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  box-shadow: var(--shadow-2xl);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.modern-bottom-sheet--open {
  transform: translateY(0);
}

.modern-bottom-sheet-handle {
  width: 40px;
  height: 4px;
  background: var(--neutral-300);
  border-radius: var(--radius-full);
  margin: var(--space-3) auto var(--space-2) auto;
  cursor: grab;
}

.modern-bottom-sheet-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
}

.modern-bottom-sheet-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
}

.modern-bottom-sheet-body {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .modern-modal-overlay {
    padding: var(--space-2);
  }
  
  .modern-modal--xs,
  .modern-modal--sm,
  .modern-modal--md,
  .modern-modal--lg,
  .modern-modal--xl,
  .modern-modal--2xl {
    width: 100%;
    max-width: none;
    margin: 0;
  }
  
  .modern-modal {
    max-height: 95vh;
  }
  
  .modern-modal-header {
    padding: var(--space-4);
  }
  
  .modern-modal-body {
    padding: var(--space-4);
  }
  
  .modern-modal-footer {
    padding: var(--space-3) var(--space-4) var(--space-4) var(--space-4);
  }
  
  .modern-modal-actions {
    flex-direction: column-reverse;
    gap: var(--space-2);
  }
  
  .form-modal-actions {
    flex-direction: column-reverse;
    gap: var(--space-2);
  }
  
  .modern-drawer--xs,
  .modern-drawer--sm,
  .modern-drawer--md,
  .modern-drawer--lg,
  .modern-drawer--xl {
    width: 100vw;
    max-width: 400px;
  }
  
  .modern-drawer-header {
    padding: var(--space-4);
  }
  
  .modern-drawer-body {
    padding: var(--space-4);
  }
  
  .modern-bottom-sheet-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .modern-bottom-sheet-body {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .modern-modal-overlay {
    padding: var(--space-1);
  }
  
  .modern-modal {
    border-radius: var(--radius-xl);
    max-height: 98vh;
  }
  
  .modern-modal-header {
    padding: var(--space-3);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .modern-modal-title-section {
    margin-left: 0;
  }
  
  .modern-modal-close {
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
  }
  
  .modern-modal-body {
    padding: var(--space-3);
  }
  
  .modern-modal-footer {
    padding: var(--space-3);
  }
  
  .modern-modal-title {
    font-size: var(--text-lg);
  }
  
  .confirmation-modal-icon {
    font-size: var(--text-4xl);
  }
  
  .modern-drawer--xs,
  .modern-drawer--sm,
  .modern-drawer--md,
  .modern-drawer--lg,
  .modern-drawer--xl {
    width: 100vw;
  }
  
  .modern-drawer {
    border-radius: 0;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-modal {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .modern-modal-header,
  .modern-modal-footer {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .modern-modal-title {
    color: var(--neutral-200);
  }
  
  .modern-modal-subtitle {
    color: var(--neutral-400);
  }
  
  .modern-modal-close {
    color: var(--neutral-400);
  }
  
  .modern-modal-close:hover {
    color: var(--neutral-200);
  }
  
  .confirmation-modal-message {
    color: var(--neutral-300);
  }
  
  .image-modal-description {
    color: var(--neutral-400);
  }
  
  .modern-drawer {
    background: var(--neutral-800);
  }
  
  .modern-drawer-header {
    background: var(--neutral-700);
    border-bottom-color: var(--neutral-600);
  }
  
  .modern-drawer-title {
    color: var(--neutral-200);
  }
  
  .modern-drawer-close {
    color: var(--neutral-400);
  }
  
  .modern-bottom-sheet {
    background: var(--neutral-800);
  }
  
  .modern-bottom-sheet-header {
    border-bottom-color: var(--neutral-600);
  }
  
  .modern-bottom-sheet-title {
    color: var(--neutral-200);
  }
  
  .modern-bottom-sheet-handle {
    background: var(--neutral-600);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-modal-overlay,
  .modern-drawer-overlay,
  .modern-bottom-sheet-overlay {
    display: none !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-modal-overlay {
    animation: none !important;
  }
  
  .modern-modal {
    animation: none !important;
    transform: none !important;
  }
  
  .modern-drawer {
    transition: none !important;
    transform: none !important;
  }
  
  .modern-bottom-sheet {
    transition: none !important;
    transform: none !important;
  }
  
  .modern-drawer-overlay,
  .modern-bottom-sheet-overlay {
    animation: none !important;
  }
}

/* Focus management */
.modern-modal:focus {
  outline: none;
}

.modern-modal-close:focus,
.modern-drawer-close:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-modal,
  .modern-drawer,
  .modern-bottom-sheet {
    border: 2px solid currentColor !important;
  }
  
  .modern-modal-header,
  .modern-modal-footer,
  .modern-drawer-header,
  .modern-bottom-sheet-header {
    border: 1px solid currentColor !important;
  }
}