import React from 'react';
import { formatCurrency, getPaymentStatusColor, getPaymentStatusText } from '../../utils/bookingHelpers';

// مكون ملخص الدفع المحسن
const PaymentSummary = ({ 
  amount, 
  paidAmount, 
  currency = 'SAR', 
  paymentStatus,
  showDetails = true,
  layout = 'vertical' // vertical, horizontal
}) => {
  const totalAmount = parseFloat(amount) || 0;
  const paid = parseFloat(paidAmount) || 0;
  const remaining = Math.max(0, totalAmount - paid);
  const paymentPercentage = totalAmount > 0 ? (paid / totalAmount) * 100 : 0;

  const getPaymentIcon = (status) => {
    const icons = {
      'paid': '💰',
      'unpaid': '💳',
      'partial': '📊',
      'refunded': '↩️'
    };
    return icons[status] || '💳';
  };

  const containerStyle = {
    display: 'flex',
    flexDirection: layout === 'vertical' ? 'column' : 'row',
    gap: layout === 'vertical' ? '4px' : '8px',
    alignItems: layout === 'horizontal' ? 'center' : 'flex-start'
  };

  return (
    <div style={containerStyle}>
      {/* المبلغ الإجمالي */}
      <div style={{ 
        fontWeight: 'bold', 
        fontSize: layout === 'horizontal' ? '13px' : '14px',
        color: '#2c3e50'
      }}>
        {formatCurrency(totalAmount, currency)}
      </div>

      {showDetails && (
        <>
          {/* حالة الدفع */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            fontSize: '11px'
          }}>
            <span>{getPaymentIcon(paymentStatus)}</span>
            <span style={{
              color: getPaymentStatusColor(paymentStatus),
              fontWeight: 'bold'
            }}>
              {getPaymentStatusText(paymentStatus)}
            </span>
          </div>

          {/* تفاصيل الدفع */}
          {paymentStatus === 'partial' && (
            <div style={{ fontSize: '10px', color: '#7f8c8d' }}>
              <div>مدفوع: {formatCurrency(paid, currency)}</div>
              <div style={{ color: '#e74c3c' }}>
                متبقي: {formatCurrency(remaining, currency)}
              </div>
            </div>
          )}

          {/* شريط التقدم للدفع الجزئي */}
          {paymentStatus === 'partial' && layout === 'vertical' && (
            <div style={{
              width: '100%',
              height: '4px',
              backgroundColor: '#ecf0f1',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${paymentPercentage}%`,
                height: '100%',
                backgroundColor: getPaymentStatusColor(paymentStatus),
                transition: 'width 0.3s ease'
              }} />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default PaymentSummary;