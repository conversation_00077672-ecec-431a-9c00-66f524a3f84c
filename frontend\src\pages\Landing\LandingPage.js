import React from 'react';
import { useNavigate } from 'react-router-dom';

const LandingPage = () => {
  const navigate = useNavigate();

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif'
    }}>
      {/* Header */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        background: 'rgba(255,255,255,0.95)',
        color: '#333',
        padding: '15px 20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        zIndex: 1000,
        backdropFilter: 'blur(10px)'
      }}>
        <h3 style={{ margin: 0 }}>🧳 نظام محاسبي لوكالات السفريات</h3>
        <div>
          <button 
            onClick={() => navigate('/login')}
            style={{
              background: '#1976d2',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '8px',
              marginLeft: '10px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            تسجيل الدخول
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ marginTop: '100px', maxWidth: '800px' }}>
        <h1 style={{ 
          fontSize: '3rem', 
          marginBottom: '20px',
          fontWeight: 'bold'
        }}>
          نظام محاسبي متكامل
          <br />
          <span style={{ color: '#FFD700' }}>لوكالات السفريات</span>
        </h1>
        
        <p style={{ 
          fontSize: '1.5rem', 
          marginBottom: '40px',
          opacity: 0.9
        }}>
          إدارة شاملة للحجوزات والمحاسبة والعملاء مع تقارير ذكية وأمان عالي
        </p>
        
        <div style={{ display: 'flex', gap: '20px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            onClick={() => navigate('/login')}
            style={{
              background: 'white',
              color: '#1976d2',
              border: 'none',
              padding: '15px 30px',
              borderRadius: '8px',
              fontSize: '1.2rem',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            ابدأ الآن
          </button>
          <button
            onClick={() => navigate('/login')}
            style={{
              background: 'transparent',
              color: 'white',
              border: '2px solid white',
              padding: '15px 30px',
              borderRadius: '8px',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}
          >
            تسجيل الدخول
          </button>
        </div>
      </div>

      {/* Features */}
      <div style={{ 
        marginTop: '80px',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '30px',
        maxWidth: '1000px',
        width: '100%'
      }}>
        {[
          { icon: '💰', title: 'محاسبة متقدمة', desc: 'نظام محاسبي شامل مع تقارير مالية' },
          { icon: '✈️', title: 'إدارة الحجوزات', desc: 'إدارة حجوزات الطيران والفنادق' },
          { icon: '👥', title: 'إدارة العملاء', desc: 'قاعدة بيانات شاملة للعملاء' },
          { icon: '📊', title: 'تقارير ذكية', desc: 'تقارير مالية وإحصائية تفاعلية' }
        ].map((feature, index) => (
          <div key={index} style={{
            background: 'rgba(255,255,255,0.1)',
            padding: '30px',
            borderRadius: '15px',
            backdropFilter: 'blur(10px)',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '15px' }}>{feature.icon}</div>
            <h3 style={{ marginBottom: '10px' }}>{feature.title}</h3>
            <p style={{ opacity: 0.8 }}>{feature.desc}</p>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div style={{ 
        marginTop: '80px',
        padding: '40px 0',
        borderTop: '1px solid rgba(255,255,255,0.2)',
        width: '100%',
        textAlign: 'center'
      }}>
        <p style={{ opacity: 0.6 }}>
          © 2024 نظام محاسبي لوكالات السفريات. جميع الحقوق محفوظة.
        </p>
      </div>
    </div>
  );
};

export default LandingPage;