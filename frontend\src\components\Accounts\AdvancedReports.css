/* Advanced Reports Styles */

.advanced-reports {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.reports-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.export-btn,
.print-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.export-btn {
  background: #28a745;
  color: white;
}

.export-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.print-btn {
  background: #6c757d;
  color: white;
}

.print-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Reports Tabs */
.reports-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 25px;
  background: #f8f9fa;
  padding: 5px;
  border-radius: 12px;
}

.reports-tabs .tab-btn {
  flex: 1;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  text-align: center;
}

.reports-tabs .tab-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #495057;
}

.reports-tabs .tab-btn.active {
  background: white;
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

/* Period Settings */
.period-settings {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.form-group input {
  padding: 10px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.refresh-btn {
  padding: 10px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Reports Content */
.reports-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

/* Aging Report */
.aging-report h4,
.profitability-report h4,
.cash-flow-report h4,
.ratios-report h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e8ed;
}

.aging-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.aging-summary .summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #e1e8ed;
  transition: all 0.3s ease;
}

.aging-summary .summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.aging-summary .summary-item.current {
  border-left-color: #28a745;
}

.aging-summary .summary-item.days30 {
  border-left-color: #ffc107;
}

.aging-summary .summary-item.days60 {
  border-left-color: #fd7e14;
}

.aging-summary .summary-item.over90 {
  border-left-color: #dc3545;
}

.aging-summary .label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.aging-summary .value {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
}

.aging-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.aging-table .table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #667eea;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.aging-table .table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.aging-table .table-row:hover {
  background: #f8f9fa;
}

.aging-table .table-footer {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #2c3e50;
  color: white;
  font-weight: 700;
  font-size: 14px;
}

.customer-name {
  font-weight: 600;
  color: #2c3e50;
}

.total-balance,
.current,
.days30,
.days60,
.over90 {
  text-align: right;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Profitability Report */
.profitability-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.profitability-summary .summary-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.profitability-summary .summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profitability-summary .summary-card h5 {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profitability-summary .summary-card .value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.profitability-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.profitability-table .table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #28a745;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.profitability-table .table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.profitability-table .table-row:hover {
  background: #f8f9fa;
}

.profitability-table .table-footer {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #2c3e50;
  color: white;
  font-weight: 700;
  font-size: 14px;
}

.service-name {
  font-weight: 600;
  color: #2c3e50;
}

.revenue,
.cost,
.gross-profit,
.margin {
  text-align: right;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Cash Flow Report */
.cash-flow-sections {
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin-bottom: 25px;
}

.cash-flow-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #e1e8ed;
}

.cash-flow-section.operating {
  border-left-color: #28a745;
}

.cash-flow-section.investing {
  border-left-color: #ffc107;
}

.cash-flow-section.financing {
  border-left-color: #17a2b8;
}

.cash-flow-section h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.flow-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.flow-item:last-of-type {
  border-bottom: none;
  margin-bottom: 10px;
}

.flow-total {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-top: 2px solid #2c3e50;
  font-weight: 700;
  font-size: 16px;
  color: #2c3e50;
}

.net-cash-flow {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 20px;
}

.net-flow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  font-weight: 700;
}

.net-flow-item .positive {
  color: #d4edda;
}

.net-flow-item .negative {
  color: #f8d7da;
}

/* Financial Ratios Report */
.ratios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.ratio-category {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #e1e8ed;
}

.ratio-category h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e8ed;
}

.ratio-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 10px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.ratio-item:hover {
  transform: translateX(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.ratio-item:last-child {
  margin-bottom: 0;
}

.ratio-value {
  font-weight: 700;
  color: #2c3e50;
  font-size: 16px;
  font-family: 'Courier New', monospace;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .aging-table .table-header,
  .aging-table .table-row,
  .aging-table .table-footer {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .profitability-table .table-header,
  .profitability-table .table-row,
  .profitability-table .table-footer {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .ratios-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .advanced-reports {
    padding: 15px;
  }
  
  .reports-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
  
  .reports-tabs {
    flex-direction: column;
  }
  
  .period-settings {
    flex-direction: column;
    align-items: stretch;
  }
  
  .aging-summary,
  .profitability-summary {
    grid-template-columns: 1fr;
  }
  
  .cash-flow-sections {
    gap: 15px;
  }
  
  .net-flow-item {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .reports-header h3 {
    font-size: 20px;
  }
  
  .aging-report h4,
  .profitability-report h4,
  .cash-flow-report h4,
  .ratios-report h4 {
    font-size: 18px;
  }
  
  .aging-summary .summary-item,
  .ratio-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .flow-item,
  .flow-total {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}

/* Print Styles */
@media print {
  .advanced-reports {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .reports-header {
    border-bottom: 2px solid #000;
    margin-bottom: 20px;
  }
  
  .header-actions,
  .reports-tabs,
  .period-settings {
    display: none !important;
  }
  
  .aging-table,
  .profitability-table {
    border: 2px solid #000;
  }
  
  .aging-table .table-header,
  .profitability-table .table-header {
    background: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .aging-table .table-footer,
  .profitability-table .table-footer {
    background: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .cash-flow-section,
  .ratio-category {
    break-inside: avoid;
    border: 1px solid #000;
  }
  
  .net-cash-flow {
    background: #f0f0f0 !important;
    color: #000 !important;
    border: 2px solid #000;
  }
}