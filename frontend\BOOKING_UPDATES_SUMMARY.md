# ملخص تحديثات صفحات الحجوزات

## ✅ ما تم إنجازه

### 1. إنشاء مكونات العمليات الجديدة
- **BookingActions.js**: مكون أزرار العمليات (عرض، تعديل، حذف، طباعة، PDF)
- **BookingActions.css**: تنسيقات الأزرار
- **utils/printService.js**: خدمات الطباعة وحفظ PDF

### 2. الصفحات المحدثة بالكامل ✅
- **FlightBookingPage.js** (صفحة الطيران) ✅
- **HajjBookingPage.js** (صفحة الحج) ✅  
- **CarBookingPage.js** (صفحة السيارات) ✅

### 3. الميزات المضافة
- ✅ أزرار عرض التفاصيل مع نافذة منبثقة
- ✅ أزرار التعديل (تفتح نموذج التعديل)
- ✅ أزرار الحذف مع تأكيد
- ✅ أزرار الطباعة الفردية
- ✅ أزرار حفظ PDF الفردي
- ✅ عمود التحديد للعمليات المجمعة
- ✅ أزرار العمليات المجمعة (طباعة، PDF، حذف)
- ✅ تحديد الكل / إلغاء التحديد

## 🔄 الصفحات المتبقية للتحديث

### صفحات تحتاج تحديث سريع:
1. **UmrahBookingPage.js** (العمرة) - تم إضافة الاستيرادات فقط
2. **PassportBookingPage.js** (الجوازات) - لم يتم التحديث
3. **BusBookingPage.js** (الباصات) - لم يتم التحديث  
4. **DocumentAuthenticationPage.js** (تعميد الوثائق) - لم يتم التحديث

## 📋 خطوات التحديث للصفحات المتبقية

لكل صفحة من الصفحات المتبقية، يجب تطبيق الخطوات التالية:

### 1. إضافة الاستيرادات
```javascript
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';
```

### 2. إضافة متغيرات الحالة
```javascript
const [selectedBookings, setSelectedBookings] = useState([]);
const [showDetailsModal, setShowDetailsModal] = useState(false);
const [selectedBooking, setSelectedBooking] = useState(null);
const [showEditForm, setShowEditForm] = useState(false);
```

### 3. إضافة الدوال (استبدال BOOKING_TYPE بنوع الحجز)
```javascript
// دوال العمليات الجديدة
const handleViewBooking = (booking) => {
  setSelectedBooking(booking);
  setShowDetailsModal(true);
};

const handleEditBooking = (booking) => {
  setSelectedBooking(booking);
  setNewBooking(booking);
  setShowEditForm(true);
};

const handleDeleteSingleBooking = (booking) => {
  handleDeleteBooking(booking.id);
};

const handlePrintBooking = (booking) => {
  printBooking(booking, 'BOOKING_TYPE');
};

const handleSavePDFBooking = (booking) => {
  savePDF(booking, 'BOOKING_TYPE');
};

// دوال العمليات المجمعة
const handleSelectAll = () => {
  setSelectedBookings(filteredBookings.map(booking => booking.id));
};

const handleClearSelection = () => {
  setSelectedBookings([]);
};

const handleBulkPrint = (selectedIds) => {
  const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
  printMultipleBookings(selectedBookingData, 'BOOKING_TYPE');
};

const handleBulkSavePDF = (selectedIds) => {
  const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
  saveMultiplePDF(selectedBookingData, 'BOOKING_TYPE');
};

const handleBulkDelete = (selectedIds) => {
  if (window.confirm(`هل أنت متأكد من حذف ${selectedIds.length} حجز؟`)) {
    setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
    setSelectedBookings([]);
  }
};

const handleBookingSelection = (bookingId, isSelected) => {
  if (isSelected) {
    setSelectedBookings([...selectedBookings, bookingId]);
  } else {
    setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
  }
};
```

### 4. تحديث رأس الجدول
إضافة عمود التحديد كأول عمود:
```javascript
<th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
  <input
    type="checkbox"
    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
    style={{ cursor: 'pointer' }}
  />
</th>
```

### 5. تحديث صفوف الجدول
إضافة عمود التحديد كأول عمود في كل صف:
```javascript
<td style={{ padding: '15px', textAlign: 'center' }}>
  <input
    type="checkbox"
    checked={selectedBookings.includes(booking.id)}
    onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
    style={{ cursor: 'pointer' }}
  />
</td>
```

### 6. استبدال أزرار الإجراءات
استبدال الأزرار القديمة بـ:
```javascript
<BookingActions
  booking={booking}
  onView={handleViewBooking}
  onEdit={handleEditBooking}
  onDelete={handleDeleteSingleBooking}
  onPrint={handlePrintBooking}
  onSavePDF={handleSavePDFBooking}
/>
```

### 7. إضافة مكون العمليات المجمعة
قبل الجدول مباشرة:
```javascript
<BulkActions
  selectedBookings={selectedBookings}
  onBulkPrint={handleBulkPrint}
  onBulkSavePDF={handleBulkSavePDF}
  onBulkDelete={handleBulkDelete}
  onSelectAll={handleSelectAll}
  onClearSelection={handleClearSelection}
/>
```

### 8. إضافة نافذة التفاصيل
قبل إغلاق المكون:
```javascript
<BookingDetailsModal
  booking={selectedBooking}
  isOpen={showDetailsModal}
  onClose={() => setShowDetailsModal(false)}
  onPrint={handlePrintBooking}
  onSavePDF={handleSavePDFBooking}
/>
```

## 🎯 أنواع الحجوزات لكل صفحة
- **UmrahBookingPage.js**: 'العمرة'
- **PassportBookingPage.js**: 'الجوازات'
- **BusBookingPage.js**: 'الباصات'
- **DocumentAuthenticationPage.js**: 'تعميد الوثائق'

## ✅ الحالة الحالية
- ✅ المكونات الأساسية جاهزة ومختبرة
- ✅ خدمات الطباعة و PDF جاهزة
- ✅ 3 صفحات محدثة بالكامل
- 🔄 4 صفحات تحتاج تحديث سريع

## 🚀 الخطوة التالية
تطبيق نفس التحديثات على الصفحات الأربع المتبقية باستخدام الخطوات المذكورة أعلاه.