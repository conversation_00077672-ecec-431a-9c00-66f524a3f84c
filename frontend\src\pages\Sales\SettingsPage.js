import React, { useState } from 'react';

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState('company');
  const [loading, setLoading] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  // إعدادات الشركة
  const [companySettings, setCompanySettings] = useState({
    name: 'شركة شراء السفر والسياحة',
    nameEn: 'Sharau Travel & Tourism Company',
    taxNumber: '300123456789003',
    commercialRegister: '1010123456',
    phone: '+966112345678',
    email: '<EMAIL>',
    website: 'www.sharautravel.com',
    address: 'الرياض، حي الملك فهد، شارع العليا',
    city: 'الرياض',
    country: 'السعودية',
    postalCode: '11564',
    logo: null
  });

  // إعدادات الفواتير
  const [invoiceSettings, setInvoiceSettings] = useState({
    prefix: 'INV',
    startNumber: 1,
    currentNumber: 125,
    taxRate: 15,
    currency: 'SAR',
    paymentTerms: 30,
    showTax: true,
    showDiscount: true,
    autoCalculateTax: true,
    defaultPaymentMethod: 'cash',
    invoiceTemplate: 'modern',
    footerText: 'شكراً لتعاملكم معنا - نتطلع لخدمتكم مرة أخرى'
  });

  // إعدادات النظام
  const [systemSettings, setSystemSettings] = useState({
    language: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: 'arabic',
    backupFrequency: 'daily',
    autoBackup: true,
    emailNotifications: true,
    smsNotifications: false,
    lowStockAlert: true,
    lowStockThreshold: 10,
    sessionTimeout: 60
  });

  // إعدادات المدفوعات
  const [paymentSettings, setPaymentSettings] = useState({
    acceptCash: true,
    acceptCard: true,
    acceptBank: true,
    acceptCheck: false,
    defaultPaymentMethod: 'cash',
    requirePaymentConfirmation: true,
    allowPartialPayments: true,
    lateFeePercentage: 2,
    gracePeriodDays: 7
  });

  const handleSave = async (settingsType) => {
    setLoading(true);
    setSaveMessage('');
    
    // محاكاة حفظ البيانات
    setTimeout(() => {
      setLoading(false);
      setSaveMessage(`تم حفظ ${getTabName(settingsType)} بنجاح!`);
      setTimeout(() => setSaveMessage(''), 3000);
    }, 1000);
  };

  const getTabName = (tab) => {
    switch(tab) {
      case 'company': return 'إعدادات الشركة';
      case 'invoice': return 'إعدادات الفواتير';
      case 'system': return 'إعدادات النظام';
      case 'payment': return 'إعدادات المدفوعات';
      default: return 'الإعدادات';
    }
  };

  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setCompanySettings({...companySettings, logo: e.target.result});
      };
      reader.readAsDataURL(file);
    }
  };

  const renderCompanySettings = () => (
    <div style={{ background: 'white', padding: '25px', borderRadius: '12px', boxShadow: '0 5px 15px rgba(0,0,0,0.1)' }}>
      <h3 style={{ color: '#2c3e50', marginBottom: '25px', fontSize: '20px' }}>🏢 معلومات الشركة</h3>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
        {/* الشعار */}
        <div style={{ gridColumn: '1 / -1', textAlign: 'center', marginBottom: '20px' }}>
          <div style={{ 
            width: '150px', 
            height: '150px', 
            border: '3px dashed #ddd', 
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 15px',
            background: companySettings.logo ? `url(${companySettings.logo})` : '#f8f9fa',
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}>
            {!companySettings.logo && (
              <div style={{ textAlign: 'center', color: '#666' }}>
                <div style={{ fontSize: '40px', marginBottom: '10px' }}>🏢</div>
                <div style={{ fontSize: '14px' }}>شعار الشركة</div>
              </div>
            )}
          </div>
          <input
            type="file"
            accept="image/*"
            onChange={handleLogoUpload}
            style={{ display: 'none' }}
            id="logo-upload"
          />
          <label
            htmlFor="logo-upload"
            style={{
              background: '#3498db',
              color: 'white',
              padding: '10px 20px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              border: 'none'
            }}
          >
            📤 رفع الشعار
          </label>
        </div>

        {/* اسم الشركة */}
        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            اسم الشركة (عربي)
          </label>
          <input
            type="text"
            value={companySettings.name}
            onChange={(e) => setCompanySettings({...companySettings, name: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            اسم الشركة (إنجليزي)
          </label>
          <input
            type="text"
            value={companySettings.nameEn}
            onChange={(e) => setCompanySettings({...companySettings, nameEn: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            الرقم الضريبي
          </label>
          <input
            type="text"
            value={companySettings.taxNumber}
            onChange={(e) => setCompanySettings({...companySettings, taxNumber: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            السجل التجاري
          </label>
          <input
            type="text"
            value={companySettings.commercialRegister}
            onChange={(e) => setCompanySettings({...companySettings, commercialRegister: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            رقم الهاتف
          </label>
          <input
            type="tel"
            value={companySettings.phone}
            onChange={(e) => setCompanySettings({...companySettings, phone: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            البريد الإلكتروني
          </label>
          <input
            type="email"
            value={companySettings.email}
            onChange={(e) => setCompanySettings({...companySettings, email: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            الموقع الإلكتروني
          </label>
          <input
            type="url"
            value={companySettings.website}
            onChange={(e) => setCompanySettings({...companySettings, website: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            المدينة
          </label>
          <input
            type="text"
            value={companySettings.city}
            onChange={(e) => setCompanySettings({...companySettings, city: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div style={{ gridColumn: '1 / -1' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            العنوان التفصيلي
          </label>
          <textarea
            value={companySettings.address}
            onChange={(e) => setCompanySettings({...companySettings, address: e.target.value})}
            rows="3"
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box',
              resize: 'vertical'
            }}
          />
        </div>
      </div>

      <div style={{ marginTop: '25px', textAlign: 'center' }}>
        <button
          onClick={() => handleSave('company')}
          disabled={loading}
          style={{
            background: loading ? '#95a5a6' : '#27ae60',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {loading ? '⏳ جاري الحفظ...' : '💾 حفظ إعدادات الشركة'}
        </button>
      </div>
    </div>
  );

  const renderInvoiceSettings = () => (
    <div style={{ background: 'white', padding: '25px', borderRadius: '12px', boxShadow: '0 5px 15px rgba(0,0,0,0.1)' }}>
      <h3 style={{ color: '#2c3e50', marginBottom: '25px', fontSize: '20px' }}>📄 إعدادات الفواتير</h3>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            بادئة رقم الفاتورة
          </label>
          <input
            type="text"
            value={invoiceSettings.prefix}
            onChange={(e) => setInvoiceSettings({...invoiceSettings, prefix: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            الرقم الحالي
          </label>
          <input
            type="number"
            value={invoiceSettings.currentNumber}
            onChange={(e) => setInvoiceSettings({...invoiceSettings, currentNumber: parseInt(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            نسبة الضريبة (%)
          </label>
          <input
            type="number"
            min="0"
            max="100"
            step="0.1"
            value={invoiceSettings.taxRate}
            onChange={(e) => setInvoiceSettings({...invoiceSettings, taxRate: parseFloat(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            العملة الافتراضية
          </label>
          <select
            value={invoiceSettings.currency}
            onChange={(e) => setInvoiceSettings({...invoiceSettings, currency: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="SAR">ريال سعودي (SAR)</option>
            <option value="USD">دولار أمريكي (USD)</option>
            <option value="EUR">يورو (EUR)</option>
            <option value="AED">درهم إماراتي (AED)</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            مدة السداد (أيام)
          </label>
          <input
            type="number"
            min="1"
            value={invoiceSettings.paymentTerms}
            onChange={(e) => setInvoiceSettings({...invoiceSettings, paymentTerms: parseInt(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            قالب الفاتورة
          </label>
          <select
            value={invoiceSettings.invoiceTemplate}
            onChange={(e) => setInvoiceSettings({...invoiceSettings, invoiceTemplate: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="modern">عصري</option>
            <option value="classic">كلاسيكي</option>
            <option value="minimal">بسيط</option>
            <option value="detailed">مفصل</option>
          </select>
        </div>
      </div>

      {/* خيارات إضافية */}
      <div style={{ marginTop: '25px' }}>
        <h4 style={{ color: '#34495e', marginBottom: '15px' }}>⚙️ خيارات إضافية</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          {[
            { key: 'showTax', label: 'إظهار الضريبة' },
            { key: 'showDiscount', label: 'إظهار الخصم' },
            { key: 'autoCalculateTax', label: 'حساب الضريبة تلقائياً' }
          ].map(option => (
            <label key={option.key} style={{ display: 'flex', alignItems: 'center', gap: '10px', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={invoiceSettings[option.key]}
                onChange={(e) => setInvoiceSettings({...invoiceSettings, [option.key]: e.target.checked})}
                style={{ transform: 'scale(1.2)' }}
              />
              <span style={{ fontSize: '14px' }}>{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* نص التذييل */}
      <div style={{ marginTop: '25px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
          نص التذييل
        </label>
        <textarea
          value={invoiceSettings.footerText}
          onChange={(e) => setInvoiceSettings({...invoiceSettings, footerText: e.target.value})}
          rows="3"
          style={{
            width: '100%',
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            boxSizing: 'border-box',
            resize: 'vertical'
          }}
        />
      </div>

      <div style={{ marginTop: '25px', textAlign: 'center' }}>
        <button
          onClick={() => handleSave('invoice')}
          disabled={loading}
          style={{
            background: loading ? '#95a5a6' : '#3498db',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {loading ? '⏳ جاري الحفظ...' : '💾 حفظ إعدادات الفواتير'}
        </button>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div style={{ background: 'white', padding: '25px', borderRadius: '12px', boxShadow: '0 5px 15px rgba(0,0,0,0.1)' }}>
      <h3 style={{ color: '#2c3e50', marginBottom: '25px', fontSize: '20px' }}>⚙️ إعدادات النظام</h3>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            اللغة
          </label>
          <select
            value={systemSettings.language}
            onChange={(e) => setSystemSettings({...systemSettings, language: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="ar">العربية</option>
            <option value="en">English</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            المنطقة الزمنية
          </label>
          <select
            value={systemSettings.timezone}
            onChange={(e) => setSystemSettings({...systemSettings, timezone: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="Asia/Riyadh">الرياض (GMT+3)</option>
            <option value="Asia/Dubai">دبي (GMT+4)</option>
            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            تنسيق التاريخ
          </label>
          <select
            value={systemSettings.dateFormat}
            onChange={(e) => setSystemSettings({...systemSettings, dateFormat: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            تكرار النسخ الاحتياطي
          </label>
          <select
            value={systemSettings.backupFrequency}
            onChange={(e) => setSystemSettings({...systemSettings, backupFrequency: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="daily">يومي</option>
            <option value="weekly">أسبوعي</option>
            <option value="monthly">شهري</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            حد تنبيه المخزون المنخفض
          </label>
          <input
            type="number"
            min="1"
            value={systemSettings.lowStockThreshold}
            onChange={(e) => setSystemSettings({...systemSettings, lowStockThreshold: parseInt(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            انتهاء الجلسة (دقيقة)
          </label>
          <input
            type="number"
            min="5"
            max="480"
            value={systemSettings.sessionTimeout}
            onChange={(e) => setSystemSettings({...systemSettings, sessionTimeout: parseInt(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
      </div>

      {/* خيارات النظام */}
      <div style={{ marginTop: '25px' }}>
        <h4 style={{ color: '#34495e', marginBottom: '15px' }}>🔔 التنبيهات والإشعارات</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          {[
            { key: 'autoBackup', label: 'النسخ الاحتياطي التلقائي' },
            { key: 'emailNotifications', label: 'إشعارات البريد الإلكتروني' },
            { key: 'smsNotifications', label: 'إشعارات الرسائل النصية' },
            { key: 'lowStockAlert', label: 'تنبيه المخزون المنخفض' }
          ].map(option => (
            <label key={option.key} style={{ display: 'flex', alignItems: 'center', gap: '10px', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={systemSettings[option.key]}
                onChange={(e) => setSystemSettings({...systemSettings, [option.key]: e.target.checked})}
                style={{ transform: 'scale(1.2)' }}
              />
              <span style={{ fontSize: '14px' }}>{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      <div style={{ marginTop: '25px', textAlign: 'center' }}>
        <button
          onClick={() => handleSave('system')}
          disabled={loading}
          style={{
            background: loading ? '#95a5a6' : '#8e44ad',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {loading ? '⏳ جاري الحفظ...' : '💾 حفظ إعدادات النظام'}
        </button>
      </div>
    </div>
  );

  const renderPaymentSettings = () => (
    <div style={{ background: 'white', padding: '25px', borderRadius: '12px', boxShadow: '0 5px 15px rgba(0,0,0,0.1)' }}>
      <h3 style={{ color: '#2c3e50', marginBottom: '25px', fontSize: '20px' }}>💳 إعدادات المدفوعات</h3>
      
      {/* طرق الدفع المقبولة */}
      <div style={{ marginBottom: '25px' }}>
        <h4 style={{ color: '#34495e', marginBottom: '15px' }}>💰 طرق الدفع المقبولة</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          {[
            { key: 'acceptCash', label: '💵 نقدي', icon: '💵' },
            { key: 'acceptCard', label: '💳 بطاقة ائتمان', icon: '💳' },
            { key: 'acceptBank', label: '🏦 تحويل بنكي', icon: '🏦' },
            { key: 'acceptCheck', label: '📝 شيك', icon: '📝' }
          ].map(option => (
            <label key={option.key} style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '10px', 
              cursor: 'pointer',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              background: paymentSettings[option.key] ? '#e8f5e8' : '#f8f9fa'
            }}>
              <input
                type="checkbox"
                checked={paymentSettings[option.key]}
                onChange={(e) => setPaymentSettings({...paymentSettings, [option.key]: e.target.checked})}
                style={{ transform: 'scale(1.2)' }}
              />
              <span style={{ fontSize: '14px' }}>{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* إعدادات إضافية */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            طريقة الدفع الافتراضية
          </label>
          <select
            value={paymentSettings.defaultPaymentMethod}
            onChange={(e) => setPaymentSettings({...paymentSettings, defaultPaymentMethod: e.target.value})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          >
            <option value="cash">نقدي</option>
            <option value="card">بطاقة ائتمان</option>
            <option value="bank">تحويل بنكي</option>
            <option value="check">شيك</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            نسبة الغرامة المتأخرة (%)
          </label>
          <input
            type="number"
            min="0"
            max="50"
            step="0.1"
            value={paymentSettings.lateFeePercentage}
            onChange={(e) => setPaymentSettings({...paymentSettings, lateFeePercentage: parseFloat(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#2c3e50' }}>
            فترة السماح (أيام)
          </label>
          <input
            type="number"
            min="0"
            max="30"
            value={paymentSettings.gracePeriodDays}
            onChange={(e) => setPaymentSettings({...paymentSettings, gracePeriodDays: parseInt(e.target.value)})}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
      </div>

      {/* خيارات المدفوعات */}
      <div style={{ marginTop: '25px' }}>
        <h4 style={{ color: '#34495e', marginBottom: '15px' }}>⚙️ خيارات المدفوعات</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
          {[
            { key: 'requirePaymentConfirmation', label: 'تتطلب تأكيد الدفع' },
            { key: 'allowPartialPayments', label: 'السماح بالدفع الجزئي' }
          ].map(option => (
            <label key={option.key} style={{ display: 'flex', alignItems: 'center', gap: '10px', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={paymentSettings[option.key]}
                onChange={(e) => setPaymentSettings({...paymentSettings, [option.key]: e.target.checked})}
                style={{ transform: 'scale(1.2)' }}
              />
              <span style={{ fontSize: '14px' }}>{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      <div style={{ marginTop: '25px', textAlign: 'center' }}>
        <button
          onClick={() => handleSave('payment')}
          disabled={loading}
          style={{
            background: loading ? '#95a5a6' : '#e67e22',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '8px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {loading ? '⏳ جاري الحفظ...' : '💾 حفظ إعدادات المدفوعات'}
        </button>
      </div>
    </div>
  );

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          ⚙️ إعدادات النظام
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          إدارة وتخصيص إعدادات النظام المحاسبي
        </p>
      </div>

      {/* رسالة الحفظ */}
      {saveMessage && (
        <div style={{
          background: '#d4edda',
          color: '#155724',
          padding: '15px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #c3e6cb',
          textAlign: 'center',
          fontSize: '16px',
          fontWeight: 'bold'
        }}>
          ✅ {saveMessage}
        </div>
      )}

      {/* التبويبات */}
      <div style={{ marginBottom: '30px' }}>
        <div style={{ 
          display: 'flex', 
          gap: '10px', 
          flexWrap: 'wrap',
          background: 'white',
          padding: '10px',
          borderRadius: '12px',
          boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
        }}>
          {[
            { key: 'company', label: '🏢 الشركة', color: '#27ae60' },
            { key: 'invoice', label: '📄 الفواتير', color: '#3498db' },
            { key: 'system', label: '⚙️ النظام', color: '#8e44ad' },
            { key: 'payment', label: '💳 المدفوعات', color: '#e67e22' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              style={{
                background: activeTab === tab.key ? tab.color : '#f8f9fa',
                color: activeTab === tab.key ? 'white' : '#2c3e50',
                border: 'none',
                padding: '12px 20px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                transition: 'all 0.3s ease',
                flex: '1',
                minWidth: '150px'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* محتوى التبويبات */}
      <div>
        {activeTab === 'company' && renderCompanySettings()}
        {activeTab === 'invoice' && renderInvoiceSettings()}
        {activeTab === 'system' && renderSystemSettings()}
        {activeTab === 'payment' && renderPaymentSettings()}
      </div>
    </div>
  );
};

export default SettingsPage;