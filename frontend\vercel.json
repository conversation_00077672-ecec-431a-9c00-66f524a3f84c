{"version": 2, "name": "sharaubtravelsoft-frontend", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}], "routes": [{"src": "/static/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico|webp))", "headers": {"cache-control": "public, max-age=31536000"}}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "geolocation=(), microphone=(), camera=()"}]}], "env": {"REACT_APP_ENV": "production"}, "build": {"env": {"REACT_APP_ENV": "production"}}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}}