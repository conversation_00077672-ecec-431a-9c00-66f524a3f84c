import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart, 
  Bar<PERSON>hart, 
  PerformanceGauge, 
  DataTable, 
  StatCard 
} from '../../components/Charts/ChartComponents';
import './ReportsPage.css';

const AdvancedReportsPage = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedSupplierType, setSelectedSupplierType] = useState('all');
  const [reportData, setReportData] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // بيانات تجريبية محسنة
  const [suppliersData] = useState([
    {
      id: 1,
      name: 'شركة الطيران العربية',
      type: 'airline',
      totalRevenue: 450000,
      totalBookings: 150,
      rating: 4.5,
      status: 'active',
      commission: 5.5,
      responseTime: 2.1,
      satisfactionScore: 4.6
    },
    {
      id: 2,
      name: 'فنادق الخليج الدولية',
      type: 'hotel',
      totalRevenue: 680000,
      totalBookings: 200,
      rating: 4.8,
      status: 'active',
      commission: 7.0,
      responseTime: 1.8,
      satisfactionScore: 4.8
    },
    {
      id: 3,
      name: 'شركة النقل السياحي',
      type: 'transport',
      totalRevenue: 120000,
      totalBookings: 80,
      rating: 4.2,
      status: 'inactive',
      commission: 4.5,
      responseTime: 3.2,
      satisfactionScore: 4.1
    },
    {
      id: 4,
      name: 'وكالة السفر الذهبية',
      type: 'tour',
      totalRevenue: 890000,
      totalBookings: 320,
      rating: 4.7,
      status: 'active',
      commission: 6.5,
      responseTime: 2.5,
      satisfactionScore: 4.7
    },
    {
      id: 5,
      name: 'مكتب التأشيرات السريع',
      type: 'visa',
      totalRevenue: 340000,
      totalBookings: 180,
      rating: 4.6,
      status: 'active',
      commission: 8.0,
      responseTime: 1.5,
      satisfactionScore: 4.5
    }
  ]);

  useEffect(() => {
    generateAdvancedReportData();
  }, [dateRange, selectedSupplierType]);

  const generateAdvancedReportData = () => {
    setIsLoading(true);
    
    setTimeout(() => {
      const filteredSuppliers = selectedSupplierType === 'all' 
        ? suppliersData 
        : suppliersData.filter(s => s.type === selectedSupplierType);

      // بيانات للمخططات
      const revenueByType = suppliersData.reduce((acc, supplier) => {
        const type = getTypeLabel(supplier.type);
        acc[type] = (acc[type] || 0) + supplier.totalRevenue;
        return acc;
      }, {});

      const monthlyData = [
        { label: 'يناير', value: 245000 },
        { label: 'فبراير', value: 269000 },
        { label: 'مارس', value: 253000 },
        { label: 'أبريل', value: 288000 },
        { label: 'مايو', value: 302000 },
        { label: 'يونيو', value: 290000 }
      ];

      const bookingsData = [
        { label: 'يناير', value: 125 },
        { label: 'فبراير', value: 140 },
        { label: 'مارس', value: 133 },
        { label: 'أبريل', value: 158 },
        { label: 'مايو', value: 167 },
        { label: 'يونيو', value: 155 }
      ];

      const pieData = Object.entries(revenueByType).map(([type, revenue]) => ({
        label: type,
        value: revenue
      }));

      // بيانات الجدول
      const tableData = filteredSuppliers.map(supplier => ({
        name: supplier.name,
        type: getTypeLabel(supplier.type),
        revenue: supplier.totalRevenue,
        bookings: supplier.totalBookings,
        rating: supplier.rating,
        responseTime: supplier.responseTime,
        status: supplier.status
      }));

      const tableColumns = [
        { key: 'name', header: 'اسم المورد' },
        { key: 'type', header: 'النوع' },
        { 
          key: 'revenue', 
          header: 'الإيرادات',
          render: (value) => formatCurrency(value)
        },
        { key: 'bookings', header: 'الحجوزات' },
        { 
          key: 'rating', 
          header: 'التقييم',
          render: (value) => `${value} ⭐`
        },
        { 
          key: 'responseTime', 
          header: 'وقت الاستجابة',
          render: (value) => `${value} ساعة`
        },
        { 
          key: 'status', 
          header: 'الحالة',
          render: (value) => value === 'active' ? 'نشط' : 'غير نشط'
        }
      ];

      setReportData({
        pieData,
        monthlyRevenue: monthlyData,
        monthlyBookings: bookingsData,
        tableData,
        tableColumns,
        totalRevenue: filteredSuppliers.reduce((sum, s) => sum + s.totalRevenue, 0),
        totalBookings: filteredSuppliers.reduce((sum, s) => sum + s.totalBookings, 0),
        averageRating: filteredSuppliers.reduce((sum, s) => sum + s.rating, 0) / filteredSuppliers.length,
        averageResponseTime: filteredSuppliers.reduce((sum, s) => sum + s.responseTime, 0) / filteredSuppliers.length,
        activeSuppliers: filteredSuppliers.filter(s => s.status === 'active').length
      });
      
      setIsLoading(false);
    }, 1000);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTypeLabel = (type) => {
    const types = {
      airline: 'شركات الطيران',
      hotel: 'الفنادق',
      transport: 'النقل',
      tour: 'الرحلات السياحية',
      visa: 'خدمات التأشيرات'
    };
    return types[type] || type;
  };

  const exportReport = (format) => {
    if (format === 'pdf') {
      alert('سيتم تصدير التقرير كملف PDF');
    } else if (format === 'excel') {
      alert('سيتم تصدير التقرير كملف Excel');
    }
  };

  const renderDashboardTab = () => (
    <div className="dashboard-tab">
      {/* بطاقات الإحصائيات */}
      <div className="stats-grid">
        <StatCard
          icon="💰"
          title="إجمالي الإيرادات"
          value={formatCurrency(reportData.totalRevenue || 0)}
          change="+12.5%"
          changeType="positive"
        />
        <StatCard
          icon="📊"
          title="إجمالي الحجوزات"
          value={reportData.totalBookings || 0}
          change="****%"
          changeType="positive"
        />
        <StatCard
          icon="⭐"
          title="متوسط التقييم"
          value={`${(reportData.averageRating || 0).toFixed(1)}/5`}
          change="+0.2"
          changeType="positive"
        />
        <StatCard
          icon="⏱️"
          title="متوسط وقت الاستجابة"
          value={`${(reportData.averageResponseTime || 0).toFixed(1)} ساعة`}
          change="-0.3"
          changeType="positive"
        />
      </div>

      {/* المخططات */}
      <div className="charts-grid">
        <div className="chart-item">
          {reportData.pieData && (
            <PieChart
              data={reportData.pieData}
              title="توزيع الإيرادات حسب النوع"
            />
          )}
        </div>
        
        <div className="chart-item">
          {reportData.monthlyRevenue && (
            <LineChart
              data={reportData.monthlyRevenue}
              title="اتجاه الإيرادات الشهرية"
              xLabel="الأشهر"
              yLabel="الإيرادات (ريال)"
            />
          )}
        </div>
      </div>

      {/* مؤشرات الأداء */}
      <div className="gauges-grid">
        <PerformanceGauge
          value={85}
          max={100}
          title="مؤشر الأداء العام"
          unit="%"
        />
        <PerformanceGauge
          value={92}
          max={100}
          title="رضا العملاء"
          unit="%"
        />
        <PerformanceGauge
          value={78}
          max={100}
          title="كفاءة الاستجابة"
          unit="%"
        />
        <PerformanceGauge
          value={95}
          max={100}
          title="معدل الاحتفاظ"
          unit="%"
        />
      </div>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="analytics-tab">
      <div className="analytics-grid">
        <div className="chart-section">
          {reportData.monthlyBookings && (
            <BarChart
              data={reportData.monthlyBookings}
              title="الحجوزات الشهرية"
              xLabel="الأشهر"
              yLabel="عدد الحجوزات"
            />
          )}
        </div>
        
        <div className="chart-section">
          {reportData.monthlyRevenue && (
            <LineChart
              data={reportData.monthlyRevenue}
              title="نمو الإيرادات"
              xLabel="الأشهر"
              yLabel="الإيرادات (ريال)"
            />
          )}
        </div>
      </div>

      <div className="detailed-analysis">
        <div className="analysis-card">
          <h3>تحليل الاتجاهات</h3>
          <div className="trend-indicators">
            <div className="trend-item">
              <span className="trend-label">نمو الإيرادات</span>
              <div className="trend-bar">
                <div className="trend-fill positive" style={{ width: '85%' }}></div>
              </div>
              <span className="trend-value">+12.5%</span>
            </div>
            <div className="trend-item">
              <span className="trend-label">نمو الحجوزات</span>
              <div className="trend-bar">
                <div className="trend-fill positive" style={{ width: '78%' }}></div>
              </div>
              <span className="trend-value">****%</span>
            </div>
            <div className="trend-item">
              <span className="trend-label">تحسن التقييم</span>
              <div className="trend-bar">
                <div className="trend-fill positive" style={{ width: '92%' }}></div>
              </div>
              <span className="trend-value">+4.2%</span>
            </div>
          </div>
        </div>

        <div className="analysis-card">
          <h3>المؤشرات الرئيسية</h3>
          <div className="kpi-grid">
            <div className="kpi-item">
              <div className="kpi-value">2.3</div>
              <div className="kpi-label">متوسط وقت الاستجابة (ساعة)</div>
              <div className="kpi-change positive">-15% تحسن</div>
            </div>
            <div className="kpi-item">
              <div className="kpi-value">4.6</div>
              <div className="kpi-label">متوسط رضا العملاء</div>
              <div className="kpi-change positive">+0.3 تحسن</div>
            </div>
            <div className="kpi-item">
              <div className="kpi-value">95.2%</div>
              <div className="kpi-label">معدل الاحتفاظ بالموردين</div>
              <div className="kpi-change positive">+2.1% تحسن</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDataTab = () => (
    <div className="data-tab">
      {reportData.tableData && reportData.tableColumns && (
        <DataTable
          data={reportData.tableData}
          columns={reportData.tableColumns}
          title="بيانات الموردين التفصيلية"
        />
      )}
      
      <div className="data-insights">
        <div className="insight-card">
          <h3>رؤى البيانات</h3>
          <ul className="insights-list">
            <li>أعلى مورد أداءً: وكالة السفر الذهبية بإيرادات {formatCurrency(890000)}</li>
            <li>أسرع استجابة: مكتب التأشيرات السريع (1.5 ساعة)</li>
            <li>أعلى تقييم: فنادق الخليج الدولية (4.8/5)</li>
            <li>أكثر الأنواع ربحية: الرحلات السياحية</li>
            <li>معدل النمو الشهري: +12.5% في الإيرادات</li>
          </ul>
        </div>

        <div className="insight-card">
          <h3>التوصيات</h3>
          <ul className="recommendations-list">
            <li>زيادة التعاون مع موردي الرحلات السياحية لتحقيق أرباح أعلى</li>
            <li>تحسين أوقات الاستجابة للموردين ذوي الأداء المنخفض</li>
            <li>تطوير برامج تحفيزية للموردين عالي الأداء</li>
            <li>مراجعة شروط العمولة مع الموردين منخفضي الإيرادات</li>
            <li>تنفيذ برامج تدريبية لتحسين جودة الخدمة</li>
          </ul>
        </div>
      </div>
    </div>
  );

  return (
    <div className="reports-page advanced">
      <div className="page-header">
        <div className="header-content">
          <h1>التقارير المتقدمة</h1>
          <p>تحليل شامل ومتقدم لأداء الموردين مع مخططات تفاعلية</p>
        </div>
        
        <div className="header-controls">
          <div className="date-range">
            <label>من تاريخ:</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
            />
            <label>إلى تاريخ:</label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
            />
          </div>
          
          <div className="supplier-filter">
            <select
              value={selectedSupplierType}
              onChange={(e) => setSelectedSupplierType(e.target.value)}
            >
              <option value="all">جميع الأنواع</option>
              <option value="airline">شركات الطيران</option>
              <option value="hotel">الفنادق</option>
              <option value="transport">النقل</option>
              <option value="tour">الرحلات السياحية</option>
              <option value="visa">خدمات التأشيرات</option>
            </select>
          </div>
          
          <div className="export-buttons">
            <button 
              className="export-btn pdf"
              onClick={() => exportReport('pdf')}
            >
              📄 تصدير PDF
            </button>
            <button 
              className="export-btn excel"
              onClick={() => exportReport('excel')}
            >
              📊 تصدير Excel
            </button>
          </div>
        </div>
      </div>

      <div className="reports-tabs">
        <div className="tabs-header">
          <button 
            className={`tab-btn ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            🏠 لوحة المعلومات
          </button>
          <button 
            className={`tab-btn ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            📈 التحليلات المتقدمة
          </button>
          <button 
            className={`tab-btn ${activeTab === 'data' ? 'active' : ''}`}
            onClick={() => setActiveTab('data')}
          >
            📋 البيانات والرؤى
          </button>
        </div>

        <div className="tabs-content">
          {isLoading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>جاري تحميل التقرير المتقدم...</p>
            </div>
          ) : (
            <>
              {activeTab === 'dashboard' && renderDashboardTab()}
              {activeTab === 'analytics' && renderAnalyticsTab()}
              {activeTab === 'data' && renderDataTab()}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedReportsPage;