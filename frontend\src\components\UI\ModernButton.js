import React from 'react';
import './ModernButton.css';

const ModernButton = ({
  children,
  variant = 'primary',
  size = 'md',
  shape = 'rounded',
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  gradient = false,
  glass = false,
  className = '',
  onClick,
  type = 'button',
  ...props
}) => {
  const buttonClasses = [
    'modern-btn',
    `modern-btn--${variant}`,
    `modern-btn--${size}`,
    `modern-btn--${shape}`,
    loading && 'modern-btn--loading',
    disabled && 'modern-btn--disabled',
    fullWidth && 'modern-btn--full-width',
    gradient && 'modern-btn--gradient',
    glass && 'modern-btn--glass',
    icon && !children && 'modern-btn--icon-only',
    className
  ].filter(Boolean).join(' ');

  const handleClick = (e) => {
    if (loading || disabled) return;
    onClick?.(e);
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <span className="modern-btn__spinner">
          <svg className="modern-btn__spinner-icon" viewBox="0 0 24 24">
            <circle
              className="modern-btn__spinner-circle"
              cx="12"
              cy="12"
              r="10"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray="31.416"
              strokeDashoffset="31.416"
            />
          </svg>
        </span>
      )}
      
      {!loading && icon && iconPosition === 'left' && (
        <span className="modern-btn__icon modern-btn__icon--left">
          {icon}
        </span>
      )}
      
      {children && (
        <span className="modern-btn__text">
          {children}
        </span>
      )}
      
      {!loading && icon && iconPosition === 'right' && (
        <span className="modern-btn__icon modern-btn__icon--right">
          {icon}
        </span>
      )}
      
      <span className="modern-btn__ripple"></span>
    </button>
  );
};

// Icon Button Component
export const IconButton = ({
  icon,
  variant = 'ghost',
  size = 'md',
  shape = 'circle',
  tooltip,
  className = '',
  ...props
}) => (
  <ModernButton
    variant={variant}
    size={size}
    shape={shape}
    icon={icon}
    className={`icon-button ${className}`}
    title={tooltip}
    {...props}
  />
);

// Button Group Component
export const ButtonGroup = ({
  children,
  variant = 'primary',
  size = 'md',
  orientation = 'horizontal',
  className = '',
  ...props
}) => (
  <div
    className={`modern-btn-group modern-btn-group--${orientation} modern-btn-group--${variant} modern-btn-group--${size} ${className}`}
    {...props}
  >
    {children}
  </div>
);

// Floating Action Button Component
export const FloatingActionButton = ({
  icon,
  variant = 'primary',
  size = 'lg',
  position = 'bottom-right',
  className = '',
  ...props
}) => (
  <ModernButton
    variant={variant}
    size={size}
    shape="circle"
    icon={icon}
    className={`fab fab--${position} ${className}`}
    {...props}
  />
);

// Split Button Component
export const SplitButton = ({
  children,
  dropdownItems = [],
  variant = 'primary',
  size = 'md',
  onMainClick,
  className = '',
  ...props
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className={`split-button ${className}`} {...props}>
      <ModernButton
        variant={variant}
        size={size}
        onClick={onMainClick}
        className="split-button__main"
      >
        {children}
      </ModernButton>
      
      <ModernButton
        variant={variant}
        size={size}
        shape="square"
        icon="▼"
        onClick={() => setIsOpen(!isOpen)}
        className="split-button__dropdown"
      />
      
      {isOpen && (
        <div className="split-button__menu">
          {dropdownItems.map((item, index) => (
            <button
              key={index}
              className="split-button__menu-item"
              onClick={() => {
                item.onClick?.();
                setIsOpen(false);
              }}
            >
              {item.icon && <span className="split-button__menu-icon">{item.icon}</span>}
              {item.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Toggle Button Component
export const ToggleButton = ({
  children,
  active = false,
  variant = 'outline',
  size = 'md',
  onChange,
  className = '',
  ...props
}) => (
  <ModernButton
    variant={active ? 'primary' : variant}
    size={size}
    onClick={() => onChange?.(!active)}
    className={`toggle-button ${active ? 'toggle-button--active' : ''} ${className}`}
    {...props}
  >
    {children}
  </ModernButton>
);

export default ModernButton;