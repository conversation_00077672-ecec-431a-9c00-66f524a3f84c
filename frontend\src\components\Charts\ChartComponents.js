import React from 'react';
import './ChartComponents.css';

// مكون الرسم البياني الدائري
export const PieChart = ({ data, title }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = 0;

  return (
    <div className="pie-chart-component">
      <h3>{title}</h3>
      <div className="pie-chart-container">
        <svg width="200" height="200" viewBox="0 0 200 200">
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const angle = (item.value / total) * 360;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            
            const x1 = 100 + 80 * Math.cos((startAngle * Math.PI) / 180);
            const y1 = 100 + 80 * Math.sin((startAngle * Math.PI) / 180);
            const x2 = 100 + 80 * Math.cos((endAngle * Math.PI) / 180);
            const y2 = 100 + 80 * Math.sin((endAngle * Math.PI) / 180);
            
            const largeArcFlag = angle > 180 ? 1 : 0;
            
            const pathData = [
              `M 100 100`,
              `L ${x1} ${y1}`,
              `A 80 80 0 ${largeArcFlag} 1 ${x2} ${y2}`,
              'Z'
            ].join(' ');
            
            currentAngle += angle;
            
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];
            
            return (
              <path
                key={index}
                d={pathData}
                fill={colors[index % colors.length]}
                stroke="white"
                strokeWidth="2"
              />
            );
          })}
        </svg>
        <div className="pie-legend">
          {data.map((item, index) => {
            const percentage = ((item.value / total) * 100).toFixed(1);
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];
            return (
              <div key={index} className="legend-item">
                <div 
                  className="legend-color" 
                  style={{ backgroundColor: colors[index % colors.length] }}
                ></div>
                <span className="legend-label">{item.label}</span>
                <span className="legend-value">{percentage}%</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// مكون الرسم البياني الخطي
export const LineChart = ({ data, title, xLabel, yLabel }) => {
  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue;

  return (
    <div className="line-chart-component">
      <h3>{title}</h3>
      <div className="chart-container">
        <div className="y-axis-label">{yLabel}</div>
        <svg width="100%" height="300" viewBox="0 0 500 300">
          {/* Grid lines */}
          {[0, 1, 2, 3, 4, 5].map(i => (
            <line
              key={i}
              x1="50"
              y1={50 + (i * 40)}
              x2="450"
              y2={50 + (i * 40)}
              stroke="#e1e8ed"
              strokeWidth="1"
            />
          ))}
          
          {/* Data line */}
          <polyline
            fill="none"
            stroke="#667eea"
            strokeWidth="3"
            points={data.map((item, index) => {
              const x = 50 + (index * (400 / (data.length - 1)));
              const y = 250 - ((item.value - minValue) / range) * 200;
              return `${x},${y}`;
            }).join(' ')}
          />
          
          {/* Data points */}
          {data.map((item, index) => {
            const x = 50 + (index * (400 / (data.length - 1)));
            const y = 250 - ((item.value - minValue) / range) * 200;
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="5"
                fill="#667eea"
                stroke="white"
                strokeWidth="2"
              />
            );
          })}
          
          {/* X-axis labels */}
          {data.map((item, index) => {
            const x = 50 + (index * (400 / (data.length - 1)));
            return (
              <text
                key={index}
                x={x}
                y="280"
                textAnchor="middle"
                fontSize="12"
                fill="#6c757d"
              >
                {item.label}
              </text>
            );
          })}
        </svg>
        <div className="x-axis-label">{xLabel}</div>
      </div>
    </div>
  );
};

// مكون الرسم البياني العمودي
export const BarChart = ({ data, title, xLabel, yLabel }) => {
  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <div className="bar-chart-component">
      <h3>{title}</h3>
      <div className="chart-container">
        <div className="y-axis-label">{yLabel}</div>
        <svg width="100%" height="300" viewBox="0 0 500 300">
          {/* Grid lines */}
          {[0, 1, 2, 3, 4, 5].map(i => (
            <line
              key={i}
              x1="50"
              y1={50 + (i * 40)}
              x2="450"
              y2={50 + (i * 40)}
              stroke="#e1e8ed"
              strokeWidth="1"
            />
          ))}
          
          {/* Bars */}
          {data.map((item, index) => {
            const barWidth = 300 / data.length * 0.8;
            const x = 50 + (index * (400 / data.length)) + (400 / data.length - barWidth) / 2;
            const height = (item.value / maxValue) * 200;
            const y = 250 - height;
            
            return (
              <g key={index}>
                <rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={height}
                  fill="url(#barGradient)"
                  rx="4"
                />
                <text
                  x={x + barWidth / 2}
                  y={y - 5}
                  textAnchor="middle"
                  fontSize="11"
                  fill="#495057"
                  fontWeight="600"
                >
                  {item.value.toLocaleString()}
                </text>
              </g>
            );
          })}
          
          {/* Gradient definition */}
          <defs>
            <linearGradient id="barGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#667eea" />
              <stop offset="100%" stopColor="#764ba2" />
            </linearGradient>
          </defs>
          
          {/* X-axis labels */}
          {data.map((item, index) => {
            const barWidth = 300 / data.length * 0.8;
            const x = 50 + (index * (400 / data.length)) + (400 / data.length) / 2;
            return (
              <text
                key={index}
                x={x}
                y="280"
                textAnchor="middle"
                fontSize="12"
                fill="#6c757d"
              >
                {item.label}
              </text>
            );
          })}
        </svg>
        <div className="x-axis-label">{xLabel}</div>
      </div>
    </div>
  );
};

// مكون مؤشر الأداء
export const PerformanceGauge = ({ value, max, title, unit }) => {
  const percentage = (value / max) * 100;
  const angle = (percentage / 100) * 180;
  
  return (
    <div className="performance-gauge">
      <h4>{title}</h4>
      <div className="gauge-container">
        <svg width="200" height="120" viewBox="0 0 200 120">
          {/* Background arc */}
          <path
            d="M 20 100 A 80 80 0 0 1 180 100"
            fill="none"
            stroke="#e1e8ed"
            strokeWidth="20"
            strokeLinecap="round"
          />
          
          {/* Progress arc */}
          <path
            d="M 20 100 A 80 80 0 0 1 180 100"
            fill="none"
            stroke="url(#gaugeGradient)"
            strokeWidth="20"
            strokeLinecap="round"
            strokeDasharray={`${(percentage / 100) * 251.2} 251.2`}
          />
          
          {/* Needle */}
          <line
            x1="100"
            y1="100"
            x2={100 + 60 * Math.cos((angle - 90) * Math.PI / 180)}
            y2={100 + 60 * Math.sin((angle - 90) * Math.PI / 180)}
            stroke="#2c3e50"
            strokeWidth="3"
            strokeLinecap="round"
          />
          
          {/* Center circle */}
          <circle cx="100" cy="100" r="8" fill="#2c3e50" />
          
          {/* Gradient definition */}
          <defs>
            <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#dc3545" />
              <stop offset="50%" stopColor="#ffc107" />
              <stop offset="100%" stopColor="#28a745" />
            </linearGradient>
          </defs>
        </svg>
        
        <div className="gauge-value">
          <span className="value">{value}</span>
          <span className="unit">{unit}</span>
        </div>
        
        <div className="gauge-labels">
          <span className="min-label">0</span>
          <span className="max-label">{max}</span>
        </div>
      </div>
    </div>
  );
};

// مكون الجدول التفاعلي
export const DataTable = ({ data, columns, title }) => {
  return (
    <div className="data-table-component">
      <h3>{title}</h3>
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              {columns.map((column, index) => (
                <th key={index}>{column.header}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {columns.map((column, colIndex) => (
                  <td key={colIndex}>
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// مكون بطاقة الإحصائية
export const StatCard = ({ icon, title, value, change, changeType }) => {
  return (
    <div className="stat-card-component">
      <div className="stat-icon">{icon}</div>
      <div className="stat-content">
        <h4>{title}</h4>
        <div className="stat-value">{value}</div>
        {change && (
          <div className={`stat-change ${changeType}`}>
            {changeType === 'positive' ? '↗' : '↘'} {change}
          </div>
        )}
      </div>
    </div>
  );
};