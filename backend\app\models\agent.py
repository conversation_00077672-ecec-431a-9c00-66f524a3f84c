"""
نموذج الوكلاء
Agent Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Decimal, ForeignKey, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.models.base import BaseModel

class AgentType(PyEnum):
    """أنواع الوكلاء"""
    INDIVIDUAL = "individual"    # وكيل فردي
    COMPANY = "company"         # شركة وكالة
    BRANCH = "branch"           # فرع
    SUB_AGENT = "sub_agent"     # وكيل فرعي

class AgentStatus(PyEnum):
    """حالة الوكيل"""
    ACTIVE = "active"      # نشط
    INACTIVE = "inactive"  # غير نشط
    SUSPENDED = "suspended"  # معلق
    TERMINATED = "terminated"  # منتهي

class CommissionType(PyEnum):
    """نوع العمولة"""
    PERCENTAGE = "percentage"  # نسبة مئوية
    FIXED = "fixed"           # مبلغ ثابت
    TIERED = "tiered"         # متدرج

class Agent(BaseModel):
    """
    نموذج الوكلاء
    """
    __tablename__ = "agents"
    
    # المعلومات الأساسية
    agent_code = Column(String(20), unique=True, nullable=False, index=True)
    agent_type = Column(Enum(AgentType), nullable=False, default=AgentType.INDIVIDUAL)
    status = Column(Enum(AgentStatus), nullable=False, default=AgentStatus.ACTIVE)
    
    # معلومات شخصية/شركة
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    company_name = Column(String(100), nullable=True)
    trade_license = Column(String(50), nullable=True)
    
    # معلومات الاتصال
    email = Column(String(100), nullable=True, index=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    website = Column(String(100), nullable=True)
    
    # العنوان
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # معلومات العمولة
    commission_type = Column(Enum(CommissionType), default=CommissionType.PERCENTAGE)
    default_commission_rate = Column(Decimal(5, 2), default=0.00)
    minimum_commission = Column(Decimal(10, 2), default=0.00)
    maximum_commission = Column(Decimal(10, 2), nullable=True)
    
    # معلومات مالية
    credit_limit = Column(Decimal(15, 2), default=0.00)
    current_balance = Column(Decimal(15, 2), default=0.00)
    total_sales = Column(Decimal(15, 2), default=0.00)
    total_commission = Column(Decimal(15, 2), default=0.00)
    
    # معلومات بنكية
    bank_name = Column(String(100), nullable=True)
    bank_account = Column(String(50), nullable=True)
    iban = Column(String(50), nullable=True)
    swift_code = Column(String(20), nullable=True)
    
    # تواريخ مهمة
    join_date = Column(DateTime, nullable=True)
    contract_start = Column(DateTime, nullable=True)
    contract_end = Column(DateTime, nullable=True)
    last_activity = Column(DateTime, nullable=True)
    
    # إعدادات الوكيل
    can_issue_tickets = Column(Boolean, default=False)
    can_cancel_bookings = Column(Boolean, default=False)
    can_modify_bookings = Column(Boolean, default=False)
    requires_approval = Column(Boolean, default=True)
    
    # معلومات إضافية
    preferred_currency = Column(String(3), default='USD')
    time_zone = Column(String(50), nullable=True)
    language = Column(String(10), default='ar')
    
    # الوكيل الرئيسي (للوكلاء الفرعيين)
    parent_agent_id = Column(Integer, ForeignKey('agents.id'), nullable=True)
    
    # العلاقات
    parent_agent = relationship("Agent", remote_side="Agent.id")
    sub_agents = relationship("Agent", remote_side="Agent.parent_agent_id")
    bookings = relationship("Booking", back_populates="agent")
    commissions = relationship("AgentCommission", back_populates="agent")
    payments = relationship("AgentPayment", back_populates="agent")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        if self.agent_type == AgentType.COMPANY:
            return self.company_name
        return f"{self.first_name or ''} {self.last_name or ''}".strip()
    
    @property
    def display_name(self):
        """الاسم المعروض"""
        return self.full_name or self.agent_code
    
    @property
    def commission_balance(self):
        """رصيد العمولة المستحقة"""
        return self.total_commission - sum(p.amount for p in self.payments)
    
    def __repr__(self):
        return f"<Agent(code='{self.agent_code}', name='{self.display_name}')>"

class AgentCommission(BaseModel):
    """
    عمولات الوكلاء
    """
    __tablename__ = "agent_commissions"
    
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=False)
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=False)
    
    # تفاصيل العمولة
    commission_type = Column(Enum(CommissionType), nullable=False)
    commission_rate = Column(Decimal(5, 2), nullable=True)  # للنسبة المئوية
    commission_amount = Column(Decimal(12, 2), nullable=False)
    base_amount = Column(Decimal(12, 2), nullable=False)  # المبلغ الأساسي للحساب
    
    # حالة العمولة
    is_paid = Column(Boolean, default=False)
    payment_date = Column(DateTime, nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # تواريخ
    earned_date = Column(DateTime, nullable=False)  # تاريخ استحقاق العمولة
    due_date = Column(DateTime, nullable=True)      # تاريخ الاستحقاق للدفع
    
    # العلاقات
    agent = relationship("Agent", back_populates="commissions")
    booking = relationship("Booking")
    
    def __repr__(self):
        return f"<AgentCommission(agent_id={self.agent_id}, amount={self.commission_amount})>"

class AgentPayment(BaseModel):
    """
    مدفوعات الوكلاء
    """
    __tablename__ = "agent_payments"
    
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=False)
    payment_method = Column(String(50), nullable=False)  # bank_transfer, cash, check
    
    amount = Column(Decimal(12, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    exchange_rate = Column(Decimal(10, 4), default=1.0000)
    
    payment_date = Column(DateTime, nullable=False)
    reference_number = Column(String(100), nullable=True)
    
    # معلومات إضافية
    description = Column(Text, nullable=True)
    bank_charges = Column(Decimal(10, 2), default=0.00)
    
    # العلاقات
    agent = relationship("Agent", back_populates="payments")
    
    def __repr__(self):
        return f"<AgentPayment(agent_id={self.agent_id}, amount={self.amount})>"

class AgentContract(BaseModel):
    """
    عقود الوكلاء
    """
    __tablename__ = "agent_contracts"
    
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=False)
    contract_number = Column(String(50), unique=True, nullable=False)
    contract_type = Column(String(50), nullable=False)  # exclusive, non-exclusive
    
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=True)
    auto_renewal = Column(Boolean, default=False)
    
    # شروط العقد
    commission_structure = Column(Text, nullable=True)  # JSON أو نص يوضح هيكل العمولة
    credit_limit = Column(Decimal(15, 2), nullable=True)
    payment_terms = Column(Integer, nullable=True)  # أيام الدفع
    
    # الأهداف والحوافز
    monthly_target = Column(Decimal(15, 2), nullable=True)
    yearly_target = Column(Decimal(15, 2), nullable=True)
    bonus_rate = Column(Decimal(5, 2), nullable=True)
    
    # ملفات العقد
    contract_file = Column(String(255), nullable=True)
    
    # حالة العقد
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    agent = relationship("Agent")
    
    @property
    def is_expired(self):
        """هل العقد منتهي الصلاحية"""
        if not self.end_date:
            return False
        from datetime import datetime
        return datetime.now() > self.end_date
    
    def __repr__(self):
        return f"<AgentContract(number='{self.contract_number}', agent='{self.agent.display_name}')>"

class AgentPerformance(BaseModel):
    """
    أداء الوكلاء
    """
    __tablename__ = "agent_performance"
    
    agent_id = Column(Integer, ForeignKey('agents.id'), nullable=False)
    period_type = Column(String(20), nullable=False)  # monthly, quarterly, yearly
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # إحصائيات الأداء
    total_bookings = Column(Integer, default=0)
    total_sales = Column(Decimal(15, 2), default=0.00)
    total_commission = Column(Decimal(15, 2), default=0.00)
    cancelled_bookings = Column(Integer, default=0)
    
    # معدلات الأداء
    conversion_rate = Column(Decimal(5, 2), default=0.00)  # معدل التحويل
    average_booking_value = Column(Decimal(12, 2), default=0.00)
    customer_satisfaction = Column(Decimal(3, 2), default=0.00)  # من 0 إلى 5
    
    # تحقيق الأهداف
    target_achievement = Column(Decimal(5, 2), default=0.00)  # نسبة تحقيق الهدف
    bonus_earned = Column(Decimal(12, 2), default=0.00)
    
    # العلاقات
    agent = relationship("Agent")
    
    def __repr__(self):
        return f"<AgentPerformance(agent_id={self.agent_id}, period='{self.period_type}')>"