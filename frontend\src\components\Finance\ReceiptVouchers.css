/* Receipt Vouchers Styles */

.receipt-vouchers {
  padding: var(--space-6);
  max-width: var(--container-max-width);
  margin: 0 auto;
  min-height: calc(100vh - var(--navbar-height));
}

/* ===== HEADER ===== */
.vouchers-header {
  margin-bottom: var(--space-6);
  padding: var(--space-6);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  gap: var(--space-6);
}

.header-title h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  margin: 0 0 var(--space-2) 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-title p {
  color: var(--neutral-600);
  font-size: var(--text-base);
  margin: 0;
}

.header-actions {
  flex-shrink: 0;
}

.btn-icon {
  margin-left: var(--space-2);
}

/* ===== STATISTICS ===== */
.vouchers-stats {
  margin-bottom: var(--space-6);
}

.stat-card {
  transition: all var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-icon {
  font-size: var(--text-3xl);
  flex-shrink: 0;
}

.stat-details {
  flex: 1;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-600);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
}

/* ===== FORM ===== */
.voucher-form-card {
  margin-bottom: var(--space-6);
  border: 2px solid var(--primary-200);
}

.voucher-form {
  /* Grid handled by ModernGrid */
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group.full-width {
  grid-column: span 3;
}

.form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
}

.form-input {
  padding: var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--neutral-800);
  background: var(--neutral-0);
  transition: all var(--transition-fast);
  font-family: var(--font-family-arabic);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.form-input:hover {
  border-color: var(--primary-400);
}

.form-input.disabled {
  background: var(--neutral-100);
  color: var(--neutral-500);
  cursor: not-allowed;
}

.form-input::placeholder {
  color: var(--neutral-500);
}

textarea.form-input {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--neutral-200);
}

/* ===== FILTERS ===== */
.filters-card {
  margin-bottom: var(--space-6);
}

.filters-grid {
  /* Grid handled by ModernGrid */
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
}

/* ===== VOUCHERS TABLE ===== */
.vouchers-list-card {
  /* Card styling handled by ModernCard */
}

.vouchers-table-container {
  overflow-x: auto;
}

.vouchers-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.vouchers-table th {
  background: var(--neutral-50);
  color: var(--neutral-700);
  font-weight: var(--font-semibold);
  padding: var(--space-4);
  text-align: right;
  border-bottom: 2px solid var(--neutral-200);
  white-space: nowrap;
}

.vouchers-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--neutral-200);
  vertical-align: top;
}

.vouchers-table tr:hover {
  background: var(--neutral-50);
}

.vouchers-table tr.cancelled-row {
  background: var(--error-50);
  opacity: 0.7;
}

.vouchers-table tr.cancelled-row:hover {
  background: var(--error-100);
}

/* Table Cell Styles */
.voucher-number {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  font-family: monospace;
}

/* Currency Breakdown */
.currency-breakdown {
  margin-top: var(--space-2);
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.currency-item {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* Amount Display */
.amount-display {
  text-align: left;
}

.primary-amount {
  font-weight: var(--font-bold);
  color: var(--success-600);
  font-family: monospace;
  font-size: var(--text-base);
}

.converted-amount {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  font-family: monospace;
  margin-top: var(--space-1);
  font-style: italic;
}

.customer-info {
  /* Customer info styling */
}

.customer-name {
  font-weight: var(--font-medium);
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
}

.customer-phone {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  font-family: monospace;
}

.amount {
  font-weight: var(--font-bold);
  color: var(--success-600);
  text-align: left;
  font-family: monospace;
}

.description {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Payment Method Badges */
.payment-method {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  text-transform: uppercase;
}

.payment-method--cash {
  background: var(--success-100);
  color: var(--success-700);
}

.payment-method--bank_transfer {
  background: var(--primary-100);
  color: var(--primary-700);
}

.payment-method--credit_card {
  background: var(--warning-100);
  color: var(--warning-700);
}

.payment-method--check {
  background: var(--info-100);
  color: var(--info-700);
}

/* Status Badges */
.status {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  text-transform: uppercase;
}

.status--active {
  background: var(--success-100);
  color: var(--success-700);
}

.status--cancelled {
  background: var(--error-100);
  color: var(--error-700);
}

/* Actions */
.actions {
  display: flex;
  gap: var(--space-2);
  justify-content: center;
}

.action-btn {
  background: none;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.action-btn:hover {
  transform: scale(1.1);
}

.edit-btn:hover {
  background: var(--primary-100);
}

.cancel-btn:hover {
  background: var(--error-100);
}

.print-btn:hover {
  background: var(--info-100);
}

.delete-btn:hover {
  background: var(--error-100);
}

/* No Data */
.no-data {
  text-align: center;
  padding: var(--space-12);
  color: var(--neutral-600);
}

.no-data-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
}

.no-data h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin: 0 0 var(--space-2) 0;
  color: var(--neutral-700);
}

.no-data p {
  font-size: var(--text-base);
  margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .vouchers-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .voucher-form {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-group.full-width {
    grid-column: span 2;
  }
  
  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .receipt-vouchers {
    padding: var(--space-4);
  }
  
  .vouchers-header {
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }
  
  .header-title h1 {
    font-size: var(--text-2xl);
  }
  
  .vouchers-stats {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .voucher-form {
    grid-template-columns: 1fr;
  }
  
  .form-group.full-width {
    grid-column: span 1;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .vouchers-table {
    font-size: var(--text-xs);
  }
  
  .vouchers-table th,
  .vouchers-table td {
    padding: var(--space-2);
  }
  
  .description {
    max-width: 120px;
  }
  
  .actions {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--text-sm);
  }
}

@media (max-width: 480px) {
  .receipt-vouchers {
    padding: var(--space-3);
  }
  
  .vouchers-header {
    padding: var(--space-3);
  }
  
  .header-title h1 {
    font-size: var(--text-xl);
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
  
  .stat-icon {
    font-size: var(--text-2xl);
  }
  
  .stat-value {
    font-size: var(--text-xl);
  }
  
  .vouchers-table-container {
    font-size: var(--text-xs);
  }
  
  /* Hide some columns on very small screens */
  .vouchers-table th:nth-child(3),
  .vouchers-table td:nth-child(3),
  .vouchers-table th:nth-child(6),
  .vouchers-table td:nth-child(6) {
    display: none;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .header-title h1 {
    color: var(--neutral-200);
  }
  
  .header-title p {
    color: var(--neutral-400);
  }
  
  .stat-value {
    color: var(--primary-400);
  }
  
  .stat-label {
    color: var(--neutral-400);
  }
  
  .form-group label {
    color: var(--neutral-300);
  }
  
  .form-input {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .form-input:focus {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px var(--primary-800);
  }
  
  .form-input.disabled {
    background: var(--neutral-700);
    color: var(--neutral-500);
  }
  
  .vouchers-table th {
    background: var(--neutral-700);
    color: var(--neutral-300);
    border-color: var(--neutral-600);
  }
  
  .vouchers-table td {
    border-color: var(--neutral-600);
  }
  
  .vouchers-table tr:hover {
    background: var(--neutral-700);
  }
  
  .vouchers-table tr.cancelled-row {
    background: var(--error-800);
  }
  
  .vouchers-table tr.cancelled-row:hover {
    background: var(--error-700);
  }
  
  .voucher-number {
    color: var(--primary-400);
  }
  
  .customer-name {
    color: var(--neutral-200);
  }
  
  .customer-phone {
    color: var(--neutral-400);
  }
  
  .amount {
    color: var(--success-400);
  }
  
  .no-data h3 {
    color: var(--neutral-300);
  }
  
  .no-data p {
    color: var(--neutral-400);
  }
}