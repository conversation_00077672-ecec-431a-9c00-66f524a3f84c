/* Modern Travel Layout Styles */

.modern-travel-layout {
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-50) 100%);
  min-height: 100vh;
}

/* ===== HEADER STYLES ===== */
.modern-layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-sm);
}

.modern-layout-header__left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex: 1;
}

.modern-layout-header__center {
  display: flex;
  justify-content: center;
  flex: 2;
}

.modern-layout-header__right {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  justify-content: flex-end;
}

.sidebar-toggle {
  background: var(--gradient-primary);
  color: var(--neutral-0);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--neutral-600);
}

.breadcrumb__item {
  font-weight: var(--font-medium);
}

.breadcrumb__item--current {
  color: var(--primary-600);
  font-weight: var(--font-semibold);
}

.breadcrumb__separator {
  color: var(--neutral-400);
  font-size: var(--text-lg);
}

/* Search Container */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 400px;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-12) var(--space-3) var(--space-4);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-full);
  background: var(--neutral-0);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.search-btn {
  position: absolute;
  left: var(--space-2);
  background: transparent;
  border: none;
  color: var(--neutral-500);
}

/* Notification Button */
.notification-btn {
  position: relative;
  background: var(--gradient-secondary);
  color: var(--neutral-0);
  border-radius: var(--radius-full);
}

.notification-btn::after {
  content: '';
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  background: var(--error-500);
  border-radius: var(--radius-full);
  border: 2px solid var(--neutral-0);
}

/* Theme Toggle في الهيدر */
.header-theme-toggle {
  margin-left: 12px;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
}

.header-theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2) !important;
}

.header-theme-toggle::before {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* User Menu */
.user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-4);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.user-menu:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  color: var(--neutral-0);
  font-weight: var(--font-bold);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  line-height: 1;
}

.user-role {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  line-height: 1;
}

/* ===== SIDEBAR STYLES ===== */
.modern-layout-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(180deg, var(--neutral-0) 0%, var(--neutral-50) 100%);
  border-left: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-xl);
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--gradient-primary);
  color: var(--neutral-0);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo-icon {
  font-size: var(--text-3xl);
  animation: pulse 2s infinite;
}

.logo-text h3 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  line-height: 1;
}

.logo-text p {
  margin: 0;
  font-size: var(--text-xs);
  opacity: 0.9;
  line-height: 1;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-6);
  background: none;
  border: none;
  text-align: right;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  transition: all var(--transition-fast);
}

.nav-link:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(-2px);
}

.nav-link:hover::before {
  background: var(--primary-500);
}

.nav-link--active {
  background: var(--gradient-primary);
  color: var(--neutral-0);
  font-weight: var(--font-semibold);
}

.nav-link--active::before {
  background: var(--accent-500);
}

/* Color variants for nav links */
.nav-link--primary:hover { background: rgba(59, 130, 246, 0.1); }
.nav-link--secondary:hover { background: rgba(168, 85, 247, 0.1); }
.nav-link--success:hover { background: rgba(34, 197, 94, 0.1); }
.nav-link--warning:hover { background: rgba(245, 158, 11, 0.1); }
.nav-link--info:hover { background: rgba(59, 130, 246, 0.1); }

.nav-icon {
  font-size: var(--text-xl);
  min-width: 24px;
  text-align: center;
}

.nav-text {
  flex: 1;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.nav-arrow {
  font-size: var(--text-xs);
  transition: transform var(--transition-fast);
}

.nav-arrow--expanded {
  transform: rotate(180deg);
}

/* Submenu */
.submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  background: var(--neutral-50);
  border-top: 1px solid var(--neutral-200);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 300px;
  }
}

.submenu-item {
  /* No additional styles needed */
}

.submenu-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-2) var(--space-6) var(--space-2) var(--space-12);
  background: none;
  border: none;
  text-align: right;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

.submenu-link:hover {
  background: var(--neutral-100);
  color: var(--primary-600);
  transform: translateX(-2px);
}

.submenu-link--active {
  background: var(--primary-100);
  color: var(--primary-700);
  font-weight: var(--font-semibold);
}

.submenu-icon {
  font-size: var(--text-base);
  min-width: 20px;
  text-align: center;
}

.submenu-text {
  flex: 1;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--neutral-200);
  background: var(--neutral-50);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.sidebar-stats {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-label {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
}

.stats-value {
  font-size: var(--text-sm);
  color: var(--primary-600);
  font-weight: var(--font-bold);
}

.logout-btn {
  background: var(--gradient-error);
  color: var(--neutral-0);
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== PAGE CONTENT ===== */
.page-content {
  padding: var(--space-6);
  background: transparent;
  min-height: calc(100vh - 80px);
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .modern-layout-header__center {
    display: none;
  }
  
  .modern-layout-header__left,
  .modern-layout-header__right {
    flex: 1;
  }
  
  .breadcrumb {
    display: none;
  }
}

@media (max-width: 768px) {
  .modern-layout-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .modern-layout-header__right {
    gap: var(--space-2);
  }
  
  .user-details {
    display: none;
  }
  
  .page-content {
    padding: var(--space-4);
  }
  
  .sidebar-header {
    padding: var(--space-4);
  }
  
  .nav-link {
    padding: var(--space-3) var(--space-4);
  }
  
  .submenu-link {
    padding: var(--space-2) var(--space-4) var(--space-2) var(--space-8);
  }
  
  .sidebar-footer {
    padding: var(--space-3) var(--space-4);
  }
}

@media (max-width: 480px) {
  .modern-layout-header {
    padding: var(--space-2) var(--space-3);
  }
  
  .breadcrumb,
  .search-container,
  .notification-btn {
    display: none;
  }
  
  .user-menu {
    padding: var(--space-2);
  }
  
  .page-content {
    padding: var(--space-3);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-travel-layout {
    background: linear-gradient(135deg, var(--neutral-900) 0%, var(--neutral-800) 100%);
  }
  
  .modern-layout-header {
    background: rgba(0, 0, 0, 0.95);
    border-bottom-color: var(--neutral-700);
  }
  
  .modern-layout-sidebar {
    background: linear-gradient(180deg, var(--neutral-800) 0%, var(--neutral-900) 100%);
    border-left-color: var(--neutral-700);
  }
  
  .sidebar-header {
    border-bottom-color: var(--neutral-700);
  }
  
  .nav-link {
    color: var(--neutral-300);
  }
  
  .nav-link:hover {
    background: rgba(59, 130, 246, 0.2);
    color: var(--neutral-100);
  }
  
  .submenu {
    background: var(--neutral-800);
    border-top-color: var(--neutral-700);
  }
  
  .submenu-link {
    color: var(--neutral-400);
  }
  
  .submenu-link:hover {
    background: var(--neutral-700);
    color: var(--primary-400);
  }
  
  .sidebar-footer {
    background: var(--neutral-800);
    border-top-color: var(--neutral-700);
  }
  
  .search-input {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .breadcrumb {
    color: var(--neutral-400);
  }
  
  .breadcrumb__item--current {
    color: var(--primary-400);
  }
  
  .user-name {
    color: var(--neutral-200);
  }
  
  .user-role {
    color: var(--neutral-400);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-layout-header,
  .modern-layout-sidebar {
    display: none !important;
  }
  
  .page-content {
    padding: 0 !important;
    margin: 0 !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .logo-icon {
    animation: none;
  }
  
  .page-content {
    animation: none;
  }
  
  .submenu {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}

/* Focus styles for keyboard navigation */
.nav-link:focus,
.submenu-link:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-layout-header,
  .modern-layout-sidebar {
    border: 2px solid currentColor;
  }
  
  .nav-link,
  .submenu-link {
    border: 1px solid transparent;
  }
  
  .nav-link:hover,
  .nav-link--active,
  .submenu-link:hover,
  .submenu-link--active {
    border-color: currentColor;
  }
}