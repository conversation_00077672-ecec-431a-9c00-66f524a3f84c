import { apiRequest } from './api';

class AuthService {
  // تسجيل الدخول
  async login(credentials) {
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    const response = await apiRequest.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }

  // تسجيل مستخدم جديد
  async register(userData) {
    const response = await apiRequest.post('/auth/register', userData);
    return response.data;
  }

  // تجديد الرمز المميز
  async refreshToken(refreshToken) {
    const response = await apiRequest.post('/auth/refresh', {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  // تسجيل الخروج
  async logout() {
    try {
      await apiRequest.post('/auth/logout');
    } catch (error) {
      // حتى لو فشل الطلب، نقوم بتسجيل الخروج محلياً
      console.error('Logout error:', error);
    }
    
    // إزالة الرموز من التخزين المحلي
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }

  // الحصول على بيانات المستخدم الحالي
  async getCurrentUser() {
    const response = await apiRequest.get('/auth/me');
    return response.data;
  }

  // طلب إعادة تعيين كلمة المرور
  async requestPasswordReset(email) {
    const response = await apiRequest.post('/auth/password-reset-request', {
      email,
    });
    return response.data;
  }

  // إعادة تعيين كلمة المرور
  async resetPassword(token, newPassword) {
    const response = await apiRequest.post('/auth/password-reset', {
      token,
      new_password: newPassword,
    });
    return response.data;
  }

  // تغيير كلمة المرور
  async changePassword(currentPassword, newPassword) {
    const response = await apiRequest.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
    return response.data;
  }

  // تفعيل الحساب
  async verifyAccount(token) {
    const response = await apiRequest.post('/auth/verify-account', {
      token,
    });
    return response.data;
  }

  // إعادة إرسال رمز التفعيل
  async resendVerification(email) {
    const response = await apiRequest.post('/auth/resend-verification', {
      email,
    });
    return response.data;
  }

  // التحقق من صحة الرمز المميز
  isTokenValid(token) {
    if (!token) return false;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  }

  // الحصول على معلومات من الرمز المميز
  getTokenInfo(token) {
    if (!token) return null;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        userId: payload.sub,
        username: payload.username,
        email: payload.email,
        isSuperuser: payload.is_superuser,
        expiresAt: new Date(payload.exp * 1000),
      };
    } catch (error) {
      return null;
    }
  }

  // التحقق من الصلاحيات
  hasPermission(permission) {
    const token = localStorage.getItem('token');
    if (!token) return false;
    
    const tokenInfo = this.getTokenInfo(token);
    if (!tokenInfo) return false;
    
    // إذا كان المستخدم مدير عام، فله جميع الصلاحيات
    if (tokenInfo.isSuperuser) return true;
    
    // هنا يمكن إضافة منطق التحقق من الصلاحيات المحددة
    // بناءً على الأدوار والصلاحيات المخزنة في الرمز المميز
    
    return false;
  }

  // التحقق من الدور
  hasRole(role) {
    const token = localStorage.getItem('token');
    if (!token) return false;
    
    const tokenInfo = this.getTokenInfo(token);
    if (!tokenInfo) return false;
    
    // منطق التحقق من الأدوار
    // يمكن تطويره بناءً على هيكل الأدوار في النظام
    
    return false;
  }
}

const authService = new AuthService();
export default authService;