# Netlify redirects file
# Handle client-side routing for React Router

# Redirect all routes to index.html for SPA
/*    /index.html   200

# API redirects (if using serverless functions)
/api/*  /.netlify/functions/:splat  200

# Security headers
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(), microphone=(), camera=()

# Cache static assets
/static/*
  Cache-Control: public, max-age=********, immutable

# Cache images
/*.png
  Cache-Control: public, max-age=********
/*.jpg
  Cache-Control: public, max-age=********
/*.jpeg
  Cache-Control: public, max-age=********
/*.gif
  Cache-Control: public, max-age=********
/*.svg
  Cache-Control: public, max-age=********
/*.webp
  Cache-Control: public, max-age=********

# Cache fonts
/*.woff
  Cache-Control: public, max-age=********
/*.woff2
  Cache-Control: public, max-age=********
/*.ttf
  Cache-Control: public, max-age=********
/*.eot
  Cache-Control: public, max-age=********

# Cache CSS and JS
/*.css
  Cache-Control: public, max-age=********
/*.js
  Cache-Control: public, max-age=********

# Don't cache HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate