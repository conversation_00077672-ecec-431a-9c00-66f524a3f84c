import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import BookingActions from '../../../components/Bookings/BookingActions';

// 🧪 اختبارات مكون إجراءات الحجوزات

// بيانات اختبار وهمية
const mockBooking = {
  id: 1,
  customerName: 'أحمد محمد',
  customerPhone: '+966501234567',
  customerEmail: '<EMAIL>',
  status: 'confirmed',
  amount: 2500,
  type: 'flight',
  destination: 'دبي'
};

const mockHandlers = {
  onView: jest.fn(),
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onPrint: jest.fn(),
  onSavePDF: jest.fn(),
  onStatusChange: jest.fn()
};

describe('BookingActions', () => {
  beforeEach(() => {
    // إعادة تعيين جميع المحاكيات قبل كل اختبار
    jest.clearAllMocks();
  });

  describe('عرض الأزرار', () => {
    test('يجب عرض جميع أزرار الإجراءات', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      expect(screen.getByLabelText('عرض التفاصيل')).toBeInTheDocument();
      expect(screen.getByLabelText('تعديل')).toBeInTheDocument();
      expect(screen.getByLabelText('حذف')).toBeInTheDocument();
      expect(screen.getByLabelText('طباعة')).toBeInTheDocument();
      expect(screen.getByLabelText('حفظ PDF')).toBeInTheDocument();
    });

    test('يجب عرض الأزرار بالأيقونات الصحيحة', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      // التحقق من وجود الأيقونات
      expect(screen.getByText('👁️')).toBeInTheDocument(); // عرض
      expect(screen.getByText('✏️')).toBeInTheDocument(); // تعديل
      expect(screen.getByText('🗑️')).toBeInTheDocument(); // حذف
      expect(screen.getByText('🖨️')).toBeInTheDocument(); // طباعة
      expect(screen.getByText('📄')).toBeInTheDocument(); // PDF
    });
  });

  describe('تنفيذ الإجراءات', () => {
    test('يجب استدعاء onView عند النقر على زر العرض', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const viewButton = screen.getByLabelText('عرض التفاصيل');
      fireEvent.click(viewButton);

      expect(mockHandlers.onView).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onView).toHaveBeenCalledWith(mockBooking);
    });

    test('يجب استدعاء onEdit عند النقر على زر التعديل', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const editButton = screen.getByLabelText('تعديل');
      fireEvent.click(editButton);

      expect(mockHandlers.onEdit).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockBooking);
    });

    test('يجب استدعاء onDelete عند النقر على زر الحذف', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const deleteButton = screen.getByLabelText('حذف');
      fireEvent.click(deleteButton);

      expect(mockHandlers.onDelete).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onDelete).toHaveBeenCalledWith(mockBooking);
    });

    test('يجب استدعاء onPrint عند النقر على زر الطباعة', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const printButton = screen.getByLabelText('طباعة');
      fireEvent.click(printButton);

      expect(mockHandlers.onPrint).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onPrint).toHaveBeenCalledWith(mockBooking);
    });

    test('يجب استدعاء onSavePDF عند النقر على زر PDF', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const pdfButton = screen.getByLabelText('حفظ PDF');
      fireEvent.click(pdfButton);

      expect(mockHandlers.onSavePDF).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onSavePDF).toHaveBeenCalledWith(mockBooking);
    });
  });

  describe('تغيير حالة الحجز', () => {
    test('يجب عرض قائمة تغيير الحالة', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          showStatusChange={true}
        />
      );

      expect(screen.getByDisplayValue('confirmed')).toBeInTheDocument();
    });

    test('يجب استدعاء onStatusChange عند تغيير الحالة', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          showStatusChange={true}
        />
      );

      const statusSelect = screen.getByDisplayValue('confirmed');
      fireEvent.change(statusSelect, { target: { value: 'cancelled' } });

      expect(mockHandlers.onStatusChange).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onStatusChange).toHaveBeenCalledWith(mockBooking.id, 'cancelled');
    });

    test('يجب عرض جميع خيارات الحالة', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          showStatusChange={true}
        />
      );

      const statusSelect = screen.getByDisplayValue('confirmed');
      
      // التحقق من وجود جميع الخيارات
      expect(statusSelect).toContainHTML('<option value="pending">معلق</option>');
      expect(statusSelect).toContainHTML('<option value="confirmed">مؤكد</option>');
      expect(statusSelect).toContainHTML('<option value="cancelled">ملغي</option>');
      expect(statusSelect).toContainHTML('<option value="completed">مكتمل</option>');
    });
  });

  describe('الأزرار المخصصة', () => {
    test('يجب إخفاء أزرار معينة عند الطلب', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          hideEdit={true}
          hideDelete={true}
        />
      );

      expect(screen.queryByLabelText('تعديل')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('حذف')).not.toBeInTheDocument();
      
      // يجب أن تبقى الأزرار الأخرى
      expect(screen.getByLabelText('عرض التفاصيل')).toBeInTheDocument();
      expect(screen.getByLabelText('طباعة')).toBeInTheDocument();
    });

    test('يجب عرض أزرار إضافية عند توفيرها', () => {
      const customActions = [
        {
          label: 'إجراء مخصص',
          icon: '⭐',
          onClick: jest.fn(),
          className: 'custom-action'
        }
      ];

      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          customActions={customActions}
        />
      );

      expect(screen.getByLabelText('إجراء مخصص')).toBeInTheDocument();
      expect(screen.getByText('⭐')).toBeInTheDocument();
    });
  });

  describe('حالات التحميل', () => {
    test('يجب تعطيل الأزرار أثناء التحميل', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          loading={true}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeDisabled();
      });
    });

    test('يجب عرض مؤشر التحميل', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
          loading={true}
        />
      );

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });
  });

  describe('إمكانية الوصول', () => {
    test('يجب أن تحتوي الأزرار على تسميات صحيحة', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      expect(screen.getByLabelText('عرض التفاصيل')).toHaveAttribute('aria-label', 'عرض التفاصيل');
      expect(screen.getByLabelText('تعديل')).toHaveAttribute('aria-label', 'تعديل');
      expect(screen.getByLabelText('حذف')).toHaveAttribute('aria-label', 'حذف');
    });

    test('يجب دعم التنقل بلوحة المفاتيح', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const viewButton = screen.getByLabelText('عرض التفاصيل');
      
      // التحقق من إمكانية التركيز
      viewButton.focus();
      expect(viewButton).toHaveFocus();
      
      // محاكاة ضغط Enter
      fireEvent.keyDown(viewButton, { key: 'Enter', code: 'Enter' });
      expect(mockHandlers.onView).toHaveBeenCalledTimes(1);
    });
  });

  describe('التفاعل مع الماوس', () => {
    test('يجب تغيير المظهر عند التمرير', () => {
      render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const viewButton = screen.getByLabelText('عرض التفاصيل');
      
      fireEvent.mouseEnter(viewButton);
      expect(viewButton).toHaveClass('action-btn-hover');
      
      fireEvent.mouseLeave(viewButton);
      expect(viewButton).not.toHaveClass('action-btn-hover');
    });
  });

  describe('معالجة الأخطاء', () => {
    test('يجب عدم تعطل المكون عند عدم توفير معالجات', () => {
      expect(() => {
        render(<BookingActions booking={mockBooking} />);
      }).not.toThrow();
    });

    test('يجب عدم تعطل المكون عند توفير بيانات حجز غير صالحة', () => {
      const invalidBooking = null;
      
      expect(() => {
        render(
          <BookingActions 
            booking={invalidBooking} 
            {...mockHandlers}
          />
        );
      }).not.toThrow();
    });
  });

  describe('الأداء', () => {
    test('يجب عدم إعادة الرسم غير الضرورية', () => {
      const { rerender } = render(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      const viewButton = screen.getByLabelText('عرض التفاصيل');
      const initialRender = viewButton.innerHTML;

      // إعادة الرسم بنفس البيانات
      rerender(
        <BookingActions 
          booking={mockBooking} 
          {...mockHandlers}
        />
      );

      expect(viewButton.innerHTML).toBe(initialRender);
    });
  });
});
