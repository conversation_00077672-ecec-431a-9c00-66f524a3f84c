import React, { useState, useEffect } from 'react';
import './AdvancedSettings.css';

// مكون إدارة الأذونات المتقدمة
export const PermissionsManager = () => {
  const [roles, setRoles] = useState([
    {
      id: 1,
      name: 'مدير النظام',
      code: 'admin',
      permissions: {
        users: { create: true, read: true, update: true, delete: true },
        bookings: { create: true, read: true, update: true, delete: true },
        payments: { create: true, read: true, update: true, delete: true },
        reports: { create: true, read: true, update: true, delete: true },
        settings: { create: true, read: true, update: true, delete: true }
      }
    },
    {
      id: 2,
      name: 'مدير',
      code: 'manager',
      permissions: {
        users: { create: true, read: true, update: true, delete: false },
        bookings: { create: true, read: true, update: true, delete: true },
        payments: { create: true, read: true, update: true, delete: false },
        reports: { create: false, read: true, update: false, delete: false },
        settings: { create: false, read: true, update: false, delete: false }
      }
    },
    {
      id: 3,
      name: 'موظف',
      code: 'employee',
      permissions: {
        users: { create: false, read: true, update: false, delete: false },
        bookings: { create: true, read: true, update: true, delete: false },
        payments: { create: true, read: true, update: false, delete: false },
        reports: { create: false, read: true, update: false, delete: false },
        settings: { create: false, read: false, update: false, delete: false }
      }
    }
  ]);

  const [selectedRole, setSelectedRole] = useState(null);
  const [showAddRole, setShowAddRole] = useState(false);
  const [newRole, setNewRole] = useState({
    name: '',
    code: '',
    permissions: {
      users: { create: false, read: false, update: false, delete: false },
      bookings: { create: false, read: false, update: false, delete: false },
      payments: { create: false, read: false, update: false, delete: false },
      reports: { create: false, read: false, update: false, delete: false },
      settings: { create: false, read: false, update: false, delete: false }
    }
  });

  const modules = [
    { key: 'users', name: 'إدارة المستخدمين', icon: '👥' },
    { key: 'bookings', name: 'إدارة الحجوزات', icon: '📅' },
    { key: 'payments', name: 'إدارة المدفوعات', icon: '💳' },
    { key: 'reports', name: 'التقارير', icon: '📊' },
    { key: 'settings', name: 'الإعدادات', icon: '⚙️' }
  ];

  const actions = [
    { key: 'create', name: 'إنشاء', color: '#28a745' },
    { key: 'read', name: 'قراءة', color: '#17a2b8' },
    { key: 'update', name: 'تحديث', color: '#ffc107' },
    { key: 'delete', name: 'حذف', color: '#dc3545' }
  ];

  const updatePermission = (roleId, module, action, value) => {
    setRoles(prev => prev.map(role => 
      role.id === roleId 
        ? {
            ...role,
            permissions: {
              ...role.permissions,
              [module]: {
                ...role.permissions[module],
                [action]: value
              }
            }
          }
        : role
    ));
  };

  const addRole = () => {
    if (newRole.name && newRole.code) {
      const role = {
        id: Date.now(),
        ...newRole
      };
      setRoles(prev => [...prev, role]);
      setNewRole({
        name: '',
        code: '',
        permissions: {
          users: { create: false, read: false, update: false, delete: false },
          bookings: { create: false, read: false, update: false, delete: false },
          payments: { create: false, read: false, update: false, delete: false },
          reports: { create: false, read: false, update: false, delete: false },
          settings: { create: false, read: false, update: false, delete: false }
        }
      });
      setShowAddRole(false);
    }
  };

  const deleteRole = (roleId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الدور؟')) {
      setRoles(prev => prev.filter(role => role.id !== roleId));
    }
  };

  return (
    <div className="permissions-manager">
      <div className="permissions-header">
        <h3>إدارة الأذونات والأدوار</h3>
        <button 
          className="add-role-btn"
          onClick={() => setShowAddRole(!showAddRole)}
        >
          {showAddRole ? '❌ إلغاء' : '➕ إضافة دور'}
        </button>
      </div>

      {showAddRole && (
        <div className="add-role-form">
          <div className="form-row">
            <input
              type="text"
              placeholder="اسم الدور"
              value={newRole.name}
              onChange={(e) => setNewRole({...newRole, name: e.target.value})}
            />
            <input
              type="text"
              placeholder="رمز الدور (بالإنجليزية)"
              value={newRole.code}
              onChange={(e) => setNewRole({...newRole, code: e.target.value})}
            />
            <button className="save-role-btn" onClick={addRole}>
              💾 حفظ الدور
            </button>
          </div>
        </div>
      )}

      <div className="roles-list">
        {roles.map(role => (
          <div key={role.id} className="role-card">
            <div className="role-header">
              <div className="role-info">
                <h4>{role.name}</h4>
                <span className="role-code">{role.code}</span>
              </div>
              <div className="role-actions">
                <button 
                  className="edit-role-btn"
                  onClick={() => setSelectedRole(selectedRole === role.id ? null : role.id)}
                >
                  {selectedRole === role.id ? '🔼 إخفاء' : '🔽 تعديل الأذونات'}
                </button>
                <button 
                  className="delete-role-btn"
                  onClick={() => deleteRole(role.id)}
                >
                  🗑️ حذف
                </button>
              </div>
            </div>

            {selectedRole === role.id && (
              <div className="permissions-grid">
                <div className="grid-header">
                  <div className="module-header">الوحدة</div>
                  {actions.map(action => (
                    <div key={action.key} className="action-header" style={{color: action.color}}>
                      {action.name}
                    </div>
                  ))}
                </div>

                {modules.map(module => (
                  <div key={module.key} className="permission-row">
                    <div className="module-name">
                      <span className="module-icon">{module.icon}</span>
                      {module.name}
                    </div>
                    {actions.map(action => (
                      <div key={action.key} className="permission-cell">
                        <label className="permission-toggle">
                          <input
                            type="checkbox"
                            checked={role.permissions[module.key][action.key]}
                            onChange={(e) => updatePermission(role.id, module.key, action.key, e.target.checked)}
                          />
                          <span className="toggle-slider" style={{backgroundColor: role.permissions[module.key][action.key] ? action.color : '#ccc'}}></span>
                        </label>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// مكون إدارة سجلات النظام
export const SystemLogs = () => {
  const [logs, setLogs] = useState([
    {
      id: 1,
      timestamp: '2024-01-15 14:30:25',
      level: 'info',
      module: 'auth',
      user: 'أحمد محمد',
      action: 'تسجيل دخول',
      details: 'تم تسجيل الدخول بنجاح من IP: *************',
      ip: '*************'
    },
    {
      id: 2,
      timestamp: '2024-01-15 14:25:10',
      level: 'warning',
      module: 'booking',
      user: 'فاطمة علي',
      action: 'محاولة حجز',
      details: 'فشل في حجز الرحلة - عدم توفر مقاعد',
      ip: '*************'
    },
    {
      id: 3,
      timestamp: '2024-01-15 14:20:45',
      level: 'error',
      module: 'payment',
      user: 'محمد سالم',
      action: 'معالجة دفعة',
      details: 'فشل في معالجة الدفعة - خطأ في بوابة الدفع',
      ip: '*************'
    },
    {
      id: 4,
      timestamp: '2024-01-15 14:15:30',
      level: 'success',
      module: 'booking',
      user: 'سارة أحمد',
      action: 'إنشاء حجز',
      details: 'تم إنشاء حجز جديد بنجاح - رقم الحجز: BK-2024-001',
      ip: '*************'
    },
    {
      id: 5,
      timestamp: '2024-01-15 14:10:15',
      level: 'info',
      module: 'system',
      user: 'النظام',
      action: 'نسخة احتياطية',
      details: 'تم إنشاء نسخة احتياطية تلقائية بنجاح',
      ip: 'localhost'
    }
  ]);

  const [filters, setFilters] = useState({
    level: 'all',
    module: 'all',
    dateFrom: '',
    dateTo: '',
    searchTerm: ''
  });

  const [currentPage, setCurrentPage] = useState(1);
  const logsPerPage = 10;

  const levels = [
    { key: 'all', name: 'جميع المستويات', color: '#6c757d' },
    { key: 'info', name: 'معلومات', color: '#17a2b8' },
    { key: 'success', name: 'نجاح', color: '#28a745' },
    { key: 'warning', name: 'تحذير', color: '#ffc107' },
    { key: 'error', name: 'خطأ', color: '#dc3545' }
  ];

  const modules = [
    { key: 'all', name: 'جميع الوحدات' },
    { key: 'auth', name: 'المصادقة' },
    { key: 'booking', name: 'الحجوزات' },
    { key: 'payment', name: 'المدفوعات' },
    { key: 'system', name: 'النظام' }
  ];

  const filteredLogs = logs.filter(log => {
    const matchesLevel = filters.level === 'all' || log.level === filters.level;
    const matchesModule = filters.module === 'all' || log.module === filters.module;
    const matchesSearch = !filters.searchTerm || 
      log.user.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      log.action.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(filters.searchTerm.toLowerCase());
    
    return matchesLevel && matchesModule && matchesSearch;
  });

  const totalPages = Math.ceil(filteredLogs.length / logsPerPage);
  const currentLogs = filteredLogs.slice(
    (currentPage - 1) * logsPerPage,
    currentPage * logsPerPage
  );

  const getLevelIcon = (level) => {
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    return icons[level] || 'ℹ️';
  };

  const getLevelColor = (level) => {
    const colors = {
      info: '#17a2b8',
      success: '#28a745',
      warning: '#ffc107',
      error: '#dc3545'
    };
    return colors[level] || '#6c757d';
  };

  const exportLogs = () => {
    const csvContent = [
      ['التاريخ والوقت', 'المستوى', 'الوحدة', 'المستخدم', 'الإجراء', 'التفاصيل', 'IP'],
      ...filteredLogs.map(log => [
        log.timestamp,
        log.level,
        log.module,
        log.user,
        log.action,
        log.details,
        log.ip
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const clearLogs = () => {
    if (window.confirm('هل أنت متأكد من حذف جميع السجلات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      setLogs([]);
    }
  };

  return (
    <div className="system-logs">
      <div className="logs-header">
        <h3>سجلات النظام</h3>
        <div className="logs-actions">
          <button className="export-logs-btn" onClick={exportLogs}>
            📤 تصدير السجلات
          </button>
          <button className="clear-logs-btn" onClick={clearLogs}>
            🗑️ مسح السجلات
          </button>
        </div>
      </div>

      <div className="logs-filters">
        <div className="filter-row">
          <select
            value={filters.level}
            onChange={(e) => setFilters({...filters, level: e.target.value})}
          >
            {levels.map(level => (
              <option key={level.key} value={level.key}>
                {level.name}
              </option>
            ))}
          </select>

          <select
            value={filters.module}
            onChange={(e) => setFilters({...filters, module: e.target.value})}
          >
            {modules.map(module => (
              <option key={module.key} value={module.key}>
                {module.name}
              </option>
            ))}
          </select>

          <input
            type="text"
            placeholder="البحث في السجلات..."
            value={filters.searchTerm}
            onChange={(e) => setFilters({...filters, searchTerm: e.target.value})}
          />
        </div>
      </div>

      <div className="logs-list">
        {currentLogs.map(log => (
          <div key={log.id} className="log-item">
            <div className="log-header">
              <div className="log-level" style={{color: getLevelColor(log.level)}}>
                {getLevelIcon(log.level)} {log.level.toUpperCase()}
              </div>
              <div className="log-timestamp">{log.timestamp}</div>
            </div>
            
            <div className="log-content">
              <div className="log-main">
                <div className="log-user-action">
                  <strong>{log.user}</strong> - {log.action}
                </div>
                <div className="log-details">{log.details}</div>
              </div>
              
              <div className="log-meta">
                <span className="log-module">{log.module}</span>
                <span className="log-ip">{log.ip}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {totalPages > 1 && (
        <div className="logs-pagination">
          <button 
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            ← السابق
          </button>
          
          <span className="page-info">
            صفحة {currentPage} من {totalPages}
          </span>
          
          <button 
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            التالي →
          </button>
        </div>
      )}
    </div>
  );
};

// مكون إدارة قوالب البريد الإلكتروني
export const EmailTemplates = () => {
  const [templates, setTemplates] = useState([
    {
      id: 1,
      name: 'تأكيد الحجز',
      subject: 'تأكيد حجزك - رقم الحجز: {{booking_number}}',
      content: `مرحباً {{customer_name}},

تم تأكيد حجزك بنجاح!

تفاصيل الحجز:
- رقم الحجز: {{booking_number}}
- التاريخ: {{booking_date}}
- الوجهة: {{destination}}
- المبلغ: {{amount}}

شكراً لاختيارك شركة شراء السياحة.

مع تحياتنا،
فريق خدمة العملاء`,
      type: 'booking_confirmation',
      isActive: true
    },
    {
      id: 2,
      name: 'تأكيد الدفع',
      subject: 'تم استلام دفعتك - رقم المعاملة: {{transaction_id}}',
      content: `عزيزي {{customer_name}},

تم استلام دفعتك بنجاح!

تفاصيل الدفعة:
- رقم المعاملة: {{transaction_id}}
- المبلغ: {{amount}}
- طريقة الدفع: {{payment_method}}
- التاريخ: {{payment_date}}

سيتم معالجة طلبك في أقرب وقت ممكن.

شكراً لثقتك بنا،
فريق المحاسبة`,
      type: 'payment_confirmation',
      isActive: true
    },
    {
      id: 3,
      name: 'تذكير بالدفع',
      subject: 'تذكير: دفعة مستحقة - رقم الحجز: {{booking_number}}',
      content: `عزيزي {{customer_name}},

نذكرك بوجود دفعة مستحقة لحجزك.

تفاصيل الدفعة المستحقة:
- رقم الحجز: {{booking_number}}
- المبلغ المستحق: {{due_amount}}
- تاريخ الاستحقاق: {{due_date}}

يرجى إتمام الدفع في أقرب وقت ممكن لتجنب إلغاء الحجز.

للدفع، يرجى زيارة: {{payment_link}}

مع تحياتنا،
فريق المحاسبة`,
      type: 'payment_reminder',
      isActive: true
    }
  ]);

  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showAddTemplate, setShowAddTemplate] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);

  const templateTypes = [
    { key: 'booking_confirmation', name: 'تأكيد الحجز' },
    { key: 'payment_confirmation', name: 'تأكيد الدفع' },
    { key: 'payment_reminder', name: 'تذكير بالدفع' },
    { key: 'booking_cancellation', name: 'إلغاء الحجز' },
    { key: 'welcome', name: 'ترحيب بالعميل الجديد' },
    { key: 'newsletter', name: 'النشرة الإخبارية' }
  ];

  const availableVariables = [
    '{{customer_name}}',
    '{{booking_number}}',
    '{{booking_date}}',
    '{{destination}}',
    '{{amount}}',
    '{{transaction_id}}',
    '{{payment_method}}',
    '{{payment_date}}',
    '{{due_amount}}',
    '{{due_date}}',
    '{{payment_link}}',
    '{{company_name}}',
    '{{company_phone}}',
    '{{company_email}}'
  ];

  const addTemplate = () => {
    const newTemplate = {
      id: Date.now(),
      name: 'قالب جديد',
      subject: '',
      content: '',
      type: 'booking_confirmation',
      isActive: true
    };
    setTemplates(prev => [...prev, newTemplate]);
    setEditingTemplate(newTemplate);
    setShowAddTemplate(false);
  };

  const updateTemplate = (templateId, updates) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId ? { ...template, ...updates } : template
    ));
  };

  const deleteTemplate = (templateId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا القالب؟')) {
      setTemplates(prev => prev.filter(template => template.id !== templateId));
      if (editingTemplate && editingTemplate.id === templateId) {
        setEditingTemplate(null);
      }
    }
  };

  const toggleTemplateStatus = (templateId) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId 
        ? { ...template, isActive: !template.isActive }
        : template
    ));
  };

  const previewTemplate = (template) => {
    const sampleData = {
      '{{customer_name}}': 'أحمد محمد',
      '{{booking_number}}': 'BK-2024-001',
      '{{booking_date}}': '2024-01-20',
      '{{destination}}': 'دبي - الإمارات',
      '{{amount}}': '2,500 ريال',
      '{{transaction_id}}': 'TXN-2024-001',
      '{{payment_method}}': 'بطاقة ائتمان',
      '{{payment_date}}': '2024-01-15',
      '{{due_amount}}': '1,250 ريال',
      '{{due_date}}': '2024-01-25',
      '{{payment_link}}': 'https://company.com/pay/123',
      '{{company_name}}': 'شركة شراء السياحة',
      '{{company_phone}}': '+966501234567',
      '{{company_email}}': '<EMAIL>'
    };

    let previewContent = template.content;
    let previewSubject = template.subject;

    Object.entries(sampleData).forEach(([variable, value]) => {
      previewContent = previewContent.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
      previewSubject = previewSubject.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
    });

    alert(`الموضوع: ${previewSubject}\n\nالمحتوى:\n${previewContent}`);
  };

  return (
    <div className="email-templates">
      <div className="templates-header">
        <h3>قوالب البريد الإلكتروني</h3>
        <button 
          className="add-template-btn"
          onClick={addTemplate}
        >
          ➕ إضافة قالب جديد
        </button>
      </div>

      <div className="templates-container">
        <div className="templates-list">
          {templates.map(template => (
            <div 
              key={template.id} 
              className={`template-item ${editingTemplate?.id === template.id ? 'active' : ''}`}
            >
              <div className="template-header">
                <div className="template-info">
                  <h4>{template.name}</h4>
                  <span className="template-type">
                    {templateTypes.find(t => t.key === template.type)?.name}
                  </span>
                </div>
                <div className="template-status">
                  <label className="status-toggle">
                    <input
                      type="checkbox"
                      checked={template.isActive}
                      onChange={() => toggleTemplateStatus(template.id)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>
              </div>

              <div className="template-actions">
                <button 
                  className="edit-btn"
                  onClick={() => setEditingTemplate(template)}
                >
                  ✏️ تعديل
                </button>
                <button 
                  className="preview-btn"
                  onClick={() => previewTemplate(template)}
                >
                  👁️ معاينة
                </button>
                <button 
                  className="delete-btn"
                  onClick={() => deleteTemplate(template.id)}
                >
                  🗑️ حذف
                </button>
              </div>
            </div>
          ))}
        </div>

        {editingTemplate && (
          <div className="template-editor">
            <div className="editor-header">
              <h4>تعديل القالب: {editingTemplate.name}</h4>
              <button 
                className="close-editor-btn"
                onClick={() => setEditingTemplate(null)}
              >
                ❌ إغلاق
              </button>
            </div>

            <div className="editor-form">
              <div className="form-group">
                <label>اسم القالب</label>
                <input
                  type="text"
                  value={editingTemplate.name}
                  onChange={(e) => updateTemplate(editingTemplate.id, { name: e.target.value })}
                />
              </div>

              <div className="form-group">
                <label>نوع القالب</label>
                <select
                  value={editingTemplate.type}
                  onChange={(e) => updateTemplate(editingTemplate.id, { type: e.target.value })}
                >
                  {templateTypes.map(type => (
                    <option key={type.key} value={type.key}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>موضوع الرسالة</label>
                <input
                  type="text"
                  value={editingTemplate.subject}
                  onChange={(e) => updateTemplate(editingTemplate.id, { subject: e.target.value })}
                  placeholder="موضوع البريد الإلكتروني"
                />
              </div>

              <div className="form-group">
                <label>محتوى الرسالة</label>
                <textarea
                  value={editingTemplate.content}
                  onChange={(e) => updateTemplate(editingTemplate.id, { content: e.target.value })}
                  placeholder="محتوى البريد الإلكتروني"
                  rows="10"
                />
              </div>

              <div className="variables-helper">
                <h5>المتغيرات المتاحة:</h5>
                <div className="variables-list">
                  {availableVariables.map(variable => (
                    <span 
                      key={variable}
                      className="variable-tag"
                      onClick={() => {
                        const textarea = document.querySelector('.template-editor textarea');
                        const start = textarea.selectionStart;
                        const end = textarea.selectionEnd;
                        const newContent = editingTemplate.content.substring(0, start) + 
                                         variable + 
                                         editingTemplate.content.substring(end);
                        updateTemplate(editingTemplate.id, { content: newContent });
                      }}
                    >
                      {variable}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// مكون إدارة التحديثات والصيانة
export const MaintenanceManager = () => {
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState('النظام قيد الصيانة. سنعود قريباً!');
  const [scheduledMaintenance, setScheduledMaintenance] = useState(null);
  const [systemHealth, setSystemHealth] = useState({
    database: { status: 'healthy', responseTime: '15ms' },
    api: { status: 'healthy', responseTime: '120ms' },
    storage: { status: 'warning', responseTime: '250ms' },
    cache: { status: 'healthy', responseTime: '5ms' }
  });

  const [updates, setUpdates] = useState([
    {
      id: 1,
      version: '2.1.0',
      title: 'تحديث أمني مهم',
      description: 'إصلاحات أمنية وتحسينات في الأداء',
      releaseDate: '2024-01-20',
      status: 'available',
      size: '25 MB',
      critical: true
    },
    {
      id: 2,
      version: '2.0.5',
      title: 'إصلاحات الأخطاء',
      description: 'إصلاح مشاكل في نظام الحجوزات',
      releaseDate: '2024-01-15',
      status: 'installed',
      size: '12 MB',
      critical: false
    }
  ]);

  const toggleMaintenanceMode = () => {
    setMaintenanceMode(!maintenanceMode);
    if (!maintenanceMode) {
      // إرسال إشعار للمستخدمين
      alert('تم تفعيل وضع الصيانة. سيتم إخطار جميع المستخدمين.');
    }
  };

  const scheduleMaintenanceWindow = () => {
    const startTime = prompt('أدخل وقت بداية الصيانة (YYYY-MM-DD HH:MM):');
    const endTime = prompt('أدخل وقت انتهاء الصيانة (YYYY-MM-DD HH:MM):');
    
    if (startTime && endTime) {
      setScheduledMaintenance({
        start: startTime,
        end: endTime,
        message: maintenanceMessage
      });
      alert('تم جدولة نافذة الصيانة بنجاح.');
    }
  };

  const installUpdate = (updateId) => {
    if (window.confirm('هل أنت متأكد من تثبيت هذا التحديث؟ قد يتطلب إعادة تشغيل النظام.')) {
      setUpdates(prev => prev.map(update => 
        update.id === updateId 
          ? { ...update, status: 'installing' }
          : update
      ));

      // محاكاة عملية التثبيت
      setTimeout(() => {
        setUpdates(prev => prev.map(update => 
          update.id === updateId 
            ? { ...update, status: 'installed' }
            : update
        ));
        alert('تم تثبيت التحديث بنجاح!');
      }, 3000);
    }
  };

  const checkForUpdates = () => {
    alert('جاري البحث عن تحديثات جديدة...');
    // محاكاة فحص التحديثات
    setTimeout(() => {
      alert('لا توجد تحديثات جديدة متاحة.');
    }, 2000);
  };

  const getHealthColor = (status) => {
    const colors = {
      healthy: '#28a745',
      warning: '#ffc107',
      error: '#dc3545'
    };
    return colors[status] || '#6c757d';
  };

  const getHealthIcon = (status) => {
    const icons = {
      healthy: '✅',
      warning: '⚠️',
      error: '❌'
    };
    return icons[status] || '❓';
  };

  return (
    <div className="maintenance-manager">
      <div className="maintenance-header">
        <h3>إدارة الصيانة والتحديثات</h3>
      </div>

      {/* وضع الصيانة */}
      <div className="maintenance-section">
        <div className="section-header">
          <h4>وضع الصيانة</h4>
          <label className="maintenance-toggle">
            <input
              type="checkbox"
              checked={maintenanceMode}
              onChange={toggleMaintenanceMode}
            />
            <span className="toggle-slider"></span>
          </label>
        </div>

        <div className="maintenance-controls">
          <div className="form-group">
            <label>رسالة الصيانة</label>
            <textarea
              value={maintenanceMessage}
              onChange={(e) => setMaintenanceMessage(e.target.value)}
              placeholder="الرسالة التي ستظهر للمستخدمين أثناء الصيانة"
              rows="3"
            />
          </div>

          <div className="maintenance-actions">
            <button 
              className="schedule-btn"
              onClick={scheduleMaintenanceWindow}
            >
              📅 جدولة نافذة صيانة
            </button>
            
            {scheduledMaintenance && (
              <div className="scheduled-info">
                <strong>صيانة مجدولة:</strong>
                <br />
                من {scheduledMaintenance.start} إلى {scheduledMaintenance.end}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* صحة النظام */}
      <div className="system-health-section">
        <h4>صحة النظام</h4>
        <div className="health-grid">
          {Object.entries(systemHealth).map(([component, health]) => (
            <div key={component} className="health-card">
              <div className="health-header">
                <span className="health-icon" style={{color: getHealthColor(health.status)}}>
                  {getHealthIcon(health.status)}
                </span>
                <span className="component-name">{component}</span>
              </div>
              <div className="health-details">
                <div className="health-status" style={{color: getHealthColor(health.status)}}>
                  {health.status}
                </div>
                <div className="response-time">
                  {health.responseTime}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* التحديثات */}
      <div className="updates-section">
        <div className="updates-header">
          <h4>تحديثات النظام</h4>
          <button 
            className="check-updates-btn"
            onClick={checkForUpdates}
          >
            🔄 فحص التحديثات
          </button>
        </div>

        <div className="updates-list">
          {updates.map(update => (
            <div key={update.id} className={`update-card ${update.critical ? 'critical' : ''}`}>
              <div className="update-header">
                <div className="update-info">
                  <h5>
                    {update.title} 
                    {update.critical && <span className="critical-badge">🔴 حرج</span>}
                  </h5>
                  <div className="update-version">الإصدار {update.version}</div>
                </div>
                <div className="update-status">
                  {update.status === 'available' && (
                    <button 
                      className="install-btn"
                      onClick={() => installUpdate(update.id)}
                    >
                      📥 تثبيت
                    </button>
                  )}
                  {update.status === 'installing' && (
                    <span className="installing-status">⏳ جاري التثبيت...</span>
                  )}
                  {update.status === 'installed' && (
                    <span className="installed-status">✅ مثبت</span>
                  )}
                </div>
              </div>
              
              <div className="update-details">
                <p>{update.description}</p>
                <div className="update-meta">
                  <span>تاريخ الإصدار: {update.releaseDate}</span>
                  <span>الحجم: {update.size}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default {
  PermissionsManager,
  SystemLogs,
  EmailTemplates,
  MaintenanceManager
};