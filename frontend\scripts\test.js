#!/usr/bin/env node

// 🧪 سكريبت تشغيل الاختبارات المحسن

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 🎯 خيارات الاختبار
const testOptions = {
  // اختبارات الوحدة
  unit: {
    testMatch: ['<rootDir>/src/**/*.test.{js,jsx}'],
    collectCoverageFrom: ['src/**/*.{js,jsx}', '!src/tests/**'],
    coverageDirectory: 'coverage/unit'
  },
  
  // اختبارات التكامل
  integration: {
    testMatch: ['<rootDir>/src/tests/integration/**/*.test.{js,jsx}'],
    collectCoverageFrom: ['src/**/*.{js,jsx}', '!src/tests/**'],
    coverageDirectory: 'coverage/integration'
  },
  
  // اختبارات E2E
  e2e: {
    testMatch: ['<rootDir>/src/tests/e2e/**/*.test.{js,jsx}'],
    collectCoverageFrom: ['src/**/*.{js,jsx}', '!src/tests/**'],
    coverageDirectory: 'coverage/e2e'
  },
  
  // جميع الاختبارات
  all: {
    testMatch: [
      '<rootDir>/src/**/*.test.{js,jsx}',
      '<rootDir>/src/tests/**/*.test.{js,jsx}'
    ],
    collectCoverageFrom: ['src/**/*.{js,jsx}', '!src/tests/**'],
    coverageDirectory: 'coverage/all'
  }
};

// 🎨 ألوان للمخرجات
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// 📝 دالة طباعة ملونة
function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 🎯 دالة تشغيل الاختبارات
function runTests(type = 'all', options = {}) {
  const testConfig = testOptions[type];
  
  if (!testConfig) {
    colorLog(`❌ نوع اختبار غير صالح: ${type}`, 'red');
    colorLog('الأنواع المتاحة: unit, integration, e2e, all', 'yellow');
    process.exit(1);
  }
  
  colorLog(`🧪 بدء تشغيل اختبارات ${type}...`, 'cyan');
  
  // إعداد الأوامر
  const jestArgs = [
    '--config', 'jest.config.js',
    '--testMatch', JSON.stringify(testConfig.testMatch),
    '--collectCoverageFrom', JSON.stringify(testConfig.collectCoverageFrom),
    '--coverageDirectory', testConfig.coverageDirectory
  ];
  
  // إضافة خيارات إضافية
  if (options.watch) {
    jestArgs.push('--watch');
  }
  
  if (options.coverage) {
    jestArgs.push('--coverage');
  }
  
  if (options.verbose) {
    jestArgs.push('--verbose');
  }
  
  if (options.silent) {
    jestArgs.push('--silent');
  }
  
  if (options.updateSnapshots) {
    jestArgs.push('--updateSnapshot');
  }
  
  if (options.bail) {
    jestArgs.push('--bail');
  }
  
  if (options.maxWorkers) {
    jestArgs.push('--maxWorkers', options.maxWorkers);
  }
  
  // تشغيل Jest
  const jest = spawn('npx', ['jest', ...jestArgs], {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  jest.on('close', (code) => {
    if (code === 0) {
      colorLog(`✅ اكتملت اختبارات ${type} بنجاح!`, 'green');
      
      // عرض تقرير التغطية إذا كان متاحاً
      if (options.coverage) {
        const coveragePath = path.join(process.cwd(), testConfig.coverageDirectory, 'lcov-report', 'index.html');
        if (fs.existsSync(coveragePath)) {
          colorLog(`📊 تقرير التغطية متاح في: ${coveragePath}`, 'blue');
        }
      }
    } else {
      colorLog(`❌ فشلت اختبارات ${type} برمز الخروج: ${code}`, 'red');
      process.exit(code);
    }
  });
  
  jest.on('error', (error) => {
    colorLog(`❌ خطأ في تشغيل الاختبارات: ${error.message}`, 'red');
    process.exit(1);
  });
}

// 📊 دالة إنشاء تقرير شامل
function generateReport() {
  colorLog('📊 إنشاء تقرير شامل للاختبارات...', 'cyan');
  
  const reportData = {
    timestamp: new Date().toISOString(),
    testTypes: {},
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      coverage: {}
    }
  };
  
  // قراءة تقارير التغطية
  Object.keys(testOptions).forEach(type => {
    const coverageFile = path.join(process.cwd(), testOptions[type].coverageDirectory, 'coverage-summary.json');
    
    if (fs.existsSync(coverageFile)) {
      try {
        const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
        reportData.testTypes[type] = coverage;
        
        // تجميع الإحصائيات
        if (coverage.total) {
          reportData.summary.coverage[type] = {
            lines: coverage.total.lines.pct,
            functions: coverage.total.functions.pct,
            branches: coverage.total.branches.pct,
            statements: coverage.total.statements.pct
          };
        }
      } catch (error) {
        colorLog(`⚠️ تعذر قراءة تقرير التغطية لـ ${type}: ${error.message}`, 'yellow');
      }
    }
  });
  
  // حفظ التقرير
  const reportPath = path.join(process.cwd(), 'coverage', 'comprehensive-report.json');
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  colorLog(`✅ تم إنشاء التقرير الشامل: ${reportPath}`, 'green');
  
  // عرض ملخص
  colorLog('\n📈 ملخص التغطية:', 'bright');
  Object.entries(reportData.summary.coverage).forEach(([type, coverage]) => {
    colorLog(`  ${type}:`, 'yellow');
    colorLog(`    الأسطر: ${coverage.lines}%`, 'cyan');
    colorLog(`    الدوال: ${coverage.functions}%`, 'cyan');
    colorLog(`    الفروع: ${coverage.branches}%`, 'cyan');
    colorLog(`    البيانات: ${coverage.statements}%`, 'cyan');
  });
}

// 🧹 دالة تنظيف ملفات الاختبار
function cleanup() {
  colorLog('🧹 تنظيف ملفات الاختبار...', 'cyan');
  
  const pathsToClean = [
    'coverage',
    'node_modules/.cache/jest',
    '.jest-cache'
  ];
  
  pathsToClean.forEach(cleanPath => {
    const fullPath = path.join(process.cwd(), cleanPath);
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
      colorLog(`✅ تم حذف: ${cleanPath}`, 'green');
    }
  });
  
  colorLog('✅ اكتمل التنظيف!', 'green');
}

// 🎯 معالج الأوامر
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';
  
  // تحليل الخيارات
  const options = {
    watch: args.includes('--watch') || args.includes('-w'),
    coverage: args.includes('--coverage') || args.includes('-c'),
    verbose: args.includes('--verbose') || args.includes('-v'),
    silent: args.includes('--silent') || args.includes('-s'),
    updateSnapshots: args.includes('--updateSnapshot') || args.includes('-u'),
    bail: args.includes('--bail') || args.includes('-b'),
    maxWorkers: args.find(arg => arg.startsWith('--maxWorkers='))?.split('=')[1]
  };
  
  // معالجة الأوامر الخاصة
  if (command === 'cleanup') {
    cleanup();
    return;
  }
  
  if (command === 'report') {
    generateReport();
    return;
  }
  
  if (command === 'help' || command === '--help' || command === '-h') {
    showHelp();
    return;
  }
  
  // تشغيل الاختبارات
  runTests(command, options);
}

// 📖 دالة عرض المساعدة
function showHelp() {
  colorLog('🧪 سكريبت تشغيل الاختبارات', 'bright');
  colorLog('');
  colorLog('الاستخدام:', 'yellow');
  colorLog('  node scripts/test.js [نوع] [خيارات]', 'cyan');
  colorLog('');
  colorLog('أنواع الاختبارات:', 'yellow');
  colorLog('  unit        - اختبارات الوحدة', 'cyan');
  colorLog('  integration - اختبارات التكامل', 'cyan');
  colorLog('  e2e         - اختبارات النهاية للنهاية', 'cyan');
  colorLog('  all         - جميع الاختبارات (افتراضي)', 'cyan');
  colorLog('');
  colorLog('أوامر خاصة:', 'yellow');
  colorLog('  cleanup     - تنظيف ملفات الاختبار', 'cyan');
  colorLog('  report      - إنشاء تقرير شامل', 'cyan');
  colorLog('  help        - عرض هذه المساعدة', 'cyan');
  colorLog('');
  colorLog('الخيارات:', 'yellow');
  colorLog('  --watch, -w           - مراقبة التغييرات', 'cyan');
  colorLog('  --coverage, -c        - إنشاء تقرير التغطية', 'cyan');
  colorLog('  --verbose, -v         - مخرجات مفصلة', 'cyan');
  colorLog('  --silent, -s          - مخرجات صامتة', 'cyan');
  colorLog('  --updateSnapshot, -u  - تحديث اللقطات', 'cyan');
  colorLog('  --bail, -b            - التوقف عند أول فشل', 'cyan');
  colorLog('  --maxWorkers=N        - عدد العمليات المتوازية', 'cyan');
  colorLog('');
  colorLog('أمثلة:', 'yellow');
  colorLog('  node scripts/test.js unit --coverage', 'cyan');
  colorLog('  node scripts/test.js integration --watch', 'cyan');
  colorLog('  node scripts/test.js all --coverage --verbose', 'cyan');
}

// 🚀 تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = {
  runTests,
  generateReport,
  cleanup
};
