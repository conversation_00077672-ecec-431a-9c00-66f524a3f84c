/* Theme Manager Styles */

.theme-manager {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.theme-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 700;
}

.theme-actions {
  display: flex;
  gap: 10px;
}

.import-theme-btn,
.reset-theme-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.import-theme-btn {
  background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(111, 66, 193, 0.3);
}

.import-theme-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(111, 66, 193, 0.4);
}

.reset-theme-btn {
  background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(253, 126, 20, 0.3);
}

.reset-theme-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(253, 126, 20, 0.4);
}

/* Themes Section */
.themes-section,
.custom-themes-section,
.font-settings-section,
.layout-settings-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
}

.themes-section h4,
.custom-themes-section h4,
.font-settings-section h4,
.layout-settings-section h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.create-theme-btn {
  padding: 10px 16px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.create-theme-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* Themes Grid */
.themes-grid,
.custom-themes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.theme-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.theme-card.active {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.theme-card.custom {
  border-style: dashed;
}

.theme-preview {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-header {
  height: 25px;
  width: 100%;
  position: relative;
}

.preview-header::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  width: 40px;
  height: 3px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
}

.preview-body {
  height: 55px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-accent {
  width: 30px;
  height: 20px;
  border-radius: 4px;
}

.theme-name {
  font-weight: 600;
  color: #495057;
  text-align: center;
  font-size: 14px;
}

.active-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.theme-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
}

.export-btn,
.delete-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.export-btn {
  background: #17a2b8;
  color: white;
}

.export-btn:hover {
  background: #138496;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

/* Create Theme Form */
.create-theme-form {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  gap: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.form-group input {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.color-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.color-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-group label {
  font-weight: 600;
  color: #495057;
  font-size: 13px;
}

.color-group input[type="color"] {
  width: 100%;
  height: 40px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-group input[type="color"]:hover {
  border-color: #667eea;
}

/* Live Preview */
.theme-preview-live {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
}

.theme-preview-live h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.live-preview {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.live-preview .preview-header {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  font-weight: 600;
}

.preview-content {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.preview-button {
  padding: 8px 16px;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.preview-text {
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.save-theme-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.save-theme-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* Font Settings */
.font-settings-grid,
.layout-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.setting-item select,
.setting-item input[type="range"] {
  padding: 10px 15px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.setting-item select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-item input[type="range"] {
  padding: 0;
  height: 6px;
  background: #e9ecef;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.setting-item input[type="range"]::-webkit-slider-thumb:hover {
  background: #5a67d8;
  transform: scale(1.1);
}

.range-value {
  font-size: 12px;
  color: #6c757d;
  font-weight: 600;
  text-align: center;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* Checkbox Items */
.checkbox-item {
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.checkbox-item label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500;
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .themes-grid,
  .custom-themes-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
  
  .color-inputs {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

@media (max-width: 768px) {
  .theme-manager {
    padding: 20px;
  }
  
  .theme-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
  
  .theme-actions {
    justify-content: center;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
  
  .themes-grid,
  .custom-themes-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .create-theme-form {
    padding: 20px;
  }
  
  .color-inputs {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
  }
  
  .font-settings-grid,
  .layout-settings-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .theme-header h3 {
    font-size: 20px;
  }
  
  .themes-grid,
  .custom-themes-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .theme-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .import-theme-btn,
  .reset-theme-btn {
    width: 100%;
    justify-content: center;
  }
  
  .color-inputs {
    grid-template-columns: 1fr 1fr;
  }
  
  .form-actions {
    margin-top: 15px;
  }
  
  .save-theme-btn {
    width: 100%;
  }
}

/* Animation for theme switching */
@keyframes themeSwitch {
  0% { opacity: 0.8; transform: scale(0.98); }
  50% { opacity: 0.9; transform: scale(1.02); }
  100% { opacity: 1; transform: scale(1); }
}

.theme-card.active {
  animation: themeSwitch 0.3s ease;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .theme-manager {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .themes-section,
  .custom-themes-section,
  .font-settings-section,
  .layout-settings-section {
    background: #4a5568;
    border-color: #718096;
  }
  
  .theme-card {
    background: #2d3748;
    border-color: #718096;
  }
  
  .create-theme-form {
    background: #2d3748;
    border-color: #718096;
  }
  
  .form-group input,
  .setting-item select {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
  
  .range-value {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
}