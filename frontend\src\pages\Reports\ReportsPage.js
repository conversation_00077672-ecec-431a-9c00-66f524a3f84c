import React, { useState, useEffect } from 'react';
import './ReportsPage.css';

const ReportsPage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedSupplierType, setSelectedSupplierType] = useState('all');
  const [reportData, setReportData] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // بيانات تجريبية للتقارير
  const suppliersData = [
    {
      id: 1,
      name: 'شركة الطيران العربية',
      type: 'airline',
      totalRevenue: 450000,
      totalBookings: 150,
      rating: 4.5,
      status: 'active',
      commission: 5.5
    },
    {
      id: 2,
      name: 'فنادق الخليج الدولية',
      type: 'hotel',
      totalRevenue: 680000,
      totalBookings: 200,
      rating: 4.8,
      status: 'active',
      commission: 7.0
    },
    {
      id: 3,
      name: 'شركة النقل السياحي',
      type: 'transport',
      totalRevenue: 120000,
      totalBookings: 80,
      rating: 4.2,
      status: 'inactive',
      commission: 4.5
    },
    {
      id: 4,
      name: 'وكالة السفر الذهبية',
      type: 'tour',
      totalRevenue: 890000,
      totalBookings: 320,
      rating: 4.7,
      status: 'active',
      commission: 6.5
    },
    {
      id: 5,
      name: 'مكتب التأشيرات السريع',
      type: 'visa',
      totalRevenue: 340000,
      totalBookings: 180,
      rating: 4.6,
      status: 'active',
      commission: 8.0
    }
  ];

  useEffect(() => {
    generateReportData();
  }, [dateRange, selectedSupplierType]); // eslint-disable-line react-hooks/exhaustive-deps

  const generateReportData = () => {
    setIsLoading(true);
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
      const filteredSuppliers = selectedSupplierType === 'all' 
        ? suppliersData 
        : suppliersData.filter(s => s.type === selectedSupplierType);

      const totalRevenue = filteredSuppliers.reduce((sum, s) => sum + s.totalRevenue, 0);
      const totalBookings = filteredSuppliers.reduce((sum, s) => sum + s.totalBookings, 0);
      const averageRating = filteredSuppliers.reduce((sum, s) => sum + s.rating, 0) / filteredSuppliers.length;
      const activeSuppliers = filteredSuppliers.filter(s => s.status === 'active').length;

      // بيانات حسب النوع
      const typeData = {};
      suppliersData.forEach(supplier => {
        if (!typeData[supplier.type]) {
          typeData[supplier.type] = {
            count: 0,
            revenue: 0,
            bookings: 0,
            avgRating: 0
          };
        }
        typeData[supplier.type].count++;
        typeData[supplier.type].revenue += supplier.totalRevenue;
        typeData[supplier.type].bookings += supplier.totalBookings;
        typeData[supplier.type].avgRating += supplier.rating;
      });

      // حساب المتوسطات
      Object.keys(typeData).forEach(type => {
        typeData[type].avgRating = typeData[type].avgRating / typeData[type].count;
      });

      setReportData({
        overview: {
          totalRevenue,
          totalBookings,
          averageRating: averageRating || 0,
          activeSuppliers,
          totalSuppliers: filteredSuppliers.length
        },
        byType: typeData,
        topPerformers: filteredSuppliers
          .sort((a, b) => b.totalRevenue - a.totalRevenue)
          .slice(0, 5),
        lowPerformers: filteredSuppliers
          .sort((a, b) => a.totalRevenue - b.totalRevenue)
          .slice(0, 5)
      });
      
      setIsLoading(false);
    }, 1000);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTypeLabel = (type) => {
    const types = {
      airline: 'شركات الطيران',
      hotel: 'الفنادق',
      transport: 'النقل',
      tour: 'الرحلات السياحية',
      visa: 'خدمات التأشيرات'
    };
    return types[type] || type;
  };

  const exportReport = (format) => {
    if (format === 'pdf') {
      alert('سيتم تصدير التقرير كملف PDF');
    } else if (format === 'excel') {
      alert('سيتم تصدير التقرير كملف Excel');
    }
  };

  const renderOverviewTab = () => (
    <div className="overview-tab">
      <div className="overview-stats">
        <div className="stat-card large">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>{formatCurrency(reportData.overview?.totalRevenue || 0)}</h3>
            <p>إجمالي الإيرادات</p>
          </div>
        </div>
        
        <div className="stat-card large">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{reportData.overview?.totalBookings || 0}</h3>
            <p>إجمالي الحجوزات</p>
          </div>
        </div>
        
        <div className="stat-card large">
          <div className="stat-icon">⭐</div>
          <div className="stat-content">
            <h3>{(reportData.overview?.averageRating || 0).toFixed(1)}</h3>
            <p>متوسط التقييم</p>
          </div>
        </div>
        
        <div className="stat-card large">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h3>{reportData.overview?.activeSuppliers || 0}</h3>
            <p>موردين نشطين</p>
          </div>
        </div>
      </div>

      <div className="simple-charts">
        <div className="chart-container">
          <h3>توزيع الإيرادات حسب النوع</h3>
          <div className="simple-bar-chart">
            {reportData.byType && Object.entries(reportData.byType).map(([type, data]) => (
              <div key={type} className="bar-item">
                <div className="bar-label">{getTypeLabel(type)}</div>
                <div className="bar-container">
                  <div 
                    className="bar-fill" 
                    style={{ 
                      width: `${(data.revenue / Math.max(...Object.values(reportData.byType).map(t => t.revenue))) * 100}%` 
                    }}
                  ></div>
                </div>
                <div className="bar-value">{formatCurrency(data.revenue)}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderSuppliersTab = () => (
    <div className="suppliers-tab">
      <div className="suppliers-analysis">
        <div className="analysis-section">
          <h3>التحليل حسب النوع</h3>
          <div className="type-analysis">
            {reportData.byType && Object.entries(reportData.byType).map(([type, data]) => (
              <div key={type} className="type-card">
                <h4>{getTypeLabel(type)}</h4>
                <div className="type-stats">
                  <div className="type-stat">
                    <span className="label">عدد الموردين:</span>
                    <span className="value">{data.count}</span>
                  </div>
                  <div className="type-stat">
                    <span className="label">الإيرادات:</span>
                    <span className="value">{formatCurrency(data.revenue)}</span>
                  </div>
                  <div className="type-stat">
                    <span className="label">الحجوزات:</span>
                    <span className="value">{data.bookings}</span>
                  </div>
                  <div className="type-stat">
                    <span className="label">متوسط التقييم:</span>
                    <span className="value">{data.avgRating.toFixed(1)} ⭐</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="analysis-section">
          <h3>أفضل الموردين أداءً</h3>
          <div className="performers-list">
            {reportData.topPerformers?.map((supplier, index) => (
              <div key={supplier.id} className="performer-item top">
                <div className="rank">#{index + 1}</div>
                <div className="performer-info">
                  <h4>{supplier.name}</h4>
                  <p>{getTypeLabel(supplier.type)}</p>
                </div>
                <div className="performer-stats">
                  <span>{formatCurrency(supplier.totalRevenue)}</span>
                  <span>{supplier.totalBookings} حجز</span>
                  <span>{supplier.rating} ⭐</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderFinancialTab = () => (
    <div className="financial-tab">
      <div className="financial-overview">
        <div className="financial-cards">
          <div className="financial-card">
            <h3>إجمالي الإيرادات</h3>
            <div className="amount">{formatCurrency(reportData.overview?.totalRevenue || 0)}</div>
            <div className="growth positive">+12.5% من الشهر الماضي</div>
          </div>
          
          <div className="financial-card">
            <h3>متوسط الإيرادات الشهرية</h3>
            <div className="amount">{formatCurrency((reportData.overview?.totalRevenue || 0) / 6)}</div>
            <div className="growth positive">+8.3% من المتوسط السابق</div>
          </div>
          
          <div className="financial-card">
            <h3>إجمالي العمولات</h3>
            <div className="amount">{formatCurrency((reportData.overview?.totalRevenue || 0) * 0.06)}</div>
            <div className="growth positive">+15.2% من الشهر الماضي</div>
          </div>
          
          <div className="financial-card">
            <h3>متوسط قيمة الحجز</h3>
            <div className="amount">{formatCurrency((reportData.overview?.totalRevenue || 0) / (reportData.overview?.totalBookings || 1))}</div>
            <div className="growth negative">-2.1% من الشهر الماضي</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPerformanceTab = () => (
    <div className="performance-tab">
      <div className="performance-metrics">
        <div className="metrics-grid">
          <div className="metric-card">
            <h3>معدل النمو الشهري</h3>
            <div className="metric-value positive">+12.5%</div>
            <div className="metric-description">نمو في الإيرادات مقارنة بالشهر الماضي</div>
          </div>
          
          <div className="metric-card">
            <h3>معدل الاحتفاظ بالموردين</h3>
            <div className="metric-value positive">95.2%</div>
            <div className="metric-description">نسبة الموردين النشطين</div>
          </div>
          
          <div className="metric-card">
            <h3>متوسط وقت الاستجابة</h3>
            <div className="metric-value neutral">2.3 ساعة</div>
            <div className="metric-description">متوسط وقت الرد على الطلبات</div>
          </div>
          
          <div className="metric-card">
            <h3>معدل رضا العملاء</h3>
            <div className="metric-value positive">4.6/5</div>
            <div className="metric-description">متوسط تقييم الموردين</div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="reports-page">
      <div className="page-header">
        <div className="header-content">
          <h1>تقارير الموردين</h1>
          <p>تحليل شامل لأداء الموردين والإيرادات</p>
        </div>
        
        <div className="header-controls">
          <div className="date-range">
            <label>من تاريخ:</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
            />
            <label>إلى تاريخ:</label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
            />
          </div>
          
          <div className="supplier-filter">
            <select
              value={selectedSupplierType}
              onChange={(e) => setSelectedSupplierType(e.target.value)}
            >
              <option value="all">جميع الأنواع</option>
              <option value="airline">شركات الطيران</option>
              <option value="hotel">الفنادق</option>
              <option value="transport">النقل</option>
              <option value="tour">الرحلات السياحية</option>
              <option value="visa">خدمات التأشيرات</option>
            </select>
          </div>
          
          <div className="export-buttons">
            <button 
              className="export-btn pdf"
              onClick={() => exportReport('pdf')}
            >
              📄 تصدير PDF
            </button>
            <button 
              className="export-btn excel"
              onClick={() => exportReport('excel')}
            >
              📊 تصدير Excel
            </button>
          </div>
        </div>
      </div>

      <div className="reports-tabs">
        <div className="tabs-header">
          <button 
            className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            📊 نظرة عامة
          </button>
          <button 
            className={`tab-btn ${activeTab === 'suppliers' ? 'active' : ''}`}
            onClick={() => setActiveTab('suppliers')}
          >
            🏢 تحليل الموردين
          </button>
          <button 
            className={`tab-btn ${activeTab === 'financial' ? 'active' : ''}`}
            onClick={() => setActiveTab('financial')}
          >
            💰 التقرير المالي
          </button>
          <button 
            className={`tab-btn ${activeTab === 'performance' ? 'active' : ''}`}
            onClick={() => setActiveTab('performance')}
          >
            📈 تقرير الأداء
          </button>
        </div>

        <div className="tabs-content">
          {isLoading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>جاري تحميل التقرير...</p>
            </div>
          ) : (
            <>
              {activeTab === 'overview' && renderOverviewTab()}
              {activeTab === 'suppliers' && renderSuppliersTab()}
              {activeTab === 'financial' && renderFinancialTab()}
              {activeTab === 'performance' && renderPerformanceTab()}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;