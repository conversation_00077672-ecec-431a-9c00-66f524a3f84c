{"name": "sharaubtravelsoft-frontend", "version": "2.0.0", "description": "نظام شراء للسفر والسياحة - واجهة أمامية حديثة ومتطورة", "private": true, "keywords": ["travel", "tourism", "booking", "react", "modern-ui", "arabic"], "author": "Sharaub Travel Software Team", "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.2", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-redux": "^8.1.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "stylis-plugin-rtl": "^2.1.1", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx --fix", "format": "prettier --write src/**/*.{js,jsx,css}", "analyze": "npm run build && npx serve -s build", "preview": "npm run build && npx serve -s build -l 3001"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "typescript": "^4.9.5"}, "proxy": "http://localhost:8000"}