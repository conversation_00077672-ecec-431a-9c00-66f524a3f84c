/* أنماط القيود اليومية المتقدمة */
.journal-entries-advanced {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* رأس الصفحة */
.entries-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  gap: 10px;
}

.header-controls .btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
  transform: translateY(-2px);
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-2px);
}

/* الإحصائيات */
.entries-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stat-card.error {
  border-left: 4px solid #e74c3c;
}

.stat-icon {
  font-size: 28px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

/* أدوات التحكم */
.entries-controls {
  margin-bottom: 20px;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-input {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
}

.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.view-mode-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-mode-btn:hover:not(.active) {
  background: #e9ecef;
}

/* عرض البيانات */
.entries-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* عرض الجدول */
.table-view {
  overflow-x: auto;
}

.entries-table {
  width: 100%;
  border-collapse: collapse;
}

.entries-table th {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.entries-table th:hover {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.entries-table td {
  padding: 12px 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.entries-table tr:hover {
  background: #f8f9fa;
}

.entries-table tr.unbalanced {
  background: #fff5f5;
  border-left: 4px solid #e74c3c;
}

.entry-id {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.description {
  text-align: right;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.amount {
  font-weight: bold;
  text-align: left;
}

.amount.debit {
  color: #27ae60;
}

.amount.credit {
  color: #e74c3c;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.balanced {
  background: #d5f4e6;
  color: #27ae60;
}

.status-badge.unbalanced {
  background: #fadbd8;
  color: #e74c3c;
}

.actions {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: scale(1.1);
}

.view-btn:hover {
  background: #3498db;
  color: white;
}

.edit-btn:hover {
  background: #f39c12;
  color: white;
}

.print-btn:hover {
  background: #9b59b6;
  color: white;
}

.delete-btn:hover {
  background: #e74c3c;
  color: white;
}

/* عرض البطاقات */
.cards-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  padding: 20px;
}

.entry-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.entry-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.entry-card.unbalanced {
  border-color: #e74c3c;
  background: #fff5f5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.entry-date {
  font-size: 12px;
  color: #7f8c8d;
}

.card-status {
  font-size: 20px;
}

.card-content {
  margin-bottom: 15px;
}

.card-content .description {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 10px;
  text-align: right;
  white-space: normal;
}

.card-content .reference {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.amounts {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.amount-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.amount-item .label {
  font-size: 12px;
  color: #7f8c8d;
}

.amount-item .value {
  font-weight: bold;
  font-size: 14px;
}

.amount-item.debit .value {
  color: #27ae60;
}

.amount-item.credit .value {
  color: #e74c3c;
}

.user,
.entries-count {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-actions .action-btn {
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
  text-align: center;
}

/* العرض الزمني */
.timeline-view {
  padding: 20px;
}

.timeline-item {
  display: flex;
  margin-bottom: 30px;
  position: relative;
}

.timeline-marker {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-left: 20px;
  margin-top: 5px;
  flex-shrink: 0;
}

.timeline-marker.balanced {
  background: #27ae60;
}

.timeline-marker.unbalanced {
  background: #e74c3c;
}

.timeline-content {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.timeline-date {
  font-size: 12px;
  color: #7f8c8d;
}

.timeline-id {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.timeline-description {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 10px;
}

.timeline-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.timeline-actions {
  display: flex;
  gap: 8px;
}

/* النماذج المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.add-entry-modal,
.edit-entry-modal,
.details-modal,
.delete-modal,
.print-modal,
.export-modal,
.filter-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.add-entry-modal,
.edit-entry-modal {
  max-width: 900px;
}

.details-modal {
  max-width: 700px;
}

.delete-modal,
.print-modal,
.export-modal,
.filter-modal {
  max-width: 500px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.modal-content {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

/* نموذج إضافة/تعديل القيد */
.entry-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.entries-section {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.section-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.entries-table-container {
  overflow-x: auto;
}

.entries-form-table {
  width: 100%;
  border-collapse: collapse;
}

.entries-form-table th {
  background: #f8f9fa;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.entries-form-table td {
  padding: 8px;
  border-bottom: 1px solid #f1f2f6;
}

.entries-form-table .form-control {
  width: 100%;
  margin: 0;
}

.amount-input {
  text-align: left;
}

.btn-sm {
  padding: 6px 10px;
  font-size: 12px;
}

.totals-summary {
  padding: 15px;
  background: #f8f9fa;
  border-top: 1px solid #ddd;
}

.totals-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.totals-row.unbalanced {
  background: #fff5f5;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #e74c3c;
}

.totals-row.balanced {
  background: #f0fff4;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #27ae60;
}

.total-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.total-item .label {
  font-size: 12px;
  color: #7f8c8d;
}

.total-item .value {
  font-weight: bold;
  font-size: 16px;
}

.total-item .value.debit {
  color: #27ae60;
}

.total-item .value.credit {
  color: #e74c3c;
}

.balance-status .status {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
}

.balance-status .status.balanced {
  background: #d5f4e6;
  color: #27ae60;
}

.balance-status .status.unbalanced {
  background: #fadbd8;
  color: #e74c3c;
}

/* نموذج عرض التفاصيل */
.entry-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.details-header {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.detail-item span {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.description-section,
.notes-section {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.description-section label,
.notes-section label {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
  display: block;
}

.description-section p,
.notes-section p {
  margin: 0;
  color: #2c3e50;
  line-height: 1.5;
}

.entries-details h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.details-table th {
  background: #f8f9fa;
  padding: 12px;
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.details-table td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.details-table tfoot tr {
  background: #f8f9fa;
  font-weight: bold;
}

.account-info {
  text-align: right;
}

.account-name {
  font-weight: bold;
  color: #2c3e50;
}

.account-code {
  font-size: 12px;
  color: #7f8c8d;
}

.audit-info h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.audit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.audit-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.audit-item label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.audit-item span {
  font-size: 14px;
  color: #2c3e50;
}

.audit-item span.balanced {
  color: #27ae60;
}

.audit-item span.unbalanced {
  color: #e74c3c;
}

/* نموذج تأكيد الحذف */
.delete-warning {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.warning-icon {
  font-size: 48px;
  color: #e74c3c;
}

.warning-text h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.warning-text p {
  margin: 0 0 15px 0;
  color: #7f8c8d;
  line-height: 1.5;
}

.entry-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  font-weight: bold;
  color: #7f8c8d;
}

.summary-item .value {
  color: #2c3e50;
}

/* نموذج خيارات الطباعة */
.print-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #2c3e50;
  cursor: pointer;
}

.option-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.print-preview {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.print-preview h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.preview-item .label {
  color: #7f8c8d;
}

.preview-item .value {
  color: #2c3e50;
  font-weight: bold;
}

/* نموذج خيارات التصدير */
.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.format-selection h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-option:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.format-option input[type="radio"] {
  width: 16px;
  height: 16px;
}

.format-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.format-icon {
  font-size: 24px;
}

.format-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.format-desc {
  font-size: 12px;
  color: #7f8c8d;
}

.export-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.export-summary h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* نموذج التصفية المتقدمة */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-inputs span {
  color: #7f8c8d;
  font-size: 14px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

/* حالة عدم وجود بيانات */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.no-data .icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-data h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.no-data p {
  margin: 0 0 20px 0;
  color: #7f8c8d;
  font-size: 16px;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .journal-entries-advanced {
    padding: 10px;
  }
  
  .entries-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .entries-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .controls-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-box {
    max-width: none;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-modes {
    flex-direction: column;
  }
  
  .entries-table {
    font-size: 12px;
  }
  
  .entries-table th,
  .entries-table td {
    padding: 8px 4px;
  }
  
  .cards-view {
    grid-template-columns: 1fr;
    padding: 10px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .totals-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .details-header {
    grid-template-columns: 1fr;
  }
  
  .audit-grid {
    grid-template-columns: 1fr;
  }
  
  .format-options {
    gap: 8px;
  }
  
  .format-option {
    padding: 10px;
  }
  
  .delete-warning {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .entries-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .header-controls {
    grid-template-columns: 1fr;
  }
  
  .entries-table th,
  .entries-table td {
    padding: 6px 2px;
    font-size: 10px;
  }
  
  .actions {
    flex-direction: column;
    gap: 2px;
  }
  
  .action-btn {
    padding: 4px 6px;
    font-size: 10px;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    padding: 15px;
  }
  
  .entries-form-table th,
  .entries-form-table td {
    padding: 4px;
    font-size: 10px;
  }
}