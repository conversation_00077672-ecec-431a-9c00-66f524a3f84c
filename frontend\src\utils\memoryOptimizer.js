import React, { useEffect, useRef, useCallback, useMemo } from 'react';

// 🧠 نظام تحسين الذاكرة وإدارة الموارد

// 📊 مراقب استخدام الذاكرة
export class MemoryMonitor {
  constructor() {
    this.observers = [];
    this.metrics = {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      timestamp: Date.now()
    };
    
    this.init();
  }

  init() {
    if ('memory' in performance) {
      this.startMonitoring();
    } else {
      console.warn('Memory API غير مدعوم في هذا المتصفح');
    }
  }

  startMonitoring() {
    setInterval(() => {
      this.updateMetrics();
    }, 5000); // كل 5 ثوان
  }

  updateMetrics() {
    if ('memory' in performance) {
      const memory = performance.memory;
      this.metrics = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        timestamp: Date.now()
      };
      
      this.notifyObservers();
      this.checkMemoryPressure();
    }
  }

  checkMemoryPressure() {
    const usagePercentage = (this.metrics.usedJSHeapSize / this.metrics.jsHeapSizeLimit) * 100;
    
    if (usagePercentage > 80) {
      this.triggerMemoryCleanup('high');
    } else if (usagePercentage > 60) {
      this.triggerMemoryCleanup('medium');
    }
  }

  triggerMemoryCleanup(level) {
    const event = new CustomEvent('memoryPressure', {
      detail: { level, metrics: this.metrics }
    });
    window.dispatchEvent(event);
  }

  subscribe(callback) {
    this.observers.push(callback);
  }

  unsubscribe(callback) {
    this.observers = this.observers.filter(obs => obs !== callback);
  }

  notifyObservers() {
    this.observers.forEach(callback => callback(this.metrics));
  }

  getMetrics() {
    return { ...this.metrics };
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 🗑️ منظف الذاكرة التلقائي
export class AutoMemoryCleaner {
  constructor() {
    this.cleanupTasks = new Set();
    this.isRunning = false;
    
    this.init();
  }

  init() {
    // الاستماع لأحداث ضغط الذاكرة
    window.addEventListener('memoryPressure', (event) => {
      this.performCleanup(event.detail.level);
    });

    // تنظيف دوري
    setInterval(() => {
      this.performRoutineCleanup();
    }, 60000); // كل دقيقة
  }

  registerCleanupTask(name, task, priority = 'medium') {
    this.cleanupTasks.add({ name, task, priority });
  }

  unregisterCleanupTask(name) {
    this.cleanupTasks = new Set(
      [...this.cleanupTasks].filter(task => task.name !== name)
    );
  }

  async performCleanup(level = 'medium') {
    if (this.isRunning) return;
    
    this.isRunning = true;
    
    try {
      const tasksToRun = this.getTasksByLevel(level);
      
      for (const task of tasksToRun) {
        try {
          await task.task();
          console.log(`✅ تم تنفيذ مهمة التنظيف: ${task.name}`);
        } catch (error) {
          console.error(`❌ فشل في تنفيذ مهمة التنظيف ${task.name}:`, error);
        }
      }
      
      // إجبار جمع القمامة إذا كان متاحاً
      if (window.gc) {
        window.gc();
      }
      
    } finally {
      this.isRunning = false;
    }
  }

  getTasksByLevel(level) {
    const priorities = {
      'low': ['low'],
      'medium': ['low', 'medium'],
      'high': ['low', 'medium', 'high']
    };
    
    const allowedPriorities = priorities[level] || ['medium'];
    
    return [...this.cleanupTasks].filter(task => 
      allowedPriorities.includes(task.priority)
    );
  }

  performRoutineCleanup() {
    this.performCleanup('low');
  }
}

// 🎯 هوك تحسين الذاكرة
export const useMemoryOptimization = (dependencies = []) => {
  const cleanupRef = useRef(new Set());
  
  useEffect(() => {
    return () => {
      // تنظيف جميع المراجع عند إلغاء التحميل
      cleanupRef.current.forEach(cleanup => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });
      cleanupRef.current.clear();
    };
  }, []);

  const addCleanup = useCallback((cleanup) => {
    cleanupRef.current.add(cleanup);
  }, []);

  const removeCleanup = useCallback((cleanup) => {
    cleanupRef.current.delete(cleanup);
  }, []);

  return { addCleanup, removeCleanup };
};

// 🔄 هوك إعادة الاستخدام المحسن
export const useOptimizedMemo = (factory, deps, options = {}) => {
  const { 
    maxAge = 300000, // 5 دقائق
    maxSize = 100 
  } = options;
  
  const cacheRef = useRef(new Map());
  const timestampRef = useRef(new Map());
  
  return useMemo(() => {
    const key = JSON.stringify(deps);
    const now = Date.now();
    
    // تنظيف الذاكرة التخزين المؤقت
    if (cacheRef.current.size > maxSize) {
      const oldestKey = [...timestampRef.current.entries()]
        .sort(([,a], [,b]) => a - b)[0][0];
      cacheRef.current.delete(oldestKey);
      timestampRef.current.delete(oldestKey);
    }
    
    // التحقق من الصلاحية
    const timestamp = timestampRef.current.get(key);
    if (timestamp && (now - timestamp) > maxAge) {
      cacheRef.current.delete(key);
      timestampRef.current.delete(key);
    }
    
    // إرجاع القيمة المخزنة أو حساب جديدة
    if (cacheRef.current.has(key)) {
      return cacheRef.current.get(key);
    }
    
    const result = factory();
    cacheRef.current.set(key, result);
    timestampRef.current.set(key, now);
    
    return result;
  }, deps);
};

// 📱 هوك تحسين الأجهزة المحمولة
export const useMobileOptimization = () => {
  const [isMobile, setIsMobile] = React.useState(false);
  const [isLowMemory, setIsLowMemory] = React.useState(false);
  
  useEffect(() => {
    // كشف الجهاز المحمول
    const checkMobile = () => {
      const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
      setIsMobile(mobile);
    };
    
    // كشف الذاكرة المنخفضة
    const checkMemory = () => {
      if ('deviceMemory' in navigator) {
        setIsLowMemory(navigator.deviceMemory < 4); // أقل من 4GB
      }
    };
    
    checkMobile();
    checkMemory();
    
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  return {
    isMobile,
    isLowMemory,
    shouldOptimize: isMobile || isLowMemory
  };
};

// 🖼️ مدير الصور المحسن
export class OptimizedImageManager {
  constructor() {
    this.imageCache = new Map();
    this.loadingImages = new Set();
    this.observer = null;
    
    this.init();
  }

  init() {
    // إعداد Intersection Observer للتحميل البطيء
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        { rootMargin: '50px' }
      );
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        this.loadImage(img);
        this.observer.unobserve(img);
      }
    });
  }

  async loadImage(img) {
    const src = img.dataset.src;
    if (!src || this.loadingImages.has(src)) return;
    
    this.loadingImages.add(src);
    
    try {
      // تحميل الصورة
      const image = new Image();
      image.onload = () => {
        img.src = src;
        img.classList.add('loaded');
        this.imageCache.set(src, image);
        this.loadingImages.delete(src);
      };
      
      image.onerror = () => {
        img.classList.add('error');
        this.loadingImages.delete(src);
      };
      
      image.src = src;
      
    } catch (error) {
      console.error('فشل في تحميل الصورة:', error);
      this.loadingImages.delete(src);
    }
  }

  observeImage(img) {
    if (this.observer) {
      this.observer.observe(img);
    } else {
      // تحميل فوري إذا لم يكن Observer مدعوماً
      this.loadImage(img);
    }
  }

  preloadImages(urls) {
    return Promise.all(
      urls.map(url => this.preloadImage(url))
    );
  }

  preloadImage(url) {
    return new Promise((resolve, reject) => {
      if (this.imageCache.has(url)) {
        resolve(this.imageCache.get(url));
        return;
      }
      
      const img = new Image();
      img.onload = () => {
        this.imageCache.set(url, img);
        resolve(img);
      };
      img.onerror = reject;
      img.src = url;
    });
  }

  clearCache() {
    this.imageCache.clear();
  }

  getCacheSize() {
    return this.imageCache.size;
  }
}

// 🎯 مكون الصورة المحسنة
export const OptimizedImage = React.memo(({ 
  src, 
  alt, 
  className = '', 
  placeholder = null,
  ...props 
}) => {
  const imgRef = useRef(null);
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  
  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;
    
    const manager = new OptimizedImageManager();
    
    const handleLoad = () => setLoaded(true);
    const handleError = () => setError(true);
    
    img.addEventListener('load', handleLoad);
    img.addEventListener('error', handleError);
    
    // بدء المراقبة للتحميل البطيء
    img.dataset.src = src;
    manager.observeImage(img);
    
    return () => {
      img.removeEventListener('load', handleLoad);
      img.removeEventListener('error', handleError);
    };
  }, [src]);
  
  return (
    <div className={`optimized-image-container ${className}`}>
      <img
        ref={imgRef}
        alt={alt}
        className={`optimized-image ${loaded ? 'loaded' : ''} ${error ? 'error' : ''}`}
        {...props}
      />
      {!loaded && !error && placeholder && (
        <div className="image-placeholder">
          {placeholder}
        </div>
      )}
      {error && (
        <div className="image-error">
          ❌ فشل في تحميل الصورة
        </div>
      )}
    </div>
  );
});

// 🚀 تهيئة نظام تحسين الذاكرة
export const initMemoryOptimization = () => {
  const monitor = new MemoryMonitor();
  const cleaner = new AutoMemoryCleaner();
  const imageManager = new OptimizedImageManager();
  
  // تسجيل مهام التنظيف الافتراضية
  cleaner.registerCleanupTask('clearImageCache', () => {
    imageManager.clearCache();
  }, 'low');
  
  cleaner.registerCleanupTask('clearConsole', () => {
    if (console.clear) console.clear();
  }, 'medium');
  
  return {
    monitor,
    cleaner,
    imageManager
  };
};

export default {
  MemoryMonitor,
  AutoMemoryCleaner,
  OptimizedImageManager,
  OptimizedImage,
  useMemoryOptimization,
  useOptimizedMemo,
  useMobileOptimization,
  initMemoryOptimization
};
