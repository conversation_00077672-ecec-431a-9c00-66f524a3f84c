.automated-transactions {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.automation-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.automation-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
}

.automation-sidebar {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-transactions {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-transactions h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.quick-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.quick-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px 10px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-icon {
  font-size: 24px;
}

.quick-name {
  font-size: 12px;
  font-weight: bold;
  color: #2c3e50;
}

.automation-stats {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.automation-stats h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #3498db;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.automation-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.predefined-rules {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.predefined-rules h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: bold;
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.rule-card {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
}

.rule-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.rule-card.revenue {
  border-left: 5px solid #27ae60;
}

.rule-card.expense {
  border-left: 5px solid #e74c3c;
}

.rule-card.transfer {
  border-left: 5px solid #3498db;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.rule-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.rule-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.4;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.active {
  background: #27ae6020;
  color: #27ae60;
}

.status-badge.inactive {
  background: #e74c3c20;
  color: #e74c3c;
}

.status-badge.success {
  background: #27ae6020;
  color: #27ae60;
}

.rule-details {
  margin-bottom: 15px;
}

.account-flow {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.from-account,
.to-account {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.from-account .label,
.to-account .label {
  font-size: 11px;
  color: #7f8c8d;
  font-weight: bold;
  text-transform: uppercase;
}

.from-account .account,
.to-account .account {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
}

.flow-arrow {
  font-size: 18px;
  color: #3498db;
  font-weight: bold;
}

.rule-actions {
  display: flex;
  gap: 10px;
}

.rule-actions .btn {
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
}

.automated-history {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.automated-history h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: bold;
}

.no-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #7f8c8d;
  text-align: center;
}

.no-transactions .icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.no-transactions h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.no-transactions p {
  margin: 0;
  font-size: 14px;
}

.transactions-table {
  overflow-x: auto;
}

.transactions-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.transactions-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
}

.transactions-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  white-space: nowrap;
}

.transaction-row:hover {
  background: #f8f9fa;
}

.transaction-row.revenue {
  border-left: 4px solid #27ae60;
}

.transaction-row.expense {
  border-left: 4px solid #e74c3c;
}

.transaction-row.transfer {
  border-left: 4px solid #3498db;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-badge.revenue {
  background: #27ae6020;
  color: #27ae60;
}

.type-badge.expense {
  background: #e74c3c20;
  color: #e74c3c;
}

.type-badge.transfer {
  background: #3498db20;
  color: #3498db;
}

.amount {
  font-weight: bold;
  color: #2c3e50;
}

/* نموذج القاعدة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.rule-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 2px solid #f1f2f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 25px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.modal-footer {
  padding: 25px;
  border-top: 2px solid #f1f2f6;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .automation-content {
    flex-direction: column;
  }
  
  .automation-sidebar {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .automation-sidebar > * {
    min-width: 250px;
  }
  
  .automation-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .rules-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-buttons {
    grid-template-columns: 1fr;
  }
  
  .account-flow {
    flex-direction: column;
    text-align: center;
  }
  
  .flow-arrow {
    transform: rotate(90deg);
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .automated-transactions {
    padding: 10px;
  }
  
  .automation-header,
  .quick-transactions,
  .automation-stats,
  .predefined-rules,
  .automated-history,
  .modal-content {
    padding: 15px;
  }
  
  .rule-modal {
    margin: 10px;
    max-width: none;
  }
  
  .automation-sidebar {
    flex-direction: column;
  }
  
  .automation-sidebar > * {
    min-width: auto;
  }
  
  .rule-actions {
    flex-direction: column;
  }
}

/* أنماط العمليات التلقائية المتقدمة */
.automated-transactions-advanced {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.automation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 24px;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  gap: 10px;
}

/* الإحصائيات المتقدمة */
.automation-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* التبويبات المحسنة */
.automation-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  background: white;
  padding: 10px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.tab-btn {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 8px;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.tab-btn:hover:not(.active) {
  background: #f8f9fa;
}

.tab-icon {
  font-size: 18px;
}

/* المعاملات السريعة المحسنة */
.quick-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.quick-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.quick-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.quick-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.quick-icon {
  font-size: 32px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(52, 152, 219, 0.1);
}

.quick-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
}

.quick-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
}

.quick-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 12px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-block {
  width: 100%;
}

/* القواعد التلقائية المحسنة */
.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.rule-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.rule-card.active {
  border-left: 4px solid #27ae60;
}

.rule-card.inactive {
  border-left: 4px solid #e74c3c;
  opacity: 0.7;
}

.rule-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.rule-icon {
  font-size: 28px;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(52, 152, 219, 0.1);
}

.rule-info {
  flex: 1;
}

.rule-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
}

.rule-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
  line-height: 1.4;
}

.rule-status {
  margin-left: auto;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d5f4e6;
  color: #27ae60;
}

.status-badge.inactive {
  background: #fadbd8;
  color: #e74c3c;
}

.rule-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.detail-item .label {
  color: #7f8c8d;
  font-weight: 500;
}

.detail-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* القوالب المحسنة */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.template-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.template-icon {
  font-size: 28px;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(52, 152, 219, 0.1);
}

.template-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
}

.template-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
}

.template-preview h5 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
}

.entries-preview {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 15px;
}

.entry-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
}

.account-name {
  color: #2c3e50;
  font-weight: 500;
}

.entry-type {
  color: #7f8c8d;
  font-size: 10px;
}

/* السجل والتقارير المحسنة */
.history-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-filters {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  width: 250px;
  font-size: 14px;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
}

.view-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.view-mode-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-mode-btn:hover:not(.active) {
  background: #e9ecef;
}

/* الجداول المحسنة */
.automation-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.automation-table th {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
}

.automation-table th:hover {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.automation-table td {
  padding: 12px 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.automation-table tr:hover {
  background: #f8f9fa;
}

.transaction-id {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-badge.quick-transaction {
  background: #d5f4e6;
  color: #27ae60;
}

.type-badge.automation-rule {
  background: #dbeafe;
  color: #3498db;
}

.amount {
  font-weight: bold;
  color: #27ae60;
}

/* البطاقات المحسنة */
.history-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.history-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.transaction-date {
  font-size: 12px;
  color: #7f8c8d;
}

.card-content {
  margin-bottom: 15px;
}

.card-content .description {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
}

.card-content .amount,
.card-content .source {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: scale(1.05);
}

.view-btn:hover {
  background: #3498db;
  color: white;
}

.print-btn:hover {
  background: #9b59b6;
  color: white;
}

.edit-btn:hover {
  background: #f39c12;
  color: white;
}

/* النماذج المنبثقة المحسنة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.print-modal,
.export-modal,
.template-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f1f2f6;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.modal-content {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #f1f2f6;
}

/* تنسيق متجاوب للمكونات المتقدمة */
@media (max-width: 768px) {
  .automated-transactions-advanced {
    padding: 10px;
  }
  
  .automation-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .automation-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    justify-content: center;
  }
  
  .quick-grid,
  .rules-grid,
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .history-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-modes {
    flex-direction: column;
  }
  
  .automation-table {
    font-size: 12px;
  }
  
  .automation-table th,
  .automation-table td {
    padding: 8px 4px;
  }
  
  .history-cards {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .automation-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .quick-card,
  .rule-card,
  .template-card {
    padding: 15px;
  }
  
  .header-controls {
    grid-template-columns: 1fr;
  }
  
  .automation-table th,
  .automation-table td {
    padding: 6px 2px;
    font-size: 10px;
  }
}