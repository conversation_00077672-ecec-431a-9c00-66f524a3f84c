import React, { useState, useEffect } from 'react';

const ReportsPage = () => {
  const [reportData, setReportData] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState('sales');
  const [dateRange, setDateRange] = useState({
    startDate: '2024-01-01',
    endDate: '2024-01-31'
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setReportData({
        salesSummary: {
          totalSales: 485000,
          totalInvoices: 127,
          averageInvoice: 3819,
          paidInvoices: 95,
          pendingInvoices: 32,
          salesByCurrency: {
            SAR: 385000,
            USD: 75000,
            YER: 25000
          },
          salesByService: {
            flight: 185000,
            umrah: 125000,
            hajj: 95000,
            car: 45000,
            bus: 25000,
            passport: 10000
          }
        },
        dailySales: [
          { date: '2024-01-01', amount: 15000, invoices: 5 },
          { date: '2024-01-02', amount: 22000, invoices: 8 },
          { date: '2024-01-03', amount: 18000, invoices: 6 },
          { date: '2024-01-04', amount: 25000, invoices: 9 },
          { date: '2024-01-05', amount: 30000, invoices: 12 },
          { date: '2024-01-06', amount: 28000, invoices: 10 },
          { date: '2024-01-07', amount: 35000, invoices: 14 }
        ],
        topCustomers: [
          { name: 'شركة السفر الذهبي', totalPurchases: 125000, invoices: 25 },
          { name: 'أحمد محمد العلي', totalPurchases: 25000, invoices: 8 },
          { name: 'فاطمة سالم الأحمد', totalPurchases: 18500, invoices: 6 },
          { name: 'خالد أحمد الزهراني', totalPurchases: 15000, invoices: 5 },
          { name: 'نورا علي السالم', totalPurchases: 12000, invoices: 4 }
        ],
        paymentMethods: {
          cash: 285000,
          bank_transfer: 150000,
          check: 50000
        },
        profitLoss: {
          revenue: 485000,
          costs: 320000,
          grossProfit: 165000,
          expenses: 85000,
          netProfit: 80000,
          profitMargin: 16.5
        },
        cashFlow: {
          cashIn: 425000,
          cashOut: 380000,
          netCashFlow: 45000,
          openingBalance: 150000,
          closingBalance: 195000
        }
      });
      setLoading(false);
    }, 1000);
  }, [dateRange]);

  const formatCurrency = (amount, currency = 'SAR') => {
    return `${amount.toLocaleString('ar-SA')} ر.س`;
  };

  const getServiceName = (service) => {
    const services = {
      flight: 'حجز طيران',
      umrah: 'حجز عمرة',
      hajj: 'حجز حج',
      car: 'تأجير سيارة',
      bus: 'حجز باص',
      passport: 'خدمات جوازات'
    };
    return services[service] || service;
  };

  const renderSalesReport = () => (
    <div style={{ display: 'grid', gap: '20px' }}>
      {/* ملخص المبيعات */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>📊 ملخص المبيعات</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '20px' 
        }}>
          {[
            { title: 'إجمالي المبيعات', value: formatCurrency(reportData.salesSummary?.totalSales), color: '#3498db', icon: '💰' },
            { title: 'عدد الفواتير', value: reportData.salesSummary?.totalInvoices, color: '#27ae60', icon: '📄' },
            { title: 'متوسط الفاتورة', value: formatCurrency(reportData.salesSummary?.averageInvoice), color: '#8e44ad', icon: '📈' },
            { title: 'فواتير مدفوعة', value: reportData.salesSummary?.paidInvoices, color: '#e67e22', icon: '✅' },
            { title: 'فواتير معلقة', value: reportData.salesSummary?.pendingInvoices, color: '#e74c3c', icon: '⏳' }
          ].map((stat, index) => (
            <div key={index} style={{
              background: '#f8f9fa',
              padding: '20px',
              borderRadius: '10px',
              border: `3px solid ${stat.color}`,
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', marginBottom: '10px' }}>{stat.icon}</div>
              <h4 style={{ color: stat.color, margin: '0 0 10px 0', fontSize: '14px' }}>{stat.title}</h4>
              <p style={{ fontSize: '18px', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>{stat.value}</p>
            </div>
          ))}
        </div>
      </div>

      {/* المبيعات حسب العملة */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>💱 المبيعات حسب العملة</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          {Object.entries(reportData.salesSummary?.salesByCurrency || {}).map(([currency, amount]) => (
            <div key={currency} style={{
              background: '#f8f9fa',
              padding: '15px',
              borderRadius: '10px',
              textAlign: 'center'
            }}>
              <h4 style={{ margin: '0 0 10px 0', color: '#3498db' }}>
                {currency === 'SAR' ? '🇸🇦 ريال سعودي' : 
                 currency === 'USD' ? '🇺🇸 دولار أمريكي' : 
                 currency === 'YER' ? '🇾🇪 ريال يمني' : currency}
              </h4>
              <p style={{ fontSize: '18px', fontWeight: 'bold', margin: 0, color: '#27ae60' }}>
                {formatCurrency(amount)}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* المبيعات حسب الخدمة */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>🛎️ المبيعات حسب الخدمة</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
          {Object.entries(reportData.salesSummary?.salesByService || {}).map(([service, amount]) => (
            <div key={service} style={{
              background: '#f8f9fa',
              padding: '15px',
              borderRadius: '10px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h4 style={{ margin: '0 0 5px 0', color: '#3498db' }}>{getServiceName(service)}</h4>
                <p style={{ fontSize: '16px', fontWeight: 'bold', margin: 0, color: '#27ae60' }}>
                  {formatCurrency(amount)}
                </p>
              </div>
              <div style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: '#3498db',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '20px'
              }}>
                {service === 'flight' ? '✈️' :
                 service === 'umrah' ? '🕋' :
                 service === 'hajj' ? '🏛️' :
                 service === 'car' ? '🚗' :
                 service === 'bus' ? '🚌' :
                 service === 'passport' ? '📘' : '🛎️'}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderCustomersReport = () => (
    <div style={{
      background: 'white',
      padding: '25px',
      borderRadius: '15px',
      boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
    }}>
      <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>👥 أفضل العملاء</h3>
      <div style={{ overflow: 'hidden', borderRadius: '10px' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ background: '#f8f9fa' }}>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>الترتيب</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>اسم العميل</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>إجمالي المشتريات</th>
              <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', color: '#2c3e50' }}>عدد الفواتير</th>
              <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold', color: '#2c3e50' }}>متوسط الفاتورة</th>
            </tr>
          </thead>
          <tbody>
            {reportData.topCustomers?.map((customer, index) => (
              <tr key={index} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '15px', textAlign: 'center' }}>
                  <span style={{
                    background: index < 3 ? '#f39c12' : '#95a5a6',
                    color: 'white',
                    padding: '5px 10px',
                    borderRadius: '50%',
                    fontWeight: 'bold'
                  }}>
                    {index + 1}
                  </span>
                </td>
                <td style={{ padding: '15px', fontWeight: 'bold' }}>{customer.name}</td>
                <td style={{ padding: '15px', color: '#27ae60', fontWeight: 'bold' }}>
                  {formatCurrency(customer.totalPurchases)}
                </td>
                <td style={{ padding: '15px', textAlign: 'center' }}>{customer.invoices}</td>
                <td style={{ padding: '15px', color: '#3498db' }}>
                  {formatCurrency(customer.totalPurchases / customer.invoices)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderProfitLossReport = () => (
    <div style={{ display: 'grid', gap: '20px' }}>
      {/* قائمة الدخل */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>📈 قائمة الدخل</h3>
        <div style={{ display: 'grid', gap: '15px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#2c3e50' }}>إجمالي الإيرادات</span>
            <span style={{ fontWeight: 'bold', color: '#27ae60' }}>{formatCurrency(reportData.profitLoss?.revenue)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#2c3e50' }}>تكلفة المبيعات</span>
            <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>({formatCurrency(reportData.profitLoss?.costs)})</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#e8f5e8', borderRadius: '8px', border: '2px solid #27ae60' }}>
            <span style={{ fontWeight: 'bold', color: '#27ae60' }}>إجمالي الربح</span>
            <span style={{ fontWeight: 'bold', color: '#27ae60' }}>{formatCurrency(reportData.profitLoss?.grossProfit)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#2c3e50' }}>المصروفات التشغيلية</span>
            <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>({formatCurrency(reportData.profitLoss?.expenses)})</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '20px', background: '#e3f2fd', borderRadius: '8px', border: '3px solid #3498db' }}>
            <span style={{ fontWeight: 'bold', color: '#3498db', fontSize: '18px' }}>صافي الربح</span>
            <span style={{ fontWeight: 'bold', color: '#3498db', fontSize: '18px' }}>{formatCurrency(reportData.profitLoss?.netProfit)}</span>
          </div>
          <div style={{ textAlign: 'center', padding: '15px', background: '#fff3cd', borderRadius: '8px', border: '2px solid #f39c12' }}>
            <span style={{ fontWeight: 'bold', color: '#f39c12', fontSize: '16px' }}>
              هامش الربح: {reportData.profitLoss?.profitMargin}%
            </span>
          </div>
        </div>
      </div>

      {/* التدفق النقدي */}
      <div style={{
        background: 'white',
        padding: '25px',
        borderRadius: '15px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>💰 التدفق النقدي</h3>
        <div style={{ display: 'grid', gap: '15px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#2c3e50' }}>الرصيد الافتتاحي</span>
            <span style={{ fontWeight: 'bold', color: '#3498db' }}>{formatCurrency(reportData.cashFlow?.openingBalance)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#e8f5e8', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#27ae60' }}>النقد الداخل</span>
            <span style={{ fontWeight: 'bold', color: '#27ae60' }}>+{formatCurrency(reportData.cashFlow?.cashIn)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#fdeaea', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>النقد الخارج</span>
            <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>-{formatCurrency(reportData.cashFlow?.cashOut)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '15px', background: '#fff3cd', borderRadius: '8px' }}>
            <span style={{ fontWeight: 'bold', color: '#f39c12' }}>صافي التدفق النقدي</span>
            <span style={{ fontWeight: 'bold', color: '#f39c12' }}>{formatCurrency(reportData.cashFlow?.netCashFlow)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', padding: '20px', background: '#e3f2fd', borderRadius: '8px', border: '3px solid #3498db' }}>
            <span style={{ fontWeight: 'bold', color: '#3498db', fontSize: '18px' }}>الرصيد الختامي</span>
            <span style={{ fontWeight: 'bold', color: '#3498db', fontSize: '18px' }}>{formatCurrency(reportData.cashFlow?.closingBalance)}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPaymentsReport = () => (
    <div style={{
      background: 'white',
      padding: '25px',
      borderRadius: '15px',
      boxShadow: '0 5px 15px rgba(0,0,0,0.1)'
    }}>
      <h3 style={{ color: '#2c3e50', marginBottom: '20px' }}>💳 طرق الدفع</h3>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        {Object.entries(reportData.paymentMethods || {}).map(([method, amount]) => {
          const methodNames = {
            cash: 'نقدي',
            bank_transfer: 'تحويل بنكي',
            check: 'شيك'
          };
          const methodIcons = {
            cash: '💵',
            bank_transfer: '🏦',
            check: '📝'
          };
          const total = Object.values(reportData.paymentMethods || {}).reduce((sum, val) => sum + val, 0);
          const percentage = ((amount / total) * 100).toFixed(1);
          
          return (
            <div key={method} style={{
              background: '#f8f9fa',
              padding: '20px',
              borderRadius: '12px',
              textAlign: 'center',
              border: '2px solid #e0e0e0'
            }}>
              <div style={{ fontSize: '40px', marginBottom: '10px' }}>{methodIcons[method]}</div>
              <h4 style={{ color: '#3498db', margin: '0 0 10px 0' }}>{methodNames[method]}</h4>
              <p style={{ fontSize: '18px', fontWeight: 'bold', margin: '0 0 5px 0', color: '#27ae60' }}>
                {formatCurrency(amount)}
              </p>
              <p style={{ fontSize: '14px', color: '#666', margin: 0 }}>
                {percentage}% من إجمالي المبيعات
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#666' }}>جاري تحميل التقارير...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '30px',
        borderRadius: '15px',
        color: 'white',
        marginBottom: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: 'bold' }}>
          📊 التقارير المالية
        </h1>
        <p style={{ margin: '10px 0 0 0', fontSize: '16px', opacity: 0.9 }}>
          تقارير شاملة ومفصلة للأداء المالي والمحاسبي
        </p>
      </div>

      {/* أدوات التحكم */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          flexWrap: 'wrap',
          gap: '15px'
        }}>
          {/* اختيار نوع التقرير */}
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            {[
              { key: 'sales', label: '📈 تقرير المبيعات', color: '#3498db' },
              { key: 'customers', label: '👥 تقرير العملاء', color: '#27ae60' },
              { key: 'profit', label: '💰 الأرباح والخسائر', color: '#e67e22' },
              { key: 'payments', label: '💳 طرق الدفع', color: '#8e44ad' }
            ].map((report) => (
              <button
                key={report.key}
                onClick={() => setSelectedReport(report.key)}
                style={{
                  background: selectedReport === report.key ? report.color : 'transparent',
                  color: selectedReport === report.key ? 'white' : report.color,
                  border: `2px solid ${report.color}`,
                  padding: '10px 20px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease'
                }}
              >
                {report.label}
              </button>
            ))}
          </div>

          {/* اختيار الفترة الزمنية */}
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <label style={{ fontWeight: 'bold', color: '#2c3e50' }}>الفترة:</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
              style={{
                padding: '8px',
                border: '2px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '14px'
              }}
            />
            <span style={{ color: '#666' }}>إلى</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
              style={{
                padding: '8px',
                border: '2px solid #e0e0e0',
                borderRadius: '6px',
                fontSize: '14px'
              }}
            />
            <button style={{
              background: '#27ae60',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}>
              📤 تصدير PDF
            </button>
          </div>
        </div>
      </div>

      {/* عرض التقرير المحدد */}
      <div>
        {selectedReport === 'sales' && renderSalesReport()}
        {selectedReport === 'customers' && renderCustomersReport()}
        {selectedReport === 'profit' && renderProfitLossReport()}
        {selectedReport === 'payments' && renderPaymentsReport()}
      </div>

      {/* CSS للأنيميشن */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default ReportsPage;
