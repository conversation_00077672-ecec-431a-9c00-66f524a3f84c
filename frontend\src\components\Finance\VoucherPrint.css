/* Voucher Print Styles - تصميم طباعة السندات المذهل */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* ===== PRINT MODAL ===== */
.voucher-print-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.print-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.print-modal-content {
  position: relative;
  width: 95vw;
  height: 95vh;
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: contentSlideIn 0.4s ease-out;
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.print-modal-header {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.print-modal-header h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  font-family: 'Cairo', sans-serif;
}

.print-modal-actions {
  display: flex;
  gap: 15px;
}

.print-btn, .close-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', sans-serif;
  display: flex;
  align-items: center;
  gap: 8px;
}

.print-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.print-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.print-preview {
  flex: 1;
  overflow: auto;
  padding: 30px;
  background: #f8fafc;
}

/* ===== VOUCHER PRINT DESIGN ===== */
.voucher-print {
  max-width: 210mm;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border: 3px solid #2563eb;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 20px 40px rgba(37, 99, 235, 0.1);
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* العلامة المائية */
.voucher-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 120px;
  color: rgba(37, 99, 235, 0.05);
  font-weight: bold;
  z-index: 1;
  pointer-events: none;
  user-select: none;
}

.voucher-content {
  position: relative;
  z-index: 2;
  padding: 40px;
}

/* ===== HEADER SECTION ===== */
.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 3px solid #2563eb;
  position: relative;
}

.voucher-header::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
}

.company-section {
  flex: 1;
  max-width: 60%;
}

.company-logo {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 60px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(37, 99, 235, 0.3));
}

.company-name {
  font-size: 32px;
  font-weight: 700;
  color: #1e40af;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(30, 64, 175, 0.1);
}

.company-name-en {
  font-size: 18px;
  color: #64748b;
  font-weight: 500;
  margin-top: 5px;
  font-style: italic;
}

.company-details {
  color: #475569;
  font-size: 15px;
  line-height: 2;
  margin-top: 15px;
}

.company-details > div {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.voucher-type-section {
  text-align: center;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  padding: 25px;
  border-radius: 20px;
  box-shadow: 0 15px 30px rgba(37, 99, 235, 0.3);
  position: relative;
  overflow: hidden;
}

.voucher-type-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.voucher-type {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.voucher-number {
  font-size: 20px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 12px 20px;
  border-radius: 12px;
  display: inline-block;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

/* ===== BODY SECTION ===== */
.voucher-body {
  margin: 40px 0;
}

.voucher-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.info-section {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 30px;
  border-radius: 15px;
  border-right: 5px solid #2563eb;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 1px 1px 2px rgba(30, 64, 175, 0.1);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.info-row:hover {
  background: rgba(37, 99, 235, 0.05);
  padding: 12px 15px;
  border-radius: 8px;
  border-bottom: 1px solid transparent;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #374151;
  font-size: 16px;
}

.info-value {
  color: #1f2937;
  font-weight: 500;
  font-size: 16px;
}

/* ===== AMOUNT SECTION ===== */
.amount-section {
  background: linear-gradient(135deg, #fef3c7, #fbbf24, #f59e0b);
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  margin: 40px 0;
  border: 3px solid #f59e0b;
  box-shadow: 0 20px 40px rgba(245, 158, 11, 0.3);
  position: relative;
  overflow: hidden;
}

.amount-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.amount-title {
  font-size: 24px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(146, 64, 14, 0.2);
}

.amount-value {
  font-size: 48px;
  font-weight: 700;
  color: #78350f;
  margin-bottom: 20px;
  font-family: 'Courier New', monospace;
  text-shadow: 3px 3px 6px rgba(120, 53, 15, 0.3);
  position: relative;
  z-index: 2;
}

.amount-words {
  font-size: 18px;
  color: #a16207;
  font-weight: 500;
  font-style: italic;
  background: rgba(255, 255, 255, 0.7);
  padding: 15px 25px;
  border-radius: 12px;
  display: inline-block;
  border: 2px solid rgba(161, 98, 7, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

/* ===== CURRENCY INFO ===== */
.currency-info {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  padding: 20px;
  border-radius: 12px;
  margin: 30px 0;
  border-right: 5px solid #10b981;
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.1);
}

.currency-rate {
  font-size: 16px;
  color: #065f46;
  font-weight: 500;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* ===== DESCRIPTION SECTION ===== */
.description-section {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  padding: 30px;
  border-radius: 15px;
  margin: 30px 0;
  border-right: 5px solid #10b981;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.description-title {
  font-size: 20px;
  font-weight: 600;
  color: #059669;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.description-text {
  font-size: 18px;
  color: #374151;
  line-height: 2;
  text-align: justify;
}

/* ===== QR SECTION ===== */
.qr-section {
  text-align: center;
  padding: 25px;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  border-radius: 15px;
  margin: 30px 0;
  border: 2px dashed #9ca3af;
}

.qr-placeholder {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  border: 3px dashed #9ca3af;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.qr-placeholder::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(156, 163, 175, 0.3), transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== FOOTER SECTION ===== */
.voucher-footer {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 3px solid #e5e7eb;
  position: relative;
}

.voucher-footer::before {
  content: '';
  position: absolute;
  top: -3px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #e5e7eb, #9ca3af, #e5e7eb);
}

.signatures {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.signature-box {
  text-align: center;
  padding: 25px;
  border: 3px dashed #9ca3af;
  border-radius: 15px;
  min-height: 100px;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  position: relative;
  transition: all 0.3s ease;
}

.signature-box:hover {
  border-color: #2563eb;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
}

.signature-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 15px;
  font-size: 16px;
}

.signature-line {
  border-bottom: 2px solid #9ca3af;
  margin: 25px 0 15px 0;
  position: relative;
}

.signature-line::before {
  content: '✍️';
  position: absolute;
  right: -15px;
  top: -15px;
  font-size: 20px;
  opacity: 0.5;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #6b7280;
  margin-top: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.print-date {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.print-date::before {
  content: '🕒';
  font-size: 16px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .print-modal-content {
    width: 98vw;
    height: 98vh;
  }
  
  .voucher-content {
    padding: 30px;
  }
  
  .voucher-info {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .signatures {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .print-modal-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }
  
  .print-modal-header h2 {
    font-size: 20px;
  }
  
  .print-modal-actions {
    width: 100%;
    justify-content: center;
  }
  
  .voucher-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .company-section {
    max-width: 100%;
  }
  
  .voucher-type-section {
    width: 100%;
  }
  
  .company-logo {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .logo-icon {
    font-size: 40px;
  }
  
  .company-name {
    font-size: 24px;
  }
  
  .voucher-type {
    font-size: 20px;
  }
  
  .amount-value {
    font-size: 32px;
  }
  
  .amount-words {
    font-size: 14px;
    padding: 10px 15px;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .voucher-print-modal {
    position: static;
    background: none;
    backdrop-filter: none;
  }
  
  .print-modal-overlay,
  .print-modal-header {
    display: none !important;
  }
  
  .print-modal-content {
    width: 100%;
    height: auto;
    background: none;
    border-radius: 0;
    box-shadow: none;
    overflow: visible;
  }
  
  .print-preview {
    padding: 0;
    background: none;
    overflow: visible;
  }
  
  .voucher-print {
    max-width: none;
    border: 2px solid #2563eb;
    border-radius: 10px;
    box-shadow: none;
    page-break-inside: avoid;
  }
  
  .voucher-content {
    padding: 20px;
  }
  
  .voucher-watermark {
    font-size: 80px;
  }
  
  .company-logo {
    gap: 15px;
  }
  
  .logo-icon {
    font-size: 40px;
  }
  
  .company-name {
    font-size: 24px;
  }
  
  .voucher-type {
    font-size: 20px;
  }
  
  .amount-value {
    font-size: 36px;
  }
  
  .signatures {
    gap: 20px;
  }
  
  .signature-box {
    min-height: 80px;
    padding: 15px;
  }
}

/* ===== PRINTING MODE ===== */
body.printing-mode {
  overflow: hidden;
}

body.printing-mode * {
  -webkit-print-color-adjust: exact !important;
  color-adjust: exact !important;
  print-color-adjust: exact !important;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.voucher-print {
  animation: fadeInUp 0.6s ease-out;
}

.info-section {
  animation: fadeInUp 0.8s ease-out;
}

.amount-section {
  animation: fadeInUp 1s ease-out;
}

.signature-box {
  animation: fadeInUp 1.2s ease-out;
}