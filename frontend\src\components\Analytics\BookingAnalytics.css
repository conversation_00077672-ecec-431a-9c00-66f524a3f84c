/* 📊 أنماط تحليلات الحجوزات */

/* 📦 الحاوية الرئيسية */
.booking-analytics {
  padding: var(--space-6);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  min-height: 100vh;
  animation: fadeInUp 0.6s ease-out;
}

/* 🎛️ أدوات التحكم */
.analytics-controls {
  margin-bottom: var(--space-8);
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.analytics-header h2 {
  margin: 0;
  font-size: 2rem;
  font-weight: 800;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.time-range-selector label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.time-range-select {
  padding: var(--space-2) var(--space-4);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.time-range-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 📊 شبكة التحليلات */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* 🎴 بطاقة التحليلات */
.analytics-card {
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.analytics-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0.8;
}

.analytics-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.analytics-card.primary::before {
  background: var(--gradient-primary);
}

.analytics-card.success::before {
  background: var(--gradient-secondary);
}

.analytics-card.warning::before {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.analytics-card.info::before {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

/* 🎭 أيقونة البطاقة */
.card-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2xl);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  flex-shrink: 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* 📝 محتوى البطاقة */
.card-content {
  flex: 1;
}

.card-content h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
  line-height: 1;
}

.card-change {
  font-size: 0.875rem;
  font-weight: 600;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  display: inline-block;
}

.card-change.positive {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.card-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
}

.card-percentage,
.card-subtitle {
  font-size: 0.75rem;
  color: var(--neutral-500);
  font-weight: 500;
}

/* 📈 منطقة الرسوم البيانية */
.analytics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.chart-container {
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.chart-container h3 {
  margin: 0 0 var(--space-6) 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--neutral-800);
}

/* 📊 رسم بياني للحالة */
.status-chart {
  margin-bottom: var(--space-4);
}

.status-bar {
  display: flex;
  height: 20px;
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.status-segment {
  transition: all var(--transition-normal);
}

.status-segment.confirmed {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
}

.status-segment.pending {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.status-segment.cancelled {
  background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
}

.status-segment.completed {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

.status-legend {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  color: var(--neutral-600);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.legend-color.confirmed {
  background: var(--secondary-500);
}

.legend-color.pending {
  background: var(--warning-500);
}

.legend-color.cancelled {
  background: var(--danger-500);
}

.legend-color.completed {
  background: var(--info-500);
}

/* 🌍 رسم بياني للوجهات */
.destinations-chart {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.destination-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.destination-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.destination-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.destination-bar {
  height: 8px;
  background: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.destination-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

.destination-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-600);
  min-width: 30px;
  text-align: center;
}

/* 💡 الرؤى والتوصيات */
.analytics-insights {
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.analytics-insights h3 {
  margin: 0 0 var(--space-6) 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  transition: all var(--transition-fast);
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.insight-card.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.insight-card.info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.insight-card.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.insight-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.insight-content h4 {
  margin: 0 0 var(--space-1) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.insight-content p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--neutral-600);
  line-height: 1.5;
}

/* 🔄 حالة التحميل */
.analytics-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
}

.analytics-loading p {
  margin-top: var(--space-4);
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .booking-analytics,
.dark-mode .booking-analytics {
  background: var(--gradient-cosmic);
}

[data-theme="dark"] .analytics-header h2,
.dark-mode .analytics-header h2 {
  color: var(--neutral-200);
}

[data-theme="dark"] .card-value,
.dark-mode .card-value {
  color: var(--neutral-200);
}

[data-theme="dark"] .chart-container h3,
.dark-mode .chart-container h3 {
  color: var(--neutral-200);
}

[data-theme="dark"] .destination-name,
.dark-mode .destination-name {
  color: var(--neutral-300);
}

[data-theme="dark"] .insight-content h4,
.dark-mode .insight-content h4 {
  color: var(--neutral-200);
}

[data-theme="dark"] .insight-content p,
.dark-mode .insight-content p {
  color: var(--neutral-400);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 1024px) {
  .analytics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .analytics-charts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .booking-analytics {
    padding: var(--space-4);
  }
  
  .analytics-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .analytics-card {
    flex-direction: column;
    text-align: center;
  }
  
  .card-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .card-value {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .status-legend {
    grid-template-columns: 1fr 1fr;
  }
  
  .destination-item {
    grid-template-columns: 1fr;
    gap: var(--space-2);
    text-align: center;
  }
}

/* 🎬 حركات خاصة */
.analytics-card {
  animation: fadeInUp 0.6s ease-out;
}

.analytics-card:nth-child(1) { animation-delay: 0.1s; }
.analytics-card:nth-child(2) { animation-delay: 0.2s; }
.analytics-card:nth-child(3) { animation-delay: 0.3s; }
.analytics-card:nth-child(4) { animation-delay: 0.4s; }

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .booking-analytics,
  .analytics-card,
  .insight-card {
    animation: none;
    transition: none;
  }
  
  .analytics-card:hover,
  .insight-card:hover {
    transform: none;
  }
}
