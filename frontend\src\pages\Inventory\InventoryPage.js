import React, { useState, useEffect } from 'react';

const InventoryPage = () => {
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const [newItem, setNewItem] = useState({
    itemName: '',
    category: 'tickets',
    description: '',
    quantity: '',
    minQuantity: '',
    unitPrice: '',
    totalValue: '',
    supplier: '',
    location: '',
    status: 'available',
    expiryDate: '',
    notes: ''
  });

  const [inventoryStats, setInventoryStats] = useState({
    totalItems: 0,
    totalValue: 0,
    lowStockItems: 0,
    expiredItems: 0,
    availableItems: 0,
    reservedItems: 0
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      const mockInventory = [
        {
          id: 1,
          itemName: 'تذاكر طيران الرياض - دبي',
          category: 'tickets',
          description: 'تذاكر طيران درجة اقتصادية',
          quantity: 25,
          minQuantity: 10,
          unitPrice: 1200,
          totalValue: 30000,
          supplier: 'الخطوط السعودية',
          location: 'مخزن A - رف 1',
          status: 'available',
          expiryDate: '2024-06-30',
          notes: 'صالحة لمدة 6 أشهر',
          lastUpdated: '2024-01-15',
          itemCode: 'TKT-001'
        },
        {
          id: 2,
          itemName: 'قسائم فندق الريتز كارلتون',
          category: 'vouchers',
          description: 'قسائم إقامة فندقية - جناح ملكي',
          quantity: 8,
          minQuantity: 5,
          unitPrice: 2500,
          totalValue: 20000,
          supplier: 'فندق الريتز كارلتون',
          location: 'مخزن B - رف 2',
          status: 'available',
          expiryDate: '2024-12-31',
          notes: 'تشمل الإفطار',
          lastUpdated: '2024-01-14',
          itemCode: 'VCH-002'
        },
        {
          id: 3,
          itemName: 'بوالص تأمين سفر',
          category: 'insurance',
          description: 'بوالص تأمين سفر شاملة',
          quantity: 3,
          minQuantity: 10,
          unitPrice: 150,
          totalValue: 450,
          supplier: 'شركة التأمين الشاملة',
          location: 'مخزن C - رف 1',
          status: 'low_stock',
          expiryDate: '2024-03-31',
          notes: 'مخزون منخفض - يحتاج تجديد',
          lastUpdated: '2024-01-13',
          itemCode: 'INS-003'
        },
        {
          id: 4,
          itemName: 'استمارات فيزا شنغن',
          category: 'documents',
          description: 'استمارات فيزا شنغن معبأة',
          quantity: 15,
          minQuantity: 20,
          unitPrice: 50,
          totalValue: 750,
          supplier: 'مكتب الفيزا السريعة',
          location: 'مخزن D - رف 3',
          status: 'reserved',
          expiryDate: '2024-02-28',
          notes: 'محجوزة لعملاء VIP',
          lastUpdated: '2024-01-12',
          itemCode: 'DOC-004'
        },
        {
          id: 5,
          itemName: 'كتيبات سياحية - اسطنبول',
          category: 'materials',
          description: 'كتيبات ومواد ترويجية سياحية',
          quantity: 0,
          minQuantity: 50,
          unitPrice: 25,
          totalValue: 0,
          supplier: 'مطبعة الإعلان',
          location: 'مخزن E - رف 1',
          status: 'out_of_stock',
          expiryDate: '2024-12-31',
          notes: 'نفد المخزون - يحتاج طلب جديد',
          lastUpdated: '2024-01-11',
          itemCode: 'MAT-005'
        },
        {
          id: 6,
          itemName: 'أجهزة GPS محمولة',
          category: 'equipment',
          description: 'أجهزة تحديد المواقع للرحلات',
          quantity: 12,
          minQuantity: 5,
          unitPrice: 800,
          totalValue: 9600,
          supplier: 'متجر الإلكترونيات',
          location: 'مخزن F - خزانة آمنة',
          status: 'available',
          expiryDate: '',
          notes: 'ضمان سنتين',
          lastUpdated: '2024-01-10',
          itemCode: 'EQP-006'
        }
      ];

      setInventory(mockInventory);

      // حساب الإحصائيات
      const totalValue = mockInventory.reduce((sum, item) => sum + item.totalValue, 0);
      const lowStockItems = mockInventory.filter(item => item.quantity <= item.minQuantity).length;
      const expiredItems = mockInventory.filter(item => 
        item.expiryDate && new Date(item.expiryDate) < new Date()
      ).length;
      const availableItems = mockInventory.filter(item => item.status === 'available').length;
      const reservedItems = mockInventory.filter(item => item.status === 'reserved').length;
      
      setInventoryStats({
        totalItems: mockInventory.length,
        totalValue: totalValue,
        lowStockItems: lowStockItems,
        expiredItems: expiredItems,
        availableItems: availableItems,
        reservedItems: reservedItems
      });

      setLoading(false);
    }, 1000);
  }, []);

  const handleAddItem = (e) => {
    e.preventDefault();
    const item = {
      id: inventory.length + 1,
      ...newItem,
      quantity: parseInt(newItem.quantity),
      minQuantity: parseInt(newItem.minQuantity),
      unitPrice: parseFloat(newItem.unitPrice),
      totalValue: parseInt(newItem.quantity) * parseFloat(newItem.unitPrice),
      lastUpdated: new Date().toISOString().split('T')[0],
      itemCode: `${newItem.category.toUpperCase().slice(0,3)}-${String(inventory.length + 7).padStart(3, '0')}`
    };
    setInventory([item, ...inventory]);
    setNewItem({
      itemName: '',
      category: 'tickets',
      description: '',
      quantity: '',
      minQuantity: '',
      unitPrice: '',
      totalValue: '',
      supplier: '',
      location: '',
      status: 'available',
      expiryDate: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const handleStatusChange = (id, newStatus) => {
    setInventory(inventory.map(item => 
      item.id === id ? { ...item, status: newStatus } : item
    ));
  };

  const handleQuantityUpdate = (id, newQuantity) => {
    setInventory(inventory.map(item => 
      item.id === id ? { 
        ...item, 
        quantity: parseInt(newQuantity),
        totalValue: parseInt(newQuantity) * item.unitPrice,
        status: parseInt(newQuantity) === 0 ? 'out_of_stock' : 
               parseInt(newQuantity) <= item.minQuantity ? 'low_stock' : 'available'
      } : item
    ));
  };

  const handleDeleteItem = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      setInventory(inventory.filter(item => item.id !== id));
    }
  };

  const getCategoryLabel = (category) => {
    const categories = {
      tickets: 'تذاكر',
      vouchers: 'قسائم',
      insurance: 'تأمين',
      documents: 'وثائق',
      materials: 'مواد ترويجية',
      equipment: 'معدات'
    };
    return categories[category] || category;
  };

  const getCategoryIcon = (category) => {
    const icons = {
      tickets: '🎫',
      vouchers: '🎟️',
      insurance: '🛡️',
      documents: '📋',
      materials: '📚',
      equipment: '🖥️'
    };
    return icons[category] || '📦';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#27ae60';
      case 'low_stock': return '#f39c12';
      case 'out_of_stock': return '#e74c3c';
      case 'reserved': return '#3498db';
      case 'expired': return '#95a5a6';
      default: return '#95a5a6';
    }
  };

  const getStatusLabel = (status) => {
    const labels = {
      available: 'متوفر',
      low_stock: 'مخزون منخفض',
      out_of_stock: 'نفد المخزون',
      reserved: 'محجوز',
      expired: 'منتهي الصلاحية'
    };
    return labels[status] || status;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const isExpired = (expiryDate) => {
    return expiryDate && new Date(expiryDate) < new Date();
  };

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل بيانات المخزون...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h1 style={{ margin: 0, color: '#2c3e50', fontSize: '28px' }}>📦 إدارة المخزون</h1>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>تتبع وإدارة جميع عناصر المخزون والمواد</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ عنصر جديد
        </button>
      </div>

      {/* Stats Cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي العناصر', value: inventoryStats.totalItems, color: '#3498db', icon: '📦' },
          { title: 'القيمة الإجمالية', value: formatCurrency(inventoryStats.totalValue), color: '#27ae60', icon: '💰' },
          { title: 'مخزون منخفض', value: inventoryStats.lowStockItems, color: '#f39c12', icon: '⚠️' },
          { title: 'منتهي الصلاحية', value: inventoryStats.expiredItems, color: '#e74c3c', icon: '⏰' },
          { title: 'متوفر', value: inventoryStats.availableItems, color: '#1abc9c', icon: '✅' },
          { title: 'محجوز', value: inventoryStats.reservedItems, color: '#9b59b6', icon: '🔒' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center',
            transition: 'transform 0.3s ease'
          }}
          onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
          onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '20px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {typeof stat.value === 'string' ? stat.value : stat.value.toLocaleString()}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* الوصول السريع */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '22px' }}>🚀 الوصول السريع</h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '15px' 
        }}>
          <div
            onClick={() => window.location.href = '/inventory/visas'}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              padding: '20px',
              borderRadius: '12px',
              cursor: 'pointer',
              textAlign: 'center',
              transition: 'transform 0.3s ease',
              boxShadow: '0 5px 15px rgba(0,0,0,0.2)'
            }}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{ fontSize: '40px', marginBottom: '10px' }}>📋</div>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>مخزون التأشيرات</h3>
            <p style={{ margin: 0, fontSize: '14px', opacity: 0.9 }}>إدارة ومتابعة مخزون التأشيرات</p>
          </div>
          
          <div
            onClick={() => setShowAddForm(true)}
            style={{
              background: 'linear-gradient(135deg, #27ae60 0%, #2ecc71 100%)',
              color: 'white',
              padding: '20px',
              borderRadius: '12px',
              cursor: 'pointer',
              textAlign: 'center',
              transition: 'transform 0.3s ease',
              boxShadow: '0 5px 15px rgba(0,0,0,0.2)'
            }}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{ fontSize: '40px', marginBottom: '10px' }}>➕</div>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>إضافة عنصر جديد</h3>
            <p style={{ margin: 0, fontSize: '14px', opacity: 0.9 }}>إضافة عنصر جديد للمخزون</p>
          </div>

          <div
            style={{
              background: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
              color: 'white',
              padding: '20px',
              borderRadius: '12px',
              cursor: 'pointer',
              textAlign: 'center',
              transition: 'transform 0.3s ease',
              boxShadow: '0 5px 15px rgba(0,0,0,0.2)'
            }}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{ fontSize: '40px', marginBottom: '10px' }}>📊</div>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>تقارير المخزون</h3>
            <p style={{ margin: 0, fontSize: '14px', opacity: 0.9 }}>عرض تقارير وإحصائيات المخزون</p>
          </div>

          <div
            style={{
              background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
              color: 'white',
              padding: '20px',
              borderRadius: '12px',
              cursor: 'pointer',
              textAlign: 'center',
              transition: 'transform 0.3s ease',
              boxShadow: '0 5px 15px rgba(0,0,0,0.2)'
            }}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <div style={{ fontSize: '40px', marginBottom: '10px' }}>⚠️</div>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>تنبيهات المخزون</h3>
            <p style={{ margin: 0, fontSize: '14px', opacity: 0.9 }}>عرض التنبيهات والعناصر المنتهية</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في المخزون..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الفئات</option>
          <option value="tickets">تذاكر</option>
          <option value="vouchers">قسائم</option>
          <option value="insurance">تأمين</option>
          <option value="documents">وثائق</option>
          <option value="materials">مواد ترويجية</option>
          <option value="equipment">معدات</option>
        </select>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="available">متوفر</option>
          <option value="low_stock">مخزون منخفض</option>
          <option value="out_of_stock">نفد المخزون</option>
          <option value="reserved">محجوز</option>
          <option value="expired">منتهي الصلاحية</option>
        </select>
      </div>

      {/* Inventory Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>رمز العنصر</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>اسم العنصر</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>الفئة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الكمية</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحد الأدنى</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>سعر الوحدة</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>القيمة الإجمالية</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>الموقع</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredInventory.map((item) => (
                <tr key={item.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease',
                  background: isExpired(item.expiryDate) ? '#ffebee' : 'transparent'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = isExpired(item.expiryDate) ? '#ffebee' : 'transparent'}>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#3498db' }}>
                    {item.itemCode}
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{item.itemName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{item.description}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ fontSize: '18px' }}>{getCategoryIcon(item.category)}</span>
                      <span>{getCategoryLabel(item.category)}</span>
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="number"
                      min="0"
                      value={item.quantity}
                      onChange={(e) => handleQuantityUpdate(item.id, e.target.value)}
                      style={{
                        width: '60px',
                        padding: '4px',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: item.quantity <= item.minQuantity ? '#e74c3c' : '#27ae60'
                      }}
                    />
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center', color: '#f39c12', fontWeight: 'bold' }}>
                    {item.minQuantity}
                  </td>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#27ae60' }}>
                    {formatCurrency(item.unitPrice)}
                  </td>
                  <td style={{ padding: '15px', fontWeight: 'bold', color: '#2c3e50' }}>
                    {formatCurrency(item.totalValue)}
                  </td>
                  <td style={{ padding: '15px', fontSize: '12px', color: '#7f8c8d' }}>
                    {item.location}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <select
                      value={item.status}
                      onChange={(e) => handleStatusChange(item.id, e.target.value)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '20px',
                        border: 'none',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        background: `${getStatusColor(item.status)}20`,
                        color: getStatusColor(item.status),
                        cursor: 'pointer'
                      }}
                    >
                      <option value="available">متوفر</option>
                      <option value="low_stock">مخزون منخفض</option>
                      <option value="out_of_stock">نفد المخزون</option>
                      <option value="reserved">محجوز</option>
                      <option value="expired">منتهي الصلاحية</option>
                    </select>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                      <button
                        style={{
                          background: '#3498db',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="عرض التفاصيل"
                      >
                        👁️
                      </button>
                      <button
                        style={{
                          background: '#f39c12',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="تعديل"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDeleteItem(item.id)}
                        style={{
                          background: '#e74c3c',
                          color: 'white',
                          border: 'none',
                          padding: '6px 10px',
                          borderRadius: '5px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                        title="حذف"
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Item Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>➕ إضافة عنصر جديد للمخزون</h2>
            
            <form onSubmit={handleAddItem}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العنصر</label>
                  <input
                    type="text"
                    value={newItem.itemName}
                    onChange={(e) => setNewItem({...newItem, itemName: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الفئة</label>
                  <select
                    value={newItem.category}
                    onChange={(e) => setNewItem({...newItem, category: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="tickets">تذاكر</option>
                    <option value="vouchers">قسائم</option>
                    <option value="insurance">تأمين</option>
                    <option value="documents">وثائق</option>
                    <option value="materials">مواد ترويجية</option>
                    <option value="equipment">معدات</option>
                  </select>
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الوصف</label>
                <textarea
                  value={newItem.description}
                  onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                  required
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الكمية</label>
                  <input
                    type="number"
                    min="0"
                    value={newItem.quantity}
                    onChange={(e) => setNewItem({...newItem, quantity: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الحد الأدنى</label>
                  <input
                    type="number"
                    min="0"
                    value={newItem.minQuantity}
                    onChange={(e) => setNewItem({...newItem, minQuantity: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>سعر الوحدة (ريال)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={newItem.unitPrice}
                    onChange={(e) => setNewItem({...newItem, unitPrice: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المورد</label>
                  <input
                    type="text"
                    value={newItem.supplier}
                    onChange={(e) => setNewItem({...newItem, supplier: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الموقع في المخزن</label>
                  <input
                    type="text"
                    value={newItem.location}
                    onChange={(e) => setNewItem({...newItem, location: e.target.value})}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الحالة</label>
                  <select
                    value={newItem.status}
                    onChange={(e) => setNewItem({...newItem, status: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  >
                    <option value="available">متوفر</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="out_of_stock">نفد المخزون</option>
                    <option value="reserved">محجوز</option>
                  </select>
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ انتهاء الصلاحية</label>
                  <input
                    type="date"
                    value={newItem.expiryDate}
                    onChange={(e) => setNewItem({...newItem, expiryDate: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newItem.notes}
                  onChange={(e) => setNewItem({...newItem, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: 'linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة العنصر
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryPage;