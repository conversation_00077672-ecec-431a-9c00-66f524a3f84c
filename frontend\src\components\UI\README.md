# Modern UI Components Library

مكتبة شاملة من المكونات الحديثة لنظام شراء للسفر والسياحة

## المكونات المتاحة

### 🎨 Design System
- **globals.css** - نظام التصميم الأساسي مع المتغيرات والألوان
- **index.js** - ملف التصدير الرئيسي لجميع المكونات

### 🃏 Cards (البطاقات)
- **ModernCard** - بطاقة أساسية مع أنماط متعددة
- **StatsCard** - بطاقة إحصائيات مع أيقونات ومؤشرات
- **FeatureCard** - بطاقة ميزة للعرض التقديمي
- **MetricCard** - بطاقة مقاييس مع اتجاهات

```jsx
import { ModernCard, StatsCard } from '../components/UI';

<StatsCard
  title="إجمالي المبيعات"
  value="245,680 ر.س"
  change="+12.5%"
  changeType="positive"
  icon="💰"
  color="success"
/>
```

### 🔘 Buttons (الأزرار)
- **ModernButton** - زر أساسي مع أنماط متعددة
- **IconButton** - زر أيقونة
- **ButtonGroup** - مجموعة أزرار
- **FloatingActionButton** - زر عائم
- **SplitButton** - زر منقسم مع قائمة
- **ToggleButton** - زر تبديل

```jsx
import { ModernButton, IconButton } from '../components/UI';

<ModernButton variant="primary" icon="➕">
  إضافة جديد
</ModernButton>

<IconButton icon="🔍" variant="ghost" size="sm" />
```

### 📐 Layout (التخطيط)
- **ModernPageLayout** - تخطيط صفحة كامل
- **ModernGrid** - شبكة مرنة
- **ModernFlex** - حاوي مرن
- **ModernContainer** - حاوي محدود العرض
- **ModernSection** - قسم مع خلفيات متنوعة

```jsx
import { ModernGrid, ModernFlex } from '../components/UI';

<ModernGrid cols={3} gap="lg" responsive>
  {/* محتوى الشبكة */}
</ModernGrid>

<ModernFlex direction="row" align="center" justify="between">
  {/* محتوى مرن */}
</ModernFlex>
```

### 📝 Forms (النماذج)
- **ModernInput** - حقل إدخال متقدم
- **ModernTextarea** - منطقة نص
- **ModernSelect** - قائمة اختيار مع بحث
- **ModernCheckbox** - مربع اختيار
- **ModernRadio** - زر راديو
- **FormGroup** - مجموعة حقول
- **FormRow** - صف نموذج

```jsx
import { ModernInput, ModernSelect, FormGroup } from '../components/UI';

<FormGroup direction="column" gap="md">
  <ModernInput
    label="اسم العميل"
    placeholder="أدخل اسم العميل"
    icon="👤"
    required
  />
  
  <ModernSelect
    label="نوع الخدمة"
    options={[
      { value: 'flight', label: 'حجز طيران' },
      { value: 'hotel', label: 'حجز فندق' }
    ]}
    searchable
  />
</FormGroup>
```

### 📊 Tables (الجداول)
- **ModernTable** - جدول متقدم مع فرز وبحث
- **DataTable** - جدول بيانات مع أدوات إضافية

```jsx
import { DataTable } from '../components/UI';

const columns = [
  { key: 'name', title: 'الاسم' },
  { key: 'email', title: 'البريد الإلكتروني' },
  { key: 'status', title: 'الحالة', render: (value) => <StatusBadge variant={value}>{value}</StatusBadge> }
];

<DataTable
  data={customers}
  columns={columns}
  searchable
  pagination
  selectable
  title="قائمة العملاء"
/>
```

### 🚨 Alerts & Notifications (التنبيهات والإشعارات)
- **ModernAlert** - تنبيه داخلي
- **Toast** - إشعار منبثق
- **Banner** - شريط إعلان
- **StatusBadge** - شارة حالة
- **ProgressAlert** - تنبيه مع شريط تقدم
- **Callout** - مربع تنبيه مميز

```jsx
import { ModernAlert, Toast, StatusBadge } from '../components/UI';

<ModernAlert variant="success" title="نجح!" dismissible>
  تم حفظ البيانات بنجاح
</ModernAlert>

<StatusBadge variant="success" pulse>
  نشط
</StatusBadge>
```

### 🪟 Modals (النوافذ المنبثقة)
- **ModernModal** - نافذة منبثقة أساسية
- **ModalWithFooter** - نافذة مع تذييل
- **ConfirmationModal** - نافذة تأكيد
- **FormModal** - نافذة نموذج
- **ImageModal** - نافذة عرض صور
- **Drawer** - درج جانبي
- **BottomSheet** - ورقة سفلية

```jsx
import { ConfirmationModal, FormModal } from '../components/UI';

<ConfirmationModal
  isOpen={showConfirm}
  onClose={() => setShowConfirm(false)}
  onConfirm={handleDelete}
  title="تأكيد الحذف"
  message="هل أنت متأكد من حذف هذا العنصر؟"
  variant="error"
/>

<FormModal
  isOpen={showForm}
  onClose={() => setShowForm(false)}
  onSubmit={handleSubmit}
  title="إضافة عميل جديد"
>
  {/* محتوى النموذج */}
</FormModal>
```

## الميزات الرئيسية

### 🎨 نظام تصميم متكامل
- متغيرات CSS مخصصة للألوان والمسافات
- دعم الوضع المظلم تلقائياً
- تدرجات لونية حديثة
- خطوط عربية محسنة

### 📱 تصميم متجاوب
- يعمل على جميع أحجام الشاشات
- تخطيطات مرنة ومتكيفة
- أولوية للهواتف المحمولة

### ♿ إمكانية الوصول
- دعم قارئات الشاشة
- تنقل بلوحة المفاتيح
- ألوان عالية التباين
- تقليل الحركة للمستخدمين الحساسين

### 🚀 الأداء
- تحميل تدريجي للمكونات
- تحسينات CSS
- رسوم متحركة محسنة
- ذاكرة تخزين مؤقت ذكية

### 🌐 دعم اللغة العربية
- اتجاه RTL كامل
- خطوط عربية محسنة
- تخطيطات مناسبة للعربية
- تنسيق التواريخ والأرقام

## كيفية الاستخدام

### 1. استيراد المكونات
```jsx
import { ModernCard, ModernButton, ModernGrid } from '../components/UI';
```

### 2. استخدام نظام التصميم
```css
/* استخدام المتغيرات */
.my-component {
  background: var(--primary-500);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}
```

### 3. تخصيص الألوان
```css
:root {
  --primary-500: #your-color;
  --secondary-500: #your-color;
}
```

## أمثلة متقدمة

### صفحة لوحة تحكم كاملة
```jsx
import { 
  ModernPageLayout, 
  ModernGrid, 
  StatsCard, 
  DataTable,
  ModernAlert 
} from '../components/UI';

function Dashboard() {
  return (
    <ModernPageLayout
      header={<DashboardHeader />}
      sidebar={<DashboardSidebar />}
    >
      <ModernAlert variant="info" title="مرحباً">
        مرحباً بك في لوحة التحكم
      </ModernAlert>
      
      <ModernGrid cols={4} gap="lg" responsive>
        <StatsCard title="المبيعات" value="1,234" icon="💰" />
        <StatsCard title="العملاء" value="567" icon="👥" />
        <StatsCard title="الطلبات" value="890" icon="📦" />
        <StatsCard title="الإيرادات" value="12,345 ر.س" icon="📈" />
      </ModernGrid>
      
      <DataTable
        title="آخر المعاملات"
        data={transactions}
        columns={columns}
        searchable
        pagination
      />
    </ModernPageLayout>
  );
}
```

### نموذج متقدم
```jsx
import { 
  FormModal, 
  FormGroup, 
  FormRow,
  ModernInput, 
  ModernSelect,
  ModernTextarea 
} from '../components/UI';

function CustomerForm({ isOpen, onClose, onSubmit }) {
  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      title="إضافة عميل جديد"
      size="lg"
    >
      <FormGroup direction="column" gap="lg">
        <FormRow cols={2} gap="md">
          <ModernInput
            label="الاسم الأول"
            placeholder="أدخل الاسم الأول"
            icon="👤"
            required
          />
          <ModernInput
            label="الاسم الأخير"
            placeholder="أدخل الاسم الأخير"
            icon="👤"
            required
          />
        </FormRow>
        
        <FormRow cols={2} gap="md">
          <ModernInput
            label="البريد الإلكتروني"
            type="email"
            placeholder="<EMAIL>"
            icon="📧"
            required
          />
          <ModernInput
            label="رقم الهاتف"
            type="tel"
            placeholder="+966 50 123 4567"
            icon="📱"
            required
          />
        </FormRow>
        
        <ModernSelect
          label="نوع العميل"
          placeholder="اختر نوع العميل"
          options={[
            { value: 'individual', label: 'فرد' },
            { value: 'company', label: 'شركة' },
            { value: 'agency', label: 'وكالة' }
          ]}
          required
        />
        
        <ModernTextarea
          label="ملاحظات"
          placeholder="أدخل أي ملاحظات إضافية..."
          rows={4}
        />
      </FormGroup>
    </FormModal>
  );
}
```

## التخصيص المتقدم

### إنشاء مكون مخصص
```jsx
import { ModernCard, ModernButton } from '../components/UI';

function CustomCard({ title, children, onAction }) {
  return (
    <ModernCard variant="elevated" className="custom-card">
      <div className="custom-card-header">
        <h3>{title}</h3>
        <ModernButton variant="ghost" size="sm" onClick={onAction}>
          إجراء
        </ModernButton>
      </div>
      <div className="custom-card-body">
        {children}
      </div>
    </ModernCard>
  );
}
```

### تخصيص الأنماط
```css
.custom-card {
  border: 2px solid var(--primary-200);
  background: var(--gradient-glass);
}

.custom-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid var(--neutral-200);
}

.custom-card-body {
  padding: var(--space-6);
}
```

## الدعم والمساهمة

هذه المكتبة مصممة خصيصاً لنظام شراء للسفر والسياحة. للحصول على الدعم أو المساهمة في التطوير، يرجى التواصل مع فريق التطوير.

## الترخيص

جميع الحقوق محفوظة لشركة شراء للسفر والسياحة © 2024