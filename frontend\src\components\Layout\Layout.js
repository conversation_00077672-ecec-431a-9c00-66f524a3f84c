import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const Layout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const menuItems = [
    { id: 'dashboard', name: 'لوحة التحكم', icon: '📊', path: '/dashboard' },
    { id: 'customers', name: 'العملاء', icon: '👥', path: '/customers' },
    { id: 'bookings', name: 'الحجوزات', icon: '✈️', path: '/bookings' },
    { id: 'sales', name: 'المبيعات', icon: '💰', path: '/sales' },
    { id: 'purchases', name: 'المشتريات', icon: '🛒', path: '/purchases' },
    { id: 'inventory', name: 'المخزون', icon: '📦', path: '/inventory' },
    { id: 'finance', name: 'المالية', icon: '💼', path: '/finance' },
    { id: 'accounts', name: 'الحسابات', icon: '📚', path: '/accounts' },
    { id: 'templates', name: 'القوالب', icon: '📄', path: '/templates' },
    { id: 'suppliers', name: 'الموردين', icon: '🏢', path: '/suppliers' },
    { id: 'agents', name: 'الوكلاء', icon: '🤝', path: '/agents' },
    { id: 'reports', name: 'التقارير', icon: '📈', path: '/reports' },
    { id: 'settings', name: 'الإعدادات', icon: '⚙️', path: '/settings' }
  ];

  const handleLogout = () => {
    if (window.confirm('هل تريد تسجيل الخروج؟')) {
      navigate('/');
    }
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh', fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Sidebar */}
      <div style={{
        width: sidebarOpen ? '280px' : '70px',
        background: 'linear-gradient(180deg, #2c3e50 0%, #34495e 100%)',
        color: 'white',
        transition: 'width 0.3s ease',
        position: 'fixed',
        height: '100vh',
        zIndex: 1000,
        overflowY: 'auto'
      }}>
        {/* Header */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid rgba(255,255,255,0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '10px' }}>🧳</div>
          {sidebarOpen && (
            <div>
              <h3 style={{ margin: 0, fontSize: '16px' }}>نظام محاسبي</h3>
              <p style={{ margin: '5px 0 0 0', fontSize: '12px', opacity: 0.7 }}>وكالات السفريات</p>
            </div>
          )}
        </div>

        {/* Toggle Button */}
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          style={{
            position: 'absolute',
            top: '20px',
            left: '10px',
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'white',
            padding: '8px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          {sidebarOpen ? '◀' : '▶'}
        </button>

        {/* Menu Items */}
        <div style={{ padding: '20px 0' }}>
          {menuItems.map(item => (
            <div
              key={item.id}
              onClick={() => navigate(item.path)}
              style={{
                padding: '15px 20px',
                cursor: 'pointer',
                background: location.pathname === item.path ? 'rgba(255,255,255,0.1)' : 'transparent',
                borderRight: location.pathname === item.path ? '4px solid #3498db' : '4px solid transparent',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '15px'
              }}
              onMouseEnter={(e) => {
                if (location.pathname !== item.path) {
                  e.target.style.background = 'rgba(255,255,255,0.05)';
                }
              }}
              onMouseLeave={(e) => {
                if (location.pathname !== item.path) {
                  e.target.style.background = 'transparent';
                }
              }}
            >
              <span style={{ fontSize: '20px' }}>{item.icon}</span>
              {sidebarOpen && <span>{item.name}</span>}
            </div>
          ))}
        </div>

        {/* Logout */}
        <div style={{ position: 'absolute', bottom: '20px', width: '100%', padding: '0 20px' }}>
          <button
            onClick={handleLogout}
            style={{
              width: '100%',
              padding: '12px',
              background: 'rgba(231, 76, 60, 0.2)',
              border: '1px solid rgba(231, 76, 60, 0.5)',
              color: 'white',
              borderRadius: '8px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '10px'
            }}
          >
            <span>🚪</span>
            {sidebarOpen && <span>تسجيل الخروج</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{
        marginRight: sidebarOpen ? '280px' : '70px',
        flex: 1,
        background: '#f8f9fa',
        transition: 'margin-right 0.3s ease'
      }}>
        {/* Top Bar */}
        <div style={{
          background: 'white',
          padding: '15px 30px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h1 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>
            {menuItems.find(item => item.path === location.pathname)?.name || 'النظام'}
          </h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            <span style={{ color: '#7f8c8d' }}>مرحباً، المدير</span>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '18px'
            }}>
              👤
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div style={{ padding: '30px' }}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default Layout;