import React, { useState, useEffect } from 'react';
import { useTheme } from '../../components/UI/ThemeProvider';
import DatabaseMonitor from './DatabaseMonitor';
import './AdvancedSystemSettings.css';

const AdvancedSystemSettings = () => {
  const { current, actions } = useTheme();
  const isDarkMode = current?.id === 'dark';
  const toggleTheme = () => actions?.changeTheme(isDarkMode ? 'light' : 'dark');
  const [activeTab, setActiveTab] = useState('general');
  const [searchTerm, setSearchTerm] = useState('');
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [notifications, setNotifications] = useState(true);

  // إعدادات النظام الشاملة
  const [settings, setSettings] = useState({
    general: {
      companyName: 'شركة شراء السياحة',
      companyLogo: '',
      companyAddress: 'الرياض، المملكة العربية السعودية',
      companyPhone: '+966-11-123-4567',
      companyEmail: '<EMAIL>',
      companyWebsite: 'www.sharaubtravelsoft.com',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      currency: 'SAR',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      theme: 'light',
      autoSave: true,
      autoBackup: true,
      notifications: true,
      soundEffects: true,
      animations: true,
      rtlSupport: true,
      maxFileSize: 10,
      sessionDuration: 8,
      autoLogout: true,
      welcomeMessage: 'مرحباً بك في نظام شراء السياحة',
      footerText: '© 2024 شركة شراء السياحة - جميع الحقوق محفوظة'
    },
    security: {
      passwordMinLength: 8,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
      sessionTimeout: 30,
      twoFactorAuth: false,
      loginAttempts: 5,
      lockoutDuration: 15,
      ipWhitelist: '',
      auditLog: true,
      encryptionEnabled: true,
      sslRequired: true,
      apiRateLimit: 1000,
      bruteForceProtection: true,
      passwordExpiry: 90,
      forcePasswordChange: false,
      allowRememberMe: true,
      maxSessions: 3,
      securityQuestions: false,
      captchaEnabled: false
    },
    database: {
      type: 'sqlite',
      host: 'localhost',
      port: 3306,
      name: 'travel_accounting',
      username: 'travel_user',
      password: '',
      maxConnections: 100,
      connectionTimeout: 30,
      slowQueryThreshold: 1000,
      indexOptimization: true,
      queryLogging: false,
      autoBackup: true,
      backupInterval: 24,
      retentionDays: 30,
      compressionEnabled: true,
      encryptionEnabled: true,
      encryptionLevel: 'AES256',
      allowedIPs: '',
      auditLog: true,
      auditRetentionDays: 90
    },
    email: {
      provider: 'smtp',
      host: 'smtp.gmail.com',
      port: 587,
      username: '',
      password: '',
      encryption: 'tls',
      fromName: 'شركة شراء السياحة',
      fromEmail: '<EMAIL>',
      replyTo: '<EMAIL>',
      maxRetries: 3,
      retryDelay: 5,
      templates: {
        bookingConfirmation: true,
        paymentReceived: true,
        systemAlerts: true,
        dailyReports: false,
        weeklyReports: true,
        monthlyReports: true,
        passwordReset: true,
        accountActivation: true
      }
    },
    integrations: {
      amadeus: {
        enabled: false,
        apiKey: '',
        apiSecret: '',
        environment: 'test',
        timeout: 30,
        retries: 3,
        cacheEnabled: true,
        cacheDuration: 300
      },
      sabre: {
        enabled: false,
        username: '',
        password: '',
        pcc: '',
        environment: 'test',
        timeout: 30,
        retries: 3
      },
      payment: {
        stripe: {
          enabled: false,
          publicKey: '',
          secretKey: '',
          webhookSecret: '',
          currency: 'SAR',
          captureMethod: 'automatic'
        },
        paypal: {
          enabled: false,
          clientId: '',
          clientSecret: '',
          environment: 'sandbox',
          currency: 'USD'
        },
        mada: {
          enabled: false,
          merchantId: '',
          terminalId: '',
          secretKey: '',
          environment: 'test'
        }
      },
      sms: {
        enabled: false,
        provider: 'twilio',
        apiKey: '',
        apiSecret: '',
        fromNumber: '',
        maxLength: 160,
        templates: {
          bookingConfirmation: true,
          paymentReminder: true,
          systemAlerts: false,
          otpVerification: true
        }
      },
      social: {
        facebook: {
          enabled: false,
          appId: '',
          appSecret: '',
          pageId: ''
        },
        twitter: {
          enabled: false,
          apiKey: '',
          apiSecret: '',
          accessToken: '',
          accessTokenSecret: ''
        },
        whatsapp: {
          enabled: false,
          businessId: '',
          accessToken: '',
          phoneNumber: ''
        }
      }
    },
    performance: {
      cacheEnabled: true,
      cacheDuration: 3600,
      compressionEnabled: true,
      minifyAssets: true,
      lazyLoading: true,
      maxFileSize: 10,
      sessionStorage: 'database',
      cdnEnabled: false,
      cdnUrl: '',
      imageOptimization: true,
      gzipCompression: true,
      browserCaching: true,
      preloadCritical: true,
      asyncLoading: true,
      resourceHints: true,
      serviceWorker: false
    },
    backup: {
      autoBackup: true,
      backupInterval: 24,
      retentionDays: 30,
      compressionEnabled: true,
      encryptionEnabled: true,
      cloudBackup: false,
      cloudProvider: 'aws',
      localBackupPath: './backups',
      maxBackupSize: 1000,
      incrementalBackup: true,
      verifyBackup: true,
      cloudCredentials: {
        accessKey: '',
        secretKey: '',
        bucket: '',
        region: 'us-east-1'
      }
    },
    monitoring: {
      enabled: true,
      logLevel: 'info',
      maxLogSize: 100,
      logRetentionDays: 30,
      performanceMonitoring: true,
      errorTracking: true,
      uptime: true,
      realTimeAlerts: true,
      healthChecks: true,
      metricsCollection: true,
      alerts: {
        email: true,
        sms: false,
        webhook: false,
        slack: false
      },
      thresholds: {
        cpuUsage: 80,
        memoryUsage: 85,
        diskUsage: 90,
        responseTime: 2000,
        errorRate: 5
      }
    },
    api: {
      version: 'v1',
      rateLimit: 1000,
      rateLimitWindow: 3600,
      corsEnabled: true,
      corsOrigins: ['http://localhost:3000'],
      apiKeyRequired: false,
      documentationEnabled: true,
      swaggerEnabled: true,
      versioning: true,
      deprecationWarnings: true,
      requestLogging: true,
      responseCompression: true,
      timeout: 30000,
      maxRequestSize: 50
    },
    localization: {
      defaultLanguage: 'ar',
      supportedLanguages: ['ar', 'en'],
      rtlSupport: true,
      dateLocalization: true,
      numberLocalization: true,
      currencyLocalization: true,
      timeZoneSupport: true,
      pluralizationRules: true,
      fallbackLanguage: 'en',
      autoDetectLanguage: false
    },
    ui: {
      theme: 'light',
      primaryColor: '#3498db',
      secondaryColor: '#2ecc71',
      accentColor: '#e74c3c',
      fontFamily: 'Cairo, Arial, sans-serif',
      fontSize: 'medium',
      animations: true,
      transitions: true,
      shadows: true,
      borderRadius: 8,
      compactMode: false,
      highContrast: false,
      reducedMotion: false,
      customCSS: ''
    },
    integrations: {
      amadeus: {
        enabled: false,
        apiKey: '',
        apiSecret: '',
        environment: 'test',
        timeout: 30
      },
      sabre: {
        enabled: false,
        username: '',
        password: '',
        pcc: '',
        environment: 'test'
      },
      payment: {
        stripe: {
          enabled: false,
          publicKey: '',
          secretKey: '',
          webhookSecret: ''
        },
        paypal: {
          enabled: false,
          clientId: '',
          clientSecret: '',
          environment: 'sandbox'
        },
        mada: {
          enabled: false,
          merchantId: '',
          terminalId: '',
          secretKey: ''
        }
      },
      sms: {
        enabled: false,
        provider: 'twilio',
        apiKey: '',
        apiSecret: '',
        fromNumber: '',
        templates: {
          bookingConfirmation: true,
          paymentReminder: true,
          systemAlerts: false
        }
      }
    },
    performance: {
      cacheEnabled: true,
      cacheDuration: 3600,
      compressionEnabled: true,
      minifyAssets: true,
      lazyLoading: true,
      maxFileSize: 10,
      sessionStorage: 'database',
      cdnEnabled: false,
      cdnUrl: '',
      imageOptimization: true,
      gzipCompression: true
    },
    backup: {
      autoBackup: true,
      backupInterval: 24,
      retentionDays: 30,
      compressionEnabled: true,
      encryptionEnabled: true,
      cloudBackup: false,
      cloudProvider: 'aws',
      cloudCredentials: {
        accessKey: '',
        secretKey: '',
        bucket: '',
        region: 'us-east-1'
      }
    },
    monitoring: {
      enabled: true,
      logLevel: 'info',
      maxLogSize: 100,
      logRetentionDays: 30,
      performanceMonitoring: true,
      errorTracking: true,
      uptime: true,
      alerts: {
        email: true,
        sms: false,
        webhook: false
      }
    }
  });

  // تبويبات الإعدادات
  const tabs = [
    { id: 'general', label: 'عام', icon: '⚙️', color: '#3498db', description: 'الإعدادات العامة للنظام' },
    { id: 'security', label: 'الأمان', icon: '🔒', color: '#e74c3c', description: 'إعدادات الأمان والحماية' },
    { id: 'database', label: 'قاعدة البيانات', icon: '🗄️', color: '#9b59b6', description: 'إعدادات قاعدة البيانات' },
    { id: 'email', label: 'البريد الإلكتروني', icon: '📧', color: '#f39c12', description: 'إعدادات البريد الإلكتروني' },
    { id: 'integrations', label: 'التكاملات', icon: '🔗', color: '#1abc9c', description: 'التكاملات مع الأنظمة الخارجية' },
    { id: 'performance', label: 'الأداء', icon: '⚡', color: '#e67e22', description: 'إعدادات الأداء والتحسين' },
    { id: 'backup', label: 'النسخ الاحتياطي', icon: '💾', color: '#34495e', description: 'إعدادات النسخ الاحتياطي' },
    { id: 'monitoring', label: 'المراقبة', icon: '📊', color: '#27ae60', description: 'مراقبة النظام والتنبيهات' },
    { id: 'api', label: 'واجهة البرمجة', icon: '🌐', color: '#8e44ad', description: 'إعدادات واجهة البرمجة' },
    { id: 'localization', label: 'التوطين', icon: '🌍', color: '#16a085', description: 'إعدادات اللغة والتوطين' },
    { id: 'ui', label: 'واجهة المستخدم', icon: '🎨', color: '#d35400', description: 'إعدادات واجهة المستخدم' }
  ];

  // تحديث الإعدادات
  const updateSetting = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setUnsavedChanges(true);
  };

  // حفظ الإعدادات
  const saveSettings = async () => {
    setSaving(true);
    try {
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // حفظ في localStorage
      localStorage.setItem('systemSettings', JSON.stringify(settings));
      
      setUnsavedChanges(false);
      setLastSaved(new Date());
      
      // إظهار إشعار النجاح
      showNotification('تم حفظ الإعدادات بنجاح', 'success');
    } catch (error) {
      showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
    } finally {
      setSaving(false);
    }
  };

  // إعادة تعيين الإعدادات
  const resetSettings = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      localStorage.removeItem('systemSettings');
      window.location.reload();
    }
  };

  // تصدير الإعدادات
  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    showNotification('تم تصدير الإعدادات بنجاح', 'success');
  };

  // استيراد الإعدادات
  const importSettings = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target.result);
          setSettings(importedSettings);
          setUnsavedChanges(true);
          showNotification('تم استيراد الإعدادات بنجاح', 'success');
        } catch (error) {
          showNotification('خطأ في ملف الإعدادات', 'error');
        }
      };
      reader.readAsText(file);
    }
  };

  // إظهار الإشعارات
  const showNotification = (message, type) => {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">
          ${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}
        </span>
        <span class="notification-message">${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.classList.add('show');
    }, 100);

    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  };

  // اختبار الاتصال
  const testConnection = async (type) => {
    try {
      showNotification(`جاري اختبار ${type}...`, 'info');
      await new Promise(resolve => setTimeout(resolve, 2000));
      showNotification(`تم اختبار ${type} بنجاح`, 'success');
      return true;
    } catch (error) {
      showNotification(`فشل اختبار ${type}`, 'error');
      return false;
    }
  };

  // تحسين الأداء
  const optimizePerformance = async () => {
    try {
      showNotification('جاري تحسين الأداء...', 'info');
      await new Promise(resolve => setTimeout(resolve, 3000));
      showNotification('تم تحسين الأداء بنجاح', 'success');
    } catch (error) {
      showNotification('فشل في تحسين الأداء', 'error');
    }
  };

  // إنشاء نسخة احتياطية
  const createBackup = async () => {
    try {
      showNotification('جاري إنشاء نسخة احتياطية...', 'info');
      await new Promise(resolve => setTimeout(resolve, 5000));

      const backupData = {
        settings,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      };

      const dataStr = JSON.stringify(backupData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    } catch (error) {
      showNotification('فشل في إنشاء النسخة الاحتياطية', 'error');
    }
  };

  // استعادة من نسخة احتياطية
  const restoreBackup = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const backupData = JSON.parse(e.target.result);
          if (backupData.settings) {
            setSettings(backupData.settings);
            setUnsavedChanges(true);
            showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
          } else {
            showNotification('ملف النسخة الاحتياطية غير صحيح', 'error');
          }
        } catch (error) {
          showNotification('خطأ في ملف النسخة الاحتياطية', 'error');
        }
      };
      reader.readAsText(file);
    }
  };

  // تحديث النظام
  const updateSystem = async () => {
    try {
      showNotification('جاري البحث عن تحديثات...', 'info');
      await new Promise(resolve => setTimeout(resolve, 3000));
      showNotification('النظام محدث إلى أحدث إصدار', 'success');
    } catch (error) {
      showNotification('فشل في تحديث النظام', 'error');
    }
  };

  // تنظيف النظام
  const cleanupSystem = async () => {
    try {
      showNotification('جاري تنظيف النظام...', 'info');
      await new Promise(resolve => setTimeout(resolve, 4000));
      showNotification('تم تنظيف النظام بنجاح', 'success');
    } catch (error) {
      showNotification('فشل في تنظيف النظام', 'error');
    }
  };

  // تحميل الإعدادات المحفوظة
  useEffect(() => {
    const savedSettings = localStorage.getItem('systemSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات المحفوظة:', error);
      }
    }
  }, []);

  // حفظ تلقائي
  useEffect(() => {
    if (settings.general.autoSave && unsavedChanges) {
      const autoSaveTimer = setTimeout(() => {
        saveSettings();
      }, 30000); // حفظ تلقائي كل 30 ثانية

      return () => clearTimeout(autoSaveTimer);
    }
  }, [unsavedChanges, settings.general.autoSave]);

  return (
    <div className={`advanced-settings ${isDarkMode ? 'dark-mode' : ''}`}>
      {/* رأس الصفحة */}
      <div className="settings-header">
        <div className="header-content">
          <div className="header-left">
            <h1>⚙️ إعدادات النظام المتقدمة</h1>
            <p>إدارة شاملة لجميع إعدادات النظام والتكاملات</p>
          </div>
          <div className="header-right">
            <div className="header-actions">
              <button 
                className="btn-toggle"
                onClick={() => setShowAdvanced(!showAdvanced)}
                title="عرض الإعدادات المتقدمة"
              >
                {showAdvanced ? '🔽' : '🔼'} متقدم
              </button>
              <button
                className="btn-dark-mode"
                onClick={toggleTheme}
                title="تبديل الوضع المظلم"
              >
                {isDarkMode ? '☀️' : '🌙'}
              </button>
              <button 
                className="btn-notifications"
                onClick={() => setNotifications(!notifications)}
                title="تبديل الإشعارات"
              >
                {notifications ? '🔔' : '🔕'}
              </button>
            </div>
          </div>
        </div>
        
        {/* شريط البحث */}
        <div className="search-bar">
          <input
            type="text"
            placeholder="🔍 البحث في الإعدادات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      {/* شريط الأدوات */}
      <div className="settings-toolbar">
        <div className="toolbar-left">
          <button
            className={`btn-save ${unsavedChanges ? 'has-changes' : ''}`}
            onClick={saveSettings}
            disabled={saving || !unsavedChanges}
          >
            {saving ? '🔄 جاري الحفظ...' : '💾 حفظ الإعدادات'}
          </button>
          {unsavedChanges && (
            <span className="unsaved-indicator">
              ⚠️ يوجد تغييرات غير محفوظة
            </span>
          )}
        </div>
        <div className="toolbar-right">
          <button className="btn-export" onClick={exportSettings}>
            📤 تصدير
          </button>
          <label className="btn-import">
            📥 استيراد
            <input
              type="file"
              accept=".json"
              onChange={importSettings}
              style={{ display: 'none' }}
            />
          </label>
          <button className="btn-backup" onClick={createBackup}>
            💾 نسخة احتياطية
          </button>
          <label className="btn-restore">
            🔄 استعادة
            <input
              type="file"
              accept=".json"
              onChange={restoreBackup}
              style={{ display: 'none' }}
            />
          </label>
          <button className="btn-reset" onClick={resetSettings}>
            🔄 إعادة تعيين
          </button>
        </div>
      </div>

      {/* التبويبات */}
      <div className="settings-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
            style={{ '--tab-color': tab.color }}
            title={tab.description}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-label">{tab.label}</span>
            {activeTab === tab.id && <span className="tab-indicator"></span>}
          </button>
        ))}
      </div>

      {/* محتوى التبويبات */}
      <div className="settings-content">
        {activeTab === 'general' && (
          <GeneralSettings
            settings={settings.general}
            updateSetting={(key, value) => updateSetting('general', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'security' && (
          <SecuritySettings
            settings={settings.security}
            updateSetting={(key, value) => updateSetting('security', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'database' && (
          <DatabaseSettings
            settings={settings.database}
            updateSetting={(key, value) => updateSetting('database', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'email' && (
          <EmailSettings
            settings={settings.email}
            updateSetting={(key, value) => updateSetting('email', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'integrations' && (
          <IntegrationsSettings
            settings={settings.integrations}
            updateSetting={(key, value) => updateSetting('integrations', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'performance' && (
          <PerformanceSettings
            settings={settings.performance}
            updateSetting={(key, value) => updateSetting('performance', key, value)}
            optimizePerformance={optimizePerformance}
          />
        )}

        {activeTab === 'backup' && (
          <BackupSettings
            settings={settings.backup}
            updateSetting={(key, value) => updateSetting('backup', key, value)}
            createBackup={createBackup}
            restoreBackup={restoreBackup}
          />
        )}

        {activeTab === 'monitoring' && (
          <MonitoringSettings
            settings={settings.monitoring}
            updateSetting={(key, value) => updateSetting('monitoring', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'api' && (
          <ApiSettings
            settings={settings.api}
            updateSetting={(key, value) => updateSetting('api', key, value)}
            testConnection={testConnection}
          />
        )}

        {activeTab === 'localization' && (
          <LocalizationSettings
            settings={settings.localization}
            updateSetting={(key, value) => updateSetting('localization', key, value)}
          />
        )}

        {activeTab === 'ui' && (
          <UISettings
            settings={settings.ui}
            updateSetting={(key, value) => updateSetting('ui', key, value)}
          />
        )}
      </div>

      {/* شريط الحالة */}
      <div className="settings-status">
        <div className="status-left">
          <span className="status-item">
            🟢 النظام يعمل بشكل طبيعي
          </span>
          <span className="status-item">
            📊 استخدام الذاكرة: 45%
          </span>
          <span className="status-item">
            ⚡ الأداء: ممتاز
          </span>
        </div>
        <div className="status-right">
          <button className="btn-system-info" onClick={updateSystem}>
            🔄 تحديث النظام
          </button>
          <button className="btn-cleanup" onClick={cleanupSystem}>
            🧹 تنظيف النظام
          </button>
        </div>
      </div>
    </div>
  );
};

// مكونات الإعدادات الفرعية
const GeneralSettings = ({ settings, updateSetting, testConnection }) => (
  <div className="settings-section">
    <h2>⚙️ الإعدادات العامة</h2>

    <div className="settings-grid">
      <div className="setting-group">
        <h3>معلومات الشركة</h3>
        <div className="form-group">
          <label>اسم الشركة</label>
          <input
            type="text"
            value={settings.companyName}
            onChange={(e) => updateSetting('companyName', e.target.value)}
            placeholder="اسم الشركة"
          />
        </div>
        <div className="form-group">
          <label>عنوان الشركة</label>
          <textarea
            value={settings.companyAddress}
            onChange={(e) => updateSetting('companyAddress', e.target.value)}
            placeholder="عنوان الشركة"
            rows="3"
          />
        </div>
        <div className="form-group">
          <label>هاتف الشركة</label>
          <input
            type="tel"
            value={settings.companyPhone}
            onChange={(e) => updateSetting('companyPhone', e.target.value)}
            placeholder="+966-11-123-4567"
          />
        </div>
        <div className="form-group">
          <label>بريد الشركة الإلكتروني</label>
          <input
            type="email"
            value={settings.companyEmail}
            onChange={(e) => updateSetting('companyEmail', e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div className="form-group">
          <label>موقع الشركة الإلكتروني</label>
          <input
            type="url"
            value={settings.companyWebsite}
            onChange={(e) => updateSetting('companyWebsite', e.target.value)}
            placeholder="www.company.com"
          />
        </div>
      </div>

      <div className="setting-group">
        <h3>إعدادات النظام</h3>
        <div className="form-group">
          <label>المنطقة الزمنية</label>
          <select
            value={settings.timezone}
            onChange={(e) => updateSetting('timezone', e.target.value)}
          >
            <option value="Asia/Riyadh">الرياض (GMT+3)</option>
            <option value="Asia/Dubai">دبي (GMT+4)</option>
            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
            <option value="UTC">UTC (GMT+0)</option>
          </select>
        </div>
        <div className="form-group">
          <label>اللغة الافتراضية</label>
          <select
            value={settings.language}
            onChange={(e) => updateSetting('language', e.target.value)}
          >
            <option value="ar">العربية</option>
            <option value="en">English</option>
          </select>
        </div>
        <div className="form-group">
          <label>العملة الافتراضية</label>
          <select
            value={settings.currency}
            onChange={(e) => updateSetting('currency', e.target.value)}
          >
            <option value="SAR">ريال سعودي (SAR)</option>
            <option value="YER">ريال يمني (YER)</option>
            <option value="USD">دولار أمريكي (USD)</option>
            <option value="EUR">يورو (EUR)</option>
          </select>
        </div>
        <div className="form-group">
          <label>تنسيق التاريخ</label>
          <select
            value={settings.dateFormat}
            onChange={(e) => updateSetting('dateFormat', e.target.value)}
          >
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>
        <div className="form-group">
          <label>تنسيق الوقت</label>
          <select
            value={settings.timeFormat}
            onChange={(e) => updateSetting('timeFormat', e.target.value)}
          >
            <option value="24h">24 ساعة</option>
            <option value="12h">12 ساعة</option>
          </select>
        </div>
      </div>

      <div className="setting-group">
        <h3>خيارات متقدمة</h3>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.autoSave}
              onChange={(e) => updateSetting('autoSave', e.target.checked)}
            />
            <span className="checkmark"></span>
            حفظ تلقائي
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.autoBackup}
              onChange={(e) => updateSetting('autoBackup', e.target.checked)}
            />
            <span className="checkmark"></span>
            نسخ احتياطي تلقائي
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.notifications}
              onChange={(e) => updateSetting('notifications', e.target.checked)}
            />
            <span className="checkmark"></span>
            الإشعارات
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.soundEffects}
              onChange={(e) => updateSetting('soundEffects', e.target.checked)}
            />
            <span className="checkmark"></span>
            المؤثرات الصوتية
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.animations}
              onChange={(e) => updateSetting('animations', e.target.checked)}
            />
            <span className="checkmark"></span>
            الحركات والانتقالات
          </label>
        </div>
      </div>
    </div>
  </div>
);

const SecuritySettings = ({ settings, updateSetting, testConnection }) => (
  <div className="settings-section">
    <h2>🔒 إعدادات الأمان</h2>

    <div className="settings-grid">
      <div className="setting-group">
        <h3>سياسة كلمات المرور</h3>
        <div className="form-group">
          <label>الحد الأدنى لطول كلمة المرور</label>
          <input
            type="number"
            min="6"
            max="32"
            value={settings.passwordMinLength}
            onChange={(e) => updateSetting('passwordMinLength', parseInt(e.target.value))}
          />
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.requireSpecialChars}
              onChange={(e) => updateSetting('requireSpecialChars', e.target.checked)}
            />
            <span className="checkmark"></span>
            يتطلب رموز خاصة
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.requireNumbers}
              onChange={(e) => updateSetting('requireNumbers', e.target.checked)}
            />
            <span className="checkmark"></span>
            يتطلب أرقام
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.requireUppercase}
              onChange={(e) => updateSetting('requireUppercase', e.target.checked)}
            />
            <span className="checkmark"></span>
            يتطلب أحرف كبيرة
          </label>
        </div>
      </div>

      <div className="setting-group">
        <h3>إعدادات الجلسة</h3>
        <div className="form-group">
          <label>مهلة انتهاء الجلسة (دقيقة)</label>
          <input
            type="number"
            min="5"
            max="480"
            value={settings.sessionTimeout}
            onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value))}
          />
        </div>
        <div className="form-group">
          <label>عدد محاولات تسجيل الدخول</label>
          <input
            type="number"
            min="3"
            max="10"
            value={settings.loginAttempts}
            onChange={(e) => updateSetting('loginAttempts', parseInt(e.target.value))}
          />
        </div>
        <div className="form-group">
          <label>مدة القفل (دقيقة)</label>
          <input
            type="number"
            min="5"
            max="60"
            value={settings.lockoutDuration}
            onChange={(e) => updateSetting('lockoutDuration', parseInt(e.target.value))}
          />
        </div>
      </div>

      <div className="setting-group">
        <h3>أمان متقدم</h3>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.twoFactorAuth}
              onChange={(e) => updateSetting('twoFactorAuth', e.target.checked)}
            />
            <span className="checkmark"></span>
            المصادقة الثنائية
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.auditLog}
              onChange={(e) => updateSetting('auditLog', e.target.checked)}
            />
            <span className="checkmark"></span>
            سجل التدقيق
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.encryptionEnabled}
              onChange={(e) => updateSetting('encryptionEnabled', e.target.checked)}
            />
            <span className="checkmark"></span>
            التشفير
          </label>
        </div>
        <div className="form-group checkbox-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.sslRequired}
              onChange={(e) => updateSetting('sslRequired', e.target.checked)}
            />
            <span className="checkmark"></span>
            SSL مطلوب
          </label>
        </div>
        <div className="form-group">
          <button
            className="btn-test"
            onClick={() => testConnection('الأمان')}
          >
            🔍 اختبار إعدادات الأمان
          </button>
        </div>
      </div>
    </div>
  </div>
);

// مكونات مؤقتة للتبويبات الأخرى
const DatabaseSettings = ({ settings = {}, updateSetting, testConnection }) => {
  // قيم افتراضية للإعدادات
  const defaultSettings = {
    type: 'sqlite',
    host: 'localhost',
    port: 3306,
    name: 'travel_accounting',
    username: 'travel_user',
    password: '',
    maxConnections: 100,
    connectionTimeout: 30,
    slowQueryThreshold: 1000,
    indexOptimization: true,
    queryLogging: false,
    autoBackup: true,
    backupInterval: 24,
    retentionDays: 30,
    compressionEnabled: true,
    encryptionEnabled: true,
    encryptionLevel: 'AES256',
    allowedIPs: '',
    auditLog: true,
    auditRetentionDays: 90
  };

  // دمج الإعدادات مع القيم الافتراضية
  const currentSettings = { ...defaultSettings, ...settings };

  const [connectionStatus, setConnectionStatus] = useState('unknown');
  const [isConnecting, setIsConnecting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const [lastBackup, setLastBackup] = useState(null);
  const [showMonitor, setShowMonitor] = useState(false);
  const [dbStats, setDbStats] = useState({
    size: '15.2 MB',
    tables: 12,
    records: 1547,
    lastOptimized: '2024-01-15'
  });

  // اختبار الاتصال بقاعدة البيانات
  const handleTestConnection = async () => {
    setIsConnecting(true);
    setConnectionStatus('testing');

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      // محاكاة نتيجة الاختبار
      const success = Math.random() > 0.3; // 70% نجاح

      if (success) {
        setConnectionStatus('success');
        showNotification('تم الاتصال بقاعدة البيانات بنجاح', 'success');
      } else {
        setConnectionStatus('error');
        showNotification('فشل في الاتصال بقاعدة البيانات', 'error');
      }
    } catch (error) {
      setConnectionStatus('error');
      showNotification('حدث خطأ أثناء اختبار الاتصال', 'error');
    } finally {
      setIsConnecting(false);
    }
  };

  // إنشاء نسخة احتياطية
  const createBackup = async () => {
    setBackupInProgress(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 3000));

      const backupData = {
        timestamp: new Date().toISOString(),
        size: '15.2 MB',
        tables: 12,
        records: 1547
      };

      setLastBackup(backupData);
      showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    } catch (error) {
      showNotification('فشل في إنشاء النسخة الاحتياطية', 'error');
    } finally {
      setBackupInProgress(false);
    }
  };

  // تحسين قاعدة البيانات
  const optimizeDatabase = async () => {
    try {
      showNotification('جاري تحسين قاعدة البيانات...', 'info');
      await new Promise(resolve => setTimeout(resolve, 4000));

      setDbStats(prev => ({
        ...prev,
        lastOptimized: new Date().toISOString().split('T')[0]
      }));

      showNotification('تم تحسين قاعدة البيانات بنجاح', 'success');
    } catch (error) {
      showNotification('فشل في تحسين قاعدة البيانات', 'error');
    }
  };

  // إظهار الإشعارات
  const showNotification = (message, type) => {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">
          ${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}
        </span>
        <span class="notification-message">${message}</span>
      </div>
    `;

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : type === 'warning' ? '#fff3cd' : '#d1ecf1'};
      color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : type === 'warning' ? '#856404' : '#0c5460'};
      border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : type === 'warning' ? '#ffeaa7' : '#bee5eb'};
      padding: 15px 20px;
      border-radius: 10px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
      z-index: 10000;
      transform: translateX(400px);
      opacity: 0;
      transition: all 0.3s ease;
      direction: rtl;
      font-family: 'Cairo', Arial, sans-serif;
      font-weight: 600;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
      notification.style.opacity = '1';
    }, 100);

    setTimeout(() => {
      notification.style.transform = 'translateX(400px)';
      notification.style.opacity = '0';
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  };

  return (
    <div className="settings-section">
      <h2>🗄️ إعدادات قاعدة البيانات</h2>

      <div className="settings-grid">
        {/* إعدادات الاتصال */}
        <div className="setting-group">
          <h3>🔗 إعدادات الاتصال</h3>

          <div className="form-group">
            <label>نوع قاعدة البيانات</label>
            <select
              value={currentSettings.type}
              onChange={(e) => updateSetting('type', e.target.value)}
            >
              <option value="sqlite">SQLite</option>
              <option value="mysql">MySQL</option>
              <option value="postgresql">PostgreSQL</option>
              <option value="mongodb">MongoDB</option>
            </select>
          </div>

          {currentSettings.type !== 'sqlite' && (
            <>
              <div className="form-group">
                <label>عنوان الخادم</label>
                <input
                  type="text"
                  value={currentSettings.host}
                  onChange={(e) => updateSetting('host', e.target.value)}
                  placeholder="localhost"
                />
              </div>

              <div className="form-group">
                <label>المنفذ</label>
                <input
                  type="number"
                  value={currentSettings.port}
                  onChange={(e) => updateSetting('port', parseInt(e.target.value))}
                  placeholder="3306"
                />
              </div>

              <div className="form-group">
                <label>اسم قاعدة البيانات</label>
                <input
                  type="text"
                  value={currentSettings.name}
                  onChange={(e) => updateSetting('name', e.target.value)}
                  placeholder="travel_accounting"
                />
              </div>

              <div className="form-group">
                <label>اسم المستخدم</label>
                <input
                  type="text"
                  value={currentSettings.username}
                  onChange={(e) => updateSetting('username', e.target.value)}
                  placeholder="travel_user"
                />
              </div>

              <div className="form-group">
                <label>كلمة المرور</label>
                <div className="password-input-container">
                  <input
                    type={showPassword ? "text" : "password"}
                    value={currentSettings.password}
                    onChange={(e) => updateSetting('password', e.target.value)}
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? '🙈' : '👁️'}
                  </button>
                </div>
              </div>
            </>
          )}

          <div className="connection-test">
            <button
              className={`btn-test-connection ${connectionStatus}`}
              onClick={handleTestConnection}
              disabled={isConnecting}
            >
              {isConnecting ? (
                <>🔄 جاري الاختبار...</>
              ) : (
                <>
                  {connectionStatus === 'success' ? '✅' :
                   connectionStatus === 'error' ? '❌' : '🔍'}
                  اختبار الاتصال
                </>
              )}
            </button>

            {connectionStatus === 'success' && (
              <span className="connection-status success">
                ✅ الاتصال ناجح
              </span>
            )}

            {connectionStatus === 'error' && (
              <span className="connection-status error">
                ❌ فشل الاتصال
              </span>
            )}
          </div>
        </div>

        {/* إعدادات الأداء */}
        <div className="setting-group">
          <h3>⚡ إعدادات الأداء</h3>

          <div className="form-group">
            <label>الحد الأقصى للاتصالات</label>
            <input
              type="number"
              min="10"
              max="1000"
              value={currentSettings.maxConnections}
              onChange={(e) => updateSetting('maxConnections', parseInt(e.target.value))}
            />
            <small>عدد الاتصالات المتزامنة المسموحة</small>
          </div>

          <div className="form-group">
            <label>مهلة الاتصال (ثانية)</label>
            <input
              type="number"
              min="5"
              max="300"
              value={currentSettings.connectionTimeout}
              onChange={(e) => updateSetting('connectionTimeout', parseInt(e.target.value))}
            />
            <small>الوقت المسموح للاتصال قبل انتهاء المهلة</small>
          </div>

          <div className="form-group">
            <label>حد الاستعلام البطيء (مللي ثانية)</label>
            <input
              type="number"
              min="100"
              max="10000"
              value={currentSettings.slowQueryThreshold}
              onChange={(e) => updateSetting('slowQueryThreshold', parseInt(e.target.value))}
            />
            <small>الاستعلامات الأبطأ من هذا الحد سيتم تسجيلها</small>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.indexOptimization}
                onChange={(e) => updateSetting('indexOptimization', e.target.checked)}
              />
              <span className="checkmark"></span>
              تحسين الفهارس تلقائياً
            </label>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.queryLogging}
                onChange={(e) => updateSetting('queryLogging', e.target.checked)}
              />
              <span className="checkmark"></span>
              تسجيل الاستعلامات
            </label>
          </div>

          <div className="form-group">
            <button
              className="btn-optimize"
              onClick={optimizeDatabase}
            >
              🚀 تحسين قاعدة البيانات
            </button>
          </div>
        </div>

        {/* النسخ الاحتياطي */}
        <div className="setting-group">
          <h3>💾 النسخ الاحتياطي</h3>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.autoBackup}
                onChange={(e) => updateSetting('autoBackup', e.target.checked)}
              />
              <span className="checkmark"></span>
              نسخ احتياطي تلقائي
            </label>
          </div>

          <div className="form-group">
            <label>فترة النسخ الاحتياطي (ساعة)</label>
            <select
              value={currentSettings.backupInterval}
              onChange={(e) => updateSetting('backupInterval', parseInt(e.target.value))}
            >
              <option value="1">كل ساعة</option>
              <option value="6">كل 6 ساعات</option>
              <option value="12">كل 12 ساعة</option>
              <option value="24">يومياً</option>
              <option value="168">أسبوعياً</option>
            </select>
          </div>

          <div className="form-group">
            <label>مدة الاحتفاظ (يوم)</label>
            <input
              type="number"
              min="1"
              max="365"
              value={currentSettings.retentionDays}
              onChange={(e) => updateSetting('retentionDays', parseInt(e.target.value))}
            />
            <small>عدد الأيام للاحتفاظ بالنسخ الاحتياطية</small>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.compressionEnabled}
                onChange={(e) => updateSetting('compressionEnabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              ضغط النسخ الاحتياطية
            </label>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.encryptionEnabled}
                onChange={(e) => updateSetting('encryptionEnabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              تشفير النسخ الاحتياطية
            </label>
          </div>

          <div className="backup-actions">
            <button
              className="btn-backup"
              onClick={createBackup}
              disabled={backupInProgress}
            >
              {backupInProgress ? '🔄 جاري الإنشاء...' : '💾 إنشاء نسخة احتياطية'}
            </button>

            {lastBackup && (
              <div className="last-backup-info">
                <span>آخر نسخة احتياطية:</span>
                <span>{new Date(lastBackup.timestamp).toLocaleString('ar-SA')}</span>
                <span>الحجم: {lastBackup.size}</span>
              </div>
            )}
          </div>
        </div>

        {/* إحصائيات قاعدة البيانات */}
        <div className="setting-group">
          <h3>📊 إحصائيات قاعدة البيانات</h3>

          <div className="db-stats">
            <div className="stat-item">
              <span className="stat-icon">📦</span>
              <div className="stat-info">
                <span className="stat-label">حجم قاعدة البيانات</span>
                <span className="stat-value">{dbStats.size}</span>
              </div>
            </div>

            <div className="stat-item">
              <span className="stat-icon">🗂️</span>
              <div className="stat-info">
                <span className="stat-label">عدد الجداول</span>
                <span className="stat-value">{dbStats.tables}</span>
              </div>
            </div>

            <div className="stat-item">
              <span className="stat-icon">📝</span>
              <div className="stat-info">
                <span className="stat-label">عدد السجلات</span>
                <span className="stat-value">{dbStats.records.toLocaleString('ar-SA')}</span>
              </div>
            </div>

            <div className="stat-item">
              <span className="stat-icon">🔧</span>
              <div className="stat-info">
                <span className="stat-label">آخر تحسين</span>
                <span className="stat-value">{dbStats.lastOptimized}</span>
              </div>
            </div>
          </div>

          <div className="db-actions">
            <button
              className="btn-monitor"
              onClick={() => setShowMonitor(!showMonitor)}
            >
              📊 مراقبة مباشرة
            </button>
            <button className="btn-analyze">
              📈 تحليل الأداء
            </button>
            <button className="btn-repair">
              🔧 إصلاح الجداول
            </button>
            <button className="btn-vacuum">
              🧹 تنظيف قاعدة البيانات
            </button>
          </div>
        </div>

        {/* إعدادات الأمان */}
        <div className="setting-group">
          <h3>🔐 إعدادات الأمان</h3>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.encryptionEnabled}
                onChange={(e) => updateSetting('encryptionEnabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              تشفير البيانات الحساسة
            </label>
          </div>

          <div className="form-group">
            <label>مستوى التشفير</label>
            <select
              value={currentSettings.encryptionLevel}
              onChange={(e) => updateSetting('encryptionLevel', e.target.value)}
              disabled={!currentSettings.encryptionEnabled}
            >
              <option value="AES128">AES-128</option>
              <option value="AES256">AES-256</option>
              <option value="AES512">AES-512</option>
            </select>
          </div>

          <div className="form-group">
            <label>عناوين IP المسموحة</label>
            <textarea
              value={currentSettings.allowedIPs}
              onChange={(e) => updateSetting('allowedIPs', e.target.value)}
              placeholder="***********&#10;********&#10;localhost"
              rows="3"
            />
            <small>عنوان IP واحد في كل سطر (اتركه فارغاً للسماح للجميع)</small>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.auditLog}
                onChange={(e) => updateSetting('auditLog', e.target.checked)}
              />
              <span className="checkmark"></span>
              تسجيل عمليات التدقيق
            </label>
          </div>

          <div className="form-group">
            <label>مدة الاحتفاظ بسجلات التدقيق (يوم)</label>
            <input
              type="number"
              min="1"
              max="365"
              value={currentSettings.auditRetentionDays}
              onChange={(e) => updateSetting('auditRetentionDays', parseInt(e.target.value))}
              disabled={!currentSettings.auditLog}
            />
          </div>
        </div>
      </div>

      {/* مراقب قاعدة البيانات */}
      {showMonitor && (
        <div className="database-monitor-overlay">
          <div className="monitor-header">
            <h3>📊 مراقب قاعدة البيانات المباشر</h3>
            <button
              className="btn-close-monitor"
              onClick={() => setShowMonitor(false)}
            >
              ✕ إغلاق
            </button>
          </div>
          <DatabaseMonitor />
        </div>
      )}
    </div>
  );
};

const EmailSettings = ({ settings, updateSetting, testConnection }) => (
  <div className="settings-section">
    <h2>📧 إعدادات البريد الإلكتروني</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
      <button onClick={() => testConnection('البريد الإلكتروني')}>
        🔍 اختبار الاتصال
      </button>
    </div>
  </div>
);

const IntegrationsSettings = ({ settings, updateSetting, testConnection }) => (
  <div className="settings-section">
    <h2>🔗 إعدادات التكاملات</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
      <button onClick={() => testConnection('التكاملات')}>
        🔍 اختبار التكاملات
      </button>
    </div>
  </div>
);

const PerformanceSettings = ({ settings, updateSetting, optimizePerformance }) => (
  <div className="settings-section">
    <h2>⚡ إعدادات الأداء</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
      <button onClick={optimizePerformance}>
        🚀 تحسين الأداء
      </button>
    </div>
  </div>
);

const BackupSettings = ({ settings, updateSetting, createBackup, restoreBackup }) => (
  <div className="settings-section">
    <h2>💾 إعدادات النسخ الاحتياطي</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
      <button onClick={createBackup}>
        💾 إنشاء نسخة احتياطية
      </button>
    </div>
  </div>
);

const MonitoringSettings = ({ settings, updateSetting, testConnection }) => (
  <div className="settings-section">
    <h2>📊 إعدادات المراقبة</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
      <button onClick={() => testConnection('المراقبة')}>
        🔍 اختبار المراقبة
      </button>
    </div>
  </div>
);

const ApiSettings = ({ settings, updateSetting, testConnection }) => (
  <div className="settings-section">
    <h2>🌐 إعدادات واجهة البرمجة</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
      <button onClick={() => testConnection('واجهة البرمجة')}>
        🔍 اختبار API
      </button>
    </div>
  </div>
);

const LocalizationSettings = ({ settings, updateSetting }) => (
  <div className="settings-section">
    <h2>🌍 إعدادات التوطين</h2>
    <div className="coming-soon">
      <h3>قيد التطوير</h3>
      <p>هذا القسم سيكون متاحاً قريباً</p>
    </div>
  </div>
);

const UISettings = ({ settings = {}, updateSetting }) => {
  const { current, actions } = useTheme();
  const isDarkMode = current?.id === 'dark';
  const toggleTheme = () => actions?.changeTheme(isDarkMode ? 'light' : 'dark');

  // قيم افتراضية للإعدادات
  const defaultUISettings = {
    fontSize: 'medium',
    animations: true,
    shadows: true,
    borderRadius: '8',
    compactMode: false,
    highContrast: false,
    primaryColor: '#3498db'
  };

  // دمج الإعدادات مع القيم الافتراضية
  const currentSettings = { ...defaultUISettings, ...settings };

  return (
    <div className="settings-section">
      <h2>🎨 إعدادات واجهة المستخدم</h2>

      <div className="settings-grid">
        <div className="setting-group">
          <h3>الثيم والمظهر</h3>

          <div className="form-group">
            <label>وضع العرض</label>
            <div className="theme-toggle-container">
              <button
                className={`theme-toggle-btn ${isDarkMode ? 'active' : ''}`}
                onClick={toggleTheme}
              >
                <span className="theme-icon">{isDarkMode ? '🌙' : '☀️'}</span>
                <span className="theme-text">
                  {isDarkMode ? 'الوضع المظلم' : 'الوضع العادي'}
                </span>
              </button>
            </div>
            <p className="theme-description">
              {isDarkMode
                ? 'الوضع المظلم مفعل - مناسب للاستخدام في الإضاءة المنخفضة'
                : 'الوضع العادي مفعل - مناسب للاستخدام في الإضاءة العادية'
              }
            </p>
          </div>

          <div className="form-group">
            <label>اللون الأساسي</label>
            <div className="color-picker-container">
              <input
                type="color"
                value="#3498db"
                onChange={(e) => updateSetting('primaryColor', e.target.value)}
                className="color-picker"
              />
              <span className="color-value">#3498db</span>
            </div>
          </div>

          <div className="form-group">
            <label>حجم الخط</label>
            <select
              value={currentSettings.fontSize}
              onChange={(e) => updateSetting('fontSize', e.target.value)}
            >
              <option value="small">صغير</option>
              <option value="medium">متوسط</option>
              <option value="large">كبير</option>
            </select>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.animations}
                onChange={(e) => updateSetting('animations', e.target.checked)}
              />
              <span className="checkmark"></span>
              تفعيل الحركات والانتقالات
            </label>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.shadows}
                onChange={(e) => updateSetting('shadows', e.target.checked)}
              />
              <span className="checkmark"></span>
              تفعيل الظلال
            </label>
          </div>
        </div>

        <div className="setting-group">
          <h3>إعدادات متقدمة</h3>

          <div className="form-group">
            <label>نمط الحدود</label>
            <select
              value={currentSettings.borderRadius}
              onChange={(e) => updateSetting('borderRadius', e.target.value)}
            >
              <option value="0">مربع</option>
              <option value="4">قليل الانحناء</option>
              <option value="8">متوسط الانحناء</option>
              <option value="16">كثير الانحناء</option>
            </select>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.compactMode}
                onChange={(e) => updateSetting('compactMode', e.target.checked)}
              />
              <span className="checkmark"></span>
              الوضع المضغوط
            </label>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={currentSettings.highContrast}
                onChange={(e) => updateSetting('highContrast', e.target.checked)}
              />
              <span className="checkmark"></span>
              تباين عالي
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSystemSettings;
