import React, { useState } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from './BookingActions';

const SimpleTest = () => {
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showModal, setShowModal] = useState(false);

  const sampleBooking = {
    id: 1,
    customerName: 'أحمد محمد',
    customerPhone: '0501234567',
    date: '2024-01-15',
    status: 'confirmed',
    amount: 1500
  };

  const handleView = (booking) => {
    console.log('عرض:', booking);
    setShowModal(true);
    alert(`تم فتح تفاصيل الحجز رقم ${booking.id}`);
  };

  const handleEdit = (booking) => {
    console.log('تعديل:', booking);
    alert(`تم فتح نموذج تعديل الحجز رقم ${booking.id}`);
  };

  const handleDelete = (booking) => {
    console.log('حذف:', booking);
    if (confirm(`هل أنت متأكد من حذف الحجز رقم ${booking.id}؟`)) {
      alert(`تم حذف الحجز رقم ${booking.id}`);
    }
  };

  const handlePrint = (booking) => {
    console.log('طباعة:', booking);
    alert(`تم إرسال الحجز رقم ${booking.id} للطباعة`);
  };

  const handleSavePDF = (booking) => {
    console.log('حفظ PDF:', booking);
    alert(`تم حفظ الحجز رقم ${booking.id} كملف PDF`);
  };

  const handleBulkPrint = (ids) => {
    console.log('طباعة مجمعة:', ids);
    alert(`تم إرسال ${ids.length} حجز للطباعة`);
  };

  const handleBulkSavePDF = (ids) => {
    console.log('PDF مجمع:', ids);
    alert(`تم حفظ ${ids.length} حجز كملف PDF`);
  };

  const handleBulkDelete = (ids) => {
    console.log('حذف مجمع:', ids);
    if (confirm(`هل أنت متأكد من حذف ${ids.length} حجز؟`)) {
      alert(`تم حذف ${ids.length} حجز`);
      setSelectedBookings([]);
    }
  };

  const handleSelectAll = () => {
    setSelectedBookings([1, 2, 3]);
    alert('تم تحديد جميع الحجوزات');
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
    alert('تم إلغاء تحديد جميع الحجوزات');
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>اختبار مكونات الحجوزات</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>أزرار العمليات الفردية:</h3>
        <BookingActions
          booking={sampleBooking}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onPrint={handlePrint}
          onSavePDF={handleSavePDF}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>العمليات المجمعة:</h3>
        <BulkActions
          selectedBookings={selectedBookings}
          onBulkPrint={handleBulkPrint}
          onBulkSavePDF={handleBulkSavePDF}
          onBulkDelete={handleBulkDelete}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
        />
      </div>

      <BookingDetailsModal
        booking={sampleBooking}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onPrint={handlePrint}
        onSavePDF={handleSavePDF}
      />
    </div>
  );
};

export default SimpleTest;