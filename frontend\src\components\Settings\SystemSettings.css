/* System Settings Styles */

.system-settings {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
}

/* Header */
.settings-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.header-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
}

.last-saved {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 6px 12px;
  border-radius: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.export-btn,
.import-btn,
.reset-btn,
.save-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.export-btn {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(23, 162, 184, 0.3);
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

.import-btn {
  background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(111, 66, 193, 0.3);
}

.import-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(111, 66, 193, 0.4);
}

.reset-btn {
  background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(253, 126, 20, 0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(253, 126, 20, 0.4);
}

.save-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.save-btn.has-changes {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
  animation: pulse 2s infinite;
}

.save-btn.saving {
  background: #ffc107;
  color: #212529;
  cursor: not-allowed;
}

.save-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

@keyframes pulse {
  0% { box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3); }
  50% { box-shadow: 0 5px 15px rgba(220, 53, 69, 0.6); }
  100% { box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3); }
}

/* Unsaved Warning */
.unsaved-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Container */
.settings-container {
  display: flex;
  gap: 25px;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

/* Sidebar */
.settings-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  flex-shrink: 0;
}

.tabs-list {
  display: flex;
  flex-direction: column;
  padding: 20px 0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 25px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
  font-weight: 500;
  text-align: right;
  border-right: 3px solid transparent;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-right-color: rgba(255, 255, 255, 0.3);
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-right-color: white;
  font-weight: 600;
}

.tab-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.tab-label {
  flex: 1;
}

/* Content */
.settings-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  max-height: 80vh;
}

/* Settings Section */
.settings-section {
  max-width: 800px;
}

.section-header {
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.section-header h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.section-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-item label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.setting-item input,
.setting-item select,
.setting-item textarea {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.setting-item input:focus,
.setting-item select:focus,
.setting-item textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-item textarea {
  resize: vertical;
  min-height: 80px;
}

/* Checkbox Items */
.checkbox-item {
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.checkbox-item label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500;
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Logo Upload */
.logo-upload {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.logo-preview {
  width: 120px;
  height: 80px;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.upload-btn {
  padding: 10px 16px;
  background: #f8f9fa;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  color: #6c757d;
  font-weight: 500;
}

.upload-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

/* Integration Groups */
.integration-group {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.integration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.integration-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.integration-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Payment Providers */
.payment-provider {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.provider-header h5 {
  margin: 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.provider-settings {
  display: grid;
  gap: 15px;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-switch input:checked + .slider {
  background-color: #667eea;
}

.toggle-switch input:checked + .slider:before {
  transform: translateX(26px);
}

/* Notification Groups */
.notification-group {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.group-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.notification-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.notification-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
}

.notification-item label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 500;
  color: #495057;
}

.sms-settings,
.push-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
  }
  
  .tabs-list {
    flex-direction: row;
    overflow-x: auto;
    padding: 15px 20px;
    gap: 10px;
  }
  
  .tab-button {
    flex-shrink: 0;
    border-right: none;
    border-bottom: 3px solid transparent;
    padding: 12px 20px;
  }
  
  .tab-button:hover {
    border-right: none;
    border-bottom-color: rgba(255, 255, 255, 0.3);
  }
  
  .tab-button.active {
    border-right: none;
    border-bottom-color: white;
  }
}

@media (max-width: 768px) {
  .system-settings {
    padding: 15px;
  }
  
  .settings-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .header-actions {
    align-items: center;
  }
  
  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .settings-content {
    padding: 20px;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .integration-settings,
  .notification-options,
  .sms-settings,
  .push-settings,
  .provider-settings {
    grid-template-columns: 1fr;
  }
  
  .tabs-list {
    flex-direction: column;
    gap: 0;
  }
  
  .tab-button {
    border-bottom: none;
    border-right: 3px solid transparent;
  }
  
  .tab-button:hover {
    border-bottom: none;
    border-right-color: rgba(255, 255, 255, 0.3);
  }
  
  .tab-button.active {
    border-bottom: none;
    border-right-color: white;
  }
}

@media (max-width: 480px) {
  .settings-header {
    padding: 20px;
  }
  
  .header-content h2 {
    font-size: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .export-btn,
  .import-btn,
  .reset-btn,
  .save-btn {
    width: 100%;
    justify-content: center;
  }
  
  .settings-content {
    padding: 15px;
  }
  
  .section-header h3 {
    font-size: 20px;
  }
  
  .integration-header,
  .group-header,
  .provider-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
}

/* Print Styles */
@media print {
  .system-settings {
    background: white;
    padding: 0;
  }
  
  .settings-header {
    box-shadow: none;
    border-bottom: 2px solid #000;
  }
  
  .header-actions,
  .action-buttons,
  .toggle-switch,
  .upload-btn {
    display: none !important;
  }
  
  .settings-container {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .settings-sidebar {
    display: none;
  }
  
  .settings-content {
    padding: 20px;
  }
  
  .integration-group,
  .notification-group,
  .payment-provider {
    break-inside: avoid;
    border: 1px solid #000;
    margin-bottom: 10px;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .system-settings {
    background: #1a1a1a;
    color: #e9ecef;
  }
  
  .settings-header,
  .settings-container {
    background: #2d3748;
    color: #e9ecef;
  }
  
  .section-header {
    border-bottom-color: #4a5568;
  }
  
  .setting-item input,
  .setting-item select,
  .setting-item textarea {
    background: #4a5568;
    border-color: #718096;
    color: #e9ecef;
  }
  
  .integration-group,
  .notification-group {
    background: #4a5568;
    border-color: #718096;
  }
  
  .payment-provider,
  .notification-item {
    background: #2d3748;
    border-color: #718096;
  }
}