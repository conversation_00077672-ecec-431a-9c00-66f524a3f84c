// Global setup for tests
module.exports = async () => {
  // Set test environment variables
  process.env.REACT_APP_ENV = 'test';
  process.env.REACT_APP_API_URL = 'http://localhost:8000/api';
  
  // Setup global test utilities
  global.testUtils = {
    // Add any global test utilities here
    mockLocalStorage: () => {
      const localStorageMock = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      };
      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock
      });
      return localStorageMock;
    },
    
    mockSessionStorage: () => {
      const sessionStorageMock = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      };
      Object.defineProperty(window, 'sessionStorage', {
        value: sessionStorageMock
      });
      return sessionStorageMock;
    },
    
    mockFetch: () => {
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({}),
          text: () => Promise.resolve(''),
        })
      );
      return global.fetch;
    }
  };
  
  console.log('🧪 Global test setup completed');
};