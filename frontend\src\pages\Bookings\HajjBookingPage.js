import React, { useState, useEffect } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';

const HajjBookingPage = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPackage, setFilterPackage] = useState('all');
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);

  const [newBooking, setNewBooking] = useState({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    passportNumber: '',
    nationalId: '',
    packageType: 'economy', // economy, standard, vip, luxury
    hajjType: 'tamattu', // tamattu, qiran, ifrad
    departureDate: '',
    returnDate: '',
    duration: 14,
    pilgrims: 1,
    maleCount: 1,
    femaleCount: 0,
    childCount: 0,
    hotelMakkah: '',
    hotelMadinah: '',
    hotelMina: '',
    hotelArafat: '',
    roomType: 'quad', // single, double, triple, quad
    transportType: 'bus', // bus, car, vip-car
    visaIncluded: true,
    flightIncluded: true,
    mealPlan: 'full-board', // none, breakfast, half-board, full-board
    guidedTour: true,
    amount: '',
    paidAmount: 0,
    remainingAmount: 0,
    currency: 'SAR',
    status: 'pending', // pending, confirmed, cancelled, completed
    paymentStatus: 'unpaid', // paid, partial, unpaid
    bookingReference: '',
    specialRequests: '',
    notes: ''
  });

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setBookings([
        {
          id: 1,
          customerName: 'إبراهيم أحمد المحمد',
          customerPhone: '+************',
          customerEmail: '<EMAIL>',
          passportNumber: 'A12345678',
          nationalId: '1234567890',
          packageType: 'vip',
          hajjType: 'tamattu',
          departureDate: '2024-06-10',
          returnDate: '2024-06-25',
          duration: 15,
          pilgrims: 2,
          maleCount: 1,
          femaleCount: 1,
          childCount: 0,
          hotelMakkah: 'فندق الحرم الشريف',
          hotelMadinah: 'فندق المسجد النبوي',
          hotelMina: 'مخيم منى VIP',
          hotelArafat: 'مخيم عرفات المكيف',
          roomType: 'double',
          transportType: 'vip-car',
          visaIncluded: true,
          flightIncluded: true,
          mealPlan: 'full-board',
          guidedTour: true,
          amount: 25000,
          paidAmount: 15000,
          remainingAmount: 10000,
          currency: 'SAR',
          status: 'confirmed',
          paymentStatus: 'partial',
          bookingReference: '*********',
          bookingDate: '2024-01-15',
          notes: 'حجاج مميزون - خدمة شخصية'
        },
        {
          id: 2,
          customerName: 'خديجة محمد السالم',
          customerPhone: '+************',
          customerEmail: '<EMAIL>',
          passportNumber: 'B87654321',
          nationalId: '0987654321',
          packageType: 'standard',
          hajjType: 'qiran',
          departureDate: '2024-06-15',
          returnDate: '2024-06-30',
          duration: 15,
          pilgrims: 4,
          maleCount: 2,
          femaleCount: 2,
          childCount: 0,
          hotelMakkah: 'فندق الأجياد',
          hotelMadinah: 'فندق طيبة',
          hotelMina: 'مخيم منى العادي',
          hotelArafat: 'مخيم عرفات',
          roomType: 'quad',
          transportType: 'bus',
          visaIncluded: true,
          flightIncluded: true,
          mealPlan: 'half-board',
          guidedTour: true,
          amount: 10000,
          paidAmount: 0,
          remainingAmount: 10000,
          currency: 'USD',
          status: 'pending',
          paymentStatus: 'unpaid',
          bookingReference: '*********',
          bookingDate: '2024-01-14',
          notes: 'عائلة - غرفة رباعية'
        },
        {
          id: 3,
          customerName: 'يوسف علي الحداد',
          customerPhone: '+************',
          customerEmail: '<EMAIL>',
          passportNumber: 'C11223344',
          nationalId: '1122334455',
          packageType: 'economy',
          hajjType: 'ifrad',
          departureDate: '2024-06-20',
          returnDate: '2024-07-05',
          duration: 15,
          pilgrims: 2,
          maleCount: 1,
          femaleCount: 1,
          childCount: 0,
          hotelMakkah: 'فندق النور',
          hotelMadinah: 'فندق الهدى',
          hotelMina: 'مخيم منى الاقتصادي',
          hotelArafat: 'مخيم عرفات الاقتصادي',
          roomType: 'double',
          transportType: 'bus',
          visaIncluded: true,
          flightIncluded: true,
          mealPlan: 'breakfast',
          guidedTour: false,
          amount: 5000000,
          paidAmount: 2500000,
          remainingAmount: 2500000,
          currency: 'YER',
          status: 'confirmed',
          paymentStatus: 'partial',
          bookingReference: '*********',
          bookingDate: '2024-01-12',
          notes: 'حجاج من اليمن - باقة اقتصادية'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddBooking = (e) => {
    e.preventDefault();
    const booking = {
      id: bookings.length + 1,
      ...newBooking,
      amount: parseFloat(newBooking.amount),
      paidAmount: parseFloat(newBooking.paidAmount),
      remainingAmount: parseFloat(newBooking.amount) - parseFloat(newBooking.paidAmount),
      pilgrims: parseInt(newBooking.maleCount) + parseInt(newBooking.femaleCount) + parseInt(newBooking.childCount),
      bookingDate: new Date().toISOString().split('T')[0],
      bookingReference: `HJ${new Date().getFullYear()}${String(bookings.length + 1).padStart(3, '0')}`
    };
    setBookings([booking, ...bookings]);
    setNewBooking({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      passportNumber: '',
      nationalId: '',
      packageType: 'economy',
      hajjType: 'tamattu',
      departureDate: '',
      returnDate: '',
      duration: 14,
      pilgrims: 1,
      maleCount: 1,
      femaleCount: 0,
      childCount: 0,
      hotelMakkah: '',
      hotelMadinah: '',
      hotelMina: '',
      hotelArafat: '',
      roomType: 'quad',
      transportType: 'bus',
      visaIncluded: true,
      flightIncluded: true,
      mealPlan: 'full-board',
      guidedTour: true,
      amount: '',
      paidAmount: 0,
      remainingAmount: 0,
      currency: 'SAR',
      status: 'pending',
      paymentStatus: 'unpaid',
      bookingReference: '',
      specialRequests: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const handleStatusChange = (id, newStatus) => {
    setBookings(bookings.map(booking => 
      booking.id === id ? { ...booking, status: newStatus } : booking
    ));
  };

  const handleDeleteBooking = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
      setBookings(bookings.filter(booking => booking.id !== id));
    }
  };

  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    handleDeleteBooking(booking.id);
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, 'الحج');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, 'الحج');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, 'الحج');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, 'الحج');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedIds.length} حجز؟`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'cancelled': return '#e74c3c';
      case 'completed': return '#8e44ad';
      default: return '#95a5a6';
    }
  };

  const getPackageText = (packageType) => {
    switch (packageType) {
      case 'economy': return 'اقتصادية';
      case 'standard': return 'عادية';
      case 'vip': return 'مميزة';
      case 'luxury': return 'فاخرة';
      default: return packageType;
    }
  };

  const getHajjTypeText = (hajjType) => {
    switch (hajjType) {
      case 'tamattu': return 'تمتع';
      case 'qiran': return 'قران';
      case 'ifrad': return 'إفراد';
      default: return hajjType;
    }
  };

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.bookingReference.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || booking.status === filterStatus;
    const matchesPackage = filterPackage === 'all' || booking.packageType === filterPackage;
    return matchesSearch && matchesStatus && matchesPackage;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #8e44ad',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل حجوزات الحج...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h2 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>🏛️ حجوزات الحج</h2>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة وتتبع حجوزات رحلات الحج</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ حجز حج جديد
        </button>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في حجوزات الحج..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="pending">معلق</option>
          <option value="confirmed">مؤكد</option>
          <option value="completed">مكتمل</option>
          <option value="cancelled">ملغي</option>
        </select>
        <select
          value={filterPackage}
          onChange={(e) => setFilterPackage(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الباقات</option>
          <option value="economy">اقتصادية</option>
          <option value="standard">عادية</option>
          <option value="vip">مميزة</option>
          <option value="luxury">فاخرة</option>
        </select>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي الحجوزات', value: bookings.length, color: '#8e44ad', icon: '🏛️' },
          { title: 'الحجوزات المؤكدة', value: bookings.filter(b => b.status === 'confirmed').length, color: '#27ae60', icon: '✅' },
          { title: 'الحجوزات المعلقة', value: bookings.filter(b => b.status === 'pending').length, color: '#f39c12', icon: '⏳' },
          { 
            title: 'إجمالي المبيعات', 
            value: (() => {
              const totals = bookings.reduce((acc, b) => {
                const currency = b.currency || 'SAR';
                acc[currency] = (acc[currency] || 0) + (b.amount || 0);
                return acc;
              }, {});
              
              return Object.entries(totals)
                .map(([currency, amount]) => formatCurrency(amount, currency))
                .join(' | ');
            })(),
            color: '#e67e22', 
            icon: '💰' 
          }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {stat.value}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

      {/* Bookings Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>الحاج</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الباقة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>نوع الحج</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>التواريخ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحجاج</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>المبلغ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookings.map((booking) => (
                <tr key={booking.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedBookings.includes(booking.id)}
                      onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{booking.customerName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{booking.customerPhone}</div>
                      <div style={{ fontSize: '11px', color: '#95a5a6' }}>{booking.bookingReference}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      background: booking.packageType === 'luxury' ? '#8e44ad20' : 
                                 booking.packageType === 'vip' ? '#f39c1220' : 
                                 booking.packageType === 'standard' ? '#27ae6020' : '#3498db20',
                      color: booking.packageType === 'luxury' ? '#8e44ad' : 
                             booking.packageType === 'vip' ? '#f39c12' : 
                             booking.packageType === 'standard' ? '#27ae60' : '#3498db'
                    }}>
                      {getPackageText(booking.packageType)}
                    </span>
                    <div style={{ fontSize: '11px', color: '#7f8c8d', marginTop: '2px' }}>
                      {booking.duration} يوم
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      background: '#8e44ad20',
                      color: '#8e44ad'
                    }}>
                      {getHajjTypeText(booking.hajjType)}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div>
                      <div>📅 {booking.departureDate}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>↩️ {booking.returnDate}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold' }}>
                      {booking.pilgrims} 👤
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      {booking.maleCount > 0 && `${booking.maleCount} رجل`}
                      {booking.femaleCount > 0 && ` ${booking.femaleCount} امرأة`}
                      {booking.childCount > 0 && ` ${booking.childCount} طفل`}
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold', color: '#27ae60' }}>
                      {formatCurrency(booking.amount, booking.currency)}
                    </div>
                    {booking.remainingAmount > 0 && (
                      <div style={{ fontSize: '11px', color: '#e74c3c' }}>
                        متبقي: {formatCurrency(booking.remainingAmount, booking.currency)}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <select
                      value={booking.status}
                      onChange={(e) => handleStatusChange(booking.id, e.target.value)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '20px',
                        border: 'none',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        background: `${getStatusColor(booking.status)}20`,
                        color: getStatusColor(booking.status),
                        cursor: 'pointer'
                      }}
                    >
                      <option value="pending">معلق</option>
                      <option value="confirmed">مؤكد</option>
                      <option value="completed">مكتمل</option>
                      <option value="cancelled">ملغي</option>
                    </select>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <BookingActions
                      booking={booking}
                      onView={handleViewBooking}
                      onEdit={handleEditBooking}
                      onDelete={handleDeleteSingleBooking}
                      onPrint={handlePrintBooking}
                      onSavePDF={handleSavePDFBooking}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Booking Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '900px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>🏛️ إضافة حجز حج جديد</h2>
            
            <form onSubmit={handleAddBooking}>
              {/* معلومات الحاج */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>👤 معلومات الحاج</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم الحاج</label>
                    <input
                      type="text"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({...newBooking, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({...newBooking, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الجواز</label>
                    <input
                      type="text"
                      value={newBooking.passportNumber}
                      onChange={(e) => setNewBooking({...newBooking, passportNumber: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهوية</label>
                    <input
                      type="text"
                      value={newBooking.nationalId}
                      onChange={(e) => setNewBooking({...newBooking, nationalId: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* تفاصيل الحج */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>🏛️ تفاصيل الحج</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الباقة</label>
                    <select
                      value={newBooking.packageType}
                      onChange={(e) => setNewBooking({...newBooking, packageType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="economy">اقتصادية</option>
                      <option value="standard">عادية</option>
                      <option value="vip">مميزة</option>
                      <option value="luxury">فاخرة</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الحج</label>
                    <select
                      value={newBooking.hajjType}
                      onChange={(e) => setNewBooking({...newBooking, hajjType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="tamattu">تمتع</option>
                      <option value="qiran">قران</option>
                      <option value="ifrad">إفراد</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مدة الرحلة (أيام)</label>
                    <input
                      type="number"
                      min="10"
                      max="30"
                      value={newBooking.duration}
                      onChange={(e) => setNewBooking({...newBooking, duration: parseInt(e.target.value)})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* التواريخ */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>📅 التواريخ</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ المغادرة</label>
                    <input
                      type="date"
                      value={newBooking.departureDate}
                      onChange={(e) => setNewBooking({...newBooking, departureDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ العودة</label>
                    <input
                      type="date"
                      value={newBooking.returnDate}
                      onChange={(e) => setNewBooking({...newBooking, returnDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* الحجاج */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>👥 الحجاج</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الرجال</label>
                    <input
                      type="number"
                      min="0"
                      value={newBooking.maleCount}
                      onChange={(e) => setNewBooking({...newBooking, maleCount: parseInt(e.target.value)})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>النساء</label>
                    <input
                      type="number"
                      min="0"
                      value={newBooking.femaleCount}
                      onChange={(e) => setNewBooking({...newBooking, femaleCount: parseInt(e.target.value)})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الأطفال</label>
                    <input
                      type="number"
                      min="0"
                      value={newBooking.childCount}
                      onChange={(e) => setNewBooking({...newBooking, childCount: parseInt(e.target.value)})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* الإقامة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>🏨 الإقامة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>فندق مكة</label>
                    <input
                      type="text"
                      value={newBooking.hotelMakkah}
                      onChange={(e) => setNewBooking({...newBooking, hotelMakkah: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>فندق المدينة</label>
                    <input
                      type="text"
                      value={newBooking.hotelMadinah}
                      onChange={(e) => setNewBooking({...newBooking, hotelMadinah: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مخيم منى</label>
                    <input
                      type="text"
                      value={newBooking.hotelMina}
                      onChange={(e) => setNewBooking({...newBooking, hotelMina: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>مخيم عرفات</label>
                    <input
                      type="text"
                      value={newBooking.hotelArafat}
                      onChange={(e) => setNewBooking({...newBooking, hotelArafat: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إجمالي المبلغ</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.amount}
                      onChange={(e) => setNewBooking({...newBooking, amount: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ المدفوع</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.paidAmount}
                      onChange={(e) => setNewBooking({...newBooking, paidAmount: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع العملة</label>
                    <select
                      value={newBooking.currency}
                      onChange={(e) => setNewBooking({...newBooking, currency: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                      <option value="YER">🇾🇪 ريال يمني (YER)</option>
                      <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>📝 ملاحظات</h3>
                <textarea
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: 'linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة الحجز
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />
    </div>
  );
};

export default HajjBookingPage;