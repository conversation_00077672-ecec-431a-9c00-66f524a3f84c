/* Modern Button Component Styles */

.modern-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-family: var(--font-family-arabic);
  font-weight: var(--font-medium);
  line-height: 1;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  overflow: hidden;
  outline: none;
  background: transparent;
}

.modern-btn:focus-visible {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Button Variants */
.modern-btn--primary {
  background: var(--gradient-primary);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.modern-btn--primary:hover:not(.modern-btn--disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn--primary:active {
  transform: translateY(0);
}

.modern-btn--secondary {
  background: var(--gradient-secondary);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.modern-btn--secondary:hover:not(.modern-btn--disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn--outline {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  box-shadow: none;
}

.modern-btn--outline:hover:not(.modern-btn--disabled) {
  background: var(--primary-600);
  color: var(--neutral-0);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modern-btn--ghost {
  background: transparent;
  color: var(--neutral-600);
  box-shadow: none;
}

.modern-btn--ghost:hover:not(.modern-btn--disabled) {
  background: var(--neutral-100);
  color: var(--neutral-800);
}

.modern-btn--success {
  background: var(--gradient-success);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.modern-btn--success:hover:not(.modern-btn--disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn--warning {
  background: var(--gradient-warning);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.modern-btn--warning:hover:not(.modern-btn--disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn--error {
  background: var(--gradient-error);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.modern-btn--error:hover:not(.modern-btn--disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-btn--info {
  background: linear-gradient(135deg, var(--info-500) 0%, var(--info-700) 100%);
  color: var(--neutral-0);
  box-shadow: var(--shadow-md);
}

.modern-btn--info:hover:not(.modern-btn--disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Button Sizes */
.modern-btn--xs {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  border-radius: var(--radius-md);
}

.modern-btn--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-lg);
}

.modern-btn--md {
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  border-radius: var(--radius-lg);
}

.modern-btn--lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

.modern-btn--xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--text-xl);
  border-radius: var(--radius-xl);
}

/* Button Shapes */
.modern-btn--rounded {
  /* Default border-radius applied by size */
}

.modern-btn--square {
  border-radius: var(--radius-base);
}

.modern-btn--circle {
  border-radius: var(--radius-full);
  aspect-ratio: 1;
}

.modern-btn--pill {
  border-radius: var(--radius-full);
}

/* Icon Only Buttons */
.modern-btn--icon-only {
  padding: var(--space-3);
  aspect-ratio: 1;
}

.modern-btn--icon-only.modern-btn--xs {
  padding: var(--space-1);
}

.modern-btn--icon-only.modern-btn--sm {
  padding: var(--space-2);
}

.modern-btn--icon-only.modern-btn--lg {
  padding: var(--space-4);
}

.modern-btn--icon-only.modern-btn--xl {
  padding: var(--space-5);
}

/* Button States */
.modern-btn--loading {
  pointer-events: none;
}

.modern-btn--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.modern-btn--full-width {
  width: 100%;
}

/* Glass Effect */
.modern-btn--glass {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: var(--neutral-800);
}

.modern-btn--glass:hover:not(.modern-btn--disabled) {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Gradient Enhancement */
.modern-btn--gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Button Components */
.modern-btn__text {
  display: flex;
  align-items: center;
}

.modern-btn__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
}

.modern-btn__icon--left {
  margin-left: calc(var(--space-1) * -1);
}

.modern-btn__icon--right {
  margin-right: calc(var(--space-1) * -1);
}

/* Loading Spinner */
.modern-btn__spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-btn__spinner-icon {
  width: 1.2em;
  height: 1.2em;
  animation: spin 1s linear infinite;
}

.modern-btn__spinner-circle {
  animation: spinDash 1.5s ease-in-out infinite;
}

@keyframes spinDash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* Ripple Effect */
.modern-btn__ripple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: inherit;
  overflow: hidden;
}

.modern-btn:active .modern-btn__ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* Icon Button Specific Styles */
.icon-button {
  /* Inherits from modern-btn */
}

/* Button Group */
.modern-btn-group {
  display: flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-base);
}

.modern-btn-group--horizontal {
  flex-direction: row;
}

.modern-btn-group--vertical {
  flex-direction: column;
}

.modern-btn-group .modern-btn {
  border-radius: 0;
  box-shadow: none;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-btn-group .modern-btn:last-child {
  border-right: none;
}

.modern-btn-group--vertical .modern-btn {
  border-right: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-btn-group--vertical .modern-btn:last-child {
  border-bottom: none;
}

/* Floating Action Button */
.fab {
  position: fixed;
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-2xl);
}

.fab--bottom-right {
  bottom: var(--space-6);
  right: var(--space-6);
}

.fab--bottom-left {
  bottom: var(--space-6);
  left: var(--space-6);
}

.fab--top-right {
  top: var(--space-6);
  right: var(--space-6);
}

.fab--top-left {
  top: var(--space-6);
  left: var(--space-6);
}

.fab:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-2xl);
}

/* Split Button */
.split-button {
  position: relative;
  display: inline-flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.split-button__main {
  border-radius: 0;
  box-shadow: none;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.split-button__dropdown {
  border-radius: 0;
  box-shadow: none;
  padding: var(--space-3);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.split-button__menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-dropdown);
  overflow: hidden;
  margin-top: var(--space-1);
}

.split-button__menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  text-align: right;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

.split-button__menu-item:hover {
  background: var(--neutral-100);
}

.split-button__menu-icon {
  font-size: var(--text-base);
}

/* Toggle Button */
.toggle-button {
  /* Inherits from modern-btn */
}

.toggle-button--active {
  /* Active state handled by variant change */
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-btn--lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }
  
  .modern-btn--xl {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
  }
  
  .fab {
    bottom: var(--space-4);
    right: var(--space-4);
  }
  
  .fab--bottom-left {
    bottom: var(--space-4);
    left: var(--space-4);
  }
  
  .modern-btn-group--horizontal {
    flex-direction: column;
  }
  
  .modern-btn-group--horizontal .modern-btn {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .modern-btn-group--horizontal .modern-btn:last-child {
    border-bottom: none;
  }
}

@media (max-width: 480px) {
  .modern-btn {
    font-size: var(--text-sm);
  }
  
  .modern-btn--full-width {
    justify-content: center;
  }
  
  .split-button__menu {
    right: auto;
    left: 0;
    width: 100%;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modern-btn--ghost {
    color: var(--neutral-400);
  }
  
  .modern-btn--ghost:hover:not(.modern-btn--disabled) {
    background: var(--neutral-800);
    color: var(--neutral-200);
  }
  
  .modern-btn--outline {
    color: var(--primary-400);
    border-color: var(--primary-400);
  }
  
  .modern-btn--outline:hover:not(.modern-btn--disabled) {
    background: var(--primary-400);
    color: var(--neutral-900);
  }
  
  .split-button__menu {
    background: var(--neutral-800);
    border-color: var(--neutral-700);
  }
  
  .split-button__menu-item {
    color: var(--neutral-300);
  }
  
  .split-button__menu-item:hover {
    background: var(--neutral-700);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .modern-btn {
    border: 2px solid currentColor;
  }
  
  .modern-btn--primary,
  .modern-btn--secondary,
  .modern-btn--success,
  .modern-btn--warning,
  .modern-btn--error {
    background: currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modern-btn {
    transition: none;
  }
  
  .modern-btn:hover {
    transform: none;
  }
  
  .modern-btn__spinner-icon {
    animation: none;
  }
  
  .modern-btn--gradient {
    animation: none;
  }
  
  .fab:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .modern-btn {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .fab {
    display: none !important;
  }
  
  .split-button__menu {
    display: none !important;
  }
}