# 🔗 نظام ربط التأشيرات مع العملاء

## 🎯 نظرة عامة

تم إنشاء نظام متقدم لربط مخزون التأشيرات مع العملاء تلقائياً. هذا النظام يوفر:

- **إدارة مخزون التأشيرات** مع تتبع شامل
- **ربط تلقائي** بين التأشيرات والعملاء
- **بحث ذكي** يشمل أرقام التأشيرات
- **إشعارات فورية** عند إضافة تأشيرات جديدة
- **واجهة مستخدم حديثة** مع تأثيرات بصرية

## 🚀 كيفية الاستخدام

### 1️⃣ اختبار النظام

قم بزيارة الصفحات التالية لاختبار النظام:

```
http://localhost:3000/test-visa-customer-link
http://localhost:3000/test-notifications
```

### 2️⃣ إضافة تأشيرة للمخزون

1. اذهب لصفحة **مخزون التأشيرات**
2. انقر على "إضافة تأشيرة جديدة"
3. املأ البيانات المطلوبة:
   - رقم الصادر (مثل: V2024001)
   - رقم السجل (مثل: R2024001)
   - نوع التأشيرة (عمل/زيارة)
   - الجنسية
   - اسم الكفيل
   - التواريخ والأسعار
4. احفظ التأشيرة

### 3️⃣ إضافة عميل مع ربط التأشيرة

1. اذهب لصفحة **إدارة العملاء**
2. انقر على "إضافة عميل جديد"
3. املأ بيانات العميل الأساسية
4. في قسم "ربط التأشيرة":
   - ابحث عن التأشيرة المطلوبة
   - اختر التأشيرة من القائمة
   - ستظهر تفاصيل التأشيرة المختارة
5. احفظ العميل

### 4️⃣ البحث السريع

يمكنك البحث في العملاء باستخدام:
- اسم العميل
- رقم الهاتف
- البريد الإلكتروني
- رقم الهوية
- رقم الجواز
- **رقم الصادر للتأشيرة**
- **رقم السجل للتأشيرة**

## ⌨️ اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|----------|---------|
| `Ctrl + Shift + V` | إضافة تأشيرة جديدة |
| `Ctrl + Shift + C` | إضافة عميل جديد |
| `Ctrl + Shift + F` | البحث السريع |
| `Ctrl + /` | عرض جميع الاختصارات |
| `Ctrl + T` | اختبار الإشعارات |

## 🎨 المميزات المتقدمة

### 📊 إحصائيات فورية
- عدد التأشيرات المتاحة/المحجوزة/المستخدمة
- عدد العملاء مع/بدون تأشيرات
- إجمالي الإيرادات
- التأشيرات منتهية الصلاحية

### 🔔 نظام الإشعارات
- إشعارات نجاح/خطأ/تحذير/معلومات
- إشعارات مع إجراءات قابلة للنقر
- إشعارات تحميل مع شريط تقدم
- إشعارات قابلة للإغلاق

### 🎯 ربط ذكي
- ربط تلقائي عند إضافة عميل
- تحديث حالة التأشيرة فوراً
- إشعار عند إضافة تأشيرات جديدة
- مزامنة البيانات بين النوافذ

### 🔍 بحث متقدم
- بحث في الوقت الفعلي
- بحث متعدد المعايير
- تمييز النتائج
- فلترة حسب الحالة

## 📁 هيكل الملفات

```
frontend/src/
├── components/
│   ├── Inventory/
│   │   ├── VisaInventory.js          # مكون مخزون التأشيرات
│   │   └── VisaInventory.css         # أنماط المخزون
│   ├── Customers/
│   │   ├── EnhancedCustomers.js      # مكون العملاء المحسن
│   │   ├── EnhancedCustomers.css     # أنماط العملاء
│   │   ├── VisaSelector.js           # مكون اختيار التأشيرة
│   │   └── VisaSelector.css          # أنماط اختيار التأشيرة
│   ├── UI/
│   │   ├── EnhancedNotifications.js  # نظام الإشعارات
│   │   ├── EnhancedNotifications.css # أنماط الإشعارات
│   │   ├── KeyboardShortcuts.js      # اختصارات لوحة المفاتيح
│   │   └── KeyboardShortcuts.css     # أنماط الاختصارات
│   └── Test/
│       └── TestNotifications.js      # اختبار الإشعارات
├── pages/
│   └── Test/
│       └── TestVisaCustomerLink.js   # صفحة اختبار الربط
├── utils/
│   └── visaCustomerLink.js           # منطق الربط
└── App.js                            # التطبيق الرئيسي
```

## 🔧 إعداد النظام

تأكد من أن App.js يحتوي على الـ Providers المطلوبة:

```javascript
import { EnhancedNotificationsProvider } from './components/UI/EnhancedNotifications';
import { KeyboardShortcutsProvider } from './components/UI/KeyboardShortcuts';

function App() {
  return (
    <ThemeProvider>
      <EnhancedNotificationsProvider>
        <KeyboardShortcutsProvider>
          {/* باقي التطبيق */}
        </KeyboardShortcutsProvider>
      </EnhancedNotificationsProvider>
    </ThemeProvider>
  );
}
```

## 🐛 استكشاف الأخطاء

### خطأ "useNotifications must be used within NotificationProvider"
- تأكد من أن `EnhancedNotificationsProvider` يغلف التطبيق
- تأكد من استيراد الـ Provider بشكل صحيح

### خطأ "useShortcuts must be used within ShortcutsProvider"
- تأكد من أن `KeyboardShortcutsProvider` يغلف التطبيق
- تأكد من استيراد الـ Provider بشكل صحيح

### التأشيرات لا تظهر في العملاء
- تأكد من أن التأشيرة محفوظة بحالة "available"
- تحقق من أن البحث يعمل بشكل صحيح
- تأكد من أن الأحداث تنتشر بين المكونات

## 🎯 الخطوات التالية

1. **اختبر النظام** باستخدام الصفحات المخصصة للاختبار
2. **أضف تأشيرات** للمخزون
3. **أضف عملاء** واربطهم بالتأشيرات
4. **جرب البحث** بأرقام التأشيرات
5. **استخدم الاختصارات** لتسريع العمل

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من أن جميع الملفات موجودة
3. تأكد من أن الـ Providers مُعدة بشكل صحيح
4. جرب صفحة الاختبار أولاً

---

🎉 **مبروك!** لديك الآن نظام ربط متقدم بين التأشيرات والعملاء!
