/* 🔔 أنماط الإشعارات المحسنة */

/* 📦 حاوية الإشعارات */
.notification-container {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 🧹 زر مسح جميع الإشعارات */
.clear-all-notifications {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-2);
}

.clear-all-btn {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.clear-all-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

/* 🎴 عنصر الإشعار */
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  transform: translateX(100%);
  opacity: 0;
  position: relative;
  overflow: hidden;
}

.notification-item.visible {
  transform: translateX(0);
  opacity: 1;
}

.notification-item:hover {
  transform: translateX(-4px) translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

/* 🎯 تحسينات الأداء */
.notification-item {
  will-change: transform, opacity;
  contain: layout style paint;
}

/* 📱 استجابة للشاشات الصغيرة */
@media (max-width: 640px) {
  .notification-container {
    top: var(--space-4);
    right: var(--space-4);
    left: var(--space-4);
    max-width: none;
    width: auto;
  }

  .notification-item {
    padding: var(--space-3);
  }
}

/* 🎨 أنواع الإشعارات */
.notification-item.success {
  border-left: 4px solid var(--secondary-500);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, var(--glass-bg) 100%);
}

.notification-item.error {
  border-left: 4px solid var(--danger-500);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, var(--glass-bg) 100%);
}

.notification-item.warning {
  border-left: 4px solid var(--warning-500);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, var(--glass-bg) 100%);
}

.notification-item.info {
  border-left: 4px solid var(--primary-500);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, var(--glass-bg) 100%);
}

.notification-item.loading {
  border-left: 4px solid var(--info-500);
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, var(--glass-bg) 100%);
}

/* 🎭 أيقونة الإشعار */
.notification-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.notification-item.success .notification-icon {
  background: rgba(34, 197, 94, 0.2);
  color: var(--secondary-600);
}

.notification-item.error .notification-icon {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger-600);
}

.notification-item.warning .notification-icon {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-600);
}

.notification-item.info .notification-icon {
  background: rgba(59, 130, 246, 0.2);
  color: var(--primary-600);
}

.notification-item.loading .notification-icon {
  background: rgba(168, 85, 247, 0.2);
  color: var(--info-600);
  animation: pulse 2s infinite;
}

/* 📝 محتوى الإشعار */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: var(--space-1);
  line-height: 1.4;
}

.notification-message {
  font-size: 0.875rem;
  color: var(--neutral-600);
  line-height: 1.5;
  margin-bottom: var(--space-1);
}

.notification-description {
  font-size: 0.75rem;
  color: var(--neutral-500);
  line-height: 1.4;
}

/* 🎬 أزرار الإجراءات */
.notification-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-3);
}

.notification-action {
  padding: var(--space-1) var(--space-3);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.notification-action.primary {
  background: var(--primary-500);
  color: white;
}

.notification-action.primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
}

.notification-action.secondary {
  background: var(--neutral-200);
  color: var(--neutral-700);
}

.notification-action.secondary:hover {
  background: var(--neutral-300);
  transform: translateY(-1px);
}

/* ❌ زر الإغلاق */
.notification-close {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  color: var(--neutral-500);
  border-radius: var(--radius-full);
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: bold;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  background: var(--danger-500);
  color: white;
  transform: scale(1.1);
}

/* 📊 شريط التقدم */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.notification-progress-bar {
  height: 100%;
  background: var(--primary-500);
  transition: width 0.1s linear;
  border-radius: 0 3px 3px 0;
}

.notification-item.success .notification-progress-bar {
  background: var(--secondary-500);
}

.notification-item.error .notification-progress-bar {
  background: var(--danger-500);
}

.notification-item.warning .notification-progress-bar {
  background: var(--warning-500);
}

.notification-item.loading .notification-progress-bar {
  background: var(--info-500);
}

/* 🔔 إشعار سريع */
.quick-notification {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  animation: slideInRight 0.3s ease-out;
}

.quick-notification-icon {
  font-size: 1.25rem;
}

.quick-notification-message {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-700);
}

.quick-notification-close {
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  color: var(--neutral-500);
  border-radius: var(--radius-full);
  cursor: pointer;
  font-size: 0.75rem;
  transition: all var(--transition-fast);
}

.quick-notification-close:hover {
  background: var(--danger-500);
  color: white;
}

/* 🔔 مركز الإشعارات */
.notification-center-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.notification-center {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  animation: zoomIn 0.3s ease-out;
}

.notification-center-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

.notification-center-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.notification-center-actions {
  display: flex;
  gap: var(--space-2);
}

.notification-center-content {
  max-height: 60vh;
  overflow-y: auto;
}

.notification-center-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.empty-message {
  font-size: 1.125rem;
  color: var(--neutral-500);
  font-weight: 500;
}

.notification-center-list {
  padding: var(--space-4);
}

.notification-center-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  border-bottom: 1px solid var(--glass-border);
}

.notification-center-item:last-child {
  border-bottom: none;
}

.notification-center-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.notification-center-item-icon {
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.notification-center-item-content {
  flex: 1;
  min-width: 0;
}

.notification-center-item-message {
  font-size: 0.875rem;
  color: var(--neutral-700);
  line-height: 1.5;
  margin-bottom: var(--space-1);
}

.notification-center-item-time {
  font-size: 0.75rem;
  color: var(--neutral-500);
}

.notification-center-item-close {
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  color: var(--neutral-500);
  border-radius: var(--radius-full);
  cursor: pointer;
  font-size: 0.75rem;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.notification-center-item-close:hover {
  background: var(--danger-500);
  color: white;
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .notification-title,
.dark-mode .notification-title {
  color: var(--neutral-200);
}

[data-theme="dark"] .notification-message,
.dark-mode .notification-message {
  color: var(--neutral-300);
}

[data-theme="dark"] .notification-center-header h3,
.dark-mode .notification-center-header h3 {
  color: var(--neutral-200);
}

[data-theme="dark"] .notification-center-item-message,
.dark-mode .notification-center-item-message {
  color: var(--neutral-300);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .notification-container {
    top: var(--space-4);
    right: var(--space-4);
    left: var(--space-4);
    max-width: none;
  }
  
  .notification-item {
    padding: var(--space-3);
  }
  
  .notification-center {
    width: 95%;
    max-height: 90vh;
  }
  
  .notification-center-header {
    padding: var(--space-4);
  }
}

/* 🎬 حركات خاصة */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .notification-item,
  .quick-notification,
  .notification-center-overlay,
  .notification-center {
    animation: none;
    transition: none;
  }
  
  .notification-item.visible {
    transform: none;
    opacity: 1;
  }
}
