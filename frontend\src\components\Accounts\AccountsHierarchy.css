.accounts-hierarchy {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.hierarchy-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.hierarchy-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
}

.hierarchy-sidebar {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-section {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.search-box {
  margin-bottom: 15px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 14px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
  font-size: 14px;
}

.filter-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.stats-section {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #3498db;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.category-stats h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: bold;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-stat {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.category-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.category-name {
  flex: 1;
  font-size: 12px;
  color: #2c3e50;
}

.category-count {
  font-size: 12px;
  font-weight: bold;
  color: #7f8c8d;
  background: white;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.hierarchy-main {
  flex: 1;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: auto;
}

.accounts-tree {
  min-height: 400px;
}

.no-accounts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
  text-align: center;
}

.no-accounts .icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.no-accounts h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.no-accounts p {
  margin: 0;
  font-size: 14px;
}

.account-node {
  margin-bottom: 2px;
}

.account-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e9ecef;
}

.account-item:hover {
  background: #f8f9fa;
  transform: translateX(-3px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-item.selected {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(-5px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.account-item.header {
  background: #f8f9fa;
  font-weight: bold;
  border: 2px solid #e9ecef;
}

.account-item.header.selected {
  background: linear-gradient(135deg, #2c3e50, #34495e);
}

.account-toggle {
  width: 20px;
  display: flex;
  justify-content: center;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #7f8c8d;
  padding: 2px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.toggle-btn.expanded {
  transform: rotate(0deg);
}

.account-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.account-item.selected .account-icon {
  color: white !important;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-badge {
  background: #3498db;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.account-item.selected .header-badge {
  background: rgba(255,255,255,0.2);
}

.account-code {
  font-size: 12px;
  color: #7f8c8d;
  font-family: monospace;
}

.account-item.selected .account-code {
  color: rgba(255,255,255,0.8);
}

.account-balance {
  font-size: 14px;
  font-weight: bold;
  text-align: left;
  min-width: 100px;
}

.account-item.selected .account-balance {
  color: white !important;
}

.account-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.account-item:hover .account-actions,
.account-item.selected .account-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #27ae6020;
  color: #27ae60;
}

.edit-btn:hover {
  background: #f39c1220;
  color: #f39c12;
}

.delete-btn:hover {
  background: #e74c3c20;
  color: #e74c3c;
}

.account-item.selected .action-btn {
  color: white;
}

.account-item.selected .action-btn:hover {
  background: rgba(255,255,255,0.2);
  color: white;
}

.account-children {
  margin-right: 20px;
  border-right: 2px solid #e9ecef;
  margin-top: 2px;
}

.account-details {
  width: 300px;
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  height: fit-content;
  max-height: 80vh;
  overflow-y: auto;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f2f6;
}

.details-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.close-details {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.details-content {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-item label {
  font-weight: bold;
  color: #7f8c8d;
  font-size: 12px;
  text-transform: uppercase;
}

.detail-item span {
  color: #2c3e50;
  font-size: 14px;
}

.detail-item .balance {
  color: #27ae60;
  font-weight: bold;
  font-size: 16px;
}

.details-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* نموذج الحساب */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.account-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 2px solid #f1f2f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 25px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:disabled {
  background: #f8f9fa;
  color: #7f8c8d;
}

.modal-footer {
  padding: 25px;
  border-top: 2px solid #f1f2f6;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .hierarchy-content {
    flex-direction: column;
  }
  
  .hierarchy-sidebar {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .hierarchy-sidebar > * {
    min-width: 250px;
  }
  
  .account-details {
    width: 100%;
    max-height: 400px;
  }
  
  .hierarchy-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .account-item {
    padding-right: 15px !important;
  }
  
  .account-actions {
    opacity: 1;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .accounts-hierarchy {
    padding: 10px;
  }
  
  .hierarchy-header,
  .search-section,
  .stats-section,
  .hierarchy-main,
  .account-details,
  .modal-content {
    padding: 15px;
  }
  
  .account-modal {
    margin: 10px;
    max-width: none;
  }
  
  .hierarchy-sidebar {
    flex-direction: column;
  }
  
  .hierarchy-sidebar > * {
    min-width: auto;
  }
}