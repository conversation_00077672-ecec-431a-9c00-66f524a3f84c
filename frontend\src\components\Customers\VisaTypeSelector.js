import React from 'react';
import './VisaTypeSelector.css';

const VisaTypeSelector = ({ value, onChange, disabled = false }) => {
  const visaTypes = [
    {
      value: '',
      label: 'اختر نوع الفيزا',
      icon: '📋',
      color: '#95a5a6',
      disabled: true
    },
    {
      value: 'work',
      label: 'فيزة عمل',
      icon: '💼',
      color: '#27ae60',
      description: 'للعمل الدائم في البلد المقصود'
    },
    {
      value: 'work_temporary',
      label: 'فيزة عمل مؤقتة',
      icon: '⏰',
      color: '#f39c12',
      description: 'للعمل المؤقت لفترة محددة'
    },
    {
      value: 'family_visit',
      label: 'فيزة زيارة عائلية',
      icon: '👨‍👩‍👧‍👦',
      color: '#8e44ad',
      description: 'لزيارة الأقارب والعائلة'
    },
    {
      value: 'tourist',
      label: 'فيزة سياحية',
      icon: '🏖️',
      color: '#3498db',
      description: 'للسياحة والترفيه'
    },
    {
      value: 'business',
      label: 'فيزة تجارية',
      icon: '🤝',
      color: '#e67e22',
      description: 'للأعمال التجارية والاجتماعات'
    },
    {
      value: 'study',
      label: 'فيزة دراسة',
      icon: '🎓',
      color: '#2980b9',
      description: 'للدراسة والتعليم'
    },
    {
      value: 'transit',
      label: 'فيزة ترانزيت',
      icon: '✈️',
      color: '#16a085',
      description: 'للمرور عبر البلد إلى وجهة أخرى'
    },
    {
      value: 'medical',
      label: 'فيزة علاج',
      icon: '🏥',
      color: '#e74c3c',
      description: 'للعلاج الطبي'
    },
    {
      value: 'diplomatic',
      label: 'فيزة دبلوماسية',
      icon: '🏛️',
      color: '#9b59b6',
      description: 'للدبلوماسيين والمسؤولين'
    },
    {
      value: 'official',
      label: 'فيزة رسمية',
      icon: '📜',
      color: '#34495e',
      description: 'للمهام الرسمية الحكومية'
    },
    {
      value: 'other',
      label: 'أخرى',
      icon: '📝',
      color: '#7f8c8d',
      description: 'أنواع أخرى من الفيز'
    }
  ];

  const selectedVisa = visaTypes.find(visa => visa.value === value);

  return (
    <div className="visa-type-selector">
      <select
        value={value}
        onChange={onChange}
        disabled={disabled}
        className="visa-select"
      >
        {visaTypes.map((visa) => (
          <option 
            key={visa.value} 
            value={visa.value}
            disabled={visa.disabled}
          >
            {visa.icon} {visa.label}
          </option>
        ))}
      </select>
      
      {selectedVisa && selectedVisa.value && (
        <div className="visa-info" style={{ borderColor: selectedVisa.color }}>
          <div className="visa-icon" style={{ backgroundColor: selectedVisa.color }}>
            {selectedVisa.icon}
          </div>
          <div className="visa-details">
            <div className="visa-name" style={{ color: selectedVisa.color }}>
              {selectedVisa.label}
            </div>
            <div className="visa-description">
              {selectedVisa.description}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisaTypeSelector;
