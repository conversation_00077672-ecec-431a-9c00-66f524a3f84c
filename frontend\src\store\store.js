import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import customersSlice from './slices/customersSlice';
import suppliersSlice from './slices/suppliersSlice';
import agentsSlice from './slices/agentsSlice';
import bookingsSlice from './slices/bookingsSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    customers: customersSlice,
    suppliers: suppliersSlice,
    agents: agentsSlice,
    bookings: bookingsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});