/* Modern Alert Components Styles */

/* ===== MODERN ALERT ===== */
.modern-alert {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius-xl);
  border: 1px solid transparent;
  font-family: var(--font-family-arabic);
  position: relative;
  overflow: hidden;
  animation: slideInDown 0.3s ease-out;
}

.modern-alert::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
  opacity: 0.3;
}

/* Alert Variants */
.modern-alert--success {
  background: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.modern-alert--error {
  background: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

.modern-alert--warning {
  background: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.modern-alert--info {
  background: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-800);
}

.modern-alert--primary {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-800);
}

.modern-alert--secondary {
  background: var(--secondary-50);
  border-color: var(--secondary-200);
  color: var(--secondary-800);
}

/* Alert Sizes */
.modern-alert--xs {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.modern-alert--sm {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
}

.modern-alert--md {
  padding: var(--space-4);
  font-size: var(--text-base);
}

.modern-alert--lg {
  padding: var(--space-5) var(--space-6);
  font-size: var(--text-lg);
}

.modern-alert--xl {
  padding: var(--space-6) var(--space-8);
  font-size: var(--text-xl);
}

.modern-alert-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  flex: 1;
}

.modern-alert-icon {
  font-size: var(--text-xl);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.modern-alert-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.modern-alert-title {
  font-weight: var(--font-bold);
  font-size: 1.1em;
  line-height: var(--leading-tight);
  margin: 0;
}

.modern-alert-message {
  line-height: var(--leading-relaxed);
  margin: 0;
}

.modern-alert-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-3);
}

.modern-alert-dismiss {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  color: currentColor;
  opacity: 0.7;
}

.modern-alert-dismiss:hover {
  opacity: 1;
}

/* ===== TOAST NOTIFICATIONS ===== */
.modern-toast-container {
  position: fixed;
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-width: 400px;
  pointer-events: none;
}

.modern-toast-container--top-right {
  top: var(--space-6);
  right: var(--space-6);
}

.modern-toast-container--top-left {
  top: var(--space-6);
  left: var(--space-6);
}

.modern-toast-container--bottom-right {
  bottom: var(--space-6);
  right: var(--space-6);
}

.modern-toast-container--bottom-left {
  bottom: var(--space-6);
  left: var(--space-6);
}

.modern-toast-container--top-center {
  top: var(--space-6);
  left: 50%;
  transform: translateX(-50%);
}

.modern-toast-container--bottom-center {
  bottom: var(--space-6);
  left: 50%;
  transform: translateX(-50%);
}

.modern-toast {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--neutral-0);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--neutral-200);
  pointer-events: auto;
  position: relative;
  overflow: hidden;
  animation: slideInRight 0.3s ease-out;
  max-width: 100%;
}

.modern-toast--exiting {
  animation: slideOutRight 0.3s ease-in forwards;
}

/* Toast Variants */
.modern-toast--success {
  border-left: 4px solid var(--success-500);
}

.modern-toast--error {
  border-left: 4px solid var(--error-500);
}

.modern-toast--warning {
  border-left: 4px solid var(--warning-500);
}

.modern-toast--info {
  border-left: 4px solid var(--info-500);
}

.modern-toast-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  flex: 1;
}

.modern-toast-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.modern-toast-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.modern-toast-title {
  font-weight: var(--font-bold);
  font-size: var(--text-sm);
  color: var(--neutral-800);
  line-height: var(--leading-tight);
  margin: 0;
}

.modern-toast-message {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
  margin: 0;
}

.modern-toast-dismiss {
  position: absolute;
  top: var(--space-2);
  left: var(--space-2);
  color: var(--neutral-500);
}

.modern-toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--primary-500);
  animation: toastProgress linear forwards;
  transform-origin: left;
}

@keyframes toastProgress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Toast Animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== BANNER ===== */
.modern-banner {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  position: relative;
  animation: slideInDown 0.3s ease-out;
}

.modern-banner--sticky {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

/* Banner Variants */
.modern-banner--success {
  background: var(--gradient-success);
  color: var(--neutral-0);
}

.modern-banner--error {
  background: var(--gradient-error);
  color: var(--neutral-0);
}

.modern-banner--warning {
  background: var(--gradient-warning);
  color: var(--neutral-0);
}

.modern-banner--info {
  background: var(--gradient-primary);
  color: var(--neutral-0);
}

.modern-banner-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex: 1;
}

.modern-banner-icon {
  font-size: var(--text-2xl);
  flex-shrink: 0;
}

.modern-banner-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.modern-banner-title {
  font-weight: var(--font-bold);
  font-size: var(--text-lg);
  line-height: var(--leading-tight);
  margin: 0;
}

.modern-banner-message {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  margin: 0;
  opacity: 0.9;
}

.modern-banner-actions {
  display: flex;
  gap: var(--space-2);
  flex-shrink: 0;
}

.modern-banner-dismiss {
  color: currentColor;
  opacity: 0.8;
}

.modern-banner-dismiss:hover {
  opacity: 1;
}

/* ===== STATUS BADGE ===== */
.modern-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: var(--font-semibold);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  position: relative;
}

/* Badge Variants */
.modern-status-badge--default {
  background: var(--neutral-100);
  color: var(--neutral-700);
}

.modern-status-badge--success {
  background: var(--success-100);
  color: var(--success-700);
}

.modern-status-badge--error {
  background: var(--error-100);
  color: var(--error-700);
}

.modern-status-badge--warning {
  background: var(--warning-100);
  color: var(--warning-700);
}

.modern-status-badge--info {
  background: var(--info-100);
  color: var(--info-700);
}

.modern-status-badge--primary {
  background: var(--primary-100);
  color: var(--primary-700);
}

.modern-status-badge--secondary {
  background: var(--secondary-100);
  color: var(--secondary-700);
}

/* Badge Sizes */
.modern-status-badge--xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
}

.modern-status-badge--sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
}

.modern-status-badge--md {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
}

.modern-status-badge--lg {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-base);
}

.modern-status-badge--pulse::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: currentColor;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
}

.modern-status-badge-icon {
  font-size: 0.8em;
}

.modern-status-badge-text {
  /* Text styling */
}

/* ===== PROGRESS ALERT ===== */
.modern-progress-alert {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius-xl);
  border: 1px solid transparent;
  animation: slideInDown 0.3s ease-out;
}

/* Progress Alert Variants */
.modern-progress-alert--success {
  background: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.modern-progress-alert--error {
  background: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

.modern-progress-alert--warning {
  background: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.modern-progress-alert--info {
  background: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-800);
}

.modern-progress-alert-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  width: 100%;
}

.modern-progress-alert-icon {
  font-size: var(--text-xl);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.modern-progress-alert-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.modern-progress-alert-title {
  font-weight: var(--font-bold);
  font-size: var(--text-base);
  line-height: var(--leading-tight);
  margin: 0;
}

.modern-progress-alert-message {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  margin: 0;
}

.modern-progress-alert-progress {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-top: var(--space-2);
}

.modern-progress-alert-progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.modern-progress-alert-progress-fill {
  height: 100%;
  background: currentColor;
  border-radius: inherit;
  transition: width var(--transition-base);
}

.modern-progress-alert-progress-text {
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  min-width: 40px;
  text-align: left;
}

/* ===== CALLOUT ===== */
.modern-callout {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  position: relative;
}

.modern-callout--bordered {
  border: 2px solid currentColor;
}

/* Callout Variants */
.modern-callout--success {
  background: var(--success-50);
  color: var(--success-700);
}

.modern-callout--error {
  background: var(--error-50);
  color: var(--error-700);
}

.modern-callout--warning {
  background: var(--warning-50);
  color: var(--warning-700);
}

.modern-callout--info {
  background: var(--info-50);
  color: var(--info-700);
}

.modern-callout--tip {
  background: var(--primary-50);
  color: var(--primary-700);
}

.modern-callout--note {
  background: var(--secondary-50);
  color: var(--secondary-700);
}

.modern-callout-icon {
  font-size: var(--text-3xl);
  flex-shrink: 0;
}

.modern-callout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.modern-callout-title {
  font-weight: var(--font-bold);
  font-size: var(--text-lg);
  line-height: var(--leading-tight);
  margin: 0;
}

.modern-callout-body {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .modern-toast-container {
    max-width: calc(100vw - var(--space-8));
  }
  
  .modern-toast-container--top-right,
  .modern-toast-container--bottom-right {
    right: var(--space-4);
  }
  
  .modern-toast-container--top-left,
  .modern-toast-container--bottom-left {
    left: var(--space-4);
  }
  
  .modern-toast-container--top-center,
  .modern-toast-container--bottom-center {
    left: var(--space-4);
    right: var(--space-4);
    transform: none;
  }
  
  .modern-banner {
    padding: var(--space-3) var(--space-4);
  }
  
  .modern-banner-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .modern-banner-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .modern-callout {
    padding: var(--space-4);
    gap: var(--space-3);
  }
  
  .modern-callout-icon {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .modern-alert {
    padding: var(--space-3);
  }
  
  .modern-alert-actions {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .modern-toast {
    padding: var(--space-3);
  }
  
  .modern-banner {
    padding: var(--space-3);
  }
  
  .modern-banner-title {
    font-size: var(--text-base);
  }
  
  .modern-banner-message {
    font-size: var(--text-sm);
  }
  
  .modern-callout {
    flex-direction: column;
    text-align: center;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-toast {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .modern-toast-title {
    color: var(--neutral-200);
  }
  
  .modern-toast-message {
    color: var(--neutral-400);
  }
  
  .modern-toast-dismiss {
    color: var(--neutral-400);
  }
  
  .modern-status-badge--default {
    background: var(--neutral-700);
    color: var(--neutral-300);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-toast-container,
  .modern-banner {
    display: none !important;
  }
  
  .modern-alert,
  .modern-callout {
    border: 1px solid #000 !important;
    background: white !important;
    color: black !important;
  }
  
  .modern-alert-dismiss,
  .modern-banner-dismiss,
  .modern-toast-dismiss {
    display: none !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-alert,
  .modern-toast,
  .modern-banner {
    animation: none !important;
  }
  
  .modern-toast--exiting {
    animation: none !important;
  }
  
  .modern-status-badge--pulse::before {
    animation: none !important;
  }
  
  .modern-toast-progress {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-alert,
  .modern-toast,
  .modern-banner,
  .modern-callout {
    border: 2px solid currentColor !important;
  }
  
  .modern-status-badge {
    border: 1px solid currentColor;
  }
}