@echo off
chcp 65001 >nul
title إصلاح سريع - نظام العملاء

echo.
echo ========================================
echo    🔧 إصلاح سريع للنظام
echo ========================================
echo.

echo 🛑 إيقاف العمليات الجارية...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 🧹 تنظيف ذاكرة التخزين المؤقت...
cd /d "%~dp0frontend"
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache" >nul 2>&1
    echo ✅ تم تنظيف ذاكرة التخزين المؤقت
)

echo.
echo 📦 تحديث التبعيات...
call npm install >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم تحديث التبعيات بنجاح
) else (
    echo ❌ فشل في تحديث التبعيات
)

echo.
echo 🚀 إعادة تشغيل النظام...
start "Frontend Server" cmd /c "npm start"

echo.
echo ⏳ انتظار تحميل النظام...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 فتح النظام...
start http://localhost:3000/customers

echo.
echo ========================================
echo ✅ تم إصلاح النظام بنجاح!
echo.
echo 📋 إذا استمرت المشاكل:
echo.
echo 1. تحقق من وحدة التحكم في المتصفح (F12)
echo 2. أعد تحميل الصفحة (Ctrl+F5)
echo 3. تأكد من عدم وجود برامج مكافحة فيروسات تحجب النظام
echo 4. تحقق من اتصال الإنترنت
echo.
echo اضغط أي مفتاح للخروج...
pause >nul