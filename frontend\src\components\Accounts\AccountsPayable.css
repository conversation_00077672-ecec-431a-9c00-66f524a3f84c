/* أنماط الذمم الدائنة المتقدمة */
.accounts-payable-advanced {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* رأس الصفحة */
.payables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  gap: 10px;
}

.header-controls .btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
  transform: translateY(-2px);
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
  transform: translateY(-2px);
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn.disabled,
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  background: #6c757d !important;
  border-color: #6c757d !important;
  color: #fff !important;
}

.btn.disabled:hover,
.btn:disabled:hover {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  background: #6c757d !important;
  border-color: #6c757d !important;
}

.btn-outline {
  background: transparent;
  border: 1px solid #ddd;
  color: #2c3e50;
}

.btn-outline:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.btn-sm {
  padding: 6px 10px;
  font-size: 12px;
}

/* الإحصائيات */
.payables-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stat-card.overdue {
  border-left: 4px solid #e74c3c;
}

.stat-card.priority {
  border-left: 4px solid #f39c12;
}

.stat-icon {
  font-size: 28px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

/* أدوات التحكم */
.payables-controls {
  margin-bottom: 20px;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-range label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.date-input {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
}

.supplier-filter {
  min-width: 200px;
}

.form-control {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.view-mode-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.view-mode-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-mode-btn:hover:not(.active) {
  background: #e9ecef;
}

/* عرض البيانات */
.payables-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* عرض الجدول */
.table-container {
  overflow-x: auto;
}

.payables-table {
  width: 100%;
  border-collapse: collapse;
}

.payables-table th {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.payables-table th:hover {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.payables-table td {
  padding: 12px 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.payables-table tr:hover {
  background: #f8f9fa;
}

.payables-table tr.overdue {
  background: rgba(231, 76, 60, 0.05);
}

.payables-table tr.paid {
  background: rgba(39, 174, 96, 0.05);
}

.payables-table tr.high {
  border-left: 3px solid #e74c3c;
}

.payables-table tr.medium {
  border-left: 3px solid #f39c12;
}

.payables-table tr.low {
  border-left: 3px solid #95a5a6;
}

.payables-table tfoot tr {
  background: #f8f9fa;
  font-weight: bold;
}

.payables-table tfoot td {
  border-top: 2px solid #2c3e50;
  border-bottom: 2px solid #2c3e50;
}

.supplier-name {
  text-align: right;
  font-weight: 500;
  color: #2c3e50;
}

.amount {
  text-align: left;
  font-weight: bold;
  font-family: monospace;
}

.amount.paid {
  color: #27ae60;
}

.amount.overdue {
  color: #e74c3c;
}

.amount.current {
  color: #3498db;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.paid {
  background: #d5f4e6;
  color: #27ae60;
}

.status-badge.overdue {
  background: #fadbd8;
  color: #e74c3c;
}

.status-badge.current {
  background: #d6eaf8;
  color: #3498db;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.priority-badge.high {
  background: #fadbd8;
  color: #e74c3c;
}

.priority-badge.medium {
  background: #fef9e7;
  color: #f39c12;
}

.priority-badge.low {
  background: #f8f9fa;
  color: #95a5a6;
}

.actions {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.action-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: scale(1.1);
}

.view-btn:hover {
  background: #3498db;
  color: white;
}

.payment-btn:hover {
  background: #28a745;
  color: white;
}

.schedule-btn:hover {
  background: #ffc107;
  color: #212529;
}

/* العرض التفصيلي */
.detailed-view {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payable-card {
  border: 1px solid #ddd;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.payable-card:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.payable-card.overdue {
  border-left: 4px solid #e74c3c;
}

.payable-card.paid {
  border-left: 4px solid #27ae60;
}

.payable-card.current {
  border-left: 4px solid #3498db;
}

.payable-card.high {
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.card-header {
  background: #f8f9fa;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.supplier-info h3 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 18px;
}

.invoice-number {
  color: #7f8c8d;
  font-size: 14px;
}

.status-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.age-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.age-badge.current {
  background: #d5f4e6;
  color: #27ae60;
}

.age-badge.overdue {
  background: #fadbd8;
  color: #e74c3c;
}

.card-body {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.payable-details,
.amount-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-item,
.amount-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f1f2f6;
}

.detail-item label,
.amount-item label {
  font-weight: 500;
  color: #7f8c8d;
}

.detail-item span,
.amount-item span {
  color: #2c3e50;
  font-weight: 500;
}

.detail-item span.high {
  color: #e74c3c;
  font-weight: bold;
}

.detail-item span.medium {
  color: #f39c12;
  font-weight: bold;
}

.detail-item span.low {
  color: #95a5a6;
}

.card-footer {
  background: #f8f9fa;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-schedule {
  color: #7f8c8d;
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

/* تحليل الأعمار والفئات */
.ageing-view,
.category-view {
  padding: 20px;
}

.ageing-summary h3,
.category-summary h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  text-align: center;
}

.ageing-chart,
.category-chart {
  margin-bottom: 30px;
}

.ageing-bars,
.category-bars {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ageing-bar,
.category-bar {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.bar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.age-label,
.category-label {
  font-weight: 500;
  color: #2c3e50;
}

.bar-container {
  height: 20px;
  background: #f1f2f6;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 5px;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.bar-fill.current {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.bar-fill.warning {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.bar-fill.critical {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.bar-fill.category {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.bar-footer {
  text-align: right;
}

.percentage {
  font-weight: bold;
  color: #2c3e50;
}

.ageing-table {
  margin-top: 20px;
}

.ageing-table tr.current {
  background: rgba(39, 174, 96, 0.05);
}

.ageing-table tr.warning {
  background: rgba(243, 156, 18, 0.05);
}

.ageing-table tr.critical {
  background: rgba(231, 76, 60, 0.05);
}

/* النماذج المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.add-modal,
.payment-modal,
.print-modal,
.export-modal,
.filter-modal,
.details-modal,
.schedule-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  margin: 20px;
  display: flex;
  flex-direction: column;
}

.add-modal,
.payment-modal,
.print-modal,
.export-modal,
.filter-modal,
.schedule-modal {
  max-width: 600px;
  min-width: 500px;
}

.details-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.modal-content {
  padding: 0;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* نماذج الإدخال */
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: auto;
  margin-bottom: 15px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 5px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-control {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* معلومات الذمة المحددة */
.selected-receivable-info,
.selected-payable-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  margin-bottom: 15px;
}

.receivable-summary,
.payable-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 500;
  color: #6c757d;
  font-size: 12px;
}

.info-item .value {
  font-weight: bold;
  color: #2c3e50;
  font-size: 13px;
}

.info-item .value.paid {
  color: #28a745;
}

.info-item .value.remaining {
  color: #dc3545;
  font-weight: bold;
}

.info-item .value.high {
  color: #dc3545;
}

.info-item .value.medium {
  color: #ffc107;
}

.info-item .value.low {
  color: #6c757d;
}

/* تحقق من صحة الدفعة */
.payment-validation {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.validation-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.validation-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

/* حاوية إدخال المبلغ */
.amount-input-container {
  display: flex;
  gap: 10px;
  align-items: stretch;
  width: 100%;
}

.amount-input-container .form-control {
  flex: 1;
  min-width: 0;
}

.full-payment-btn {
  white-space: nowrap;
  padding: 10px 15px;
  font-size: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid #28a745;
  background: transparent;
  color: #28a745;
  cursor: pointer;
  flex-shrink: 0;
}

.full-payment-btn:hover {
  background: #28a745;
  color: white;
  border-color: #28a745;
  transform: scale(1.05);
}

.full-payment-indicator {
  font-weight: bold;
  color: #28a745;
}

/* خيارات الطباعة */
.print-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #2c3e50;
  cursor: pointer;
}

.option-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

/* خيارات التصدير */
.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.format-selection h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-option:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.format-option input[type="radio"] {
  width: 16px;
  height: 16px;
}

.format-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.format-icon {
  font-size: 24px;
}

.format-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.format-desc {
  font-size: 12px;
  color: #7f8c8d;
}

.export-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.export-summary h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.summary-item .label {
  color: #7f8c8d;
}

.summary-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.summary-item .value.overdue {
  color: #e74c3c;
}

/* نموذج التصفية */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group > label {
  font-size: 14px;
  font-weight: bold;
  color: #2c3e50;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-inputs span {
  color: #7f8c8d;
  font-size: 14px;
}

/* نموذج التفاصيل */
.payable-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.details-header {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.amount-summary,
.description-section,
.schedule-section {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.amount-summary h4,
.description-section h4,
.schedule-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.schedule-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.schedule-item.overdue {
  border-left: 4px solid #e74c3c;
}

.schedule-item.pending {
  border-left: 4px solid #f39c12;
}

.schedule-item.paid {
  border-left: 4px solid #27ae60;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-date {
  font-weight: bold;
  color: #2c3e50;
}

.schedule-amount {
  font-weight: bold;
  font-family: monospace;
  color: #2c3e50;
}

.schedule-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.schedule-status.paid {
  background: #d5f4e6;
  color: #27ae60;
}

.schedule-status.overdue {
  background: #fadbd8;
  color: #e74c3c;
}

.schedule-status.pending {
  background: #fef9e7;
  color: #f39c12;
}

/* نموذج الجدولة */
.schedule-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.supplier-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.supplier-info h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.supplier-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.schedule-options h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.option-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.upcoming-payments {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.upcoming-payments h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.payments-calendar {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .accounts-payable-advanced {
    padding: 10px;
  }
  
  .payables-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .payables-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .controls-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-box {
    max-width: none;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-modes {
    flex-direction: column;
  }
  
  .payables-table {
    font-size: 12px;
  }
  
  .payables-table th,
  .payables-table td {
    padding: 8px 4px;
  }
  
  .card-body {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    padding: 15px;
    gap: 20px;
  }

  .amount-grid {
    grid-template-columns: 1fr;
  }
  
  .receivable-summary,
  .payable-summary {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .amount-input-container {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .full-payment-btn {
    width: 100%;
    text-align: center;
  }
  
  .add-modal,
  .payment-modal,
  .print-modal,
  .export-modal,
  .filter-modal,
  .details-modal,
  .schedule-modal {
    margin: 10px;
    max-width: calc(100vw - 20px);
    min-width: auto;
  }
  
  .details-header {
    grid-template-columns: 1fr;
  }
  
  .option-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .payables-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .header-controls {
    grid-template-columns: 1fr;
  }
  
  .payables-table th,
  .payables-table td {
    padding: 6px 2px;
    font-size: 10px;
  }
  
  .actions {
    flex-direction: column;
    gap: 2px;
  }
  
  .action-btn {
    padding: 4px 6px;
    font-size: 10px;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    padding: 15px;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 10px;
  }
}