import React, { useState, useEffect, useRef } from 'react';
import suppliersService from '../../services/SuppliersService';
import './SupplierSelector.css';

const SupplierSelector = ({ 
  value, 
  onChange, 
  placeholder = "اكتب اسم المورد...",
  disabled = false,
  onSelect = null,
  showActiveOnly = true
}) => {
  const [searchTerm, setSearchTerm] = useState(value || '');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [allSuppliers, setAllSuppliers] = useState([]);
  
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // تحميل البيانات عند تحميل المكون
  useEffect(() => {
    const loadData = () => {
      const suppliers = showActiveOnly ? suppliersService.getActiveSuppliers() : suppliersService.getAllSuppliers();
      const formattedSuppliers = suppliers.map(supplier => ({
        id: supplier.id,
        name: supplier.name,
        type: 'supplier',
        contact: supplier.contact,
        phone: supplier.phone,
        email: supplier.email,
        supplierType: supplier.type,
        services: supplier.services || [],
        details: `مورد ${getSupplierTypeLabel(supplier.type)} - ${supplier.contact}`,
        icon: getSupplierIcon(supplier.type),
        color: getSupplierColor(supplier.type),
        remainingAmount: supplier.remainingAmount || 0,
        rating: supplier.rating || 0,
        status: supplier.status
      }));
      
      setAllSuppliers(formattedSuppliers);
    };

    loadData();

    // إضافة مستمع للتحديثات
    const handleSuppliersUpdate = () => loadData();
    suppliersService.addListener(handleSuppliersUpdate);

    return () => {
      suppliersService.removeListener(handleSuppliersUpdate);
    };
  }, [showActiveOnly]);

  // تحديث قيمة البحث عند تغيير القيمة الخارجية
  useEffect(() => {
    setSearchTerm(value || '');
  }, [value]);

  // الحصول على تسمية نوع المورد
  const getSupplierTypeLabel = (type) => {
    const types = {
      'airline': 'طيران',
      'hotel': 'فنادق',
      'transport': 'نقل',
      'visa': 'تأشيرات',
      'insurance': 'تأمين',
      'restaurant': 'مطاعم',
      'tour': 'رحلات',
      'other': 'أخرى'
    };
    return types[type] || 'غير محدد';
  };

  // الحصول على أيقونة نوع المورد
  const getSupplierIcon = (type) => {
    const icons = {
      'airline': '✈️',
      'hotel': '🏨',
      'transport': '🚌',
      'visa': '📋',
      'insurance': '🛡️',
      'restaurant': '🍽️',
      'tour': '🗺️',
      'other': '🏢'
    };
    return icons[type] || '🏢';
  };

  // الحصول على لون نوع المورد
  const getSupplierColor = (type) => {
    const colors = {
      'airline': '#3498db',
      'hotel': '#e74c3c',
      'transport': '#f39c12',
      'visa': '#9b59b6',
      'insurance': '#27ae60',
      'restaurant': '#e67e22',
      'tour': '#16a085',
      'other': '#95a5a6'
    };
    return colors[type] || '#95a5a6';
  };

  // البحث في الموردين
  const searchSuppliers = (term) => {
    if (!term.trim()) {
      return allSuppliers.slice(0, 10); // عرض أول 10 موردين
    }

    const searchLower = term.toLowerCase();
    return allSuppliers.filter(supplier => 
      supplier.name.toLowerCase().includes(searchLower) ||
      supplier.contact.toLowerCase().includes(searchLower) ||
      supplier.phone.includes(term) ||
      supplier.email.toLowerCase().includes(searchLower) ||
      supplier.services.some(service => service.toLowerCase().includes(searchLower)) ||
      getSupplierTypeLabel(supplier.supplierType).toLowerCase().includes(searchLower)
    ).slice(0, 10);
  };

  // معالجة تغيير النص
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    
    const filteredSuggestions = searchSuppliers(newValue);
    setSuggestions(filteredSuggestions);
    setShowSuggestions(true);
    setSelectedIndex(-1);

    // إشعار المكون الأب بالتغيير
    if (onChange) {
      onChange(e);
    }
  };

  // معالجة التركيز على الحقل
  const handleFocus = () => {
    const filteredSuggestions = searchSuppliers(searchTerm);
    setSuggestions(filteredSuggestions);
    setShowSuggestions(true);
  };

  // معالجة فقدان التركيز
  const handleBlur = () => {
    // تأخير إخفاء الاقتراحات للسماح بالنقر عليها
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 200);
  };

  // معالجة اختيار مورد
  const handleSelectSupplier = (supplier) => {
    setSearchTerm(supplier.name);
    setShowSuggestions(false);
    setSelectedIndex(-1);

    // إشعار المكون الأب بالاختيار
    if (onChange) {
      const event = {
        target: { value: supplier.name }
      };
      onChange(event);
    }

    if (onSelect) {
      onSelect(supplier);
    }
  };

  // معالجة الضغط على المفاتيح
  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSelectSupplier(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
      default:
        break;
    }
  };

  // تمرير التركيز للخيار المحدد
  useEffect(() => {
    if (selectedIndex >= 0 && suggestionsRef.current) {
      const selectedElement = suggestionsRef.current.children[selectedIndex];
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  return (
    <div className="supplier-selector">
      <div className="selector-input-container">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="selector-input"
          autoComplete="off"
        />
        
        {searchTerm && (
          <button
            type="button"
            className="clear-button"
            onClick={() => {
              setSearchTerm('');
              setSuggestions([]);
              setShowSuggestions(false);
              if (onChange) {
                onChange({ target: { value: '' } });
              }
            }}
          >
            ✕
          </button>
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <div className="suggestions-container" ref={suggestionsRef}>
          {suggestions.map((supplier, index) => (
            <div
              key={supplier.id}
              className={`suggestion-item ${index === selectedIndex ? 'selected' : ''}`}
              onClick={() => handleSelectSupplier(supplier)}
            >
              <div className="suggestion-icon" style={{ color: supplier.color }}>
                {supplier.icon}
              </div>
              <div className="suggestion-content">
                <div className="suggestion-name">{supplier.name}</div>
                <div className="suggestion-details">{supplier.details}</div>
                <div className="suggestion-contact">
                  {supplier.phone && <span>📞 {supplier.phone}</span>}
                  {supplier.email && <span>📧 {supplier.email}</span>}
                </div>
                {supplier.services.length > 0 && (
                  <div className="suggestion-services">
                    🔧 {supplier.services.slice(0, 2).join(', ')}
                    {supplier.services.length > 2 && '...'}
                  </div>
                )}
                {supplier.remainingAmount > 0 && (
                  <div className="suggestion-debt">
                    💰 ذمة دائنة: {supplier.remainingAmount.toLocaleString()} ريال
                  </div>
                )}
                {supplier.rating > 0 && (
                  <div className="suggestion-rating">
                    ⭐ التقييم: {supplier.rating}/5
                  </div>
                )}
              </div>
              <div className="suggestion-type">
                مورد
              </div>
            </div>
          ))}
        </div>
      )}

      {showSuggestions && suggestions.length === 0 && searchTerm.trim() && (
        <div className="suggestions-container">
          <div className="no-suggestions">
            <div className="no-suggestions-icon">🔍</div>
            <div className="no-suggestions-text">لا توجد موردين مطابقين</div>
            <div className="no-suggestions-hint">
              جرب البحث باسم مختلف أو نوع الخدمة
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupplierSelector;
