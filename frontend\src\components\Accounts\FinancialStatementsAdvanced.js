import React, { useState, useEffect, useMemo, useRef } from 'react';
import './FinancialStatements.css';

const FinancialStatementsAdvanced = ({ accounts, transactions, currentUser, financialPeriod }) => {
  const [selectedPeriod, setSelectedPeriod] = useState({
    startDate: financialPeriod?.startDate || new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: financialPeriod?.endDate || new Date().toISOString().split('T')[0]
  });
  const [activeStatement, setActiveStatement] = useState('income');
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showComparisonModal, setShowComparisonModal] = useState(false);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [viewMode, setViewMode] = useState('detailed');
  const [comparisonPeriod, setComparisonPeriod] = useState({
    startDate: new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0],
    endDate: new Date(new Date().getFullYear() - 1, 11, 31).toISOString().split('T')[0]
  });
  const [printOptions, setPrintOptions] = useState({
    includeComparison: false,
    includePercentages: true,
    includeNotes: true,
    showZeroBalances: false,
    pageSize: 'A4',
    orientation: 'portrait',
    includeHeader: true,
    includeFooter: true
  });
  const [exportFormat, setExportFormat] = useState('csv');

  const printRef = useRef();

  // حساب أرصدة الحسابات للفترة المحددة
  const calculateBalances = (startDate, endDate) => {
    const balances = {};
    
    // تهيئة الأرصدة
    accounts.forEach(account => {
      balances[account.id] = {
        accountId: account.id,
        accountName: account.name,
        category: account.category,
        balance: account.balance || 0
      };
    });

    // حساب الحركات خلال الفترة
    transactions.forEach(transaction => {
      const transactionDate = new Date(transaction.date);
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (transactionDate >= start && transactionDate <= end) {
        transaction.entries?.forEach(entry => {
          if (balances[entry.accountId]) {
            const debitAmount = parseFloat(entry.debit) || 0;
            const creditAmount = parseFloat(entry.credit) || 0;
            
            const account = accounts.find(acc => acc.id === entry.accountId);
            if (account) {
              if (account.category === 'revenue') {
                // الإيرادات: دائن موجب
                balances[entry.accountId].balance += creditAmount - debitAmount;
              } else if (account.category === 'expenses') {
                // المصروفات: مدين موجب
                balances[entry.accountId].balance += debitAmount - creditAmount;
              } else if (account.category === 'assets') {
                // الأصول: مدين موجب
                balances[entry.accountId].balance += debitAmount - creditAmount;
              } else if (account.category === 'liabilities' || account.category === 'equity') {
                // الخصوم وحقوق الملكية: دائن موجب
                balances[entry.accountId].balance += creditAmount - debitAmount;
              }
            }
          }
        });
      }
    });

    return balances;
  };

  // حساب أرصدة الفترة الحالية
  const currentBalances = useMemo(() => {
    return calculateBalances(selectedPeriod.startDate, selectedPeriod.endDate);
  }, [accounts, transactions, selectedPeriod]);

  // حساب أرصدة فترة المقارنة
  const comparisonBalances = useMemo(() => {
    return calculateBalances(comparisonPeriod.startDate, comparisonPeriod.endDate);
  }, [accounts, transactions, comparisonPeriod]);

  // قائمة الدخل
  const incomeStatement = useMemo(() => {
    const revenues = accounts
      .filter(acc => acc.category === 'revenue' && acc.type === 'detail')
      .map(acc => ({
        ...acc,
        currentBalance: currentBalances[acc.id]?.balance || 0,
        comparisonBalance: comparisonBalances[acc.id]?.balance || 0
      }))
      .filter(acc => printOptions.showZeroBalances || Math.abs(acc.currentBalance) > 0.01);

    const expenses = accounts
      .filter(acc => acc.category === 'expenses' && acc.type === 'detail')
      .map(acc => ({
        ...acc,
        currentBalance: currentBalances[acc.id]?.balance || 0,
        comparisonBalance: comparisonBalances[acc.id]?.balance || 0
      }))
      .filter(acc => printOptions.showZeroBalances || Math.abs(acc.currentBalance) > 0.01);

    const totalRevenues = revenues.reduce((sum, acc) => sum + acc.currentBalance, 0);
    const totalExpenses = expenses.reduce((sum, acc) => sum + acc.currentBalance, 0);
    const netIncome = totalRevenues - totalExpenses;

    const totalRevenuesComparison = revenues.reduce((sum, acc) => sum + acc.comparisonBalance, 0);
    const totalExpensesComparison = expenses.reduce((sum, acc) => sum + acc.comparisonBalance, 0);
    const netIncomeComparison = totalRevenuesComparison - totalExpensesComparison;

    return {
      revenues,
      expenses,
      totalRevenues,
      totalExpenses,
      netIncome,
      totalRevenuesComparison,
      totalExpensesComparison,
      netIncomeComparison
    };
  }, [accounts, currentBalances, comparisonBalances, printOptions.showZeroBalances]);

  // قائمة المركز المالي
  const balanceSheet = useMemo(() => {
    const assets = accounts
      .filter(acc => acc.category === 'assets' && acc.type === 'detail')
      .map(acc => ({
        ...acc,
        currentBalance: currentBalances[acc.id]?.balance || 0,
        comparisonBalance: comparisonBalances[acc.id]?.balance || 0
      }))
      .filter(acc => printOptions.showZeroBalances || Math.abs(acc.currentBalance) > 0.01);

    const liabilities = accounts
      .filter(acc => acc.category === 'liabilities' && acc.type === 'detail')
      .map(acc => ({
        ...acc,
        currentBalance: currentBalances[acc.id]?.balance || 0,
        comparisonBalance: comparisonBalances[acc.id]?.balance || 0
      }))
      .filter(acc => printOptions.showZeroBalances || Math.abs(acc.currentBalance) > 0.01);

    const equity = accounts
      .filter(acc => acc.category === 'equity' && acc.type === 'detail')
      .map(acc => ({
        ...acc,
        currentBalance: currentBalances[acc.id]?.balance || 0,
        comparisonBalance: comparisonBalances[acc.id]?.balance || 0
      }))
      .filter(acc => printOptions.showZeroBalances || Math.abs(acc.currentBalance) > 0.01);

    const totalAssets = assets.reduce((sum, acc) => sum + acc.currentBalance, 0);
    const totalLiabilities = liabilities.reduce((sum, acc) => sum + acc.currentBalance, 0);
    const totalEquity = equity.reduce((sum, acc) => sum + acc.currentBalance, 0) + incomeStatement.netIncome;

    const totalAssetsComparison = assets.reduce((sum, acc) => sum + acc.comparisonBalance, 0);
    const totalLiabilitiesComparison = liabilities.reduce((sum, acc) => sum + acc.comparisonBalance, 0);
    const totalEquityComparison = equity.reduce((sum, acc) => sum + acc.comparisonBalance, 0) + incomeStatement.netIncomeComparison;

    return {
      assets,
      liabilities,
      equity,
      totalAssets,
      totalLiabilities,
      totalEquity,
      totalAssetsComparison,
      totalLiabilitiesComparison,
      totalEquityComparison,
      isBalanced: Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01
    };
  }, [accounts, currentBalances, comparisonBalances, incomeStatement, printOptions.showZeroBalances]);

  // قائمة التدفقات النقدية
  const cashFlowStatement = useMemo(() => {
    // هذا مثال مبسط - في التطبيق الحقيقي يحتاج تحليل أعمق
    const cashAccounts = accounts.filter(acc => 
      acc.name.includes('نقد') || acc.name.includes('بنك') || acc.name.includes('صندوق')
    );

    const operatingCashFlow = incomeStatement.netIncome; // مبسط
    const investingCashFlow = 0; // يحتاج حساب من حركات الأصول الثابتة
    const financingCashFlow = 0; // يحتاج حساب من حركات القروض وحقوق الملكية

    const netCashFlow = operatingCashFlow + investingCashFlow + financingCashFlow;
    const beginningCash = cashAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0) - netCashFlow;
    const endingCash = beginningCash + netCashFlow;

    return {
      operatingCashFlow,
      investingCashFlow,
      financingCashFlow,
      netCashFlow,
      beginningCash,
      endingCash
    };
  }, [accounts, incomeStatement.netIncome]);

  // النسب المالية
  const financialRatios = useMemo(() => {
    const currentAssets = balanceSheet.assets
      .filter(acc => acc.name.includes('متداول') || acc.name.includes('نقد') || acc.name.includes('بنك'))
      .reduce((sum, acc) => sum + acc.currentBalance, 0);
    
    const currentLiabilities = balanceSheet.liabilities
      .filter(acc => acc.name.includes('متداول') || acc.name.includes('مستحق'))
      .reduce((sum, acc) => sum + acc.currentBalance, 0);

    const currentRatio = currentLiabilities > 0 ? currentAssets / currentLiabilities : 0;
    const debtToEquityRatio = balanceSheet.totalEquity > 0 ? balanceSheet.totalLiabilities / balanceSheet.totalEquity : 0;
    const returnOnAssets = balanceSheet.totalAssets > 0 ? (incomeStatement.netIncome / balanceSheet.totalAssets) * 100 : 0;
    const returnOnEquity = balanceSheet.totalEquity > 0 ? (incomeStatement.netIncome / balanceSheet.totalEquity) * 100 : 0;
    const profitMargin = incomeStatement.totalRevenues > 0 ? (incomeStatement.netIncome / incomeStatement.totalRevenues) * 100 : 0;

    return {
      currentRatio,
      debtToEquityRatio,
      returnOnAssets,
      returnOnEquity,
      profitMargin
    };
  }, [balanceSheet, incomeStatement]);

  // وظائف الطباعة
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>القوائم المالية</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .statement-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .statement-section { margin: 30px 0; page-break-inside: avoid; }
          .section-title { font-size: 16px; font-weight: bold; background: #f5f5f5; padding: 10px; margin-bottom: 15px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .account-name { text-align: right; }
          .amount { text-align: left; font-weight: bold; }
          .positive { color: #27ae60; }
          .negative { color: #e74c3c; }
          .total-row { background: #f8f9fa; font-weight: bold; border-top: 2px solid #333; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } .page-break { page-break-before: always; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    const statements = [];
    
    if (activeStatement === 'income' || activeStatement === 'all') {
      statements.push(generateIncomeStatementHTML());
    }
    
    if (activeStatement === 'balance' || activeStatement === 'all') {
      statements.push(generateBalanceSheetHTML());
    }
    
    if (activeStatement === 'cashflow' || activeStatement === 'all') {
      statements.push(generateCashFlowHTML());
    }

    return `
      ${printOptions.includeHeader ? `
        <div class="header">
          <div class="company-name">شركة شراء السياحية</div>
          <div class="statement-title">القوائم المالية</div>
          <div class="date-range">
            للفترة من ${new Date(selectedPeriod.startDate).toLocaleDateString('ar-SA')} 
            إلى ${new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
          </div>
        </div>
      ` : ''}
      
      ${statements.join('<div class="page-break"></div>')}
      
      ${printOptions.includeFooter ? `
        <div class="footer">
          <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
          <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
        </div>
      ` : ''}
    `;
  };

  const generateIncomeStatementHTML = () => {
    return `
      <div class="statement-section">
        <div class="section-title">قائمة الدخل</div>
        <table>
          <thead>
            <tr>
              <th>البيان</th>
              <th>المبلغ</th>
              ${printOptions.includeComparison ? '<th>فترة المقارنة</th>' : ''}
              ${printOptions.includePercentages ? '<th>النسبة %</th>' : ''}
            </tr>
          </thead>
          <tbody>
            <tr class="total-row">
              <td class="account-name"><strong>الإيرادات</strong></td>
              <td class="amount positive">${incomeStatement.totalRevenues.toLocaleString()}</td>
              ${printOptions.includeComparison ? `<td class="amount">${incomeStatement.totalRevenuesComparison.toLocaleString()}</td>` : ''}
              ${printOptions.includePercentages ? '<td>100%</td>' : ''}
            </tr>
            ${incomeStatement.revenues.map(acc => `
              <tr>
                <td class="account-name">${acc.name}</td>
                <td class="amount positive">${acc.currentBalance.toLocaleString()}</td>
                ${printOptions.includeComparison ? `<td class="amount">${acc.comparisonBalance.toLocaleString()}</td>` : ''}
                ${printOptions.includePercentages ? `<td>${incomeStatement.totalRevenues > 0 ? ((acc.currentBalance / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>` : ''}
              </tr>
            `).join('')}
            
            <tr class="total-row">
              <td class="account-name"><strong>المصروفات</strong></td>
              <td class="amount negative">(${incomeStatement.totalExpenses.toLocaleString()})</td>
              ${printOptions.includeComparison ? `<td class="amount">(${incomeStatement.totalExpensesComparison.toLocaleString()})</td>` : ''}
              ${printOptions.includePercentages ? `<td>${incomeStatement.totalRevenues > 0 ? ((incomeStatement.totalExpenses / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>` : ''}
            </tr>
            ${incomeStatement.expenses.map(acc => `
              <tr>
                <td class="account-name">${acc.name}</td>
                <td class="amount negative">(${acc.currentBalance.toLocaleString()})</td>
                ${printOptions.includeComparison ? `<td class="amount">(${acc.comparisonBalance.toLocaleString()})</td>` : ''}
                ${printOptions.includePercentages ? `<td>${incomeStatement.totalRevenues > 0 ? ((acc.currentBalance / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>` : ''}
              </tr>
            `).join('')}
            
            <tr class="total-row">
              <td class="account-name"><strong>صافي الدخل</strong></td>
              <td class="amount ${incomeStatement.netIncome >= 0 ? 'positive' : 'negative'}">
                ${incomeStatement.netIncome >= 0 ? '' : '('}${Math.abs(incomeStatement.netIncome).toLocaleString()}${incomeStatement.netIncome >= 0 ? '' : ')'}
              </td>
              ${printOptions.includeComparison ? `
                <td class="amount ${incomeStatement.netIncomeComparison >= 0 ? 'positive' : 'negative'}">
                  ${incomeStatement.netIncomeComparison >= 0 ? '' : '('}${Math.abs(incomeStatement.netIncomeComparison).toLocaleString()}${incomeStatement.netIncomeComparison >= 0 ? '' : ')'}
                </td>
              ` : ''}
              ${printOptions.includePercentages ? `<td>${incomeStatement.totalRevenues > 0 ? ((incomeStatement.netIncome / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>` : ''}
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateBalanceSheetHTML = () => {
    return `
      <div class="statement-section">
        <div class="section-title">قائمة المركز المالي</div>
        <table>
          <thead>
            <tr>
              <th>البيان</th>
              <th>المبلغ</th>
              ${printOptions.includeComparison ? '<th>فترة المقارنة</th>' : ''}
            </tr>
          </thead>
          <tbody>
            <tr class="total-row">
              <td class="account-name"><strong>الأصول</strong></td>
              <td class="amount positive">${balanceSheet.totalAssets.toLocaleString()}</td>
              ${printOptions.includeComparison ? `<td class="amount">${balanceSheet.totalAssetsComparison.toLocaleString()}</td>` : ''}
            </tr>
            ${balanceSheet.assets.map(acc => `
              <tr>
                <td class="account-name">${acc.name}</td>
                <td class="amount positive">${acc.currentBalance.toLocaleString()}</td>
                ${printOptions.includeComparison ? `<td class="amount">${acc.comparisonBalance.toLocaleString()}</td>` : ''}
              </tr>
            `).join('')}
            
            <tr class="total-row">
              <td class="account-name"><strong>الخصوم</strong></td>
              <td class="amount negative">${balanceSheet.totalLiabilities.toLocaleString()}</td>
              ${printOptions.includeComparison ? `<td class="amount">${balanceSheet.totalLiabilitiesComparison.toLocaleString()}</td>` : ''}
            </tr>
            ${balanceSheet.liabilities.map(acc => `
              <tr>
                <td class="account-name">${acc.name}</td>
                <td class="amount negative">${acc.currentBalance.toLocaleString()}</td>
                ${printOptions.includeComparison ? `<td class="amount">${acc.comparisonBalance.toLocaleString()}</td>` : ''}
              </tr>
            `).join('')}
            
            <tr class="total-row">
              <td class="account-name"><strong>حقوق الملكية</strong></td>
              <td class="amount positive">${balanceSheet.totalEquity.toLocaleString()}</td>
              ${printOptions.includeComparison ? `<td class="amount">${balanceSheet.totalEquityComparison.toLocaleString()}</td>` : ''}
            </tr>
            ${balanceSheet.equity.map(acc => `
              <tr>
                <td class="account-name">${acc.name}</td>
                <td class="amount positive">${acc.currentBalance.toLocaleString()}</td>
                ${printOptions.includeComparison ? `<td class="amount">${acc.comparisonBalance.toLocaleString()}</td>` : ''}
              </tr>
            `).join('')}
            <tr>
              <td class="account-name">صافي الدخل المحتجز</td>
              <td class="amount ${incomeStatement.netIncome >= 0 ? 'positive' : 'negative'}">${incomeStatement.netIncome.toLocaleString()}</td>
              ${printOptions.includeComparison ? `<td class="amount">${incomeStatement.netIncomeComparison.toLocaleString()}</td>` : ''}
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateCashFlowHTML = () => {
    return `
      <div class="statement-section">
        <div class="section-title">قائمة التدفقات النقدية</div>
        <table>
          <thead>
            <tr>
              <th>البيان</th>
              <th>المبلغ</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="account-name">التدفقات النقدية من الأنشطة التشغيلية</td>
              <td class="amount ${cashFlowStatement.operatingCashFlow >= 0 ? 'positive' : 'negative'}">${cashFlowStatement.operatingCashFlow.toLocaleString()}</td>
            </tr>
            <tr>
              <td class="account-name">التدفقات النقدية من الأنشطة الاستثمارية</td>
              <td class="amount ${cashFlowStatement.investingCashFlow >= 0 ? 'positive' : 'negative'}">${cashFlowStatement.investingCashFlow.toLocaleString()}</td>
            </tr>
            <tr>
              <td class="account-name">التدفقات النقدية من الأنشطة التمويلية</td>
              <td class="amount ${cashFlowStatement.financingCashFlow >= 0 ? 'positive' : 'negative'}">${cashFlowStatement.financingCashFlow.toLocaleString()}</td>
            </tr>
            <tr class="total-row">
              <td class="account-name"><strong>صافي التدفق النقدي</strong></td>
              <td class="amount ${cashFlowStatement.netCashFlow >= 0 ? 'positive' : 'negative'}"><strong>${cashFlowStatement.netCashFlow.toLocaleString()}</strong></td>
            </tr>
            <tr>
              <td class="account-name">النقدية في بداية الفترة</td>
              <td class="amount">${cashFlowStatement.beginningCash.toLocaleString()}</td>
            </tr>
            <tr class="total-row">
              <td class="account-name"><strong>النقدية في نهاية الفترة</strong></td>
              <td class="amount positive"><strong>${cashFlowStatement.endingCash.toLocaleString()}</strong></td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  // وظائف التصدير
  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    let csvContent = '';
    
    if (activeStatement === 'income' || activeStatement === 'all') {
      csvContent += 'قائمة الدخل\n';
      csvContent += 'البيان,المبلغ\n';
      csvContent += `إجمالي الإيرادات,${incomeStatement.totalRevenues}\n`;
      incomeStatement.revenues.forEach(acc => {
        csvContent += `${acc.name},${acc.currentBalance}\n`;
      });
      csvContent += `إجمالي المصروفات,${incomeStatement.totalExpenses}\n`;
      incomeStatement.expenses.forEach(acc => {
        csvContent += `${acc.name},${acc.currentBalance}\n`;
      });
      csvContent += `صافي الدخل,${incomeStatement.netIncome}\n\n`;
    }

    if (activeStatement === 'balance' || activeStatement === 'all') {
      csvContent += 'قائمة المركز المالي\n';
      csvContent += 'البيان,المبلغ\n';
      csvContent += `إجمالي الأصول,${balanceSheet.totalAssets}\n`;
      balanceSheet.assets.forEach(acc => {
        csvContent += `${acc.name},${acc.currentBalance}\n`;
      });
      csvContent += `إجمالي الخصوم,${balanceSheet.totalLiabilities}\n`;
      balanceSheet.liabilities.forEach(acc => {
        csvContent += `${acc.name},${acc.currentBalance}\n`;
      });
      csvContent += `إجمالي حقوق الملكية,${balanceSheet.totalEquity}\n`;
      balanceSheet.equity.forEach(acc => {
        csvContent += `${acc.name},${acc.currentBalance}\n`;
      });
    }

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `القوائم-المالية-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  return (
    <div className="financial-statements-advanced">
      <div className="statements-header">
        <div className="header-content">
          <h2>📊 القوائم المالية المتقدمة</h2>
          <p>عرض شامل للقوائم المالية والتحليل المالي</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-success"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowComparisonModal(true)}
          >
            📈 مقارنة
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => setShowAnalysisModal(true)}
          >
            🔍 تحليل مالي
          </button>
        </div>
      </div>

      {/* الإحصائيات السريعة */}
      <div className="statements-stats">
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{incomeStatement.totalRevenues.toLocaleString()}</div>
            <div className="stat-label">إجمالي الإيرادات</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💸</div>
          <div className="stat-info">
            <div className="stat-value">{incomeStatement.totalExpenses.toLocaleString()}</div>
            <div className="stat-label">إجمالي المصروفات</div>
          </div>
        </div>
        
        <div className={`stat-card ${incomeStatement.netIncome >= 0 ? 'profit' : 'loss'}`}>
          <div className="stat-icon">{incomeStatement.netIncome >= 0 ? '📈' : '📉'}</div>
          <div className="stat-info">
            <div className="stat-value">{Math.abs(incomeStatement.netIncome).toLocaleString()}</div>
            <div className="stat-label">{incomeStatement.netIncome >= 0 ? 'صافي الربح' : 'صافي الخسارة'}</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">🏢</div>
          <div className="stat-info">
            <div className="stat-value">{balanceSheet.totalAssets.toLocaleString()}</div>
            <div className="stat-label">إجمالي الأصول</div>
          </div>
        </div>
        
        <div className={`stat-card ${balanceSheet.isBalanced ? 'balanced' : 'error'}`}>
          <div className="stat-icon">{balanceSheet.isBalanced ? '✅' : '⚠️'}</div>
          <div className="stat-info">
            <div className="stat-value">{balanceSheet.isBalanced ? 'متوازن' : 'غير متوازن'}</div>
            <div className="stat-label">حالة الميزانية</div>
          </div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="statements-controls">
        <div className="controls-row">
          <div className="date-range">
            <label>الفترة المالية:</label>
            <input
              type="date"
              value={selectedPeriod.startDate}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={selectedPeriod.endDate}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
          
          <div className="statement-tabs">
            <button
              className={`tab-btn ${activeStatement === 'income' ? 'active' : ''}`}
              onClick={() => setActiveStatement('income')}
            >
              📊 قائمة الدخل
            </button>
            <button
              className={`tab-btn ${activeStatement === 'balance' ? 'active' : ''}`}
              onClick={() => setActiveStatement('balance')}
            >
              🏢 المركز المالي
            </button>
            <button
              className={`tab-btn ${activeStatement === 'cashflow' ? 'active' : ''}`}
              onClick={() => setActiveStatement('cashflow')}
            >
              💰 التدفقات النقدية
            </button>
            <button
              className={`tab-btn ${activeStatement === 'all' ? 'active' : ''}`}
              onClick={() => setActiveStatement('all')}
            >
              📋 جميع القوائم
            </button>
          </div>
          
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'detailed' ? 'active' : ''}`}
              onClick={() => setViewMode('detailed')}
            >
              📋 تفصيلي
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'summary' ? 'active' : ''}`}
              onClick={() => setViewMode('summary')}
            >
              📊 ملخص
            </button>
          </div>
        </div>
      </div>

      {/* عرض القوائم المالية */}
      <div className="statements-content">
        {/* قائمة الدخل */}
        {(activeStatement === 'income' || activeStatement === 'all') && (
          <div className="statement-section">
            <div className="section-header">
              <h3>📊 قائمة الدخل</h3>
              <div className="section-period">
                للفترة من {new Date(selectedPeriod.startDate).toLocaleDateString('ar-SA')} 
                إلى {new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
              </div>
            </div>
            
            <div className="table-container">
              <table className="financial-table">
                <thead>
                  <tr>
                    <th>البيان</th>
                    <th>المبلغ</th>
                    {printOptions.includePercentages && <th>النسبة %</th>}
                  </tr>
                </thead>
                <tbody>
                  <tr className="category-header">
                    <td className="account-name"><strong>الإيرادات</strong></td>
                    <td className="amount positive">{incomeStatement.totalRevenues.toLocaleString()}</td>
                    {printOptions.includePercentages && <td>100%</td>}
                  </tr>
                  {viewMode === 'detailed' && incomeStatement.revenues.map(acc => (
                    <tr key={acc.id}>
                      <td className="account-name sub-account">{acc.name}</td>
                      <td className="amount positive">{acc.currentBalance.toLocaleString()}</td>
                      {printOptions.includePercentages && (
                        <td>{incomeStatement.totalRevenues > 0 ? ((acc.currentBalance / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>
                      )}
                    </tr>
                  ))}
                  
                  <tr className="category-header">
                    <td className="account-name"><strong>المصروفات</strong></td>
                    <td className="amount negative">({incomeStatement.totalExpenses.toLocaleString()})</td>
                    {printOptions.includePercentages && (
                      <td>{incomeStatement.totalRevenues > 0 ? ((incomeStatement.totalExpenses / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>
                    )}
                  </tr>
                  {viewMode === 'detailed' && incomeStatement.expenses.map(acc => (
                    <tr key={acc.id}>
                      <td className="account-name sub-account">{acc.name}</td>
                      <td className="amount negative">({acc.currentBalance.toLocaleString()})</td>
                      {printOptions.includePercentages && (
                        <td>{incomeStatement.totalRevenues > 0 ? ((acc.currentBalance / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</td>
                      )}
                    </tr>
                  ))}
                  
                  <tr className="total-row">
                    <td className="account-name"><strong>صافي الدخل</strong></td>
                    <td className={`amount ${incomeStatement.netIncome >= 0 ? 'positive' : 'negative'}`}>
                      <strong>
                        {incomeStatement.netIncome >= 0 ? '' : '('}
                        {Math.abs(incomeStatement.netIncome).toLocaleString()}
                        {incomeStatement.netIncome >= 0 ? '' : ')'}
                      </strong>
                    </td>
                    {printOptions.includePercentages && (
                      <td><strong>{incomeStatement.totalRevenues > 0 ? ((incomeStatement.netIncome / incomeStatement.totalRevenues) * 100).toFixed(1) : 0}%</strong></td>
                    )}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* قائمة المركز المالي */}
        {(activeStatement === 'balance' || activeStatement === 'all') && (
          <div className="statement-section">
            <div className="section-header">
              <h3>🏢 قائمة المركز المالي</h3>
              <div className="section-period">
                كما في {new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
              </div>
            </div>
            
            <div className="table-container">
              <table className="financial-table">
                <thead>
                  <tr>
                    <th>البيان</th>
                    <th>المبلغ</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="category-header">
                    <td className="account-name"><strong>الأصول</strong></td>
                    <td className="amount positive">{balanceSheet.totalAssets.toLocaleString()}</td>
                  </tr>
                  {viewMode === 'detailed' && balanceSheet.assets.map(acc => (
                    <tr key={acc.id}>
                      <td className="account-name sub-account">{acc.name}</td>
                      <td className="amount positive">{acc.currentBalance.toLocaleString()}</td>
                    </tr>
                  ))}
                  
                  <tr className="category-header">
                    <td className="account-name"><strong>الخصوم</strong></td>
                    <td className="amount negative">{balanceSheet.totalLiabilities.toLocaleString()}</td>
                  </tr>
                  {viewMode === 'detailed' && balanceSheet.liabilities.map(acc => (
                    <tr key={acc.id}>
                      <td className="account-name sub-account">{acc.name}</td>
                      <td className="amount negative">{acc.currentBalance.toLocaleString()}</td>
                    </tr>
                  ))}
                  
                  <tr className="category-header">
                    <td className="account-name"><strong>حقوق الملكية</strong></td>
                    <td className="amount positive">{balanceSheet.totalEquity.toLocaleString()}</td>
                  </tr>
                  {viewMode === 'detailed' && balanceSheet.equity.map(acc => (
                    <tr key={acc.id}>
                      <td className="account-name sub-account">{acc.name}</td>
                      <td className="amount positive">{acc.currentBalance.toLocaleString()}</td>
                    </tr>
                  ))}
                  {viewMode === 'detailed' && (
                    <tr>
                      <td className="account-name sub-account">صافي الدخل المحتجز</td>
                      <td className={`amount ${incomeStatement.netIncome >= 0 ? 'positive' : 'negative'}`}>
                        {incomeStatement.netIncome.toLocaleString()}
                      </td>
                    </tr>
                  )}
                  
                  <tr className="total-row">
                    <td className="account-name"><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                    <td className="amount positive">
                      <strong>{(balanceSheet.totalLiabilities + balanceSheet.totalEquity).toLocaleString()}</strong>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              {!balanceSheet.isBalanced && (
                <div className="balance-warning">
                  ⚠️ تحذير: الميزانية غير متوازنة! الفرق: {Math.abs(balanceSheet.totalAssets - (balanceSheet.totalLiabilities + balanceSheet.totalEquity)).toLocaleString()}
                </div>
              )}
            </div>
          </div>
        )}

        {/* قائمة التدفقات النقدية */}
        {(activeStatement === 'cashflow' || activeStatement === 'all') && (
          <div className="statement-section">
            <div className="section-header">
              <h3>💰 قائمة التدفقات النقدية</h3>
              <div className="section-period">
                للفترة من {new Date(selectedPeriod.startDate).toLocaleDateString('ar-SA')} 
                إلى {new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
              </div>
            </div>
            
            <div className="table-container">
              <table className="financial-table">
                <thead>
                  <tr>
                    <th>البيان</th>
                    <th>المبلغ</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="category-header">
                    <td className="account-name"><strong>التدفقات النقدية من الأنشطة التشغيلية</strong></td>
                    <td className={`amount ${cashFlowStatement.operatingCashFlow >= 0 ? 'positive' : 'negative'}`}>
                      {cashFlowStatement.operatingCashFlow.toLocaleString()}
                    </td>
                  </tr>
                  
                  <tr className="category-header">
                    <td className="account-name"><strong>التدفقات النقدية من الأنشطة الاستثمارية</strong></td>
                    <td className={`amount ${cashFlowStatement.investingCashFlow >= 0 ? 'positive' : 'negative'}`}>
                      {cashFlowStatement.investingCashFlow.toLocaleString()}
                    </td>
                  </tr>
                  
                  <tr className="category-header">
                    <td className="account-name"><strong>التدفقات النقدية من الأنشطة التمويلية</strong></td>
                    <td className={`amount ${cashFlowStatement.financingCashFlow >= 0 ? 'positive' : 'negative'}`}>
                      {cashFlowStatement.financingCashFlow.toLocaleString()}
                    </td>
                  </tr>
                  
                  <tr className="total-row">
                    <td className="account-name"><strong>صافي التدفق النقدي</strong></td>
                    <td className={`amount ${cashFlowStatement.netCashFlow >= 0 ? 'positive' : 'negative'}`}>
                      <strong>{cashFlowStatement.netCashFlow.toLocaleString()}</strong>
                    </td>
                  </tr>
                  
                  <tr>
                    <td className="account-name">النقدية في بداية الفترة</td>
                    <td className="amount">{cashFlowStatement.beginningCash.toLocaleString()}</td>
                  </tr>
                  
                  <tr className="total-row">
                    <td className="account-name"><strong>النقدية في نهاية الفترة</strong></td>
                    <td className="amount positive">
                      <strong>{cashFlowStatement.endingCash.toLocaleString()}</strong>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* النماذج المنبثقة */}
      
      {/* نموذج خيارات الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeComparison}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeComparison: e.target.checked }))}
                    />
                    تضمين فترة المقارنة
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includePercentages}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includePercentages: e.target.checked }))}
                    />
                    تضمين النسب المئوية
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeNotes}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeNotes: e.target.checked }))}
                    />
                    تضمين الملاحظات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.showZeroBalances}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, showZeroBalances: e.target.checked }))}
                    />
                    إظهار الأرصدة الصفرية
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeHeader}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeHeader: e.target.checked }))}
                    />
                    تضمين رأس الصفحة
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeFooter}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeFooter: e.target.checked }))}
                    />
                    تضمين تذييل الصفحة
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">القائمة المحددة:</span>
                      <span className="value">
                        {activeStatement === 'income' ? 'قائمة الدخل' :
                         activeStatement === 'balance' ? 'قائمة المركز المالي' :
                         activeStatement === 'cashflow' ? 'قائمة التدفقات النقدية' :
                         'جميع القوائم'}
                      </span>
                    </div>
                    <div className="summary-item">
                      <span className="label">الفترة:</span>
                      <span className="value">
                        {new Date(selectedPeriod.startDate).toLocaleDateString('ar-SA')} - 
                        {new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="summary-item">
                      <span className="label">صافي الدخل:</span>
                      <span className={`value ${incomeStatement.netIncome >= 0 ? 'positive' : 'negative'}`}>
                        {incomeStatement.netIncome.toLocaleString()}
                      </span>
                    </div>
                    <div className="summary-item">
                      <span className="label">إجمالي الأصول:</span>
                      <span className="value">{balanceSheet.totalAssets.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج المقارنة */}
      {showComparisonModal && (
        <div className="modal-overlay">
          <div className="comparison-modal">
            <div className="modal-header">
              <h3>📈 مقارنة الفترات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowComparisonModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="comparison-settings">
                <div className="period-selection">
                  <h4>فترة المقارنة:</h4>
                  <div className="date-range">
                    <input
                      type="date"
                      value={comparisonPeriod.startDate}
                      onChange={(e) => setComparisonPeriod(prev => ({ ...prev, startDate: e.target.value }))}
                      className="date-input"
                    />
                    <span>إلى</span>
                    <input
                      type="date"
                      value={comparisonPeriod.endDate}
                      onChange={(e) => setComparisonPeriod(prev => ({ ...prev, endDate: e.target.value }))}
                      className="date-input"
                    />
                  </div>
                </div>

                <div className="comparison-preview">
                  <h4>معاينة المقارنة:</h4>
                  <table className="comparison-table">
                    <thead>
                      <tr>
                        <th>البيان</th>
                        <th>الفترة الحالية</th>
                        <th>فترة المقارنة</th>
                        <th>التغيير</th>
                        <th>النسبة %</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>إجمالي الإيرادات</td>
                        <td className="amount positive">{incomeStatement.totalRevenues.toLocaleString()}</td>
                        <td className="amount">{incomeStatement.totalRevenuesComparison.toLocaleString()}</td>
                        <td className={`amount ${(incomeStatement.totalRevenues - incomeStatement.totalRevenuesComparison) >= 0 ? 'positive' : 'negative'}`}>
                          {(incomeStatement.totalRevenues - incomeStatement.totalRevenuesComparison).toLocaleString()}
                        </td>
                        <td>
                          {incomeStatement.totalRevenuesComparison > 0 ? 
                            (((incomeStatement.totalRevenues - incomeStatement.totalRevenuesComparison) / incomeStatement.totalRevenuesComparison) * 100).toFixed(1) : 
                            0}%
                        </td>
                      </tr>
                      <tr>
                        <td>إجمالي المصروفات</td>
                        <td className="amount negative">{incomeStatement.totalExpenses.toLocaleString()}</td>
                        <td className="amount">{incomeStatement.totalExpensesComparison.toLocaleString()}</td>
                        <td className={`amount ${(incomeStatement.totalExpenses - incomeStatement.totalExpensesComparison) >= 0 ? 'negative' : 'positive'}`}>
                          {(incomeStatement.totalExpenses - incomeStatement.totalExpensesComparison).toLocaleString()}
                        </td>
                        <td>
                          {incomeStatement.totalExpensesComparison > 0 ? 
                            (((incomeStatement.totalExpenses - incomeStatement.totalExpensesComparison) / incomeStatement.totalExpensesComparison) * 100).toFixed(1) : 
                            0}%
                        </td>
                      </tr>
                      <tr>
                        <td><strong>صافي الدخل</strong></td>
                        <td className={`amount ${incomeStatement.netIncome >= 0 ? 'positive' : 'negative'}`}>
                          <strong>{incomeStatement.netIncome.toLocaleString()}</strong>
                        </td>
                        <td className="amount">
                          <strong>{incomeStatement.netIncomeComparison.toLocaleString()}</strong>
                        </td>
                        <td className={`amount ${(incomeStatement.netIncome - incomeStatement.netIncomeComparison) >= 0 ? 'positive' : 'negative'}`}>
                          <strong>{(incomeStatement.netIncome - incomeStatement.netIncomeComparison).toLocaleString()}</strong>
                        </td>
                        <td>
                          <strong>
                            {incomeStatement.netIncomeComparison !== 0 ? 
                              (((incomeStatement.netIncome - incomeStatement.netIncomeComparison) / Math.abs(incomeStatement.netIncomeComparison)) * 100).toFixed(1) : 
                              0}%
                          </strong>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowComparisonModal(false)}
              >
                إغلاق
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => {
                  setPrintOptions(prev => ({ ...prev, includeComparison: true }));
                  setShowComparisonModal(false);
                }}
              >
                ✅ تطبيق المقارنة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التحليل المالي */}
      {showAnalysisModal && (
        <div className="modal-overlay">
          <div className="analysis-modal">
            <div className="modal-header">
              <h3>🔍 التحليل المالي</h3>
              <button 
                className="close-btn"
                onClick={() => setShowAnalysisModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="financial-analysis">
                <div className="ratios-section">
                  <h4>النسب المالية:</h4>
                  <div className="ratios-grid">
                    <div className="ratio-card">
                      <div className="ratio-name">نسبة السيولة الجارية</div>
                      <div className="ratio-value">{financialRatios.currentRatio.toFixed(2)}</div>
                      <div className="ratio-desc">الأصول المتداولة / الخصوم المتداولة</div>
                    </div>
                    
                    <div className="ratio-card">
                      <div className="ratio-name">نسبة الدين إلى حقوق الملكية</div>
                      <div className="ratio-value">{financialRatios.debtToEquityRatio.toFixed(2)}</div>
                      <div className="ratio-desc">إجمالي الخصوم / حقوق الملكية</div>
                    </div>
                    
                    <div className="ratio-card">
                      <div className="ratio-name">العائد على الأصول</div>
                      <div className="ratio-value">{financialRatios.returnOnAssets.toFixed(1)}%</div>
                      <div className="ratio-desc">صافي الدخل / إجمالي الأصول</div>
                    </div>
                    
                    <div className="ratio-card">
                      <div className="ratio-name">العائد على حقوق الملكية</div>
                      <div className="ratio-value">{financialRatios.returnOnEquity.toFixed(1)}%</div>
                      <div className="ratio-desc">صافي الدخل / حقوق الملكية</div>
                    </div>
                    
                    <div className="ratio-card">
                      <div className="ratio-name">هامش الربح</div>
                      <div className="ratio-value">{financialRatios.profitMargin.toFixed(1)}%</div>
                      <div className="ratio-desc">صافي الدخل / إجمالي الإيرادات</div>
                    </div>
                  </div>
                </div>

                <div className="analysis-insights">
                  <h4>التحليل والتوصيات:</h4>
                  <div className="insights-list">
                    {financialRatios.currentRatio < 1 && (
                      <div className="insight warning">
                        ⚠️ نسبة السيولة منخفضة - قد تواجه الشركة صعوبة في سداد التزاماتها قصيرة الأجل
                      </div>
                    )}
                    {financialRatios.currentRatio > 2 && (
                      <div className="insight info">
                        💡 نسبة السيولة عالية - قد تحتاج الشركة لاستثمار النقدية الزائدة
                      </div>
                    )}
                    {financialRatios.debtToEquityRatio > 2 && (
                      <div className="insight warning">
                        ⚠️ نسبة الدين عالية - الشركة معتمدة بشكل كبير على التمويل الخارجي
                      </div>
                    )}
                    {financialRatios.profitMargin < 0 && (
                      <div className="insight error">
                        🔴 هامش ربح سالب - الشركة تحقق خسائر
                      </div>
                    )}
                    {financialRatios.profitMargin > 10 && (
                      <div className="insight success">
                        ✅ هامش ربح جيد - الشركة تحقق ربحية صحية
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowAnalysisModal(false)}
              >
                إغلاق
              </button>
              <button 
                className="btn btn-info"
                onClick={() => {
                  // يمكن إضافة وظيفة لحفظ التحليل أو طباعته
                  alert('سيتم إضافة وظيفة حفظ التحليل قريباً');
                }}
              >
                💾 حفظ التحليل
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FinancialStatementsAdvanced;