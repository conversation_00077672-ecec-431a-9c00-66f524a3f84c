# ===================================================================
# 🛠️ سكربت التثبيت الشامل لنظام شراء للسفر والسياحة
# Complete Installation Script for Sharau Travel System
# ===================================================================

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "⚠️ هذا السكربت يحتاج صلاحيات المدير" -ForegroundColor Yellow
    Write-Host "🔄 إعادة تشغيل بصلاحيات المدير..." -ForegroundColor Cyan
    
    Start-Process PowerShell -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
    exit
}

# إعداد الألوان
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

Write-Host "
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║    🛠️  التثبيت الشامل للنظام  🛠️                               ║
║         Complete System Installation                            ║
║                                                                  ║
║    ✈️ نظام شراء للسفر والسياحة المتطور ✈️                     ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
" -ForegroundColor Cyan

# متغيرات النظام
$PROJECT_PATH = "c:\Users\<USER>\Desktop\sharaubtravelsoft"
$LOG_FILE = "$PROJECT_PATH\installation.log"

# دالة كتابة السجلات
function Write-Log {
    param($Message, $Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if (Test-Path $PROJECT_PATH) {
        Add-Content -Path $LOG_FILE -Value $logEntry
    }
    
    switch ($Level) {
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "⚠️ $Message" -ForegroundColor Yellow }
        default { Write-Host "ℹ️ $Message" -ForegroundColor White }
    }
}

# دالة فحص وتثبيت Node.js
function Install-NodeJS {
    Write-Log "فحص Node.js..." "INFO"
    
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Log "Node.js مثبت مسبقاً: $nodeVersion" "SUCCESS"
            return $true
        }
    } catch {
        # Node.js غير مثبت
    }
    
    Write-Log "Node.js غير مثبت. بدء التثبيت..." "WARNING"
    
    # تحميل Node.js
    $nodeUrl = "https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi"
    $nodeInstaller = "$env:TEMP\nodejs-installer.msi"
    
    try {
        Write-Log "تحميل Node.js..." "INFO"
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller -UseBasicParsing
        
        Write-Log "تثبيت Node.js..." "INFO"
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$nodeInstaller`" /quiet /norestart" -Wait
        
        # إضافة Node.js إلى PATH
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        
        # فحص التثبيت
        Start-Sleep -Seconds 5
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Log "تم تثبيت Node.js بنجاح: $nodeVersion" "SUCCESS"
            Remove-Item $nodeInstaller -Force -ErrorAction SilentlyContinue
            return $true
        } else {
            Write-Log "فشل في تثبيت Node.js" "ERROR"
            return $false
        }
    } catch {
        Write-Log "خطأ في تثبيت Node.js: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة تثبيت Git (اختياري)
function Install-Git {
    Write-Log "فحص Git..." "INFO"
    
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Log "Git مثبت مسبقاً: $gitVersion" "SUCCESS"
            return $true
        }
    } catch {
        # Git غير مثبت
    }
    
    Write-Log "Git غير مثبت. هل تريد تثبيته؟ (اختياري)" "WARNING"
    $installGit = Read-Host "اكتب 'y' للتثبيت أو أي شيء آخر للتخطي"
    
    if ($installGit -eq 'y' -or $installGit -eq 'Y') {
        $gitUrl = "https://github.com/git-for-windows/git/releases/download/v2.41.0.windows.3/Git-********-64-bit.exe"
        $gitInstaller = "$env:TEMP\git-installer.exe"
        
        try {
            Write-Log "تحميل Git..." "INFO"
            Invoke-WebRequest -Uri $gitUrl -OutFile $gitInstaller -UseBasicParsing
            
            Write-Log "تثبيت Git..." "INFO"
            Start-Process -FilePath $gitInstaller -ArgumentList "/SILENT" -Wait
            
            Write-Log "تم تثبيت Git بنجاح" "SUCCESS"
            Remove-Item $gitInstaller -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "خطأ في تثبيت Git: $($_.Exception.Message)" "ERROR"
        }
    }
    
    return $true
}

# دالة إعداد سياسة التنفيذ
function Set-ExecutionPolicy {
    Write-Log "إعداد سياسة تنفيذ PowerShell..." "INFO"
    
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Log "تم إعداد سياسة التنفيذ بنجاح" "SUCCESS"
        return $true
    } catch {
        Write-Log "فشل في إعداد سياسة التنفيذ: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# دالة إنشاء مجلدات النظام
function Create-SystemFolders {
    Write-Log "إنشاء مجلدات النظام..." "INFO"
    
    $folders = @(
        "$PROJECT_PATH\frontend\src\components\Templates",
        "$PROJECT_PATH\frontend\src\pages\Templates", 
        "$PROJECT_PATH\frontend\src\components\Finance",
        "$PROJECT_PATH\backend\routes",
        "$PROJECT_PATH\backend\models",
        "$PROJECT_PATH\backend\middleware",
        "$PROJECT_PATH\logs",
        "$PROJECT_PATH\backups",
        "$PROJECT_PATH\uploads"
    )
    
    foreach ($folder in $folders) {
        try {
            if (-not (Test-Path $folder)) {
                New-Item -ItemType Directory -Path $folder -Force | Out-Null
                Write-Log "تم إنشاء المجلد: $folder" "SUCCESS"
            }
        } catch {
            Write-Log "فشل في إنشاء المجلد: $folder" "ERROR"
        }
    }
}

# دالة إنشاء ملفات package.json
function Create-PackageFiles {
    Write-Log "إنشاء ملفات package.json..." "INFO"
    
    # Frontend package.json
    $frontendPackage = @{
        name = "sharau-frontend"
        version = "1.0.0"
        description = "Sharau Travel System Frontend"
        private = $true
        dependencies = @{
            "@testing-library/jest-dom" = "^5.16.4"
            "@testing-library/react" = "^13.3.0"
            "@testing-library/user-event" = "^13.5.0"
            "react" = "^18.2.0"
            "react-dom" = "^18.2.0"
            "react-router-dom" = "^6.3.0"
            "react-scripts" = "5.0.1"
            "web-vitals" = "^2.1.4"
            "axios" = "^1.4.0"
            "chart.js" = "^3.9.1"
            "react-chartjs-2" = "^4.3.1"
            "date-fns" = "^2.29.3"
            "react-datepicker" = "^4.8.0"
            "react-select" = "^5.4.0"
            "react-toastify" = "^9.1.3"
        }
        scripts = @{
            start = "react-scripts start"
            build = "react-scripts build"
            test = "react-scripts test"
            eject = "react-scripts eject"
        }
        eslintConfig = @{
            extends = @("react-app", "react-app/jest")
        }
        browserslist = @{
            production = @(">0.2%", "not dead", "not op_mini all")
            development = @("last 1 chrome version", "last 1 firefox version", "last 1 safari version")
        }
    } | ConvertTo-Json -Depth 4
    
    try {
        Set-Content -Path "$PROJECT_PATH\frontend\package.json" -Value $frontendPackage -Encoding UTF8
        Write-Log "تم إنشاء frontend/package.json" "SUCCESS"
    } catch {
        Write-Log "فشل في إنشاء frontend/package.json" "ERROR"
    }
    
    # Backend package.json
    $backendPackage = @{
        name = "sharau-backend"
        version = "1.0.0"
        description = "Sharau Travel System Backend"
        main = "server.js"
        scripts = @{
            start = "node server.js"
            dev = "nodemon server.js"
            test = "jest"
        }
        dependencies = @{
            express = "^4.18.2"
            cors = "^2.8.5"
            dotenv = "^16.3.1"
            mongoose = "^7.5.0"
            bcryptjs = "^2.4.3"
            jsonwebtoken = "^9.0.2"
            multer = "^1.4.5"
            nodemailer = "^6.9.4"
            helmet = "^7.0.0"
            "express-rate-limit" = "^6.10.0"
            compression = "^1.7.4"
            morgan = "^1.10.0"
        }
        devDependencies = @{
            nodemon = "^3.0.1"
            jest = "^29.6.2"
            supertest = "^6.3.3"
        }
    } | ConvertTo-Json -Depth 3
    
    try {
        Set-Content -Path "$PROJECT_PATH\backend\package.json" -Value $backendPackage -Encoding UTF8
        Write-Log "تم إنشاء backend/package.json" "SUCCESS"
    } catch {
        Write-Log "فشل في إنشاء backend/package.json" "ERROR"
    }
}

# دالة إنشاء ملف README
function Create-README {
    $readmeContent = @"
# نظام شراء للسفر والسياحة المتطور
# Sharau Travel & Tourism System

## 🌟 نظام متطور وشامل لإدارة السفر والسياحة

### ✨ المميزات الرئيسية:
- 🎨 نظام قوالب مذهل ومتطور
- 💰 إدارة السندات المالية (قبض وصرف)
- ✈️ إدارة حجوزات الطيران والفنادق
- 📊 تقارير تحليلية شاملة
- 🌍 دعم العملات المتعددة
- 📱 واجهة مستخدم عصرية ومتجاوبة

### 🚀 كيفية التشغيل:

#### الطريقة الأولى - استخدام الأيقونة:
1. انقر نقراً مزدوجاً على أيقونة "نظام شراء للسفر والسياحة" على سطح المكتب
2. انتظر تحميل النظام
3. سيفتح المتصفح تلقائياً

#### الطريقة الثانية - التشغيل اليدوي:
```bash
# تشغيل Backend
cd backend
npm start

# تشغيل Frontend (في نافذة أخرى)
cd frontend
npm start
```

### 🌐 الروابط:
- Frontend: http://localhost:3001
- Backend: http://localhost:5000
- القوالب: http://localhost:3001/templates
- السندات: http://localhost:3001/vouchers

### 📋 المتطلبات:
- Node.js (v16 أو أحدث)
- npm (يأتي مع Node.js)
- متصفح حديث (Chrome, Firefox, Edge)

### 🛠️ التثبيت:
```bash
# تشغيل سكربت التثبيت
powershell -ExecutionPolicy Bypass -File install-system.ps1

# إنشاء أيقونة سطح المكتب
powershell -ExecutionPolicy Bypass -File create-desktop-shortcut.ps1
```

### 📁 هيكل المشروع:
```
sharaubtravelsoft/
├── frontend/                 # تطبيق React
│   ├── src/
│   │   ├── components/
│   │   │   ├── Templates/    # نظام القوالب
│   │   │   └── Finance/      # النظام المالي
│   │   └── pages/
├── backend/                  # خادم Express
│   ├── routes/              # مسارات API
│   ├── models/              # نماذج البيانات
│   └── middleware/          # البرمجيات الوسطية
├── logs/                    # ملفات السجلات
├── backups/                 # النسخ الاحتياطية
└── uploads/                 # الملفات المرفوعة
```

### 🎯 الميزات المتقدمة:

#### 🎨 نظام القوالب:
- قوالب سندات فاخرة ومتطورة
- منشئ قوالب تفاعلي
- معاينة مباشرة
- تصدير بصيغ متعددة

#### 💰 النظام المالي:
- سندات قبض وصرف
- دعم العملات المتعددة
- تحويل المبالغ إلى كلمات
- تقارير مالية شاملة

#### 📊 التقارير:
- إحصائيات مفصلة
- رسوم بيانية تفاعلية
- تصدير التقارير
- تحليلات متقدمة

### 🔧 استكشاف الأخطاء:
- تحقق من ملف السجلات: system.log
- تأكد من تثبيت Node.js
- تحقق من توفر المنافذ 3001 و 5000

### 📞 الدعم:
للحصول على المساعدة، راجع:
- دليل المستخدم: دليل_المستخدم.txt
- ملف التكوين: app-config.json
- الدليل الشامل: ULTIMATE_TEMPLATES_SYSTEM.md

---

تم تطوير هذا النظام بعناية فائقة ليوفر أفضل تجربة في إدارة السفر والسياحة.

© 2024 Sharau Development Team
"@
    
    try {
        Set-Content -Path "$PROJECT_PATH\README.md" -Value $readmeContent -Encoding UTF8
        Write-Log "تم إنشاء ملف README.md" "SUCCESS"
    } catch {
        Write-Log "فشل في إنشاء ملف README.md" "ERROR"
    }
}

# دالة تثبيت التبعيات
function Install-Dependencies {
    Write-Log "تثبيت تبعيات النظام..." "INFO"
    
    # تثبيت تبعيات Frontend
    if (Test-Path "$PROJECT_PATH\frontend\package.json") {
        Write-Log "تثبيت تبعيات Frontend..." "INFO"
        Set-Location "$PROJECT_PATH\frontend"
        
        try {
            npm install
            Write-Log "تم تثبيت تبعيات Frontend بنجاح" "SUCCESS"
        } catch {
            Write-Log "فشل في تثبيت تبعيات Frontend" "ERROR"
            return $false
        }
    }
    
    # تثبيت تبعيات Backend
    if (Test-Path "$PROJECT_PATH\backend\package.json") {
        Write-Log "تثبيت تبعيات Backend..." "INFO"
        Set-Location "$PROJECT_PATH\backend"
        
        try {
            npm install
            Write-Log "تم تثبيت تبعيات Backend بنجاح" "SUCCESS"
        } catch {
            Write-Log "فشل في تثبيت تبعيات Backend" "ERROR"
            return $false
        }
    }
    
    return $true
}

# دالة إنشاء أيقونة سطح المكتب
function Create-DesktopShortcut {
    Write-Log "إنشاء أيقونة سطح المكتب..." "INFO"
    
    $shortcutScript = "$PROJECT_PATH\create-desktop-shortcut.ps1"
    if (Test-Path $shortcutScript) {
        try {
            & $shortcutScript
            Write-Log "تم إنشاء أيقونة سطح المكتب بنجاح" "SUCCESS"
        } catch {
            Write-Log "فشل في إنشاء أيقونة سطح المكتب" "ERROR"
        }
    }
}

# ===================================================================
# 🚀 تنفيذ التثبيت الشامل
# ===================================================================

Write-Host "🔄 بدء التثبيت الشامل للنظام..." -ForegroundColor Yellow
Write-Host ""

try {
    # إعداد سياسة التنفيذ
    if (-not (Set-ExecutionPolicy)) {
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # فحص وتثبيت Node.js
    if (-not (Install-NodeJS)) {
        Write-Log "فشل في تثبيت Node.js" "ERROR"
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # تثبيت Git (اختياري)
    Install-Git | Out-Null
    
    # إنشاء مجلدات النظام
    Create-SystemFolders
    
    # إنشاء ملفات package.json
    Create-PackageFiles
    
    # إنشاء ملف README
    Create-README
    
    # تثبيت التبعيات
    if (-not (Install-Dependencies)) {
        Write-Log "فشل في تثبيت التبعيات" "ERROR"
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # إنشاء أيقونة سطح المكتب
    Create-DesktopShortcut
    
    # رسالة النجاح
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
    Write-Host "║                    🎉 تم التثبيت بنجاح! 🎉                     ║" -ForegroundColor Green
    Write-Host "╠══════════════════════════════════════════════════════════════════╣" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "║  ✅ تم تثبيت جميع المتطلبات بنجاح                              ║" -ForegroundColor Green
    Write-Host "║  ✅ تم إنشاء مجلدات النظام                                     ║" -ForegroundColor Green
    Write-Host "║  ✅ تم تثبيت جميع التبعيات                                     ║" -ForegroundColor Green
    Write-Host "║  ✅ تم إنشاء أيقونة سطح المكتب                                ║" -ForegroundColor Green
    Write-Host "║  ✅ النظام جاهز للاستخدام                                      ║" -ForegroundColor Green
    Write-Host "║                                                                  ║" -ForegroundColor Green
    Write-Host "╚══════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 لتشغيل النظام:" -ForegroundColor Cyan
    Write-Host "   • انقر نقراً مزدوجاً على أيقونة 'نظام شراء للسفر والسياحة' على سطح المكتب" -ForegroundColor White
    Write-Host "   • أو شغل الملف: start-sharau-app.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "📖 للمساعدة:" -ForegroundColor Cyan
    Write-Host "   • راجع دليل المستخدم: دليل_المستخدم.txt" -ForegroundColor White
    Write-Host "   • راجع الدليل الشامل: ULTIMATE_TEMPLATES_SYSTEM.md" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Log "خطأ في التثبيت: $($_.Exception.Message)" "ERROR"
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "🎯 التثبيت مكتمل! استمتع بالنظام المذهل!" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"