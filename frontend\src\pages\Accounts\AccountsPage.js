import React, { useState, useEffect } from 'react';
import {
  ChartOfAccounts,
  JournalEntries,
  TrialBalance,
  FinancialStatements,
  AccountsReceivable,
  AccountsPayable,
  BankReconciliation,
  TaxReports
} from '../../components/Accounts/AccountsComponents';
import AdvancedReports from '../../components/Accounts/AdvancedReports';
import UserManagement from '../../components/Accounts/UserManagement';
import AccountingSettings from '../../components/Accounts/AccountingSettings';
import AgentsSuppliers from '../../components/Accounts/AgentsSuppliers';
import './AccountsPage.css';

const AccountsPage = () => {
  const [activeTab, setActiveTab] = useState('chart');
  const [accounts, setAccounts] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [financialPeriod, setFinancialPeriod] = useState({
    startDate: '2024-01-01',
    endDate: '2024-12-31'
  });

  // بيانات تجريبية للحسابات
  const sampleAccounts = [
    // الأصول (Assets)
    { id: '1000', name: 'الأصول', type: 'header', category: 'assets', balance: 0, parent: null },
    { id: '1100', name: 'الأصول المتداولة', type: 'header', category: 'assets', balance: 0, parent: '1000' },
    { id: '1110', name: 'النقدية والبنوك', type: 'detail', category: 'assets', balance: 500000, parent: '1100' },
    { id: '1111', name: 'الصندوق', type: 'detail', category: 'assets', balance: 25000, parent: '1110' },
    { id: '1112', name: 'البنك الأهلي - الحساب الجاري', type: 'detail', category: 'assets', balance: 350000, parent: '1110' },
    { id: '1113', name: 'بنك الراجحي - حساب التوفير', type: 'detail', category: 'assets', balance: 125000, parent: '1110' },
    { id: '1120', name: 'العملاء والذمم المدينة', type: 'detail', category: 'assets', balance: 180000, parent: '1100' },
    { id: '1130', name: 'المخزون', type: 'detail', category: 'assets', balance: 75000, parent: '1100' },
    { id: '1140', name: 'المصروفات المدفوعة مقدماً', type: 'detail', category: 'assets', balance: 15000, parent: '1100' },
    
    { id: '1200', name: 'الأصول الثابتة', type: 'header', category: 'assets', balance: 0, parent: '1000' },
    { id: '1210', name: 'الأثاث والمعدات', type: 'detail', category: 'assets', balance: 120000, parent: '1200' },
    { id: '1220', name: 'أجهزة الكمبيوتر', type: 'detail', category: 'assets', balance: 80000, parent: '1200' },
    { id: '1230', name: 'السيارات', type: 'detail', category: 'assets', balance: 200000, parent: '1200' },
    { id: '1240', name: 'مجمع الإهلاك', type: 'detail', category: 'assets', balance: -50000, parent: '1200' },

    // الخصوم (Liabilities)
    { id: '2000', name: 'الخصوم', type: 'header', category: 'liabilities', balance: 0, parent: null },
    { id: '2100', name: 'الخصوم المتداولة', type: 'header', category: 'liabilities', balance: 0, parent: '2000' },
    { id: '2110', name: 'الموردون والذمم الدائنة', type: 'detail', category: 'liabilities', balance: 95000, parent: '2100' },
    { id: '2120', name: 'المصروفات المستحقة', type: 'detail', category: 'liabilities', balance: 25000, parent: '2100' },
    { id: '2130', name: 'ضريبة القيمة المضافة', type: 'detail', category: 'liabilities', balance: 18000, parent: '2100' },
    { id: '2140', name: 'رواتب الموظفين المستحقة', type: 'detail', category: 'liabilities', balance: 45000, parent: '2100' },
    
    { id: '2200', name: 'الخصوم طويلة الأجل', type: 'header', category: 'liabilities', balance: 0, parent: '2000' },
    { id: '2210', name: 'قروض البنوك', type: 'detail', category: 'liabilities', balance: 300000, parent: '2200' },

    // حقوق الملكية (Equity)
    { id: '3000', name: 'حقوق الملكية', type: 'header', category: 'equity', balance: 0, parent: null },
    { id: '3100', name: 'رأس المال', type: 'detail', category: 'equity', balance: 500000, parent: '3000' },
    { id: '3200', name: 'الأرباح المحتجزة', type: 'detail', category: 'equity', balance: 150000, parent: '3000' },
    { id: '3300', name: 'أرباح السنة الحالية', type: 'detail', category: 'equity', balance: 0, parent: '3000' },

    // الإيرادات (Revenue)
    { id: '4000', name: 'الإيرادات', type: 'header', category: 'revenue', balance: 0, parent: null },
    { id: '4100', name: 'إيرادات الخدمات السياحية', type: 'detail', category: 'revenue', balance: 850000, parent: '4000' },
    { id: '4110', name: 'إيرادات حجز الطيران', type: 'detail', category: 'revenue', balance: 450000, parent: '4100' },
    { id: '4120', name: 'إيرادات حجز الفنادق', type: 'detail', category: 'revenue', balance: 280000, parent: '4100' },
    { id: '4130', name: 'إيرادات العمرة والحج', type: 'detail', category: 'revenue', balance: 120000, parent: '4100' },
    { id: '4200', name: 'إيرادات أخرى', type: 'detail', category: 'revenue', balance: 25000, parent: '4000' },

    // المصروفات (Expenses)
    { id: '5000', name: 'المصروفات', type: 'header', category: 'expenses', balance: 0, parent: null },
    { id: '5100', name: 'مصروفات التشغيل', type: 'header', category: 'expenses', balance: 0, parent: '5000' },
    { id: '5110', name: 'الرواتب والأجور', type: 'detail', category: 'expenses', balance: 180000, parent: '5100' },
    { id: '5120', name: 'الإيجار', type: 'detail', category: 'expenses', balance: 60000, parent: '5100' },
    { id: '5130', name: 'الكهرباء والماء', type: 'detail', category: 'expenses', balance: 18000, parent: '5100' },
    { id: '5140', name: 'الاتصالات والإنترنت', type: 'detail', category: 'expenses', balance: 12000, parent: '5100' },
    { id: '5150', name: 'مصروفات التسويق', type: 'detail', category: 'expenses', balance: 35000, parent: '5100' },
    { id: '5160', name: 'مصروفات السفر', type: 'detail', category: 'expenses', balance: 25000, parent: '5100' },
    { id: '5170', name: 'مصروفات الصيانة', type: 'detail', category: 'expenses', balance: 15000, parent: '5100' },
    
    { id: '5200', name: 'المصروفات الإدارية', type: 'header', category: 'expenses', balance: 0, parent: '5000' },
    { id: '5210', name: 'القرطاسية والمطبوعات', type: 'detail', category: 'expenses', balance: 8000, parent: '5200' },
    { id: '5220', name: 'الرسوم الحكومية', type: 'detail', category: 'expenses', balance: 12000, parent: '5200' },
    { id: '5230', name: 'مصروفات قانونية ومحاسبية', type: 'detail', category: 'expenses', balance: 20000, parent: '5200' },
    
    { id: '5300', name: 'المصروفات المالية', type: 'header', category: 'expenses', balance: 0, parent: '5000' },
    { id: '5310', name: 'فوائد القروض', type: 'detail', category: 'expenses', balance: 18000, parent: '5300' },
    { id: '5320', name: 'رسوم بنكية', type: 'detail', category: 'expenses', balance: 5000, parent: '5300' }
  ];

  // بيانات تجريبية للمعاملات
  const sampleTransactions = [
    {
      id: 'JE-001',
      date: '2024-01-15',
      description: 'إيداع رأس المال الأولي',
      reference: 'INIT-001',
      entries: [
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 500000, credit: 0 },
        { accountId: '3100', accountName: 'رأس المال', debit: 0, credit: 500000 }
      ],
      totalDebit: 500000,
      totalCredit: 500000,
      status: 'posted',
      createdBy: 'أحمد محمد'
    },
    {
      id: 'JE-002',
      date: '2024-01-20',
      description: 'شراء أثاث ومعدات المكتب',
      reference: 'PO-001',
      entries: [
        { accountId: '1210', accountName: 'الأثاث والمعدات', debit: 120000, credit: 0 },
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 0, credit: 120000 }
      ],
      totalDebit: 120000,
      totalCredit: 120000,
      status: 'posted',
      createdBy: 'فاطمة علي'
    },
    {
      id: 'JE-003',
      date: '2024-02-01',
      description: 'إيرادات حجز طيران - شهر يناير',
      reference: 'REV-001',
      entries: [
        { accountId: '1120', accountName: 'العملاء والذمم المدينة', debit: 150000, credit: 0 },
        { accountId: '4110', accountName: 'إيرادات حجز الطيران', debit: 0, credit: 150000 }
      ],
      totalDebit: 150000,
      totalCredit: 150000,
      status: 'posted',
      createdBy: 'محمد سالم'
    },
    {
      id: 'JE-004',
      date: '2024-02-05',
      description: 'دفع رواتب الموظفين - يناير',
      reference: 'PAY-001',
      entries: [
        { accountId: '5110', accountName: 'الرواتب والأجور', debit: 45000, credit: 0 },
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 0, credit: 45000 }
      ],
      totalDebit: 45000,
      totalCredit: 45000,
      status: 'posted',
      createdBy: 'سارة أحمد'
    },
    {
      id: 'JE-005',
      date: '2024-02-10',
      description: 'دفع إيجار المكتب - فبراير',
      reference: 'RENT-002',
      entries: [
        { accountId: '5120', accountName: 'الإيجار', debit: 15000, credit: 0 },
        { accountId: '1112', accountName: 'البنك الأهلي - الحساب الجاري', debit: 0, credit: 15000 }
      ],
      totalDebit: 15000,
      totalCredit: 15000,
      status: 'posted',
      createdBy: 'أحمد محمد'
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setAccounts(sampleAccounts);
      setTransactions(sampleTransactions);
      setIsLoading(false);
    }, 1000);
  }, []);

  const updateFinancialPeriod = (period) => {
    setFinancialPeriod(period);
    // إعادة تحميل البيانات حسب الفترة الجديدة
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  };

  const addAccount = (newAccount) => {
    const account = {
      ...newAccount,
      id: `ACC-${Date.now()}`,
      balance: 0
    };
    setAccounts(prev => [...prev, account]);
  };

  const updateAccount = (accountId, updatedData) => {
    setAccounts(prev => prev.map(acc => 
      acc.id === accountId ? { ...acc, ...updatedData } : acc
    ));
  };

  const deleteAccount = (accountId) => {
    setAccounts(prev => prev.filter(acc => acc.id !== accountId));
  };

  const addTransaction = (newTransaction) => {
    const transaction = {
      ...newTransaction,
      id: `JE-${String(transactions.length + 1).padStart(3, '0')}`,
      status: 'draft',
      createdBy: 'المستخدم الحالي'
    };
    setTransactions(prev => [...prev, transaction]);
  };

  const updateTransaction = (transactionId, updatedData) => {
    setTransactions(prev => prev.map(trans => 
      trans.id === transactionId ? { ...trans, ...updatedData } : trans
    ));
  };

  const deleteTransaction = (transactionId) => {
    setTransactions(prev => prev.filter(trans => trans.id !== transactionId));
  };

  const postTransaction = (transactionId) => {
    const transaction = transactions.find(t => t.id === transactionId);
    if (transaction) {
      // تحديث أرصدة الحسابات
      transaction.entries.forEach(entry => {
        updateAccountBalance(entry.accountId, entry.debit - entry.credit);
      });
      
      // تحديث حالة المعاملة
      updateTransaction(transactionId, { status: 'posted' });
    }
  };

  const updateAccountBalance = (accountId, amount) => {
    setAccounts(prev => prev.map(acc => 
      acc.id === accountId 
        ? { ...acc, balance: acc.balance + amount }
        : acc
    ));
  };

  const renderTabContent = () => {
    const commonProps = {
      accounts,
      transactions,
      financialPeriod,
      isLoading,
      onAddAccount: addAccount,
      onUpdateAccount: updateAccount,
      onDeleteAccount: deleteAccount,
      onAddTransaction: addTransaction,
      onUpdateTransaction: updateTransaction,
      onDeleteTransaction: deleteTransaction,
      onPostTransaction: postTransaction
    };

    switch (activeTab) {
      case 'chart':
        return <ChartOfAccounts {...commonProps} />;
      case 'journal':
        return <JournalEntries {...commonProps} />;
      case 'trial':
        return <TrialBalance {...commonProps} />;
      case 'statements':
        return <FinancialStatements {...commonProps} />;
      case 'receivables':
        return <AccountsReceivable {...commonProps} />;
      case 'payables':
        return <AccountsPayable {...commonProps} />;
      case 'reconciliation':
        return <BankReconciliation {...commonProps} />;
      case 'tax':
        return <TaxReports {...commonProps} />;
      case 'advanced':
        return <AdvancedReports {...commonProps} />;
      case 'users':
        return <UserManagement {...commonProps} />;
      case 'agents-suppliers':
        return <AgentsSuppliers {...commonProps} />;
      case 'settings':
        return <AccountingSettings {...commonProps} />;
      default:
        return <ChartOfAccounts {...commonProps} />;
    }
  };

  return (
    <div className="accounts-page">
      <div className="page-header">
        <div className="header-content">
          <h1>نظام الحسابات</h1>
          <p>إدارة شاملة للحسابات والمعاملات المالية</p>
        </div>
        
        <div className="header-controls">
          <div className="period-selector">
            <label>الفترة المالية:</label>
            <input
              type="date"
              value={financialPeriod.startDate}
              onChange={(e) => updateFinancialPeriod({
                ...financialPeriod,
                startDate: e.target.value
              })}
            />
            <span>إلى</span>
            <input
              type="date"
              value={financialPeriod.endDate}
              onChange={(e) => updateFinancialPeriod({
                ...financialPeriod,
                endDate: e.target.value
              })}
            />
          </div>
        </div>
      </div>

      <div className="accounts-container">
        <div className="accounts-sidebar">
          <div className="accounts-tabs">
            <button 
              className={`tab-btn ${activeTab === 'chart' ? 'active' : ''}`}
              onClick={() => setActiveTab('chart')}
            >
              <span className="tab-icon">📊</span>
              دليل الحسابات
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'journal' ? 'active' : ''}`}
              onClick={() => setActiveTab('journal')}
            >
              <span className="tab-icon">📝</span>
              قيود اليومية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'trial' ? 'active' : ''}`}
              onClick={() => setActiveTab('trial')}
            >
              <span className="tab-icon">⚖️</span>
              ميزان المراجعة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'statements' ? 'active' : ''}`}
              onClick={() => setActiveTab('statements')}
            >
              <span className="tab-icon">📋</span>
              القوائم المالية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'receivables' ? 'active' : ''}`}
              onClick={() => setActiveTab('receivables')}
            >
              <span className="tab-icon">💰</span>
              الذمم المدينة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'payables' ? 'active' : ''}`}
              onClick={() => setActiveTab('payables')}
            >
              <span className="tab-icon">💳</span>
              الذمم الدائنة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'reconciliation' ? 'active' : ''}`}
              onClick={() => setActiveTab('reconciliation')}
            >
              <span className="tab-icon">🏦</span>
              تسوية البنوك
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'tax' ? 'active' : ''}`}
              onClick={() => setActiveTab('tax')}
            >
              <span className="tab-icon">📄</span>
              التقارير الضريبية
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'advanced' ? 'active' : ''}`}
              onClick={() => setActiveTab('advanced')}
            >
              <span className="tab-icon">📈</span>
              التقارير المتقدمة
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              <span className="tab-icon">👥</span>
              إدارة المستخدمين
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'agents-suppliers' ? 'active' : ''}`}
              onClick={() => setActiveTab('agents-suppliers')}
            >
              <span className="tab-icon">🏢</span>
              الوكلاء والموردين
            </button>
            
            <button 
              className={`tab-btn ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              <span className="tab-icon">⚙️</span>
              الإعدادات
            </button>
          </div>
        </div>

        <div className="accounts-content">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default AccountsPage;