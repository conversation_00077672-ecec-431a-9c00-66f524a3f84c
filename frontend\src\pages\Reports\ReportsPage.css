.reports-page {
  padding: 20px;
  font-family: 'Cairo', <PERSON>l, sans-serif;
  direction: rtl;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.header-content h1 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-top: 20px;
  flex-wrap: wrap;
}

.date-range {
  display: flex;
  gap: 10px;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 15px;
  border-radius: 10px;
}

.date-range label {
  font-size: 14px;
  font-weight: 500;
}

.date-range input {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

.supplier-filter select {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
}

.export-buttons {
  display: flex;
  gap: 10px;
}

.export-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.export-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.reports-tabs {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  flex: 1;
  padding: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.tabs-content {
  padding: 30px;
  min-height: 600px;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Overview Tab */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card.large {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.stat-card.large:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 40px;
  opacity: 0.8;
}

.stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  font-weight: 700;
}

.stat-content p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.chart-container {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

/* Bar Chart */
.bar-chart {
  display: flex;
  align-items: end;
  gap: 15px;
  height: 250px;
  padding: 20px 0;
}

.bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.bar {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  transition: all 0.3s ease;
}

.bar:hover {
  opacity: 0.8;
}

.bar-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.bar-value {
  font-size: 11px;
  color: #495057;
  font-weight: 600;
}

/* Line Chart */
.line-chart {
  display: flex;
  align-items: end;
  gap: 15px;
  height: 250px;
  padding: 20px 0;
  position: relative;
}

.line-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  position: relative;
}

.point {
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  position: absolute;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.line-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.line-value {
  font-size: 11px;
  color: #495057;
  font-weight: 600;
}

/* Suppliers Tab */
.suppliers-analysis {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.analysis-section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.analysis-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.type-analysis {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.type-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.type-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.type-card h4 {
  margin: 0 0 15px 0;
  color: #667eea;
  font-size: 18px;
  font-weight: 600;
}

.type-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.type-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.type-stat:last-child {
  border-bottom: none;
}

.type-stat .label {
  color: #6c757d;
  font-size: 14px;
}

.type-stat .value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}

.performers-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.performer-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.performer-item.top {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #b8dacc;
}

.performer-item.low {
  background: linear-gradient(135deg, #f8d7da 0%, #f1c2c7 100%);
  border: 1px solid #e6b3ba;
}

.performer-item:hover {
  transform: translateX(-5px);
}

.rank {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
}

.performer-info {
  flex: 1;
}

.performer-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.performer-info p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.performer-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: left;
}

.performer-stats span {
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

/* Financial Tab */
.financial-overview {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.financial-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.financial-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.financial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.financial-card h3 {
  margin: 0 0 15px 0;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.amount {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.growth {
  font-size: 14px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.growth.positive {
  background: #d4edda;
  color: #155724;
}

.growth.negative {
  background: #f8d7da;
  color: #721c24;
}

.revenue-breakdown {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.revenue-breakdown h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 600;
}

.pie-chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.pie-chart {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.pie-slice {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-radius: 10px;
  background: var(--color);
  color: white;
  min-width: 120px;
  text-align: center;
}

.pie-label {
  font-size: 14px;
  font-weight: 500;
}

.pie-value {
  font-size: 18px;
  font-weight: 700;
}

/* Performance Tab */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
}

.metric-card h3 {
  margin: 0 0 15px 0;
  color: #6c757d;
  font-size: 16px;
  font-weight: 500;
}

.metric-value {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 10px;
  display: block;
}

.metric-value.positive {
  color: #28a745;
}

.metric-value.negative {
  color: #dc3545;
}

.metric-value.neutral {
  color: #667eea;
}

.metric-description {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.4;
}

.performance-trends {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.performance-trends h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 600;
}

.trends-chart {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.trend-label {
  min-width: 100px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.trend-bar {
  flex: 1;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.trend-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  transition: width 0.5s ease;
}

.trend-value {
  min-width: 50px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .type-analysis {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .reports-page {
    padding: 15px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .date-range {
    flex-direction: column;
    gap: 10px;
  }
  
  .export-buttons {
    justify-content: center;
  }
  
  .tabs-header {
    flex-direction: column;
  }
  
  .tab-btn {
    border-bottom: none;
    border-left: 3px solid transparent;
  }
  
  .tab-btn.active {
    border-left-color: #667eea;
  }
  
  .tabs-content {
    padding: 20px;
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
  
  .bar-chart,
  .line-chart {
    height: 200px;
  }
  
  .financial-cards {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .performer-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .performer-stats {
    flex-direction: row;
    justify-content: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 15px;
  }
  
  .header-content h1 {
    font-size: 20px;
  }
  
  .stat-card.large {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .stat-icon {
    font-size: 30px;
  }
  
  .stat-content h3 {
    font-size: 24px;
  }
  
  .chart-container {
    padding: 15px;
  }
  
  .bar-chart,
  .line-chart {
    height: 150px;
    gap: 8px;
  }
  
  .amount {
    font-size: 24px;
  }
  
  .metric-value {
    font-size: 28px;
  }
  
  .trend-item {
    flex-direction: column;
    gap: 10px;
  }
  
  .trend-label {
    min-width: auto;
    text-align: center;
  }
}

/* Advanced Reports Styles */
.reports-page.advanced {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.chart-item {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.gauges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.chart-section {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.detailed-analysis {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.analysis-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.analysis-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.trend-indicators {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.trend-label {
  min-width: 140px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.trend-bar {
  flex: 1;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.trend-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.5s ease;
}

.trend-fill.positive {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.trend-fill.negative {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.trend-value {
  min-width: 60px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.kpi-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e1e8ed;
}

.kpi-value {
  font-size: 32px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.kpi-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
  line-height: 1.3;
}

.kpi-change {
  font-size: 11px;
  font-weight: 500;
  padding: 3px 8px;
  border-radius: 12px;
  display: inline-block;
}

.kpi-change.positive {
  background: #d4edda;
  color: #155724;
}

.kpi-change.negative {
  background: #f8d7da;
  color: #721c24;
}

.data-insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.insight-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.insight-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.insights-list,
.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.insights-list li,
.recommendations-list li {
  padding: 12px 0;
  border-bottom: 1px solid #e1e8ed;
  color: #495057;
  line-height: 1.5;
  position: relative;
  padding-right: 20px;
}

.insights-list li:before {
  content: '💡';
  position: absolute;
  right: 0;
  top: 12px;
}

.recommendations-list li:before {
  content: '🎯';
  position: absolute;
  right: 0;
  top: 12px;
}

.insights-list li:last-child,
.recommendations-list li:last-child {
  border-bottom: none;
}

/* Simple Charts Styles */
.simple-charts {
  margin-top: 30px;
}

.simple-bar-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px 0;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.bar-label {
  min-width: 150px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.bar-container {
  flex: 1;
  height: 25px;
  background: #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  transition: width 0.8s ease;
  min-width: 20px;
}

.bar-value {
  min-width: 120px;
  text-align: left;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

/* Enhanced Loading Animation */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-container p {
  color: #6c757d;
  font-size: 16px;
  margin: 0;
}

/* Error States */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 15px;
  color: #dc3545;
}

.error-icon {
  font-size: 48px;
}

.error-message {
  font-size: 16px;
  text-align: center;
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #5a67d8;
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
  .simple-bar-chart .bar-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .bar-label {
    min-width: auto;
    text-align: center;
  }
  
  .bar-value {
    min-width: auto;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .simple-bar-chart {
    padding: 15px 0;
  }
  
  .bar-container {
    height: 20px;
  }
  
  .bar-label,
  .bar-value {
    font-size: 12px;
  }
}