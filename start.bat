@echo off
echo ========================================
echo   نظام محاسبي متكامل لوكالات السفريات
echo   Travel Agency Accounting System
echo ========================================
echo.

echo [1/5] التحقق من متطلبات النظام...
echo Checking system requirements...

:: التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت. يرجى تثبيت Python 3.9 أو أحدث
    echo Error: Python is not installed. Please install Python 3.9 or later
    pause
    exit /b 1
)

:: التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت. يرجى تثبيت Node.js 16 أو أحدث
    echo Error: Node.js is not installed. Please install Node.js 16 or later
    pause
    exit /b 1
)

:: التحقق من MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: MySQL غير مثبت أو غير متاح في PATH
    echo Warning: MySQL is not installed or not available in PATH
    echo يرجى التأكد من تثبيت MySQL وتشغيله
    echo Please make sure MySQL is installed and running
)

echo [2/5] إعداد قاعدة البيانات...
echo Setting up database...

:: إنشاء ملف .env إذا لم يكن موجوداً
if not exist "backend\.env" (
    echo إنشاء ملف الإعدادات...
    echo Creating configuration file...
    copy "backend\.env.example" "backend\.env"
    echo تم إنشاء ملف .env. يرجى تحديث إعدادات قاعدة البيانات
    echo .env file created. Please update database settings
)

:: تشغيل سكريبت قاعدة البيانات
echo تشغيل سكريبت إنشاء قاعدة البيانات...
echo Running database initialization script...
mysql -u root -p < database\init.sql
if %errorlevel% neq 0 (
    echo تحذير: فشل في تشغيل سكريبت قاعدة البيانات
    echo Warning: Failed to run database script
    echo يرجى تشغيل database\init.sql يدوياً
    echo Please run database\init.sql manually
)

echo [3/5] تثبيت متطلبات Backend...
echo Installing Backend dependencies...

cd backend
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    echo Creating virtual environment...
    python -m venv venv
)

echo تفعيل البيئة الافتراضية...
echo Activating virtual environment...
call venv\Scripts\activate.bat

echo تثبيت المتطلبات...
echo Installing requirements...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo خطأ: فشل في تثبيت متطلبات Python
    echo Error: Failed to install Python requirements
    pause
    exit /b 1
)

cd ..

echo [4/5] تثبيت متطلبات Frontend...
echo Installing Frontend dependencies...

cd frontend
if not exist "node_modules" (
    echo تثبيت حزم Node.js...
    echo Installing Node.js packages...
    npm install
    
    if %errorlevel% neq 0 (
        echo خطأ: فشل في تثبيت حزم Node.js
        echo Error: Failed to install Node.js packages
        pause
        exit /b 1
    )
) else (
    echo حزم Node.js مثبتة مسبقاً
    echo Node.js packages already installed
)

cd ..

echo [5/5] بدء تشغيل النظام...
echo Starting the system...

:: إنشاء ملفات batch لتشغيل الخوادم
echo @echo off > start_backend.bat
echo cd backend >> start_backend.bat
echo call venv\Scripts\activate.bat >> start_backend.bat
echo echo بدء تشغيل خادم Backend... >> start_backend.bat
echo echo Starting Backend server... >> start_backend.bat
echo uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 >> start_backend.bat

echo @echo off > start_frontend.bat
echo cd frontend >> start_frontend.bat
echo echo بدء تشغيل خادم Frontend... >> start_frontend.bat
echo echo Starting Frontend server... >> start_frontend.bat
echo npm start >> start_frontend.bat

echo.
echo ========================================
echo تم إعداد النظام بنجاح!
echo System setup completed successfully!
echo ========================================
echo.
echo لبدء تشغيل النظام:
echo To start the system:
echo.
echo 1. تشغيل Backend: start_backend.bat
echo    Start Backend: start_backend.bat
echo.
echo 2. تشغيل Frontend: start_frontend.bat  
echo    Start Frontend: start_frontend.bat
echo.
echo أو استخدم: run_system.bat لتشغيل كلاهما معاً
echo Or use: run_system.bat to start both together
echo.
echo الروابط:
echo Links:
echo - Frontend: http://localhost:3000
echo - Backend API: http://localhost:8000
echo - API Documentation: http://localhost:8000/api/docs
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo Default login credentials:
echo - Username: admin
echo - Password: admin123
echo.
pause