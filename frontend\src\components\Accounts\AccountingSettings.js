import React, { useState } from 'react';
import './AccountingSettings.css';

// مكون الإعدادات المحاسبية
export const AccountingSettings = ({ isLoading }) => {
  const [activeSection, setActiveSection] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      companyName: 'شركة شراء السفر والسياحة',
      fiscalYearStart: '01-01',
      currency: 'SAR',
      decimalPlaces: 2,
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'arabic',
      language: 'ar'
    },
    accounting: {
      accountNumbering: 'automatic',
      allowNegativeInventory: false,
      requireApproval: true,
      approvalLimit: 10000,
      autoBackup: true,
      backupFrequency: 'daily'
    },
    taxation: {
      vatEnabled: true,
      vatRate: 15,
      zakatEnabled: true,
      zakatRate: 2.5,
      withholdingTaxEnabled: false,
      exciseTaxEnabled: false
    },
    security: {
      sessionTimeout: 30,
      passwordPolicy: 'strong',
      twoFactorAuth: false,
      auditLog: true,
      dataEncryption: true
    },
    integration: {
      bankIntegration: false,
      erpIntegration: false,
      zatcaIntegration: true,
      emailNotifications: true,
      smsNotifications: false
    }
  });

  const handleSettingChange = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const handleSaveSettings = () => {
    console.log('حفظ الإعدادات:', settings);
    alert('تم حفظ الإعدادات بنجاح');
  };

  const handleResetSettings = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟')) {
      // إعادة تعيين الإعدادات
      console.log('إعادة تعيين الإعدادات');
    }
  };

  // مكون الإعدادات العامة
  const GeneralSettings = () => (
    <div className="settings-section">
      <h4>الإعدادات العامة</h4>
      
      <div className="settings-grid">
        <div className="setting-group">
          <label>اسم الشركة</label>
          <input
            type="text"
            value={settings.general.companyName}
            onChange={(e) => handleSettingChange('general', 'companyName', e.target.value)}
          />
        </div>

        <div className="setting-group">
          <label>بداية السنة المالية</label>
          <select
            value={settings.general.fiscalYearStart}
            onChange={(e) => handleSettingChange('general', 'fiscalYearStart', e.target.value)}
          >
            <option value="01-01">1 يناير</option>
            <option value="04-01">1 أبريل</option>
            <option value="07-01">1 يوليو</option>
            <option value="10-01">1 أكتوبر</option>
          </select>
        </div>

        <div className="setting-group">
          <label>العملة الأساسية</label>
          <select
            value={settings.general.currency}
            onChange={(e) => handleSettingChange('general', 'currency', e.target.value)}
          >
            <option value="SAR">ريال سعودي (SAR)</option>
            <option value="USD">دولار أمريكي (USD)</option>
            <option value="EUR">يورو (EUR)</option>
            <option value="GBP">جنيه إسترليني (GBP)</option>
          </select>
        </div>

        <div className="setting-group">
          <label>عدد المنازل العشرية</label>
          <select
            value={settings.general.decimalPlaces}
            onChange={(e) => handleSettingChange('general', 'decimalPlaces', parseInt(e.target.value))}
          >
            <option value="0">0</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
          </select>
        </div>

        <div className="setting-group">
          <label>تنسيق التاريخ</label>
          <select
            value={settings.general.dateFormat}
            onChange={(e) => handleSettingChange('general', 'dateFormat', e.target.value)}
          >
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>

        <div className="setting-group">
          <label>تنسيق الأرقام</label>
          <select
            value={settings.general.numberFormat}
            onChange={(e) => handleSettingChange('general', 'numberFormat', e.target.value)}
          >
            <option value="arabic">عربي (١٢٣٤٥)</option>
            <option value="english">إنجليزي (12345)</option>
          </select>
        </div>

        <div className="setting-group">
          <label>لغة النظام</label>
          <select
            value={settings.general.language}
            onChange={(e) => handleSettingChange('general', 'language', e.target.value)}
          >
            <option value="ar">العربية</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>
    </div>
  );

  // مكون الإعدادات المحاسبية
  const AccountingSettings = () => (
    <div className="settings-section">
      <h4>الإعدادات المحاسبية</h4>
      
      <div className="settings-grid">
        <div className="setting-group">
          <label>ترقيم الحسابات</label>
          <select
            value={settings.accounting.accountNumbering}
            onChange={(e) => handleSettingChange('accounting', 'accountNumbering', e.target.value)}
          >
            <option value="automatic">تلقائي</option>
            <option value="manual">يدوي</option>
          </select>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.accounting.allowNegativeInventory}
              onChange={(e) => handleSettingChange('accounting', 'allowNegativeInventory', e.target.checked)}
            />
            السماح بالمخزون السالب
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.accounting.requireApproval}
              onChange={(e) => handleSettingChange('accounting', 'requireApproval', e.target.checked)}
            />
            طلب موافقة للمعاملات الكبيرة
          </label>
        </div>

        <div className="setting-group">
          <label>حد الموافقة (ريال)</label>
          <input
            type="number"
            value={settings.accounting.approvalLimit}
            onChange={(e) => handleSettingChange('accounting', 'approvalLimit', parseFloat(e.target.value))}
          />
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.accounting.autoBackup}
              onChange={(e) => handleSettingChange('accounting', 'autoBackup', e.target.checked)}
            />
            النسخ الاحتياطي التلقائي
          </label>
        </div>

        <div className="setting-group">
          <label>تكرار النسخ الاحتياطي</label>
          <select
            value={settings.accounting.backupFrequency}
            onChange={(e) => handleSettingChange('accounting', 'backupFrequency', e.target.value)}
            disabled={!settings.accounting.autoBackup}
          >
            <option value="daily">يومي</option>
            <option value="weekly">أسبوعي</option>
            <option value="monthly">شهري</option>
          </select>
        </div>
      </div>
    </div>
  );

  // مكون الإعدادات الضريبية
  const TaxationSettings = () => (
    <div className="settings-section">
      <h4>الإعدادات الضريبية</h4>
      
      <div className="settings-grid">
        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.taxation.vatEnabled}
              onChange={(e) => handleSettingChange('taxation', 'vatEnabled', e.target.checked)}
            />
            تفعيل ضريبة القيمة المضافة
          </label>
        </div>

        <div className="setting-group">
          <label>معدل ضريبة القيمة المضافة (%)</label>
          <input
            type="number"
            step="0.1"
            value={settings.taxation.vatRate}
            onChange={(e) => handleSettingChange('taxation', 'vatRate', parseFloat(e.target.value))}
            disabled={!settings.taxation.vatEnabled}
          />
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.taxation.zakatEnabled}
              onChange={(e) => handleSettingChange('taxation', 'zakatEnabled', e.target.checked)}
            />
            تفعيل الزكاة
          </label>
        </div>

        <div className="setting-group">
          <label>معدل الزكاة (%)</label>
          <input
            type="number"
            step="0.1"
            value={settings.taxation.zakatRate}
            onChange={(e) => handleSettingChange('taxation', 'zakatRate', parseFloat(e.target.value))}
            disabled={!settings.taxation.zakatEnabled}
          />
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.taxation.withholdingTaxEnabled}
              onChange={(e) => handleSettingChange('taxation', 'withholdingTaxEnabled', e.target.checked)}
            />
            تفعيل ضريبة الاستقطاع
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.taxation.exciseTaxEnabled}
              onChange={(e) => handleSettingChange('taxation', 'exciseTaxEnabled', e.target.checked)}
            />
            تفعيل الضريبة الانتقائية
          </label>
        </div>
      </div>
    </div>
  );

  // مكون إعدادات الأمان
  const SecuritySettings = () => (
    <div className="settings-section">
      <h4>إعدادات الأمان</h4>
      
      <div className="settings-grid">
        <div className="setting-group">
          <label>مهلة انتهاء الجلسة (دقيقة)</label>
          <select
            value={settings.security.sessionTimeout}
            onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
          >
            <option value="15">15 دقيقة</option>
            <option value="30">30 دقيقة</option>
            <option value="60">60 دقيقة</option>
            <option value="120">120 دقيقة</option>
          </select>
        </div>

        <div className="setting-group">
          <label>سياسة كلمة المرور</label>
          <select
            value={settings.security.passwordPolicy}
            onChange={(e) => handleSettingChange('security', 'passwordPolicy', e.target.value)}
          >
            <option value="simple">بسيطة</option>
            <option value="medium">متوسطة</option>
            <option value="strong">قوية</option>
          </select>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.security.twoFactorAuth}
              onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
            />
            المصادقة الثنائية
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.security.auditLog}
              onChange={(e) => handleSettingChange('security', 'auditLog', e.target.checked)}
            />
            سجل المراجعة
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.security.dataEncryption}
              onChange={(e) => handleSettingChange('security', 'dataEncryption', e.target.checked)}
            />
            تشفير البيانات
          </label>
        </div>
      </div>
    </div>
  );

  // مكون إعدادات التكامل
  const IntegrationSettings = () => (
    <div className="settings-section">
      <h4>إعدادات التكامل</h4>
      
      <div className="settings-grid">
        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.integration.bankIntegration}
              onChange={(e) => handleSettingChange('integration', 'bankIntegration', e.target.checked)}
            />
            التكامل مع البنوك
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.integration.erpIntegration}
              onChange={(e) => handleSettingChange('integration', 'erpIntegration', e.target.checked)}
            />
            التكامل مع أنظمة ERP
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.integration.zatcaIntegration}
              onChange={(e) => handleSettingChange('integration', 'zatcaIntegration', e.target.checked)}
            />
            التكامل مع هيئة الزكاة والضريبة
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.integration.emailNotifications}
              onChange={(e) => handleSettingChange('integration', 'emailNotifications', e.target.checked)}
            />
            إشعارات البريد الإلكتروني
          </label>
        </div>

        <div className="setting-group checkbox-group">
          <label>
            <input
              type="checkbox"
              checked={settings.integration.smsNotifications}
              onChange={(e) => handleSettingChange('integration', 'smsNotifications', e.target.checked)}
            />
            إشعارات الرسائل النصية
          </label>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الإعدادات...</p>
      </div>
    );
  }

  return (
    <div className="accounting-settings">
      <div className="settings-header">
        <h3>الإعدادات المحاسبية</h3>
        <div className="header-actions">
          <button className="save-btn" onClick={handleSaveSettings}>
            💾 حفظ الإعدادات
          </button>
          <button className="reset-btn" onClick={handleResetSettings}>
            🔄 إعادة تعيين
          </button>
        </div>
      </div>

      {/* تبويبات الإعدادات */}
      <div className="settings-tabs">
        <button 
          className={`tab-btn ${activeSection === 'general' ? 'active' : ''}`}
          onClick={() => setActiveSection('general')}
        >
          ⚙️ عام
        </button>
        <button 
          className={`tab-btn ${activeSection === 'accounting' ? 'active' : ''}`}
          onClick={() => setActiveSection('accounting')}
        >
          📊 محاسبة
        </button>
        <button 
          className={`tab-btn ${activeSection === 'taxation' ? 'active' : ''}`}
          onClick={() => setActiveSection('taxation')}
        >
          📄 ضرائب
        </button>
        <button 
          className={`tab-btn ${activeSection === 'security' ? 'active' : ''}`}
          onClick={() => setActiveSection('security')}
        >
          🔒 أمان
        </button>
        <button 
          className={`tab-btn ${activeSection === 'integration' ? 'active' : ''}`}
          onClick={() => setActiveSection('integration')}
        >
          🔗 تكامل
        </button>
      </div>

      {/* محتوى الإعدادات */}
      <div className="settings-content">
        {activeSection === 'general' && <GeneralSettings />}
        {activeSection === 'accounting' && <AccountingSettings />}
        {activeSection === 'taxation' && <TaxationSettings />}
        {activeSection === 'security' && <SecuritySettings />}
        {activeSection === 'integration' && <IntegrationSettings />}
      </div>
    </div>
  );
};

export default AccountingSettings;