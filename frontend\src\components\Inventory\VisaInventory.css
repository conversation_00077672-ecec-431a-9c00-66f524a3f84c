/* 📋 أنماط مخزون التأشيرات */

.visa-inventory {
  padding: var(--space-6);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  min-height: 100vh;
  animation: fadeInUp 0.6s ease-out;
}

/* 🎯 رأس الصفحة */
.visa-inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
}

.header-title h1 {
  margin: 0 0 var(--space-1) 0;
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-title p {
  margin: 0;
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

/* 📊 إحصائيات المخزون */
.visa-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  padding: var(--space-6);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  opacity: 0.8;
}

.stat-card.total::before {
  background: var(--gradient-primary);
}

.stat-card.available::before {
  background: var(--gradient-secondary);
}

.stat-card.reserved::before {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
}

.stat-card.used::before {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.stat-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2xl);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  flex-shrink: 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.stat-content h3 {
  margin: 0 0 var(--space-2) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--neutral-800);
  line-height: 1;
}

/* 🔍 البحث والفلاتر */
.visa-filters {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-5);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.visa-search-input {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 1rem;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
  width: 100%;
}

.visa-search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.visa-search-input::placeholder {
  color: var(--neutral-400);
}

.status-filter {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.status-filter:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 📋 جدول المخزون */
.visa-inventory-table {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-bottom: var(--space-6);
}

.visa-inventory-table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.visa-inventory-table th {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  padding: var(--space-4) var(--space-5);
  text-align: right;
  font-weight: 600;
  color: var(--neutral-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid var(--glass-border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.visa-inventory-table td {
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  color: var(--neutral-600);
  font-size: 0.875rem;
  vertical-align: middle;
}

.visa-row {
  transition: all var(--transition-fast);
}

.visa-row:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.visa-row:last-child td {
  border-bottom: none;
}

/* 🏷️ أرقام مميزة */
.issue-number,
.registry-number {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  color: var(--primary-600);
  background: rgba(59, 130, 246, 0.1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  display: inline-block;
}

/* 🎯 حالة التأشيرة */
.status-select {
  padding: var(--space-1) var(--space-3);
  border: none;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.status-select.available {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
  color: white;
}

.status-select.reserved {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
}

.status-select.used {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
  color: white;
}

.status-select.expired {
  background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
  color: white;
}

/* 🎬 أزرار الإجراءات */
.visa-actions {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-btn.view {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* ➕ نموذج إضافة التأشيرة */
.visa-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.visa-form-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideInUp 0.4s ease-out;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
}

.form-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
  cursor: pointer;
  font-size: 1.25rem;
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background: var(--danger-500);
  color: white;
  transform: scale(1.1);
}

/* 📝 نموذج التأشيرة */
.visa-form {
  padding: var(--space-6);
}

.form-grid {
  display: grid;
  gap: var(--space-6);
}

.form-section {
  padding: var(--space-5);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-700);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-3);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-size: 0.875rem;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

/* 🎬 إجراءات النموذج */
.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-6);
  border-top: 1px solid var(--glass-border);
  margin-top: var(--space-6);
}

/* 🔄 حالة التحميل */
.visa-inventory-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
  min-height: 50vh;
}

.visa-inventory-loading p {
  margin-top: var(--space-4);
  font-size: 1.125rem;
  color: var(--neutral-600);
  font-weight: 500;
}

/* 📱 التصميم المتجاوب */
@media (max-width: 1024px) {
  .visa-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .visa-filters {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .visa-inventory {
    padding: var(--space-4);
  }
  
  .visa-inventory-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .visa-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .visa-form-modal {
    width: 95%;
    margin: var(--space-4);
  }
}

@media (max-width: 480px) {
  .header-title h1 {
    font-size: 2rem;
  }
  
  .visa-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .stat-value {
    font-size: 2rem;
  }
}

/* 🎬 حركات خاصة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .visa-inventory,
  .stat-card,
  .visa-row,
  .action-btn {
    animation: none;
    transition: none;
  }
  
  .stat-card:hover,
  .action-btn:hover,
  .visa-row:hover {
    transform: none;
  }
}
