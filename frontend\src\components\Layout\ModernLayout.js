import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ModernPageLayout, ModernHeader, ModernSidebar, ModernMain } from '../UI/ModernLayout';
import ModernButton, { IconButton } from '../UI/ModernButton';
import ModernCard, { CardBody } from '../UI/ModernCard';
import ThemeToggle from '../UI/ThemeToggle';
import './ModernLayout.css';

const ModernLayout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentUser] = useState({
    name: 'أحمد محمد العلي',
    role: 'مدير النظام',
    avatar: '👤'
  });

  const menuItems = [
    { 
      id: 'dashboard', 
      name: 'لوحة التحكم', 
      icon: '📊', 
      path: '/dashboard',
      color: 'primary'
    },
    { 
      id: 'customers', 
      name: 'العملاء', 
      icon: '👥', 
      path: '/customers',
      color: 'secondary'
    },
    { 
      id: 'bookings', 
      name: 'الحجوزات', 
      icon: '✈️', 
      path: '/bookings',
      color: 'info'
    },
    { 
      id: 'sales', 
      name: 'المبيعات', 
      icon: '💰', 
      path: '/sales',
      color: 'success',
      submenu: [
        { name: 'إدارة المبيعات', path: '/sales/management', icon: '📋' },
        { name: 'الفواتير', path: '/sales/invoices', icon: '🧾' },
        { name: 'العملاء', path: '/sales/customers', icon: '👤' },
        { name: 'التقارير', path: '/sales/reports', icon: '📊' },
        { name: 'المدفوعات', path: '/sales/payments', icon: '💳' },
        { name: 'الإعدادات', path: '/sales/settings', icon: '⚙️' }
      ]
    },
    { 
      id: 'purchases', 
      name: 'المشتريات', 
      icon: '🛒', 
      path: '/purchases',
      color: 'warning'
    },
    { 
      id: 'inventory', 
      name: 'المخزون', 
      icon: '📦', 
      path: '/inventory',
      color: 'info',
      submenu: [
        { name: 'إدارة المخزون', path: '/inventory', icon: '📦' },
        { name: 'مخزون التأشيرات', path: '/inventory/visas', icon: '📋' }
      ]
    },
    { 
      id: 'finance', 
      name: 'المالية', 
      icon: '💼', 
      path: '/finance',
      color: 'primary'
    },
    { 
      id: 'accounts', 
      name: 'الحسابات', 
      icon: '📚', 
      path: '/accounts',
      color: 'secondary'
    },
    { 
      id: 'templates', 
      name: 'القوالب', 
      icon: '📄', 
      path: '/templates',
      color: 'info'
    },
    { 
      id: 'suppliers', 
      name: 'الموردين', 
      icon: '🏢', 
      path: '/suppliers',
      color: 'warning'
    },
    { 
      id: 'agents', 
      name: 'الوكلاء', 
      icon: '🤝', 
      path: '/agents',
      color: 'success'
    },
    { 
      id: 'reports', 
      name: 'التقارير', 
      icon: '📈', 
      path: '/reports',
      color: 'primary'
    },
    { 
      id: 'settings', 
      name: 'الإعدادات', 
      icon: '⚙️', 
      path: '/settings',
      color: 'secondary'
    }
  ];

  const [expandedMenus, setExpandedMenus] = useState(new Set());

  const toggleSubmenu = (itemId) => {
    const newExpanded = new Set(expandedMenus);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedMenus(newExpanded);
  };

  const handleLogout = () => {
    if (window.confirm('هل تريد تسجيل الخروج؟')) {
      navigate('/');
    }
  };

  const isActiveRoute = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Header Component
  const HeaderContent = ({ onToggleSidebar, sidebarCollapsed }) => (
    <div className="modern-layout-header">
      <div className="modern-layout-header__left">
        <IconButton
          icon={sidebarCollapsed ? '☰' : '✕'}
          variant="ghost"
          size="lg"
          onClick={onToggleSidebar}
          className="sidebar-toggle"
        />
        <div className="breadcrumb">
          <span className="breadcrumb__item">شراء للسفر والسياحة</span>
          <span className="breadcrumb__separator">›</span>
          <span className="breadcrumb__item breadcrumb__item--current">
            {menuItems.find(item => isActiveRoute(item.path))?.name || 'الصفحة الرئيسية'}
          </span>
        </div>
      </div>

      <div className="modern-layout-header__center">
        <div className="search-container">
          <input
            type="text"
            placeholder="البحث في النظام..."
            className="search-input"
          />
          <IconButton
            icon="🔍"
            variant="ghost"
            size="sm"
            className="search-btn"
          />
        </div>
      </div>

      <div className="modern-layout-header__right">
        <ThemeToggle
          size="small"
          showText={false}
          className="header-theme-toggle"
        />

        <IconButton
          icon="🔔"
          variant="ghost"
          size="lg"
          className="notification-btn"
        />

        <div className="user-menu">
          <div className="user-info">
            <span className="user-avatar">{currentUser.avatar}</span>
            <div className="user-details">
              <span className="user-name">{currentUser.name}</span>
              <span className="user-role">{currentUser.role}</span>
            </div>
          </div>
          <IconButton
            icon="⚙️"
            variant="ghost"
            size="sm"
            onClick={handleLogout}
          />
        </div>
      </div>
    </div>
  );

  // Sidebar Component
  const SidebarContent = () => (
    <div className="modern-layout-sidebar">
      <div className="sidebar-header">
        <div className="logo">
          <span className="logo-icon">🌟</span>
          {!sidebarCollapsed && (
            <div className="logo-text">
              <h3>شراء للسفر</h3>
              <p>نظام إدارة شامل</p>
            </div>
          )}
        </div>
      </div>

      <nav className="sidebar-nav">
        <ul className="nav-list">
          {menuItems.map((item) => (
            <li key={item.id} className="nav-item">
              <button
                className={`nav-link ${isActiveRoute(item.path) ? 'nav-link--active' : ''} nav-link--${item.color}`}
                onClick={() => {
                  if (item.submenu) {
                    toggleSubmenu(item.id);
                  } else {
                    navigate(item.path);
                  }
                }}
              >
                <span className="nav-icon">{item.icon}</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">{item.name}</span>
                    {item.submenu && (
                      <span className={`nav-arrow ${expandedMenus.has(item.id) ? 'nav-arrow--expanded' : ''}`}>
                        ▼
                      </span>
                    )}
                  </>
                )}
              </button>

              {item.submenu && expandedMenus.has(item.id) && !sidebarCollapsed && (
                <ul className="submenu">
                  {item.submenu.map((subItem, index) => (
                    <li key={index} className="submenu-item">
                      <button
                        className={`submenu-link ${isActiveRoute(subItem.path) ? 'submenu-link--active' : ''}`}
                        onClick={() => navigate(subItem.path)}
                      >
                        <span className="submenu-icon">{subItem.icon}</span>
                        <span className="submenu-text">{subItem.name}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <div className="sidebar-footer">
        {!sidebarCollapsed && (
          <ModernCard variant="stats" size="sm" className="sidebar-stats">
            <CardBody>
              <div className="stats-item">
                <span className="stats-label">المعاملات اليوم</span>
                <span className="stats-value">127</span>
              </div>
              <div className="stats-item">
                <span className="stats-label">الإيرادات</span>
                <span className="stats-value">45,230 ر.س</span>
              </div>
            </CardBody>
          </ModernCard>
        )}
        
        <ModernButton
          variant="error"
          size="sm"
          icon="🚪"
          onClick={handleLogout}
          className="logout-btn"
          fullWidth={!sidebarCollapsed}
        >
          {!sidebarCollapsed && 'تسجيل الخروج'}
        </ModernButton>
      </div>
    </div>
  );

  return (
    <ModernPageLayout
      header={<HeaderContent />}
      sidebar={<SidebarContent />}
      sidebarCollapsed={sidebarCollapsed}
      sidebarPosition="right"
      className="modern-travel-layout"
    >
      <div className="page-content">
        {children}
      </div>
    </ModernPageLayout>
  );
};

export default ModernLayout;