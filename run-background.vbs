' ===================================================================
' 🔇 سكربت التشغيل في الخلفية لنظام شراء للسفر والسياحة
' Background Runner for Sharau Travel System
' ===================================================================

Dim WshShell, ProjectPath, ScriptPath, UsePwsh

' تحديد مسار المشروع
ProjectPath = "c:\Users\<USER>\Desktop\sharaubtravelsoft"
ScriptPath = ProjectPath & "\start-sharau-app.ps1"

Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = ProjectPath

' تفضيل PowerShell 7 إن وجد (تحقق سريع عبر محاولة تشغيل pwsh -v)
On Error Resume Next
WshShell.Run "pwsh -NoLogo -NoProfile -v", 0, True
If Err.Number = 0 Then
    UsePwsh = True
Else
    UsePwsh = False
End If
On Error GoTo 0

If UsePwsh Then
    WshShell.Run "pwsh -WindowStyle Hidden -NoLogo -NoProfile -ExecutionPolicy Bypass -File """ & ScriptPath & """", 0, False
Else
    WshShell.Run "powershell.exe -WindowStyle Hidden -NoLogo -NoProfile -ExecutionPolicy Bypass -File """ & ScriptPath & """", 0, False
End If

Set WshShell = Nothing