import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { EnhancedNotificationsProvider, useNotifications } from '../../../components/UI/EnhancedNotifications';

// 🧪 اختبارات نظام الإشعارات المحسن

// مكون اختبار لاستخدام الهوك
const TestComponent = () => {
  const { success, error, warning, info, loading } = useNotifications();
  
  return (
    <div>
      <button onClick={() => success('نجح العمل!')}>Success</button>
      <button onClick={() => error('حدث خطأ!')}>Error</button>
      <button onClick={() => warning('تحذير!')}>Warning</button>
      <button onClick={() => info('معلومة مفيدة')}>Info</button>
      <button onClick={() => loading('جاري التحميل...')}>Loading</button>
    </div>
  );
};

const TestWrapper = ({ children }) => (
  <EnhancedNotificationsProvider>
    {children}
  </EnhancedNotificationsProvider>
);

describe('EnhancedNotifications', () => {
  beforeEach(() => {
    // تنظيف DOM قبل كل اختبار
    document.body.innerHTML = '';
  });

  afterEach(() => {
    // تنظيف المؤقتات
    jest.clearAllTimers();
  });

  describe('عرض الإشعارات', () => {
    test('يجب عرض إشعار النجاح', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const successButton = screen.getByText('Success');
      fireEvent.click(successButton);

      await waitFor(() => {
        expect(screen.getByText('نجح العمل!')).toBeInTheDocument();
      });

      expect(screen.getByRole('alert')).toHaveClass('notification-success');
    });

    test('يجب عرض إشعار الخطأ', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const errorButton = screen.getByText('Error');
      fireEvent.click(errorButton);

      await waitFor(() => {
        expect(screen.getByText('حدث خطأ!')).toBeInTheDocument();
      });

      expect(screen.getByRole('alert')).toHaveClass('notification-error');
    });

    test('يجب عرض إشعار التحذير', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const warningButton = screen.getByText('Warning');
      fireEvent.click(warningButton);

      await waitFor(() => {
        expect(screen.getByText('تحذير!')).toBeInTheDocument();
      });

      expect(screen.getByRole('alert')).toHaveClass('notification-warning');
    });

    test('يجب عرض إشعار المعلومات', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const infoButton = screen.getByText('Info');
      fireEvent.click(infoButton);

      await waitFor(() => {
        expect(screen.getByText('معلومة مفيدة')).toBeInTheDocument();
      });

      expect(screen.getByRole('alert')).toHaveClass('notification-info');
    });
  });

  describe('إدارة الإشعارات', () => {
    test('يجب إزالة الإشعار عند النقر على زر الإغلاق', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const successButton = screen.getByText('Success');
      fireEvent.click(successButton);

      await waitFor(() => {
        expect(screen.getByText('نجح العمل!')).toBeInTheDocument();
      });

      const closeButton = screen.getByLabelText('إغلاق الإشعار');
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText('نجح العمل!')).not.toBeInTheDocument();
      });
    });

    test('يجب إزالة الإشعار تلقائياً بعد المدة المحددة', async () => {
      jest.useFakeTimers();

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const successButton = screen.getByText('Success');
      fireEvent.click(successButton);

      await waitFor(() => {
        expect(screen.getByText('نجح العمل!')).toBeInTheDocument();
      });

      // تقديم الوقت بـ 5 ثوان (المدة الافتراضية)
      act(() => {
        jest.advanceTimersByTime(5000);
      });

      await waitFor(() => {
        expect(screen.queryByText('نجح العمل!')).not.toBeInTheDocument();
      });

      jest.useRealTimers();
    });

    test('يجب عرض عدة إشعارات في نفس الوقت', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const successButton = screen.getByText('Success');
      const errorButton = screen.getByText('Error');

      fireEvent.click(successButton);
      fireEvent.click(errorButton);

      await waitFor(() => {
        expect(screen.getByText('نجح العمل!')).toBeInTheDocument();
        expect(screen.getByText('حدث خطأ!')).toBeInTheDocument();
      });

      const notifications = screen.getAllByRole('alert');
      expect(notifications).toHaveLength(2);
    });
  });

  describe('إشعارات التحميل', () => {
    test('يجب عرض إشعار التحميل مع الرسوم المتحركة', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const loadingButton = screen.getByText('Loading');
      fireEvent.click(loadingButton);

      await waitFor(() => {
        expect(screen.getByText('جاري التحميل...')).toBeInTheDocument();
      });

      expect(screen.getByRole('alert')).toHaveClass('notification-loading');
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });
  });

  describe('إشعارات مع إجراءات', () => {
    test('يجب عرض أزرار الإجراءات', async () => {
      const TestComponentWithActions = () => {
        const { success } = useNotifications();
        
        const handleSuccess = () => {
          success('تم الحفظ بنجاح!', {
            actions: [
              {
                label: 'عرض',
                onClick: () => console.log('عرض')
              },
              {
                label: 'تراجع',
                onClick: () => console.log('تراجع')
              }
            ]
          });
        };
        
        return <button onClick={handleSuccess}>Success with Actions</button>;
      };

      render(
        <TestWrapper>
          <TestComponentWithActions />
        </TestWrapper>
      );

      const button = screen.getByText('Success with Actions');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('تم الحفظ بنجاح!')).toBeInTheDocument();
        expect(screen.getByText('عرض')).toBeInTheDocument();
        expect(screen.getByText('تراجع')).toBeInTheDocument();
      });
    });

    test('يجب تنفيذ إجراء عند النقر على الزر', async () => {
      const mockAction = jest.fn();
      
      const TestComponentWithMockAction = () => {
        const { success } = useNotifications();
        
        const handleSuccess = () => {
          success('تم الحفظ!', {
            actions: [
              {
                label: 'اختبار',
                onClick: mockAction
              }
            ]
          });
        };
        
        return <button onClick={handleSuccess}>Test Action</button>;
      };

      render(
        <TestWrapper>
          <TestComponentWithMockAction />
        </TestWrapper>
      );

      const button = screen.getByText('Test Action');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('اختبار')).toBeInTheDocument();
      });

      const actionButton = screen.getByText('اختبار');
      fireEvent.click(actionButton);

      expect(mockAction).toHaveBeenCalledTimes(1);
    });
  });

  describe('إعدادات الإشعارات', () => {
    test('يجب احترام إعداد المدة المخصصة', async () => {
      jest.useFakeTimers();

      const TestComponentWithCustomDuration = () => {
        const { success } = useNotifications();
        
        const handleSuccess = () => {
          success('إشعار مخصص', { duration: 2000 });
        };
        
        return <button onClick={handleSuccess}>Custom Duration</button>;
      };

      render(
        <TestWrapper>
          <TestComponentWithCustomDuration />
        </TestWrapper>
      );

      const button = screen.getByText('Custom Duration');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('إشعار مخصص')).toBeInTheDocument();
      });

      // تقديم الوقت بثانيتين
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(screen.queryByText('إشعار مخصص')).not.toBeInTheDocument();
      });

      jest.useRealTimers();
    });

    test('يجب عدم إزالة الإشعار إذا كانت المدة 0', async () => {
      jest.useFakeTimers();

      const TestComponentWithPersistent = () => {
        const { success } = useNotifications();
        
        const handleSuccess = () => {
          success('إشعار دائم', { duration: 0 });
        };
        
        return <button onClick={handleSuccess}>Persistent</button>;
      };

      render(
        <TestWrapper>
          <TestComponentWithPersistent />
        </TestWrapper>
      );

      const button = screen.getByText('Persistent');
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.getByText('إشعار دائم')).toBeInTheDocument();
      });

      // تقديم الوقت بـ 10 ثوان
      act(() => {
        jest.advanceTimersByTime(10000);
      });

      // يجب أن يبقى الإشعار
      expect(screen.getByText('إشعار دائم')).toBeInTheDocument();

      jest.useRealTimers();
    });
  });

  describe('إمكانية الوصول', () => {
    test('يجب أن تحتوي الإشعارات على خصائص إمكانية الوصول الصحيحة', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const successButton = screen.getByText('Success');
      fireEvent.click(successButton);

      await waitFor(() => {
        const notification = screen.getByRole('alert');
        expect(notification).toHaveAttribute('aria-live', 'polite');
        expect(notification).toHaveAttribute('aria-atomic', 'true');
      });
    });

    test('يجب أن تكون أزرار الإغلاق قابلة للوصول', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const successButton = screen.getByText('Success');
      fireEvent.click(successButton);

      await waitFor(() => {
        const closeButton = screen.getByLabelText('إغلاق الإشعار');
        expect(closeButton).toBeInTheDocument();
        expect(closeButton).toHaveAttribute('type', 'button');
      });
    });
  });

  describe('معالجة الأخطاء', () => {
    test('يجب عدم تعطل التطبيق عند حدوث خطأ في الإشعار', () => {
      const TestComponentWithError = () => {
        const { success } = useNotifications();
        
        const handleError = () => {
          // محاولة إرسال كائن غير صالح
          success(null);
        };
        
        return <button onClick={handleError}>Error Test</button>;
      };

      expect(() => {
        render(
          <TestWrapper>
            <TestComponentWithError />
          </TestWrapper>
        );
        
        const button = screen.getByText('Error Test');
        fireEvent.click(button);
      }).not.toThrow();
    });
  });
});
