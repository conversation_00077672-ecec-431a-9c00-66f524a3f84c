/* Modern Travel System - Global Styles */

/* Import all component styles */
@import '../components/UI/globals.css';
@import '../components/UI/ModernCard.css';
@import '../components/UI/ModernButton.css';
@import '../components/UI/ModernLayout.css';
@import '../components/UI/ModernForm.css';
@import '../components/UI/ModernTable.css';
@import '../components/UI/ModernAlert.css';
@import '../components/UI/ModernModal.css';
@import '../components/UI/ModernNavigation.css';
@import '../components/UI/ModernCharts.css';
@import '../components/Layout/ModernSystemLayout.css';

/* ===== GLOBAL SYSTEM STYLES ===== */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-arabic);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--neutral-800);
  background: var(--gradient-background);
  direction: rtl;
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-arabic);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--neutral-900);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--text-4xl);
}

h2 {
  font-size: var(--text-3xl);
}

h3 {
  font-size: var(--text-2xl);
}

h4 {
  font-size: var(--text-xl);
}

h5 {
  font-size: var(--text-lg);
}

h6 {
  font-size: var(--text-base);
}

p {
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
}

a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: var(--radius-full);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--neutral-400) var(--neutral-100);
}

/* ===== SELECTION STYLING ===== */
::selection {
  background: var(--primary-200);
  color: var(--primary-900);
}

::-moz-selection {
  background: var(--primary-200);
  color: var(--primary-900);
}

/* ===== FOCUS STYLES ===== */
:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== UTILITY CLASSES ===== */

/* Text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

/* Color utilities */
.text-primary { color: var(--primary-600); }
.text-secondary { color: var(--secondary-600); }
.text-success { color: var(--success-600); }
.text-warning { color: var(--warning-600); }
.text-error { color: var(--error-600); }
.text-info { color: var(--info-600); }
.text-neutral { color: var(--neutral-600); }

.bg-primary { background-color: var(--primary-500); }
.bg-secondary { background-color: var(--secondary-500); }
.bg-success { background-color: var(--success-500); }
.bg-warning { background-color: var(--warning-500); }
.bg-error { background-color: var(--error-500); }
.bg-info { background-color: var(--info-500); }
.bg-neutral { background-color: var(--neutral-500); }

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Margin top/bottom */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* Display utilities */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

/* Flex utilities */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Border utilities */
.border { border: 1px solid var(--neutral-300); }
.border-0 { border: none; }
.border-t { border-top: 1px solid var(--neutral-300); }
.border-b { border-bottom: 1px solid var(--neutral-300); }
.border-l { border-left: 1px solid var(--neutral-300); }
.border-r { border-right: 1px solid var(--neutral-300); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-none { box-shadow: none; }

/* Width/Height utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Z-index utilities */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Opacity utilities */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Cursor utilities */
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

/* ===== ANIMATION UTILITIES ===== */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.5s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:text-sm { font-size: var(--text-sm); }
  .sm\:text-base { font-size: var(--text-base); }
  .sm\:p-2 { padding: var(--space-2); }
  .sm\:p-4 { padding: var(--space-4); }
  .sm\:m-2 { margin: var(--space-2); }
  .sm\:m-4 { margin: var(--space-4); }
}

@media (max-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:text-sm { font-size: var(--text-sm); }
  .md\:text-base { font-size: var(--text-base); }
  .md\:p-2 { padding: var(--space-2); }
  .md\:p-4 { padding: var(--space-4); }
  .md\:m-2 { margin: var(--space-2); }
  .md\:m-4 { margin: var(--space-4); }
}

@media (max-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:text-sm { font-size: var(--text-sm); }
  .lg\:text-base { font-size: var(--text-base); }
  .lg\:p-2 { padding: var(--space-2); }
  .lg\:p-4 { padding: var(--space-4); }
  .lg\:m-2 { margin: var(--space-2); }
  .lg\:m-4 { margin: var(--space-4); }
}

/* ===== PRINT STYLES ===== */
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }
  
  * {
    color: black !important;
    background: white !important;
    box-shadow: none !important;
  }
  
  a {
    text-decoration: underline !important;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
  
  @page {
    margin: 1cm;
  }
}

/* ===== DARK MODE UTILITIES ===== */
@media (prefers-color-scheme: dark) {
  .dark\:text-white { color: white; }
  .dark\:text-gray-100 { color: var(--neutral-100); }
  .dark\:text-gray-200 { color: var(--neutral-200); }
  .dark\:text-gray-300 { color: var(--neutral-300); }
  .dark\:text-gray-400 { color: var(--neutral-400); }
  
  .dark\:bg-gray-800 { background-color: var(--neutral-800); }
  .dark\:bg-gray-900 { background-color: var(--neutral-900); }
  
  .dark\:border-gray-600 { border-color: var(--neutral-600); }
  .dark\:border-gray-700 { border-color: var(--neutral-700); }
}

/* ===== ACCESSIBILITY UTILITIES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  .border {
    border-width: 2px !important;
  }
  
  a {
    text-decoration: underline !important;
  }
  
  button, .btn {
    border: 2px solid currentColor !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-pulse,
  .animate-bounce,
  .animate-spin,
  .animate-fade-in,
  .animate-slide-in-up,
  .animate-slide-in-down {
    animation: none !important;
  }
}