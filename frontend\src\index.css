/* 🎨 استيراد الأنماط المتطورة */
@import './styles/GlobalTheme.css';
@import './styles/modern-system.css';
@import './styles/animations.css';
@import './styles/animation-classes.css';

/* 🔤 استيراد الخطوط من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* ===== الأنماط العامة المحدثة ===== */

/* 🔄 إعادة تعيين شاملة محسنة */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 🎨 تحسينات الجسم */
body {
  font-family: 'Cairo', 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
  color: var(--neutral-800);
  background: var(--gradient-aurora);
  background-attachment: fixed;
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  transition: all var(--transition-normal);
}

/* 🌙 تحسينات الوضع المظلم للجسم */
[data-theme="dark"] body,
.dark-mode body {
  background: var(--gradient-cosmic);
  color: var(--neutral-100);
}

/* 💻 تحسين الكود */
code {
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', monospace;
  font-size: 0.875em;
  background: rgba(99, 102, 241, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  color: var(--primary-700);
}

/* ===== شريط التمرير المتطور ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

::-webkit-scrollbar-corner {
  background: var(--neutral-100);
}

/* 🌙 شريط التمرير للوضع المظلم */
[data-theme="dark"] ::-webkit-scrollbar-track,
.dark-mode ::-webkit-scrollbar-track {
  background: var(--neutral-800);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb,
.dark-mode ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover,
.dark-mode ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
}

/* ===== حركات محسنة للتوافق مع النظام الجديد ===== */

/* 💫 حركة الظهور المحسنة */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 🎯 حركات الانزلاق المحسنة */
.slide-in-right {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
  animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 🔄 مؤشر التحميل المحسن */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--primary-200);
  border-radius: 50%;
  border-top-color: var(--primary-600);
  animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

/* 🌟 مؤشر تحميل متقدم */
.loading-spinner-advanced {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-radius: 50%;
  background: conic-gradient(from 0deg, var(--primary-600), var(--primary-300), var(--primary-600));
  animation: spin 1.5s linear infinite;
  position: relative;
}

.loading-spinner-advanced::before {
  content: '';
  position: absolute;
  inset: 3px;
  background: var(--bg-primary);
  border-radius: 50%;
}

/* 💎 مؤشر تحميل بريق */
.loading-shimmer {
  display: inline-block;
  width: 100px;
  height: 20px;
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200% 100%;
  border-radius: var(--radius-md);
  animation: shimmer 2s infinite;
}

/* ===== خلفيات متدرجة متطورة ===== */

/* 🎨 تدرجات أساسية محسنة */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-success {
  background: var(--gradient-secondary);
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-400) 0%, var(--warning-600) 100%);
}

.gradient-error {
  background: linear-gradient(135deg, var(--danger-400) 0%, var(--danger-600) 100%);
}

/* 🌅 تدرجات خاصة */
.gradient-sunset {
  background: var(--gradient-sunset);
}

.gradient-ocean {
  background: var(--gradient-ocean);
}

.gradient-forest {
  background: var(--gradient-forest);
}

.gradient-royal {
  background: var(--gradient-royal);
}

.gradient-aurora {
  background: var(--gradient-aurora);
}

.gradient-cosmic {
  background: var(--gradient-cosmic);
}

/* 🎭 تدرجات متحركة */
.gradient-animated {
  background: linear-gradient(-45deg, var(--primary-400), var(--secondary-400), var(--info-400), var(--warning-400));
  background-size: 400% 400%;
  animation: gradientShift 4s ease infinite;
}

/* ===== تأثيرات البطاقات المتطورة ===== */

/* 🎴 تأثير الرفع */
.card-hover {
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

/* 💎 تأثير Glassmorphism */
.card-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  transition: all var(--transition-normal);
}

.card-glass:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: translateY(-4px);
  box-shadow: var(--shadow-glow);
}

/* 🎯 تأثير Neumorphism */
.card-neuro {
  background: var(--neuro-light);
  box-shadow: var(--neuro-shadow-light), var(--neuro-shadow-dark);
  transition: all var(--transition-normal);
}

.card-neuro:hover {
  box-shadow: var(--neuro-inset-light), var(--neuro-inset-dark);
}

/* ===== تأثيرات الأزرار المتطورة ===== */

/* ✨ زر بتأثير البريق */
.btn-animate {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.btn-animate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-animate:hover::before {
  left: 100%;
}

/* 🌊 زر بتأثير الموجة */
.btn-ripple {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-fast);
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* 🎭 زر بتأثير التدرج المتحرك */
.btn-gradient-animated {
  background: linear-gradient(-45deg, var(--primary-500), var(--secondary-500), var(--info-500), var(--warning-500));
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
  color: white;
  border: none;
}

/* ===== أنماط النماذج المتطورة ===== */

/* 📝 حاوية النموذج */
.form-container {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
}

.form-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

/* 🎯 رأس النموذج */
.form-header {
  text-align: center;
  margin-bottom: var(--space-8);
  color: var(--neutral-800);
}

.form-header h1 {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: var(--space-2);
}

.form-header p {
  color: var(--neutral-600);
  font-size: 1.125rem;
}

/* ===== شبكة لوحة التحكم المتطورة ===== */

/* 📊 شبكة أساسية */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* 📱 شبكة متجاوبة */
.dashboard-grid-responsive {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: repeat(12, 1fr);
}

.dashboard-grid-responsive .col-span-1 { grid-column: span 1; }
.dashboard-grid-responsive .col-span-2 { grid-column: span 2; }
.dashboard-grid-responsive .col-span-3 { grid-column: span 3; }
.dashboard-grid-responsive .col-span-4 { grid-column: span 4; }
.dashboard-grid-responsive .col-span-6 { grid-column: span 6; }
.dashboard-grid-responsive .col-span-8 { grid-column: span 8; }
.dashboard-grid-responsive .col-span-12 { grid-column: span 12; }

/* ===== التصميم المتجاوب المحسن ===== */

/* 📱 الهواتف الصغيرة */
@media (max-width: 480px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .form-container {
    padding: var(--space-4);
    margin: var(--space-2);
    border-radius: var(--radius-xl);
  }

  .dashboard-grid-responsive {
    grid-template-columns: 1fr;
  }

  .dashboard-grid-responsive .col-span-1,
  .dashboard-grid-responsive .col-span-2,
  .dashboard-grid-responsive .col-span-3,
  .dashboard-grid-responsive .col-span-4,
  .dashboard-grid-responsive .col-span-6,
  .dashboard-grid-responsive .col-span-8,
  .dashboard-grid-responsive .col-span-12 {
    grid-column: span 1;
  }
}

/* 📱 الأجهزة اللوحية */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-5);
  }

  .form-container {
    padding: var(--space-6);
    margin: var(--space-3);
  }

  .dashboard-grid-responsive {
    grid-template-columns: repeat(6, 1fr);
  }

  .dashboard-grid-responsive .col-span-8,
  .dashboard-grid-responsive .col-span-12 {
    grid-column: span 6;
  }
}

/* 💻 الشاشات المتوسطة */
@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .dashboard-grid-responsive {
    grid-template-columns: repeat(8, 1fr);
  }

  .dashboard-grid-responsive .col-span-12 {
    grid-column: span 8;
  }
}

/* ===== أنماط الطباعة المحسنة ===== */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  .print-break {
    page-break-before: always;
  }

  .print-avoid-break {
    page-break-inside: avoid;
  }

  .print-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: white;
    border-bottom: 1px solid #ccc;
  }

  .print-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: white;
    border-top: 1px solid #ccc;
    text-align: center;
    font-size: 10pt;
  }
}

/* ===== أنماط الأرقام العربية ===== */
.arabic-numbers {
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
  direction: ltr;
  text-align: left;
}

.currency-format {
  font-family: 'JetBrains Mono', monospace;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* ===== شارات الحالة المتطورة ===== */

/* ✅ حالة نشط */
.status-active {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.status-active:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* ❌ حالة غير نشط */
.status-inactive {
  background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.status-inactive:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* ⏳ حالة معلق */
.status-pending {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.status-pending:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* 💎 حالة مميز */
.status-premium {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.status-premium:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* 🔄 حالة قيد المعالجة */
.status-processing {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.status-processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

.status-processing:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* ===== أدوات مساعدة إضافية ===== */

/* 🎨 تأثيرات خاصة */
.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.neuro-effect {
  background: var(--neuro-light);
  box-shadow: var(--neuro-shadow-light), var(--neuro-shadow-dark);
}

.glow-effect {
  box-shadow: var(--shadow-glow);
}

.shimmer-effect {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 🔄 حالات التفاعل */
.interactive {
  transition: all var(--transition-fast);
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* 📱 تحسينات الأجهزة المحمولة */
@media (max-width: 640px) {
  .status-active,
  .status-inactive,
  .status-pending,
  .status-premium,
  .status-processing {
    font-size: 0.625rem;
    padding: 2px 8px;
  }
}