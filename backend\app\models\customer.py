"""
نموذج العملاء
Customer Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Decimal, ForeignKey, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.models.base import BaseModel

class CustomerType(PyEnum):
    """أنواع العملاء"""
    INDIVIDUAL = "individual"  # فردي
    CORPORATE = "corporate"    # شركة
    GOVERNMENT = "government"  # حكومي
    TRAVEL_AGENT = "travel_agent"  # وكيل سفر

class CustomerStatus(PyEnum):
    """حالة العميل"""
    ACTIVE = "active"      # نشط
    INACTIVE = "inactive"  # غير نشط
    BLOCKED = "blocked"    # محظور
    VIP = "vip"           # عميل مميز

class Customer(BaseModel):
    """
    نموذج العملاء
    """
    __tablename__ = "customers"
    
    # المعلومات الأساسية
    customer_code = Column(String(20), unique=True, nullable=False, index=True)
    customer_type = Column(Enum(CustomerType), nullable=False, default=CustomerType.INDIVIDUAL)
    status = Column(Enum(CustomerStatus), nullable=False, default=CustomerStatus.ACTIVE)
    
    # معلومات شخصية/شركة
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    company_name = Column(String(100), nullable=True)
    title = Column(String(50), nullable=True)  # المسمى الوظيفي
    
    # معلومات الاتصال
    email = Column(String(100), nullable=True, index=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    website = Column(String(100), nullable=True)
    
    # العنوان
    address_line1 = Column(String(200), nullable=True)
    address_line2 = Column(String(200), nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # معلومات الهوية
    national_id = Column(String(50), nullable=True)
    passport_number = Column(String(50), nullable=True)
    passport_expiry = Column(DateTime, nullable=True)
    visa_number = Column(String(50), nullable=True)
    visa_expiry = Column(DateTime, nullable=True)
    
    # معلومات مالية
    credit_limit = Column(Decimal(15, 2), default=0.00)
    current_balance = Column(Decimal(15, 2), default=0.00)
    payment_terms = Column(Integer, default=0)  # شروط الدفع بالأيام
    discount_percentage = Column(Decimal(5, 2), default=0.00)
    
    # معلومات إضافية
    birth_date = Column(DateTime, nullable=True)
    nationality = Column(String(50), nullable=True)
    preferred_language = Column(String(10), default='ar')
    preferred_currency = Column(String(3), default='USD')
    
    # معلومات التسويق
    source = Column(String(50), nullable=True)  # مصدر العميل
    referral_code = Column(String(20), nullable=True)  # كود الإحالة
    marketing_consent = Column(Boolean, default=False)  # موافقة التسويق
    
    # العلاقات
    bookings = relationship("Booking", back_populates="customer")
    invoices = relationship("Invoice", back_populates="customer")
    payments = relationship("Payment", back_populates="customer")
    contacts = relationship("CustomerContact", back_populates="customer")
    documents = relationship("CustomerDocument", back_populates="customer")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        if self.customer_type == CustomerType.CORPORATE:
            return self.company_name
        return f"{self.first_name or ''} {self.last_name or ''}".strip()
    
    @property
    def display_name(self):
        """الاسم المعروض"""
        return self.full_name or self.customer_code
    
    def __repr__(self):
        return f"<Customer(code='{self.customer_code}', name='{self.display_name}')>"

class CustomerContact(BaseModel):
    """
    جهات اتصال العميل الإضافية
    """
    __tablename__ = "customer_contacts"
    
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    contact_type = Column(String(20), nullable=False)  # primary, billing, emergency
    
    name = Column(String(100), nullable=False)
    title = Column(String(50), nullable=True)
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    
    is_primary = Column(Boolean, default=False)
    
    # العلاقات
    customer = relationship("Customer", back_populates="contacts")
    
    def __repr__(self):
        return f"<CustomerContact(name='{self.name}', type='{self.contact_type}')>"

class CustomerDocument(BaseModel):
    """
    مستندات العميل
    """
    __tablename__ = "customer_documents"
    
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    document_type = Column(String(50), nullable=False)  # passport, visa, id, contract
    document_name = Column(String(100), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=True)
    mime_type = Column(String(100), nullable=True)
    
    issue_date = Column(DateTime, nullable=True)
    expiry_date = Column(DateTime, nullable=True)
    issuing_authority = Column(String(100), nullable=True)
    
    # العلاقات
    customer = relationship("Customer", back_populates="documents")
    
    def __repr__(self):
        return f"<CustomerDocument(type='{self.document_type}', name='{self.document_name}')>"

class CustomerNote(BaseModel):
    """
    ملاحظات العميل
    """
    __tablename__ = "customer_notes"
    
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    note_type = Column(String(20), default='general')  # general, important, warning
    title = Column(String(100), nullable=True)
    content = Column(Text, nullable=False)
    is_private = Column(Boolean, default=False)  # ملاحظة خاصة
    
    # العلاقات
    customer = relationship("Customer")
    
    def __repr__(self):
        return f"<CustomerNote(customer_id={self.customer_id}, type='{self.note_type}')>"