import React from 'react';
import './BookingActions.css';

// مكون أزرار العمليات للحجوزات
export const BookingActions = ({ 
  booking, 
  onView, 
  onEdit, 
  onDelete, 
  onPrint, 
  onSavePDF,
  showAll = true 
}) => {
  return (
    <div className="booking-actions">
      {showAll && (
        <>
          <button
            className="booking-action-button action-view"
            title="عرض التفاصيل"
            onClick={() => onView(booking)}
          >
            👁️
          </button>
          <button
            className="booking-action-button action-edit"
            title="تعديل"
            onClick={() => onEdit(booking)}
          >
            ✏️
          </button>
          <button
            className="booking-action-button action-delete"
            title="حذف"
            onClick={() => onDelete(booking)}
          >
            🗑️
          </button>
        </>
      )}
      <button
        className="booking-action-button action-print"
        title="طباعة"
        onClick={() => onPrint(booking)}
      >
        🖨️
      </button>
      <button
        className="booking-action-button action-pdf"
        title="حفظ PDF"
        onClick={() => onSavePDF(booking)}
      >
        📄
      </button>
    </div>
  );
};

// مكون أزرار العمليات المجمعة
export const BulkActions = ({ 
  selectedBookings, 
  onBulkPrint, 
  onBulkSavePDF, 
  onBulkDelete,
  onSelectAll,
  onClearSelection 
}) => {
  const hasSelection = selectedBookings.length > 0;

  return (
    <div className="bulk-actions-container">
      <div className="bulk-actions-info">
        <button
          className="booking-action-button"
          onClick={onSelectAll}
          style={{
            background: 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
            color: 'white'
          }}
        >
          ☑️ تحديد الكل
        </button>
        {hasSelection && (
          <button
            className="booking-action-button"
            onClick={onClearSelection}
            style={{
              background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
              color: 'white'
            }}
          >
            ❌ إلغاء التحديد
          </button>
        )}
      </div>
      
      {hasSelection && (
        <div className="bulk-actions-buttons">
          <span className="selection-count" style={{
            fontWeight: 'bold',
            color: '#2c3e50',
            marginLeft: '15px'
          }}>
            تم تحديد {selectedBookings.length} عنصر
          </span>
          <button
            className="booking-action-button action-print"
            onClick={() => onBulkPrint(selectedBookings)}
          >
            🖨️ طباعة المحدد
          </button>
          <button
            className="booking-action-button action-pdf"
            onClick={() => onBulkSavePDF(selectedBookings)}
          >
            📄 حفظ PDF
          </button>
          <button
            className="booking-action-button action-delete"
            onClick={() => onBulkDelete(selectedBookings)}
          >
            🗑️ حذف المحدد
          </button>
        </div>
      )}
    </div>
  );
};

// مكون نافذة عرض التفاصيل
export const BookingDetailsModal = ({ booking, isOpen, onClose, onPrint, onSavePDF }) => {
  if (!isOpen || !booking) return null;

  return (
    <div className="booking-modal-overlay" onClick={onClose}>
      <div className="booking-modal-content" onClick={e => e.stopPropagation()}>
        <div className="booking-modal-header">
          <h3>تفاصيل الحجز #{booking.id}</h3>
          <div className="booking-modal-actions">
            <button
              className="booking-action-button action-print"
              title="طباعة"
              onClick={() => onPrint(booking)}
            >
              🖨️
            </button>
            <button
              className="booking-action-button action-pdf"
              title="حفظ PDF"
              onClick={() => onSavePDF(booking)}
            >
              📄
            </button>
            <button
              className="booking-action-button"
              title="إغلاق"
              onClick={onClose}
              style={{
                background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
                color: 'white'
              }}
            >
              ✕
            </button>
          </div>
        </div>
        
        <div className="booking-modal-body">
          <div className="booking-details-grid">
            <div className="detail-item">
              <label>رقم الحجز:</label>
              <span>{booking.id}</span>
            </div>
            <div className="detail-item">
              <label>اسم العميل:</label>
              <span>{booking.customerName}</span>
            </div>
            <div className="detail-item">
              <label>تاريخ الحجز:</label>
              <span>{booking.date}</span>
            </div>
            <div className="detail-item">
              <label>الحالة:</label>
              <span className={`status-badge status-${booking.status}`}>
                {booking.status === 'confirmed' ? 'مؤكد' :
                 booking.status === 'pending' ? 'في الانتظار' :
                 booking.status === 'cancelled' ? 'ملغي' : booking.status}
              </span>
            </div>
            <div className="detail-item">
              <label>المبلغ:</label>
              <span>{booking.amount} ريال</span>
            </div>
            {booking.service && (
              <div className="detail-item">
                <label>الخدمة:</label>
                <span>{booking.service}</span>
              </div>
            )}
            {booking.destination && (
              <div className="detail-item">
                <label>الوجهة:</label>
                <span>{booking.destination}</span>
              </div>
            )}
            {booking.notes && (
              <div className="detail-item full-width">
                <label>ملاحظات:</label>
                <span>{booking.notes}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="booking-modal-footer">
          <button
            className="booking-action-button action-print"
            onClick={() => onPrint(booking)}
            style={{ marginLeft: '10px' }}
          >
            🖨️ طباعة
          </button>
          <button
            className="booking-action-button action-pdf"
            onClick={() => onSavePDF(booking)}
            style={{ marginLeft: '10px' }}
          >
            📄 حفظ PDF
          </button>
          <button
            className="booking-action-button"
            onClick={onClose}
            style={{
              background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
              color: 'white'
            }}
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default BookingActions;