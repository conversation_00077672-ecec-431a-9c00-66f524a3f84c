import React, { useState, useEffect, useRef } from 'react';
import { CurrencyManager, CURRENCIES } from '../Finance/CurrencyManager';
import './ModernTemplatesSystem.css';

const ModernTemplatesSystem = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [viewMode, setViewMode] = useState('grid'); // grid, list, cards
  const [sortBy, setSortBy] = useState('recent');
  const [filterStatus, setFilterStatus] = useState('all');
  const editorRef = useRef(null);

  const [newTemplate, setNewTemplate] = useState({
    name: '',
    category: 'invoice',
    type: 'document', // document, email, report, voucher
    description: '',
    content: '',
    variables: [],
    tags: [],
    isActive: true,
    language: 'ar',
    thumbnail: '',
    settings: {
      pageSize: 'A4',
      orientation: 'portrait',
      margins: 'normal',
      fontSize: 'medium',
      colorScheme: 'blue'
    }
  });

  const [templateStats, setTemplateStats] = useState({
    total: 0,
    active: 0,
    documents: 0,
    emails: 0,
    reports: 0,
    vouchers: 0,
    recent: 0
  });

  // فئات القوالب المتطورة
  const templateCategories = {
    all: { name: 'جميع القوالب', icon: '📄', color: '#3498db' },
    invoice: { name: 'الفواتير', icon: '🧾', color: '#e74c3c' },
    contract: { name: 'العقود', icon: '📋', color: '#f39c12' },
    email: { name: 'الإيميلات', icon: '📧', color: '#9b59b6' },
    report: { name: 'التقارير', icon: '📊', color: '#1abc9c' },
    voucher: { name: 'السندات', icon: '💰', color: '#27ae60' },
    certificate: { name: 'الشهادات', icon: '🏆', color: '#e67e22' },
    letter: { name: 'الرسائل', icon: '✉️', color: '#34495e' },
    receipt: { name: 'الإيصالات', icon: '🧾', color: '#16a085' }
  };

  // أنواع القوالب
  const templateTypes = {
    document: { name: 'مستند', icon: '📄' },
    email: { name: 'إيميل', icon: '📧' },
    report: { name: 'تقرير', icon: '📊' },
    voucher: { name: 'سند', icon: '💰' }
  };

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    setLoading(true);
    
    // محاكاة تحميل البيانات مع قوالب السندات الجديدة
    setTimeout(() => {
      const mockTemplates = [
        // قوالب السندات الجديدة المذهلة
        {
          id: 'voucher_receipt_001',
          name: 'سند قبض فاخر',
          category: 'voucher',
          type: 'voucher',
          description: 'سند قبض بتصميم فاخر ومذهل مع دعم العملات المتعددة',
          content: generateReceiptVoucherTemplate(),
          variables: ['voucher_number', 'date', 'customer_name', 'customer_phone', 'amount', 'currency', 'amount_words', 'payment_method', 'description', 'reference', 'created_by', 'company_name', 'company_address', 'company_phone'],
          tags: ['سند', 'قبض', 'مالية', 'عملات', 'فاخر'],
          isActive: true,
          language: 'ar',
          thumbnail: '💰',
          settings: {
            pageSize: 'A4',
            orientation: 'portrait',
            margins: 'normal',
            fontSize: 'medium',
            colorScheme: 'blue'
          },
          createdDate: '2024-12-19',
          lastUsed: '2024-12-19',
          usageCount: 15,
          rating: 5.0,
          downloads: 45
        },
        {
          id: 'voucher_payment_001',
          name: 'سند صرف احترافي',
          category: 'voucher',
          type: 'voucher',
          description: 'سند صرف بتصميم احترافي مع نظام الاعتماد والعملات المتعددة',
          content: generatePaymentVoucherTemplate(),
          variables: ['voucher_number', 'date', 'beneficiary_name', 'beneficiary_phone', 'amount', 'currency', 'amount_words', 'payment_method', 'description', 'reference', 'approved_by', 'created_by', 'company_name', 'company_address', 'company_phone'],
          tags: ['سند', 'صرف', 'مالية', 'عملات', 'احترافي'],
          isActive: true,
          language: 'ar',
          thumbnail: '💸',
          settings: {
            pageSize: 'A4',
            orientation: 'portrait',
            margins: 'normal',
            fontSize: 'medium',
            colorScheme: 'red'
          },
          createdDate: '2024-12-19',
          lastUsed: '2024-12-19',
          usageCount: 12,
          rating: 4.9,
          downloads: 38
        },
        {
          id: 'invoice_flight_001',
          name: 'فاتورة حجز طيران متطورة',
          category: 'invoice',
          type: 'document',
          description: 'فاتورة حجز طيران بتصميم عصري مع QR Code وتفاصيل شاملة',
          content: generateFlightInvoiceTemplate(),
          variables: ['invoice_number', 'date', 'customer_name', 'phone', 'destination', 'departure_date', 'return_date', 'amount', 'currency', 'flight_details', 'booking_reference'],
          tags: ['فاتورة', 'طيران', 'حجز', 'سفر'],
          isActive: true,
          language: 'ar',
          thumbnail: '✈️',
          settings: {
            pageSize: 'A4',
            orientation: 'portrait',
            margins: 'normal',
            fontSize: 'medium',
            colorScheme: 'blue'
          },
          createdDate: '2024-12-18',
          lastUsed: '2024-12-19',
          usageCount: 28,
          rating: 4.8,
          downloads: 67
        },
        {
          id: 'contract_tourism_001',
          name: 'عقد خدمات سياحية شامل',
          category: 'contract',
          type: 'document',
          description: 'عقد شامل للخدمات السياحية مع شروط وأحكام متطورة',
          content: generateTourismContractTemplate(),
          variables: ['contract_number', 'date', 'customer_name', 'id_number', 'service_details', 'amount', 'currency', 'terms', 'duration'],
          tags: ['عقد', 'سياحة', 'خدمات', 'شروط'],
          isActive: true,
          language: 'ar',
          thumbnail: '📋',
          settings: {
            pageSize: 'A4',
            orientation: 'portrait',
            margins: 'wide',
            fontSize: 'medium',
            colorScheme: 'green'
          },
          createdDate: '2024-12-17',
          lastUsed: '2024-12-18',
          usageCount: 19,
          rating: 4.7,
          downloads: 42
        },
        {
          id: 'email_booking_001',
          name: 'إيميل تأكيد حجز تفاعلي',
          category: 'email',
          type: 'email',
          description: 'رسالة إيميل تفاعلية لتأكيد الحجز مع تصميم responsive',
          content: generateBookingEmailTemplate(),
          variables: ['customer_name', 'booking_number', 'destination', 'travel_date', 'passengers', 'total_amount', 'currency', 'contact_info'],
          tags: ['إيميل', 'تأكيد', 'حجز', 'تفاعلي'],
          isActive: true,
          language: 'ar',
          thumbnail: '📧',
          settings: {
            pageSize: 'Email',
            orientation: 'portrait',
            margins: 'narrow',
            fontSize: 'medium',
            colorScheme: 'purple'
          },
          createdDate: '2024-12-16',
          lastUsed: '2024-12-19',
          usageCount: 56,
          rating: 4.9,
          downloads: 89
        },
        {
          id: 'report_sales_001',
          name: 'تقرير مبيعات تحليلي',
          category: 'report',
          type: 'report',
          description: 'تقرير مبيعات شامل مع رسوم بيانية وتحليلات متقدمة',
          content: generateSalesReportTemplate(),
          variables: ['month', 'year', 'total_sales', 'currency', 'bookings_count', 'top_destinations', 'growth_rate', 'notes'],
          tags: ['تقرير', 'مبيعات', 'تحليل', 'إحصائيات'],
          isActive: true,
          language: 'ar',
          thumbnail: '📊',
          settings: {
            pageSize: 'A4',
            orientation: 'landscape',
            margins: 'normal',
            fontSize: 'small',
            colorScheme: 'teal'
          },
          createdDate: '2024-12-15',
          lastUsed: '2024-12-18',
          usageCount: 23,
          rating: 4.6,
          downloads: 34
        }
      ];

      setTemplates(mockTemplates);
      
      // حساب الإحصائيات
      const stats = {
        total: mockTemplates.length,
        active: mockTemplates.filter(t => t.isActive).length,
        documents: mockTemplates.filter(t => t.type === 'document').length,
        emails: mockTemplates.filter(t => t.type === 'email').length,
        reports: mockTemplates.filter(t => t.type === 'report').length,
        vouchers: mockTemplates.filter(t => t.type === 'voucher').length,
        recent: mockTemplates.filter(t => {
          const created = new Date(t.createdDate);
          const week = new Date();
          week.setDate(week.getDate() - 7);
          return created >= week;
        }).length
      };
      
      setTemplateStats(stats);
      setLoading(false);
    }, 1500);
  };

  // دالة إنشاء قالب سند القبض
  const generateReceiptVoucherTemplate = () => {
    return `
      <div class="voucher-template receipt-voucher">
        <div class="voucher-header">
          <div class="company-section">
            <div class="company-logo">
              <div class="logo">✈️</div>
              <div class="company-info">
                <h1>{{company_name}}</h1>
                <p class="company-details">{{company_address}}</p>
                <p class="company-details">{{company_phone}}</p>
              </div>
            </div>
          </div>
          <div class="voucher-type-section">
            <h2 class="voucher-type">💰 سند قبض</h2>
            <div class="voucher-number">{{voucher_number}}</div>
          </div>
        </div>
        
        <div class="voucher-body">
          <div class="voucher-info">
            <div class="info-section">
              <h3>📅 معلومات التاريخ</h3>
              <p><strong>التاريخ:</strong> {{date}}</p>
              <p><strong>المرجع:</strong> {{reference}}</p>
            </div>
            <div class="info-section">
              <h3>👤 معلومات العميل</h3>
              <p><strong>الاسم:</strong> {{customer_name}}</p>
              <p><strong>الهاتف:</strong> {{customer_phone}}</p>
              <p><strong>طريقة الدفع:</strong> {{payment_method}}</p>
            </div>
          </div>
          
          <div class="amount-section">
            <h3>المبلغ المستلم</h3>
            <div class="amount-value">{{amount}} {{currency}}</div>
            <div class="amount-words">{{amount_words}}</div>
          </div>
          
          <div class="description-section">
            <h3>📝 البيان</h3>
            <p>{{description}}</p>
          </div>
        </div>
        
        <div class="voucher-footer">
          <div class="signatures">
            <div class="signature-box">
              <p>توقيع المستلم</p>
              <div class="signature-line"></div>
            </div>
            <div class="signature-box">
              <p>توقيع المحاسب</p>
              <div class="signature-line"></div>
              <small>{{created_by}}</small>
            </div>
          </div>
        </div>
      </div>
    `;
  };

  // دالة إنشاء قالب سند الصرف
  const generatePaymentVoucherTemplate = () => {
    return `
      <div class="voucher-template payment-voucher">
        <div class="voucher-header">
          <div class="company-section">
            <div class="company-logo">
              <div class="logo">✈️</div>
              <div class="company-info">
                <h1>{{company_name}}</h1>
                <p class="company-details">{{company_address}}</p>
                <p class="company-details">{{company_phone}}</p>
              </div>
            </div>
          </div>
          <div class="voucher-type-section payment">
            <h2 class="voucher-type">💸 سند صرف</h2>
            <div class="voucher-number">{{voucher_number}}</div>
          </div>
        </div>
        
        <div class="voucher-body">
          <div class="voucher-info">
            <div class="info-section">
              <h3>📅 معلومات التاريخ</h3>
              <p><strong>التاريخ:</strong> {{date}}</p>
              <p><strong>المرجع:</strong> {{reference}}</p>
            </div>
            <div class="info-section">
              <h3>🏢 معلومات المستفيد</h3>
              <p><strong>الاسم:</strong> {{beneficiary_name}}</p>
              <p><strong>الهاتف:</strong> {{beneficiary_phone}}</p>
              <p><strong>طريقة الدفع:</strong> {{payment_method}}</p>
            </div>
          </div>
          
          <div class="amount-section payment">
            <h3>المبلغ المدفوع</h3>
            <div class="amount-value">{{amount}} {{currency}}</div>
            <div class="amount-words">{{amount_words}}</div>
          </div>
          
          <div class="description-section">
            <h3>📝 البيان</h3>
            <p>{{description}}</p>
          </div>
        </div>
        
        <div class="voucher-footer">
          <div class="signatures">
            <div class="signature-box">
              <p>توقيع المستفيد</p>
              <div class="signature-line"></div>
            </div>
            <div class="signature-box">
              <p>توقيع المحاسب</p>
              <div class="signature-line"></div>
              <small>{{created_by}}</small>
            </div>
            <div class="signature-box">
              <p>توقيع المعتمد</p>
              <div class="signature-line"></div>
              <small>{{approved_by}}</small>
            </div>
          </div>
        </div>
      </div>
    `;
  };

  // دالة إنشاء قالب فاتورة الطيران
  const generateFlightInvoiceTemplate = () => {
    return `
      <div class="invoice-template modern">
        <div class="invoice-header">
          <div class="company-branding">
            <h1>✈️ {{company_name}}</h1>
            <p>وكالة السفر والسياحة</p>
          </div>
          <div class="invoice-details">
            <h2>فاتورة حجز طيران</h2>
            <p><strong>رقم الفاتورة:</strong> {{invoice_number}}</p>
            <p><strong>التاريخ:</strong> {{date}}</p>
          </div>
        </div>
        
        <div class="customer-info">
          <h3>معلومات العميل</h3>
          <p><strong>الاسم:</strong> {{customer_name}}</p>
          <p><strong>الهاتف:</strong> {{phone}}</p>
          <p><strong>مرجع الحجز:</strong> {{booking_reference}}</p>
        </div>
        
        <div class="flight-details">
          <h3>تفاصيل الرحلة</h3>
          <table>
            <tr>
              <td><strong>الوجهة:</strong></td>
              <td>{{destination}}</td>
            </tr>
            <tr>
              <td><strong>تاريخ المغادرة:</strong></td>
              <td>{{departure_date}}</td>
            </tr>
            <tr>
              <td><strong>تاريخ العودة:</strong></td>
              <td>{{return_date}}</td>
            </tr>
          </table>
        </div>
        
        <div class="amount-summary">
          <h3>ملخص المبلغ</h3>
          <div class="total-amount">{{amount}} {{currency}}</div>
        </div>
        
        <div class="qr-section">
          <div class="qr-code">[QR Code]</div>
          <p>امسح للتحقق من الفاتورة</p>
        </div>
      </div>
    `;
  };

  // دالة إنشاء قالب عقد السياحة
  const generateTourismContractTemplate = () => {
    return `
      <div class="contract-template">
        <div class="contract-header">
          <h1>عقد خدمات سياحية</h1>
          <p><strong>رقم العقد:</strong> {{contract_number}}</p>
          <p><strong>التاريخ:</strong> {{date}}</p>
        </div>
        
        <div class="parties">
          <div class="party">
            <h3>الطرف الأول (مقدم الخدمة)</h3>
            <p>{{company_name}}</p>
          </div>
          <div class="party">
            <h3>الطرف الثاني (العميل)</h3>
            <p><strong>الاسم:</strong> {{customer_name}}</p>
            <p><strong>رقم الهوية:</strong> {{id_number}}</p>
          </div>
        </div>
        
        <div class="service-details">
          <h3>تفاصيل الخدمة</h3>
          <p>{{service_details}}</p>
          <p><strong>المدة:</strong> {{duration}}</p>
          <p><strong>المبلغ:</strong> {{amount}} {{currency}}</p>
        </div>
        
        <div class="terms">
          <h3>الشروط والأحكام</h3>
          <div>{{terms}}</div>
        </div>
        
        <div class="signatures">
          <div class="signature">
            <p>توقيع العميل</p>
            <div class="signature-line"></div>
          </div>
          <div class="signature">
            <p>توقيع مقدم الخدمة</p>
            <div class="signature-line"></div>
          </div>
        </div>
      </div>
    `;
  };

  // دالة إنشاء قالب إيميل الحجز
  const generateBookingEmailTemplate = () => {
    return `
      <div class="email-template modern">
        <div class="email-header">
          <h1>✈️ تأكيد الحجز</h1>
        </div>
        
        <div class="email-body">
          <p>عزيزي/عزيزتي {{customer_name}}،</p>
          <p>نشكركم لاختياركم خدماتنا السياحية.</p>
          
          <div class="booking-details">
            <h3>تفاصيل الحجز</h3>
            <p><strong>رقم الحجز:</strong> {{booking_number}}</p>
            <p><strong>الوجهة:</strong> {{destination}}</p>
            <p><strong>تاريخ السفر:</strong> {{travel_date}}</p>
            <p><strong>عدد المسافرين:</strong> {{passengers}}</p>
            <p><strong>المبلغ الإجمالي:</strong> {{total_amount}} {{currency}}</p>
          </div>
          
          <div class="contact-info">
            <p>للاستفسار: {{contact_info}}</p>
          </div>
          
          <p>مع أطيب التحيات،<br>فريق العمل</p>
        </div>
        
        <div class="email-footer">
          <p>هذه رسالة تلقائية، يرجى عدم الرد عليها</p>
        </div>
      </div>
    `;
  };

  // دالة إنشاء قالب تقرير المبيعات
  const generateSalesReportTemplate = () => {
    return `
      <div class="report-template">
        <div class="report-header">
          <h1>📊 تقرير المبيعات</h1>
          <p>{{month}} {{year}}</p>
        </div>
        
        <div class="report-summary">
          <div class="metric">
            <h3>إجمالي المبيعات</h3>
            <div class="value">{{total_sales}} {{currency}}</div>
            <div class="growth">نمو: {{growth_rate}}%</div>
          </div>
          <div class="metric">
            <h3>عدد الحجوزات</h3>
            <div class="value">{{bookings_count}}</div>
          </div>
        </div>
        
        <div class="top-destinations">
          <h3>أفضل الوجهات</h3>
          <div>{{top_destinations}}</div>
        </div>
        
        <div class="notes">
          <h3>ملاحظات</h3>
          <p>{{notes}}</p>
        </div>
        
        <div class="report-footer">
          <p>تم إنشاء التقرير تلقائياً</p>
        </div>
      </div>
    `;
  };

  // فلترة القوالب
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = activeTab === 'all' || template.category === activeTab;
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && template.isActive) ||
                         (filterStatus === 'inactive' && !template.isActive);
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // ترتيب القوالب
  const sortedTemplates = [...filteredTemplates].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'recent':
        return new Date(b.createdDate) - new Date(a.createdDate);
      case 'popular':
        return b.usageCount - a.usageCount;
      case 'rating':
        return (b.rating || 0) - (a.rating || 0);
      default:
        return 0;
    }
  });

  const handleCreateTemplate = () => {
    setShowCreateModal(true);
  };

  const handlePreviewTemplate = (template) => {
    setSelectedTemplate(template);
    setShowPreviewModal(true);
  };

  const handleUseTemplate = (template) => {
    // تطبيق القالب
    console.log('Using template:', template.name);
    // يمكن إضافة منطق تطبيق القالب هنا
  };

  const handleDownloadTemplate = (template) => {
    // تحميل القالب
    console.log('Downloading template:', template.name);
    // يمكن إضافة منطق تحميل القالب هنا
  };

  if (loading) {
    return (
      <div className="templates-loading">
        <div className="loading-spinner"></div>
        <h3>جاري تحميل القوالب المذهلة...</h3>
        <p>انتظر قليلاً بينما نحضر لك أروع القوالب</p>
      </div>
    );
  }

  return (
    <div className="modern-templates-system">
      {/* Header */}
      <div className="templates-header">
        <div className="header-content">
          <div className="title-section">
            <h1>🎨 نظام القوالب المتطور</h1>
            <p>مجموعة شاملة من القوالب الاحترافية والمذهلة</p>
          </div>
          <div className="header-actions">
            <button className="create-btn" onClick={handleCreateTemplate}>
              ✨ إنشاء قالب جديد
            </button>
          </div>
        </div>
      </div>

      {/* Statistics Dashboard */}
      <div className="templates-stats">
        <div className="stat-card">
          <div className="stat-icon">📄</div>
          <div className="stat-info">
            <div className="stat-value">{templateStats.total}</div>
            <div className="stat-label">إجمالي القوالب</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <div className="stat-value">{templateStats.active}</div>
            <div className="stat-label">القوالب النشطة</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{templateStats.vouchers}</div>
            <div className="stat-label">قوالب السندات</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">🆕</div>
          <div className="stat-info">
            <div className="stat-value">{templateStats.recent}</div>
            <div className="stat-label">قوالب جديدة</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="templates-controls">
        <div className="search-section">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث في القوالب..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="filter-section">
          <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
            <option value="recent">الأحدث</option>
            <option value="name">الاسم</option>
            <option value="popular">الأكثر استخداماً</option>
            <option value="rating">الأعلى تقييماً</option>
          </select>

          <select value={filterStatus} onChange={(e) => setFilterStatus(e.target.value)}>
            <option value="all">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>

          <div className="view-mode">
            <button 
              className={viewMode === 'grid' ? 'active' : ''} 
              onClick={() => setViewMode('grid')}
            >
              ⊞
            </button>
            <button 
              className={viewMode === 'list' ? 'active' : ''} 
              onClick={() => setViewMode('list')}
            >
              ☰
            </button>
          </div>
        </div>
      </div>

      {/* Categories Tabs */}
      <div className="categories-tabs">
        {Object.entries(templateCategories).map(([key, category]) => (
          <button
            key={key}
            className={`category-tab ${activeTab === key ? 'active' : ''}`}
            onClick={() => setActiveTab(key)}
            style={{ '--category-color': category.color }}
          >
            <span className="category-icon">{category.icon}</span>
            <span className="category-name">{category.name}</span>
            <span className="category-count">
              {key === 'all' ? templates.length : templates.filter(t => t.category === key).length}
            </span>
          </button>
        ))}
      </div>

      {/* Templates Grid/List */}
      <div className={`templates-container ${viewMode}`}>
        {sortedTemplates.length === 0 ? (
          <div className="no-templates">
            <div className="no-templates-icon">📄</div>
            <h3>لا توجد قوالب</h3>
            <p>لم يتم العثور على قوالب تطابق معايير البحث</p>
            <button className="create-first-btn" onClick={handleCreateTemplate}>
              إنشاء أول قالب
            </button>
          </div>
        ) : (
          sortedTemplates.map(template => (
            <div key={template.id} className="template-card">
              <div className="template-thumbnail">
                <div className="thumbnail-icon">{template.thumbnail}</div>
                <div className="template-type">{templateTypes[template.type]?.icon}</div>
                {!template.isActive && <div className="inactive-badge">غير نشط</div>}
              </div>
              
              <div className="template-content">
                <div className="template-header">
                  <h3 className="template-name">{template.name}</h3>
                  <div className="template-category" style={{ color: templateCategories[template.category]?.color }}>
                    {templateCategories[template.category]?.icon} {templateCategories[template.category]?.name}
                  </div>
                </div>
                
                <p className="template-description">{template.description}</p>
                
                <div className="template-tags">
                  {template.tags.slice(0, 3).map((tag, index) => (
                    <span key={index} className="tag">{tag}</span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="tag more">+{template.tags.length - 3}</span>
                  )}
                </div>
                
                <div className="template-meta">
                  <div className="meta-item">
                    <span className="meta-icon">📅</span>
                    <span>{template.createdDate}</span>
                  </div>
                  <div className="meta-item">
                    <span className="meta-icon">📊</span>
                    <span>{template.usageCount} استخدام</span>
                  </div>
                  {template.rating && (
                    <div className="meta-item">
                      <span className="meta-icon">⭐</span>
                      <span>{template.rating}</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="template-actions">
                <button 
                  className="action-btn preview-btn"
                  onClick={() => handlePreviewTemplate(template)}
                  title="معاينة"
                >
                  👁️
                </button>
                <button 
                  className="action-btn use-btn"
                  onClick={() => handleUseTemplate(template)}
                  title="استخدام"
                >
                  ✨
                </button>
                <button 
                  className="action-btn download-btn"
                  onClick={() => handleDownloadTemplate(template)}
                  title="تحميل"
                >
                  📥
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create Template Modal */}
      {showCreateModal && (
        <div className="modal-overlay" onClick={() => setShowCreateModal(false)}>
          <div className="modal-content create-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>✨ إنشاء قالب جديد</h2>
              <button className="close-btn" onClick={() => setShowCreateModal(false)}>✕</button>
            </div>
            <div className="modal-body">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم القالب</label>
                  <input
                    type="text"
                    value={newTemplate.name}
                    onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                    placeholder="أدخل اسم القالب"
                  />
                </div>
                <div className="form-group">
                  <label>الفئة</label>
                  <select
                    value={newTemplate.category}
                    onChange={(e) => setNewTemplate({...newTemplate, category: e.target.value})}
                  >
                    {Object.entries(templateCategories).filter(([key]) => key !== 'all').map(([key, category]) => (
                      <option key={key} value={key}>{category.icon} {category.name}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>النوع</label>
                  <select
                    value={newTemplate.type}
                    onChange={(e) => setNewTemplate({...newTemplate, type: e.target.value})}
                  >
                    {Object.entries(templateTypes).map(([key, type]) => (
                      <option key={key} value={key}>{type.icon} {type.name}</option>
                    ))}
                  </select>
                </div>
                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={newTemplate.description}
                    onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
                    placeholder="وصف مختصر للقالب"
                    rows="3"
                  />
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button className="cancel-btn" onClick={() => setShowCreateModal(false)}>
                إلغاء
              </button>
              <button className="save-btn">
                💾 حفظ القالب
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreviewModal && selectedTemplate && (
        <div className="modal-overlay" onClick={() => setShowPreviewModal(false)}>
          <div className="modal-content preview-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>👁️ معاينة القالب: {selectedTemplate.name}</h2>
              <button className="close-btn" onClick={() => setShowPreviewModal(false)}>✕</button>
            </div>
            <div className="modal-body">
              <div className="preview-content">
                <div 
                  className="template-preview"
                  dangerouslySetInnerHTML={{ __html: selectedTemplate.content }}
                />
              </div>
            </div>
            <div className="modal-footer">
              <button className="use-template-btn" onClick={() => handleUseTemplate(selectedTemplate)}>
                ✨ استخدام هذا القالب
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModernTemplatesSystem;