import React, { useState, useEffect } from 'react';
import './ModernLayout.css';

// Modern Header Component
export const ModernHeader = ({ 
  children, 
  sticky = true, 
  glass = false,
  className = '',
  ...props 
}) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    if (sticky) {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [sticky]);

  const headerClasses = [
    'modern-header',
    sticky && 'modern-header--sticky',
    glass && 'modern-header--glass',
    isScrolled && 'modern-header--scrolled',
    className
  ].filter(Boolean).join(' ');

  return (
    <header className={headerClasses} {...props}>
      <div className="modern-header__container">
        {children}
      </div>
    </header>
  );
};

// Modern Sidebar Component
export const ModernSidebar = ({ 
  children, 
  collapsed = false,
  position = 'right',
  overlay = false,
  className = '',
  onToggle,
  ...props 
}) => {
  const sidebarClasses = [
    'modern-sidebar',
    `modern-sidebar--${position}`,
    collapsed && 'modern-sidebar--collapsed',
    overlay && 'modern-sidebar--overlay',
    className
  ].filter(Boolean).join(' ');

  return (
    <>
      {overlay && !collapsed && (
        <div 
          className="modern-sidebar__overlay"
          onClick={onToggle}
        />
      )}
      <aside className={sidebarClasses} {...props}>
        <div className="modern-sidebar__content">
          {children}
        </div>
      </aside>
    </>
  );
};

// Modern Main Content Component
export const ModernMain = ({ 
  children, 
  padding = true,
  className = '',
  ...props 
}) => {
  const mainClasses = [
    'modern-main',
    padding && 'modern-main--padded',
    className
  ].filter(Boolean).join(' ');

  return (
    <main className={mainClasses} {...props}>
      {children}
    </main>
  );
};

// Modern Footer Component
export const ModernFooter = ({ 
  children, 
  sticky = false,
  className = '',
  ...props 
}) => {
  const footerClasses = [
    'modern-footer',
    sticky && 'modern-footer--sticky',
    className
  ].filter(Boolean).join(' ');

  return (
    <footer className={footerClasses} {...props}>
      <div className="modern-footer__container">
        {children}
      </div>
    </footer>
  );
};

// Modern Container Component
export const ModernContainer = ({ 
  children, 
  size = 'default',
  centered = false,
  className = '',
  ...props 
}) => {
  const containerClasses = [
    'modern-container',
    `modern-container--${size}`,
    centered && 'modern-container--centered',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} {...props}>
      {children}
    </div>
  );
};

// Modern Grid Component
export const ModernGrid = ({ 
  children, 
  cols = 1,
  gap = 'md',
  responsive = true,
  className = '',
  ...props 
}) => {
  const gridClasses = [
    'modern-grid',
    `modern-grid--cols-${cols}`,
    `modern-grid--gap-${gap}`,
    responsive && 'modern-grid--responsive',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={gridClasses} {...props}>
      {children}
    </div>
  );
};

// Modern Flex Component
export const ModernFlex = ({ 
  children, 
  direction = 'row',
  align = 'stretch',
  justify = 'start',
  wrap = false,
  gap = 'md',
  className = '',
  ...props 
}) => {
  const flexClasses = [
    'modern-flex',
    `modern-flex--${direction}`,
    `modern-flex--align-${align}`,
    `modern-flex--justify-${justify}`,
    `modern-flex--gap-${gap}`,
    wrap && 'modern-flex--wrap',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={flexClasses} {...props}>
      {children}
    </div>
  );
};

// Modern Section Component
export const ModernSection = ({ 
  children, 
  background = 'default',
  padding = 'lg',
  className = '',
  ...props 
}) => {
  const sectionClasses = [
    'modern-section',
    `modern-section--bg-${background}`,
    `modern-section--padding-${padding}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <section className={sectionClasses} {...props}>
      {children}
    </section>
  );
};

// Modern Page Layout Component
export const ModernPageLayout = ({ 
  children,
  header,
  sidebar,
  footer,
  sidebarCollapsed = false,
  sidebarPosition = 'right',
  className = '',
  ...props 
}) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(sidebarCollapsed);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const layoutClasses = [
    'modern-page-layout',
    sidebar && 'modern-page-layout--with-sidebar',
    isSidebarCollapsed && 'modern-page-layout--sidebar-collapsed',
    `modern-page-layout--sidebar-${sidebarPosition}`,
    className
  ].filter(Boolean).join(' ');

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className={layoutClasses} {...props}>
      {header && (
        <ModernHeader>
          {React.cloneElement(header, { 
            onToggleSidebar: toggleSidebar,
            sidebarCollapsed: isSidebarCollapsed 
          })}
        </ModernHeader>
      )}
      
      <div className="modern-page-layout__body">
        {sidebar && (
          <ModernSidebar
            collapsed={isMobile ? false : isSidebarCollapsed}
            position={sidebarPosition}
            overlay={isMobile}
            onToggle={toggleSidebar}
          >
            {sidebar}
          </ModernSidebar>
        )}
        
        <ModernMain>
          {children}
        </ModernMain>
      </div>
      
      {footer && (
        <ModernFooter>
          {footer}
        </ModernFooter>
      )}
    </div>
  );
};

// Modern Card Layout Component
export const ModernCardLayout = ({ 
  children,
  cols = 3,
  gap = 'lg',
  className = '',
  ...props 
}) => (
  <ModernGrid 
    cols={cols} 
    gap={gap} 
    responsive 
    className={`modern-card-layout ${className}`}
    {...props}
  >
    {children}
  </ModernGrid>
);

// Modern Dashboard Layout Component
export const ModernDashboardLayout = ({ 
  children,
  widgets = [],
  className = '',
  ...props 
}) => (
  <div className={`modern-dashboard-layout ${className}`} {...props}>
    {widgets.length > 0 && (
      <div className="modern-dashboard-layout__widgets">
        <ModernGrid cols={4} gap="lg" responsive>
          {widgets}
        </ModernGrid>
      </div>
    )}
    
    <div className="modern-dashboard-layout__content">
      {children}
    </div>
  </div>
);

// Modern Split Layout Component
export const ModernSplitLayout = ({ 
  left,
  right,
  ratio = '1:1',
  direction = 'horizontal',
  resizable = false,
  className = '',
  ...props 
}) => {
  const [leftSize, rightSize] = ratio.split(':').map(Number);
  const totalSize = leftSize + rightSize;
  const leftPercent = (leftSize / totalSize) * 100;
  const rightPercent = (rightSize / totalSize) * 100;

  const splitClasses = [
    'modern-split-layout',
    `modern-split-layout--${direction}`,
    resizable && 'modern-split-layout--resizable',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={splitClasses} {...props}>
      <div 
        className="modern-split-layout__left"
        style={{ 
          [direction === 'horizontal' ? 'width' : 'height']: `${leftPercent}%` 
        }}
      >
        {left}
      </div>
      
      {resizable && (
        <div className="modern-split-layout__divider" />
      )}
      
      <div 
        className="modern-split-layout__right"
        style={{ 
          [direction === 'horizontal' ? 'width' : 'height']: `${rightPercent}%` 
        }}
      >
        {right}
      </div>
    </div>
  );
};

export default ModernPageLayout;