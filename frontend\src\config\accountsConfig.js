// إعدادات نظام الحسابات
export const accountsConfig = {
  // إعدادات عامة
  general: {
    companyName: 'شركة شراء السفر والسياحة',
    fiscalYearStart: '01-01', // بداية السنة المالية
    currency: 'SAR',
    locale: 'ar-SA',
    dateFormat: 'YYYY-MM-DD',
    decimalPlaces: 2
  },

  // إعدادات دليل الحسابات
  chartOfAccounts: {
    // هيكل ترقيم الحسابات
    accountNumbering: {
      assets: '1000-1999',           // الأصول
      liabilities: '2000-2999',      // الخصوم
      equity: '3000-3999',           // حقوق الملكية
      revenue: '4000-4999',          // الإيرادات
      expenses: '5000-5999',         // المصروفات
      otherIncome: '6000-6999',      // إيرادات أخرى
      otherExpenses: '7000-7999'     // مصروفات أخرى
    },

    // الحسابات الافتراضية
    defaultAccounts: {
      cash: '1111',                  // الصندوق
      bank: '1112',                  // البنك
      accountsReceivable: '1120',    // العملاء
      inventory: '1130',             // المخزون
      accountsPayable: '2110',       // الموردون
      vatPayable: '2130',            // ضريبة القيمة المضافة
      capital: '3100',               // رأس المال
      retainedEarnings: '3200',      // الأرباح المحتجزة
      salesRevenue: '4100',          // إيرادات المبيعات
      costOfSales: '5100',           // تكلفة المبيعات
      operatingExpenses: '5200'      // المصروفات التشغيلية
    }
  },

  // إعدادات قيود اليومية
  journalEntries: {
    // أنواع القيود
    entryTypes: {
      general: 'قيد عام',
      sales: 'قيد مبيعات',
      purchase: 'قيد مشتريات',
      payment: 'قيد دفع',
      receipt: 'قيد قبض',
      adjustment: 'قيد تسوية',
      closing: 'قيد إقفال'
    },

    // حالات القيود
    statuses: {
      draft: 'مسودة',
      posted: 'مرحل',
      approved: 'معتمد',
      cancelled: 'ملغي'
    },

    // إعدادات الترقيم
    numbering: {
      prefix: 'JE',
      startNumber: 1,
      digits: 6,
      resetAnnually: true
    }
  },

  // إعدادات التقارير المالية
  financialReports: {
    // فترات التقارير
    periods: {
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      semiAnnual: 'نصف سنوي',
      annual: 'سنوي'
    },

    // أنواع التقارير
    reportTypes: {
      incomeStatement: 'قائمة الدخل',
      balanceSheet: 'الميزانية العمومية',
      cashFlow: 'قائمة التدفقات النقدية',
      trialBalance: 'ميزان المراجعة',
      generalLedger: 'دفتر الأستاذ العام',
      accountStatement: 'كشف حساب'
    }
  },

  // إعدادات الضرائب
  taxation: {
    // ضريبة القيمة المضافة
    vat: {
      rate: 0.15,                    // 15%
      applicableFrom: '2018-01-01',
      exemptCategories: [
        'basic_food_items',
        'medical_services',
        'education_services'
      ]
    },

    // الزكاة
    zakat: {
      rate: 0.025,                   // 2.5%
      nisab: 85000,                  // النصاب بالريال السعودي
      applicableAssets: [
        'cash',
        'bank_deposits',
        'trade_receivables',
        'inventory'
      ]
    }
  },

  // إعدادات العملات
  currencies: {
    base: 'SAR',
    supported: [
      { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
      { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
      { code: 'EUR', name: 'يورو', symbol: '€' },
      { code: 'GBP', name: 'جنيه إسترليني', symbol: '£' },
      { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' }
    ]
  },

  // إعدادات الأمان
  security: {
    // مستويات الصلاحيات
    permissions: {
      admin: {
        name: 'مدير النظام',
        permissions: ['*'] // جميع الصلاحيات
      },
      accountant: {
        name: 'محاسب',
        permissions: [
          'view_accounts',
          'create_entries',
          'edit_entries',
          'post_entries',
          'view_reports',
          'export_reports'
        ]
      },
      bookkeeper: {
        name: 'مسك دفاتر',
        permissions: [
          'view_accounts',
          'create_entries',
          'edit_draft_entries',
          'view_basic_reports'
        ]
      },
      viewer: {
        name: 'مستعلم',
        permissions: [
          'view_accounts',
          'view_reports'
        ]
      }
    },

    // حدود المبالغ للموافقات
    approvalLimits: {
      bookkeeper: 10000,
      accountant: 50000,
      admin: 1000000
    }
  },

  // إعدادات النسخ الاحتياطي
  backup: {
    // تكرار النسخ الاحتياطية
    frequency: {
      daily: 'يومي',
      weekly: 'أسبوعي',
      monthly: 'شهري'
    },

    // أنواع النسخ الاحتياطية
    types: {
      full: 'نسخة كاملة',
      incremental: 'نسخة تزايدية',
      differential: 'نسخة تفاضلية'
    },

    // مواقع التخزين
    locations: {
      local: 'محلي',
      cloud: 'سحابي',
      external: 'خارجي'
    }
  },

  // إعدادات التكامل
  integration: {
    // أنظمة خارجية
    externalSystems: {
      banks: {
        enabled: true,
        supportedBanks: [
          'alahli',
          'alrajhi',
          'samba',
          'riyad',
          'alinma'
        ]
      },
      
      erp: {
        enabled: false,
        supportedSystems: [
          'sap',
          'oracle',
          'microsoft_dynamics'
        ]
      },

      government: {
        zatca: {
          enabled: true,
          environment: 'sandbox', // sandbox, production
          apiUrl: 'https://api.zatca.gov.sa'
        }
      }
    }
  },

  // إعدادات الواجهة
  ui: {
    // الألوان
    colors: {
      primary: '#667eea',
      secondary: '#764ba2',
      success: '#28a745',
      warning: '#ffc107',
      danger: '#dc3545',
      info: '#17a2b8'
    },

    // الخطوط
    fonts: {
      primary: 'Cairo',
      secondary: 'Segoe UI',
      monospace: 'Courier New'
    },

    // أحجام الصفحات
    pagination: {
      defaultPageSize: 25,
      pageSizeOptions: [10, 25, 50, 100]
    }
  },

  // إعدادات الأداء
  performance: {
    // التخزين المؤقت
    cache: {
      enabled: true,
      duration: 300000, // 5 دقائق
      maxSize: 100 // عدد العناصر
    },

    // تحميل البيانات
    dataLoading: {
      batchSize: 1000,
      lazyLoading: true,
      virtualScrolling: true
    }
  },

  // إعدادات التدقيق
  audit: {
    // تسجيل العمليات
    logging: {
      enabled: true,
      level: 'info', // debug, info, warn, error
      retention: 365 // أيام
    },

    // العمليات المسجلة
    trackedOperations: [
      'create_account',
      'update_account',
      'delete_account',
      'create_entry',
      'update_entry',
      'post_entry',
      'approve_entry',
      'cancel_entry',
      'generate_report',
      'export_data',
      'import_data'
    ]
  }
};

// دوال مساعدة
export const formatCurrency = (amount, currency = 'SAR') => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: accountsConfig.general.decimalPlaces
  }).format(amount);
};

export const formatDate = (date, format = accountsConfig.general.dateFormat) => {
  const d = new Date(date);
  return d.toLocaleDateString('ar-SA');
};

export const generateAccountNumber = (category, sequence) => {
  const ranges = accountsConfig.chartOfAccounts.accountNumbering;
  const range = ranges[category];
  if (!range) return null;
  
  const [start] = range.split('-');
  const baseNumber = parseInt(start);
  return (baseNumber + sequence).toString();
};

export const validateAccountNumber = (accountNumber) => {
  const num = parseInt(accountNumber);
  const ranges = accountsConfig.chartOfAccounts.accountNumbering;
  
  for (const [category, range] of Object.entries(ranges)) {
    const [start, end] = range.split('-').map(n => parseInt(n));
    if (num >= start && num <= end) {
      return { valid: true, category };
    }
  }
  
  return { valid: false, category: null };
};

export const getAccountCategory = (accountNumber) => {
  const validation = validateAccountNumber(accountNumber);
  return validation.valid ? validation.category : null;
};

export const hasPermission = (userRole, permission) => {
  const rolePermissions = accountsConfig.security.permissions[userRole];
  if (!rolePermissions) return false;
  
  return rolePermissions.permissions.includes('*') || 
         rolePermissions.permissions.includes(permission);
};

export const needsApproval = (userRole, amount) => {
  const limit = accountsConfig.security.approvalLimits[userRole];
  return amount > limit;
};

export const calculateVAT = (amount, isInclusive = false) => {
  const rate = accountsConfig.taxation.vat.rate;
  
  if (isInclusive) {
    // المبلغ شامل الضريبة
    const baseAmount = amount / (1 + rate);
    const vatAmount = amount - baseAmount;
    return { baseAmount, vatAmount, totalAmount: amount };
  } else {
    // المبلغ قبل الضريبة
    const vatAmount = amount * rate;
    const totalAmount = amount + vatAmount;
    return { baseAmount: amount, vatAmount, totalAmount };
  }
};

export const calculateZakat = (zakatableAssets) => {
  const { rate, nisab } = accountsConfig.taxation.zakat;
  
  if (zakatableAssets < nisab) {
    return 0;
  }
  
  return zakatableAssets * rate;
};

export default accountsConfig;