# تعليمات رفع قاعدة البيانات للاستضافة
# Database Hosting Upload Instructions

## نظرة عامة
هذا الدليل يوضح كيفية رفع قاعدة البيانات إلى مزود الاستضافة بشكل صحيح.

## الملفات المطلوبة

### للاستضافة المحلية أو VPS
استخدم: `complete_database.sql`

### للاستضافة المشتركة
استخدم: `hosting_setup.sql`

## خطوات الرفع

### 1. الاستضافة المشتركة (Shared Hosting)

#### أ. عبر cPanel/phpMyAdmin
1. **تسجيل الدخول إلى cPanel**
   - ادخل إلى لوحة تحكم الاستضافة
   - ابحث عن "phpMyAdmin" أو "MySQL Databases"

2. **إنشاء قاعدة البيانات**
   ```
   - اذهب إلى "MySQL Databases"
   - أنشئ قاعدة بيانات جديدة (مثل: username_travel)
   - أنشئ مستخدم جديد
   - اربط المستخدم بقاعدة البيانات مع جميع الصلاحيات
   ```

3. **استيراد قاعدة البيانات**
   ```
   - افتح phpMyAdmin
   - اختر قاعدة البيانات التي أنشأتها
   - اضغط على "Import"
   - اختر ملف hosting_setup.sql
   - اضغط "Go"
   ```

#### ب. عبر MySQL Command Line (إذا متوفر)
```bash
mysql -h hostname -u username -p database_name < hosting_setup.sql
```

### 2. الخادم المخصص (VPS/Dedicated Server)

#### أ. عبر SSH
```bash
# رفع الملف إلى الخادم
scp complete_database.sql user@server:/path/to/file/

# الاتصال بالخادم
ssh user@server

# تنفيذ قاعدة البيانات
mysql -u root -p < complete_database.sql
```

#### ب. عبر FTP + phpMyAdmin
```
1. ارفع ملف complete_database.sql عبر FTP
2. استخدم phpMyAdmin لاستيراد الملف
```

## إعدادات مهمة

### 1. تحديث معلومات الاتصال
بعد رفع قاعدة البيانات، حدث ملف `.env` في Backend:

```env
# معلومات قاعدة البيانات
DB_HOST=localhost  # أو عنوان الخادم
DB_PORT=3306
DB_NAME=your_database_name  # اسم قاعدة البيانات الفعلي
DB_USER=your_username       # اسم المستخدم الفعلي
DB_PASSWORD=your_password   # كلمة المرور الفعلية

# للاستضافة المشتركة قد تحتاج:
# DB_HOST=localhost
# DB_NAME=cpanel_username_travel
# DB_USER=cpanel_username_travel_user
```

### 2. تحديث Frontend
حدث ملف `.env` في Frontend:

```env
# عنوان API
REACT_APP_API_URL=https://yourdomain.com/api
# أو
REACT_APP_API_URL=https://api.yourdomain.com
```

## مشاكل شائعة وحلولها

### 1. خطأ في حجم الملف
```
Error: File size too large
```

**الحل:**
- قسم الملف إلى أجزاء أصغر
- أو استخدم MySQL Command Line
- أو اطلب من مزود الاستضافة زيادة الحد الأقصى

### 2. خطأ في الصلاحيات
```
Error: Access denied for user
```

**الحل:**
- تأكد من ربط المستخدم بقاعدة البيانات
- تأكد من منح جميع الصلاحيات
- تحقق من اسم المستخدم وكلمة المرور

### 3. خطأ في الترميز
```
Error: Incorrect string value
```

**الحل:**
- تأكد من أن قاعدة البيانات تدعم UTF8MB4
- في phpMyAdmin: اختر utf8mb4_unicode_ci

### 4. خطأ في المحرك
```
Error: Unknown storage engine 'InnoDB'
```

**الحل:**
- معظم الاستضافات تدعم InnoDB
- إذا لم تدعم، غيّر ENGINE=InnoDB إلى ENGINE=MyISAM

## اختبار قاعدة البيانات

### 1. اختبار الاتصال
```sql
-- تشغيل هذا الاستعلام للتأكد من وجود الجداول
SHOW TABLES;

-- التأكد من وجود المستخدم الافتراضي
SELECT * FROM users WHERE username = 'admin';

-- التأكد من الإعدادات
SELECT * FROM system_settings;
```

### 2. اختبار تسجيل الدخول
```
اسم المستخدم: admin
كلمة المرور: admin123
```

## أمان قاعدة البيانات

### 1. تغيير كلمات المرور الافتراضية
```sql
-- تغيير كلمة مرور المدير
UPDATE users 
SET hashed_password = '$2b$12$NEW_HASHED_PASSWORD' 
WHERE username = 'admin';
```

### 2. إنشاء مستخدم جديد لقاعدة البيانات
```sql
-- إنشاء مستخدم جديد
CREATE USER 'new_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON database_name.* TO 'new_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. حذف المستخدمات الافتراضية (اختياري)
```sql
-- حذف مستخدم travel_user إذا لم تعد تحتاجه
DROP USER 'travel_user'@'localhost';
DROP USER 'travel_user'@'%';
```

## النسخ الاحتياطي

### 1. نسخ احتياطي يدوي
```bash
# إنشاء نسخة احتياطية
mysqldump -h hostname -u username -p database_name > backup_$(date +%Y%m%d).sql

# ضغط النسخة الاحتياطية
gzip backup_$(date +%Y%m%d).sql
```

### 2. نسخ احتياطي تلقائي (cron job)
```bash
# إضافة إلى crontab
0 2 * * * /usr/bin/mysqldump -h localhost -u username -ppassword database_name | gzip > /path/to/backups/backup_$(date +\%Y\%m\%d).sql.gz
```

## مراقبة الأداء

### 1. فحص حالة قاعدة البيانات
```sql
-- فحص حجم قاعدة البيانات
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'your_database_name'
GROUP BY table_schema;

-- فحص أكبر الجداول
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'your_database_name'
ORDER BY (data_length + index_length) DESC;
```

### 2. تحسين الأداء
```sql
-- تحليل الجداول
ANALYZE TABLE table_name;

-- إصلاح الجداول
REPAIR TABLE table_name;

-- تحسين الجداول
OPTIMIZE TABLE table_name;
```

## دعم فني

### معلومات مطلوبة عند طلب المساعدة
1. نوع الاستضافة (مشتركة/VPS/مخصصة)
2. إصدار MySQL/MariaDB
3. رسالة الخطأ الكاملة
4. خطوات إعادة إنتاج المشكلة

### جهات الاتصال
- مزود الاستضافة: للمشاكل المتعلقة بالخادم
- فريق التطوير: للمشاكل المتعلقة بالنظام

## ملاحظات مهمة

### للاستضافة المشتركة
- قد تكون هناك قيود على حجم قاعدة البيانات
- قد تكون هناك قيود على عدد الاتصالات المتزامنة
- بعض الميزات المتقدمة قد لا تكون متوفرة

### للخوادم المخصصة
- تأكد من تكوين الأمان بشكل صحيح
- فعّل SSL للاتصالات
- راقب استخدام الموارد

---

**تذكير مهم:** احتفظ بنسخة احتياطية من قاعدة البيانات قبل أي تحديث أو تعديل!