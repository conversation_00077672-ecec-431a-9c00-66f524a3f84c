import React from 'react';
import { getStatusColor, getStatusText } from '../../utils/bookingHelpers';

// مكون شارة الحالة المحسنة
const BookingStatusBadge = ({ status, size = 'normal', showIcon = true }) => {
  const getStatusIcon = (status) => {
    const icons = {
      'pending': '⏳',
      'confirmed': '✅',
      'cancelled': '❌',
      'completed': '🎉',
      'in-progress': '🔄',
      'ready': '📋',
      'delivered': '📦',
      'ministry-done': '🏛️',
      'embassy-done': '🏢'
    };
    
    return icons[status] || '📄';
  };

  const getSizeStyles = (size) => {
    const sizes = {
      'small': {
        padding: '2px 6px',
        fontSize: '10px',
        borderRadius: '8px'
      },
      'normal': {
        padding: '4px 8px',
        fontSize: '11px',
        borderRadius: '12px'
      },
      'large': {
        padding: '6px 12px',
        fontSize: '12px',
        borderRadius: '16px'
      }
    };
    
    return sizes[size] || sizes.normal;
  };

  const sizeStyles = getSizeStyles(size);
  const statusColor = getStatusColor(status);
  const statusText = getStatusText(status);
  const statusIcon = getStatusIcon(status);

  return (
    <span style={{
      ...sizeStyles,
      fontWeight: 'bold',
      background: `${statusColor}20`,
      color: statusColor,
      border: `1px solid ${statusColor}40`,
      display: 'inline-flex',
      alignItems: 'center',
      gap: '4px',
      whiteSpace: 'nowrap'
    }}>
      {showIcon && <span>{statusIcon}</span>}
      <span>{statusText}</span>
    </span>
  );
};

export default BookingStatusBadge;