.accounts-page {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 16px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.period-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.period-selector label {
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
}

.period-selector input {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.period-selector span {
  color: #7f8c8d;
  font-weight: bold;
}

.accounts-container {
  display: flex;
  gap: 25px;
  min-height: 600px;
}

.accounts-sidebar {
  width: 280px;
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  height: fit-content;
}

.accounts-tabs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border: none;
  background: transparent;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  transition: all 0.3s ease;
  text-align: right;
  width: 100%;
}

.tab-btn:hover {
  background: #f8f9fa;
  transform: translateX(-5px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.tab-btn.active:hover {
  transform: translateX(-5px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.tab-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.accounts-content {
  flex: 1;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: auto;
}

/* تنسيق الجداول */
.accounts-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.accounts-table th,
.accounts-table td {
  padding: 12px;
  text-align: right;
  border: 1px solid #dee2e6;
}

.accounts-table th {
  background: #f8f9fa;
  font-weight: bold;
  color: #2c3e50;
}

.accounts-table tbody tr:hover {
  background: #f8f9fa;
}

/* تنسيق الأزرار */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

/* تنسيق النماذج */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* تنسيق البطاقات */
.card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-bottom: 25px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.card-title {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: bold;
}

/* تنسيق الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 14px;
  font-weight: bold;
}

/* تنسيق التحميل */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #7f8c8d;
}

.loading::before {
  content: '';
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .accounts-container {
    flex-direction: column;
  }
  
  .accounts-sidebar {
    width: 100%;
  }
  
  .accounts-tabs {
    flex-direction: row;
    overflow-x: auto;
    gap: 10px;
  }
  
  .tab-btn {
    white-space: nowrap;
    min-width: 150px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .period-selector {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .accounts-page {
    padding: 10px;
  }
  
  .page-header,
  .accounts-sidebar,
  .accounts-content,
  .card {
    padding: 15px;
  }
  
  .header-content h1 {
    font-size: 24px;
  }
  
  .period-selector {
    flex-direction: column;
    align-items: stretch;
  }
}