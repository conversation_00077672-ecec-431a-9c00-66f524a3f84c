/* Modern Form Components Styles */

/* ===== MODERN INPUT ===== */
.modern-input-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  width: 100%;
}

.modern-input-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.modern-input-required {
  color: var(--error-500);
  font-weight: var(--font-bold);
}

.modern-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-input {
  width: 100%;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background: var(--neutral-0);
  color: var(--neutral-800);
  font-family: var(--font-family-arabic);
  font-weight: var(--font-medium);
  transition: all var(--transition-base);
  outline: none;
}

.modern-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-input::placeholder {
  color: var(--neutral-500);
  font-weight: var(--font-normal);
}

/* Input Sizes */
.modern-input--xs {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.modern-input--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.modern-input--md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
}

.modern-input--lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
}

.modern-input--xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--text-xl);
}

/* Input Variants */
.modern-input--default {
  /* Default styling already applied */
}

.modern-input--filled {
  background: var(--neutral-100);
  border: 2px solid transparent;
}

.modern-input--filled:focus {
  background: var(--neutral-0);
  border-color: var(--primary-500);
}

.modern-input--outlined {
  background: transparent;
  border: 2px solid var(--neutral-400);
}

.modern-input--ghost {
  background: transparent;
  border: 2px solid transparent;
  border-bottom: 2px solid var(--neutral-300);
  border-radius: 0;
}

.modern-input--ghost:focus {
  border-bottom-color: var(--primary-500);
}

/* Input States */
.modern-input--focused {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-input--error {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.modern-input--success {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.modern-input--disabled {
  background: var(--neutral-100);
  color: var(--neutral-500);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Input with Icons */
.modern-input--with-icon.modern-input--icon-right {
  padding-right: var(--space-12);
}

.modern-input--with-icon.modern-input--icon-left {
  padding-left: var(--space-12);
}

.modern-input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--neutral-500);
  font-size: var(--text-lg);
  pointer-events: none;
  transition: color var(--transition-fast);
}

.modern-input-icon--right {
  right: var(--space-4);
}

.modern-input-icon--left {
  left: var(--space-4);
}

.modern-input--focused + .modern-input-icon,
.modern-input:focus ~ .modern-input-icon {
  color: var(--primary-500);
}

/* Input Messages */
.modern-input-message {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.modern-input-message--error {
  color: var(--error-600);
}

.modern-input-message--success {
  color: var(--success-600);
}

/* ===== MODERN TEXTAREA ===== */
.modern-textarea-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  width: 100%;
}

.modern-textarea-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.modern-textarea-required {
  color: var(--error-500);
  font-weight: var(--font-bold);
}

.modern-textarea {
  width: 100%;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background: var(--neutral-0);
  color: var(--neutral-800);
  font-family: var(--font-family-arabic);
  font-weight: var(--font-medium);
  transition: all var(--transition-base);
  outline: none;
  resize: vertical;
  min-height: 100px;
}

.modern-textarea:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-textarea::placeholder {
  color: var(--neutral-500);
  font-weight: var(--font-normal);
}

/* Textarea Sizes */
.modern-textarea--xs {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.modern-textarea--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.modern-textarea--md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
}

.modern-textarea--lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
}

.modern-textarea--xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--text-xl);
}

/* Textarea States */
.modern-textarea--focused {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-textarea--error {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.modern-textarea--success {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.modern-textarea--disabled {
  background: var(--neutral-100);
  color: var(--neutral-500);
  cursor: not-allowed;
  opacity: 0.7;
}

.modern-textarea--no-resize {
  resize: none;
}

/* Textarea Messages */
.modern-textarea-message {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.modern-textarea-message--error {
  color: var(--error-600);
}

.modern-textarea-message--success {
  color: var(--success-600);
}

/* ===== MODERN SELECT ===== */
.modern-select-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  width: 100%;
  position: relative;
}

.modern-select-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.modern-select-required {
  color: var(--error-500);
  font-weight: var(--font-bold);
}

.modern-select {
  position: relative;
  width: 100%;
}

.modern-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background: var(--neutral-0);
  color: var(--neutral-800);
  font-family: var(--font-family-arabic);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-base);
  outline: none;
}

.modern-select-trigger:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Select Sizes */
.modern-select--xs .modern-select-trigger {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.modern-select--sm .modern-select-trigger {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.modern-select--md .modern-select-trigger {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
}

.modern-select--lg .modern-select-trigger {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
}

.modern-select--xl .modern-select-trigger {
  padding: var(--space-5) var(--space-8);
  font-size: var(--text-xl);
}

.modern-select-value {
  flex: 1;
  text-align: right;
}

.modern-select-arrow {
  font-size: var(--text-sm);
  color: var(--neutral-500);
  transition: transform var(--transition-fast);
}

.modern-select-arrow--up {
  transform: rotate(180deg);
}

/* Select States */
.modern-select--focused .modern-select-trigger {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-select--error .modern-select-trigger {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.modern-select--success .modern-select-trigger {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.modern-select--disabled .modern-select-trigger {
  background: var(--neutral-100);
  color: var(--neutral-500);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Select Dropdown */
.modern-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-dropdown);
  margin-top: var(--space-1);
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-select-search {
  padding: var(--space-3);
  border-bottom: 1px solid var(--neutral-200);
}

.modern-select-search-input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  outline: none;
}

.modern-select-search-input:focus {
  border-color: var(--primary-500);
}

.modern-select-options {
  max-height: 200px;
  overflow-y: auto;
}

.modern-select-option {
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

.modern-select-option:hover {
  background: var(--neutral-100);
}

.modern-select-option--selected {
  background: var(--primary-100);
  color: var(--primary-700);
  font-weight: var(--font-semibold);
}

.modern-select-no-options {
  padding: var(--space-4);
  text-align: center;
  color: var(--neutral-500);
  font-size: var(--text-sm);
}

/* Select Messages */
.modern-select-message {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.modern-select-message--error {
  color: var(--error-600);
}

.modern-select-message--success {
  color: var(--success-600);
}

/* ===== MODERN CHECKBOX ===== */
.modern-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-fast);
}

.modern-checkbox:hover .modern-checkbox-checkmark {
  border-color: var(--primary-400);
}

.modern-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.modern-checkbox-checkmark {
  position: relative;
  border: 2px solid var(--neutral-400);
  border-radius: var(--radius-md);
  background: var(--neutral-0);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Checkbox Sizes */
.modern-checkbox--xs .modern-checkbox-checkmark {
  width: 16px;
  height: 16px;
}

.modern-checkbox--sm .modern-checkbox-checkmark {
  width: 18px;
  height: 18px;
}

.modern-checkbox--md .modern-checkbox-checkmark {
  width: 20px;
  height: 20px;
}

.modern-checkbox--lg .modern-checkbox-checkmark {
  width: 24px;
  height: 24px;
}

.modern-checkbox--xl .modern-checkbox-checkmark {
  width: 28px;
  height: 28px;
}

.modern-checkbox-check {
  color: var(--neutral-0);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
}

.modern-checkbox--checked .modern-checkbox-checkmark {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.modern-checkbox--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modern-checkbox-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
}

/* ===== MODERN RADIO ===== */
.modern-radio {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-fast);
}

.modern-radio:hover .modern-radio-circle {
  border-color: var(--primary-400);
}

.modern-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.modern-radio-circle {
  position: relative;
  border: 2px solid var(--neutral-400);
  border-radius: var(--radius-full);
  background: var(--neutral-0);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Radio Sizes */
.modern-radio--xs .modern-radio-circle {
  width: 16px;
  height: 16px;
}

.modern-radio--sm .modern-radio-circle {
  width: 18px;
  height: 18px;
}

.modern-radio--md .modern-radio-circle {
  width: 20px;
  height: 20px;
}

.modern-radio--lg .modern-radio-circle {
  width: 24px;
  height: 24px;
}

.modern-radio--xl .modern-radio-circle {
  width: 28px;
  height: 28px;
}

.modern-radio-dot {
  width: 8px;
  height: 8px;
  background: var(--neutral-0);
  border-radius: var(--radius-full);
}

.modern-radio--checked .modern-radio-circle {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.modern-radio--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modern-radio-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
}

/* ===== MODERN FORM ===== */
.modern-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
}

.modern-form--default {
  /* Default styling */
}

.modern-form--card {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
}

.modern-form--glass {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
}

/* Form Sizes */
.modern-form--xs {
  gap: var(--space-3);
}

.modern-form--sm {
  gap: var(--space-4);
}

.modern-form--md {
  gap: var(--space-6);
}

.modern-form--lg {
  gap: var(--space-8);
}

.modern-form--xl {
  gap: var(--space-10);
}

/* ===== FORM GROUP ===== */
.form-group {
  display: flex;
  width: 100%;
}

.form-group--column {
  flex-direction: column;
}

.form-group--row {
  flex-direction: row;
  align-items: flex-end;
}

.form-group--gap-xs {
  gap: var(--space-1);
}

.form-group--gap-sm {
  gap: var(--space-2);
}

.form-group--gap-md {
  gap: var(--space-4);
}

.form-group--gap-lg {
  gap: var(--space-6);
}

.form-group--gap-xl {
  gap: var(--space-8);
}

/* ===== FORM ROW ===== */
.form-row {
  display: grid;
  width: 100%;
}

.form-row--cols-auto {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.form-row--cols-1 {
  grid-template-columns: 1fr;
}

.form-row--cols-2 {
  grid-template-columns: 1fr 1fr;
}

.form-row--cols-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.form-row--cols-4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.form-row--gap-xs {
  gap: var(--space-1);
}

.form-row--gap-sm {
  gap: var(--space-2);
}

.form-row--gap-md {
  gap: var(--space-4);
}

.form-row--gap-lg {
  gap: var(--space-6);
}

.form-row--gap-xl {
  gap: var(--space-8);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .form-row--cols-2,
  .form-row--cols-3,
  .form-row--cols-4 {
    grid-template-columns: 1fr;
  }
  
  .form-group--row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .modern-form--card,
  .modern-form--glass {
    padding: var(--space-6);
  }
}

@media (max-width: 480px) {
  .modern-form--card,
  .modern-form--glass {
    padding: var(--space-4);
  }
  
  .modern-input--lg,
  .modern-input--xl {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
  }
  
  .modern-textarea--lg,
  .modern-textarea--xl {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-input,
  .modern-textarea,
  .modern-select-trigger {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .modern-input::placeholder,
  .modern-textarea::placeholder {
    color: var(--neutral-500);
  }
  
  .modern-input-label,
  .modern-textarea-label,
  .modern-select-label {
    color: var(--neutral-300);
  }
  
  .modern-checkbox-label,
  .modern-radio-label {
    color: var(--neutral-300);
  }
  
  .modern-checkbox-checkmark,
  .modern-radio-circle {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .modern-select-dropdown {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .modern-select-option {
    color: var(--neutral-300);
  }
  
  .modern-select-option:hover {
    background: var(--neutral-700);
  }
  
  .modern-select-option--selected {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .modern-form--card {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-input,
  .modern-textarea,
  .modern-select-trigger {
    border: 1px solid #000 !important;
    background: white !important;
    color: black !important;
  }
  
  .modern-select-dropdown {
    display: none !important;
  }
  
  .modern-checkbox-checkmark,
  .modern-radio-circle {
    border: 1px solid #000 !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-input,
  .modern-textarea,
  .modern-select-trigger,
  .modern-checkbox,
  .modern-radio {
    transition: none !important;
  }
  
  .modern-select-dropdown {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-input,
  .modern-textarea,
  .modern-select-trigger,
  .modern-checkbox-checkmark,
  .modern-radio-circle {
    border: 2px solid currentColor !important;
  }
}