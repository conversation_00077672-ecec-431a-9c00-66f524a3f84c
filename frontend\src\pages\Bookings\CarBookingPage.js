import React, { useState, useEffect } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';

const CarBookingPage = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);

  const [newBooking, setNewBooking] = useState({
    customerName: '',
    customerPhone: '',
    carType: 'hilux', // hilux, prado, porsche, sedan
    fromLocation: '',
    toLocation: '',
    departureDate: '',
    seatsCount: 4,
    bookingType: 'seats', // seats, full
    attachments: [],
    driverName: '',
    driverPhone: '',
    plateNumber: '',
    totalAmount: '',
    paidAmount: 0,
    currency: 'SAR',
    status: 'pending',
    paymentStatus: 'unpaid',
    bookingReference: '',
    notes: ''
  });

  useEffect(() => {
    setTimeout(() => {
      setBookings([
        {
          id: 1,
          customerName: 'عبدالرحمن محمد الأحمد',
          customerPhone: '+966501234567',
          carType: 'prado',
          fromLocation: 'الرياض',
          toLocation: 'جدة',
          departureDate: '2024-02-15',
          seatsCount: 7,
          bookingType: 'full',
          attachments: ['صورة الهوية', 'رخصة القيادة'],
          driverName: 'أحمد السائق',
          driverPhone: '+966551234567',
          plateNumber: 'أ ب ج 1234',
          totalAmount: 1200,
          paidAmount: 1200,
          currency: 'SAR',
          status: 'confirmed',
          paymentStatus: 'paid',
          bookingReference: 'CAR2024001',
          bookingDate: '2024-01-15',
          notes: 'رحلة عائلية - سيارة كاملة'
        },
        {
          id: 2,
          customerName: 'نوال سالم الخالد',
          customerPhone: '+966507654321',
          carType: 'hilux',
          fromLocation: 'الدمام',
          toLocation: 'الرياض',
          departureDate: '2024-02-20',
          seatsCount: 2,
          bookingType: 'seats',
          attachments: ['صورة الهوية'],
          driverName: 'محمد السائق',
          driverPhone: '+966557654321',
          plateNumber: 'د هـ و 5678',
          totalAmount: 400,
          paidAmount: 200,
          currency: 'YER',
          status: 'pending',
          paymentStatus: 'partial',
          bookingReference: 'CAR2024002',
          bookingDate: '2024-01-14',
          notes: 'حجز مقاعد فقط'
        },
        {
          id: 3,
          customerName: 'خالد عبدالله المحمد',
          customerPhone: '+966559876543',
          carType: 'porsche',
          fromLocation: 'جدة',
          toLocation: 'مكة المكرمة',
          departureDate: '2024-02-25',
          seatsCount: 4,
          bookingType: 'full',
          attachments: ['صورة الهوية', 'رخصة القيادة', 'تأمين السيارة'],
          driverName: 'سالم السائق',
          driverPhone: '+966559876543',
          plateNumber: 'ز ح ط 9999',
          totalAmount: 500,
          paidAmount: 250,
          currency: 'USD',
          status: 'confirmed',
          paymentStatus: 'partial',
          bookingReference: 'CAR2024003',
          bookingDate: '2024-01-13',
          notes: 'سيارة فاخرة للمناسبات'
        },
        {
          id: 4,
          customerName: 'فاطمة أحمد الزهراني',
          customerPhone: '+966552468135',
          carType: 'sedan',
          fromLocation: 'الطائف',
          toLocation: 'الرياض',
          departureDate: '2024-02-28',
          seatsCount: 3,
          bookingType: 'seats',
          attachments: ['صورة الهوية'],
          driverName: 'عبدالله السائق',
          driverPhone: '+966552468135',
          plateNumber: 'ي ك ل 7777',
          totalAmount: 300,
          paidAmount: 0,
          currency: 'SAR',
          status: 'pending',
          paymentStatus: 'unpaid',
          bookingReference: 'CAR2024004',
          bookingDate: '2024-01-12',
          notes: 'رحلة عمل'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddBooking = (e) => {
    e.preventDefault();
    const booking = {
      id: bookings.length + 1,
      ...newBooking,
      totalAmount: parseFloat(newBooking.totalAmount),
      paidAmount: parseFloat(newBooking.paidAmount),
      bookingDate: new Date().toISOString().split('T')[0],
      bookingReference: `CAR${new Date().getFullYear()}${String(bookings.length + 1).padStart(3, '0')}`
    };
    setBookings([booking, ...bookings]);
    setNewBooking({
      customerName: '',
      customerPhone: '',
      carType: 'hilux',
      fromLocation: '',
      toLocation: '',
      departureDate: '',
      seatsCount: 4,
      bookingType: 'seats',
      attachments: [],
      driverName: '',
      driverPhone: '',
      plateNumber: '',
      totalAmount: '',
      paidAmount: 0,
      currency: 'SAR',
      status: 'pending',
      paymentStatus: 'unpaid',
      bookingReference: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
      setBookings(bookings.filter(b => b.id !== booking.id));
    }
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, 'السيارات');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, 'السيارات');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, 'السيارات');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, 'السيارات');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedIds.length} حجز؟`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };

  const getCarTypeText = (carType) => {
    switch (carType) {
      case 'hilux': return 'هيلوكس';
      case 'prado': return 'برادو';
      case 'porsche': return 'بورش';
      case 'sedan': return 'صالون';
      default: return carType;
    }
  };

  const getBookingTypeText = (bookingType) => {
    switch (bookingType) {
      case 'seats': return 'مقاعد';
      case 'full': return 'كاملة';
      default: return bookingType;
    }
  };

  const getRentalTypeText = (rentalType) => {
    switch (rentalType) {
      case 'hourly': return 'بالساعة';
      case 'daily': return 'يومي';
      case 'weekly': return 'أسبوعي';
      case 'monthly': return 'شهري';
      default: return rentalType;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return '#27ae60';
      case 'pending': return '#f39c12';
      case 'cancelled': return '#e74c3c';
      case 'completed': return '#8e44ad';
      case 'active': return '#1abc9c';
      default: return '#95a5a6';
    }
  };

  const formatCurrency = (amount, currency = 'SAR') => {
    const currencySymbols = {
      'SAR': { symbol: 'ر.س', locale: 'ar-SA' },
      'YER': { symbol: 'ر.ي', locale: 'ar-YE' },
      'USD': { symbol: '$', locale: 'en-US' }
    };
    
    const currencyInfo = currencySymbols[currency] || currencySymbols['SAR'];
    
    if (currency === 'USD') {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } else {
      return `${amount.toLocaleString(currencyInfo.locale)} ${currencyInfo.symbol}`;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.pickupLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.bookingReference.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || booking.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #1abc9c',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل حجوزات السيارات...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h2 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>🚗 حجوزات السيارات</h2>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة وتتبع حجوزات تأجير السيارات</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #1abc9c 0%, #16a085 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ حجز سيارة جديد
        </button>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في حجوزات السيارات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="pending">معلق</option>
          <option value="confirmed">مؤكد</option>
          <option value="active">نشط</option>
          <option value="completed">مكتمل</option>
          <option value="cancelled">ملغي</option>
        </select>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي الحجوزات', value: bookings.length, color: '#1abc9c', icon: '🚗' },
          { title: 'الحجوزات المؤكدة', value: bookings.filter(b => b.status === 'confirmed').length, color: '#27ae60', icon: '✅' },
          { title: 'الحجوزات النشطة', value: bookings.filter(b => b.status === 'active').length, color: '#1abc9c', icon: '🔄' },
          { 
            title: 'إجمالي المبيعات', 
            value: (() => {
              const totals = bookings.reduce((acc, b) => {
                const currency = b.currency || 'SAR';
                acc[currency] = (acc[currency] || 0) + (b.totalAmount || 0);
                return acc;
              }, {});
              
              return Object.entries(totals)
                .map(([currency, amount]) => formatCurrency(amount, currency))
                .join(' | ');
            })(),
            color: '#8e44ad', 
            icon: '💰' 
          }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {stat.value}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

      {/* Bookings Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>العميل</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>نوع السيارة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الرحلة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>السائق والسيارة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>المقاعد والنوع</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>المبلغ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookings.map((booking) => (
                <tr key={booking.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedBookings.includes(booking.id)}
                      onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{booking.customerName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{booking.customerPhone}</div>
                      <div style={{ fontSize: '11px', color: '#95a5a6' }}>{booking.bookingReference}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      background: booking.carType === 'porsche' ? '#8e44ad20' : 
                                 booking.carType === 'prado' ? '#e67e2220' : 
                                 booking.carType === 'hilux' ? '#27ae6020' : '#3498db20',
                      color: booking.carType === 'porsche' ? '#8e44ad' : 
                             booking.carType === 'prado' ? '#e67e22' : 
                             booking.carType === 'hilux' ? '#27ae60' : '#3498db'
                    }}>
                      {getCarTypeText(booking.carType)}
                    </span>
                    {booking.attachments && booking.attachments.length > 0 && (
                      <div style={{ fontSize: '10px', color: '#9b59b6', marginTop: '2px' }}>
                        📎 {booking.attachments.length} مرفق
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>
                      {booking.fromLocation} → {booking.toLocation}
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      📅 {booking.departureDate}
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>
                      👤 {booking.driverName}
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d', marginBottom: '2px' }}>
                      📞 {booking.driverPhone}
                    </div>
                    <div style={{ fontSize: '10px', color: '#95a5a6' }}>
                      🚗 {booking.plateNumber}
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>
                      👥 {booking.seatsCount} مقعد
                    </div>
                    <span style={{
                      padding: '2px 6px',
                      borderRadius: '8px',
                      fontSize: '10px',
                      fontWeight: 'bold',
                        background: '#27ae6020',
                        color: '#27ae60'
                      }}>
                        مع سائق
                      </span>
                    ) : (
                      <span style={{
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '11px',
                        fontWeight: 'bold',
                        background: '#95a5a620',
                        color: '#95a5a6'
                      }}>
                        بدون سائق
                      </span>
                    )
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold', color: '#27ae60' }}>
                      {formatCurrency(booking.totalAmount, booking.currency)}
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      مدفوع: {formatCurrency(booking.paidAmount, booking.currency)}
                    </div>
                    {booking.paidAmount < booking.totalAmount && (
                      <div style={{ fontSize: '10px', color: '#e74c3c' }}>
                        متبقي: {formatCurrency(booking.totalAmount - booking.paidAmount, booking.currency)}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      background: `${getStatusColor(booking.status)}20`,
                      color: getStatusColor(booking.status)
                    }}>
                      {booking.status === 'confirmed' ? 'مؤكد' :
                       booking.status === 'pending' ? 'معلق' :
                       booking.status === 'cancelled' ? 'ملغي' :
                       booking.status === 'completed' ? 'مكتمل' :
                       booking.status === 'active' ? 'نشط' : booking.status}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <BookingActions
                      booking={booking}
                      onView={handleViewBooking}
                      onEdit={handleEditBooking}
                      onDelete={handleDeleteSingleBooking}
                      onPrint={handlePrintBooking}
                      onSavePDF={handleSavePDFBooking}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Booking Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>🚗 حجز سيارة جديد</h2>
            
            <form onSubmit={handleAddBooking}>
              {/* معلومات العميل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#1abc9c', marginBottom: '15px' }}>👤 معلومات العميل</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({...newBooking, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({...newBooking, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>


              {/* تفاصيل السيارة والرحلة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#1abc9c', marginBottom: '15px' }}>🚗 تفاصيل السيارة والرحلة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع السيارة</label>
                    <select
                      value={newBooking.carType}
                      onChange={(e) => setNewBooking({...newBooking, carType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="hilux">هيلوكس</option>
                      <option value="prado">برادو</option>
                      <option value="porsche">بورش</option>
                      <option value="sedan">صالون</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>من</label>
                    <input
                      type="text"
                      value={newBooking.fromLocation}
                      onChange={(e) => setNewBooking({...newBooking, fromLocation: e.target.value})}
                      required
                      placeholder="مثال: الرياض"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إلى</label>
                    <input
                      type="text"
                      value={newBooking.toLocation}
                      onChange={(e) => setNewBooking({...newBooking, toLocation: e.target.value})}
                      required
                      placeholder="مثال: جدة"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>تاريخ المغادرة</label>
                    <input
                      type="date"
                      value={newBooking.departureDate}
                      onChange={(e) => setNewBooking({...newBooking, departureDate: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>عدد المقاعد</label>
                    <input
                      type="number"
                      min="1"
                      max="8"
                      value={newBooking.seatsCount}
                      onChange={(e) => setNewBooking({...newBooking, seatsCount: parseInt(e.target.value)})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الحجز</label>
                    <select
                      value={newBooking.bookingType}
                      onChange={(e) => setNewBooking({...newBooking, bookingType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="seats">مقاعد</option>
                      <option value="full">كاملة</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* المرفقات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#1abc9c', marginBottom: '15px' }}>📎 المرفقات</h3>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إضافة مرفقات</label>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      const fileNames = files.map(file => file.name);
                      setNewBooking({...newBooking, attachments: [...newBooking.attachments, ...fileNames]});
                    }}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                  />
                  {newBooking.attachments.length > 0 && (
                    <div style={{ marginTop: '10px' }}>
                      <p style={{ fontSize: '12px', color: '#7f8c8d', marginBottom: '5px' }}>المرفقات المضافة:</p>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
                        {newBooking.attachments.map((attachment, index) => (
                          <span key={index} style={{
                            padding: '4px 8px',
                            background: '#1abc9c20',
                            color: '#1abc9c',
                            borderRadius: '12px',
                            fontSize: '11px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px'
                          }}>
                            📎 {attachment}
                            <button
                              type="button"
                              onClick={() => {
                                const newAttachments = newBooking.attachments.filter((_, i) => i !== index);
                                setNewBooking({...newBooking, attachments: newAttachments});
                              }}
                              style={{
                                background: 'none',
                                border: 'none',
                                color: '#1abc9c',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              ✕
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* معلومات السائق والسيارة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#1abc9c', marginBottom: '15px' }}>👨‍✈️ معلومات السائق والسيارة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم السائق</label>
                    <input
                      type="text"
                      value={newBooking.driverName}
                      onChange={(e) => setNewBooking({...newBooking, driverName: e.target.value})}
                      required
                      placeholder="مثال: أحمد السائق"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم هاتف السائق</label>
                    <input
                      type="tel"
                      value={newBooking.driverPhone}
                      onChange={(e) => setNewBooking({...newBooking, driverPhone: e.target.value})}
                      required
                      placeholder="+966xxxxxxxxx"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم لوحة السيارة</label>
                    <input
                      type="text"
                      value={newBooking.plateNumber}
                      onChange={(e) => setNewBooking({...newBooking, plateNumber: e.target.value})}
                      required
                      placeholder="مثال: أ ب ج 1234"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#1abc9c', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ الإجمالي</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.totalAmount}
                      onChange={(e) => setNewBooking({...newBooking, totalAmount: e.target.value})}
                      required
                      placeholder="0.00"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ المدفوع</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.paidAmount}
                      onChange={(e) => setNewBooking({...newBooking, paidAmount: e.target.value})}
                      placeholder="0.00"
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع العملة</label>
                    <select
                      value={newBooking.currency}
                      onChange={(e) => setNewBooking({...newBooking, currency: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="SAR">🇸🇦 ريال سعودي (SAR)</option>
                      <option value="YER">🇾🇪 ريال يمني (YER)</option>
                      <option value="USD">🇺🇸 دولار أمريكي (USD)</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* الملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#1abc9c', marginBottom: '15px' }}>📝 الملاحظات</h3>
                <textarea
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                  rows="3"
                  placeholder="أي ملاحظات إضافية..."
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              {/* أزرار الحفظ والإلغاء */}
              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #95a5a6',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#95a5a6',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #1abc9c',
                    borderRadius: '8px',
                    background: '#1abc9c',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer'
                  }}
                >
                  حفظ الحجز
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />
    </div>
  );
};

export default CarBookingPage;