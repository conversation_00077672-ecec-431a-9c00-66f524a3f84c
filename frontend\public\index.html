<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3B82F6" />
    <meta name="description" content="نظام شراء للسفر والسياحة - نظام إدارة شامل ومتطور للشركات السياحية" />
    <meta name="keywords" content="سفر, سياحة, حجوزات, تأشيرات, فنادق, طيران, إدارة, نظام" />
    <meta name="author" content="Sharaub Travel Software" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="نظام شراء للسفر والسياحة" />
    <meta property="og:description" content="نظام إدارة شامل ومتطور للشركات السياحية" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="%PUBLIC_URL%" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    <meta property="og:locale" content="ar_SA" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="نظام شراء للسفر والسياحة" />
    <meta name="twitter:description" content="نظام إدارة شامل ومتطور للشركات السياحية" />
    <meta name="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <title>نظام شراء للسفر والسياحة</title>
    
    <style>
      body {
        margin: 0;
        font-family: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        direction: rtl;
        text-align: right;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      /* Modern Loading Screen */
      .loading-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-family: 'Cairo', sans-serif;
      }
      
      .loading-logo {
        font-size: 4rem;
        margin-bottom: 2rem;
        animation: float 3s ease-in-out infinite;
      }
      
      .loading-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
      }
      
      .loading-subtitle {
        font-size: 1.2rem;
        font-weight: 400;
        margin-bottom: 3rem;
        opacity: 0.9;
        text-align: center;
      }
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-dots {
        margin-top: 2rem;
        display: flex;
        gap: 0.5rem;
      }
      
      .loading-dot {
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
        animation: pulse 1.5s ease-in-out infinite;
      }
      
      .loading-dot:nth-child(2) {
        animation-delay: 0.3s;
      }
      
      .loading-dot:nth-child(3) {
        animation-delay: 0.6s;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 0.4; transform: scale(0.8); }
        50% { opacity: 1; transform: scale(1.2); }
      }
    </style>
  </head>
  <body>
    <noscript>يجب تفعيل JavaScript لتشغيل هذا التطبيق.</noscript>
    <div id="root">
      <div class="loading-container">
        <div class="loading-logo">✈️</div>
        <div class="loading-title">شراء للسفر والسياحة</div>
        <div class="loading-subtitle">نظام إدارة متطور للشركات السياحية</div>
        <div class="loading-spinner"></div>
        <div class="loading-dots">
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
        </div>
      </div>
    </div>
  </body>
</html>