/* Modern Dashboard Styles */

.modern-dashboard {
  padding: var(--space-6);
  background: transparent;
  min-height: 100vh;
  animation: fadeInUp 0.6s ease-out;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

.dashboard-title h1 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-title p {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
}

.dashboard-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.time-range-selector {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  background: var(--neutral-0);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.time-range-selector:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Stats Grid */
.stats-grid {
  margin-bottom: var(--space-8);
}

/* Main Content Grid */
.main-content-grid {
  margin-bottom: var(--space-8);
}

.dashboard-section {
  height: fit-content;
}

/* Activities Section */
.activities-section .modern-card {
  height: 100%;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--neutral-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.activity-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-300);
}

.activity-icon {
  flex-shrink: 0;
}

.activity-icon-bg {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  color: var(--neutral-0);
  font-weight: var(--font-bold);
}

.activity-icon-bg--primary { background: var(--gradient-primary); }
.activity-icon-bg--secondary { background: var(--gradient-secondary); }
.activity-icon-bg--success { background: var(--gradient-success); }
.activity-icon-bg--warning { background: var(--gradient-warning); }
.activity-icon-bg--info { background: linear-gradient(135deg, var(--info-500) 0%, var(--info-700) 100%); }

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.activity-title {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-800);
  line-height: var(--leading-tight);
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  font-weight: var(--font-medium);
  white-space: nowrap;
}

.activity-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-customer {
  font-size: var(--text-xs);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
}

.activity-amount {
  font-size: var(--text-sm);
  color: var(--success-600);
  font-weight: var(--font-bold);
  font-family: var(--font-family-mono);
}

.activity-footer {
  display: flex;
  justify-content: flex-start;
}

.activity-status {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.activity-status--success {
  background: var(--success-100);
  color: var(--success-700);
}

.activity-status--warning {
  background: var(--warning-100);
  color: var(--warning-700);
}

.activity-status--error {
  background: var(--error-100);
  color: var(--error-700);
}

.activity-status--neutral {
  background: var(--neutral-100);
  color: var(--neutral-700);
}

.activities-footer {
  border-top: 1px solid var(--neutral-200);
  padding-top: var(--space-4);
}

/* Quick Actions Section */
.quick-actions-section .modern-card {
  height: 100%;
}

.quick-actions-grid {
  gap: var(--space-4);
}

/* Metrics Section */
.metrics-section .modern-card {
  height: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

/* Charts Section */
.charts-section {
  margin-bottom: var(--space-8);
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-6);
  background: var(--neutral-50);
  border: 2px dashed var(--neutral-300);
  border-radius: var(--radius-xl);
  text-align: center;
}

.chart-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
  opacity: 0.7;
}

.chart-placeholder p {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
}

.chart-placeholder small {
  font-size: var(--text-sm);
  color: var(--neutral-500);
}

/* Weather Widget */
.weather-widget {
  background: var(--gradient-primary);
  color: var(--neutral-0);
  margin-bottom: var(--space-6);
}

.weather-info h3 {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
}

.weather-info p {
  margin: 0;
  font-size: var(--text-sm);
  opacity: 0.9;
}

.weather-icon {
  font-size: var(--text-4xl);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-section {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.dashboard-section:nth-child(1) { animation-delay: 0.1s; }
.dashboard-section:nth-child(2) { animation-delay: 0.2s; }
.dashboard-section:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .metrics-section {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .modern-dashboard {
    padding: var(--space-4);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
    text-align: center;
    padding: var(--space-4);
  }
  
  .dashboard-title h1 {
    font-size: var(--text-3xl);
  }
  
  .dashboard-title p {
    font-size: var(--text-base);
  }
  
  .dashboard-controls {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .main-content-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    text-align: center;
  }
  
  .activity-header {
    flex-direction: column;
    gap: var(--space-1);
    text-align: center;
  }
  
  .activity-details {
    flex-direction: column;
    gap: var(--space-1);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .modern-dashboard {
    padding: var(--space-3);
  }
  
  .dashboard-header {
    padding: var(--space-3);
  }
  
  .dashboard-title h1 {
    font-size: var(--text-2xl);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-controls {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .time-range-selector {
    width: 100%;
  }
  
  .activities-list {
    max-height: 300px;
  }
  
  .activity-item {
    padding: var(--space-3);
  }
  
  .chart-placeholder {
    padding: var(--space-8) var(--space-4);
  }
  
  .chart-icon {
    font-size: var(--text-4xl);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dashboard-header {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .dashboard-title h1 {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .dashboard-title p {
    color: var(--neutral-400);
  }
  
  .time-range-selector {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
  }
  
  .activity-item {
    background: var(--neutral-800);
    border-color: var(--neutral-700);
  }
  
  .activity-item:hover {
    border-color: var(--primary-600);
  }
  
  .activity-title {
    color: var(--neutral-200);
  }
  
  .activity-customer {
    color: var(--neutral-400);
  }
  
  .activity-time {
    color: var(--neutral-500);
  }
  
  .activities-footer {
    border-top-color: var(--neutral-700);
  }
  
  .chart-placeholder {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .chart-placeholder p {
    color: var(--neutral-300);
  }
  
  .chart-placeholder small {
    color: var(--neutral-500);
  }
}

/* Print Styles */
@media print {
  .modern-dashboard {
    padding: 0;
    background: white !important;
  }
  
  .dashboard-header {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .dashboard-controls {
    display: none !important;
  }
  
  .activity-item {
    break-inside: avoid;
    border: 1px solid #000 !important;
    background: white !important;
  }
  
  .chart-placeholder {
    border: 1px solid #000 !important;
    background: white !important;
  }
  
  .weather-widget {
    background: white !important;
    color: black !important;
    border: 1px solid #000 !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .modern-dashboard,
  .dashboard-section,
  .activity-item {
    animation: none !important;
  }
  
  .activity-item:hover {
    transform: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .dashboard-header,
  .activity-item,
  .chart-placeholder {
    border: 2px solid currentColor !important;
  }
  
  .activity-status {
    border: 1px solid currentColor;
  }
}