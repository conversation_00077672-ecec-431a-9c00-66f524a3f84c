# مثال على ملف متغيرات البيئة
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب بيئتك

# عنوان API الخلفي
REACT_APP_API_URL=http://localhost:8000/api

# بيئة التطبيق (development, production, test)
REACT_APP_ENV=development

# مفتاح التشفير للجلسات
REACT_APP_SECRET_KEY=your-secret-key-here

# إعدادات قاعدة البيانات (للمرجع فقط)
# DATABASE_URL=postgresql://username:password@localhost:5432/sharaubtravelsoft

# إعدادات البريد الإلكتروني
REACT_APP_EMAIL_SERVICE=gmail
REACT_APP_EMAIL_USER=<EMAIL>
REACT_APP_EMAIL_PASS=your-app-password

# إعدادات التخزين السحابي
REACT_APP_CLOUDINARY_CLOUD_NAME=your-cloud-name
REACT_APP_CLOUDINARY_API_KEY=your-api-key

# إعدادات الدفع
REACT_APP_STRIPE_PUBLIC_KEY=pk_test_your-stripe-key
REACT_APP_PAYPAL_CLIENT_ID=your-paypal-client-id

# إعدادات الخرائط
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-key

# إعدادات التحليلات
REACT_APP_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# إعدادات الإشعارات
REACT_APP_FIREBASE_API_KEY=your-firebase-key
REACT_APP_FIREBASE_PROJECT_ID=your-project-id

# إعدادات التطوير
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug

# إعدادات الأمان
REACT_APP_ENABLE_HTTPS=false
REACT_APP_CORS_ORIGIN=http://localhost:3000

# إعدادات الملفات
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# إعدادات اللغة والمنطقة
REACT_APP_DEFAULT_LANGUAGE=ar
REACT_APP_DEFAULT_CURRENCY=SAR
REACT_APP_DEFAULT_TIMEZONE=Asia/Riyadh