import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './LandingPage.css';

const LandingPage = () => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  // تتبع التمرير
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // تغيير الشرائح تلقائياً
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % 3);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: '🎯',
      title: 'إدارة شاملة للحجوزات',
      description: 'نظام متكامل لإدارة جميع أنواع الحجوزات من العمرة والحج إلى الطيران والفنادق'
    },
    {
      icon: '📊',
      title: 'تقارير مفصلة ومتقدمة',
      description: 'تقارير مالية وإحصائية شاملة مع رسوم بيانية تفاعلية لمتابعة الأداء'
    },
    {
      icon: '💳',
      title: 'إدارة مالية متطورة',
      description: 'نظام محاسبي متكامل مع إدارة المدفوعات والفواتير والضرائب'
    },
    {
      icon: '👥',
      title: 'إدارة العملاء والموردين',
      description: 'قاعدة بيانات شاملة للعملاء والموردين مع تتبع التفاعلات والمعاملات'
    },
    {
      icon: '🔒',
      title: 'أمان عالي وحماية البيانات',
      description: 'حماية متقدمة للبيانات مع نسخ احتياطية تلقائية وتشفير عالي المستوى'
    },
    {
      icon: '📱',
      title: 'واجهة متجاوبة وسهلة',
      description: 'تصميم عصري ومتجاوب يعمل على جميع الأجهزة مع تجربة مستخدم مميزة'
    }
  ];

  const testimonials = [
    {
      name: 'أحمد محمد الأحمد',
      company: 'شركة الرحلات الذهبية',
      text: 'نظام رائع ساعدنا في تنظيم عملنا وزيادة الكفاءة بشكل كبير. أنصح به بشدة.',
      rating: 5
    },
    {
      name: 'فاطمة علي السالم',
      company: 'مؤسسة السفر المميز',
      text: 'واجهة سهلة الاستخدام وميزات متقدمة. وفر علينا الكثير من الوقت والجهد.',
      rating: 5
    },
    {
      name: 'محمد عبدالله القحطاني',
      company: 'شركة الحرمين للسياحة',
      text: 'الدعم الفني ممتاز والنظام مستقر جداً. تجربة رائعة منذ البداية.',
      rating: 5
    }
  ];

  const stats = [
    { number: '1000+', label: 'عميل راضي' },
    { number: '50+', label: 'وجهة سياحية' },
    { number: '24/7', label: 'دعم فني' },
    { number: '99.9%', label: 'وقت التشغيل' }
  ];

  return (
    <div className="landing-page">
      {/* شريط التنقل */}
      <nav className={`navbar ${isScrolled ? 'scrolled' : ''}`}>
        <div className="nav-container">
          <div className="nav-logo">
            <span className="logo-icon">✈️</span>
            <span className="logo-text">شراء السفر</span>
          </div>
          
          <div className="nav-menu">
            <a href="#home" className="nav-link">الرئيسية</a>
            <a href="#features" className="nav-link">المميزات</a>
            <a href="#about" className="nav-link">عن النظام</a>
            <a href="#contact" className="nav-link">اتصل بنا</a>
          </div>
          
          <div className="nav-actions">
            <button 
              className="btn-secondary"
              onClick={() => navigate('/login')}
            >
              تسجيل الدخول
            </button>
            <button 
              className="btn-primary"
              onClick={() => navigate('/login')}
            >
              تجربة مجانية
            </button>
          </div>
        </div>
      </nav>

      {/* القسم الرئيسي */}
      <section id="home" className="hero-section">
        <div className="hero-background">
          <div className="hero-overlay"></div>
        </div>
        
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              نظام إدارة السفر والسياحة
              <span className="highlight"> الأكثر تطوراً</span>
            </h1>
            <p className="hero-description">
              حل متكامل لإدارة شركات السفر والسياحة مع أحدث التقنيات
              وأفضل معايير الأمان والجودة لتنمية أعمالك
            </p>
            
            <div className="hero-actions">
              <button 
                className="btn-hero-primary"
                onClick={() => navigate('/login')}
              >
                <span className="btn-icon">🚀</span>
                ابدأ الآن مجاناً
              </button>
              <button className="btn-hero-secondary">
                <span className="btn-icon">▶️</span>
                شاهد العرض التوضيحي
              </button>
            </div>
            
            <div className="hero-stats">
              {stats.map((stat, index) => (
                <div key={index} className="stat-item">
                  <div className="stat-number">{stat.number}</div>
                  <div className="stat-label">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="hero-visual">
            <div className="dashboard-preview">
              <div className="preview-header">
                <div className="preview-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div className="preview-title">لوحة التحكم</div>
              </div>
              <div className="preview-content">
                <div className="preview-sidebar">
                  <div className="sidebar-item active">🏠 الرئيسية</div>
                  <div className="sidebar-item">👥 العملاء</div>
                  <div className="sidebar-item">📋 الحجوزات</div>
                  <div className="sidebar-item">💰 المبيعات</div>
                  <div className="sidebar-item">📊 التقارير</div>
                </div>
                <div className="preview-main">
                  <div className="preview-cards">
                    <div className="preview-card">
                      <div className="card-icon">📈</div>
                      <div className="card-info">
                        <div className="card-title">إجمالي المبيعات</div>
                        <div className="card-value">250,000 ر.س</div>
                      </div>
                    </div>
                    <div className="preview-card">
                      <div className="card-icon">👥</div>
                      <div className="card-info">
                        <div className="card-title">العملاء الجدد</div>
                        <div className="card-value">45 عميل</div>
                      </div>
                    </div>
                  </div>
                  <div className="preview-chart">
                    <div className="chart-bars">
                      <div className="bar" style={{height: '60%'}}></div>
                      <div className="bar" style={{height: '80%'}}></div>
                      <div className="bar" style={{height: '45%'}}></div>
                      <div className="bar" style={{height: '90%'}}></div>
                      <div className="bar" style={{height: '70%'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* قسم المميزات */}
      <section id="features" className="features-section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">مميزات النظام</h2>
            <p className="section-description">
              اكتشف الميزات المتقدمة التي تجعل نظامنا الخيار الأمثل لشركتك
            </p>
          </div>
          
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">{feature.icon}</div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-description">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* قسم عن النظام */}
      <section id="about" className="about-section">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2 className="about-title">لماذا تختار نظام شراء السفر؟</h2>
              <p className="about-description">
                نحن نفهم تحديات صناعة السفر والسياحة، ولذلك طورنا نظاماً متكاملاً 
                يلبي جميع احتياجاتك من إدارة الحجوزات إلى التقارير المالية المتقدمة.
              </p>
              
              <div className="about-features">
                <div className="about-feature">
                  <span className="feature-check">✅</span>
                  <span>سهولة الاستخدام والتعلم السريع</span>
                </div>
                <div className="about-feature">
                  <span className="feature-check">✅</span>
                  <span>دعم فني متاح 24/7</span>
                </div>
                <div className="about-feature">
                  <span className="feature-check">✅</span>
                  <span>تحديثات مستمرة ومجانية</span>
                </div>
                <div className="about-feature">
                  <span className="feature-check">✅</span>
                  <span>أمان وحماية عالية للبيانات</span>
                </div>
              </div>
              
              <button 
                className="btn-about"
                onClick={() => navigate('/login')}
              >
                ابدأ تجربتك المجانية
              </button>
            </div>
            
            <div className="about-visual">
              <div className="visual-container">
                <div className="visual-card card-1">
                  <div className="card-header">
                    <span className="card-icon">📊</span>
                    <span className="card-title">تقارير مفصلة</span>
                  </div>
                  <div className="card-content">
                    <div className="progress-bar">
                      <div className="progress" style={{width: '85%'}}></div>
                    </div>
                    <span className="progress-text">85% نمو في المبيعات</span>
                  </div>
                </div>
                
                <div className="visual-card card-2">
                  <div className="card-header">
                    <span className="card-icon">🎯</span>
                    <span className="card-title">إدارة الحجوزات</span>
                  </div>
                  <div className="card-content">
                    <div className="booking-items">
                      <div className="booking-item">✈️ رحلة الرياض - جدة</div>
                      <div className="booking-item">🏨 فندق الحرم المكي</div>
                      <div className="booking-item">🚌 نقل VIP</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* قسم آراء العملاء */}
      <section className="testimonials-section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">ماذا يقول عملاؤنا</h2>
            <p className="section-description">
              تجارب حقيقية من عملائنا الذين حققوا نجاحاً باستخدام نظامنا
            </p>
          </div>
          
          <div className="testimonials-slider">
            <div className="testimonial-card">
              <div className="testimonial-content">
                <div className="testimonial-text">
                  "{testimonials[currentSlide].text}"
                </div>
                <div className="testimonial-rating">
                  {[...Array(testimonials[currentSlide].rating)].map((_, i) => (
                    <span key={i} className="star">⭐</span>
                  ))}
                </div>
                <div className="testimonial-author">
                  <div className="author-name">{testimonials[currentSlide].name}</div>
                  <div className="author-company">{testimonials[currentSlide].company}</div>
                </div>
              </div>
            </div>
            
            <div className="slider-dots">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  className={`dot ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => setCurrentSlide(index)}
                ></button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* قسم الاتصال */}
      <section id="contact" className="contact-section">
        <div className="container">
          <div className="contact-content">
            <div className="contact-info">
              <h2 className="contact-title">تواصل معنا</h2>
              <p className="contact-description">
                فريقنا جاهز لمساعدتك في بدء رحلتك مع نظام شراء السفر
              </p>
              
              <div className="contact-items">
                <div className="contact-item">
                  <span className="contact-icon">📞</span>
                  <div className="contact-details">
                    <div className="contact-label">الهاتف</div>
                    <div className="contact-value">+966 50 123 4567</div>
                  </div>
                </div>
                
                <div className="contact-item">
                  <span className="contact-icon">📧</span>
                  <div className="contact-details">
                    <div className="contact-label">البريد الإلكتروني</div>
                    <div className="contact-value"><EMAIL></div>
                  </div>
                </div>
                
                <div className="contact-item">
                  <span className="contact-icon">🌐</span>
                  <div className="contact-details">
                    <div className="contact-label">الموقع الإلكتروني</div>
                    <div className="contact-value">www.sharaubtravelsoft.com</div>
                  </div>
                </div>
                
                <div className="contact-item">
                  <span className="contact-icon">📍</span>
                  <div className="contact-details">
                    <div className="contact-label">العنوان</div>
                    <div className="contact-value">الرياض، المملكة العربية السعودية</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="contact-form">
              <form className="form">
                <div className="form-group">
                  <input type="text" placeholder="الاسم الكامل" className="form-input" />
                </div>
                <div className="form-group">
                  <input type="email" placeholder="البريد الإلكتروني" className="form-input" />
                </div>
                <div className="form-group">
                  <input type="tel" placeholder="رقم الهاتف" className="form-input" />
                </div>
                <div className="form-group">
                  <textarea placeholder="رسالتك" className="form-textarea" rows="4"></textarea>
                </div>
                <button type="submit" className="btn-form">
                  <span className="btn-icon">📤</span>
                  إرسال الرسالة
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* التذييل */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <div className="footer-logo">
                <span className="logo-icon">✈️</span>
                <span className="logo-text">شراء السفر</span>
              </div>
              <p className="footer-description">
                نظام متكامل لإدارة شركات السفر والسياحة مع أحدث التقنيات
                وأفضل معايير الأمان والجودة.
              </p>
            </div>
            
            <div className="footer-section">
              <h3 className="footer-title">روابط سريعة</h3>
              <ul className="footer-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">المميزات</a></li>
                <li><a href="#about">عن النظام</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
              </ul>
            </div>
            
            <div className="footer-section">
              <h3 className="footer-title">الخدمات</h3>
              <ul className="footer-links">
                <li><a href="#">إدارة الحجوزات</a></li>
                <li><a href="#">النظام المحاسبي</a></li>
                <li><a href="#">التقارير والإحصائيات</a></li>
                <li><a href="#">الدعم الفني</a></li>
              </ul>
            </div>
            
            <div className="footer-section">
              <h3 className="footer-title">تابعنا</h3>
              <div className="social-links">
                <a href="#" className="social-link">📘</a>
                <a href="#" className="social-link">📷</a>
                <a href="#" className="social-link">🐦</a>
                <a href="#" className="social-link">💼</a>
              </div>
            </div>
          </div>
          
          <div className="footer-bottom">
            <p>© 2024 شراء السفر. جميع الحقوق محفوظة.</p>
            <div className="footer-bottom-links">
              <a href="#">سياسة الخصوصية</a>
              <a href="#">شروط الاستخدام</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;