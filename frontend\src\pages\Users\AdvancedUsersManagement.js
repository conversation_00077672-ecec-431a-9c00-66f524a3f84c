import React, { useState, useEffect, useMemo } from 'react';
import './AdvancedUsersManagement.css';

const AdvancedUsersManagement = () => {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    role: 'all',
    status: 'all',
    department: 'all',
    lastLogin: 'all',
    permissions: 'all'
  });
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(12);
  const [viewMode, setViewMode] = useState('grid'); // grid, table, kanban
  const [showUserModal, setShowUserModal] = useState(false);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [bulkAction, setBulkAction] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  // نظام الأذونات المتقدم
  const permissionsSystem = {
    // أذونات النظام العامة
    system: {
      name: 'النظام العام',
      icon: '⚙️',
      color: '#e74c3c',
      permissions: {
        'system.admin': { name: 'إدارة النظام', level: 'admin', critical: true },
        'system.settings': { name: 'إعدادات النظام', level: 'admin', critical: true },
        'system.backup': { name: 'النسخ الاحتياطي', level: 'admin', critical: true },
        'system.logs': { name: 'سجلات النظام', level: 'manager', critical: false },
        'system.maintenance': { name: 'صيانة النظام', level: 'admin', critical: true }
      }
    },
    // أذونات المستخدمين
    users: {
      name: 'إدارة المستخدمين',
      icon: '👥',
      color: '#3498db',
      permissions: {
        'users.view': { name: 'عرض المستخدمين', level: 'employee', critical: false },
        'users.create': { name: 'إضافة مستخدمين', level: 'admin', critical: true },
        'users.edit': { name: 'تعديل المستخدمين', level: 'manager', critical: true },
        'users.delete': { name: 'حذف المستخدمين', level: 'admin', critical: true },
        'users.permissions': { name: 'إدارة الأذونات', level: 'admin', critical: true },
        'users.roles': { name: 'إدارة الأدوار', level: 'admin', critical: true },
        'users.bulk': { name: 'العمليات المجمعة', level: 'manager', critical: true },
        'users.export': { name: 'تصدير البيانات', level: 'manager', critical: false },
        'users.import': { name: 'استيراد البيانات', level: 'admin', critical: true }
      }
    },
    // أذونات المبيعات
    sales: {
      name: 'إدارة المبيعات',
      icon: '💰',
      color: '#f39c12',
      permissions: {
        'sales.view': { name: 'عرض المبيعات', level: 'employee', critical: false },
        'sales.create': { name: 'إنشاء مبيعات', level: 'employee', critical: false },
        'sales.edit': { name: 'تعديل المبيعات', level: 'manager', critical: true },
        'sales.delete': { name: 'حذف المبيعات', level: 'admin', critical: true },
        'sales.approve': { name: 'اعتماد المبيعات', level: 'manager', critical: true },
        'sales.reports': { name: 'تقارير المبيعات', level: 'manager', critical: false },
        'sales.analytics': { name: 'تحليلات المبيعات', level: 'manager', critical: false },
        'sales.commission': { name: 'إدارة العمولات', level: 'admin', critical: true },
        'sales.pricing': { name: 'إدارة الأسعار', level: 'admin', critical: true }
      }
    },
    // أذونات العملاء
    customers: {
      name: 'إدارة العملاء',
      icon: '👥',
      color: '#27ae60',
      permissions: {
        'customers.view': { name: 'عرض العملاء', level: 'employee', critical: false },
        'customers.create': { name: 'إضافة عملاء', level: 'employee', critical: false },
        'customers.edit': { name: 'تعديل العملاء', level: 'employee', critical: false },
        'customers.delete': { name: 'حذف العملاء', level: 'manager', critical: true },
        'customers.sensitive': { name: 'البيانات الحساسة', level: 'manager', critical: true },
        'customers.history': { name: 'تاريخ العملاء', level: 'employee', critical: false },
        'customers.communication': { name: 'التواصل مع العملاء', level: 'employee', critical: false },
        'customers.export': { name: 'تصدير بيانات العملاء', level: 'manager', critical: true }
      }
    },
    // أذونات الحجوزات
    bookings: {
      name: 'إدارة الحجوزات',
      icon: '📋',
      color: '#9b59b6',
      permissions: {
        'bookings.view': { name: 'عرض الحجوزات', level: 'employee', critical: false },
        'bookings.create': { name: 'إنشاء حجوزات', level: 'employee', critical: false },
        'bookings.edit': { name: 'تعديل الحجوزات', level: 'employee', critical: false },
        'bookings.cancel': { name: 'إلغاء الحجوزات', level: 'manager', critical: true },
        'bookings.confirm': { name: 'تأكيد الحجوزات', level: 'manager', critical: true },
        'bookings.refund': { name: 'استرداد الحجوزات', level: 'admin', critical: true },
        'bookings.modify': { name: 'تعديل متقدم', level: 'manager', critical: true },
        'bookings.reports': { name: 'تقارير الحجوزات', level: 'manager', critical: false }
      }
    },
    // أذونات المالية
    finance: {
      name: 'إدارة المالية',
      icon: '💳',
      color: '#e67e22',
      permissions: {
        'finance.view': { name: 'عرض المالية', level: 'manager', critical: false },
        'finance.transactions': { name: 'المعاملات المالية', level: 'manager', critical: true },
        'finance.invoices': { name: 'إدارة الفواتير', level: 'employee', critical: false },
        'finance.payments': { name: 'إدارة المدفوعات', level: 'manager', critical: true },
        'finance.refunds': { name: 'المبالغ المستردة', level: 'admin', critical: true },
        'finance.reports': { name: 'التقارير المالية', level: 'manager', critical: false },
        'finance.audit': { name: 'مراجعة مالية', level: 'admin', critical: true },
        'finance.budget': { name: 'إدارة الميزانية', level: 'admin', critical: true }
      }
    },
    // أذونات التقارير
    reports: {
      name: 'التقارير والتحليلات',
      icon: '📊',
      color: '#1abc9c',
      permissions: {
        'reports.view': { name: 'عرض التقارير', level: 'employee', critical: false },
        'reports.create': { name: 'إنشاء تقارير', level: 'manager', critical: false },
        'reports.export': { name: 'تصدير التقارير', level: 'manager', critical: false },
        'reports.advanced': { name: 'تقارير متقدمة', level: 'admin', critical: false },
        'reports.analytics': { name: 'التحليلات المتقدمة', level: 'admin', critical: false },
        'reports.dashboard': { name: 'لوحة التحكم', level: 'employee', critical: false },
        'reports.realtime': { name: 'التقارير الفورية', level: 'manager', critical: false }
      }
    }
  };

  // بيانات المستخدمين المتقدمة
  const initialUsers = [
    {
      id: 1,
      name: 'أحمد محمد الأحمد',
      email: '<EMAIL>',
      phone: '+966501234567',
      avatar: 'https://ui-avatars.com/api/?name=أحمد+محمد&background=667eea&color=fff&size=100',
      role: 'super_admin',
      department: 'الإدارة العامة',
      position: 'المدير العام',
      status: 'active',
      lastLogin: '2024-01-15T10:30:00',
      createdAt: '2023-01-01',
      updatedAt: '2024-01-15',
      loginCount: 1250,
      location: 'الرياض، السعودية',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: true,
      permissions: ['system.*', 'users.*', 'sales.*', 'customers.*', 'bookings.*', 'finance.*', 'reports.*'],
      customPermissions: [],
      sessionTimeout: 480, // minutes
      ipRestrictions: [],
      workingHours: { start: '08:00', end: '17:00' },
      tags: ['مدير', 'أساسي', 'مهم'],
      notes: 'المدير العام للشركة - صلاحيات كاملة'
    },
    {
      id: 2,
      name: 'فاطمة علي السالم',
      email: '<EMAIL>',
      phone: '+966507654321',
      avatar: 'https://ui-avatars.com/api/?name=فاطمة+علي&background=f39c12&color=fff&size=100',
      role: 'sales_manager',
      department: 'المبيعات',
      position: 'مدير المبيعات',
      status: 'active',
      lastLogin: '2024-01-15T09:15:00',
      createdAt: '2023-02-15',
      updatedAt: '2024-01-14',
      loginCount: 890,
      location: 'جدة، السعودية',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: true,
      permissions: [
        'users.view', 'users.export',
        'sales.*',
        'customers.*',
        'bookings.view', 'bookings.create', 'bookings.edit', 'bookings.confirm',
        'finance.view', 'finance.invoices', 'finance.payments',
        'reports.view', 'reports.create', 'reports.export'
      ],
      customPermissions: ['sales.special_discount'],
      sessionTimeout: 360,
      ipRestrictions: ['***********/24'],
      workingHours: { start: '09:00', end: '18:00' },
      tags: ['مبيعات', 'مدير', 'موثوق'],
      notes: 'مدير المبيعات الرئيسي - خبرة 5 سنوات'
    },
    {
      id: 3,
      name: 'محمد عبدالله القحطاني',
      email: '<EMAIL>',
      phone: '+966509876543',
      avatar: 'https://ui-avatars.com/api/?name=محمد+عبدالله&background=3498db&color=fff&size=100',
      role: 'booking_supervisor',
      department: 'الحجوزات',
      position: 'مشرف الحجوزات',
      status: 'active',
      lastLogin: '2024-01-14T16:45:00',
      createdAt: '2023-03-10',
      updatedAt: '2024-01-12',
      loginCount: 654,
      location: 'الدمام، السعودية',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: false,
      permissions: [
        'users.view',
        'customers.view', 'customers.create', 'customers.edit', 'customers.history',
        'bookings.*',
        'finance.view', 'finance.invoices',
        'reports.view', 'reports.dashboard'
      ],
      customPermissions: [],
      sessionTimeout: 240,
      ipRestrictions: [],
      workingHours: { start: '08:30', end: '17:30' },
      tags: ['حجوزات', 'مشرف'],
      notes: 'مشرف قسم الحجوزات - متخصص في الحج والعمرة'
    },
    {
      id: 4,
      name: 'نورا سعد الغامدي',
      email: '<EMAIL>',
      phone: '+966502468135',
      avatar: 'https://ui-avatars.com/api/?name=نورا+سعد&background=27ae60&color=fff&size=100',
      role: 'customer_service',
      department: 'خدمة العملاء',
      position: 'موظفة خدمة عملاء',
      status: 'active',
      lastLogin: '2024-01-15T08:20:00',
      createdAt: '2023-04-20',
      updatedAt: '2024-01-10',
      loginCount: 432,
      location: 'مكة المكرمة، السعودية',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: false,
      permissions: [
        'customers.view', 'customers.create', 'customers.edit', 'customers.history', 'customers.communication',
        'bookings.view', 'bookings.create', 'bookings.edit',
        'finance.view', 'finance.invoices',
        'reports.view', 'reports.dashboard'
      ],
      customPermissions: [],
      sessionTimeout: 180,
      ipRestrictions: [],
      workingHours: { start: '09:00', end: '17:00' },
      tags: ['خدمة عملاء', 'موظف'],
      notes: 'موظفة خدمة عملاء متميزة - تتحدث الإنجليزية'
    },
    {
      id: 5,
      name: 'خالد أحمد البراك',
      email: '<EMAIL>',
      phone: '+************',
      avatar: 'https://ui-avatars.com/api/?name=خالد+أحمد&background=95a5a6&color=fff&size=100',
      role: 'accountant',
      department: 'المحاسبة',
      position: 'محاسب',
      status: 'inactive',
      lastLogin: '2024-01-10T14:30:00',
      createdAt: '2023-05-15',
      updatedAt: '2024-01-08',
      loginCount: 298,
      location: 'الرياض، السعودية',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: true,
      permissions: [
        'customers.view',
        'bookings.view',
        'finance.*',
        'reports.view', 'reports.create', 'reports.export'
      ],
      customPermissions: [],
      sessionTimeout: 300,
      ipRestrictions: ['10.0.0.0/8'],
      workingHours: { start: '08:00', end: '16:00' },
      tags: ['محاسبة', 'معطل'],
      notes: 'محاسب معطل مؤقتاً - في إجازة'
    },
    {
      id: 6,
      name: 'سارة محمد الزهراني',
      email: '<EMAIL>',
      phone: '+************',
      avatar: 'https://ui-avatars.com/api/?name=سارة+محمد&background=9b59b6&color=fff&size=100',
      role: 'marketing_manager',
      department: 'التسويق',
      position: 'مدير التسويق',
      status: 'active',
      lastLogin: '2024-01-15T11:00:00',
      createdAt: '2023-06-01',
      updatedAt: '2024-01-13',
      loginCount: 567,
      location: 'الرياض، السعودية',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: true,
      permissions: [
        'users.view',
        'customers.view', 'customers.create', 'customers.edit', 'customers.communication', 'customers.export',
        'sales.view', 'sales.reports', 'sales.analytics',
        'bookings.view',
        'reports.*'
      ],
      customPermissions: ['marketing.campaigns', 'marketing.analytics'],
      sessionTimeout: 360,
      ipRestrictions: [],
      workingHours: { start: '09:00', end: '18:00' },
      tags: ['تسويق', 'مدير', 'إبداعي'],
      notes: 'مدير التسويق - متخصص في التسويق الرقمي'
    }
  ];

  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'customer_service',
    department: '',
    position: '',
    status: 'active',
    location: '',
    timezone: 'Asia/Riyadh',
    language: 'ar',
    twoFactorEnabled: false,
    permissions: [],
    customPermissions: [],
    sessionTimeout: 240,
    ipRestrictions: [],
    workingHours: { start: '09:00', end: '17:00' },
    tags: [],
    notes: ''
  });

  // الأدوار المتقدمة
  const roles = {
    super_admin: {
      name: 'مدير عام',
      color: '#e74c3c',
      icon: '👑',
      level: 'admin',
      description: 'صلاحيات كاملة على النظام',
      defaultPermissions: ['system.*', 'users.*', 'sales.*', 'customers.*', 'bookings.*', 'finance.*', 'reports.*']
    },
    sales_manager: {
      name: 'مدير مبيعات',
      color: '#f39c12',
      icon: '💼',
      level: 'manager',
      description: 'إدارة المبيعات والعملاء',
      defaultPermissions: ['sales.*', 'customers.*', 'bookings.view', 'bookings.create', 'reports.view']
    },
    booking_supervisor: {
      name: 'مشرف حجوزات',
      color: '#3498db',
      icon: '📋',
      level: 'manager',
      description: 'إشراف على الحجوزات',
      defaultPermissions: ['bookings.*', 'customers.view', 'customers.edit', 'reports.view']
    },
    customer_service: {
      name: 'خدمة عملاء',
      color: '#27ae60',
      icon: '🎧',
      level: 'employee',
      description: 'خدمة ودعم العملاء',
      defaultPermissions: ['customers.view', 'customers.edit', 'bookings.view', 'bookings.create']
    },
    accountant: {
      name: 'محاسب',
      color: '#e67e22',
      icon: '💰',
      level: 'manager',
      description: 'إدارة الشؤون المالية',
      defaultPermissions: ['finance.*', 'reports.view', 'reports.create']
    },
    marketing_manager: {
      name: 'مدير تسويق',
      color: '#9b59b6',
      icon: '📈',
      level: 'manager',
      description: 'إدارة التسويق والحملات',
      defaultPermissions: ['customers.view', 'customers.export', 'reports.*', 'sales.view']
    }
  };

  const departments = [
    'الإدارة العامة',
    'المبيعات',
    'الحجوزات',
    'خدمة العملاء',
    'المحاسبة',
    'التسويق',
    'تقنية المعلومات',
    'الموارد البشرية',
    'العمليات',
    'الجودة'
  ];

  const timezones = [
    { value: 'Asia/Riyadh', label: 'الرياض (GMT+3)' },
    { value: 'Asia/Dubai', label: 'دبي (GMT+4)' },
    { value: 'Asia/Kuwait', label: 'الكويت (GMT+3)' },
    { value: 'Asia/Qatar', label: 'قطر (GMT+3)' },
    { value: 'Asia/Bahrain', label: 'البحرين (GMT+3)' }
  ];

  useEffect(() => {
    setUsers(initialUsers);
    setFilteredUsers(initialUsers);
  }, []);

  // تصفية وبحث متقدم
  useEffect(() => {
    let filtered = users.filter(user => {
      const matchesSearch = 
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesRole = filters.role === 'all' || user.role === filters.role;
      const matchesStatus = filters.status === 'all' || user.status === filters.status;
      const matchesDepartment = filters.department === 'all' || user.department === filters.department;
      
      const matchesLastLogin = filters.lastLogin === 'all' || (() => {
        const lastLogin = new Date(user.lastLogin);
        const now = new Date();
        const diffDays = Math.floor((now - lastLogin) / (1000 * 60 * 60 * 24));
        
        switch (filters.lastLogin) {
          case 'today': return diffDays === 0;
          case 'week': return diffDays <= 7;
          case 'month': return diffDays <= 30;
          case 'old': return diffDays > 30;
          default: return true;
        }
      })();

      const matchesPermissions = filters.permissions === 'all' || (() => {
        switch (filters.permissions) {
          case 'admin': return user.permissions.some(p => p.includes('admin') || p.includes('*'));
          case 'limited': return !user.permissions.some(p => p.includes('*'));
          case 'custom': return user.customPermissions.length > 0;
          default: return true;
        }
      })();

      return matchesSearch && matchesRole && matchesStatus && matchesDepartment && matchesLastLogin && matchesPermissions;
    });

    // ترتيب النتائج
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];
        
        if (sortConfig.key === 'lastLogin' || sortConfig.key === 'createdAt') {
          aValue = new Date(aValue);
          bValue = new Date(bValue);
        }
        
        if (sortConfig.direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    setFilteredUsers(filtered);
    setCurrentPage(1);
  }, [users, searchTerm, filters, sortConfig]);

  // حساب الصفحات
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  // إحصائيات متقدمة
  const stats = useMemo(() => {
    const total = users.length;
    const active = users.filter(u => u.status === 'active').length;
    const inactive = users.filter(u => u.status === 'inactive').length;
    const admins = users.filter(u => roles[u.role]?.level === 'admin').length;
    const managers = users.filter(u => roles[u.role]?.level === 'manager').length;
    const employees = users.filter(u => roles[u.role]?.level === 'employee').length;
    const twoFactorEnabled = users.filter(u => u.twoFactorEnabled).length;
    const recentLogins = users.filter(u => {
      const lastLogin = new Date(u.lastLogin);
      const now = new Date();
      return (now - lastLogin) / (1000 * 60 * 60 * 24) <= 7;
    }).length;

    return {
      total,
      active,
      inactive,
      admins,
      managers,
      employees,
      twoFactorEnabled,
      recentLogins,
      activePercentage: total > 0 ? Math.round((active / total) * 100) : 0,
      securityScore: total > 0 ? Math.round((twoFactorEnabled / total) * 100) : 0
    };
  }, [users]);

  // وظائف إدارة المستخدمين
  const handleAddUser = () => {
    if (newUser.password !== newUser.confirmPassword) {
      alert('كلمات المرور غير متطابقة');
      return;
    }

    const user = {
      id: users.length + 1,
      ...newUser,
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(newUser.name)}&background=667eea&color=fff&size=100`,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      lastLogin: null,
      loginCount: 0,
      permissions: newUser.permissions.length > 0 ? newUser.permissions : roles[newUser.role]?.defaultPermissions || []
    };

    setUsers([...users, user]);
    setShowUserModal(false);
    resetNewUser();
  };

  const handleEditUser = () => {
    const updatedUsers = users.map(user => 
      user.id === selectedUser.id 
        ? { ...selectedUser, updatedAt: new Date().toISOString().split('T')[0] }
        : user
    );
    setUsers(updatedUsers);
    setShowUserModal(false);
    setSelectedUser(null);
  };

  const handleDeleteUser = (userId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      const updatedUsers = users.filter(user => user.id !== userId);
      setUsers(updatedUsers);
    }
  };

  const toggleUserStatus = (userId) => {
    const updatedUsers = users.map(user => 
      user.id === userId 
        ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' }
        : user
    );
    setUsers(updatedUsers);
  };

  const resetNewUser = () => {
    setNewUser({
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      role: 'customer_service',
      department: '',
      position: '',
      status: 'active',
      location: '',
      timezone: 'Asia/Riyadh',
      language: 'ar',
      twoFactorEnabled: false,
      permissions: [],
      customPermissions: [],
      sessionTimeout: 240,
      ipRestrictions: [],
      workingHours: { start: '09:00', end: '17:00' },
      tags: [],
      notes: ''
    });
  };

  // وظائف الأذونات
  const hasPermission = (user, permission) => {
    return user.permissions.some(p => {
      if (p === permission) return true;
      if (p.endsWith('.*')) {
        const prefix = p.slice(0, -2);
        return permission.startsWith(prefix + '.');
      }
      return false;
    });
  };

  const togglePermission = (permission) => {
    if (selectedUser) {
      const hasCurrentPermission = selectedUser.permissions.includes(permission);
      const newPermissions = hasCurrentPermission
        ? selectedUser.permissions.filter(p => p !== permission)
        : [...selectedUser.permissions, permission];
      
      setSelectedUser({
        ...selectedUser,
        permissions: newPermissions
      });
    }
  };

  const getPermissionLevel = (permission) => {
    for (const category of Object.values(permissionsSystem)) {
      if (category.permissions[permission]) {
        return category.permissions[permission].level;
      }
    }
    return 'employee';
  };

  const isPermissionCritical = (permission) => {
    for (const category of Object.values(permissionsSystem)) {
      if (category.permissions[permission]) {
        return category.permissions[permission].critical;
      }
    }
    return false;
  };

  // عرض المكونات
  const renderUserCard = (user) => (
    <div key={user.id} className={`user-card ${selectedUsers.includes(user.id) ? 'selected' : ''}`}>
      <div className="user-card-header">
        <input
          type="checkbox"
          checked={selectedUsers.includes(user.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedUsers([...selectedUsers, user.id]);
            } else {
              setSelectedUsers(selectedUsers.filter(id => id !== user.id));
            }
          }}
          className="user-checkbox"
        />
        <div className="user-status-indicator">
          <span className={`status-dot ${user.status}`}></span>
        </div>
      </div>

      <div className="user-avatar-section">
        <img src={user.avatar} alt={user.name} className="user-avatar" />
        <div className="user-role-badge" style={{ backgroundColor: roles[user.role]?.color }}>
          {roles[user.role]?.icon}
        </div>
      </div>

      <div className="user-info-section">
        <h3 className="user-name">{user.name}</h3>
        <p className="user-position">{user.position}</p>
        <p className="user-department">{user.department}</p>
        <p className="user-email">{user.email}</p>
      </div>

      <div className="user-stats-section">
        <div className="user-stat">
          <span className="stat-icon">🔐</span>
          <span className="stat-value">{user.permissions.length}</span>
          <span className="stat-label">صلاحية</span>
        </div>
        <div className="user-stat">
          <span className="stat-icon">📅</span>
          <span className="stat-value">{user.loginCount}</span>
          <span className="stat-label">دخول</span>
        </div>
        <div className="user-stat">
          <span className="stat-icon">🌍</span>
          <span className="stat-value">{user.location.split('،')[0]}</span>
          <span className="stat-label">الموقع</span>
        </div>
      </div>

      <div className="user-security-section">
        {user.twoFactorEnabled && (
          <span className="security-badge">
            <span className="badge-icon">🔒</span>
            2FA
          </span>
        )}
        {user.ipRestrictions.length > 0 && (
          <span className="security-badge">
            <span className="badge-icon">🌐</span>
            IP محدود
          </span>
        )}
      </div>

      <div className="user-tags-section">
        {user.tags.map((tag, index) => (
          <span key={index} className="user-tag">{tag}</span>
        ))}
      </div>

      <div className="user-actions-section">
        <button
          className="action-btn view"
          onClick={() => {
            setSelectedUser(user);
            setActiveTab('overview');
            setShowUserModal(true);
          }}
          title="عرض التفاصيل"
        >
          👁️
        </button>
        <button
          className="action-btn permissions"
          onClick={() => {
            setSelectedUser({...user});
            setShowPermissionsModal(true);
          }}
          title="إدارة الأذونات"
        >
          🔐
        </button>
        <button
          className="action-btn edit"
          onClick={() => {
            setSelectedUser({...user});
            setActiveTab('basic');
            setShowUserModal(true);
          }}
          title="تعديل"
        >
          ✏️
        </button>
        <button
          className="action-btn status"
          onClick={() => toggleUserStatus(user.id)}
          title={user.status === 'active' ? 'تعطيل' : 'تفعيل'}
        >
          {user.status === 'active' ? '⏸️' : '▶️'}
        </button>
        <button
          className="action-btn delete"
          onClick={() => handleDeleteUser(user.id)}
          title="حذف"
        >
          🗑️
        </button>
      </div>

      <div className="user-last-login">
        <span className="last-login-label">آخر دخول:</span>
        <span className="last-login-time">
          {user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول'}
        </span>
      </div>
    </div>
  );

  const renderPermissionsModal = () => (
    <div className="modal-overlay">
      <div className="modal permissions-modal">
        <div className="modal-header">
          <h3>إدارة أذونات المستخدم</h3>
          <div className="user-info-header">
            <img src={selectedUser?.avatar} alt={selectedUser?.name} className="user-avatar-small" />
            <div>
              <div className="user-name-small">{selectedUser?.name}</div>
              <div className="user-role-small">{roles[selectedUser?.role]?.name}</div>
            </div>
          </div>
          <button 
            className="modal-close"
            onClick={() => setShowPermissionsModal(false)}
          >
            ✕
          </button>
        </div>
        
        <div className="modal-body permissions-body">
          <div className="permissions-summary">
            <div className="summary-stat">
              <span className="summary-number">{selectedUser?.permissions.length || 0}</span>
              <span className="summary-label">إجمالي الأذونات</span>
            </div>
            <div className="summary-stat">
              <span className="summary-number">{selectedUser?.customPermissions.length || 0}</span>
              <span className="summary-label">أذونات مخصصة</span>
            </div>
            <div className="summary-stat">
              <span className="summary-number">
                {selectedUser?.permissions.filter(p => isPermissionCritical(p)).length || 0}
              </span>
              <span className="summary-label">أذونات حساسة</span>
            </div>
          </div>

          <div className="permissions-categories">
            {Object.entries(permissionsSystem).map(([categoryKey, category]) => (
              <div key={categoryKey} className="permission-category">
                <div className="category-header">
                  <span 
                    className="category-icon"
                    style={{ backgroundColor: category.color }}
                  >
                    {category.icon}
                  </span>
                  <h4 className="category-name">{category.name}</h4>
                  <div className="category-stats">
                    {Object.keys(category.permissions).filter(p => 
                      selectedUser?.permissions.includes(p) || 
                      selectedUser?.permissions.some(up => up.endsWith('.*') && p.startsWith(up.slice(0, -2)))
                    ).length} / {Object.keys(category.permissions).length}
                  </div>
                </div>

                <div className="permissions-grid">
                  {Object.entries(category.permissions).map(([permissionKey, permission]) => {
                    const isGranted = hasPermission(selectedUser, permissionKey);
                    const level = permission.level;
                    const isCritical = permission.critical;

                    return (
                      <div 
                        key={permissionKey} 
                        className={`permission-item ${isGranted ? 'granted' : ''} ${isCritical ? 'critical' : ''}`}
                      >
                        <div className="permission-header">
                          <label className="permission-label">
                            <input
                              type="checkbox"
                              checked={isGranted}
                              onChange={() => togglePermission(permissionKey)}
                              className="permission-checkbox"
                            />
                            <span className="permission-name">{permission.name}</span>
                          </label>
                          <div className="permission-badges">
                            <span className={`level-badge ${level}`}>
                              {level === 'admin' ? '👑' : level === 'manager' ? '👨‍💼' : '👤'}
                            </span>
                            {isCritical && (
                              <span className="critical-badge" title="إذن حساس">
                                ⚠️
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="permission-key">{permissionKey}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          <div className="custom-permissions-section">
            <h4>الأذونات المخصصة</h4>
            <div className="custom-permissions-list">
              {selectedUser?.customPermissions.map((permission, index) => (
                <div key={index} className="custom-permission-item">
                  <span className="custom-permission-name">{permission}</span>
                  <button
                    className="remove-custom-permission"
                    onClick={() => {
                      const newCustomPermissions = selectedUser.customPermissions.filter((_, i) => i !== index);
                      setSelectedUser({
                        ...selectedUser,
                        customPermissions: newCustomPermissions
                      });
                    }}
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
            <div className="add-custom-permission">
              <input
                type="text"
                placeholder="إضافة إذن مخصص..."
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && e.target.value.trim()) {
                    const newCustomPermissions = [...(selectedUser?.customPermissions || []), e.target.value.trim()];
                    setSelectedUser({
                      ...selectedUser,
                      customPermissions: newCustomPermissions
                    });
                    e.target.value = '';
                  }
                }}
                className="custom-permission-input"
              />
            </div>
          </div>
        </div>
        
        <div className="modal-footer">
          <button 
            className="btn-cancel"
            onClick={() => setShowPermissionsModal(false)}
          >
            إلغاء
          </button>
          <button 
            className="btn-save"
            onClick={() => {
              handleEditUser();
              setShowPermissionsModal(false);
            }}
          >
            حفظ الأذونات
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="advanced-users-management">
      {/* رأس الصفحة المتقدم */}
      <div className="page-header-advanced">
        <div className="header-content">
          <div className="header-text">
            <h1 className="page-title">
              <span className="title-icon">👥</span>
              إدارة المستخدمين المتقدمة
            </h1>
            <p className="page-description">
              نظام إدارة مستخدمين متطور مع تحكم دقيق في الأذونات والصلاحيات
            </p>
          </div>
          <div className="header-actions">
            <button 
              className="btn-add-user"
              onClick={() => {
                resetNewUser();
                setSelectedUser(null);
                setActiveTab('basic');
                setShowUserModal(true);
              }}
            >
              <span className="btn-icon">➕</span>
              إضافة مستخدم جديد
            </button>
          </div>
        </div>

        {/* إحصائيات متقدمة */}
        <div className="advanced-stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">👥</div>
            <div className="stat-info">
              <div className="stat-number">{stats.total}</div>
              <div className="stat-label">إجمالي المستخدمين</div>
              <div className="stat-trend">+12% هذا الشهر</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">✅</div>
            <div className="stat-info">
              <div className="stat-number">{stats.active}</div>
              <div className="stat-label">مستخدمين نشطين</div>
              <div className="stat-percentage">{stats.activePercentage}%</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">👑</div>
            <div className="stat-info">
              <div className="stat-number">{stats.admins}</div>
              <div className="stat-label">مديرين</div>
              <div className="stat-trend">مستقر</div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">🔒</div>
            <div className="stat-info">
              <div className="stat-number">{stats.twoFactorEnabled}</div>
              <div className="stat-label">2FA مفعل</div>
              <div className="stat-percentage">{stats.securityScore}%</div>
            </div>
          </div>
          <div className="stat-card secondary">
            <div className="stat-icon">📅</div>
            <div className="stat-info">
              <div className="stat-number">{stats.recentLogins}</div>
              <div className="stat-label">دخول حديث</div>
              <div className="stat-trend">آخر 7 أيام</div>
            </div>
          </div>
        </div>
      </div>

      {/* أدوات التحكم المتقدمة */}
      <div className="advanced-controls">
        <div className="search-and-filters">
          <div className="search-box-advanced">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              placeholder="البحث في المستخدمين، الأقسام، المناصب، العلامات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input-advanced"
            />
            <button className="search-clear" onClick={() => setSearchTerm('')}>
              ✕
            </button>
          </div>

          <div className="filters-advanced">
            <select
              value={filters.role}
              onChange={(e) => setFilters({...filters, role: e.target.value})}
              className="filter-select"
            >
              <option value="all">جميع الأدوار</option>
              {Object.entries(roles).map(([key, role]) => (
                <option key={key} value={key}>{role.name}</option>
              ))}
            </select>

            <select
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
              className="filter-select"
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="inactive">معطل</option>
            </select>

            <select
              value={filters.department}
              onChange={(e) => setFilters({...filters, department: e.target.value})}
              className="filter-select"
            >
              <option value="all">جميع الأقسام</option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>

            <select
              value={filters.lastLogin}
              onChange={(e) => setFilters({...filters, lastLogin: e.target.value})}
              className="filter-select"
            >
              <option value="all">جميع تواريخ الدخول</option>
              <option value="today">اليوم</option>
              <option value="week">آخر أسبوع</option>
              <option value="month">آخر شهر</option>
              <option value="old">أكثر من شهر</option>
            </select>

            <select
              value={filters.permissions}
              onChange={(e) => setFilters({...filters, permissions: e.target.value})}
              className="filter-select"
            >
              <option value="all">جميع الأذونات</option>
              <option value="admin">أذونات إدارية</option>
              <option value="limited">أذونات محدودة</option>
              <option value="custom">أذونات مخصصة</option>
            </select>
          </div>
        </div>

        <div className="view-controls">
          <div className="view-mode-selector">
            <button
              className={`view-mode-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
              title="عرض شبكي"
            >
              ⊞
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => setViewMode('table')}
              title="عرض جدولي"
            >
              ☰
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'kanban' ? 'active' : ''}`}
              onClick={() => setViewMode('kanban')}
              title="عرض كانبان"
            >
              ⊡
            </button>
          </div>

          <div className="sort-controls">
            <select
              value={`${sortConfig.key}-${sortConfig.direction}`}
              onChange={(e) => {
                const [key, direction] = e.target.value.split('-');
                setSortConfig({ key, direction });
              }}
              className="sort-select"
            >
              <option value="name-asc">الاسم (أ-ي)</option>
              <option value="name-desc">الاسم (ي-أ)</option>
              <option value="lastLogin-desc">آخر دخول (الأحدث)</option>
              <option value="lastLogin-asc">آخر دخول (الأقدم)</option>
              <option value="createdAt-desc">تاريخ الإنشاء (الأحدث)</option>
              <option value="createdAt-asc">تاريخ الإنشاء (الأقدم)</option>
              <option value="loginCount-desc">عدد مرات الدخول (الأكثر)</option>
              <option value="loginCount-asc">عدد مرات الدخول (الأقل)</option>
            </select>
          </div>
        </div>
      </div>

      {/* العمليات المجمعة المتقدمة */}
      {selectedUsers.length > 0 && (
        <div className="bulk-actions-advanced">
          <div className="bulk-info">
            <span className="bulk-count">{selectedUsers.length}</span>
            مستخدم محدد
          </div>
          <div className="bulk-buttons">
            <button 
              className="bulk-btn activate"
              onClick={() => {
                setBulkAction('activate');
                setShowBulkModal(true);
              }}
            >
              <span className="btn-icon">✅</span>
              تفعيل
            </button>
            <button 
              className="bulk-btn deactivate"
              onClick={() => {
                setBulkAction('deactivate');
                setShowBulkModal(true);
              }}
            >
              <span className="btn-icon">⏸️</span>
              تعطيل
            </button>
            <button 
              className="bulk-btn permissions"
              onClick={() => {
                setBulkAction('permissions');
                setShowBulkModal(true);
              }}
            >
              <span className="btn-icon">🔐</span>
              تعديل الأذونات
            </button>
            <button 
              className="bulk-btn export"
              onClick={() => {
                setBulkAction('export');
                setShowBulkModal(true);
              }}
            >
              <span className="btn-icon">📤</span>
              تصدير
            </button>
            <button 
              className="bulk-btn delete"
              onClick={() => {
                setBulkAction('delete');
                setShowBulkModal(true);
              }}
            >
              <span className="btn-icon">🗑️</span>
              حذف
            </button>
          </div>
          <button 
            className="bulk-clear"
            onClick={() => setSelectedUsers([])}
          >
            إلغاء التحديد
          </button>
        </div>
      )}

      {/* عرض المستخدمين */}
      <div className="users-display">
        {viewMode === 'grid' && (
          <div className="users-grid-advanced">
            {currentUsers.map(renderUserCard)}
          </div>
        )}

        {filteredUsers.length === 0 && (
          <div className="no-users-advanced">
            <div className="no-users-icon">👥</div>
            <div className="no-users-text">لا توجد مستخدمين</div>
            <div className="no-users-description">
              {searchTerm || Object.values(filters).some(f => f !== 'all')
                ? 'لا توجد نتائج تطابق معايير البحث والتصفية'
                : 'لم يتم إضافة أي مستخدمين بعد'
              }
            </div>
            <button 
              className="btn-add-first-user"
              onClick={() => {
                resetNewUser();
                setSelectedUser(null);
                setActiveTab('basic');
                setShowUserModal(true);
              }}
            >
              إضافة أول مستخدم
            </button>
          </div>
        )}
      </div>

      {/* ترقيم الصفحات المتقدم */}
      {totalPages > 1 && (
        <div className="pagination-advanced">
          <div className="pagination-info">
            عرض {indexOfFirstUser + 1} - {Math.min(indexOfLastUser, filteredUsers.length)} من {filteredUsers.length} مستخدم
          </div>
          
          <div className="pagination-controls">
            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              الأول
            </button>
            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              السابق
            </button>
            
            <div className="pagination-numbers">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let page;
                if (totalPages <= 5) {
                  page = i + 1;
                } else if (currentPage <= 3) {
                  page = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  page = totalPages - 4 + i;
                } else {
                  page = currentPage - 2 + i;
                }
                
                return (
                  <button
                    key={page}
                    className={`pagination-number ${page === currentPage ? 'active' : ''}`}
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </button>
                );
              })}
            </div>
            
            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              التالي
            </button>
            <button
              className="pagination-btn"
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
            >
              الأخير
            </button>
          </div>
          
          <div className="pagination-size">
            <select
              value={usersPerPage}
              onChange={(e) => {
                setUsersPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="pagination-size-select"
            >
              <option value={6}>6 لكل صفحة</option>
              <option value={12}>12 لكل صفحة</option>
              <option value={24}>24 لكل صفحة</option>
              <option value={48}>48 لكل صفحة</option>
            </select>
          </div>
        </div>
      )}

      {/* نافذة إدارة الأذونات */}
      {showPermissionsModal && selectedUser && renderPermissionsModal()}

      {/* باقي النوافذ المنبثقة ستكون في الجزء التالي */}
    </div>
  );
};

export default AdvancedUsersManagement;