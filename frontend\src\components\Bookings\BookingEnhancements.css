/* تحسينات إضافية لمكونات الحجوزات */

/* تحسينات الجداول */
.booking-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.booking-table thead {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.booking-table th {
  padding: 15px;
  font-weight: bold;
  text-align: right;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.booking-table td {
  padding: 15px;
  border-bottom: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.booking-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات الأزرار */
.booking-action-button {
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  min-width: 36px;
  justify-content: center;
}

.booking-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.booking-action-button:active {
  transform: translateY(0);
}

/* ألوان الأزرار */
.action-view {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.action-edit {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
}

.action-delete {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

.action-print {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
  color: white;
}

.action-pdf {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  color: white;
}

/* تحسينات العمليات المجمعة */
.bulk-actions-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.bulk-actions-info {
  font-weight: bold;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bulk-actions-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* تحسينات النوافذ المنبثقة */
.booking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.booking-modal-content {
  background: white;
  border-radius: 20px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* تحسينات شارات الحالة */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* تحسينات ملخص الدفع */
.payment-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #dee2e6;
  border-radius: 10px;
  padding: 12px;
  margin: 8px 0;
}

.payment-progress-bar {
  width: 100%;
  height: 6px;
  background: #ecf0f1;
  border-radius: 3px;
  overflow: hidden;
  margin-top: 8px;
}

.payment-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
  transition: width 0.5s ease;
  border-radius: 3px;
}

/* تحسينات التحديد */
.selection-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #3498db;
  transform: scale(1.2);
}

.selection-checkbox:hover {
  transform: scale(1.3);
}

/* تحسينات البحث والفلترة */
.search-filter-container {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 15px rgba(0,0,0,0.08);
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.search-input {
  flex: 1;
  min-width: 250px;
  padding: 12px 16px;
  border: 2px solid #dee2e6;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #dee2e6;
  border-radius: 10px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .booking-table {
    font-size: 12px;
  }
  
  .booking-table th,
  .booking-table td {
    padding: 10px 8px;
  }
  
  .bulk-actions-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bulk-actions-buttons {
    justify-content: center;
  }
  
  .search-filter-container {
    flex-direction: column;
  }
  
  .search-input {
    min-width: 100%;
  }
}

/* تحسينات الطباعة */
@media print {
  .booking-actions,
  .bulk-actions-container,
  .search-filter-container {
    display: none !important;
  }
  
  .booking-table {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .booking-table th,
  .booking-table td {
    border: 1px solid #000;
    padding: 8px;
  }
}

/* تأثيرات التحميل */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات الإشعارات */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 10px;
  color: white;
  font-weight: bold;
  z-index: 2000;
  animation: slideInRight 0.3s ease-out;
}

.notification.success {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.notification.error {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.notification.warning {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}