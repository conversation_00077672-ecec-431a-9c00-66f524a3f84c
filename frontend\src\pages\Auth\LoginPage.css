/* صفحة تسجيل الدخول المحترفة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

.login-page {
  min-height: 100vh;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  overflow: hidden;
  position: relative;
}

/* خلفية متحركة */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 25%, 
    #f093fb 50%, 
    #f5576c 75%, 
    #4facfe 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
}

/* زر العودة للصفحة الرئيسية */
.back-to-home-btn {
  position: fixed;
  top: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: none;
  border-radius: 50px;
  font-family: 'Cairo', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.back-to-home-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.back-to-home-btn:active {
  transform: translateY(0);
}

.back-icon {
  font-size: 16px;
}

.back-text {
  white-space: nowrap;
}

/* عناصر متحركة في الخلفية */
.floating-element {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* الحاوية الرئيسية */
.login-container {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 40px;
}

/* بطاقة تسجيل الدخول */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 450px;
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* رأس الصفحة */
.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.company-logo {
  margin-bottom: 30px;
}

.logo-icon {
  font-size: 4rem;
  margin-bottom: 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.company-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.company-tagline {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 400;
}

/* نموذج تسجيل الدخول */
.login-form {
  margin-bottom: 30px;
}

.form-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  margin: 0 0 30px 0;
}

/* رسالة الخطأ */
.error-message {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-icon {
  font-size: 1.2rem;
}

/* مجموعات النموذج */
.form-group {
  margin-bottom: 25px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
  font-size: 1rem;
}

.label-icon {
  font-size: 1.1rem;
}

.form-input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.form-input:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

/* حاوية كلمة المرور */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #7f8c8d;
  transition: color 0.3s ease;
  padding: 5px;
}

.password-toggle:hover {
  color: #667eea;
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* خيارات النموذج */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  color: #2c3e50;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e8ed;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* زر تسجيل الدخول */
.login-button {
  width: 100%;
  padding: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 25px;
  font-family: inherit;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
}

.login-button.loading {
  background: #95a5a6;
}

.button-icon {
  font-size: 1.2rem;
}

/* مؤشر التحميل */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* قسم التجربة */
.demo-section {
  text-align: center;
  margin-bottom: 30px;
}

.demo-text {
  color: #7f8c8d;
  margin: 0 0 15px 0;
  font-size: 0.95rem;
}

.demo-button {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-family: inherit;
}

.demo-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
}

.demo-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.demo-icon {
  font-size: 1.1rem;
}

/* معلومات تسجيل الدخول التجريبية */
.demo-credentials {
  margin-top: 20px;
  padding: 15px;
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.2);
  border-radius: 10px;
  text-align: right;
}

.credentials-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.credential-item {
  font-size: 0.85rem;
  color: #34495e;
  margin: 5px 0;
  padding: 5px 0;
  border-bottom: 1px solid rgba(52, 152, 219, 0.1);
}

.credential-item:last-child {
  border-bottom: none;
}

.credential-item strong {
  color: #2980b9;
  font-weight: 600;
}

/* تذييل تسجيل الدخول */
.login-footer {
  border-top: 1px solid #e1e8ed;
  padding-top: 25px;
}

.features-list {
  margin-bottom: 25px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  color: #2c3e50;
  font-size: 0.95rem;
}

.feature-icon {
  font-size: 1.1rem;
  color: #667eea;
}

.copyright {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.85rem;
}

.copyright p {
  margin: 5px 0;
}

.version {
  color: #bdc3c7;
}

/* اللوحة الجانبية */
.info-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  color: white;
  animation: slideInRight 0.8s ease-out 0.2s both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.info-content h3 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.info-title {
  background: linear-gradient(135deg, #fff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* معلومات الاتصال */
.contact-info h4 {
  font-size: 1.3rem;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  font-size: 0.95rem;
}

.contact-icon {
  font-size: 1.1rem;
  opacity: 0.8;
}

/* التجاوب مع الشاشات */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
    gap: 30px;
  }
  
  .info-panel {
    max-width: 450px;
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 15px;
  }
  
  .login-card,
  .info-panel {
    padding: 30px 25px;
  }
  
  .company-name {
    font-size: 2rem;
  }
  
  .logo-icon {
    font-size: 3rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }
  
  .stat-item {
    padding: 15px 10px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .login-card,
  .info-panel {
    padding: 25px 20px;
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .company-name {
    font-size: 1.8rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

/* تأثيرات إضافية */
.login-card:hover {
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.info-panel:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* تحسينات الوصولية */
.form-input:focus,
.login-button:focus,
.demo-button:focus,
.password-toggle:focus,
.back-to-home-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* أنماط متجاوبة للزر */
@media (max-width: 768px) {
  .back-to-home-btn {
    top: 20px;
    right: 20px;
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .back-text {
    display: none;
  }
  
  .back-icon {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .back-to-home-btn {
    top: 15px;
    right: 15px;
    padding: 8px 12px;
  }
  
  .demo-credentials {
    padding: 12px;
    font-size: 0.8rem;
  }
  
  .credentials-title {
    font-size: 0.85rem;
  }
  
  .credential-item {
    font-size: 0.8rem;
  }
}

/* طباعة */
@media print {
  .login-page {
    background: white !important;
  }
  
  .login-background,
  .info-panel {
    display: none !important;
  }
  
  .login-card {
    box-shadow: none !important;
    background: white !important;
  }
}