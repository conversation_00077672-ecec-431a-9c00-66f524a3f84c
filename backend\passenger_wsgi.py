# -*- coding: utf-8 -*-
"""
Passenger WSGI entrypoint for <PERSON><PERSON> (Shared Hosting)
This wraps the FastAPI ASGI app into WSGI using asgiref.AsgiToWsgi
"""
import os
import sys

# Ensure backend directory is on sys.path and set CWD to backend
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
os.chdir(BASE_DIR)
if BASE_DIR not in sys.path:
    sys.path.insert(0, BASE_DIR)

# Optional: point to a virtualenv if you create one via hPanel (adjust path if needed)
# VENV_PATH = os.path.join(BASE_DIR, 'venv')
# if os.path.isdir(VENV_PATH):
#     os.environ['PATH'] = os.path.join(VENV_PATH, 'bin') + os.pathsep + os.environ.get('PATH', '')

from asgiref.wsgi import AsgiToWsgi
from app.main import app as fastapi_app

# WSGI callable for Passenger
application = AsgiToWsgi(fastapi_app)