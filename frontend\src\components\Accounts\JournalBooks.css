.journal-books {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.journal-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  padding: 10px 15px;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.date-range label {
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
  font-size: 14px;
}

.date-range input {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.journal-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
}

.journal-sidebar {
  width: 320px;
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  height: fit-content;
  max-height: 80vh;
  overflow-y: auto;
}

.search-box {
  margin-bottom: 25px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 14px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.journal-types h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: bold;
}

.journal-type {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.journal-type:hover {
  transform: translateX(-3px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.journal-type.active {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(-5px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
}

.journal-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.journal-type.active .journal-icon {
  color: white !important;
}

.journal-info {
  flex: 1;
}

.journal-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 2px;
}

.journal-description {
  font-size: 12px;
  opacity: 0.7;
}

.journal-type.active .journal-description {
  opacity: 0.9;
}

.journal-count {
  background: #f8f9fa;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  min-width: 25px;
  text-align: center;
}

.journal-type.active .journal-count {
  background: rgba(255,255,255,0.2);
  color: white;
}

.journal-main {
  flex: 1;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: auto;
}

.journal-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f2f6;
}

.title-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title-icon {
  font-size: 32px;
}

.title-info h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: bold;
}

.title-info p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.entries-count {
  background: #f8f9fa;
  color: #2c3e50;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
}

.journal-entries {
  min-height: 400px;
}

.no-entries {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
  text-align: center;
}

.no-entries .icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.no-entries h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.no-entries p {
  margin: 0;
  font-size: 14px;
}

.entries-table {
  overflow-x: auto;
}

.entries-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.entries-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
}

.entries-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  white-space: nowrap;
}

.entry-row:hover {
  background: #f8f9fa;
}

.entry-row.revenue {
  border-left: 4px solid #27ae60;
}

.entry-row.expense {
  border-left: 4px solid #e74c3c;
}

.entry-row.transfer {
  border-left: 4px solid #3498db;
}

.entry-row.manual {
  border-left: 4px solid #9b59b6;
}

.entry-id {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.description {
  text-align: right;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.debit {
  color: #27ae60;
  font-weight: bold;
}

.credit {
  color: #e74c3c;
  font-weight: bold;
}

.user {
  color: #7f8c8d;
  font-size: 12px;
}

.actions {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.btn-view, .btn-edit, .btn-print {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-view:hover {
  background: #3498db20;
}

.btn-edit:hover {
  background: #f39c1220;
}

.btn-print:hover {
  background: #9b59b620;
}

/* نموذج إضافة قيد */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.entry-modal, .entry-details-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 2px solid #f1f2f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 25px;
}

.entry-header-form {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.entries-section {
  margin-bottom: 25px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #2c3e50;
}

.entries-table-container {
  overflow-x: auto;
  border: 2px solid #e9ecef;
  border-radius: 10px;
}

.entries-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.entries-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: center;
  border-bottom: 2px solid #dee2e6;
  font-weight: bold;
  color: #2c3e50;
}

.entries-table td {
  padding: 8px;
  border-bottom: 1px solid #dee2e6;
}

.entries-table .form-control {
  border: 1px solid #ddd;
  padding: 8px;
  font-size: 13px;
}

.debit-input:focus {
  border-color: #27ae60;
  box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.1);
}

.credit-input:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
}

.btn-remove {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 5px 8px;
  cursor: pointer;
  font-size: 12px;
}

.totals-row {
  background: #f8f9fa;
  font-weight: bold;
}

.total-debit {
  color: #27ae60;
}

.total-credit {
  color: #e74c3c;
}

.difference-row {
  background: #fff5f5;
}

.difference {
  text-align: center;
}

.balance-warning {
  color: #e74c3c;
  font-size: 16px;
}

.modal-footer {
  padding: 25px;
  border-top: 2px solid #f1f2f6;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

/* نموذج تفاصيل القيد */
.entry-info {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-weight: bold;
  color: #7f8c8d;
  font-size: 12px;
  text-transform: uppercase;
}

.info-item span {
  color: #2c3e50;
  font-size: 14px;
}

.entry-details-table {
  margin-bottom: 25px;
}

.entry-details-table h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.entry-details-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.entry-details-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  font-weight: bold;
  color: #2c3e50;
}

.entry-details-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
}

.account-info {
  text-align: right;
}

.account-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.account-code {
  font-size: 12px;
  color: #7f8c8d;
}

.entry-notes {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.entry-notes h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.entry-notes p {
  margin: 0;
  color: #2c3e50;
  line-height: 1.6;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .journal-content {
    flex-direction: column;
  }
  
  .journal-sidebar {
    width: 100%;
    max-height: 300px;
  }
  
  .journal-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .date-range {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .journal-title {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .entries-table {
    font-size: 12px;
  }
  
  .entries-table th,
  .entries-table td {
    padding: 8px 4px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .journal-books {
    padding: 10px;
  }
  
  .journal-header,
  .journal-sidebar,
  .journal-main,
  .modal-content {
    padding: 15px;
  }
  
  .entry-modal,
  .entry-details-modal {
    margin: 10px;
    max-width: none;
  }
}

/* أنماط الوظائف المتقدمة */
.journal-books-advanced {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* أنماط الطباعة والتصدير */
.print-modal,
.export-modal,
.filter-modal,
.details-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.print-options,
.export-options,
.filter-form {
  padding: 20px 0;
}

.option-group,
.filter-group {
  margin-bottom: 20px;
}

.option-group label,
.filter-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
}

.option-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

/* أنماط التصدير */
.format-selection h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-option:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.format-option input[type="radio"] {
  width: 18px;
  height: 18px;
}

.format-option input[type="radio"]:checked + .format-info {
  color: #3498db;
}

.format-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.format-icon {
  font-size: 24px;
}

.format-name {
  font-weight: bold;
  font-size: 16px;
}

.format-desc {
  font-size: 12px;
  color: #7f8c8d;
}

.export-summary {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 2px solid #f1f2f6;
}

.export-summary h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.summary-item .label {
  font-weight: 500;
  color: #7f8c8d;
}

.summary-item .value {
  font-weight: bold;
  color: #2c3e50;
}

/* أنماط التصفية المتقدمة */
.range-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-inputs span {
  color: #7f8c8d;
  font-size: 14px;
}

.users-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

/* أنماط عرض البطاقات */
.cards-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.transaction-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.transaction-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
  border-color: #3498db;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.transaction-id {
  font-weight: bold;
  color: #3498db;
  font-family: monospace;
}

.transaction-date {
  font-size: 12px;
  color: #7f8c8d;
}

.card-actions {
  display: flex;
  gap: 5px;
}

.card-content .description {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 10px;
  line-height: 1.4;
}

.card-content .reference {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 15px;
}

.amounts {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.amount-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.amount-item .label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.amount-item .value {
  font-size: 14px;
  font-weight: bold;
}

.amount-item.debit .value {
  color: #27ae60;
}

.amount-item.credit .value {
  color: #e74c3c;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #7f8c8d;
  padding-top: 10px;
  border-top: 1px solid #f1f2f6;
}

/* أنماط العرض الزمني */
.timeline-view {
  padding: 20px 0;
  position: relative;
}

.timeline-view::before {
  content: '';
  position: absolute;
  right: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #3498db;
  transform: translateX(50%);
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row-reverse;
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-left: 30px;
  margin-right: 0;
}

.timeline-marker {
  width: 16px;
  height: 16px;
  background: #3498db;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 0 0 2px #3498db;
  position: absolute;
  right: 50%;
  transform: translateX(50%);
  z-index: 2;
}

.timeline-content {
  background: white;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  margin-right: 30px;
  flex: 1;
  max-width: 45%;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.timeline-date {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.timeline-id {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
  font-size: 12px;
}

.timeline-description {
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.timeline-amount {
  font-size: 12px;
  color: #7f8c8d;
}

/* أنماط نموذج التفاصيل */
.details-modal {
  max-width: 800px;
}

.entry-details {
  padding: 20px 0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
  text-transform: uppercase;
}

.detail-item span {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.description-section,
.notes-section {
  margin-bottom: 20px;
}

.description-section label,
.notes-section label {
  display: block;
  font-size: 14px;
  color: #7f8c8d;
  font-weight: bold;
  margin-bottom: 8px;
}

.description-section p,
.notes-section p {
  margin: 0;
  color: #2c3e50;
  line-height: 1.6;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.entries-details h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.details-table th {
  background: #2c3e50;
  color: white;
  padding: 12px 8px;
  text-align: center;
  font-weight: bold;
}

.details-table td {
  padding: 10px 8px;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

.details-table tfoot td {
  background: #f8f9fa;
  font-weight: bold;
  border-top: 2px solid #2c3e50;
}

.account-info {
  text-align: right;
}

.account-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.account-code {
  font-size: 12px;
  color: #7f8c8d;
}

.metadata-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f1f2f6;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.metadata-item label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: bold;
}

.metadata-item span {
  font-size: 13px;
  color: #2c3e50;
}

/* أنماط أزرار الإجراءات المحسنة */
.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: scale(1.1);
}

.view-btn:hover {
  background: #3498db20;
  color: #3498db;
}

.edit-btn:hover {
  background: #f39c1220;
  color: #f39c12;
}

.print-btn:hover {
  background: #9b59b620;
  color: #9b59b6;
}

.delete-btn:hover {
  background: #e74c3c20;
  color: #e74c3c;
}

/* أنماط الترتيب */
.journal-table th {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.journal-table th:hover {
  background: #e9ecef;
}

/* أنماط أزرار أنماط العرض */
.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.view-mode-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-mode-btn:hover:not(.active) {
  background: #e9ecef;
}

/* تحسينات الجدول */
.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.journal-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.journal-table th {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.journal-table td {
  padding: 12px 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.transaction-row:hover {
  background: #f8f9fa;
}

.transaction-row:nth-child(even) {
  background: #fafbfc;
}

.amount.debit {
  color: #27ae60;
  font-weight: bold;
}

.amount.credit {
  color: #e74c3c;
  font-weight: bold;
}

/* تنسيق متجاوب للوظائف المتقدمة */
@media (max-width: 768px) {
  .journal-books-advanced {
    padding: 10px;
  }
  
  .cards-view {
    grid-template-columns: 1fr;
  }
  
  .timeline-view::before {
    right: 20px;
    transform: none;
  }
  
  .timeline-item {
    flex-direction: row !important;
    padding-right: 40px;
  }
  
  .timeline-item .timeline-content {
    margin-right: 0 !important;
    margin-left: 0 !important;
    max-width: none;
  }
  
  .timeline-marker {
    right: 12px;
    transform: none;
  }
  
  .details-grid,
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .format-options {
    gap: 8px;
  }
  
  .format-option {
    padding: 12px;
  }
  
  .range-inputs {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-modes {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .amounts {
    flex-direction: column;
    gap: 10px;
  }
  
  .transaction-card {
    padding: 15px;
  }
  
  .action-btn {
    padding: 4px 6px;
    font-size: 12px;
  }
}