import React, { useState, useMemo } from 'react';
import './AccountingPeriods.css';

const AccountingPeriods = ({ transactions, accounts, financialPeriod, updateFinancialPeriod, currentUser }) => {
  const [showPeriodModal, setShowPeriodModal] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState(null);
  const [periods, setPeriods] = useState([
    {
      id: 'FY2024',
      name: 'السنة المالية 2024',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      isActive: true,
      createdBy: 'المدير العام',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'FY2023',
      name: 'السنة المالية 2023',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      status: 'closed',
      isActive: false,
      createdBy: 'المدير العام',
      createdAt: '2023-01-01T00:00:00Z'
    }
  ]);

  const [newPeriod, setNewPeriod] = useState({
    name: '',
    startDate: '',
    endDate: '',
    description: ''
  });

  // حساب إحصائيات الفترة المحاسبية
  const periodStats = useMemo(() => {
    const currentPeriodTransactions = transactions.filter(trans => {
      const transDate = new Date(trans.date);
      const periodStart = new Date(financialPeriod.startDate);
      const periodEnd = new Date(financialPeriod.endDate);
      return transDate >= periodStart && transDate <= periodEnd;
    });

    const stats = {
      totalTransactions: currentPeriodTransactions.length,
      totalRevenue: 0,
      totalExpenses: 0,
      netIncome: 0,
      totalAssets: 0,
      totalLiabilities: 0,
      totalEquity: 0
    };

    // حساب الإيرادات والمصروفات
    currentPeriodTransactions.forEach(trans => {
      trans.entries?.forEach(entry => {
        const account = accounts.find(acc => acc.id === entry.accountId);
        if (account) {
          const amount = (entry.debit || 0) - (entry.credit || 0);
          
          switch (account.category) {
            case 'revenue':
              stats.totalRevenue += (entry.credit || 0);
              break;
            case 'expenses':
              stats.totalExpenses += (entry.debit || 0);
              break;
            case 'assets':
              stats.totalAssets += amount;
              break;
            case 'liabilities':
              stats.totalLiabilities += (entry.credit || 0) - (entry.debit || 0);
              break;
            case 'equity':
              stats.totalEquity += (entry.credit || 0) - (entry.debit || 0);
              break;
          }
        }
      });
    });

    stats.netIncome = stats.totalRevenue - stats.totalExpenses;

    return stats;
  }, [transactions, accounts, financialPeriod]);

  // إنشاء فترة محاسبية جديدة
  const createNewPeriod = () => {
    if (!newPeriod.name.trim() || !newPeriod.startDate || !newPeriod.endDate) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (new Date(newPeriod.startDate) >= new Date(newPeriod.endDate)) {
      alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
      return;
    }

    const periodId = `FY${new Date(newPeriod.startDate).getFullYear()}`;
    
    const period = {
      id: periodId,
      name: newPeriod.name,
      startDate: newPeriod.startDate,
      endDate: newPeriod.endDate,
      description: newPeriod.description,
      status: 'draft',
      isActive: false,
      createdBy: currentUser?.name || 'النظام',
      createdAt: new Date().toISOString()
    };

    setPeriods(prev => [period, ...prev]);
    setNewPeriod({ name: '', startDate: '', endDate: '', description: '' });
    setShowPeriodModal(false);
    alert('تم إنشاء الفترة المحاسبية بنجاح');
  };

  // تفعيل فترة محاسبية
  const activatePeriod = (period) => {
    if (window.confirm(`هل أنت متأكد من تفعيل الفترة "${period.name}"؟`)) {
      // إلغاء تفعيل الفترات الأخرى
      setPeriods(prev => prev.map(p => ({
        ...p,
        isActive: p.id === period.id,
        status: p.id === period.id ? 'active' : (p.status === 'active' ? 'closed' : p.status)
      })));

      // تحديث الفترة المالية الحالية
      updateFinancialPeriod({
        startDate: period.startDate,
        endDate: period.endDate,
        name: period.name
      });

      alert(`تم تفعيل الفترة "${period.name}" بنجاح`);
    }
  };

  // إغلاق فترة محاسبية
  const closePeriod = (period) => {
    if (window.confirm(`هل أنت متأكد من إغلاق الفترة "${period.name}"؟ لن يمكن تعديلها بعد الإغلاق.`)) {
      setPeriods(prev => prev.map(p => 
        p.id === period.id 
          ? { ...p, status: 'closed', isActive: false }
          : p
      ));
      alert(`تم إغلاق الفترة "${period.name}" بنجاح`);
    }
  };

  // حذف فترة محاسبية
  const deletePeriod = (period) => {
    if (period.isActive) {
      alert('لا يمكن حذف الفترة النشطة');
      return;
    }

    if (period.status === 'closed') {
      alert('لا يمكن حذف فترة مغلقة');
      return;
    }

    if (window.confirm(`هل أنت متأكد من حذف الفترة "${period.name}"؟`)) {
      setPeriods(prev => prev.filter(p => p.id !== period.id));
      alert('تم حذف الفترة بنجاح');
    }
  };

  // تصدير تقرير الفترة
  const exportPeriodReport = (period) => {
    const periodTransactions = transactions.filter(trans => {
      const transDate = new Date(trans.date);
      const periodStart = new Date(period.startDate);
      const periodEnd = new Date(period.endDate);
      return transDate >= periodStart && transDate <= periodEnd;
    });

    const csvContent = [
      ['تقرير الفترة المحاسبية:', period.name],
      ['من:', new Date(period.startDate).toLocaleDateString('ar-SA')],
      ['إلى:', new Date(period.endDate).toLocaleDateString('ar-SA')],
      [''],
      ['رقم المعاملة', 'التاريخ', 'الوصف', 'النوع', 'المبلغ'],
      ...periodTransactions.map(trans => [
        trans.id,
        new Date(trans.date).toLocaleDateString('ar-SA'),
        trans.description,
        trans.type === 'revenue' ? 'إيراد' : trans.type === 'expense' ? 'مصروف' : 'تحويل',
        trans.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `period-report-${period.id}.csv`;
    link.click();
  };

  return (
    <div className="accounting-periods">
      <div className="periods-header">
        <div className="header-content">
          <h2>📅 الفترات المحاسبية</h2>
          <p>إدارة وتنظيم الفترات المالية للشركة</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowPeriodModal(true)}
          >
            ➕ فترة جديدة
          </button>
        </div>
      </div>

      <div className="periods-content">
        <div className="current-period-card">
          <div className="card-header">
            <h3>🎯 الفترة المحاسبية الحالية</h3>
            <span className="period-status active">نشطة</span>
          </div>
          
          <div className="period-info">
            <div className="period-details">
              <h4>{financialPeriod.name || 'السنة المالية الحالية'}</h4>
              <p>
                من {new Date(financialPeriod.startDate).toLocaleDateString('ar-SA')} 
                إلى {new Date(financialPeriod.endDate).toLocaleDateString('ar-SA')}
              </p>
            </div>
            
            <div className="period-stats">
              <div className="stats-grid">
                <div className="stat-item revenue">
                  <div className="stat-icon">📈</div>
                  <div className="stat-info">
                    <div className="stat-value">{periodStats.totalRevenue.toLocaleString()}</div>
                    <div className="stat-label">إجمالي الإيرادات</div>
                  </div>
                </div>
                
                <div className="stat-item expense">
                  <div className="stat-icon">📉</div>
                  <div className="stat-info">
                    <div className="stat-value">{periodStats.totalExpenses.toLocaleString()}</div>
                    <div className="stat-label">إجمالي المصروفات</div>
                  </div>
                </div>
                
                <div className="stat-item net-income">
                  <div className="stat-icon">💰</div>
                  <div className="stat-info">
                    <div className="stat-value">{periodStats.netIncome.toLocaleString()}</div>
                    <div className="stat-label">صافي الدخل</div>
                  </div>
                </div>
                
                <div className="stat-item transactions">
                  <div className="stat-icon">📊</div>
                  <div className="stat-info">
                    <div className="stat-value">{periodStats.totalTransactions}</div>
                    <div className="stat-label">عدد المعاملات</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="periods-list">
          <div className="list-header">
            <h3>📋 جميع الفترات المحاسبية</h3>
            <div className="list-filters">
              <select className="filter-select">
                <option value="all">جميع الفترات</option>
                <option value="active">النشطة</option>
                <option value="closed">المغلقة</option>
                <option value="draft">المسودات</option>
              </select>
            </div>
          </div>

          <div className="periods-grid">
            {periods.map(period => (
              <div key={period.id} className={`period-card ${period.status}`}>
                <div className="card-header">
                  <div className="period-title">
                    <h4>{period.name}</h4>
                    <span className={`period-status ${period.status}`}>
                      {period.status === 'active' ? 'نشطة' : 
                       period.status === 'closed' ? 'مغلقة' : 'مسودة'}
                    </span>
                  </div>
                  {period.isActive && (
                    <div className="active-badge">الحالية</div>
                  )}
                </div>

                <div className="period-details">
                  <div className="date-range">
                    <div className="date-item">
                      <span className="label">من:</span>
                      <span className="date">{new Date(period.startDate).toLocaleDateString('ar-SA')}</span>
                    </div>
                    <div className="date-item">
                      <span className="label">إلى:</span>
                      <span className="date">{new Date(period.endDate).toLocaleDateString('ar-SA')}</span>
                    </div>
                  </div>

                  <div className="period-meta">
                    <div className="meta-item">
                      <span className="label">أنشأها:</span>
                      <span className="value">{period.createdBy}</span>
                    </div>
                    <div className="meta-item">
                      <span className="label">تاريخ الإنشاء:</span>
                      <span className="value">{new Date(period.createdAt).toLocaleDateString('ar-SA')}</span>
                    </div>
                  </div>

                  {period.description && (
                    <div className="period-description">
                      <p>{period.description}</p>
                    </div>
                  )}
                </div>

                <div className="period-actions">
                  {!period.isActive && period.status !== 'closed' && (
                    <button
                      className="btn btn-success"
                      onClick={() => activatePeriod(period)}
                    >
                      ✅ تفعيل
                    </button>
                  )}
                  
                  {period.isActive && (
                    <button
                      className="btn btn-warning"
                      onClick={() => closePeriod(period)}
                    >
                      🔒 إغلاق
                    </button>
                  )}
                  
                  <button
                    className="btn btn-info"
                    onClick={() => exportPeriodReport(period)}
                  >
                    📊 تقرير
                  </button>
                  
                  {period.status === 'draft' && !period.isActive && (
                    <button
                      className="btn btn-danger"
                      onClick={() => deletePeriod(period)}
                    >
                      🗑️ حذف
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* نموذج إضافة فترة جديدة */}
      {showPeriodModal && (
        <div className="modal-overlay">
          <div className="period-modal">
            <div className="modal-header">
              <h3>➕ فترة محاسبية جديدة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPeriodModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>اسم الفترة *</label>
                  <input
                    type="text"
                    value={newPeriod.name}
                    onChange={(e) => setNewPeriod(prev => ({ ...prev, name: e.target.value }))}
                    className="form-control"
                    placeholder="مثال: السنة المالية 2025"
                  />
                </div>

                <div className="form-group">
                  <label>تاريخ البداية *</label>
                  <input
                    type="date"
                    value={newPeriod.startDate}
                    onChange={(e) => setNewPeriod(prev => ({ ...prev, startDate: e.target.value }))}
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label>تاريخ النهاية *</label>
                  <input
                    type="date"
                    value={newPeriod.endDate}
                    onChange={(e) => setNewPeriod(prev => ({ ...prev, endDate: e.target.value }))}
                    className="form-control"
                  />
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={newPeriod.description}
                    onChange={(e) => setNewPeriod(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="وصف مختصر للفترة المحاسبية..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPeriodModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={createNewPeriod}
              >
                💾 إنشاء الفترة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountingPeriods;