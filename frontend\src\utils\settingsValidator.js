// أداة للتحقق من صحة إعدادات النظام
export const validateSettings = (settings) => {
  const errors = {};

  // التحقق من الإعدادات العامة
  if (!settings.general?.companyName?.trim()) {
    errors.companyName = 'اسم الشركة مطلوب';
  }

  if (!settings.general?.currency) {
    errors.currency = 'العملة مطلوبة';
  }

  // التحقق من معلومات الشركة
  if (!settings.business?.email || !isValidEmail(settings.business.email)) {
    errors.businessEmail = 'بريد إلكتروني صحيح مطلوب';
  }

  if (!settings.business?.phone?.trim()) {
    errors.businessPhone = 'رقم الهاتف مطلوب';
  }

  // التحقق من الإعدادات المالية
  if (settings.financial?.defaultCommissionRate < 0 || settings.financial?.defaultCommissionRate > 100) {
    errors.commissionRate = 'معدل العمولة يجب أن يكون بين 0 و 100';
  }

  if (settings.financial?.defaultTaxRate < 0 || settings.financial?.defaultTaxRate > 100) {
    errors.taxRate = 'معدل الضريبة يجب أن يكون بين 0 و 100';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// التحقق من صحة البريد الإلكتروني
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// التحقق من صحة رقم الهاتف السعودي
export const isValidSaudiPhone = (phone) => {
  const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// التحقق من صحة الرقم الضريبي
export const isValidTaxNumber = (taxNumber) => {
  const taxRegex = /^[0-9]{15}$/;
  return taxRegex.test(taxNumber);
};

// تنظيف البيانات قبل الحفظ
export const sanitizeSettings = (settings) => {
  const sanitized = JSON.parse(JSON.stringify(settings));

  // تنظيف النصوص
  if (sanitized.general?.companyName) {
    sanitized.general.companyName = sanitized.general.companyName.trim();
  }

  if (sanitized.business?.email) {
    sanitized.business.email = sanitized.business.email.trim().toLowerCase();
  }

  if (sanitized.business?.phone) {
    sanitized.business.phone = sanitized.business.phone.replace(/\s/g, '');
  }

  // تحويل الأرقام
  if (sanitized.financial?.defaultCommissionRate) {
    sanitized.financial.defaultCommissionRate = parseFloat(sanitized.financial.defaultCommissionRate);
  }

  if (sanitized.financial?.defaultTaxRate) {
    sanitized.financial.defaultTaxRate = parseFloat(sanitized.financial.defaultTaxRate);
  }

  return sanitized;
};

// إعدادات افتراضية
export const getDefaultSettings = () => ({
  general: {
    companyName: 'شركة شراء السفر والسياحة',
    companyLogo: '',
    language: 'ar',
    currency: 'SAR',
    timezone: 'Asia/Riyadh',
    dateFormat: 'DD/MM/YYYY',
    fiscalYearStart: '01/01'
  },
  business: {
    businessType: 'travel_agency',
    licenseNumber: '',
    taxNumber: '',
    commercialRegister: '',
    address: '',
    phone: '',
    email: '',
    website: ''
  },
  financial: {
    defaultCommissionRate: 5.0,
    defaultTaxRate: 15.0,
    paymentTerms: 30,
    invoicePrefix: 'INV',
    invoiceStartNumber: 1000,
    autoGenerateInvoices: true,
    requireApproval: true,
    multiCurrency: false
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    bookingConfirmations: true,
    paymentReminders: true,
    systemAlerts: true,
    marketingEmails: false
  },
  security: {
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordPolicy: 'medium',
    allowedIPs: [],
    encryptData: true,
    auditLog: true,
    autoLogout: true
  },
  integrations: {
    paymentGateways: {
      paypal: { enabled: false, clientId: '', secretKey: '' },
      stripe: { enabled: false, publishableKey: '', secretKey: '' },
      mada: { enabled: false, merchantId: '', terminalId: '' }
    },
    apis: {
      amadeus: { enabled: false, apiKey: '', secretKey: '' },
      sabre: { enabled: false, apiKey: '', secretKey: '' },
      googleMaps: { enabled: false, apiKey: '' }
    },
    socialMedia: {
      facebook: { enabled: false, pageId: '', accessToken: '' },
      twitter: { enabled: false, username: '', apiKey: '' },
      instagram: { enabled: false, username: '', accessToken: '' }
    }
  }
});