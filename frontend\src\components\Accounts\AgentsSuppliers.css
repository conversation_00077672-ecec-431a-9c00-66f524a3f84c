/* Agents & Suppliers Styles */

.agents-suppliers {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.page-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

/* Main Tabs */
.main-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 25px;
  background: #f8f9fa;
  padding: 5px;
  border-radius: 12px;
}

.main-tabs .tab-btn {
  flex: 1;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  text-align: center;
}

.main-tabs .tab-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #495057;
}

.main-tabs .tab-btn.active {
  background: white;
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

/* Page Content */
.page-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

/* Entities List */
.entities-list {
  padding: 20px 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e1e8ed;
}

.list-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.add-entity-btn {
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
}

.add-entity-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Entities Filters */
.entities-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 250px;
}

.search-box input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.status-filter select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

/* Entities Table */
.entities-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.entities-table .table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr 0.8fr auto;
  gap: 15px;
  padding: 15px;
  background: #667eea;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.entities-table .table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr 0.8fr auto;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
  transition: background-color 0.3s ease;
}

.entities-table .table-row:hover {
  background: #f8f9fa;
}

.entities-table .table-row:last-child {
  border-bottom: none;
}

.entities-table .table-row.inactive {
  opacity: 0.6;
}

.entity-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.entity-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.entity-contact {
  font-size: 12px;
  color: #6c757d;
}

.entity-id {
  font-size: 11px;
  color: #adb5bd;
  font-family: 'Courier New', monospace;
}

.type-badge,
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-align: center;
  display: inline-block;
  min-width: 60px;
}

.type-badge {
  background: #17a2b8;
}

.contact-person {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.person-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 13px;
}

.person-email {
  font-size: 11px;
  color: #6c757d;
}

.current-balance,
.credit-limit {
  text-align: right;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.current-balance .positive {
  color: #28a745;
}

.current-balance .zero {
  color: #6c757d;
}

.entity-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.action-btn.view:hover {
  background: rgba(23, 162, 184, 0.2);
}

.action-btn.accounts:hover {
  background: rgba(102, 126, 234, 0.2);
}

.action-btn.edit:hover {
  background: rgba(255, 193, 7, 0.2);
}

.action-btn.delete:hover {
  background: rgba(220, 53, 69, 0.2);
}

/* Entity Details */
.entity-details {
  padding: 20px 0;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e1e8ed;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-info h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.entity-type-badge {
  background: #17a2b8;
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.add-account-btn,
.back-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-account-btn {
  background: #28a745;
  color: white;
}

.add-account-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.back-btn {
  background: #6c757d;
  color: white;
}

.back-btn:hover {
  background: #5a6268;
}

/* Entity Info Card */
.entity-info-card {
  background: #f8f9fa;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e1e8ed;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.info-item span {
  color: #2c3e50;
  font-size: 14px;
}

.info-item .positive {
  color: #28a745;
  font-weight: 600;
}

.info-item .zero {
  color: #6c757d;
}

/* Entity Accounts */
.entity-accounts {
  margin-bottom: 25px;
}

.entity-accounts h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e8ed;
}

.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.account-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.account-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.account-number {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  color: #667eea;
  font-size: 16px;
}

.account-actions {
  display: flex;
  gap: 8px;
}

.account-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 15px;
}

.account-details {
  display: grid;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f1f3f4;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.detail-item span {
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
}

.detail-item .positive {
  color: #28a745;
}

.detail-item .zero {
  color: #6c757d;
}

/* Entity Transactions */
.entity-transactions h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e8ed;
}

/* Transactions Header */
.transactions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.add-transaction-btn {
  padding: 8px 12px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.add-transaction-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(40, 167, 69, 0.4);
}

.transactions-table {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.transactions-table .table-header {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #28a745;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.transactions-table .table-row {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
}

.transactions-table .table-row:hover {
  background: #f8f9fa;
}

.transactions-table .table-row:last-child {
  border-bottom: none;
}

.transaction-date {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #6c757d;
}

.transaction-description {
  font-size: 13px;
  color: #2c3e50;
}

.transaction-debit,
.transaction-credit,
.transaction-balance {
  text-align: right;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 600;
}

.transaction-reference {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #6c757d;
}

/* Entity Modal */
.entity-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-content.delete-confirm {
  max-width: 450px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background: rgba(220, 53, 69, 0.1);
}

.modal-body {
  padding: 20px;
  flex: 1;
}

.entity-form,
.account-form,
.transaction-form {
  display: grid;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
  flex-shrink: 0;
}

.save-btn,
.cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .entities-table .table-header,
  .entities-table .table-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .entities-table .table-header > div,
  .entities-table .table-row > div {
    padding: 8px;
    border-bottom: 1px solid #e1e8ed;
  }
  
  .entities-table .table-header > div:last-child,
  .entities-table .table-row > div:last-child {
    border-bottom: none;
  }
  
  .transactions-table .table-header,
  .transactions-table .table-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .accounts-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .agents-suppliers {
    padding: 15px;
  }
  
  .main-tabs {
    flex-direction: column;
  }
  
  .list-header,
  .details-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .entities-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .entity-actions {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .account-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .account-actions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .page-header h3 {
    font-size: 20px;
  }
  
  .list-header h4,
  .details-header h4 {
    font-size: 18px;
  }
  
  .entity-actions {
    justify-content: center;
  }
  
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 10px;
  }
  
  .info-item,
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

/* Print Styles */
@media print {
  .agents-suppliers {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .page-header {
    border-bottom: 2px solid #000;
    margin-bottom: 20px;
  }
  
  .main-tabs,
  .add-entity-btn,
  .entities-filters,
  .entity-actions,
  .account-actions,
  .header-actions,
  .entity-modal {
    display: none !important;
  }
  
  .entities-table,
  .transactions-table {
    border: 2px solid #000;
  }
  
  .entities-table .table-header,
  .transactions-table .table-header {
    background: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .account-card,
  .entity-info-card {
    break-inside: avoid;
    border: 1px solid #000;
    margin-bottom: 10px;
  }
}

/* Delete Confirmation Styles */
.delete-warning {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 24px;
  color: #856404;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text p {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 14px;
}

.warning-text strong {
  color: #2c3e50;
  font-size: 16px;
  display: block;
  margin: 10px 0;
}

.warning-note {
  font-size: 12px !important;
  color: #6c757d !important;
  font-style: italic;
}

.delete-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
}

.delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* Transaction Form Styles */
.transaction-summary {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item span:first-child {
  font-weight: 600;
  color: #6c757d;
  font-size: 14px;
}

.summary-item span:last-child {
  font-weight: 600;
  font-size: 14px;
}

.summary-item .debit {
  color: #dc3545;
}

.summary-item .credit {
  color: #28a745;
}

.summary-item .neutral {
  color: #6c757d;
}

/* Enhanced Button Styles */
.save-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.cancel-btn:hover {
  transform: translateY(-1px);
}