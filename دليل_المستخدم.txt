╔══════════════════════════════════════════════════════════════════╗
║                    دليل استخدام النظام                         ║
║              نظام شراء للسفر والسياحة المتطور                  ║
╚══════════════════════════════════════════════════════════════════╝

🚀 كيفية تشغيل النظام:
═══════════════════════════

1️⃣ انقر نقراً مزدوجاً على أيقونة "نظام شراء للسفر والسياحة" على سطح المكتب

2️⃣ انتظر حتى يتم تحميل النظام (قد يستغرق دقيقة أو دقيقتين في المرة الأولى)

3️⃣ سيفتح المتصفح تلقائياً على العنوان: http://localhost:3001

4️⃣ استمتع بالنظام المذهل!

🎯 الميزات الرئيسية:
═══════════════════════

📄 نظام القوالب المذهل:
   • قوالب السندات الفاخرة (قبض وصرف)
   • فواتير الطيران المتطورة
   • عقود الخدمات السياحية
   • رسائل البريد الإلكتروني التفاعلية
   • تقارير المبيعات التحليلية

💰 إدارة السندات المتطورة:
   • سندات قبض بتصميمات متعددة
   • سندات صرف احترافية
   • دعم العملات المتعددة
   • تحويل المبالغ إلى كلمات

🎨 منشئ القوالب المتطور:
   • تصميمات متنوعة (عصري، كلاسيكي، بسيط، فاخر)
   • أنظمة ألوان متعددة
   • تخصيص كامل للعناصر
   • معاينة مباشرة

📊 التقارير والإحصائيات:
   • لوحة معلومات شاملة
   • إحصائيات مفصلة
   • تقارير تحليلية
   • رسوم بيانية تفاعلية

🌐 الروابط المهمة:
═══════════════════

• الصفحة الرئيسية: http://localhost:3001
• القوالب المذهلة: http://localhost:3001/templates
• السندات المتطورة: http://localhost:3001/vouchers
• لوحة التحكم: http://localhost:3001/dashboard
• التقارير: http://localhost:3001/reports

⚙️ استكشاف الأخطاء:
═══════════════════════

❓ إذا لم يفتح المتصفح تلقائياً:
   افتح المتصفح يدوياً واذهب إلى: http://localhost:3001

❓ إذا ظهرت رسالة خطأ:
   • تأكد من أن Node.js مثبت على النظام
   • أعد تشغيل النظام
   • تحقق من ملف السجلات: system.log

❓ إذا كان النظام بطيئاً:
   • انتظر قليلاً حتى يتم التحميل الكامل
   • أغلق التطبيقات الأخرى لتوفير الذاكرة
   • أعد تشغيل الكمبيوتر إذا لزم الأمر

🛑 إيقاف النظام:
═══════════════════

• أغلق نافذة PowerShell (الشاشة السوداء)
• أو اضغط Ctrl+C في نافذة PowerShell

📞 الدعم الفني:
═══════════════

في حالة وجود أي مشاكل أو استفسارات:
• راجع ملف السجلات: system.log
• تحقق من ملف التكوين: app-config.json
• راجع الدليل الشامل: ULTIMATE_TEMPLATES_SYSTEM.md

═══════════════════════════════════════════════════════════════════

🎉 استمتع بتجربة النظام المذهل! 🎉

تم تطوير هذا النظام بعناية فائقة ليوفر لك أفضل تجربة
في إدارة السفر والسياحة مع قوالب مذهلة وميزات متطورة.

═══════════════════════════════════════════════════════════════════
