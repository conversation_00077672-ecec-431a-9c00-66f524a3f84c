<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الإشعارات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #1f2937;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }

        .buttons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        button {
            padding: 1rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: white;
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .success { background: #10b981; }
        .error { background: #ef4444; }
        .warning { background: #f59e0b; }
        .info { background: #3b82f6; }
        .loading { background: #8b5cf6; }
        .multiple { background: #06b6d4; }
        .flood { background: #f97316; }
        .clear { background: #6b7280; }

        .features {
            background: #f9fafb;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .features h3 {
            margin-top: 0;
            color: #374151;
        }

        .features ul {
            color: #6b7280;
            line-height: 1.6;
        }

        .status {
            text-align: center;
            padding: 1rem;
            background: #dbeafe;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            color: #1e40af;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 اختبار نظام الإشعارات المحسن</h1>
        
        <div class="status">
            ✅ النظام جاهز للاختبار - اضغط على أي زر لاختبار الإشعارات
        </div>

        <div class="buttons-grid">
            <button class="success" onclick="window.open('http://localhost:3001/simple-notification-test', '_blank')">
                🚀 فتح صفحة الاختبار
            </button>
            
            <button class="info" onclick="window.open('http://localhost:3001/login', '_blank')">
                🔐 تسجيل الدخول
            </button>
            
            <button class="warning" onclick="window.open('http://localhost:3001', '_blank')">
                🏠 الصفحة الرئيسية
            </button>
            
            <button class="clear" onclick="window.open('http://localhost:3001/test-notifications-enhanced', '_blank')">
                🔔 اختبار محمي
            </button>
        </div>

        <div class="features">
            <h3>📋 ميزات النظام المحسن:</h3>
            <ul>
                <li>✅ منع تكرار الإشعارات المتشابهة</li>
                <li>🔢 حد أقصى 5 إشعارات معروضة</li>
                <li>⏰ إزالة تلقائية بعد 4 ثوانٍ</li>
                <li>🧹 تنظيف دوري كل 10 ثوانٍ</li>
                <li>🗑️ زر مسح الكل عند وجود أكثر من إشعارين</li>
                <li>📱 تصميم متجاوب للشاشات الصغيرة</li>
                <li>🎭 تأثيرات حركية سلسة</li>
                <li>🎯 تحسينات الأداء</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 2rem; color: #6b7280;">
            <p>🛠️ تم إصلاح مشكلة تراكم الإشعارات بنجاح!</p>
            <p>💡 النظام الآن محصن ضد الفيضان ويعمل بكفاءة عالية</p>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 مرحباً بك في نظام الإشعارات المحسن!');
            console.log('🔧 تم إصلاح جميع مشاكل التراكم والأداء');
            console.log('🚀 النظام جاهز للاستخدام');
        }, 1000);
    </script>
</body>
</html>
