"""
إعدادات التطبيق الأساسية
Application Core Configuration
"""

from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import validator
import os
from pathlib import Path

class Settings(BaseSettings):
    # إعدادات التطبيق الأساسية
    PROJECT_NAME: str = "نظام محاسبي لوكالات السفريات"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = True
    
    # إعدادات الأمان
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # إعدادات قاعدة البيانات
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_NAME: str = "travel_accounting"
    DB_USER: str = "root"
    DB_PASSWORD: str = ""
    DATABASE_URL: Optional[str] = None
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"mysql+pymysql://{values.get('DB_USER')}:{values.get('DB_PASSWORD')}@{values.get('DB_HOST')}:{values.get('DB_PORT')}/{values.get('DB_NAME')}"
    
    # إعدادات Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # إعدادات CORS
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
    ]

    # مضيفون موثوقون للنشر
    ALLOWED_HOSTS: List[str] = [
        "*"
    ]
    
    # إعدادات البريد الإلكتروني
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # إعدادات رفع الملفات
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "uploads"
    ALLOWED_FILE_TYPES: List[str] = [
        "image/jpeg", "image/png", "image/gif",
        "application/pdf", "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ]
    
    # إعدادات النسخ الاحتياطي
    BACKUP_DIR: str = "backups"
    AUTO_BACKUP_ENABLED: bool = True
    BACKUP_RETENTION_DAYS: int = 30
    
    # إعدادات التقارير
    REPORTS_DIR: str = "reports"
    TEMP_DIR: str = "temp"
    
    # إعدادات العملات
    DEFAULT_CURRENCY: str = "USD"
    SUPPORTED_CURRENCIES: List[str] = ["USD", "EUR", "SAR", "AED", "EGP", "JOD"]
    
    # إعدادات النظام
    ITEMS_PER_PAGE: int = 20
    MAX_ITEMS_PER_PAGE: int = 100
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# إنشاء مثيل الإعدادات
settings = Settings()

# إنشاء المجلدات المطلوبة
def create_directories():
    directories = [
        settings.UPLOAD_DIR,
        settings.BACKUP_DIR,
        settings.REPORTS_DIR,
        settings.TEMP_DIR,
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

# تشغيل إنشاء المجلدات عند استيراد الملف
create_directories()