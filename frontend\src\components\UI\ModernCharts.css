/* Modern Charts Components Styles */

/* ===== PROGRESS CIRCLE ===== */
.progress-circle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.progress-circle-svg {
  transform: rotate(-90deg);
}

.progress-circle-background {
  opacity: 0.3;
}

.progress-circle-progress {
  transition: stroke-dashoffset 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-circle-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.progress-circle-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  line-height: 1;
}

.progress-circle-label {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  margin-top: var(--space-1);
  line-height: 1;
}

/* ===== PROGRESS BAR ===== */
.progress-bar {
  width: 100%;
}

.progress-bar-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
}

.progress-bar-value {
  font-size: var(--text-xs);
  color: var(--neutral-600);
}

.progress-bar-track {
  width: 100%;
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  border-radius: inherit;
  transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-bar--striped .progress-bar-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

.progress-bar--animated .progress-bar-fill::before {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }
  100% {
    background-position: 0 0;
  }
}

.progress-bar-value-only {
  text-align: center;
  margin-top: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
}

/* ===== BAR CHART ===== */
.bar-chart {
  display: inline-block;
  background: var(--neutral-0);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
}

.bar-chart-svg {
  overflow: visible;
}

.bar-chart-rect {
  transition: all var(--transition-base);
  cursor: pointer;
}

.bar-chart-rect:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.bar-chart-value {
  font-family: var(--font-family-mono);
  font-weight: var(--font-semibold);
}

.bar-chart-label {
  font-weight: var(--font-medium);
}

/* ===== LINE CHART ===== */
.line-chart {
  display: inline-block;
  background: var(--neutral-0);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
}

.line-chart-svg {
  overflow: visible;
}

.line-chart-path {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all var(--transition-base);
}

.line-chart-point {
  transition: all var(--transition-base);
  cursor: pointer;
}

.line-chart-point:hover {
  r: 6;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
}

.line-chart-label {
  font-weight: var(--font-medium);
}

/* ===== DONUT CHART ===== */
.donut-chart {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  background: var(--neutral-0);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
}

.donut-chart-svg {
  flex-shrink: 0;
}

.donut-chart-segment {
  transition: all var(--transition-base);
  cursor: pointer;
}

.donut-chart-segment:hover {
  opacity: 0.8;
  transform: scale(1.02);
  transform-origin: center;
}

.donut-chart-legend {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  flex: 1;
}

.donut-chart-legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
}

.donut-chart-legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.donut-chart-legend-label {
  font-weight: var(--font-medium);
  color: var(--neutral-700);
  flex: 1;
}

.donut-chart-legend-value {
  font-family: var(--font-family-mono);
  font-weight: var(--font-semibold);
  color: var(--neutral-600);
  font-size: var(--text-xs);
}

/* ===== METRIC DISPLAY ===== */
.metric-display {
  background: var(--neutral-0);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-base);
}

.metric-display:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-display--sm {
  padding: var(--space-4);
}

.metric-display--lg {
  padding: var(--space-8);
}

.metric-display-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.metric-display-icon {
  font-size: var(--text-2xl);
  opacity: 0.8;
}

.metric-display--sm .metric-display-icon {
  font-size: var(--text-xl);
}

.metric-display--lg .metric-display-icon {
  font-size: var(--text-3xl);
}

.metric-display-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-display-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  line-height: 1;
  margin-bottom: var(--space-3);
  font-family: var(--font-family-mono);
}

.metric-display--sm .metric-display-value {
  font-size: var(--text-2xl);
}

.metric-display--lg .metric-display-value {
  font-size: var(--text-4xl);
}

.metric-display-change {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.metric-display-change--positive {
  color: var(--success-600);
}

.metric-display-change--negative {
  color: var(--error-600);
}

.metric-display-change--neutral {
  color: var(--neutral-600);
}

.metric-display-change-icon {
  font-size: var(--text-base);
}

.metric-display-change-value {
  font-family: var(--font-family-mono);
}

.metric-display-trend {
  margin-top: var(--space-3);
  opacity: 0.7;
}

.metric-display-trend-svg {
  width: 100%;
  height: 20px;
}

/* ===== GAUGE CHART ===== */
.gauge-chart {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  background: var(--neutral-0);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
}

.gauge-chart-svg {
  overflow: visible;
}

.gauge-chart-background {
  opacity: 0.3;
}

.gauge-chart-progress {
  transition: stroke-dashoffset 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.gauge-chart-content {
  position: absolute;
  bottom: var(--space-4);
  text-align: center;
}

.gauge-chart-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  line-height: 1;
  font-family: var(--font-family-mono);
}

.gauge-chart-label {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  margin-top: var(--space-1);
  font-weight: var(--font-medium);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .donut-chart {
    flex-direction: column;
    text-align: center;
  }
  
  .donut-chart-legend {
    width: 100%;
  }
  
  .metric-display {
    padding: var(--space-4);
  }
  
  .metric-display-value {
    font-size: var(--text-2xl);
  }
  
  .metric-display--lg .metric-display-value {
    font-size: var(--text-3xl);
  }
  
  .bar-chart,
  .line-chart {
    padding: var(--space-3);
  }
  
  .gauge-chart {
    padding: var(--space-3);
  }
}

@media (max-width: 480px) {
  .progress-circle-value {
    font-size: var(--text-lg);
  }
  
  .progress-circle-label {
    font-size: var(--text-xs);
  }
  
  .metric-display-header {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
  
  .metric-display-value {
    font-size: var(--text-xl);
  }
  
  .donut-chart-legend-item {
    flex-direction: column;
    text-align: center;
    gap: var(--space-1);
  }
  
  .gauge-chart-value {
    font-size: var(--text-xl);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .bar-chart,
  .line-chart,
  .donut-chart,
  .metric-display,
  .gauge-chart {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .progress-circle-value,
  .metric-display-value,
  .gauge-chart-value {
    color: var(--neutral-200);
  }
  
  .progress-circle-label,
  .progress-bar-label,
  .progress-bar-value-only,
  .metric-display-title,
  .donut-chart-legend-label,
  .gauge-chart-label {
    color: var(--neutral-400);
  }
  
  .progress-bar-value,
  .donut-chart-legend-value {
    color: var(--neutral-500);
  }
  
  .bar-chart-value,
  .bar-chart-label,
  .line-chart-label {
    fill: var(--neutral-400);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .bar-chart,
  .line-chart,
  .donut-chart,
  .metric-display,
  .gauge-chart {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .progress-circle-value,
  .metric-display-value,
  .gauge-chart-value {
    color: black !important;
  }
  
  .progress-circle-label,
  .progress-bar-label,
  .metric-display-title,
  .donut-chart-legend-label,
  .gauge-chart-label {
    color: #666 !important;
  }
  
  .bar-chart-rect:hover,
  .line-chart-point:hover,
  .donut-chart-segment:hover {
    transform: none !important;
    opacity: 1 !important;
  }
  
  .metric-display:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .progress-circle-progress,
  .progress-bar-fill,
  .gauge-chart-progress {
    transition: none !important;
  }
  
  .bar-chart-rect,
  .line-chart-path,
  .line-chart-point,
  .donut-chart-segment,
  .metric-display {
    transition: none !important;
  }
  
  .progress-bar--animated .progress-bar-fill::before {
    animation: none !important;
  }
  
  .bar-chart-rect:hover,
  .line-chart-point:hover,
  .donut-chart-segment:hover,
  .metric-display:hover {
    transform: none !important;
  }
}

/* Focus styles for keyboard navigation */
.bar-chart-rect:focus,
.line-chart-point:focus,
.donut-chart-segment:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bar-chart,
  .line-chart,
  .donut-chart,
  .metric-display,
  .gauge-chart {
    border: 2px solid currentColor !important;
  }
  
  .progress-bar-track {
    border: 1px solid currentColor;
  }
  
  .donut-chart-legend-color {
    border: 1px solid currentColor;
  }
}