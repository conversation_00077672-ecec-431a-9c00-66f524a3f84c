"""
إعدادات قاعدة البيانات
Database Configuration and Connection
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
import logging

from app.core.config import settings

# إعداد المحرك
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=settings.DEBUG,  # إظهار استعلامات SQL في وضع التطوير
)

# إعداد جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# إعداد البيانات الوصفية
metadata = MetaData()

# إنشاء الفئة الأساسية للنماذج
Base = declarative_base(metadata=metadata)

# دالة للحصول على جلسة قاعدة البيانات
def get_db():
    """
    إنشاء جلسة قاعدة بيانات جديدة لكل طلب
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logging.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

# دالة لاختبار الاتصال بقاعدة البيانات
def test_database_connection():
    """
    اختبار الاتصال بقاعدة البيانات
    """
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        logging.error(f"Database connection failed: {e}")
        return False

# دالة لإنشاء جداول قاعدة البيانات
def create_tables():
    """
    إنشاء جميع الجداول في قاعدة البيانات
    """
    try:
        Base.metadata.create_all(bind=engine)
        logging.info("Database tables created successfully")
        return True
    except Exception as e:
        logging.error(f"Failed to create database tables: {e}")
        return False

# دالة لحذف جميع الجداول (للاختبار فقط)
def drop_tables():
    """
    حذف جميع الجداول من قاعدة البيانات
    تستخدم للاختبار فقط
    """
    try:
        Base.metadata.drop_all(bind=engine)
        logging.info("Database tables dropped successfully")
        return True
    except Exception as e:
        logging.error(f"Failed to drop database tables: {e}")
        return False