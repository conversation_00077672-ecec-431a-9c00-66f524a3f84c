@echo off
chcp 65001 >nul
title اختبار المميزات الجديدة - عرض العملاء الشامل

echo.
echo ========================================
echo    🧪 اختبار المميزات الجديدة
echo      عرض العملاء الشامل
echo ========================================
echo.

echo 🌐 فتح النظام...
start http://localhost:3000/customers

echo.
echo 📋 قائمة اختبار المميزات الجديدة:
echo.
echo ✅ 1. عرض الجدول الشامل:
echo    □ تحقق من ظهور 21 عمود
echo    □ تحقق من التمرير الأفقي
echo    □ تحقق من الألوان المميزة
echo    □ تحقق من أحجام الخطوط
echo.
echo ✅ 2. اختبار البيانات:
echo    □ اسم العميل (عمود 1)
echo    □ الجوال (عمود 2)
echo    □ رقم الجواز (أزرق - عمود 3)
echo    □ المهنة (عمود 4)
echo    □ الإيميل (عمود 5)
echo    □ اسم الوكيل (عمود 6)
echo    □ مكتب التفويض (عمود 7)
echo    □ رقم الطلب (بنفسجي - عمود 8)
echo    □ نوع الفيزا (ملون - عمود 9)
echo    □ رقم الصادر (عمود 10)
echo    □ اسم الشركة (عمود 11)
echo    □ تاريخ التسليم (عمود 12)
echo    □ تاريخ الترحيل (عمود 13)
echo    □ رسوم المعاملة (أخضر - عمود 14)
echo    □ رسوم الفيزا (أزرق - عمود 15)
echo    □ العملة (عمود 16)
echo    □ حالة المعاملة (ملونة - عمود 17)
echo    □ حالة السداد (ملونة - عمود 18)
echo    □ حالة التسليم (ملونة - عمود 19)
echo    □ حالة العميل (قابلة للتعديل - عمود 20)
echo    □ الإجراءات (عمود 21)
echo.
echo ✅ 3. اختبار أزرار الأدوات:
echo    □ زر الطباعة (🖨️ طباعة)
echo    □ زر التصدير (📊 تصدير Excel)
echo    □ زر الاستيراد (📥 استيراد)
echo.
echo ✅ 4. اختبار الطباعة:
echo    □ اضغط زر الطباعة
echo    □ تحقق من فتح نافذة جديدة
echo    □ تحقق من التصميم الاحترافي
echo    □ تحقق من شعار الشركة
echo    □ تحقق من تاريخ التقرير
echo    □ تحقق من الإحصائيات
echo    □ جرب الطباعة الفعلية
echo.
echo ✅ 5. اختبار التصدير:
echo    □ اضغط زر التصدير
echo    □ تحقق من تحميل ملف CSV
echo    □ افتح الملف في Excel
echo    □ تحقق من جميع الأعمدة الـ29
echo    □ تحقق من البيانات العربية
echo    □ تحقق من اسم الملف (يحتوي على التاريخ)
echo.
echo ✅ 6. اختبار الاستيراد:
echo    □ استخدم الملف المُصدر كنموذج
echo    □ أضف عميل جديد في الملف
echo    □ احفظ الملف كـ CSV
echo    □ اضغط زر الاستيراد
echo    □ اختر الملف المحدث
echo    □ تحقق من رسالة التأكيد
echo    □ تحقق من ظهور العميل الجديد
echo.
echo ✅ 7. اختبار الإحصائيات:
echo    □ تحقق من إحصائيات أسفل الجدول
echo    □ إجمالي العملاء المعروضين
echo    □ إجمالي رسوم المعاملات
echo    □ إجمالي رسوم الفيزا
echo    □ المعاملات المكتملة
echo    □ جرب الفلترة وراقب تغير الإحصائيات
echo.
echo ✅ 8. اختبار التفاعل:
echo    □ جرب تغيير حالة عميل
echo    □ جرب حذف عميل
echo    □ جرب البحث والفلترة
echo    □ تحقق من تحديث الإحصائيات
echo.
echo ✅ 9. اختبار التصميم المتجاوب:
echo    □ جرب تصغير النافذة
echo    □ تحقق من ظهور التمرير الأفقي
echo    □ تحقق من وضوح النصوص
echo    □ تحقق من عمل الأزرار
echo.
echo ✅ 10. اختبار الأداء:
echo    □ تحقق من سرعة التحميل
echo    □ تحقق من سلاسة التمرير
echo    □ تحقق من استجابة الأزرار
echo    □ تحقق من عدم وجود أخطاء في الكونسول
echo.
echo ========================================
echo.
echo 💡 نصائح للاختبار:
echo.
echo 🔍 للتحقق من الأخطاء:
echo    - اضغط F12 لفتح أدوات المطور
echo    - تحقق من تبويب Console
echo    - تأكد من عدم وجود أخطاء حمراء
echo.
echo 📊 لاختبار التصدير/الاستيراد:
echo    - استخدم Excel أو Google Sheets
echo    - تأكد من دعم UTF-8 للنصوص العربية
echo    - احفظ الملف بتنسيق CSV عند التعديل
echo.
echo 🖨️ لاختبار الطباعة:
echo    - جرب معاينة الطباعة أولاً
echo    - تحقق من التنسيق في المعاينة
echo    - جرب طباعة صفحة واحدة كاختبار
echo.
echo ========================================
echo.
echo 🎯 معايير النجاح:
echo.
echo ✅ جميع الأعمدة الـ21 تظهر بوضوح
echo ✅ الألوان والتنسيق يعملان بشكل صحيح
echo ✅ أزرار الطباعة والتصدير والاستيراد تعمل
echo ✅ الإحصائيات تحسب بدقة
echo ✅ التصميم متجاوب ومريح للاستخدام
echo ✅ لا توجد أخطاء في وحدة التحكم
echo ✅ الأداء سريع ومستقر
echo.
echo ========================================
echo.
echo 🎉 إذا نجحت جميع الاختبارات:
echo    النظام جاهز للاستخدام الفعلي!
echo.
echo 🔧 إذا فشل أي اختبار:
echo    استخدم ملف "إصلاح_سريع.bat"
echo.
echo اضغط أي مفتاح للخروج...
pause >nul