@echo off
chcp 65001 >nul
title نظام شراء للسفر والسياحة - Sharau Travel System

echo.
echo ╔══════════════════════════════════════════════════════════════════╗
echo ║                                                                  ║
echo ║    ✈️  نظام شراء للسفر والسياحة المتطور  ✈️                    ║
echo ║         Sharau Travel ^& Tourism System                          ║
echo ║                                                                  ║
echo ║    🚀 التشغيل السريع - Quick Start 🚀                          ║
echo ║                                                                  ║
echo ╚══════════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔄 تشغيل النظام...
echo.

REM تشغيل السكربت الرئيسي
where pwsh >nul 2>nul
if %errorlevel%==0 (
    pwsh -NoLogo -NoProfile -ExecutionPolicy Bypass -File "start-sharau-app.ps1"
) else (
    powershell.exe -NoLogo -NoProfile -ExecutionPolicy Bypass -File "start-sharau-app.ps1"
)

echo.
echo 🎯 تم إنهاء النظام
pause