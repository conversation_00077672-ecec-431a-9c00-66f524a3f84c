"""
نماذج المحاسبة والمالية
Accounting and Financial Models
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Decimal, ForeignKey, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.models.base import BaseModel

class AccountType(PyEnum):
    """أنواع الحسابات"""
    ASSET = "asset"              # أصول
    LIABILITY = "liability"      # خصوم
    EQUITY = "equity"           # حقوق الملكية
    REVENUE = "revenue"         # إيرادات
    EXPENSE = "expense"         # مصروفات

class AccountCategory(PyEnum):
    """فئات الحسابات"""
    CURRENT_ASSET = "current_asset"           # أصول متداولة
    FIXED_ASSET = "fixed_asset"               # أصول ثابتة
    CURRENT_LIABILITY = "current_liability"   # خصوم متداولة
    LONG_TERM_LIABILITY = "long_term_liability"  # خصوم طويلة الأجل
    OPERATING_REVENUE = "operating_revenue"   # إيرادات تشغيلية
    OTHER_REVENUE = "other_revenue"          # إيرادات أخرى
    OPERATING_EXPENSE = "operating_expense"   # مصروفات تشغيلية
    ADMINISTRATIVE_EXPENSE = "administrative_expense"  # مصروفات إدارية

class TransactionType(PyEnum):
    """أنواع المعاملات"""
    DEBIT = "debit"    # مدين
    CREDIT = "credit"  # دائن

class JournalEntryStatus(PyEnum):
    """حالة القيد"""
    DRAFT = "draft"        # مسودة
    POSTED = "posted"      # مرحل
    REVERSED = "reversed"  # معكوس

class Account(BaseModel):
    """
    دليل الحسابات
    """
    __tablename__ = "accounts"
    
    # معلومات الحساب
    account_code = Column(String(20), unique=True, nullable=False, index=True)
    account_name = Column(String(100), nullable=False)
    account_name_en = Column(String(100), nullable=True)
    
    # تصنيف الحساب
    account_type = Column(Enum(AccountType), nullable=False)
    account_category = Column(Enum(AccountCategory), nullable=False)
    
    # الهيكل الهرمي
    parent_account_id = Column(Integer, ForeignKey('accounts.id'), nullable=True)
    level = Column(Integer, default=1)
    is_parent = Column(Boolean, default=False)
    
    # إعدادات الحساب
    is_active = Column(Boolean, default=True)
    allow_posting = Column(Boolean, default=True)  # السماح بالترحيل المباشر
    
    # معلومات إضافية
    description = Column(Text, nullable=True)
    opening_balance = Column(Decimal(15, 2), default=0.00)
    current_balance = Column(Decimal(15, 2), default=0.00)
    
    # العلاقات
    parent_account = relationship("Account", remote_side="Account.id")
    child_accounts = relationship("Account", remote_side="Account.parent_account_id")
    journal_entries = relationship("JournalEntryLine", back_populates="account")
    
    @property
    def full_code(self):
        """الكود الكامل للحساب"""
        if self.parent_account:
            return f"{self.parent_account.full_code}.{self.account_code}"
        return self.account_code
    
    def __repr__(self):
        return f"<Account(code='{self.account_code}', name='{self.account_name}')>"

class JournalEntry(BaseModel):
    """
    القيود المحاسبية
    """
    __tablename__ = "journal_entries"
    
    # معلومات القيد
    entry_number = Column(String(50), unique=True, nullable=False, index=True)
    entry_date = Column(DateTime, nullable=False)
    status = Column(Enum(JournalEntryStatus), default=JournalEntryStatus.DRAFT)
    
    # معلومات المرجع
    reference_type = Column(String(50), nullable=True)  # invoice, payment, booking
    reference_id = Column(Integer, nullable=True)
    reference_number = Column(String(100), nullable=True)
    
    # وصف القيد
    description = Column(Text, nullable=False)
    
    # معلومات مالية
    total_debit = Column(Decimal(15, 2), default=0.00)
    total_credit = Column(Decimal(15, 2), default=0.00)
    currency = Column(String(3), default='USD')
    exchange_rate = Column(Decimal(10, 4), default=1.0000)
    
    # تواريخ مهمة
    posted_date = Column(DateTime, nullable=True)
    reversed_date = Column(DateTime, nullable=True)
    
    # العلاقات
    lines = relationship("JournalEntryLine", back_populates="journal_entry", cascade="all, delete-orphan")
    
    @property
    def is_balanced(self):
        """هل القيد متوازن"""
        return self.total_debit == self.total_credit
    
    def __repr__(self):
        return f"<JournalEntry(number='{self.entry_number}', date='{self.entry_date}')>"

class JournalEntryLine(BaseModel):
    """
    تفاصيل القيود المحاسبية
    """
    __tablename__ = "journal_entry_lines"
    
    journal_entry_id = Column(Integer, ForeignKey('journal_entries.id'), nullable=False)
    account_id = Column(Integer, ForeignKey('accounts.id'), nullable=False)
    
    # تفاصيل السطر
    line_number = Column(Integer, nullable=False)
    description = Column(Text, nullable=True)
    
    # المبالغ
    debit_amount = Column(Decimal(15, 2), default=0.00)
    credit_amount = Column(Decimal(15, 2), default=0.00)
    
    # معلومات إضافية
    cost_center = Column(String(50), nullable=True)
    project_code = Column(String(50), nullable=True)
    
    # العلاقات
    journal_entry = relationship("JournalEntry", back_populates="lines")
    account = relationship("Account", back_populates="journal_entries")
    
    @property
    def transaction_type(self):
        """نوع المعاملة"""
        if self.debit_amount > 0:
            return TransactionType.DEBIT
        return TransactionType.CREDIT
    
    @property
    def amount(self):
        """المبلغ"""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount
    
    def __repr__(self):
        return f"<JournalEntryLine(account='{self.account.account_code}', amount={self.amount})>"

class Invoice(BaseModel):
    """
    الفواتير
    """
    __tablename__ = "invoices"
    
    # معلومات الفاتورة
    invoice_number = Column(String(50), unique=True, nullable=False, index=True)
    invoice_type = Column(String(20), nullable=False)  # sales, purchase
    invoice_date = Column(DateTime, nullable=False)
    due_date = Column(DateTime, nullable=True)
    
    # العلاقات
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    booking_id = Column(Integer, ForeignKey('bookings.id'), nullable=True)
    
    # المعلومات المالية
    subtotal = Column(Decimal(15, 2), default=0.00)
    tax_amount = Column(Decimal(15, 2), default=0.00)
    discount_amount = Column(Decimal(15, 2), default=0.00)
    total_amount = Column(Decimal(15, 2), default=0.00)
    paid_amount = Column(Decimal(15, 2), default=0.00)
    currency = Column(String(3), default='USD')
    
    # حالة الفاتورة
    status = Column(String(20), default='draft')  # draft, sent, paid, overdue, cancelled
    
    # معلومات إضافية
    description = Column(Text, nullable=True)
    terms_conditions = Column(Text, nullable=True)
    
    # العلاقات
    customer = relationship("Customer", back_populates="invoices")
    supplier = relationship("Supplier")
    booking = relationship("Booking")
    lines = relationship("InvoiceLine", back_populates="invoice", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="invoice")
    
    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount
    
    @property
    def is_paid(self):
        """هل الفاتورة مدفوعة"""
        return self.paid_amount >= self.total_amount
    
    def __repr__(self):
        return f"<Invoice(number='{self.invoice_number}', total={self.total_amount})>"

class InvoiceLine(BaseModel):
    """
    تفاصيل الفواتير
    """
    __tablename__ = "invoice_lines"
    
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=False)
    
    # تفاصيل السطر
    line_number = Column(Integer, nullable=False)
    description = Column(Text, nullable=False)
    quantity = Column(Decimal(10, 2), default=1.00)
    unit_price = Column(Decimal(12, 2), nullable=False)
    discount_percentage = Column(Decimal(5, 2), default=0.00)
    tax_percentage = Column(Decimal(5, 2), default=0.00)
    
    # المبالغ المحسوبة
    line_total = Column(Decimal(15, 2), nullable=False)
    discount_amount = Column(Decimal(15, 2), default=0.00)
    tax_amount = Column(Decimal(15, 2), default=0.00)
    net_amount = Column(Decimal(15, 2), nullable=False)
    
    # العلاقات
    invoice = relationship("Invoice", back_populates="lines")
    
    def __repr__(self):
        return f"<InvoiceLine(description='{self.description}', amount={self.net_amount})>"

class Payment(BaseModel):
    """
    المدفوعات
    """
    __tablename__ = "payments"
    
    # معلومات الدفعة
    payment_number = Column(String(50), unique=True, nullable=False, index=True)
    payment_type = Column(String(20), nullable=False)  # received, paid
    payment_method = Column(String(50), nullable=False)  # cash, bank_transfer, card, check
    payment_date = Column(DateTime, nullable=False)
    
    # العلاقات
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=True)
    
    # المعلومات المالية
    amount = Column(Decimal(15, 2), nullable=False)
    currency = Column(String(3), default='USD')
    exchange_rate = Column(Decimal(10, 4), default=1.0000)
    
    # معلومات إضافية
    reference_number = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    bank_charges = Column(Decimal(10, 2), default=0.00)
    
    # العلاقات
    customer = relationship("Customer", back_populates="payments")
    supplier = relationship("Supplier")
    invoice = relationship("Invoice", back_populates="payments")
    
    def __repr__(self):
        return f"<Payment(number='{self.payment_number}', amount={self.amount})>"

class BankAccount(BaseModel):
    """
    الحسابات البنكية
    """
    __tablename__ = "bank_accounts"
    
    # معلومات الحساب
    account_name = Column(String(100), nullable=False)
    account_number = Column(String(50), nullable=False)
    iban = Column(String(50), nullable=True)
    swift_code = Column(String(20), nullable=True)
    
    # معلومات البنك
    bank_name = Column(String(100), nullable=False)
    bank_branch = Column(String(100), nullable=True)
    bank_address = Column(Text, nullable=True)
    
    # معلومات الحساب
    currency = Column(String(3), default='USD')
    current_balance = Column(Decimal(15, 2), default=0.00)
    opening_balance = Column(Decimal(15, 2), default=0.00)
    
    # ربط بدليل الحسابات
    chart_account_id = Column(Integer, ForeignKey('accounts.id'), nullable=True)
    
    # العلاقات
    chart_account = relationship("Account")
    
    def __repr__(self):
        return f"<BankAccount(name='{self.account_name}', bank='{self.bank_name}')>"