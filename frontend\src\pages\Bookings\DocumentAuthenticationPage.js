import React, { useState, useEffect } from 'react';
import { BookingActions, BulkActions, BookingDetailsModal } from '../../components/Bookings';
import { printBooking, printMultipleBookings, savePDF, saveMultiplePDF } from '../../utils/printService';

const DocumentAuthenticationPage = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDocType, setFilterDocType] = useState('all');
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);

  const [newBooking, setNewBooking] = useState({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    documentType: 'certificate', // certificate, diploma, contract, medical, legal, commercial
    documentTitle: '',
    issuingAuthority: '',
    destinationCountry: '',
    authenticationType: 'ministry', // ministry, embassy, consulate, apostille
    urgentService: false,
    numberOfCopies: 1,
    deliveryMethod: 'pickup', // pickup, delivery
    deliveryAddress: '',
    amount: '',
    paidAmount: 0,
    currency: 'SAR',
    status: 'pending', // pending, in-progress, ministry-done, embassy-done, ready, delivered, cancelled
    paymentStatus: 'unpaid',
    bookingReference: '',
    expectedDelivery: '',
    notes: ''
  });

  useEffect(() => {
    setTimeout(() => {
      setBookings([
        {
          id: 1,
          customerName: 'أحمد سالم المحمد',
          customerPhone: '+966501234567',
          customerEmail: '<EMAIL>',
          documentType: 'certificate',
          documentTitle: 'شهادة البكالوريوس في الهندسة',
          issuingAuthority: 'جامعة الملك سعود',
          destinationCountry: 'كندا',
          authenticationType: 'apostille',
          urgentService: true,
          numberOfCopies: 2,
          deliveryMethod: 'pickup',
          amount: 800,
          paidAmount: 800,
          currency: 'SAR',
          status: 'ready',
          paymentStatus: 'paid',
          bookingReference: 'DOC2024001',
          expectedDelivery: '2024-02-20',
          bookingDate: '2024-01-15',
          notes: 'تعميد عاجل للهجرة'
        },
        {
          id: 2,
          customerName: 'فاطمة علي الأحمد',
          customerPhone: '+966507654321',
          customerEmail: '<EMAIL>',
          documentType: 'medical',
          documentTitle: 'تقرير طبي شامل',
          issuingAuthority: 'مستشفى الملك فهد',
          destinationCountry: 'ألمانيا',
          authenticationType: 'embassy',
          urgentService: false,
          numberOfCopies: 1,
          deliveryMethod: 'delivery',
          deliveryAddress: 'الرياض، حي الملز',
          amount: 600,
          paidAmount: 300,
          currency: 'SAR',
          status: 'in-progress',
          paymentStatus: 'partial',
          bookingReference: 'DOC2024002',
          expectedDelivery: '2024-02-25',
          bookingDate: '2024-01-14',
          notes: 'تقرير طبي للعلاج'
        },
        {
          id: 3,
          customerName: 'محمد يوسف الخالد',
          customerPhone: '+966551234567',
          customerEmail: '<EMAIL>',
          documentType: 'commercial',
          documentTitle: 'عقد تجاري',
          issuingAuthority: 'وزارة التجارة',
          destinationCountry: 'الإمارات',
          authenticationType: 'ministry',
          urgentService: false,
          numberOfCopies: 3,
          deliveryMethod: 'pickup',
          amount: 450,
          paidAmount: 0,
          currency: 'SAR',
          status: 'pending',
          paymentStatus: 'unpaid',
          bookingReference: 'DOC2024003',
          expectedDelivery: '2024-02-28',
          bookingDate: '2024-01-13',
          notes: 'عقد تجاري للشراكة'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddBooking = (e) => {
    e.preventDefault();
    const booking = {
      id: bookings.length + 1,
      ...newBooking,
      amount: parseFloat(newBooking.amount),
      paidAmount: parseFloat(newBooking.paidAmount),
      bookingDate: new Date().toISOString().split('T')[0],
      bookingReference: `DOC${new Date().getFullYear()}${String(bookings.length + 1).padStart(3, '0')}`
    };
    setBookings([booking, ...bookings]);
    setNewBooking({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      documentType: 'certificate',
      documentTitle: '',
      issuingAuthority: '',
      destinationCountry: '',
      authenticationType: 'ministry',
      urgentService: false,
      numberOfCopies: 1,
      deliveryMethod: 'pickup',
      deliveryAddress: '',
      amount: '',
      paidAmount: 0,
      currency: 'SAR',
      status: 'pending',
      paymentStatus: 'unpaid',
      bookingReference: '',
      expectedDelivery: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const handleStatusChange = (id, newStatus) => {
    setBookings(bookings.map(booking => 
      booking.id === id ? { ...booking, status: newStatus } : booking
    ));
  };

  const handleDeleteBooking = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
      setBookings(bookings.filter(booking => booking.id !== id));
    }
  };

  // دوال العمليات الجديدة
  const handleViewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowDetailsModal(true);
  };

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking);
    setNewBooking(booking);
    setShowEditForm(true);
  };

  const handleDeleteSingleBooking = (booking) => {
    handleDeleteBooking(booking.id);
  };

  const handlePrintBooking = (booking) => {
    printBooking(booking, 'تعميد الوثائق');
  };

  const handleSavePDFBooking = (booking) => {
    savePDF(booking, 'تعميد الوثائق');
  };

  // دوال العمليات المجمعة
  const handleSelectAll = () => {
    setSelectedBookings(filteredBookings.map(booking => booking.id));
  };

  const handleClearSelection = () => {
    setSelectedBookings([]);
  };

  const handleBulkPrint = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    printMultipleBookings(selectedBookingData, 'تعميد الوثائق');
  };

  const handleBulkSavePDF = (selectedIds) => {
    const selectedBookingData = bookings.filter(booking => selectedIds.includes(booking.id));
    saveMultiplePDF(selectedBookingData, 'تعميد الوثائق');
  };

  const handleBulkDelete = (selectedIds) => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedIds.length} طلب؟`)) {
      setBookings(bookings.filter(booking => !selectedIds.includes(booking.id)));
      setSelectedBookings([]);
    }
  };

  const handleBookingSelection = (bookingId, isSelected) => {
    if (isSelected) {
      setSelectedBookings([...selectedBookings, bookingId]);
    } else {
      setSelectedBookings(selectedBookings.filter(id => id !== bookingId));
    }
  };

  const getDocumentTypeText = (docType) => {
    switch (docType) {
      case 'certificate': return 'شهادة';
      case 'diploma': return 'دبلوم';
      case 'contract': return 'عقد';
      case 'medical': return 'طبي';
      case 'legal': return 'قانوني';
      case 'commercial': return 'تجاري';
      default: return docType;
    }
  };

  const getAuthenticationTypeText = (authType) => {
    switch (authType) {
      case 'ministry': return 'وزارة الخارجية';
      case 'embassy': return 'السفارة';
      case 'consulate': return 'القنصلية';
      case 'apostille': return 'أبوستيل';
      default: return authType;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'معلق';
      case 'in-progress': return 'قيد التنفيذ';
      case 'ministry-done': return 'منجز بالوزارة';
      case 'embassy-done': return 'منجز بالسفارة';
      case 'ready': return 'جاهز';
      case 'delivered': return 'مسلم';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ready': return '#27ae60';
      case 'delivered': return '#8e44ad';
      case 'ministry-done': return '#3498db';
      case 'embassy-done': return '#1abc9c';
      case 'in-progress': return '#f39c12';
      case 'pending': return '#95a5a6';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.documentTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.bookingReference.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || booking.status === filterStatus;
    const matchesDocType = filterDocType === 'all' || booking.documentType === filterDocType;
    return matchesSearch && matchesStatus && matchesDocType;
  });

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '5px solid #f3f3f3',
          borderTop: '5px solid #9b59b6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p style={{ marginTop: '20px', color: '#7f8c8d' }}>جاري تحميل طلبات تعميد الوثائق...</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h2 style={{ margin: 0, color: '#2c3e50', fontSize: '24px' }}>📋 تعميد الوثائق</h2>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>إدارة وتتبع طلبات تعميد الوثائق</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            background: 'linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 25px',
            borderRadius: '10px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          ➕ طلب تعميد جديد
        </button>
      </div>

      {/* Filters */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '15px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        display: 'flex',
        gap: '20px',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        <div style={{ flex: 1, minWidth: '200px' }}>
          <input
            type="text"
            placeholder="🔍 البحث في طلبات التعميد..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '14px',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الحالات</option>
          <option value="pending">معلق</option>
          <option value="in-progress">قيد التنفيذ</option>
          <option value="ministry-done">منجز بالوزارة</option>
          <option value="embassy-done">منجز بالسفارة</option>
          <option value="ready">جاهز</option>
          <option value="delivered">مسلم</option>
          <option value="cancelled">ملغي</option>
        </select>
        <select
          value={filterDocType}
          onChange={(e) => setFilterDocType(e.target.value)}
          style={{
            padding: '12px',
            border: '2px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            minWidth: '120px'
          }}
        >
          <option value="all">جميع الوثائق</option>
          <option value="certificate">شهادة</option>
          <option value="diploma">دبلوم</option>
          <option value="contract">عقد</option>
          <option value="medical">طبي</option>
          <option value="legal">قانوني</option>
          <option value="commercial">تجاري</option>
        </select>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {[
          { title: 'إجمالي الطلبات', value: bookings.length, color: '#9b59b6', icon: '📋' },
          { title: 'جاهز للتسليم', value: bookings.filter(b => b.status === 'ready').length, color: '#27ae60', icon: '✅' },
          { title: 'قيد التنفيذ', value: bookings.filter(b => b.status === 'in-progress').length, color: '#f39c12', icon: '⏳' },
          { title: 'إجمالي المبيعات', value: formatCurrency(bookings.reduce((sum, b) => sum + b.amount, 0)), color: '#8e44ad', icon: '💰' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '15px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: `3px solid ${stat.color}20`,
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '30px', marginBottom: '10px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: stat.color, marginBottom: '5px' }}>
              {stat.value}
            </div>
            <div style={{ fontSize: '14px', color: '#7f8c8d' }}>{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Bulk Actions */}
      <BulkActions
        selectedBookings={selectedBookings}
        onBulkPrint={handleBulkPrint}
        onBulkSavePDF={handleBulkSavePDF}
        onBulkDelete={handleBulkDelete}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />

      {/* Bookings Table */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold', width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedBookings.length === filteredBookings.length && filteredBookings.length > 0}
                    onChange={(e) => e.target.checked ? handleSelectAll() : handleClearSelection()}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>العميل</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الوثيقة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>نوع التعميد</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الوجهة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>التسليم المتوقع</th>
                <th style={{ padding: '15px', textAlign: 'right', fontWeight: 'bold' }}>المبلغ</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الحالة</th>
                <th style={{ padding: '15px', textAlign: 'center', fontWeight: 'bold' }}>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookings.map((booking) => (
                <tr key={booking.id} style={{
                  borderBottom: '1px solid #dee2e6',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <input
                      type="checkbox"
                      checked={selectedBookings.includes(booking.id)}
                      onChange={(e) => handleBookingSelection(booking.id, e.target.checked)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{booking.customerName}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{booking.customerPhone}</div>
                      <div style={{ fontSize: '11px', color: '#95a5a6' }}>{booking.bookingReference}</div>
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>{booking.documentTitle}</div>
                      <div style={{ fontSize: '12px', color: '#7f8c8d' }}>{booking.issuingAuthority}</div>
                      <span style={{
                        padding: '2px 6px',
                        borderRadius: '8px',
                        fontSize: '10px',
                        fontWeight: 'bold',
                        background: '#9b59b620',
                        color: '#9b59b6'
                      }}>
                        {getDocumentTypeText(booking.documentType)}
                      </span>
                      {booking.urgentService && (
                        <span style={{
                          padding: '2px 6px',
                          borderRadius: '8px',
                          fontSize: '10px',
                          fontWeight: 'bold',
                          background: '#e74c3c20',
                          color: '#e74c3c',
                          marginLeft: '5px'
                        }}>
                          ⚡ عاجل
                        </span>
                      )}
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      background: booking.authenticationType === 'apostille' ? '#8e44ad20' : 
                                 booking.authenticationType === 'embassy' ? '#3498db20' : 
                                 booking.authenticationType === 'consulate' ? '#1abc9c20' : '#27ae6020',
                      color: booking.authenticationType === 'apostille' ? '#8e44ad' : 
                             booking.authenticationType === 'embassy' ? '#3498db' : 
                             booking.authenticationType === 'consulate' ? '#1abc9c' : '#27ae60'
                    }}>
                      {getAuthenticationTypeText(booking.authenticationType)}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div style={{ fontWeight: 'bold' }}>
                      🌍 {booking.destinationCountry}
                    </div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      {booking.numberOfCopies} نسخة
                    </div>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <div>📅 {booking.expectedDelivery}</div>
                    <div style={{ fontSize: '11px', color: '#7f8c8d' }}>
                      {booking.deliveryMethod === 'pickup' ? '🏢 استلام' : '🚚 توصيل'}
                    </div>
                  </td>
                  <td style={{ padding: '15px' }}>
                    <div style={{ fontWeight: 'bold', color: '#27ae60' }}>
                      {formatCurrency(booking.amount)}
                    </div>
                    {booking.paidAmount < booking.amount && (
                      <div style={{ fontSize: '11px', color: '#e74c3c' }}>
                        متبقي: {formatCurrency(booking.amount - booking.paidAmount)}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <span style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      background: `${getStatusColor(booking.status)}20`,
                      color: getStatusColor(booking.status)
                    }}>
                      {getStatusText(booking.status)}
                    </span>
                  </td>
                  <td style={{ padding: '15px', textAlign: 'center' }}>
                    <BookingActions
                      booking={booking}
                      onView={handleViewBooking}
                      onEdit={handleEditBooking}
                      onDelete={handleDeleteSingleBooking}
                      onPrint={handlePrintBooking}
                      onSavePDF={handleSavePDFBooking}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Booking Modal */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '700px',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ margin: '0 0 25px 0', color: '#2c3e50' }}>📋 طلب تعميد وثيقة جديد</h2>
            
            <form onSubmit={handleAddBooking}>
              {/* معلومات العميل */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>👤 معلومات العميل</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>اسم العميل</label>
                    <input
                      type="text"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({...newBooking, customerName: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>رقم الهاتف</label>
                    <input
                      type="tel"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({...newBooking, customerPhone: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={newBooking.customerEmail}
                      onChange={(e) => setNewBooking({...newBooking, customerEmail: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* تفاصيل الوثيقة */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>📋 تفاصيل الوثيقة</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع الوثيقة</label>
                    <select
                      value={newBooking.documentType}
                      onChange={(e) => setNewBooking({...newBooking, documentType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="certificate">شهادة</option>
                      <option value="diploma">دبلوم</option>
                      <option value="contract">عقد</option>
                      <option value="medical">طبي</option>
                      <option value="legal">قانوني</option>
                      <option value="commercial">تجاري</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>عنوان الوثيقة</label>
                    <input
                      type="text"
                      value={newBooking.documentTitle}
                      onChange={(e) => setNewBooking({...newBooking, documentTitle: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>الجهة المصدرة</label>
                    <input
                      type="text"
                      value={newBooking.issuingAuthority}
                      onChange={(e) => setNewBooking({...newBooking, issuingAuthority: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>البلد المقصود</label>
                    <input
                      type="text"
                      value={newBooking.destinationCountry}
                      onChange={(e) => setNewBooking({...newBooking, destinationCountry: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* تفاصيل التعميد */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>🏛️ تفاصيل التعميد</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>نوع التعميد</label>
                    <select
                      value={newBooking.authenticationType}
                      onChange={(e) => setNewBooking({...newBooking, authenticationType: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="ministry">وزارة الخارجية</option>
                      <option value="embassy">السفارة</option>
                      <option value="consulate">القنصلية</option>
                      <option value="apostille">أبوستيل</option>
                    </select>
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>عدد النسخ</label>
                    <input
                      type="number"
                      min="1"
                      value={newBooking.numberOfCopies}
                      onChange={(e) => setNewBooking({...newBooking, numberOfCopies: parseInt(e.target.value)})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>التسليم المتوقع</label>
                    <input
                      type="date"
                      value={newBooking.expectedDelivery}
                      onChange={(e) => setNewBooking({...newBooking, expectedDelivery: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontWeight: 'bold' }}>
                      <input
                        type="checkbox"
                        checked={newBooking.urgentService}
                        onChange={(e) => setNewBooking({...newBooking, urgentService: e.target.checked})}
                      />
                      خدمة عاجلة
                    </label>
                  </div>
                </div>
              </div>

              {/* طريقة التسليم */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>🚚 طريقة التسليم</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>طريقة التسليم</label>
                    <select
                      value={newBooking.deliveryMethod}
                      onChange={(e) => setNewBooking({...newBooking, deliveryMethod: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    >
                      <option value="pickup">استلام من المكتب</option>
                      <option value="delivery">توصيل للمنزل</option>
                    </select>
                  </div>
                  {newBooking.deliveryMethod === 'delivery' && (
                    <div>
                      <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>عنوان التوصيل</label>
                      <input
                        type="text"
                        value={newBooking.deliveryAddress}
                        onChange={(e) => setNewBooking({...newBooking, deliveryAddress: e.target.value})}
                        style={{
                          width: '100%',
                          padding: '12px',
                          border: '2px solid #e0e0e0',
                          borderRadius: '8px',
                          fontSize: '14px',
                          boxSizing: 'border-box'
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* المعلومات المالية */}
              <div style={{ marginBottom: '25px' }}>
                <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>💰 المعلومات المالية</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>إجمالي المبلغ</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.amount}
                      onChange={(e) => setNewBooking({...newBooking, amount: e.target.value})}
                      required
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>المبلغ المدفوع</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newBooking.paidAmount}
                      onChange={(e) => setNewBooking({...newBooking, paidAmount: e.target.value})}
                      style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div style={{ marginBottom: '25px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>ملاحظات</label>
                <textarea
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({...newBooking, notes: e.target.value})}
                  rows="3"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '14px',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '15px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  style={{
                    padding: '12px 25px',
                    border: '2px solid #e0e0e0',
                    background: 'white',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 25px',
                    background: 'linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  إضافة الطلب
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onPrint={handlePrintBooking}
        onSavePDF={handleSavePDFBooking}
      />
    </div>
  );
};

export default DocumentAuthenticationPage;