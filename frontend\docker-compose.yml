version: '3.8'

services:
  # Frontend development server
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - REACT_APP_ENV=development
      - REACT_APP_API_URL=http://localhost:8000/api
      - CHOKIDAR_USEPOLLING=true
    stdin_open: true
    tty: true
    networks:
      - travel-network

  # Frontend production build
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - REACT_APP_ENV=production
    networks:
      - travel-network
    depends_on:
      - backend

  # Backend service (placeholder)
  backend:
    image: python:3.9-alpine
    ports:
      - "8000:8000"
    command: echo "Backend service placeholder"
    networks:
      - travel-network

  # Database service (placeholder)
  database:
    image: postgres:13-alpine
    environment:
      POSTGRES_DB: sharaubtravelsoft
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - travel-network

  # Redis for caching
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    networks:
      - travel-network

networks:
  travel-network:
    driver: bridge

volumes:
  postgres_data: