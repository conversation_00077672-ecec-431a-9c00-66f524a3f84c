/* Modern Card Component Styles */

.modern-card {
  background: var(--neutral-0);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--neutral-200);
  overflow: hidden;
  transition: all var(--transition-base);
  position: relative;
}

/* Card Variants */
.modern-card--default {
  /* Default styling already applied */
}

.modern-card--elevated {
  box-shadow: var(--shadow-lg);
}

.modern-card--outlined {
  border: 2px solid var(--neutral-300);
  box-shadow: none;
}

.modern-card--stats {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  border: 1px solid var(--primary-200);
}

.modern-card--feature {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  text-align: center;
}

.modern-card--metric {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
}

/* Card Sizes */
.modern-card--xs {
  padding: var(--space-3);
}

.modern-card--sm {
  padding: var(--space-4);
}

.modern-card--md {
  padding: var(--space-6);
}

.modern-card--lg {
  padding: var(--space-8);
}

.modern-card--xl {
  padding: var(--space-10);
}

/* Hover Effects */
.modern-card--hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-300);
}

.modern-card--hover:hover .modern-card__title {
  color: var(--primary-600);
}

/* Glass Effect */
.modern-card--glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-2xl);
}

/* Gradient Effect */
.modern-card--gradient {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Card Components */
.modern-card__header {
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

.modern-card__body {
  padding: var(--space-6);
}

.modern-card__footer {
  padding: var(--space-4) var(--space-6) var(--space-6) var(--space-6);
  border-top: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

.modern-card__title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
  line-height: var(--leading-tight);
  transition: color var(--transition-fast);
}

.modern-card__subtitle {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-sm);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
}

.modern-card__actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.modern-card__actions--left {
  justify-content: flex-start;
}

.modern-card__actions--center {
  justify-content: center;
}

.modern-card__actions--right {
  justify-content: flex-end;
}

.modern-card__actions--between {
  justify-content: space-between;
}

/* Stats Card Specific Styles */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.stats-card--primary::before {
  background: var(--gradient-primary);
}

.stats-card--secondary::before {
  background: var(--gradient-secondary);
}

.stats-card--success::before {
  background: var(--gradient-success);
}

.stats-card--warning::before {
  background: var(--gradient-warning);
}

.stats-card--error::before {
  background: var(--gradient-error);
}

.stats-card__content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.stats-card__info {
  flex: 1;
}

.stats-card__value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
  margin: var(--space-2) 0;
  line-height: 1;
}

.stats-card__change {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.stats-card__change--positive {
  color: var(--success-600);
}

.stats-card__change--negative {
  color: var(--error-600);
}

.stats-card__change--neutral {
  color: var(--neutral-600);
}

.stats-card__change-icon {
  font-size: var(--text-base);
}

.stats-card__icon {
  font-size: var(--text-4xl);
  opacity: 0.7;
  margin-left: var(--space-4);
}

/* Feature Card Specific Styles */
.feature-card {
  text-align: center;
  transition: all var(--transition-base);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.feature-card__icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
  opacity: 0.8;
}

.feature-card .modern-card__title {
  font-size: var(--text-lg);
  margin-bottom: var(--space-3);
}

.feature-card .modern-card__subtitle {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-6);
}

/* Metric Card Specific Styles */
.metric-card {
  text-align: center;
  position: relative;
}

.metric-card--primary {
  border-left: 4px solid var(--primary-500);
}

.metric-card--secondary {
  border-left: 4px solid var(--secondary-500);
}

.metric-card--success {
  border-left: 4px solid var(--success-500);
}

.metric-card--warning {
  border-left: 4px solid var(--warning-500);
}

.metric-card--error {
  border-left: 4px solid var(--error-500);
}

.metric-card__label {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-card__value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.metric-card__unit {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--neutral-600);
  margin-right: var(--space-1);
}

.metric-card__trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.metric-card__trend--up {
  color: var(--success-600);
}

.metric-card__trend--down {
  color: var(--error-600);
}

.metric-card__trend--neutral {
  color: var(--neutral-600);
}

.metric-card__trend-icon {
  font-size: var(--text-sm);
}

/* Interactive States */
.modern-card:focus {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-card:active {
  transform: translateY(-2px);
}

/* Loading State */
.modern-card--loading {
  position: relative;
  overflow: hidden;
}

.modern-card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-card__header,
  .modern-card__body,
  .modern-card__footer {
    padding: var(--space-4);
  }
  
  .stats-card__content {
    flex-direction: column;
    gap: var(--space-4);
  }
  
  .stats-card__icon {
    margin-left: 0;
    align-self: center;
  }
  
  .stats-card__value {
    font-size: var(--text-2xl);
  }
  
  .metric-card__value {
    font-size: var(--text-xl);
  }
  
  .feature-card__icon {
    font-size: var(--text-4xl);
  }
}

@media (max-width: 480px) {
  .modern-card {
    border-radius: var(--radius-xl);
  }
  
  .modern-card__actions {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .stats-card__value {
    font-size: var(--text-xl);
  }
  
  .metric-card__value {
    font-size: var(--text-lg);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modern-card {
    background: var(--neutral-100);
    border-color: var(--neutral-300);
  }
  
  .modern-card__header,
  .modern-card__footer {
    background: var(--neutral-200);
    border-color: var(--neutral-300);
  }
  
  .modern-card__title {
    color: var(--neutral-900);
  }
  
  .modern-card__subtitle {
    color: var(--neutral-700);
  }
  
  .modern-card--glass {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .stats-card {
    background: linear-gradient(135deg, var(--neutral-200) 0%, var(--neutral-300) 100%);
  }
}

/* Print Styles */
@media print {
  .modern-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .modern-card--hover:hover {
    transform: none !important;
  }
  
  .modern-card--loading::after {
    display: none !important;
  }
}