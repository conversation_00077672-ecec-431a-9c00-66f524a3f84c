/* Modern Table Component Styles */

/* ===== TABLE CONTAINER ===== */
.modern-table-container {
  background: var(--neutral-0);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all var(--transition-base);
}

.modern-table-container--default {
  /* Default styling already applied */
}

.modern-table-container--card {
  box-shadow: var(--shadow-xl);
  border: none;
}

.modern-table-container--minimal {
  background: transparent;
  border: none;
  box-shadow: none;
}

.modern-table-container--compact {
  border-radius: var(--radius-lg);
}

/* ===== TABLE HEADER ===== */
.modern-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  background: var(--neutral-50);
  border-bottom: 1px solid var(--neutral-200);
  gap: var(--space-4);
}

.modern-table-search {
  flex: 1;
  max-width: 400px;
}

.modern-table-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.modern-table-selected-count {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  background: var(--primary-100);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
}

/* ===== TABLE WRAPPER ===== */
.modern-table-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
}

/* ===== TABLE ===== */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family-arabic);
}

.modern-table--striped tbody tr:nth-child(even) {
  background: var(--neutral-50);
}

.modern-table--hover tbody tr:hover {
  background: var(--primary-50);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modern-table--selectable tbody tr {
  cursor: pointer;
}

/* ===== TABLE HEAD ===== */
.modern-table-head {
  background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-200) 100%);
}

.modern-table-head-row {
  border-bottom: 2px solid var(--neutral-300);
}

.modern-table-head-cell {
  padding: var(--space-4) var(--space-6);
  text-align: right;
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  white-space: nowrap;
}

.modern-table-head-cell--sortable {
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-fast);
}

.modern-table-head-cell--sortable:hover {
  background: var(--neutral-200);
  color: var(--primary-600);
}

.modern-table-head-cell--checkbox {
  width: 50px;
  text-align: center;
}

.modern-table-head-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-2);
}

.modern-table-sort-icon {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  transition: color var(--transition-fast);
}

.modern-table-head-cell--sortable:hover .modern-table-sort-icon {
  color: var(--primary-600);
}

/* ===== TABLE BODY ===== */
.modern-table-body {
  /* Body styles */
}

.modern-table-row {
  border-bottom: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
}

.modern-table-row:last-child {
  border-bottom: none;
}

.modern-table-row--selected {
  background: var(--primary-100) !important;
  border-color: var(--primary-300);
}

.modern-table-cell {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-sm);
  color: var(--neutral-700);
  vertical-align: middle;
  line-height: var(--leading-relaxed);
}

.modern-table-cell--checkbox {
  width: 50px;
  text-align: center;
}

/* ===== TABLE CHECKBOX ===== */
.modern-table-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid var(--neutral-400);
  border-radius: var(--radius-sm);
  background: var(--neutral-0);
  cursor: pointer;
  transition: all var(--transition-fast);
  appearance: none;
  position: relative;
}

.modern-table-checkbox:checked {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.modern-table-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--neutral-0);
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
}

.modern-table-checkbox:hover {
  border-color: var(--primary-400);
}

/* ===== EMPTY STATE ===== */
.modern-table-empty {
  text-align: center;
  padding: var(--space-12) var(--space-6);
}

.modern-table-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.modern-table-empty-icon {
  font-size: var(--text-6xl);
  opacity: 0.5;
}

.modern-table-empty-text {
  font-size: var(--text-lg);
  color: var(--neutral-500);
  font-weight: var(--font-medium);
}

/* ===== LOADING STATE ===== */
.modern-table-loading {
  padding: var(--space-6);
}

.modern-table-skeleton {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.modern-table-skeleton-row {
  display: flex;
  gap: var(--space-4);
}

.modern-table-skeleton-cell {
  height: 20px;
  background: linear-gradient(
    90deg,
    var(--neutral-200) 25%,
    var(--neutral-300) 50%,
    var(--neutral-200) 75%
  );
  background-size: 200% 100%;
  border-radius: var(--radius-md);
  animation: skeleton-loading 1.5s infinite;
  flex: 1;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== PAGINATION ===== */
.modern-table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  background: var(--neutral-50);
  border-top: 1px solid var(--neutral-200);
}

.modern-table-pagination-info {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  font-weight: var(--font-medium);
}

.modern-table-pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modern-table-pagination-pages {
  display: flex;
  gap: var(--space-1);
}

/* ===== DATA TABLE ===== */
.data-table {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.data-table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-4);
}

.data-table-title-section {
  flex: 1;
}

.data-table-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--neutral-800);
  line-height: var(--leading-tight);
}

.data-table-subtitle {
  margin: 0;
  font-size: var(--text-base);
  color: var(--neutral-600);
  line-height: var(--leading-relaxed);
}

.data-table-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-shrink: 0;
}

/* ===== COMPACT MODE ===== */
.modern-table-container--compact .modern-table-header {
  padding: var(--space-4);
}

.modern-table-container--compact .modern-table-head-cell,
.modern-table-container--compact .modern-table-cell {
  padding: var(--space-3) var(--space-4);
}

.modern-table-container--compact .modern-table-pagination {
  padding: var(--space-3) var(--space-4);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .modern-table-wrapper {
    overflow-x: scroll;
  }
  
  .modern-table {
    min-width: 600px;
  }
}

@media (max-width: 768px) {
  .modern-table-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .modern-table-search {
    max-width: none;
  }
  
  .modern-table-actions {
    justify-content: space-between;
  }
  
  .modern-table-pagination {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
  
  .modern-table-pagination-controls {
    justify-content: center;
  }
  
  .data-table-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .data-table-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  
  .modern-table-head-cell,
  .modern-table-cell {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-xs);
  }
  
  .modern-table {
    min-width: 500px;
  }
}

@media (max-width: 480px) {
  .modern-table-container {
    border-radius: var(--radius-lg);
  }
  
  .modern-table-header {
    padding: var(--space-4);
  }
  
  .modern-table-pagination {
    padding: var(--space-3);
  }
  
  .modern-table-pagination-pages {
    display: none;
  }
  
  .modern-table-head-cell,
  .modern-table-cell {
    padding: var(--space-2) var(--space-3);
  }
  
  .modern-table-selected-count {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
  }
  
  .data-table-title {
    font-size: var(--text-xl);
  }
  
  .data-table-subtitle {
    font-size: var(--text-sm);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .modern-table-container {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
  }
  
  .modern-table-header,
  .modern-table-pagination {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
  }
  
  .modern-table-head {
    background: linear-gradient(135deg, var(--neutral-700) 0%, var(--neutral-800) 100%);
  }
  
  .modern-table-head-row {
    border-bottom-color: var(--neutral-600);
  }
  
  .modern-table-head-cell {
    color: var(--neutral-200);
  }
  
  .modern-table-head-cell--sortable:hover {
    background: var(--neutral-600);
    color: var(--primary-400);
  }
  
  .modern-table--striped tbody tr:nth-child(even) {
    background: var(--neutral-700);
  }
  
  .modern-table--hover tbody tr:hover {
    background: var(--primary-800);
  }
  
  .modern-table-row {
    border-bottom-color: var(--neutral-600);
  }
  
  .modern-table-row--selected {
    background: var(--primary-800) !important;
    border-color: var(--primary-600);
  }
  
  .modern-table-cell {
    color: var(--neutral-300);
  }
  
  .modern-table-checkbox {
    background: var(--neutral-700);
    border-color: var(--neutral-500);
  }
  
  .modern-table-checkbox:checked {
    background: var(--primary-600);
    border-color: var(--primary-600);
  }
  
  .modern-table-empty-text {
    color: var(--neutral-400);
  }
  
  .modern-table-pagination-info {
    color: var(--neutral-400);
  }
  
  .modern-table-selected-count {
    background: var(--primary-800);
    color: var(--primary-300);
  }
  
  .data-table-title {
    color: var(--neutral-200);
  }
  
  .data-table-subtitle {
    color: var(--neutral-400);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .modern-table-container {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .modern-table-header,
  .modern-table-pagination {
    display: none !important;
  }
  
  .modern-table {
    font-size: 12px !important;
  }
  
  .modern-table-head {
    background: #f0f0f0 !important;
  }
  
  .modern-table-head-cell,
  .modern-table-cell {
    border: 1px solid #000 !important;
    padding: 8px !important;
  }
  
  .modern-table--hover tbody tr:hover {
    background: transparent !important;
    transform: none !important;
    box-shadow: none !important;
  }
  
  .modern-table-row--selected {
    background: #e0e0e0 !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .modern-table-container,
  .modern-table-row,
  .modern-table-head-cell--sortable,
  .modern-table-checkbox {
    transition: none !important;
  }
  
  .modern-table--hover tbody tr:hover {
    transform: none !important;
  }
  
  .modern-table-skeleton-cell {
    animation: none !important;
  }
}

/* Focus styles for keyboard navigation */
.modern-table-head-cell--sortable:focus,
.modern-table-checkbox:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.modern-table-row:focus-within {
  outline: 2px solid var(--primary-500);
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-table-container,
  .modern-table-head-cell,
  .modern-table-cell {
    border: 2px solid currentColor !important;
  }
  
  .modern-table-checkbox {
    border: 2px solid currentColor !important;
  }
  
  .modern-table-row--selected {
    background: currentColor !important;
    color: white !important;
  }
}