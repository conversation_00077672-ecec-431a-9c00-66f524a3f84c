/* Accounting Settings Styles */

.accounting-settings {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.settings-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.save-btn,
.reset-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.save-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.reset-btn {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}

/* Settings Tabs */
.settings-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 25px;
  background: #f8f9fa;
  padding: 5px;
  border-radius: 12px;
  overflow-x: auto;
}

.settings-tabs .tab-btn {
  flex: 1;
  min-width: 120px;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
}

.settings-tabs .tab-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #495057;
}

.settings-tabs .tab-btn.active {
  background: white;
  color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

/* Settings Content */
.settings-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.settings-section {
  padding: 20px 0;
}

.settings-section h4 {
  margin: 0 0 25px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e8ed;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.setting-group label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group.checkbox-group label {
  cursor: pointer;
  user-select: none;
}

.setting-group input[type="text"],
.setting-group input[type="number"],
.setting-group input[type="email"],
.setting-group input[type="password"],
.setting-group select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.setting-group input[type="text"]:focus,
.setting-group input[type="number"]:focus,
.setting-group input[type="email"]:focus,
.setting-group input[type="password"]:focus,
.setting-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-group input[type="text"]:disabled,
.setting-group input[type="number"]:disabled,
.setting-group select:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.setting-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
  cursor: pointer;
}

/* Special Styling for Different Setting Types */
.setting-group input[type="number"] {
  text-align: right;
  font-family: 'Courier New', monospace;
}

.setting-group select {
  cursor: pointer;
}

/* Setting Group Variations */
.setting-group.full-width {
  grid-column: 1 / -1;
}

.setting-group.half-width {
  grid-column: span 1;
}

/* Setting Descriptions */
.setting-description {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
  line-height: 1.4;
}

/* Setting Warnings */
.setting-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 10px 12px;
  margin-top: 8px;
  font-size: 12px;
  color: #856404;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-warning::before {
  content: '⚠️';
  font-size: 14px;
}

/* Setting Success */
.setting-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 10px 12px;
  margin-top: 8px;
  font-size: 12px;
  color: #155724;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-success::before {
  content: '✅';
  font-size: 14px;
}

/* Setting Error */
.setting-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 10px 12px;
  margin-top: 8px;
  font-size: 12px;
  color: #721c24;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-error::before {
  content: '❌';
  font-size: 14px;
}

/* Advanced Settings */
.advanced-settings {
  background: #f8f9fa;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.advanced-settings h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.advanced-settings h5::before {
  content: '🔧';
  font-size: 18px;
}

/* Setting Cards */
.setting-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.setting-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.setting-card h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.setting-card .card-content {
  display: grid;
  gap: 15px;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #667eea;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .accounting-settings {
    padding: 15px;
  }
  
  .settings-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    text-align: center;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .settings-tabs {
    flex-direction: column;
  }
  
  .settings-tabs .tab-btn {
    min-width: auto;
  }
  
  .settings-grid {
    gap: 20px;
  }
  
  .setting-group.checkbox-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .settings-header h3 {
    font-size: 20px;
  }
  
  .settings-section h4 {
    font-size: 18px;
  }
  
  .save-btn,
  .reset-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .setting-group input,
  .setting-group select {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .setting-card {
    padding: 15px;
  }
}

/* Print Styles */
@media print {
  .accounting-settings {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .settings-header {
    border-bottom: 2px solid #000;
    margin-bottom: 20px;
  }
  
  .header-actions,
  .settings-tabs {
    display: none !important;
  }
  
  .settings-section {
    break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .settings-section h4 {
    border-bottom: 1px solid #000;
  }
  
  .setting-card {
    border: 1px solid #000;
    break-inside: avoid;
  }
  
  .setting-group input,
  .setting-group select {
    border: 1px solid #000;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .accounting-settings {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .settings-header h3,
  .settings-section h4 {
    color: #e2e8f0;
  }
  
  .settings-tabs {
    background: #4a5568;
  }
  
  .settings-tabs .tab-btn {
    color: #a0aec0;
  }
  
  .settings-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
  }
  
  .settings-tabs .tab-btn.active {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .setting-group input,
  .setting-group select {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  
  .setting-group input:focus,
  .setting-group select:focus {
    border-color: #667eea;
  }
  
  .setting-card {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .advanced-settings {
    background: #4a5568;
    border-color: #718096;
  }
}