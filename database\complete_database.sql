-- =====================================================
-- قاعدة بيانات نظام شراء السياحة الشاملة
-- Sharau Travel System Complete Database
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS u485286157_sharaubsoft 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE u485286157_sharaubsoft;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER IF NOT EXISTS 'u485286157_sharab'@'localhost' IDENTIFIED BY '';
CREATE USER IF NOT EXISTS 'u485286157_sharab'@'%' IDENTIFIED BY '';
GRANT ALL PRIVILEGES ON u485286157_sharaubsoft.* TO 'u485286157_sharab'@'localhost';
GRANT ALL PRIVILEGES ON u485286157_sharaubsoft.* TO 'u485286157_sharab'@'%';
FLUSH PRIVILEGES;

-- إعدادات قاعدة البيانات
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET time_zone = '+00:00';

-- تفعيل الفهرسة الكاملة للنصوص العربية
SET GLOBAL innodb_ft_min_token_size = 1;
SET GLOBAL ft_min_word_len = 1;

-- إعدادات الأداء
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;

-- =====================================================
-- جداول إدارة المستخدمين والصلاحيات
-- =====================================================

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    avatar VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    is_superuser BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    failed_login_attempts INT DEFAULT 0,
    locked_until DATETIME,
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'UTC',
    theme VARCHAR(20) DEFAULT 'light',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الأدوار
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    INDEX idx_name (name),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    INDEX idx_name (name),
    INDEX idx_module (module),
    INDEX idx_action (action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العلاقة بين المستخدمين والأدوار
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول العلاقة بين الأدوار والصلاحيات
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول العملاء
-- =====================================================

-- جدول العملاء الرئيسي
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    customer_type ENUM('individual', 'corporate', 'government', 'travel_agent') NOT NULL DEFAULT 'individual',
    status ENUM('active', 'inactive', 'blocked', 'vip') NOT NULL DEFAULT 'active',
    
    -- معلومات شخصية/شركة
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    company_name VARCHAR(100),
    title VARCHAR(50),
    
    -- معلومات الاتصال
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    fax VARCHAR(20),
    website VARCHAR(100),
    
    -- العنوان
    address_line1 VARCHAR(200),
    address_line2 VARCHAR(200),
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50),
    
    -- معلومات الهوية
    national_id VARCHAR(50),
    passport_number VARCHAR(50),
    passport_expiry DATETIME,
    visa_number VARCHAR(50),
    visa_expiry DATETIME,
    
    -- معلومات مالية
    credit_limit DECIMAL(15, 2) DEFAULT 0.00,
    current_balance DECIMAL(15, 2) DEFAULT 0.00,
    payment_terms INT DEFAULT 0,
    discount_percentage DECIMAL(5, 2) DEFAULT 0.00,
    
    -- معلومات إضافية
    birth_date DATETIME,
    nationality VARCHAR(50),
    preferred_language VARCHAR(10) DEFAULT 'ar',
    preferred_currency VARCHAR(3) DEFAULT 'USD',
    
    -- معلومات التسويق
    source VARCHAR(50),
    referral_code VARCHAR(20),
    marketing_consent BOOLEAN DEFAULT FALSE,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    
    INDEX idx_customer_code (customer_code),
    INDEX idx_customer_type (customer_type),
    INDEX idx_status (status),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (first_name, last_name, company_name, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول جهات اتصال العملاء
CREATE TABLE IF NOT EXISTS customer_contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    contact_type VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    title VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_contact_type (contact_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مستندات العملاء
CREATE TABLE IF NOT EXISTS customer_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    document_name VARCHAR(100) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    issue_date DATETIME,
    expiry_date DATETIME,
    issuing_authority VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_document_type (document_type),
    INDEX idx_expiry_date (expiry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول ملاحظات العملاء
CREATE TABLE IF NOT EXISTS customer_notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    note_type VARCHAR(20) DEFAULT 'general',
    title VARCHAR(100),
    content TEXT NOT NULL,
    is_private BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id),
    INDEX idx_note_type (note_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول الموردين
-- =====================================================

-- جدول الموردين
CREATE TABLE IF NOT EXISTS suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    supplier_type ENUM('airline', 'hotel', 'car_rental', 'tour_operator', 'insurance', 'other') NOT NULL,
    status ENUM('active', 'inactive', 'blocked', 'preferred') NOT NULL DEFAULT 'active',
    
    -- معلومات الشركة
    company_name VARCHAR(100) NOT NULL,
    trade_name VARCHAR(100),
    registration_number VARCHAR(50),
    tax_number VARCHAR(50),
    
    -- معلومات الاتصال
    email VARCHAR(100),
    phone VARCHAR(20),
    fax VARCHAR(20),
    website VARCHAR(100),
    
    -- العنوان
    address_line1 VARCHAR(200),
    address_line2 VARCHAR(200),
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50),
    
    -- معلومات مالية
    credit_limit DECIMAL(15, 2) DEFAULT 0.00,
    current_balance DECIMAL(15, 2) DEFAULT 0.00,
    payment_terms INT DEFAULT 30,
    commission_rate DECIMAL(5, 2) DEFAULT 0.00,
    
    -- معلومات البنك
    bank_name VARCHAR(100),
    bank_account VARCHAR(50),
    iban VARCHAR(50),
    swift_code VARCHAR(20),
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_supplier_type (supplier_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (company_name, trade_name, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول جهات اتصال الموردين
CREATE TABLE IF NOT EXISTS supplier_contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT NOT NULL,
    contact_type VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    title VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_contact_type (contact_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول الوكلاء
-- =====================================================

-- جدول الوكلاء
CREATE TABLE IF NOT EXISTS agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_code VARCHAR(20) UNIQUE NOT NULL,
    agent_type ENUM('individual', 'company', 'branch') NOT NULL DEFAULT 'individual',
    status ENUM('active', 'inactive', 'suspended', 'terminated') NOT NULL DEFAULT 'active',
    
    -- معلومات شخصية/شركة
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    company_name VARCHAR(100),
    
    -- معلومات الاتصال
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    
    -- العنوان
    address_line1 VARCHAR(200),
    address_line2 VARCHAR(200),
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50),
    
    -- معلومات العمولة
    commission_rate DECIMAL(5, 2) DEFAULT 0.00,
    commission_type ENUM('percentage', 'fixed') DEFAULT 'percentage',
    minimum_commission DECIMAL(10, 2) DEFAULT 0.00,
    
    -- معلومات مالية
    current_balance DECIMAL(15, 2) DEFAULT 0.00,
    total_sales DECIMAL(15, 2) DEFAULT 0.00,
    total_commission DECIMAL(15, 2) DEFAULT 0.00,
    
    -- معلومات البنك
    bank_name VARCHAR(100),
    bank_account VARCHAR(50),
    iban VARCHAR(50),
    
    hire_date DATETIME,
    termination_date DATETIME,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    
    INDEX idx_agent_code (agent_code),
    INDEX idx_agent_type (agent_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (first_name, last_name, company_name, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول الحجوزات
-- =====================================================

-- جدول الحجوزات الرئيسي
CREATE TABLE IF NOT EXISTS bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_number VARCHAR(50) UNIQUE NOT NULL,
    booking_type ENUM('flight', 'hotel', 'car_rental', 'tour', 'package', 'visa', 'insurance', 'transfer') NOT NULL,
    status ENUM('draft', 'pending', 'confirmed', 'cancelled', 'completed', 'refunded', 'partially_refunded') NOT NULL DEFAULT 'draft',
    payment_status ENUM('unpaid', 'partially_paid', 'paid', 'refunded', 'overdue') NOT NULL DEFAULT 'unpaid',
    
    -- العلاقات الأساسية
    customer_id INT NOT NULL,
    supplier_id INT,
    agent_id INT,
    
    -- تواريخ مهمة
    booking_date DATETIME NOT NULL,
    travel_date DATETIME,
    return_date DATETIME,
    confirmation_date DATETIME,
    cancellation_date DATETIME,
    
    -- المعلومات المالية
    base_price DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    taxes DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    fees DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    discount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    paid_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- معلومات العمولة
    commission_rate DECIMAL(5, 2) DEFAULT 0.00,
    commission_amount DECIMAL(12, 2) DEFAULT 0.00,
    agent_commission_rate DECIMAL(5, 2) DEFAULT 0.00,
    agent_commission_amount DECIMAL(12, 2) DEFAULT 0.00,
    
    -- معلومات إضافية
    reference_number VARCHAR(100),
    confirmation_number VARCHAR(100),
    pnr VARCHAR(20),
    
    -- تفاصيل الحجز (JSON)
    booking_details JSON,
    
    -- ملاحظات
    internal_notes TEXT,
    customer_notes TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (agent_id) REFERENCES agents(id),
    
    INDEX idx_booking_number (booking_number),
    INDEX idx_booking_type (booking_type),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_customer_id (customer_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_travel_date (travel_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول ركاب الحجز
CREATE TABLE IF NOT EXISTS booking_passengers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    passenger_type VARCHAR(20) NOT NULL,
    
    -- معلومات شخصية
    title VARCHAR(10),
    first_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    last_name VARCHAR(50) NOT NULL,
    birth_date DATETIME,
    gender VARCHAR(10),
    nationality VARCHAR(50),
    
    -- معلومات جواز السفر
    passport_number VARCHAR(50),
    passport_expiry DATETIME,
    passport_country VARCHAR(50),
    
    -- معلومات إضافية
    special_requests TEXT,
    meal_preference VARCHAR(50),
    seat_preference VARCHAR(50),
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_passenger_type (passenger_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول خدمات الحجز
CREATE TABLE IF NOT EXISTS booking_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    
    -- تفاصيل الخدمة
    description TEXT,
    quantity INT DEFAULT 1,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    
    -- تواريخ الخدمة
    service_date DATETIME,
    service_time VARCHAR(10),
    
    -- تفاصيل إضافية (JSON)
    service_details JSON,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_service_type (service_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مدفوعات الحجز
CREATE TABLE IF NOT EXISTS booking_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
    
    payment_date DATETIME NOT NULL,
    reference_number VARCHAR(100),
    
    -- معلومات إضافية
    payment_details JSON,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_payment_method (payment_method),
    INDEX idx_payment_date (payment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مستندات الحجز
CREATE TABLE IF NOT EXISTS booking_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    document_name VARCHAR(100) NOT NULL,
    
    file_path VARCHAR(255) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_document_type (document_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تاريخ تغييرات الحجز
CREATE TABLE IF NOT EXISTS booking_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    
    old_status VARCHAR(50),
    new_status VARCHAR(50),
    
    description TEXT,
    changed_by INT NOT NULL,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول المحاسبة
-- =====================================================

-- جدول دليل الحسابات
CREATE TABLE IF NOT EXISTS accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_name_en VARCHAR(100),
    
    -- تصنيف الحساب
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    account_category ENUM('current_asset', 'fixed_asset', 'current_liability', 'long_term_liability', 'operating_revenue', 'other_revenue', 'operating_expense', 'administrative_expense') NOT NULL,
    
    -- الهيكل الهرمي
    parent_account_id INT,
    level INT DEFAULT 1,
    is_parent BOOLEAN DEFAULT FALSE,
    
    -- إعدادات الحساب
    is_active BOOLEAN DEFAULT TRUE,
    allow_posting BOOLEAN DEFAULT TRUE,
    
    -- معلومات إضافية
    description TEXT,
    opening_balance DECIMAL(15, 2) DEFAULT 0.00,
    current_balance DECIMAL(15, 2) DEFAULT 0.00,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    notes TEXT,
    
    FOREIGN KEY (parent_account_id) REFERENCES accounts(id),
    INDEX idx_account_code (account_code),
    INDEX idx_account_type (account_type),
    INDEX idx_account_category (account_category),
    INDEX idx_parent_account_id (parent_account_id),
    INDEX idx_is_active (is_active),
    FULLTEXT idx_search (account_name, account_name_en)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول القيود المحاسبية
CREATE TABLE IF NOT EXISTS journal_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entry_number VARCHAR(50) UNIQUE NOT NULL,
    entry_date DATETIME NOT NULL,
    status ENUM('draft', 'posted', 'reversed') DEFAULT 'draft',
    
    -- معلومات المرجع
    reference_type VARCHAR(50),
    reference_id INT,
    reference_number VARCHAR(100),
    
    -- معلومات إضافية
    description TEXT,
    total_debit DECIMAL(15, 2) DEFAULT 0.00,
    total_credit DECIMAL(15, 2) DEFAULT 0.00,
    
    posted_date DATETIME,
    posted_by INT,
    reversed_date DATETIME,
    reversed_by INT,
    reversal_reason TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (posted_by) REFERENCES users(id),
    FOREIGN KEY (reversed_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_entry_number (entry_number),
    INDEX idx_entry_date (entry_date),
    INDEX idx_status (status),
    INDEX idx_reference_type (reference_type),
    INDEX idx_reference_id (reference_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE IF NOT EXISTS journal_entry_lines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    
    description TEXT,
    debit_amount DECIMAL(15, 2) DEFAULT 0.00,
    credit_amount DECIMAL(15, 2) DEFAULT 0.00,
    
    -- معلومات إضافية
    cost_center VARCHAR(50),
    project_code VARCHAR(50),
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_journal_entry_id (journal_entry_id),
    INDEX idx_account_id (account_id),
    INDEX idx_debit_amount (debit_amount),
    INDEX idx_credit_amount (credit_amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول الفواتير والمدفوعات
-- =====================================================

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_type ENUM('sales', 'purchase', 'credit_note', 'debit_note') NOT NULL,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') NOT NULL DEFAULT 'draft',
    
    -- العلاقات
    customer_id INT,
    supplier_id INT,
    booking_id INT,
    
    -- تواريخ
    invoice_date DATETIME NOT NULL,
    due_date DATETIME,
    
    -- المبالغ
    subtotal DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    paid_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- معلومات إضافية
    payment_terms VARCHAR(100),
    notes TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (booking_id) REFERENCES bookings(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_invoice_type (invoice_type),
    INDEX idx_status (status),
    INDEX idx_customer_id (customer_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_due_date (due_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بنود الفواتير
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    
    item_description TEXT NOT NULL,
    quantity DECIMAL(10, 3) DEFAULT 1.000,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(12, 2) NOT NULL,
    
    -- معلومات إضافية
    item_code VARCHAR(50),
    tax_rate DECIMAL(5, 2) DEFAULT 0.00,
    discount_rate DECIMAL(5, 2) DEFAULT 0.00,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    payment_type ENUM('receipt', 'payment', 'refund') NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'debit_card', 'check', 'online') NOT NULL,
    
    -- العلاقات
    customer_id INT,
    supplier_id INT,
    invoice_id INT,
    
    -- معلومات الدفع
    amount DECIMAL(12, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
    
    payment_date DATETIME NOT NULL,
    reference_number VARCHAR(100),
    
    -- معلومات البنك/البطاقة
    bank_name VARCHAR(100),
    account_number VARCHAR(50),
    check_number VARCHAR(50),
    card_last_four VARCHAR(4),
    
    -- معلومات إضافية
    description TEXT,
    notes TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_payment_number (payment_number),
    INDEX idx_payment_type (payment_type),
    INDEX idx_payment_method (payment_method),
    INDEX idx_customer_id (customer_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_payment_date (payment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول إدارة المخزون والتأشيرات
-- =====================================================

-- جدول أنواع التأشيرات
CREATE TABLE IF NOT EXISTS visa_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    visa_code VARCHAR(20) UNIQUE NOT NULL,
    visa_name VARCHAR(100) NOT NULL,
    visa_name_en VARCHAR(100),
    
    -- معلومات التأشيرة
    country VARCHAR(50) NOT NULL,
    visa_category ENUM('tourist', 'business', 'transit', 'work', 'student', 'family', 'medical') NOT NULL,
    duration_days INT,
    validity_days INT,
    
    -- معلومات مالية
    cost_price DECIMAL(10, 2) DEFAULT 0.00,
    selling_price DECIMAL(10, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- إعدادات
    is_active BOOLEAN DEFAULT TRUE,
    requires_interview BOOLEAN DEFAULT FALSE,
    processing_time_days INT DEFAULT 7,
    
    description TEXT,
    requirements TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_visa_code (visa_code),
    INDEX idx_country (country),
    INDEX idx_visa_category (visa_category),
    INDEX idx_is_active (is_active),
    FULLTEXT idx_search (visa_name, visa_name_en, country)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مخزون التأشيرات
CREATE TABLE IF NOT EXISTS visa_inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    visa_type_id INT NOT NULL,
    agent_id INT,
    
    -- معلومات المخزون
    total_quantity INT DEFAULT 0,
    available_quantity INT DEFAULT 0,
    reserved_quantity INT DEFAULT 0,
    sold_quantity INT DEFAULT 0,
    
    -- معلومات مالية
    unit_cost DECIMAL(10, 2) DEFAULT 0.00,
    unit_price DECIMAL(10, 2) DEFAULT 0.00,
    
    -- تواريخ
    purchase_date DATETIME,
    expiry_date DATETIME,
    
    batch_number VARCHAR(50),
    supplier_reference VARCHAR(100),
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (visa_type_id) REFERENCES visa_types(id),
    FOREIGN KEY (agent_id) REFERENCES agents(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_visa_type_id (visa_type_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_batch_number (batch_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول التقارير والإحصائيات
-- =====================================================

-- جدول التقارير المحفوظة
CREATE TABLE IF NOT EXISTS saved_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_name VARCHAR(100) NOT NULL,
    report_type VARCHAR(50) NOT NULL,
    report_category VARCHAR(50) NOT NULL,
    
    -- معايير التقرير
    report_criteria JSON,
    
    -- إعدادات التقرير
    is_public BOOLEAN DEFAULT FALSE,
    is_scheduled BOOLEAN DEFAULT FALSE,
    schedule_frequency VARCHAR(20),
    
    description TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_report_type (report_type),
    INDEX idx_report_category (report_category),
    INDEX idx_created_by (created_by),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- جداول إعدادات النظام
-- =====================================================

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    category VARCHAR(50) NOT NULL,
    
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    
    FOREIGN KEY (updated_by) REFERENCES users(id),
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    
    -- تفاصيل النشاط
    description TEXT,
    old_values JSON,
    new_values JSON,
    
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_entity_type (entity_type),
    INDEX idx_entity_id (entity_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- إدراج البيانات الأولية
-- =====================================================

-- إدراج الأدوار الأساسية
INSERT IGNORE INTO roles (name, display_name, description, color) VALUES
('super_admin', 'مدير عام', 'مدير عام للنظام مع جميع الصلاحيات', '#dc3545'),
('admin', 'مدير', 'مدير النظام', '#007bff'),
('manager', 'مدير قسم', 'مدير قسم أو فرع', '#28a745'),
('accountant', 'محاسب', 'محاسب مالي', '#ffc107'),
('employee', 'موظف', 'موظف عادي', '#6c757d'),
('agent', 'وكيل', 'وكيل مبيعات', '#17a2b8');

-- إدراج الصلاحيات الأساسية
INSERT IGNORE INTO permissions (name, display_name, module, action) VALUES
-- صلاحيات المستخدمين
('users.create', 'إنشاء مستخدم', 'users', 'create'),
('users.read', 'عرض المستخدمين', 'users', 'read'),
('users.update', 'تعديل مستخدم', 'users', 'update'),
('users.delete', 'حذف مستخدم', 'users', 'delete'),

-- صلاحيات العملاء
('customers.create', 'إنشاء عميل', 'customers', 'create'),
('customers.read', 'عرض العملاء', 'customers', 'read'),
('customers.update', 'تعديل عميل', 'customers', 'update'),
('customers.delete', 'حذف عميل', 'customers', 'delete'),

-- صلاحيات الموردين
('suppliers.create', 'إنشاء مورد', 'suppliers', 'create'),
('suppliers.read', 'عرض الموردين', 'suppliers', 'read'),
('suppliers.update', 'تعديل مورد', 'suppliers', 'update'),
('suppliers.delete', 'حذف مورد', 'suppliers', 'delete'),

-- صلاحيات الوكلاء
('agents.create', 'إنشاء وكيل', 'agents', 'create'),
('agents.read', 'عرض الوكلاء', 'agents', 'read'),
('agents.update', 'تعديل وكيل', 'agents', 'update'),
('agents.delete', 'حذف وكيل', 'agents', 'delete'),

-- صلاحيات الحجوزات
('bookings.create', 'إنشاء حجز', 'bookings', 'create'),
('bookings.read', 'عرض الحجوزات', 'bookings', 'read'),
('bookings.update', 'تعديل حجز', 'bookings', 'update'),
('bookings.delete', 'حذف حجز', 'bookings', 'delete'),
('bookings.confirm', 'تأكيد حجز', 'bookings', 'confirm'),
('bookings.cancel', 'إلغاء حجز', 'bookings', 'cancel'),

-- صلاحيات المحاسبة
('accounting.create', 'إنشاء قيد محاسبي', 'accounting', 'create'),
('accounting.read', 'عرض القيود المحاسبية', 'accounting', 'read'),
('accounting.update', 'تعديل قيد محاسبي', 'accounting', 'update'),
('accounting.delete', 'حذف قيد محاسبي', 'accounting', 'delete'),
('accounting.post', 'ترحيل قيد محاسبي', 'accounting', 'post'),

-- صلاحيات الفواتير
('invoices.create', 'إنشاء فاتورة', 'invoices', 'create'),
('invoices.read', 'عرض الفواتير', 'invoices', 'read'),
('invoices.update', 'تعديل فاتورة', 'invoices', 'update'),
('invoices.delete', 'حذف فاتورة', 'invoices', 'delete'),

-- صلاحيات المدفوعات
('payments.create', 'إنشاء دفعة', 'payments', 'create'),
('payments.read', 'عرض المدفوعات', 'payments', 'read'),
('payments.update', 'تعديل دفعة', 'payments', 'update'),
('payments.delete', 'حذف دفعة', 'payments', 'delete'),

-- صلاحيات التقارير
('reports.financial', 'التقارير المالية', 'reports', 'financial'),
('reports.sales', 'تقارير المبيعات', 'reports', 'sales'),
('reports.customers', 'تقارير العملاء', 'reports', 'customers'),
('reports.suppliers', 'تقارير الموردين', 'reports', 'suppliers'),
('reports.agents', 'تقارير الوكلاء', 'reports', 'agents'),

-- صلاحيات الإعدادات
('settings.system', 'إعدادات النظام', 'settings', 'system'),
('settings.users', 'إعدادات المستخدمين', 'settings', 'users'),
('settings.backup', 'النسخ الاحتياطي', 'settings', 'backup'),

-- صلاحيات المخزون
('inventory.create', 'إنشاء مخزون', 'inventory', 'create'),
('inventory.read', 'عرض المخزون', 'inventory', 'read'),
('inventory.update', 'تعديل مخزون', 'inventory', 'update'),
('inventory.delete', 'حذف مخزون', 'inventory', 'delete');

-- إنشاء المستخدم الافتراضي (مدير عام)
INSERT IGNORE INTO users (
    username, 
    email, 
    full_name, 
    hashed_password, 
    is_verified, 
    is_superuser, 
    is_active
) VALUES (
    'admin',
    '<EMAIL>',
    'مدير النظام',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', -- password: admin123
    TRUE,
    TRUE,
    TRUE
);

-- ربط المستخدم الافتراضي بدور المدير العام
INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.username = 'admin' AND r.name = 'super_admin';

-- منح جميع الصلاحيات لدور المدير العام
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'super_admin';

-- إدراج دليل الحسابات الأساسي
INSERT IGNORE INTO accounts (account_code, account_name, account_type, account_category, is_parent, allow_posting) VALUES
-- الأصول
('1000', 'الأصول', 'asset', 'current_asset', TRUE, FALSE),
('1100', 'الأصول المتداولة', 'asset', 'current_asset', TRUE, FALSE),
('1110', 'النقدية', 'asset', 'current_asset', FALSE, TRUE),
('1120', 'البنوك', 'asset', 'current_asset', FALSE, TRUE),
('1130', 'العملاء', 'asset', 'current_asset', FALSE, TRUE),
('1140', 'المخزون', 'asset', 'current_asset', FALSE, TRUE),

('1200', 'الأصول الثابتة', 'asset', 'fixed_asset', TRUE, FALSE),
('1210', 'الأثاث والمعدات', 'asset', 'fixed_asset', FALSE, TRUE),
('1220', 'أجهزة الكمبيوتر', 'asset', 'fixed_asset', FALSE, TRUE),

-- الخصوم
('2000', 'الخصوم', 'liability', 'current_liability', TRUE, FALSE),
('2100', 'الخصوم المتداولة', 'liability', 'current_liability', TRUE, FALSE),
('2110', 'الموردون', 'liability', 'current_liability', FALSE, TRUE),
('2120', 'مصروفات مستحقة', 'liability', 'current_liability', FALSE, TRUE),

-- حقوق الملكية
('3000', 'حقوق الملكية', 'equity', 'current_liability', TRUE, FALSE),
('3100', 'رأس المال', 'equity', 'current_liability', FALSE, TRUE),
('3200', 'الأرباح المحتجزة', 'equity', 'current_liability', FALSE, TRUE),

-- الإيرادات
('4000', 'الإيرادات', 'revenue', 'operating_revenue', TRUE, FALSE),
('4100', 'إيرادات المبيعات', 'revenue', 'operating_revenue', FALSE, TRUE),
('4200', 'إيرادات العمولات', 'revenue', 'operating_revenue', FALSE, TRUE),

-- المصروفات
('5000', 'المصروفات', 'expense', 'operating_expense', TRUE, FALSE),
('5100', 'تكلفة المبيعات', 'expense', 'operating_expense', FALSE, TRUE),
('5200', 'مصروفات التشغيل', 'expense', 'operating_expense', FALSE, TRUE),
('5300', 'مصروفات إدارية', 'expense', 'administrative_expense', FALSE, TRUE);

-- إدراج إعدادات النظام الأساسية
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, category, description) VALUES
('company_name', 'شركة شراء السياحة', 'string', 'general', 'اسم الشركة'),
('company_logo', '', 'string', 'general', 'شعار الشركة'),
('default_currency', 'USD', 'string', 'financial', 'العملة الافتراضية'),
('tax_rate', '15.00', 'number', 'financial', 'معدل الضريبة الافتراضي'),
('backup_frequency', 'daily', 'string', 'system', 'تكرار النسخ الاحتياطي'),
('session_timeout', '30', 'number', 'security', 'انتهاء الجلسة بالدقائق'),
('max_login_attempts', '5', 'number', 'security', 'عدد محاولات تسجيل الدخول'),
('password_min_length', '8', 'number', 'security', 'الحد الأدنى لطول كلمة المرور'),
('email_notifications', 'true', 'boolean', 'notifications', 'تفعيل إشعارات البريد الإلكتروني'),
('sms_notifications', 'false', 'boolean', 'notifications', 'تفعيل إشعارات الرسائل النصية');

-- إنشاء فهارس إضافية للأداء
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_last_login ON users(last_login);
CREATE INDEX idx_roles_created_at ON roles(created_at);
CREATE INDEX idx_permissions_module_action ON permissions(module, action);
CREATE INDEX idx_bookings_total_amount ON bookings(total_amount);
CREATE INDEX idx_invoices_total_amount ON invoices(total_amount);
CREATE INDEX idx_payments_amount ON payments(amount);

-- إنشاء Views للتقارير السريعة
CREATE OR REPLACE VIEW customer_summary AS
SELECT 
    c.id,
    c.customer_code,
    CASE 
        WHEN c.customer_type = 'corporate' THEN c.company_name
        ELSE CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, ''))
    END as customer_name,
    c.customer_type,
    c.status,
    c.current_balance,
    COUNT(b.id) as total_bookings,
    COALESCE(SUM(b.total_amount), 0) as total_sales,
    c.created_at
FROM customers c
LEFT JOIN bookings b ON c.id = b.customer_id
GROUP BY c.id;

CREATE OR REPLACE VIEW booking_summary AS
SELECT 
    b.id,
    b.booking_number,
    b.booking_type,
    b.status,
    b.payment_status,
    CASE 
        WHEN c.customer_type = 'corporate' THEN c.company_name
        ELSE CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, ''))
    END as customer_name,
    s.company_name as supplier_name,
    CONCAT(COALESCE(a.first_name, ''), ' ', COALESCE(a.last_name, '')) as agent_name,
    b.total_amount,
    b.paid_amount,
    (b.total_amount - b.paid_amount) as remaining_amount,
    b.booking_date,
    b.travel_date
FROM bookings b
LEFT JOIN customers c ON b.customer_id = c.id
LEFT JOIN suppliers s ON b.supplier_id = s.id
LEFT JOIN agents a ON b.agent_id = a.id;

CREATE OR REPLACE VIEW financial_summary AS
SELECT 
    DATE(je.entry_date) as entry_date,
    a.account_type,
    a.account_name,
    SUM(jel.debit_amount) as total_debit,
    SUM(jel.credit_amount) as total_credit,
    (SUM(jel.debit_amount) - SUM(jel.credit_amount)) as net_amount
FROM journal_entries je
JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
JOIN accounts a ON jel.account_id = a.id
WHERE je.status = 'posted'
GROUP BY DATE(je.entry_date), a.account_type, a.id;

-- إعدادات الأمان
-- تفعيل SSL (يجب تكوينه في إعدادات MySQL)
-- SET GLOBAL require_secure_transport = ON;

-- إنشاء مستخدم للقراءة فقط للتقارير
CREATE USER IF NOT EXISTS 'travel_readonly'@'localhost' IDENTIFIED BY 'readonly_2024';
GRANT SELECT ON travel_accounting.* TO 'travel_readonly'@'localhost';
FLUSH PRIVILEGES;

COMMIT;

-- =====================================================
-- انتهاء إنشاء قاعدة البيانات
-- =====================================================

SELECT 'تم إنشاء قاعدة البيانات بنجاح!' as status;