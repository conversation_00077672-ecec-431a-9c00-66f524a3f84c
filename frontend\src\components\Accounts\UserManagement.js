import React, { useState, useMemo } from 'react';
import AdvancedPermissionsSystem from './AdvancedPermissionsSystem';
import RoleManagementSystem from './RoleManagementSystem';
import './UserManagement.css';

// مكون إدارة المستخدمين والصلاحيات
export const UserManagement = ({ isLoading }) => {
  const [activeSection, setActiveSection] = useState('users');
  const [showAddUserForm, setShowAddUserForm] = useState(false);
  const [showEditUserForm, setShowEditUserForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);
  const [selectedUserForPermissions, setSelectedUserForPermissions] = useState(null);

  // بيانات الأدوار والصلاحيات المحدثة
  const rolesData = {
    admin: {
      name: 'مدير النظام',
      description: 'صلاحيات كاملة لجميع أجزاء النظام',
      permissions: ['*'],
      color: '#dc3545',
      icon: '👑'
    },
    financial_manager: {
      name: 'مدير مالي',
      description: 'إدارة كاملة للشؤون المالية والمحاسبية',
      permissions: [
        'dashboard.view', 'dashboard.view_analytics',
        'finance.*',
        'accounts.*',
        'reports.financial_reports', 'reports.export', 'reports.analytics',
        'customers.view', 'customers.view_financial_info',
        'suppliers.view', 'suppliers.approve_payments',
        'agents.view', 'agents.manage_commissions'
      ],
      color: '#28a745',
      icon: '💼'
    },
    accountant: {
      name: 'محاسب',
      description: 'صلاحيات محاسبية كاملة مع إمكانية الترحيل',
      permissions: [
        'dashboard.view',
        'finance.view', 'finance.create_entries', 'finance.edit_entries', 'finance.post_entries',
        'finance.view_trial_balance', 'finance.view_income_statement', 'finance.view_balance_sheet',
        'finance.bank_reconciliation', 'finance.export_reports',
        'accounts.view', 'accounts.create', 'accounts.edit',
        'reports.financial_reports', 'reports.export',
        'customers.view', 'suppliers.view'
      ],
      color: '#17a2b8',
      icon: '📊'
    },
    bookkeeper: {
      name: 'مسك دفاتر',
      description: 'إدخال القيود وعرض التقارير الأساسية',
      permissions: [
        'dashboard.view',
        'finance.view', 'finance.create_entries', 'finance.edit_entries',
        'finance.view_trial_balance',
        'accounts.view',
        'reports.view', 'reports.financial_reports',
        'customers.view', 'suppliers.view'
      ],
      color: '#ffc107',
      icon: '📝'
    },
    sales_manager: {
      name: 'مدير مبيعات',
      description: 'إدارة العملاء والحجوزات والمبيعات',
      permissions: [
        'dashboard.view', 'dashboard.view_analytics',
        'bookings.*',
        'flight_bookings.*',
        'umrah_bookings.*',
        'hajj_bookings.*',
        'customers.*',
        'sales.*',
        'agents.view', 'agents.manage_commissions',
        'reports.sales_reports', 'reports.operational_reports'
      ],
      color: '#fd7e14',
      icon: '🎯'
    },
    operations_manager: {
      name: 'مدير عمليات',
      description: 'إدارة العمليات التشغيلية والموردين',
      permissions: [
        'dashboard.view', 'dashboard.view_analytics',
        'bookings.view', 'bookings.confirm', 'bookings.cancel',
        'suppliers.*',
        'inventory.*',
        'visa_inventory.*',
        'customers.view', 'customers.view_history',
        'reports.operational_reports',
        'purchases.view', 'purchases.approve'
      ],
      color: '#6f42c1',
      icon: '⚡'
    },
    customer_service: {
      name: 'خدمة العملاء',
      description: 'التعامل مع العملاء وإدارة الحجوزات الأساسية',
      permissions: [
        'dashboard.view',
        'customers.view', 'customers.create', 'customers.edit',
        'bookings.view', 'bookings.create', 'bookings.edit',
        'flight_bookings.view', 'flight_bookings.create', 'flight_bookings.edit',
        'umrah_bookings.view', 'umrah_bookings.create', 'umrah_bookings.edit',
        'passport_services.*',
        'document_authentication.*'
      ],
      color: '#20c997',
      icon: '🎧'
    },
    auditor: {
      name: 'مراجع',
      description: 'مراجعة العمليات والتقارير دون إمكانية التعديل',
      permissions: [
        'dashboard.view',
        'finance.view', 'finance.view_trial_balance', 'finance.view_income_statement', 'finance.view_balance_sheet',
        'accounts.view', 'accounts.view_transactions',
        'bookings.view', 'bookings.view_history',
        'customers.view', 'customers.view_history',
        'suppliers.view', 'suppliers.view_performance',
        'reports.*',
        'system.view_logs', 'system.security_audit'
      ],
      color: '#e83e8c',
      icon: '🔍'
    },
    viewer: {
      name: 'مستعلم',
      description: 'عرض البيانات فقط دون إمكانية التعديل',
      permissions: [
        'dashboard.view',
        'customers.view',
        'bookings.view',
        'suppliers.view',
        'reports.view'
      ],
      color: '#6c757d',
      icon: '👁️'
    }
  };

  // بيانات تجريبية للمستخدمين المحدثة
  const [usersData, setUsersData] = useState([
    {
      id: 'USR-001',
      name: 'أحمد محمد العلي',
      email: '<EMAIL>',
      role: 'admin',
      department: 'الإدارة العامة',
      status: 'active',
      lastLogin: '2024-02-10 14:30',
      createdAt: '2024-01-01',
      phone: '+966501234567',
      position: 'المدير العام',
      permissions: ['*'], // جميع الصلاحيات
      avatar: '👨‍💼'
    },
    {
      id: 'USR-002',
      name: 'فاطمة سالم أحمد',
      email: '<EMAIL>',
      role: 'financial_manager',
      department: 'المحاسبة',
      status: 'active',
      lastLogin: '2024-02-10 09:15',
      createdAt: '2024-01-15',
      phone: '+************',
      position: 'مدير مالي',
      permissions: rolesData.financial_manager?.permissions || [],
      avatar: '👩‍💼'
    },
    {
      id: 'USR-003',
      name: 'محمد عبدالله الزهراني',
      email: '<EMAIL>',
      role: 'accountant',
      department: 'المحاسبة',
      status: 'active',
      lastLogin: '2024-02-09 16:45',
      createdAt: '2024-01-20',
      phone: '+************',
      position: 'محاسب أول',
      permissions: rolesData.accountant?.permissions || [],
      avatar: '👨‍💻'
    },
    {
      id: 'USR-004',
      name: 'سارة محمد الأحمد',
      email: '<EMAIL>',
      role: 'sales_manager',
      department: 'المبيعات',
      status: 'active',
      lastLogin: '2024-02-10 11:20',
      createdAt: '2024-01-25',
      phone: '+************',
      position: 'مدير مبيعات',
      permissions: rolesData.sales_manager?.permissions || [],
      avatar: '👩‍💼'
    },
    {
      id: 'USR-005',
      name: 'خالد عبدالله المطيري',
      email: '<EMAIL>',
      role: 'operations_manager',
      department: 'العمليات',
      status: 'active',
      lastLogin: '2024-02-09 15:30',
      createdAt: '2024-01-30',
      phone: '+************',
      position: 'مدير عمليات',
      permissions: rolesData.operations_manager?.permissions || [],
      avatar: '👨‍🔧'
    },
    {
      id: 'USR-006',
      name: 'نورا عبدالرحمن القحطاني',
      email: '<EMAIL>',
      role: 'customer_service',
      department: 'خدمة العملاء',
      status: 'active',
      lastLogin: '2024-02-10 08:45',
      createdAt: '2024-02-01',
      phone: '+966506789012',
      position: 'موظف خدمة عملاء',
      permissions: rolesData.customer_service?.permissions || [],
      avatar: '👩‍💻'
    },
    {
      id: 'USR-007',
      name: 'عبدالعزيز أحمد الغامدي',
      email: '<EMAIL>',
      role: 'bookkeeper',
      department: 'المحاسبة',
      status: 'active',
      lastLogin: '2024-02-09 13:15',
      createdAt: '2024-02-05',
      phone: '+966507890123',
      position: 'مسك دفاتر',
      permissions: rolesData.bookkeeper?.permissions || [],
      avatar: '👨‍📊'
    },
    {
      id: 'USR-008',
      name: 'ريم محمد الشهري',
      email: '<EMAIL>',
      role: 'auditor',
      department: 'المراجعة الداخلية',
      status: 'active',
      lastLogin: '2024-02-08 10:30',
      createdAt: '2024-02-10',
      phone: '+966508901234',
      position: 'مراجع داخلي',
      permissions: rolesData.auditor?.permissions || [],
      avatar: '👩‍🔍'
    },
    {
      id: 'USR-009',
      name: 'يوسف علي الحربي',
      email: '<EMAIL>',
      role: 'viewer',
      department: 'الاستعلامات',
      status: 'inactive',
      lastLogin: '2024-02-05 16:20',
      createdAt: '2024-02-12',
      phone: '+966509012345',
      position: 'موظف استعلامات',
      permissions: rolesData.viewer?.permissions || [],
      avatar: '👨‍👀'
    }
  ]);

  // نظام الأذونات الشامل للنظام
  const systemPermissions = {
    // أذونات لوحة التحكم
    dashboard: {
      name: 'لوحة التحكم',
      icon: '📊',
      permissions: {
        'dashboard.view': 'عرض لوحة التحكم',
        'dashboard.stats': 'عرض الإحصائيات',
        'dashboard.charts': 'عرض الرسوم البيانية',
        'dashboard.export': 'تصدير بيانات لوحة التحكم'
      }
    },

    // أذونات الحجوزات
    bookings: {
      name: 'إدارة الحجوزات',
      icon: '✈️',
      permissions: {
        'bookings.view': 'عرض الحجوزات',
        'bookings.create': 'إنشاء حجوزات جديدة',
        'bookings.edit': 'تعديل الحجوزات',
        'bookings.delete': 'حذف الحجوزات',
        'bookings.confirm': 'تأكيد الحجوزات',
        'bookings.cancel': 'إلغاء الحجوزات',
        'bookings.refund': 'استرداد الحجوزات',
        'bookings.export': 'تصدير بيانات الحجوزات',
        'bookings.reports': 'تقارير الحجوزات'
      }
    },

    // أذونات العملاء
    customers: {
      name: 'إدارة العملاء',
      icon: '👥',
      permissions: {
        'customers.view': 'عرض العملاء',
        'customers.create': 'إضافة عملاء جدد',
        'customers.edit': 'تعديل بيانات العملاء',
        'customers.delete': 'حذف العملاء',
        'customers.export': 'تصدير قائمة العملاء',
        'customers.reports': 'تقارير العملاء',
        'customers.history': 'عرض تاريخ العملاء',
        'customers.notes': 'إدارة ملاحظات العملاء'
      }
    },

    // أذونات الوكلاء
    agents: {
      name: 'إدارة الوكلاء',
      icon: '🏢',
      permissions: {
        'agents.view': 'عرض الوكلاء',
        'agents.create': 'إضافة وكلاء جدد',
        'agents.edit': 'تعديل بيانات الوكلاء',
        'agents.delete': 'حذف الوكلاء',
        'agents.commissions': 'إدارة عمولات الوكلاء',
        'agents.contracts': 'إدارة عقود الوكلاء',
        'agents.reports': 'تقارير الوكلاء',
        'agents.export': 'تصدير بيانات الوكلاء'
      }
    },

    // أذونات الموردين
    suppliers: {
      name: 'إدارة الموردين',
      icon: '🏭',
      permissions: {
        'suppliers.view': 'عرض الموردين',
        'suppliers.create': 'إضافة موردين جدد',
        'suppliers.edit': 'تعديل بيانات الموردين',
        'suppliers.delete': 'حذف الموردين',
        'suppliers.contracts': 'إدارة عقود الموردين',
        'suppliers.payments': 'إدارة مدفوعات الموردين',
        'suppliers.reports': 'تقارير الموردين',
        'suppliers.export': 'تصدير بيانات الموردين'
      }
    },

    // أذونات المحاسبة
    accounting: {
      name: 'النظام المحاسبي',
      icon: '💰',
      permissions: {
        'accounting.view': 'عرض البيانات المحاسبية',
        'accounting.chart_accounts': 'إدارة دليل الحسابات',
        'accounting.journal_entries': 'إدخال القيود اليومية',
        'accounting.post_entries': 'ترحيل القيود',
        'accounting.trial_balance': 'ميزان المراجعة',
        'accounting.financial_statements': 'القوائم المالية',
        'accounting.general_ledger': 'دفتر الأستاذ العام',
        'accounting.receivables': 'إدارة الذمم المدينة',
        'accounting.payables': 'إدارة الذمم الدائنة',
        'accounting.bank_reconciliation': 'تسوية البنوك',
        'accounting.tax_reports': 'التقارير الضريبية',
        'accounting.audit_trail': 'مسار المراجعة',
        'accounting.periods': 'إدارة الفترات المحاسبية',
        'accounting.automated_transactions': 'المعاملات التلقائية',
        'accounting.export': 'تصدير البيانات المحاسبية'
      }
    },

    // أذونات التقارير
    reports: {
      name: 'التقارير والتحليلات',
      icon: '📈',
      permissions: {
        'reports.view': 'عرض التقارير',
        'reports.financial': 'التقارير المالية',
        'reports.operational': 'التقارير التشغيلية',
        'reports.sales': 'تقارير المبيعات',
        'reports.customers': 'تقارير العملاء',
        'reports.agents': 'تقارير الوكلاء',
        'reports.suppliers': 'تقارير الموردين',
        'reports.custom': 'التقارير المخصصة',
        'reports.export': 'تصدير التقارير',
        'reports.schedule': 'جدولة التقارير',
        'reports.analytics': 'التحليلات المتقدمة'
      }
    },

    // أذونات إدارة النظام
    system: {
      name: 'إدارة النظام',
      icon: '⚙️',
      permissions: {
        'system.settings': 'إعدادات النظام',
        'system.users': 'إدارة المستخدمين',
        'system.roles': 'إدارة الأدوار والصلاحيات',
        'system.backup': 'النسخ الاحتياطي',
        'system.restore': 'استعادة البيانات',
        'system.logs': 'عرض سجلات النظام',
        'system.maintenance': 'صيانة النظام',
        'system.security': 'إعدادات الأمان',
        'system.integrations': 'التكاملات الخارجية',
        'system.notifications': 'إدارة الإشعارات'
      }
    },

    // أذونات الأمان والمراجعة
    security: {
      name: 'الأمان والمراجعة',
      icon: '🔒',
      permissions: {
        'security.audit': 'مراجعة العمليات',
        'security.logs': 'سجلات الأمان',
        'security.access_control': 'التحكم في الوصول',
        'security.password_policy': 'سياسة كلمات المرور',
        'security.session_management': 'إدارة الجلسات',
        'security.two_factor': 'المصادقة الثنائية',
        'security.encryption': 'إعدادات التشفير',
        'security.compliance': 'الامتثال والمطابقة'
      }
    }
  };



  // بيانات الصلاحيات
  const permissionsData = {
    'view_accounts': 'عرض الحسابات',
    'create_accounts': 'إنشاء حسابات جديدة',
    'edit_accounts': 'تعديل الحسابات',
    'delete_accounts': 'حذف الحسابات',
    'create_entries': 'إنشاء قيود يومية',
    'edit_entries': 'تعديل القيود',
    'edit_draft_entries': 'تعديل القيود المسودة فقط',
    'post_entries': 'ترحيل القيود',
    'delete_entries': 'حذف القيود',
    'view_reports': 'عرض التقارير',
    'view_basic_reports': 'عرض التقارير الأساسية',
    'export_reports': 'تصدير التقارير',
    'manage_reconciliation': 'إدارة تسوية البنوك',
    'manage_users': 'إدارة المستخدمين',
    'system_settings': 'إعدادات النظام'
  };

  const filteredUsers = useMemo(() => {
    return usersData.filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.email.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesRole = filterRole === 'all' || user.role === filterRole;
      return matchesSearch && matchesRole;
    });
  }, [searchTerm, filterRole]); // eslint-disable-line react-hooks/exhaustive-deps

  const getRoleLabel = (role) => {
    return rolesData[role]?.name || role;
  };

  const getRoleColor = (role) => {
    return rolesData[role]?.color || '#6c757d';
  };

  // دالة لحساب عدد صلاحيات المستخدم
  const getUserPermissionsCount = (user) => {
    if (!user.permissions) return 0;
    if (user.permissions.includes('*')) {
      return Object.values(systemPermissions).reduce((total, category) =>
        total + Object.keys(category.permissions).length, 0
      );
    }
    return user.permissions.length;
  };

  // دالة لفتح نافذة إدارة صلاحيات المستخدم
  const openUserPermissions = (user) => {
    setSelectedUserForPermissions(user);
    setShowPermissionsModal(true);
  };

  // دالة لتحديث صلاحيات المستخدم
  const updateUserPermissions = (permissions) => {
    if (selectedUserForPermissions) {
      const updatedUsers = usersData.map(user =>
        user.id === selectedUserForPermissions.id
          ? { ...user, permissions }
          : user
      );
      setUsersData(updatedUsers);
      setSelectedUserForPermissions({ ...selectedUserForPermissions, permissions });
    }
  };

  // مكون عرض الأذونات التفصيلية
  const PermissionsSection = () => {
    const [selectedUserForPermissions, setSelectedUserForPermissions] = useState(usersData[0]);
    const permissionCategories = getPermissionsByCategory();

    return (
      <div className="permissions-section">
        <div className="section-header">
          <h3>🔑 الأذونات التفصيلية للنظام</h3>
          <div className="user-selector">
            <label>اختر المستخدم:</label>
            <select
              value={selectedUserForPermissions?.id || ''}
              onChange={(e) => {
                const user = usersData.find(u => u.id === e.target.value);
                setSelectedUserForPermissions(user);
              }}
            >
              {usersData.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name} - {rolesData[user.role]?.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {selectedUserForPermissions && (
          <div className="user-permissions-overview">
            <div className="user-card">
              <div className="user-avatar-large">{selectedUserForPermissions.avatar}</div>
              <div className="user-info-detailed">
                <h4>{selectedUserForPermissions.name}</h4>
                <p>{selectedUserForPermissions.position}</p>
                <div className="role-info">
                  <span
                    className="role-badge-large"
                    style={{ backgroundColor: rolesData[selectedUserForPermissions.role]?.color }}
                  >
                    {rolesData[selectedUserForPermissions.role]?.icon} {rolesData[selectedUserForPermissions.role]?.name}
                  </span>
                </div>
                <div className="permissions-summary">
                  <strong>{getUserPermissionsCount(selectedUserForPermissions)}</strong> صلاحية من أصل{' '}
                  <strong>
                    {Object.values(systemPermissions).reduce((total, category) =>
                      total + Object.keys(category.permissions).length, 0
                    )}
                  </strong>
                </div>
              </div>
            </div>

            <div className="permissions-grid">
              {Object.entries(permissionCategories).map(([categoryKey, category]) => (
                <div key={categoryKey} className="permission-category">
                  <div className="category-header">
                    <span className="category-icon">{category.icon}</span>
                    <h5>{category.name}</h5>
                    <div className="category-stats">
                      {category.permissionsList.filter(perm =>
                        hasPermission(selectedUserForPermissions.permissions, perm.key)
                      ).length} / {category.permissionsList.length}
                    </div>
                  </div>

                  <div className="permissions-list">
                    {category.permissionsList.map(permission => {
                      const hasAccess = hasPermission(selectedUserForPermissions.permissions, permission.key);
                      return (
                        <div
                          key={permission.key}
                          className={`permission-item ${hasAccess ? 'granted' : 'denied'}`}
                        >
                          <span className="permission-status">
                            {hasAccess ? '✅' : '❌'}
                          </span>
                          <span className="permission-name">{permission.name}</span>
                          <span className="permission-key">{permission.key}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const getStatusLabel = (status) => {
    return status === 'active' ? 'نشط' : 'غير نشط';
  };

  const getStatusColor = (status) => {
    return status === 'active' ? '#28a745' : '#dc3545';
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditUserForm(true);
  };

  const handleDeleteUser = (userId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      console.log('حذف المستخدم:', userId);
    }
  };

  const handleToggleUserStatus = (userId) => {
    console.log('تغيير حالة المستخدم:', userId);
  };

  // مكون قائمة المستخدمين
  const UsersSection = () => (
    <div className="users-section">
      <div className="section-header">
        <h4>إدارة المستخدمين</h4>
        <button 
          className="add-user-btn"
          onClick={() => setShowAddUserForm(true)}
        >
          ➕ إضافة مستخدم
        </button>
      </div>

      {/* فلاتر البحث */}
      <div className="users-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="البحث في المستخدمين..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="role-filter">
          <select
            value={filterRole}
            onChange={(e) => setFilterRole(e.target.value)}
          >
            <option value="all">جميع الأدوار</option>
            <option value="admin">مدير النظام</option>
            <option value="accountant">محاسب</option>
            <option value="bookkeeper">مسك دفاتر</option>
            <option value="viewer">مستعلم</option>
          </select>
        </div>
      </div>

      {/* جدول المستخدمين */}
      <div className="users-table">
        <div className="table-header">
          <div>المستخدم</div>
          <div>الدور</div>
          <div>القسم</div>
          <div>الحالة</div>
          <div>آخر دخول</div>
          <div>إجراءات</div>
        </div>

        {filteredUsers.map(user => (
          <div key={user.id} className={`table-row ${user.status}`}>
            <div className="user-info">
              <div className="user-avatar">{user.avatar}</div>
              <div className="user-details">
                <div className="user-name">{user.name}</div>
                <div className="user-email">{user.email}</div>
                <div className="user-meta">
                  <span className="user-id">ID: {user.id}</span>
                  <span className="user-position">{user.position}</span>
                </div>
              </div>
            </div>

            <div className="user-role">
              <span
                className="role-badge"
                style={{ backgroundColor: rolesData[user.role]?.color }}
              >
                <span className="role-icon">{rolesData[user.role]?.icon}</span>
                {rolesData[user.role]?.name || user.role}
              </span>
              <div className="permissions-count">
                {getUserPermissionsCount(user)} صلاحية
              </div>
            </div>
            
            <div className="user-department">{user.department}</div>
            
            <div className="user-status">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(user.status) }}
              >
                {getStatusLabel(user.status)}
              </span>
            </div>
            
            <div className="last-login">{user.lastLogin}</div>
            
            <div className="user-actions">
              <button
                className="action-btn view"
                title="عرض التفاصيل"
              >
                👁️
              </button>
              <button
                className="action-btn edit"
                title="تعديل"
                onClick={() => handleEditUser(user)}
              >
                ✏️
              </button>
              <button
                className="action-btn permissions"
                title="إدارة الصلاحيات"
                onClick={() => openUserPermissions(user)}
              >
                🔧
              </button>
              <button
                className="action-btn toggle"
                title={user.status === 'active' ? 'إلغاء التفعيل' : 'تفعيل'}
                onClick={() => handleToggleUserStatus(user.id)}
              >
                {user.status === 'active' ? '🔒' : '🔓'}
              </button>
              <button
                className="action-btn delete"
                title="حذف"
                onClick={() => handleDeleteUser(user.id)}
              >
                🗑️
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // مكون إدارة الأدوار
  const RolesSection = () => (
    <div className="roles-section">
      <div className="section-header">
        <h4>إدارة الأدوار والصلاحيات</h4>
        <button className="add-role-btn">
          ➕ إضافة دور
        </button>
      </div>

      <div className="roles-grid">
        {Object.entries(rolesData).map(([roleId, role]) => (
          <div key={roleId} className="role-card">
            <div className="role-header">
              <div 
                className="role-color"
                style={{ backgroundColor: role.color }}
              ></div>
              <h5>{role.name}</h5>
            </div>
            
            <div className="role-description">
              {role.description}
            </div>
            
            <div className="role-permissions">
              <h6>الصلاحيات:</h6>
              <div className="permissions-list">
                {role.permissions.includes('*') ? (
                  <span className="permission-item all">جميع الصلاحيات</span>
                ) : (
                  role.permissions.map(permission => (
                    <span key={permission} className="permission-item">
                      {permissionsData[permission]}
                    </span>
                  ))
                )}
              </div>
            </div>
            
            <div className="role-actions">
              <button className="edit-role-btn">✏️ تعديل</button>
              <button className="delete-role-btn">🗑️ حذف</button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // مكون سجل النشاطات
  const ActivityLogSection = () => {
    const activityData = [
      {
        id: 'ACT-001',
        user: 'أحمد محمد العلي',
        action: 'تسجيل دخول',
        details: 'تسجيل دخول ناجح من IP: *************',
        timestamp: '2024-02-10 14:30:25',
        type: 'login'
      },
      {
        id: 'ACT-002',
        user: 'فاطمة سالم أحمد',
        action: 'إنشاء قيد محاسبي',
        details: 'إنشاء قيد رقم JE-2024-001',
        timestamp: '2024-02-10 09:15:42',
        type: 'create'
      },
      {
        id: 'ACT-003',
        user: 'محمد عبدالله الزهراني',
        action: 'تعديل حساب',
        details: 'تعديل حساب رقم 1112 - البنك الأهلي',
        timestamp: '2024-02-09 16:45:18',
        type: 'edit'
      },
      {
        id: 'ACT-004',
        user: 'أحمد محمد العلي',
        action: 'تصدير تقرير',
        details: 'تصدير ميزان المراجعة للفترة 2024-01',
        timestamp: '2024-02-09 11:20:33',
        type: 'export'
      }
    ];

  // دوال مساعدة لإدارة الأذونات
  const hasPermission = (userPermissions, permission) => {
    if (userPermissions.includes('*')) return true;
    if (userPermissions.includes(permission)) return true;

    // التحقق من الأذونات الشاملة (مثل accounting.*)
    const wildcardPermission = permission.split('.')[0] + '.*';
    return userPermissions.includes(wildcardPermission);
  };

  const getPermissionsByCategory = () => {
    const categories = {};
    Object.keys(systemPermissions).forEach(category => {
      categories[category] = {
        ...systemPermissions[category],
        permissionsList: Object.entries(systemPermissions[category].permissions).map(([key, value]) => ({
          key,
          name: value
        }))
      };
    });
    return categories;
  };



  const getRolePermissionsCount = (role) => {
    const roleData = rolesData[role];
    if (!roleData) return 0;
    if (roleData.permissions.includes('*')) {
      return Object.values(systemPermissions).reduce((total, category) =>
        total + Object.keys(category.permissions).length, 0
      );
    }
    return roleData.permissions.length;
  };

  const getPermissionCategoryName = (permission) => {
    const category = permission.split('.')[0];
    return systemPermissions[category]?.name || 'غير محدد';
  };

  const formatPermissionName = (permission) => {
    const [category, action] = permission.split('.');
    const categoryData = systemPermissions[category];
    if (categoryData && categoryData.permissions[permission]) {
      return categoryData.permissions[permission];
    }
    return permission;
  };





    const getActivityIcon = (type) => {
      const icons = {
        login: '🔐',
        create: '➕',
        edit: '✏️',
        delete: '🗑️',
        export: '📊',
        import: '📥'
      };
      return icons[type] || '📝';
    };

    const getActivityColor = (type) => {
      const colors = {
        login: '#17a2b8',
        create: '#28a745',
        edit: '#ffc107',
        delete: '#dc3545',
        export: '#6f42c1',
        import: '#fd7e14'
      };
      return colors[type] || '#6c757d';
    };

    return (
      <div className="activity-log-section">
        <div className="section-header">
          <h4>سجل النشاطات</h4>
          <div className="log-filters">
            <select>
              <option value="all">جميع النشاطات</option>
              <option value="login">تسجيل الدخول</option>
              <option value="create">إنشاء</option>
              <option value="edit">تعديل</option>
              <option value="delete">حذف</option>
              <option value="export">تصدير</option>
            </select>
            <input type="date" />
          </div>
        </div>

        <div className="activity-timeline">
          {activityData.map(activity => (
            <div key={activity.id} className="activity-item">
              <div 
                className="activity-icon"
                style={{ backgroundColor: getActivityColor(activity.type) }}
              >
                {getActivityIcon(activity.type)}
              </div>
              
              <div className="activity-content">
                <div className="activity-header">
                  <span className="activity-user">{activity.user}</span>
                  <span className="activity-action">{activity.action}</span>
                  <span className="activity-time">{activity.timestamp}</span>
                </div>
                <div className="activity-details">{activity.details}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل إدارة المستخدمين...</p>
      </div>
    );
  }

  return (
    <div className="user-management">
      <div className="management-header">
        <h3>إدارة المستخدمين والصلاحيات</h3>
      </div>

      {/* تبويبات الإدارة */}
      <div className="management-tabs">
        <button 
          className={`tab-btn ${activeSection === 'users' ? 'active' : ''}`}
          onClick={() => setActiveSection('users')}
        >
          👥 المستخدمين
        </button>
        <button 
          className={`tab-btn ${activeSection === 'roles' ? 'active' : ''}`}
          onClick={() => setActiveSection('roles')}
        >
          🔐 الأدوار والصلاحيات
        </button>
        <button
          className={`tab-btn ${activeSection === 'permissions' ? 'active' : ''}`}
          onClick={() => setActiveSection('permissions')}
        >
          🔑 الأذونات التفصيلية
        </button>
        <button
          className={`tab-btn ${activeSection === 'activity' ? 'active' : ''}`}
          onClick={() => setActiveSection('activity')}
        >
          📋 سجل النشاطات
        </button>
        <button
          className={`tab-btn ${activeSection === 'advanced-permissions' ? 'active' : ''}`}
          onClick={() => setActiveSection('advanced-permissions')}
        >
          🔧 نظام الصلاحيات المتقدم
        </button>
        <button
          className={`tab-btn ${activeSection === 'role-management' ? 'active' : ''}`}
          onClick={() => setActiveSection('role-management')}
        >
          👑 إدارة الأدوار المتقدمة
        </button>
      </div>

      {/* محتوى الإدارة */}
      <div className="management-content">
        {activeSection === 'users' && <UsersSection />}
        {activeSection === 'roles' && <RolesSection />}
        {activeSection === 'permissions' && <PermissionsSection />}
        {activeSection === 'activity' && <ActivityLogSection />}
        {activeSection === 'advanced-permissions' && (
          <div className="advanced-permissions-container">
            <h3>🔧 نظام الصلاحيات المتقدم</h3>
            <p>اختر مستخدماً لإدارة صلاحياته بشكل تفصيلي</p>
            <div className="users-grid">
              {usersData.map(user => (
                <div key={user.id} className="user-card-advanced">
                  <div className="user-info">
                    <span className="user-avatar">{user.avatar}</span>
                    <div>
                      <h4>{user.name}</h4>
                      <p>{user.email}</p>
                      <span className="user-role" style={{ color: getRoleColor(user.role) }}>
                        {getRoleLabel(user.role)}
                      </span>
                    </div>
                  </div>
                  <button
                    className="manage-permissions-btn"
                    onClick={() => openUserPermissions(user)}
                  >
                    🔧 إدارة الصلاحيات
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
        {activeSection === 'role-management' && <RoleManagementSystem />}
      </div>

      {/* نموذج إضافة مستخدم */}
      {showAddUserForm && (
        <div className="user-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h4>إضافة مستخدم جديد</h4>
              <button 
                className="close-btn"
                onClick={() => setShowAddUserForm(false)}
              >
                ❌
              </button>
            </div>
            
            <div className="modal-body">
              <div className="user-form">
                <div className="form-group">
                  <label>الاسم الكامل</label>
                  <input type="text" placeholder="أدخل الاسم الكامل" />
                </div>

                <div className="form-group">
                  <label>البريد الإلكتروني</label>
                  <input type="email" placeholder="أدخل البريد الإلكتروني" />
                </div>

                <div className="form-group">
                  <label>كلمة المرور</label>
                  <input type="password" placeholder="أدخل كلمة المرور" />
                </div>

                <div className="form-group">
                  <label>الدور</label>
                  <select>
                    <option value="">اختر الدور</option>
                    <option value="admin">مدير النظام</option>
                    <option value="accountant">محاسب</option>
                    <option value="bookkeeper">مسك دفاتر</option>
                    <option value="viewer">مستعلم</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>القسم</label>
                  <input type="text" placeholder="أدخل القسم" />
                </div>

                <div className="form-group">
                  <label>الحالة</label>
                  <select>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="save-btn">💾 حفظ المستخدم</button>
              <button 
                className="cancel-btn"
                onClick={() => setShowAddUserForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إدارة الصلاحيات المتقدمة */}
      {showPermissionsModal && selectedUserForPermissions && (
        <div className="modal-overlay">
          <div className="modal advanced-permissions-modal">
            <div className="modal-header">
              <h3>
                🔧 إدارة صلاحيات المستخدم: {selectedUserForPermissions.name}
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowPermissionsModal(false)}
              >
                ✕
              </button>
            </div>
            <div className="modal-body">
              <AdvancedPermissionsSystem
                user={selectedUserForPermissions}
                onPermissionsChange={updateUserPermissions}
              />
            </div>
            <div className="modal-footer">
              <button
                className="save-btn"
                onClick={() => setShowPermissionsModal(false)}
              >
                💾 حفظ التغييرات
              </button>
              <button
                className="cancel-btn"
                onClick={() => setShowPermissionsModal(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;