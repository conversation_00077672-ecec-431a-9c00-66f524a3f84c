import React, { useState, useMemo } from 'react';
import ModernButton, { IconButton } from './ModernButton';
import { ModernInput } from './ModernForm';
import './ModernTable.css';

// Modern Table Component
export const ModernTable = ({
  data = [],
  columns = [],
  loading = false,
  sortable = true,
  searchable = true,
  pagination = true,
  pageSize = 10,
  selectable = false,
  striped = true,
  hover = true,
  compact = false,
  variant = 'default',
  className = '',
  onRowClick,
  onSelectionChange,
  ...props
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRows, setSelectedRows] = useState(new Set());

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = data;

    // Apply search filter
    if (searchTerm) {
      filtered = data.filter(row =>
        columns.some(column => {
          const value = row[column.key];
          return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
        })
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [data, searchTerm, sortConfig, columns]);

  // Pagination
  const totalPages = Math.ceil(processedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedData = pagination 
    ? processedData.slice(startIndex, startIndex + pageSize)
    : processedData;

  const handleSort = (key) => {
    if (!sortable) return;

    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedRows(new Set(paginatedData.map((_, index) => startIndex + index)));
    } else {
      setSelectedRows(new Set());
    }
    onSelectionChange?.(checked ? paginatedData : []);
  };

  const handleSelectRow = (index, checked) => {
    const actualIndex = startIndex + index;
    const newSelected = new Set(selectedRows);
    
    if (checked) {
      newSelected.add(actualIndex);
    } else {
      newSelected.delete(actualIndex);
    }
    
    setSelectedRows(newSelected);
    onSelectionChange?.(Array.from(newSelected).map(i => data[i]));
  };

  const tableClasses = [
    'modern-table-container',
    `modern-table-container--${variant}`,
    compact && 'modern-table-container--compact',
    className
  ].filter(Boolean).join(' ');

  const tableBodyClasses = [
    'modern-table',
    striped && 'modern-table--striped',
    hover && 'modern-table--hover',
    selectable && 'modern-table--selectable'
  ].filter(Boolean).join(' ');

  if (loading) {
    return (
      <div className={tableClasses}>
        <div className="modern-table-loading">
          <div className="modern-table-skeleton">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="modern-table-skeleton-row">
                {columns.map((_, j) => (
                  <div key={j} className="modern-table-skeleton-cell" />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={tableClasses} {...props}>
      {/* Table Header */}
      {(searchable || selectable) && (
        <div className="modern-table-header">
          {searchable && (
            <div className="modern-table-search">
              <ModernInput
                type="text"
                placeholder="البحث في الجدول..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                icon="🔍"
                size="sm"
              />
            </div>
          )}
          
          {selectable && selectedRows.size > 0 && (
            <div className="modern-table-actions">
              <span className="modern-table-selected-count">
                تم تحديد {selectedRows.size} عنصر
              </span>
              <ModernButton
                variant="error"
                size="sm"
                icon="🗑️"
                onClick={() => {
                  setSelectedRows(new Set());
                  onSelectionChange?.([]);
                }}
              >
                إلغاء التحديد
              </ModernButton>
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="modern-table-wrapper">
        <table className={tableBodyClasses}>
          <thead className="modern-table-head">
            <tr className="modern-table-head-row">
              {selectable && (
                <th className="modern-table-head-cell modern-table-head-cell--checkbox">
                  <input
                    type="checkbox"
                    checked={selectedRows.size === paginatedData.length && paginatedData.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="modern-table-checkbox"
                  />
                </th>
              )}
              
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`modern-table-head-cell ${
                    sortable && column.sortable !== false ? 'modern-table-head-cell--sortable' : ''
                  }`}
                  onClick={() => column.sortable !== false && handleSort(column.key)}
                  style={{ width: column.width }}
                >
                  <div className="modern-table-head-content">
                    <span>{column.title}</span>
                    {sortable && column.sortable !== false && (
                      <span className="modern-table-sort-icon">
                        {sortConfig.key === column.key ? (
                          sortConfig.direction === 'asc' ? '↑' : '↓'
                        ) : '↕'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          
          <tbody className="modern-table-body">
            {paginatedData.length > 0 ? (
              paginatedData.map((row, index) => (
                <tr
                  key={row.id || index}
                  className={`modern-table-row ${
                    selectedRows.has(startIndex + index) ? 'modern-table-row--selected' : ''
                  }`}
                  onClick={() => onRowClick?.(row, index)}
                >
                  {selectable && (
                    <td className="modern-table-cell modern-table-cell--checkbox">
                      <input
                        type="checkbox"
                        checked={selectedRows.has(startIndex + index)}
                        onChange={(e) => handleSelectRow(index, e.target.checked)}
                        className="modern-table-checkbox"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </td>
                  )}
                  
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className="modern-table-cell"
                      style={{ textAlign: column.align || 'right' }}
                    >
                      {column.render ? column.render(row[column.key], row, index) : row[column.key]}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length + (selectable ? 1 : 0)}
                  className="modern-table-empty"
                >
                  <div className="modern-table-empty-content">
                    <div className="modern-table-empty-icon">📋</div>
                    <div className="modern-table-empty-text">لا توجد بيانات للعرض</div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="modern-table-pagination">
          <div className="modern-table-pagination-info">
            عرض {startIndex + 1} إلى {Math.min(startIndex + pageSize, processedData.length)} من {processedData.length} عنصر
          </div>
          
          <div className="modern-table-pagination-controls">
            <IconButton
              icon="⏮"
              variant="ghost"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(1)}
            />
            <IconButton
              icon="◀"
              variant="ghost"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            />
            
            <div className="modern-table-pagination-pages">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }
                
                return (
                  <ModernButton
                    key={pageNum}
                    variant={currentPage === pageNum ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                  >
                    {pageNum}
                  </ModernButton>
                );
              })}
            </div>
            
            <IconButton
              icon="▶"
              variant="ghost"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            />
            <IconButton
              icon="⏭"
              variant="ghost"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(totalPages)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Data Table Component (Enhanced version)
export const DataTable = ({
  data = [],
  columns = [],
  title,
  subtitle,
  actions,
  filters,
  exportable = false,
  refreshable = false,
  onRefresh,
  onExport,
  ...tableProps
}) => {
  const handleExport = () => {
    if (onExport) {
      onExport(data);
    } else {
      // Default CSV export
      const csvContent = [
        columns.map(col => col.title).join(','),
        ...data.map(row => columns.map(col => row[col.key]).join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'data.csv';
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="data-table">
      {(title || subtitle || actions || filters || exportable || refreshable) && (
        <div className="data-table-header">
          <div className="data-table-title-section">
            {title && <h2 className="data-table-title">{title}</h2>}
            {subtitle && <p className="data-table-subtitle">{subtitle}</p>}
          </div>
          
          <div className="data-table-actions">
            {filters}
            
            {refreshable && (
              <IconButton
                icon="🔄"
                variant="ghost"
                onClick={onRefresh}
                tooltip="تحديث البيانات"
              />
            )}
            
            {exportable && (
              <IconButton
                icon="📥"
                variant="ghost"
                onClick={handleExport}
                tooltip="تصدير البيانات"
              />
            )}
            
            {actions}
          </div>
        </div>
      )}
      
      <ModernTable data={data} columns={columns} {...tableProps} />
    </div>
  );
};

export default ModernTable;