@echo off
chcp 65001 >nul
title اختبار سريع - نظام العملاء المحدث

echo.
echo ========================================
echo    🧪 اختبار سريع للنظام المحدث
echo ========================================
echo.

echo 🔍 فحص الملفات...
if exist "frontend\src\pages\Customers\CustomersPage.js" (
    echo ✅ ملف العملاء موجود
) else (
    echo ❌ ملف العملاء مفقود
    pause
    exit
)

echo.
echo 🌐 فتح النظام في المتصفح...
start http://localhost:3000/customers

echo.
echo ========================================
echo ✅ تم فتح النظام بنجاح!
echo.
echo 📋 قائمة الاختبار السريع:
echo.
echo 1. تسجيل الدخول: admin / admin123
echo 2. تحقق من ظهور الإحصائيات (6 بطاقات)
echo 3. تحقق من عرض العملاء في الجدول
echo 4. جرب البحث والفلترة
echo 5. اضغط على "إضافة عميل جديد"
echo 6. تحقق من جميع الأقسام الستة
echo 7. جرب رفع ملف
echo 8. احفظ عميل تجريبي
echo.
echo ========================================
echo.
echo 💡 إذا واجهت مشاكل:
echo    - أعد تحميل الصفحة (F5)
echo    - تحقق من وحدة التحكم (F12)
echo    - تأكد من تشغيل الخادم
echo.
echo اضغط أي مفتاح للخروج...
pause >nul