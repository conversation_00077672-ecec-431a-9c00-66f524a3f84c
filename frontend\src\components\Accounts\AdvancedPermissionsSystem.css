.advanced-permissions-system {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 20px 0;
}

.permissions-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permissions-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.search-box {
  position: relative;
}

.search-input {
  padding: 10px 15px;
  border: none;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  placeholder-color: rgba(255, 255, 255, 0.7);
  min-width: 250px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.permissions-content {
  display: flex;
  min-height: 600px;
}

.modules-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 20px 0;
}

.modules-sidebar h4 {
  padding: 0 20px;
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.module-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;
}

.module-item:hover {
  background: #e9ecef;
}

.module-item.active {
  background: #e3f2fd;
  border-right-color: #2196f3;
  color: #1976d2;
}

.module-icon {
  font-size: 1.2rem;
  margin-left: 10px;
  width: 25px;
  text-align: center;
}

.module-name {
  flex: 1;
  font-weight: 500;
}

.permissions-count {
  background: #6c757d;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.module-item.active .permissions-count {
  background: #2196f3;
}

.permissions-panel {
  flex: 1;
  padding: 20px;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.module-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1.3rem;
  font-weight: 600;
}

.toggle-all-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.toggle-all-btn:hover {
  background: #138496;
  transform: translateY(-1px);
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.permission-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.permission-item:hover {
  background: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.permission-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  gap: 10px;
}

.permission-checkbox {
  margin-top: 2px;
  width: 18px;
  height: 18px;
  accent-color: #28a745;
}

.permission-text {
  flex: 1;
}

.permission-text strong {
  display: block;
  color: #495057;
  font-weight: 600;
  margin-bottom: 4px;
  line-height: 1.4;
}

.permission-text small {
  color: #6c757d;
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
}

.permissions-summary {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 20px;
}

.permissions-summary h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.summary-stats {
  display: flex;
  gap: 30px;
}

.summary-stats span {
  color: #6c757d;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .permissions-content {
    flex-direction: column;
  }
  
  .modules-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .permissions-grid {
    grid-template-columns: 1fr;
  }
  
  .permissions-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-input {
    min-width: 100%;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 10px;
  }
}

/* تأثيرات الانتقال */
.permission-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين مظهر الـ checkbox */
.permission-checkbox {
  position: relative;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.permission-checkbox:checked {
  background: #28a745;
  border-color: #28a745;
}

.permission-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.permission-checkbox:hover {
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

/* تحسين مظهر الأزرار */
.toggle-all-btn {
  position: relative;
  overflow: hidden;
}

.toggle-all-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.toggle-all-btn:hover::before {
  left: 100%;
}

/* تحسين مظهر البحث */
.search-input {
  transition: all 0.3s ease;
}

.search-input:focus {
  transform: scale(1.02);
}

/* إضافة مؤشرات بصرية للحالة */
.module-item {
  position: relative;
}

.module-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: all 0.3s ease;
}

.module-item.active::before {
  background: #2196f3;
}

/* تحسين التباعد والتنسيق */
.permissions-panel {
  overflow-y: auto;
  max-height: 600px;
}

.permissions-panel::-webkit-scrollbar {
  width: 6px;
}

.permissions-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.permissions-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.permissions-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
