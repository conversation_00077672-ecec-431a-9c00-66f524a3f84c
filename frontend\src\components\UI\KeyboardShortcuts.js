import React, { useEffect, useState, createContext, useContext } from 'react';
import './KeyboardShortcuts.css';

// ⌨️ سياق اختصارات لوحة المفاتيح
const ShortcutsContext = createContext();

// 🎯 اختصارات النظام الافتراضية
const DEFAULT_SHORTCUTS = {
  // 🔍 البحث والتنقل
  'ctrl+k': { action: 'openSearch', description: 'فتح البحث السريع', category: 'navigation' },
  'ctrl+/': { action: 'showShortcuts', description: 'عرض الاختصارات', category: 'help' },
  'alt+h': { action: 'goHome', description: 'الذهاب للرئيسية', category: 'navigation' },
  'alt+d': { action: 'goDashboard', description: 'لوحة التحكم', category: 'navigation' },
  
  // 📋 إدارة البيانات
  'ctrl+n': { action: 'createNew', description: 'إنشاء جديد', category: 'data' },
  'ctrl+s': { action: 'save', description: 'حفظ', category: 'data' },
  'ctrl+e': { action: 'edit', description: 'تعديل', category: 'data' },
  'delete': { action: 'delete', description: 'حذف', category: 'data' },
  
  // 🎨 واجهة المستخدم
  'ctrl+shift+t': { action: 'toggleTheme', description: 'تبديل الثيم', category: 'ui' },
  'ctrl+shift+s': { action: 'toggleSidebar', description: 'إظهار/إخفاء الشريط الجانبي', category: 'ui' },
  'f11': { action: 'toggleFullscreen', description: 'ملء الشاشة', category: 'ui' },
  'escape': { action: 'closeModal', description: 'إغلاق النافذة المنبثقة', category: 'ui' },
  
  // 📊 التقارير والطباعة
  'ctrl+p': { action: 'print', description: 'طباعة', category: 'reports' },
  'ctrl+shift+e': { action: 'export', description: 'تصدير', category: 'reports' },
  
  // 🔄 التحديث والمزامنة
  'f5': { action: 'refresh', description: 'تحديث الصفحة', category: 'system' },
  'ctrl+r': { action: 'refresh', description: 'تحديث الصفحة', category: 'system' },
};

// 🎯 فئات الاختصارات
const SHORTCUT_CATEGORIES = {
  navigation: { name: 'التنقل', icon: '🧭', color: '#3b82f6' },
  data: { name: 'إدارة البيانات', icon: '📋', color: '#10b981' },
  ui: { name: 'واجهة المستخدم', icon: '🎨', color: '#8b5cf6' },
  reports: { name: 'التقارير', icon: '📊', color: '#f59e0b' },
  help: { name: 'المساعدة', icon: '❓', color: '#ef4444' },
  system: { name: 'النظام', icon: '⚙️', color: '#6b7280' }
};

// 🎛️ مزود اختصارات لوحة المفاتيح
export const ShortcutsProvider = ({ children, customShortcuts = {} }) => {
  const [shortcuts, setShortcuts] = useState({ ...DEFAULT_SHORTCUTS, ...customShortcuts });
  const [isEnabled, setIsEnabled] = useState(true);
  const [showHelp, setShowHelp] = useState(false);
  const [lastPressed, setLastPressed] = useState('');

  // 🎯 تسجيل اختصار جديد
  const registerShortcut = (key, action, description, category = 'custom') => {
    setShortcuts(prev => ({
      ...prev,
      [key]: { action, description, category }
    }));
  };

  // ❌ إلغاء تسجيل اختصار
  const unregisterShortcut = (key) => {
    setShortcuts(prev => {
      const newShortcuts = { ...prev };
      delete newShortcuts[key];
      return newShortcuts;
    });
  };

  // 🔄 تفعيل/إلغاء تفعيل الاختصارات
  const toggleShortcuts = () => {
    setIsEnabled(!isEnabled);
  };

  // 📋 الحصول على اختصارات فئة معينة
  const getShortcutsByCategory = (category) => {
    return Object.entries(shortcuts).filter(([key, shortcut]) => shortcut.category === category);
  };

  // ⌨️ معالج الضغط على المفاتيح
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isEnabled) return;

      // 🚫 تجاهل الاختصارات في حقول الإدخال
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.isContentEditable) {
        return;
      }

      const key = getKeyString(event);
      setLastPressed(key);

      if (shortcuts[key]) {
        event.preventDefault();
        
        // 🎯 تنفيذ الإجراء
        const shortcut = shortcuts[key];
        executeAction(shortcut.action, event);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts, isEnabled]);

  // 🔤 تحويل الحدث إلى نص المفتاح
  const getKeyString = (event) => {
    const parts = [];
    
    if (event.ctrlKey) parts.push('ctrl');
    if (event.altKey) parts.push('alt');
    if (event.shiftKey) parts.push('shift');
    if (event.metaKey) parts.push('meta');
    
    const key = event.key.toLowerCase();
    if (key !== 'control' && key !== 'alt' && key !== 'shift' && key !== 'meta') {
      parts.push(key);
    }
    
    return parts.join('+');
  };

  // 🎬 تنفيذ الإجراء
  const executeAction = (action, event) => {
    // 🎯 إجراءات النظام المدمجة
    switch (action) {
      case 'showShortcuts':
        setShowHelp(true);
        break;
      case 'toggleTheme':
        // سيتم ربطه مع نظام الثيمات
        document.dispatchEvent(new CustomEvent('toggleTheme'));
        break;
      case 'toggleFullscreen':
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
        break;
      case 'refresh':
        window.location.reload();
        break;
      case 'print':
        window.print();
        break;
      default:
        // 🔄 إرسال حدث مخصص للإجراءات الأخرى
        document.dispatchEvent(new CustomEvent('shortcutAction', {
          detail: { action, event }
        }));
    }
  };

  const value = {
    shortcuts,
    isEnabled,
    showHelp,
    lastPressed,
    registerShortcut,
    unregisterShortcut,
    toggleShortcuts,
    getShortcutsByCategory,
    setShowHelp
  };

  return (
    <ShortcutsContext.Provider value={value}>
      {children}
      {showHelp && <ShortcutsHelp />}
    </ShortcutsContext.Provider>
  );
};

// 🎣 هوك استخدام الاختصارات
export const useShortcuts = () => {
  const context = useContext(ShortcutsContext);
  if (!context) {
    throw new Error('useShortcuts must be used within ShortcutsProvider');
  }
  return context;
};

// 📚 مكون مساعدة الاختصارات
const ShortcutsHelp = () => {
  const { shortcuts, setShowHelp, getShortcutsByCategory } = useShortcuts();

  return (
    <div className="shortcuts-help-overlay" onClick={() => setShowHelp(false)}>
      <div className="shortcuts-help-modal" onClick={(e) => e.stopPropagation()}>
        <div className="shortcuts-help-header">
          <h2>⌨️ اختصارات لوحة المفاتيح</h2>
          <button
            className="shortcuts-help-close"
            onClick={() => setShowHelp(false)}
          >
            ✕
          </button>
        </div>

        <div className="shortcuts-help-content">
          {Object.entries(SHORTCUT_CATEGORIES).map(([categoryKey, category]) => {
            const categoryShortcuts = getShortcutsByCategory(categoryKey);
            
            if (categoryShortcuts.length === 0) return null;

            return (
              <div key={categoryKey} className="shortcuts-category">
                <div className="shortcuts-category-header">
                  <span className="shortcuts-category-icon">{category.icon}</span>
                  <span className="shortcuts-category-name">{category.name}</span>
                </div>
                
                <div className="shortcuts-list">
                  {categoryShortcuts.map(([key, shortcut]) => (
                    <div key={key} className="shortcut-item">
                      <div className="shortcut-keys">
                        {key.split('+').map((part, index) => (
                          <React.Fragment key={part}>
                            {index > 0 && <span className="shortcut-plus">+</span>}
                            <kbd className="shortcut-key">{formatKey(part)}</kbd>
                          </React.Fragment>
                        ))}
                      </div>
                      <div className="shortcut-description">{shortcut.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        <div className="shortcuts-help-footer">
          <p>💡 اضغط <kbd>Ctrl</kbd> + <kbd>/</kbd> لإظهار هذه النافذة في أي وقت</p>
        </div>
      </div>
    </div>
  );
};

// 🔤 تنسيق أسماء المفاتيح
const formatKey = (key) => {
  const keyMap = {
    ctrl: 'Ctrl',
    alt: 'Alt',
    shift: 'Shift',
    meta: 'Cmd',
    escape: 'Esc',
    delete: 'Del',
    ' ': 'Space',
    arrowup: '↑',
    arrowdown: '↓',
    arrowleft: '←',
    arrowright: '→'
  };
  
  return keyMap[key] || key.toUpperCase();
};

// 🎯 مكون عرض الاختصار
export const ShortcutDisplay = ({ shortcut, className = '' }) => {
  if (!shortcut) return null;

  return (
    <div className={`shortcut-display ${className}`}>
      {shortcut.split('+').map((part, index) => (
        <React.Fragment key={part}>
          {index > 0 && <span className="shortcut-plus">+</span>}
          <kbd className="shortcut-key">{formatKey(part)}</kbd>
        </React.Fragment>
      ))}
    </div>
  );
};

// 🔘 مكون زر مع اختصار
export const ShortcutButton = ({ shortcut, children, onClick, ...props }) => {
  const { registerShortcut, unregisterShortcut } = useShortcuts();

  useEffect(() => {
    if (shortcut && onClick) {
      registerShortcut(shortcut, 'customAction', '', 'custom');
      
      const handleCustomAction = (event) => {
        if (event.detail.action === 'customAction') {
          onClick();
        }
      };

      document.addEventListener('shortcutAction', handleCustomAction);
      
      return () => {
        unregisterShortcut(shortcut);
        document.removeEventListener('shortcutAction', handleCustomAction);
      };
    }
  }, [shortcut, onClick, registerShortcut, unregisterShortcut]);

  return (
    <button {...props} onClick={onClick}>
      {children}
      {shortcut && (
        <ShortcutDisplay shortcut={shortcut} className="button-shortcut" />
      )}
    </button>
  );
};

// 📊 مكون إحصائيات الاختصارات
export const ShortcutsStats = () => {
  const { shortcuts, lastPressed, isEnabled } = useShortcuts();
  const [usageCount, setUsageCount] = useState(0);

  useEffect(() => {
    if (lastPressed) {
      setUsageCount(prev => prev + 1);
    }
  }, [lastPressed]);

  return (
    <div className="shortcuts-stats">
      <div className="shortcuts-stats-item">
        <span className="shortcuts-stats-label">الاختصارات المتاحة:</span>
        <span className="shortcuts-stats-value">{Object.keys(shortcuts).length}</span>
      </div>
      <div className="shortcuts-stats-item">
        <span className="shortcuts-stats-label">مرات الاستخدام:</span>
        <span className="shortcuts-stats-value">{usageCount}</span>
      </div>
      <div className="shortcuts-stats-item">
        <span className="shortcuts-stats-label">الحالة:</span>
        <span className={`shortcuts-stats-status ${isEnabled ? 'enabled' : 'disabled'}`}>
          {isEnabled ? 'مفعل' : 'معطل'}
        </span>
      </div>
      {lastPressed && (
        <div className="shortcuts-stats-item">
          <span className="shortcuts-stats-label">آخر اختصار:</span>
          <ShortcutDisplay shortcut={lastPressed} className="shortcuts-stats-shortcut" />
        </div>
      )}
    </div>
  );
};

// تصدير مع أسماء مختلفة للمرونة
export const KeyboardShortcutsProvider = ShortcutsProvider;
export default ShortcutsProvider;
