"""
نموذج المستخدمين والصلاحيات
User and Permissions Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.models.base import BaseModel

# جدول العلاقة بين المستخدمين والأدوار (Many-to-Many)
user_roles = Table(
    'user_roles',
    BaseModel.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True)
)

# جدول العلاقة بين الأدوار والصلاحيات (Many-to-Many)
role_permissions = Table(
    'role_permissions',
    BaseModel.metadata,
    Column('role_id', Integer, Foreign<PERSON><PERSON>('roles.id'), primary_key=True),
    Column('permission_id', Integer, Foreign<PERSON>ey('permissions.id'), primary_key=True)
)

class User(BaseModel):
    """
    نموذج المستخدمين
    """
    __tablename__ = "users"
    
    # المعلومات الأساسية
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # معلومات إضافية
    phone = Column(String(20), nullable=True)
    address = Column(Text, nullable=True)
    avatar = Column(String(255), nullable=True)  # مسار صورة المستخدم
    
    # حالة المستخدم
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    
    # إعدادات المستخدم
    language = Column(String(10), default='ar')  # اللغة المفضلة
    timezone = Column(String(50), default='UTC')
    theme = Column(String(20), default='light')  # المظهر
    
    # العلاقات
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    created_records = relationship("BaseModel", foreign_keys="BaseModel.created_by")
    updated_records = relationship("BaseModel", foreign_keys="BaseModel.updated_by")
    
    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"

class Role(BaseModel):
    """
    نموذج الأدوار
    """
    __tablename__ = "roles"
    
    name = Column(String(50), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)  # الاسم المعروض
    description = Column(Text, nullable=True)
    color = Column(String(7), default='#007bff')  # لون الدور في الواجهة
    
    # العلاقات
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")
    
    def __repr__(self):
        return f"<Role(name='{self.name}')>"

class Permission(BaseModel):
    """
    نموذج الصلاحيات
    """
    __tablename__ = "permissions"
    
    name = Column(String(100), unique=True, nullable=False)  # اسم الصلاحية
    display_name = Column(String(100), nullable=False)  # الاسم المعروض
    description = Column(Text, nullable=True)
    module = Column(String(50), nullable=False)  # الوحدة التي تنتمي إليها الصلاحية
    action = Column(String(50), nullable=False)  # العملية (create, read, update, delete)
    
    # العلاقات
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")
    
    def __repr__(self):
        return f"<Permission(name='{self.name}', module='{self.module}')>"

class UserSession(BaseModel):
    """
    نموذج جلسات المستخدمين
    """
    __tablename__ = "user_sessions"
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True, nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    ip_address = Column(String(45), nullable=True)  # IPv4 أو IPv6
    user_agent = Column(Text, nullable=True)
    is_revoked = Column(Boolean, default=False)
    
    # العلاقات
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserSession(user_id={self.user_id}, expires_at='{self.expires_at}')>"

class UserActivity(BaseModel):
    """
    نموذج أنشطة المستخدمين (سجل العمليات)
    """
    __tablename__ = "user_activities"
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    action = Column(String(100), nullable=False)  # نوع العملية
    module = Column(String(50), nullable=False)  # الوحدة
    record_id = Column(Integer, nullable=True)  # معرف السجل المتأثر
    old_values = Column(Text, nullable=True)  # القيم القديمة (JSON)
    new_values = Column(Text, nullable=True)  # القيم الجديدة (JSON)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # العلاقات
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserActivity(user_id={self.user_id}, action='{self.action}')>"