/* أنماط القوائم المالية المتقدمة */
.financial-statements-advanced {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* رأس الصفحة */
.statements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  gap: 10px;
}

.header-controls .btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
  transform: translateY(-2px);
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* الإحصائيات */
.statements-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stat-card.profit {
  border-left: 4px solid #27ae60;
}

.stat-card.loss {
  border-left: 4px solid #e74c3c;
}

.stat-card.balanced {
  border-left: 4px solid #27ae60;
}

.stat-card.error {
  border-left: 4px solid #e74c3c;
}

.stat-icon {
  font-size: 28px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

/* أدوات التحكم */
.statements-controls {
  margin-bottom: 20px;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-range label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.date-input {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
}

.statement-tabs {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.tab-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.tab-btn:hover:not(.active) {
  background: #e9ecef;
}

.view-modes {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
}

.view-mode-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-mode-btn.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-mode-btn:hover:not(.active) {
  background: #e9ecef;
}

/* عرض القوائم المالية */
.statements-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.statement-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 20px;
  text-align: center;
}

.section-header h3 {
  margin: 0 0 5px 0;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.section-period {
  font-size: 14px;
  opacity: 0.9;
}

.table-container {
  overflow-x: auto;
}

.financial-table {
  width: 100%;
  border-collapse: collapse;
}

.financial-table th {
  background: #f8f9fa;
  color: #2c3e50;
  padding: 15px 10px;
  text-align: center;
  font-weight: bold;
  border-bottom: 2px solid #ddd;
}

.financial-table td {
  padding: 12px 10px;
  border-bottom: 1px solid #f1f2f6;
}

.financial-table tr:hover {
  background: #f8f9fa;
}

.account-name {
  text-align: right;
  font-weight: 500;
  color: #2c3e50;
}

.sub-account {
  padding-right: 30px;
  font-weight: normal;
  color: #5a6c7d;
}

.amount {
  text-align: left;
  font-weight: bold;
  font-family: monospace;
}

.amount.positive {
  color: #27ae60;
}

.amount.negative {
  color: #e74c3c;
}

.category-header {
  background: #f8f9fa;
  font-weight: bold;
}

.category-header .account-name {
  font-weight: bold;
  color: #2c3e50;
}

.total-row {
  background: #e8f4f8;
  border-top: 2px solid #3498db;
  border-bottom: 2px solid #3498db;
}

.total-row .account-name {
  font-weight: bold;
  color: #2c3e50;
}

.total-row .amount {
  font-weight: bold;
  font-size: 16px;
}

.balance-warning {
  background: #fff5f5;
  color: #e74c3c;
  padding: 15px;
  margin: 15px;
  border-radius: 8px;
  border: 1px solid #fadbd8;
  text-align: center;
  font-weight: bold;
}

/* النماذج المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.print-modal,
.export-modal,
.comparison-modal,
.analysis-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.print-modal,
.export-modal {
  max-width: 500px;
}

.comparison-modal,
.analysis-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #e74c3c;
}

.modal-content {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #f1f2f6;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
}

/* نموذج خيارات الطباعة */
.print-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #2c3e50;
  cursor: pointer;
}

.option-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.form-control {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* نموذج خيارات التصدير */
.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.format-selection h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-option:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.format-option input[type="radio"] {
  width: 16px;
  height: 16px;
}

.format-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.format-icon {
  font-size: 24px;
}

.format-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.format-desc {
  font-size: 12px;
  color: #7f8c8d;
}

.export-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.export-summary h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.summary-item .label {
  color: #7f8c8d;
}

.summary-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.summary-item .value.positive {
  color: #27ae60;
}

.summary-item .value.negative {
  color: #e74c3c;
}

/* نموذج المقارنة */
.comparison-settings {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.period-selection h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
}

.comparison-preview h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-table th {
  background: #f8f9fa;
  padding: 12px;
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.comparison-table td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #f1f2f6;
}

.comparison-table .amount {
  font-weight: bold;
  font-family: monospace;
}

/* نموذج التحليل المالي */
.financial-analysis {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.ratios-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.ratios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.ratio-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
  text-align: center;
  transition: all 0.3s ease;
}

.ratio-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.ratio-name {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 8px;
  font-weight: 500;
}

.ratio-value {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.ratio-desc {
  font-size: 10px;
  color: #95a5a6;
  font-style: italic;
}

.analysis-insights h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.insight {
  padding: 12px 15px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.insight.success {
  background: #d5f4e6;
  color: #27ae60;
  border: 1px solid #a9dfbf;
}

.insight.warning {
  background: #fef9e7;
  color: #f39c12;
  border: 1px solid #f7dc6f;
}

.insight.error {
  background: #fadbd8;
  color: #e74c3c;
  border: 1px solid #f1948a;
}

.insight.info {
  background: #d6eaf8;
  color: #3498db;
  border: 1px solid #aed6f1;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .financial-statements-advanced {
    padding: 10px;
  }
  
  .statements-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .statements-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .controls-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .date-range {
    flex-direction: column;
    align-items: stretch;
  }
  
  .statement-tabs {
    flex-direction: column;
  }
  
  .view-modes {
    flex-direction: column;
  }
  
  .financial-table {
    font-size: 12px;
  }
  
  .financial-table th,
  .financial-table td {
    padding: 8px 4px;
  }
  
  .ratios-grid {
    grid-template-columns: 1fr;
  }
  
  .comparison-table {
    font-size: 12px;
  }
  
  .comparison-table th,
  .comparison-table td {
    padding: 6px 4px;
  }
  
  .format-options {
    gap: 8px;
  }
  
  .format-option {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .statements-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .header-controls {
    grid-template-columns: 1fr;
  }
  
  .financial-table th,
  .financial-table td {
    padding: 6px 2px;
    font-size: 10px;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-content {
    padding: 15px;
  }
  
  .tab-btn {
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .view-mode-btn {
    font-size: 10px;
    padding: 6px 8px;
  }
}