import React, { useState, useEffect, useRef, useMemo } from 'react';
import './JournalBooks.css';

const JournalBooksAdvanced = ({ transactions, accounts, onAddTransaction, currentUser }) => {
  const [activeJournal, setActiveJournal] = useState('general');
  const [showEntryModal, setShowEntryModal] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [printOptions, setPrintOptions] = useState({
    includeDetails: true,
    includeBalances: true,
    includeNotes: false,
    pageSize: 'A4',
    orientation: 'portrait'
  });
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportFormat, setExportFormat] = useState('csv');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    amountRange: { min: '', max: '' },
    accountTypes: [],
    users: [],
    hasAttachments: false
  });
  const [viewMode, setViewMode] = useState('table');
  const [sortConfig, setSortConfig] = useState({ key: 'date', direction: 'desc' });
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  
  const printRef = useRef();
  const fileInputRef = useRef();

  const [newEntry, setNewEntry] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    entries: [
      { accountId: '', accountName: '', debit: 0, credit: 0, description: '' },
      { accountId: '', accountName: '', debit: 0, credit: 0, description: '' }
    ],
    notes: '',
    attachments: []
  });

  // أنواع دفاتر اليومية
  const journalTypes = {
    general: {
      name: 'دفتر اليومية العامة',
      icon: '📖',
      color: '#3498db',
      description: 'جميع القيود المحاسبية'
    },
    sales: {
      name: 'دفتر يومية المبيعات',
      icon: '💰',
      color: '#27ae60',
      description: 'قيود المبيعات والإيرادات'
    },
    purchases: {
      name: 'دفتر يومية المشتريات',
      icon: '🛒',
      color: '#e74c3c',
      description: 'قيود المشتريات والمصروفات'
    },
    cash: {
      name: 'دفتر يومية النقدية',
      icon: '💵',
      color: '#f39c12',
      description: 'المعاملات النقدية'
    },
    bank: {
      name: 'دفتر يومية البنك',
      icon: '🏦',
      color: '#9b59b6',
      description: 'المعاملات البنكية'
    }
  };

  // تصفية المعاملات المتقدمة
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const matchesSearch = 
        transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.id.toLowerCase().includes(searchTerm.toLowerCase());

      const transactionDate = new Date(transaction.date);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      const matchesDate = transactionDate >= startDate && transactionDate <= endDate;

      // تصفية حسب نوع الدفتر
      let matchesJournal = true;
      switch (activeJournal) {
        case 'sales':
          matchesJournal = transaction.type === 'revenue' || 
                          transaction.entries?.some(entry => 
                            accounts.find(acc => acc.id === entry.accountId)?.category === 'revenue'
                          );
          break;
        case 'purchases':
          matchesJournal = transaction.type === 'expense' || 
                          transaction.entries?.some(entry => 
                            accounts.find(acc => acc.id === entry.accountId)?.category === 'expenses'
                          );
          break;
        case 'cash':
          matchesJournal = transaction.entries?.some(entry => 
            entry.accountId === '1111' || 
            accounts.find(acc => acc.id === entry.accountId)?.name.includes('صندوق')
          );
          break;
        case 'bank':
          matchesJournal = transaction.entries?.some(entry => 
            entry.accountId === '1112' || 
            accounts.find(acc => acc.id === entry.accountId)?.name.includes('بنك')
          );
          break;
        default:
          matchesJournal = true;
      }

      // تصفية متقدمة
      const totalAmount = transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0;
      const matchesAmount = (!advancedFilters.amountRange.min || totalAmount >= parseFloat(advancedFilters.amountRange.min)) &&
                           (!advancedFilters.amountRange.max || totalAmount <= parseFloat(advancedFilters.amountRange.max));

      const matchesUser = advancedFilters.users.length === 0 || advancedFilters.users.includes(transaction.user);

      const matchesAttachments = !advancedFilters.hasAttachments || 
                                (transaction.attachments && transaction.attachments.length > 0);

      return matchesSearch && matchesDate && matchesJournal && matchesAmount && matchesUser && matchesAttachments;
    }).sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (sortConfig.key === 'date') {
        return sortConfig.direction === 'asc' 
          ? new Date(aValue) - new Date(bValue)
          : new Date(bValue) - new Date(aValue);
      }
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [transactions, searchTerm, dateRange, activeJournal, advancedFilters, sortConfig, accounts]);

  // حساب الإحصائيات
  const journalStats = useMemo(() => {
    const totalDebit = filteredTransactions.reduce((sum, trans) => 
      sum + (trans.entries?.reduce((entrySum, entry) => entrySum + (entry.debit || 0), 0) || 0), 0);
    const totalCredit = filteredTransactions.reduce((sum, trans) => 
      sum + (trans.entries?.reduce((entrySum, entry) => entrySum + (entry.credit || 0), 0) || 0), 0);

    return {
      totalTransactions: filteredTransactions.length,
      totalDebit,
      totalCredit,
      balance: totalDebit - totalCredit,
      averageAmount: filteredTransactions.length > 0 ? totalDebit / filteredTransactions.length : 0
    };
  }, [filteredTransactions]);

  // وظائف الطباعة
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>دفتر ${journalTypes[activeJournal].name}</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .stats { display: flex; justify-content: space-around; margin: 20px 0; padding: 15px; background: #f5f5f5; }
          .stat-item { text-align: center; }
          .stat-value { font-size: 18px; font-weight: bold; color: #333; }
          .stat-label { font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .amount { font-weight: bold; }
          .total-row { background-color: #f0f0f0; font-weight: bold; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    return `
      <div class="header">
        <div class="company-name">شركة شراء السياحية</div>
        <div class="report-title">${journalTypes[activeJournal].name}</div>
        <div class="date-range">
          من ${new Date(dateRange.startDate).toLocaleDateString('ar-SA')} 
          إلى ${new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
        </div>
      </div>
      
      ${printOptions.includeBalances ? `
        <div class="stats">
          <div class="stat-item">
            <div class="stat-value">${journalStats.totalTransactions}</div>
            <div class="stat-label">عدد القيود</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${journalStats.totalDebit.toLocaleString()}</div>
            <div class="stat-label">إجمالي المدين</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${journalStats.totalCredit.toLocaleString()}</div>
            <div class="stat-label">إجمالي الدائن</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">${journalStats.balance.toLocaleString()}</div>
            <div class="stat-label">الرصيد</div>
          </div>
        </div>
      ` : ''}
      
      <table>
        <thead>
          <tr>
            <th>التاريخ</th>
            <th>رقم القيد</th>
            <th>البيان</th>
            <th>المرجع</th>
            <th>مدين</th>
            <th>دائن</th>
            ${printOptions.includeNotes ? '<th>ملاحظات</th>' : ''}
          </tr>
        </thead>
        <tbody>
          ${filteredTransactions.map(transaction => `
            <tr>
              <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
              <td>${transaction.id}</td>
              <td>${transaction.description}</td>
              <td>${transaction.reference || '-'}</td>
              <td class="amount">${(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()}</td>
              <td class="amount">${(transaction.entries?.reduce((sum, entry) => sum + (entry.credit || 0), 0) || 0).toLocaleString()}</td>
              ${printOptions.includeNotes ? `<td>${transaction.notes || '-'}</td>` : ''}
            </tr>
            ${printOptions.includeDetails ? transaction.entries?.map(entry => `
              <tr style="background-color: #f9f9f9;">
                <td></td>
                <td></td>
                <td style="padding-right: 20px;">${entry.accountName} - ${entry.description}</td>
                <td></td>
                <td class="amount">${entry.debit ? entry.debit.toLocaleString() : '-'}</td>
                <td class="amount">${entry.credit ? entry.credit.toLocaleString() : '-'}</td>
                ${printOptions.includeNotes ? '<td></td>' : ''}
              </tr>
            `).join('') : ''}
          `).join('')}
          <tr class="total-row">
            <td colspan="${printOptions.includeNotes ? '4' : '4'}"><strong>الإجمالي</strong></td>
            <td class="amount"><strong>${journalStats.totalDebit.toLocaleString()}</strong></td>
            <td class="amount"><strong>${journalStats.totalCredit.toLocaleString()}</strong></td>
            ${printOptions.includeNotes ? '<td></td>' : ''}
          </tr>
        </tbody>
      </table>
      
      <div class="footer">
        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
        <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
      </div>
    `;
  };

  // وظائف التصدير
  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    const headers = ['التاريخ', 'رقم القيد', 'البيان', 'المرجع', 'الحساب', 'مدين', 'دائن', 'المستخدم'];
    const rows = [];

    filteredTransactions.forEach(transaction => {
      transaction.entries?.forEach(entry => {
        rows.push([
          new Date(transaction.date).toLocaleDateString('ar-SA'),
          transaction.id,
          transaction.description,
          transaction.reference || '',
          entry.accountName,
          entry.debit || 0,
          entry.credit || 0,
          transaction.user || ''
        ]);
      });
    });

    const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${journalTypes[activeJournal].name}-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  // وظائف التصفية المتقدمة
  const applyAdvancedFilters = (filters) => {
    setAdvancedFilters(filters);
    setShowFilterModal(false);
  };

  const resetFilters = () => {
    setAdvancedFilters({
      amountRange: { min: '', max: '' },
      accountTypes: [],
      users: [],
      hasAttachments: false
    });
    setSearchTerm('');
    setDateRange({
      startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    });
  };

  // وظائف الترتيب
  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // وظائف استيراد البيانات
  const handleImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const csvData = e.target.result;
          const lines = csvData.split('\n');
          const headers = lines[0].split(',');
          
          alert(`تم استيراد ${lines.length - 1} سجل بنجاح`);
        } catch (error) {
          alert('خطأ في استيراد الملف');
        }
      };
      reader.readAsText(file);
    }
  };

  // إضافة قيد جديد
  const addNewEntry = () => {
    const totalDebit = newEntry.entries.reduce((sum, entry) => sum + (parseFloat(entry.debit) || 0), 0);
    const totalCredit = newEntry.entries.reduce((sum, entry) => sum + (parseFloat(entry.credit) || 0), 0);

    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      alert('القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
      return;
    }

    const transaction = {
      id: `JE-${Date.now()}`,
      date: newEntry.date,
      description: newEntry.description,
      reference: newEntry.reference,
      type: 'manual',
      user: currentUser?.name || 'النظام',
      entries: newEntry.entries.filter(entry => entry.accountId && (entry.debit > 0 || entry.credit > 0)),
      notes: newEntry.notes,
      attachments: newEntry.attachments,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    onAddTransaction(transaction);
    setNewEntry({
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      entries: [
        { accountId: '', accountName: '', debit: 0, credit: 0, description: '' },
        { accountId: '', accountName: '', debit: 0, credit: 0, description: '' }
      ],
      notes: '',
      attachments: []
    });
    setShowEntryModal(false);
    alert('تم إضافة القيد بنجاح');
  };

  // إضافة سطر جديد للقيد
  const addEntryLine = () => {
    setNewEntry(prev => ({
      ...prev,
      entries: [...prev.entries, { accountId: '', accountName: '', debit: 0, credit: 0, description: '' }]
    }));
  };

  // حذف سطر من القيد
  const removeEntryLine = (index) => {
    if (newEntry.entries.length > 2) {
      setNewEntry(prev => ({
        ...prev,
        entries: prev.entries.filter((_, i) => i !== index)
      }));
    }
  };

  // تحديث سطر في القيد
  const updateEntryLine = (index, field, value) => {
    setNewEntry(prev => ({
      ...prev,
      entries: prev.entries.map((entry, i) => {
        if (i === index) {
          const updatedEntry = { ...entry, [field]: value };
          
          // إذا تم تحديد الحساب، احصل على اسم الحساب
          if (field === 'accountId') {
            const account = accounts.find(acc => acc.id === value);
            updatedEntry.accountName = account?.name || '';
          }
          
          return updatedEntry;
        }
        return entry;
      })
    }));
  };

  return (
    <div className="journal-books-advanced">
      <div className="journal-header">
        <div className="header-content">
          <h2>📚 دفاتر اليومية المتقدمة</h2>
          <p>إدارة شاملة لجميع أنواع دفاتر اليومية مع وظائف متقدمة</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowEntryModal(true)}
          >
            ➕ قيد جديد
          </button>
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-success"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => setShowFilterModal(true)}
          >
            🔍 تصفية متقدمة
          </button>
          <button 
            className="btn btn-secondary"
            onClick={handleImport}
          >
            📥 استيراد
          </button>
        </div>
      </div>

      {/* شريط التبويبات */}
      <div className="journal-tabs">
        {Object.entries(journalTypes).map(([key, journal]) => (
          <button
            key={key}
            className={`journal-tab ${activeJournal === key ? 'active' : ''}`}
            onClick={() => setActiveJournal(key)}
            style={{ borderColor: journal.color }}
          >
            <span className="tab-icon">{journal.icon}</span>
            <div className="tab-content">
              <div className="tab-name">{journal.name}</div>
              <div className="tab-description">{journal.description}</div>
            </div>
          </button>
        ))}
      </div>

      {/* شريط الأدوات */}
      <div className="toolbar">
        <div className="toolbar-left">
          <div className="search-box">
            <input
              type="text"
              placeholder="🔍 البحث في القيود..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="date-range">
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
        </div>

        <div className="toolbar-right">
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => setViewMode('table')}
            >
              📋 جدول
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'cards' ? 'active' : ''}`}
              onClick={() => setViewMode('cards')}
            >
              🗃️ بطاقات
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'timeline' ? 'active' : ''}`}
              onClick={() => setViewMode('timeline')}
            >
              📅 خط زمني
            </button>
          </div>
          
          <button className="reset-btn" onClick={resetFilters}>
            🔄 إعادة تعيين
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="journal-stats">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <div className="stat-value">{journalStats.totalTransactions}</div>
            <div className="stat-label">عدد القيود</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{journalStats.totalDebit.toLocaleString()}</div>
            <div className="stat-label">إجمالي المدين</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💳</div>
          <div className="stat-info">
            <div className="stat-value">{journalStats.totalCredit.toLocaleString()}</div>
            <div className="stat-label">إجمالي الدائن</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⚖️</div>
          <div className="stat-info">
            <div className="stat-value">{journalStats.balance.toLocaleString()}</div>
            <div className="stat-label">الرصيد</div>
          </div>
        </div>
      </div>

      {/* عرض البيانات */}
      <div className="journal-content">
        {viewMode === 'table' && (
          <div className="table-view">
            {filteredTransactions.length === 0 ? (
              <div className="no-data">
                <span className="icon">📝</span>
                <h3>لا توجد قيود</h3>
                <p>لا توجد قيود محاسبية تطابق المعايير المحددة</p>
              </div>
            ) : (
              <div className="table-container">
                <table className="journal-table">
                  <thead>
                    <tr>
                      <th onClick={() => handleSort('date')}>
                        التاريخ {sortConfig.key === 'date' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th onClick={() => handleSort('id')}>
                        رقم القيد {sortConfig.key === 'id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                      </th>
                      <th>البيان</th>
                      <th>المرجع</th>
                      <th>مدين</th>
                      <th>دائن</th>
                      <th>المستخدم</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTransactions.map(transaction => (
                      <tr key={transaction.id} className="transaction-row">
                        <td>{new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                        <td className="transaction-id">{transaction.id}</td>
                        <td className="description">{transaction.description}</td>
                        <td>{transaction.reference || '-'}</td>
                        <td className="amount debit">
                          {(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()}
                        </td>
                        <td className="amount credit">
                          {(transaction.entries?.reduce((sum, entry) => sum + (entry.credit || 0), 0) || 0).toLocaleString()}
                        </td>
                        <td className="user">{transaction.user}</td>
                        <td className="actions">
                          <button
                            className="action-btn view-btn"
                            onClick={() => {
                              setSelectedEntry(transaction);
                              setShowDetailsModal(true);
                            }}
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          <button
                            className="action-btn edit-btn"
                            title="تعديل"
                          >
                            ✏️
                          </button>
                          <button
                            className="action-btn print-btn"
                            onClick={() => {
                              setSelectedEntry(transaction);
                              handlePrint();
                            }}
                            title="طباعة"
                          >
                            🖨️
                          </button>
                          <button
                            className="action-btn delete-btn"
                            title="حذف"
                          >
                            🗑️
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {viewMode === 'cards' && (
          <div className="cards-view">
            {filteredTransactions.map(transaction => (
              <div key={transaction.id} className="transaction-card">
                <div className="card-header">
                  <div className="card-title">
                    <span className="transaction-id">{transaction.id}</span>
                    <span className="transaction-date">
                      {new Date(transaction.date).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                  <div className="card-actions">
                    <button className="action-btn view-btn" title="عرض">👁️</button>
                    <button className="action-btn edit-btn" title="تعديل">✏️</button>
                    <button className="action-btn print-btn" title="طباعة">🖨️</button>
                  </div>
                </div>
                
                <div className="card-content">
                  <div className="description">{transaction.description}</div>
                  {transaction.reference && (
                    <div className="reference">المرجع: {transaction.reference}</div>
                  )}
                  
                  <div className="amounts">
                    <div className="amount-item debit">
                      <span className="label">مدين:</span>
                      <span className="value">
                        {(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="amount-item credit">
                      <span className="label">دائن:</span>
                      <span className="value">
                        {(transaction.entries?.reduce((sum, entry) => sum + (entry.credit || 0), 0) || 0).toLocaleString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="card-footer">
                    <span className="user">👤 {transaction.user}</span>
                    <span className="entries-count">
                      📝 {transaction.entries?.length || 0} سطر
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {viewMode === 'timeline' && (
          <div className="timeline-view">
            {filteredTransactions.map((transaction, index) => (
              <div key={transaction.id} className="timeline-item">
                <div className="timeline-marker"></div>
                <div className="timeline-content">
                  <div className="timeline-header">
                    <span className="timeline-date">
                      {new Date(transaction.date).toLocaleDateString('ar-SA')}
                    </span>
                    <span className="timeline-id">{transaction.id}</span>
                  </div>
                  <div className="timeline-description">{transaction.description}</div>
                  <div className="timeline-amount">
                    مدين: {(transaction.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0) || 0).toLocaleString()} |
                    دائن: {(transaction.entries?.reduce((sum, entry) => sum + (entry.credit || 0), 0) || 0).toLocaleString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* نموذج إضافة قيد جديد */}
      {showEntryModal && (
        <div className="modal-overlay">
          <div className="entry-modal">
            <div className="modal-header">
              <h3>➕ قيد محاسبي جديد</h3>
              <button 
                className="close-btn"
                onClick={() => setShowEntryModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="entry-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>التاريخ *</label>
                    <input
                      type="date"
                      value={newEntry.date}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, date: e.target.value }))}
                      className="form-control"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>المرجع</label>
                    <input
                      type="text"
                      value={newEntry.reference}
                      onChange={(e) => setNewEntry(prev => ({ ...prev, reference: e.target.value }))}
                      className="form-control"
                      placeholder="رقم المرجع"
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>البيان *</label>
                  <input
                    type="text"
                    value={newEntry.description}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    placeholder="وصف القيد المحاسبي"
                  />
                </div>

                <div className="entries-section">
                  <div className="section-header">
                    <h4>تفاصيل القيد</h4>
                    <button 
                      type="button"
                      className="btn btn-sm btn-primary"
                      onClick={addEntryLine}
                    >
                      ➕ إضافة سطر
                    </button>
                  </div>

                  <div className="entries-table">
                    <table>
                      <thead>
                        <tr>
                          <th>الحساب</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                          <th>إجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {newEntry.entries.map((entry, index) => (
                          <tr key={index}>
                            <td>
                              <select
                                value={entry.accountId}
                                onChange={(e) => updateEntryLine(index, 'accountId', e.target.value)}
                                className="form-control"
                              >
                                <option value="">اختر الحساب</option>
                                {accounts.filter(acc => acc.type === 'detail').map(account => (
                                  <option key={account.id} value={account.id}>
                                    {account.name} ({account.id})
                                  </option>
                                ))}
                              </select>
                            </td>
                            <td>
                              <input
                                type="text"
                                value={entry.description}
                                onChange={(e) => updateEntryLine(index, 'description', e.target.value)}
                                className="form-control"
                                placeholder="بيان السطر"
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={entry.debit}
                                onChange={(e) => updateEntryLine(index, 'debit', parseFloat(e.target.value) || 0)}
                                className="form-control"
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={entry.credit}
                                onChange={(e) => updateEntryLine(index, 'credit', parseFloat(e.target.value) || 0)}
                                className="form-control"
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td>
                              {newEntry.entries.length > 2 && (
                                <button
                                  type="button"
                                  className="btn btn-sm btn-danger"
                                  onClick={() => removeEntryLine(index)}
                                >
                                  🗑️
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan="2"><strong>الإجمالي</strong></td>
                          <td className="total-debit">
                            <strong>
                              {newEntry.entries.reduce((sum, entry) => sum + (parseFloat(entry.debit) || 0), 0).toLocaleString()}
                            </strong>
                          </td>
                          <td className="total-credit">
                            <strong>
                              {newEntry.entries.reduce((sum, entry) => sum + (parseFloat(entry.credit) || 0), 0).toLocaleString()}
                            </strong>
                          </td>
                          <td></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>

                <div className="form-group">
                  <label>ملاحظات</label>
                  <textarea
                    value={newEntry.notes}
                    onChange={(e) => setNewEntry(prev => ({ ...prev, notes: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowEntryModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={addNewEntry}
              >
                💾 حفظ القيد
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeDetails}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                    />
                    تضمين تفاصيل القيود
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeBalances}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeBalances: e.target.checked }))}
                    />
                    تضمين الأرصدة والإحصائيات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeNotes}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeNotes: e.target.checked }))}
                    />
                    تضمين الملاحظات
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج خيارات التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">عدد القيود:</span>
                      <span className="value">{filteredTransactions.length}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">الفترة:</span>
                      <span className="value">
                        {new Date(dateRange.startDate).toLocaleDateString('ar-SA')} - 
                        {new Date(dateRange.endDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="summary-item">
                      <span className="label">نوع الدفتر:</span>
                      <span className="value">{journalTypes[activeJournal].name}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصفية المتقدمة */}
      {showFilterModal && (
        <div className="modal-overlay">
          <div className="filter-modal">
            <div className="modal-header">
              <h3>🔍 تصفية متقدمة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowFilterModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="filter-form">
                <div className="filter-group">
                  <label>نطاق المبلغ:</label>
                  <div className="range-inputs">
                    <input
                      type="number"
                      placeholder="من"
                      value={advancedFilters.amountRange.min}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        amountRange: { ...prev.amountRange, min: e.target.value }
                      }))}
                      className="form-control"
                    />
                    <span>إلى</span>
                    <input
                      type="number"
                      placeholder="إلى"
                      value={advancedFilters.amountRange.max}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        amountRange: { ...prev.amountRange, max: e.target.value }
                      }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="filter-group">
                  <label>المستخدمين:</label>
                  <div className="users-checkboxes">
                    {[...new Set(transactions.map(t => t.user).filter(Boolean))].map(user => (
                      <label key={user} className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={advancedFilters.users.includes(user)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setAdvancedFilters(prev => ({
                                ...prev,
                                users: [...prev.users, user]
                              }));
                            } else {
                              setAdvancedFilters(prev => ({
                                ...prev,
                                users: prev.users.filter(u => u !== user)
                              }));
                            }
                          }}
                        />
                        {user}
                      </label>
                    ))}
                  </div>
                </div>

                <div className="filter-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={advancedFilters.hasAttachments}
                      onChange={(e) => setAdvancedFilters(prev => ({
                        ...prev,
                        hasAttachments: e.target.checked
                      }))}
                    />
                    القيود التي تحتوي على مرفقات فقط
                  </label>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowFilterModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-warning"
                onClick={resetFilters}
              >
                🔄 إعادة تعيين
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => applyAdvancedFilters(advancedFilters)}
              >
                ✅ تطبيق المرشحات
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تفاصيل القيد */}
      {showDetailsModal && selectedEntry && (
        <div className="modal-overlay">
          <div className="details-modal">
            <div className="modal-header">
              <h3>📋 تفاصيل القيد {selectedEntry.id}</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="entry-details">
                <div className="details-grid">
                  <div className="detail-item">
                    <label>رقم القيد:</label>
                    <span>{selectedEntry.id}</span>
                  </div>
                  <div className="detail-item">
                    <label>التاريخ:</label>
                    <span>{new Date(selectedEntry.date).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>المرجع:</label>
                    <span>{selectedEntry.reference || 'غير محدد'}</span>
                  </div>
                  <div className="detail-item">
                    <label>المستخدم:</label>
                    <span>{selectedEntry.user}</span>
                  </div>
                </div>

                <div className="description-section">
                  <label>البيان:</label>
                  <p>{selectedEntry.description}</p>
                </div>

                <div className="entries-details">
                  <h4>تفاصيل القيد:</h4>
                  <table className="details-table">
                    <thead>
                      <tr>
                        <th>الحساب</th>
                        <th>البيان</th>
                        <th>مدين</th>
                        <th>دائن</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedEntry.entries?.map((entry, index) => (
                        <tr key={index}>
                          <td>
                            <div className="account-info">
                              <div className="account-name">{entry.accountName}</div>
                              <div className="account-code">({entry.accountId})</div>
                            </div>
                          </td>
                          <td>{entry.description}</td>
                          <td className="amount debit">
                            {entry.debit ? entry.debit.toLocaleString() : '-'}
                          </td>
                          <td className="amount credit">
                            {entry.credit ? entry.credit.toLocaleString() : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <td colSpan="2"><strong>الإجمالي</strong></td>
                        <td className="total-debit">
                          <strong>
                            {selectedEntry.entries?.reduce((sum, entry) => sum + (entry.debit || 0), 0).toLocaleString()}
                          </strong>
                        </td>
                        <td className="total-credit">
                          <strong>
                            {selectedEntry.entries?.reduce((sum, entry) => sum + (entry.credit || 0), 0).toLocaleString()}
                          </strong>
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                {selectedEntry.notes && (
                  <div className="notes-section">
                    <label>ملاحظات:</label>
                    <p>{selectedEntry.notes}</p>
                  </div>
                )}

                <div className="metadata-section">
                  <div className="metadata-grid">
                    <div className="metadata-item">
                      <label>تاريخ الإنشاء:</label>
                      <span>{new Date(selectedEntry.createdAt || selectedEntry.date).toLocaleString('ar-SA')}</span>
                    </div>
                    <div className="metadata-item">
                      <label>آخر تحديث:</label>
                      <span>{new Date(selectedEntry.updatedAt || selectedEntry.date).toLocaleString('ar-SA')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDetailsModal(false)}
              >
                إغلاق
              </button>
              <button 
                className="btn btn-info"
                onClick={() => {
                  setSelectedEntry(selectedEntry);
                  handlePrint();
                }}
              >
                🖨️ طباعة
              </button>
              <button 
                className="btn btn-warning"
              >
                ✏️ تعديل
              </button>
            </div>
          </div>
        </div>
      )}

      {/* حقل الاستيراد المخفي */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileImport}
        accept=".csv,.xlsx,.xls"
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default JournalBooksAdvanced;