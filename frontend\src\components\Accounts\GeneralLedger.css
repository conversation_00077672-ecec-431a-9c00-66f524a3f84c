.general-ledger {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.ledger-header {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: bold;
}

.header-content p {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  padding: 10px 15px;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.date-range label {
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
  font-size: 14px;
}

.date-range input {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.ledger-content {
  display: flex;
  gap: 25px;
  min-height: 600px;
}

.accounts-sidebar {
  width: 350px;
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  height: fit-content;
  max-height: 80vh;
  overflow-y: auto;
}

.search-box {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 14px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.accounts-tree {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.account-category {
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.category-header {
  padding: 15px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  color: #2c3e50;
}

.category-icon {
  font-size: 18px;
}

.category-name {
  flex: 1;
  font-size: 16px;
}

.accounts-count {
  font-size: 12px;
  color: #7f8c8d;
  background: white;
  padding: 4px 8px;
  border-radius: 12px;
}

.category-accounts {
  background: white;
}

.account-item {
  padding: 12px 15px;
  border-bottom: 1px solid #f1f2f6;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.account-item:hover {
  background: #f8f9fa;
  transform: translateX(-3px);
}

.account-item.selected {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  transform: translateX(-5px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.account-info {
  flex: 1;
}

.account-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 2px;
}

.account-code {
  font-size: 12px;
  opacity: 0.7;
}

.account-item.selected .account-code {
  opacity: 0.9;
}

.account-balance {
  font-weight: bold;
  font-size: 14px;
  text-align: left;
}

.account-item.selected .account-balance {
  color: white !important;
}

.ledger-details {
  flex: 1;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: auto;
}

.account-ledger {
  height: 100%;
}

.account-header {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f2f6;
}

.account-title h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: bold;
}

.account-title .account-code {
  color: #7f8c8d;
  font-size: 14px;
  margin-top: 5px;
  display: block;
}

.account-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.summary-item {
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  border: 2px solid;
}

.summary-item.debit {
  background: #27ae6010;
  border-color: #27ae60;
}

.summary-item.credit {
  background: #e74c3c10;
  border-color: #e74c3c;
}

.summary-item.balance {
  background: #3498db10;
  border-color: #3498db;
}

.summary-item .label {
  display: block;
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.summary-item .value {
  display: block;
  font-size: 18px;
  font-weight: bold;
}

.summary-item.debit .value {
  color: #27ae60;
}

.summary-item.credit .value {
  color: #e74c3c;
}

.summary-item.balance .value {
  color: #3498db;
}

.movements-table {
  overflow-x: auto;
  margin-top: 20px;
}

.movements-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.movements-table th {
  background: #f8f9fa;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  font-weight: bold;
  color: #2c3e50;
  white-space: nowrap;
}

.movements-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  white-space: nowrap;
}

.movement-row:hover {
  background: #f8f9fa;
}

.movement-row.revenue {
  border-left: 4px solid #27ae60;
}

.movement-row.expense {
  border-left: 4px solid #e74c3c;
}

.movement-row.transfer {
  border-left: 4px solid #3498db;
}

.movement-row.manual {
  border-left: 4px solid #9b59b6;
}

.entry-id {
  font-family: monospace;
  font-weight: bold;
  color: #3498db;
}

.debit {
  color: #27ae60;
  font-weight: bold;
}

.credit {
  color: #e74c3c;
  font-weight: bold;
}

.balance {
  color: #2c3e50;
  font-weight: bold;
  background: #f8f9fa;
}

.user {
  color: #7f8c8d;
  font-size: 12px;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-badge.revenue {
  background: #27ae6020;
  color: #27ae60;
}

.type-badge.expense {
  background: #e74c3c20;
  color: #e74c3c;
}

.type-badge.transfer {
  background: #3498db20;
  color: #3498db;
}

.type-badge.manual {
  background: #9b59b620;
  color: #9b59b6;
}

.no-selection {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-selection-content {
  text-align: center;
  color: #7f8c8d;
}

.no-selection-content .icon {
  font-size: 64px;
  margin-bottom: 20px;
  display: block;
}

.no-selection-content h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.no-selection-content p {
  margin: 0;
  font-size: 14px;
}

/* نموذج المعاملة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.transaction-modal {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 2px solid #f1f2f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 25px;
}

.transaction-type-selector {
  margin-bottom: 25px;
}

.transaction-type-selector label {
  display: block;
  margin-bottom: 15px;
  font-weight: bold;
  color: #2c3e50;
}

.type-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.type-btn {
  padding: 12px 15px;
  border: 2px solid;
  border-radius: 10px;
  background: transparent;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  text-align: center;
}

.type-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.modal-footer {
  padding: 25px;
  border-top: 2px solid #f1f2f6;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
  .ledger-content {
    flex-direction: column;
  }
  
  .accounts-sidebar {
    width: 100%;
    max-height: 400px;
  }
  
  .ledger-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .date-range {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .account-summary {
    grid-template-columns: 1fr;
  }
  
  .movements-table {
    font-size: 12px;
  }
  
  .movements-table th,
  .movements-table td {
    padding: 8px 4px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .type-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .general-ledger {
    padding: 10px;
  }
  
  .ledger-header,
  .accounts-sidebar,
  .ledger-details,
  .modal-content {
    padding: 15px;
  }
  
  .transaction-modal {
    margin: 10px;
    max-width: none;
  }
}