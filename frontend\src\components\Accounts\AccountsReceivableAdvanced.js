import React, { useState, useEffect, useMemo, useRef } from 'react';
import CustomerAgentSelector from '../Finance/CustomerAgentSelector';
import './AccountsReceivable.css';

const AccountsReceivableAdvanced = ({ accounts, transactions, customers, currentUser, customersService, agentsService }) => {
  const [selectedPeriod, setSelectedPeriod] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [ageingPeriod, setAgeingPeriod] = useState('current');
  const [viewMode, setViewMode] = useState('summary');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showFollowUpModal, setShowFollowUpModal] = useState(false);
  const [selectedReceivable, setSelectedReceivable] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: 'dueDate', direction: 'asc' });
  
  const [newReceivable, setNewReceivable] = useState({
    customerId: '',
    customerName: '',
    amount: '',
    dueDate: '',
    invoiceNumber: '',
    description: '',
    terms: '30'
  });

  const [payment, setPayment] = useState({
    receivableId: '',
    amount: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: 'cash',
    reference: '',
    notes: ''
  });

  const [filters, setFilters] = useState({
    status: 'all',
    ageGroup: 'all',
    amountRange: { min: '', max: '' },
    customerType: 'all',
    overdue: false,
    hasPayments: 'all'
  });

  const [printOptions, setPrintOptions] = useState({
    includeDetails: true,
    includeAgeing: true,
    includePayments: true,
    showZeroBalances: false,
    groupByCustomer: true,
    includeNotes: true,
    pageSize: 'A4',
    orientation: 'portrait'
  });

  const [exportFormat, setExportFormat] = useState('csv');

  const printRef = useRef();

  // تحميل الذمم المدينة من بيانات العملاء الحقيقية
  const [receivables, setReceivables] = useState([]);

  // تحميل البيانات من الخدمات
  useEffect(() => {
    if (customersService) {
      // تحويل بيانات العملاء إلى ذمم مدينة
      const customersWithReceivables = customersService.getCustomersWithReceivables();
      const receivablesData = customersWithReceivables.map((customer, index) => ({
        id: index + 1,
        customerId: customer.id,
        customerName: customer.customerName,
        amount: customer.totalAmount || 0,
        paidAmount: customer.paidAmount || 0,
        remainingAmount: customer.remainingAmount || 0,
        invoiceNumber: `INV-${customer.id}`,
        invoiceDate: customer.createdDate,
        dueDate: new Date(new Date(customer.createdDate).getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        description: `خدمات سياحية - ${customer.visaType || 'غير محدد'}`,
        status: customer.remainingAmount > 0 ?
          (new Date() > new Date(new Date(customer.createdDate).getTime() + 30 * 24 * 60 * 60 * 1000) ? 'overdue' : 'current') : 'paid',
        terms: '30',
        lastPaymentDate: customer.paidAmount > 0 ? customer.createdDate : null,
        followUps: []
      }));

      setReceivables(receivablesData);
    }
  }, [customersService, customers]);

  // حساب عمر الذمم
  const calculateAge = (dueDate) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today - due;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // تصنيف الذمم حسب العمر
  const getAgeGroup = (age) => {
    if (age <= 0) return 'current';
    if (age <= 30) return '1-30';
    if (age <= 60) return '31-60';
    if (age <= 90) return '61-90';
    return '90+';
  };

  // تصفية وترتيب الذمم
  const filteredReceivables = useMemo(() => {
    let filtered = receivables.filter(receivable => {
      // تصفية النص
      const matchesSearch = !searchTerm || 
        receivable.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        receivable.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        receivable.description.toLowerCase().includes(searchTerm.toLowerCase());

      // تصفية العميل
      const matchesCustomer = !selectedCustomer || receivable.customerId === selectedCustomer;

      // تصفية الفترة
      const invoiceDate = new Date(receivable.invoiceDate);
      const startDate = new Date(selectedPeriod.startDate);
      const endDate = new Date(selectedPeriod.endDate);
      const matchesPeriod = invoiceDate >= startDate && invoiceDate <= endDate;

      // تصفية الحالة
      const matchesStatus = filters.status === 'all' || receivable.status === filters.status;

      // تصفية عمر الذمة
      const age = calculateAge(receivable.dueDate);
      const ageGroup = getAgeGroup(age);
      const matchesAge = filters.ageGroup === 'all' || ageGroup === filters.ageGroup;

      // تصفية المبلغ
      const matchesAmount = (!filters.amountRange.min || receivable.remainingAmount >= parseFloat(filters.amountRange.min)) &&
                           (!filters.amountRange.max || receivable.remainingAmount <= parseFloat(filters.amountRange.max));

      // تصفية المتأخرة
      const isOverdue = age > 0 && receivable.remainingAmount > 0;
      const matchesOverdue = !filters.overdue || isOverdue;

      // تصفية وجود دفعات
      const hasPayments = receivable.paidAmount > 0;
      const matchesPayments = filters.hasPayments === 'all' || 
                             (filters.hasPayments === 'yes' && hasPayments) ||
                             (filters.hasPayments === 'no' && !hasPayments);

      return matchesSearch && matchesCustomer && matchesPeriod && matchesStatus && 
             matchesAge && matchesAmount && matchesOverdue && matchesPayments;
    });

    // ترتيب النتائج
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        if (sortConfig.key === 'age') {
          aValue = calculateAge(a.dueDate);
          bValue = calculateAge(b.dueDate);
        }

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [receivables, searchTerm, selectedCustomer, selectedPeriod, filters, sortConfig]);

  // حساب الإحصائيات
  const statistics = useMemo(() => {
    const totalAmount = filteredReceivables.reduce((sum, r) => sum + r.amount, 0);
    const totalPaid = filteredReceivables.reduce((sum, r) => sum + r.paidAmount, 0);
    const totalRemaining = filteredReceivables.reduce((sum, r) => sum + r.remainingAmount, 0);
    const overdueAmount = filteredReceivables
      .filter(r => calculateAge(r.dueDate) > 0 && r.remainingAmount > 0)
      .reduce((sum, r) => sum + r.remainingAmount, 0);
    const currentAmount = filteredReceivables
      .filter(r => calculateAge(r.dueDate) <= 0 && r.remainingAmount > 0)
      .reduce((sum, r) => sum + r.remainingAmount, 0);

    return {
      totalInvoices: filteredReceivables.length,
      totalAmount,
      totalPaid,
      totalRemaining,
      overdueAmount,
      currentAmount,
      collectionRate: totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0
    };
  }, [filteredReceivables]);

  // تحليل أعمار الذمم
  const ageingAnalysis = useMemo(() => {
    const analysis = {
      current: 0,
      '1-30': 0,
      '31-60': 0,
      '61-90': 0,
      '90+': 0
    };

    filteredReceivables.forEach(receivable => {
      if (receivable.remainingAmount > 0) {
        const age = calculateAge(receivable.dueDate);
        const ageGroup = getAgeGroup(age);
        analysis[ageGroup] += receivable.remainingAmount;
      }
    });

    return analysis;
  }, [filteredReceivables]);

  // وظائف الترتيب
  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // وظائف إدارة الذمم
  const handleAddReceivable = () => {
    if (!newReceivable.customerName || !newReceivable.amount || !newReceivable.dueDate) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    const receivable = {
      id: Date.now(),
      ...newReceivable,
      customerName: newReceivable.customerName,
      amount: parseFloat(newReceivable.amount),
      paidAmount: 0,
      remainingAmount: parseFloat(newReceivable.amount),
      invoiceDate: new Date().toISOString().split('T')[0],
      status: 'current',
      lastPaymentDate: null,
      followUps: []
    };

    setReceivables(prev => [...prev, receivable]);
    setNewReceivable({
      customerId: '',
      customerName: '',
      amount: '',
      dueDate: '',
      invoiceNumber: '',
      description: '',
      terms: '30'
    });
    setShowAddModal(false);
    alert('تم إضافة الذمة المدينة بنجاح');
  };

  const handlePayment = () => {
    // التحقق من الحقول المطلوبة
    if (!payment.receivableId || !payment.amount || !payment.paymentDate) {
      alert('يرجى ملء جميع الحقول المطلوبة (الذمة المدينة، مبلغ الدفعة، تاريخ الدفعة)');
      return;
    }

    const paymentAmount = parseFloat(payment.amount);
    
    // التحقق من صحة المبلغ
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
      alert('يرجى إدخال مبلغ صحيح أكبر من الصفر');
      return;
    }

    // العثور على الذمة المحددة للتحقق من المبلغ المتبقي
    const selectedReceivable = receivables.find(r => r.id === parseInt(payment.receivableId));
    if (!selectedReceivable) {
      alert('لم يتم العثور على الذمة المحددة');
      return;
    }

    // التحقق من أن مبلغ الدفعة لا يتجاوز المبلغ المتبقي
    if (paymentAmount > selectedReceivable.remainingAmount) {
      alert(`مبلغ الدفعة (${paymentAmount.toLocaleString()}) يتجاوز المبلغ المتبقي (${selectedReceivable.remainingAmount.toLocaleString()})`);
      return;
    }
    
    // تحديث الذمة المدينة
    setReceivables(prev => prev.map(receivable => {
      if (receivable.id === parseInt(payment.receivableId)) {
        const newPaidAmount = receivable.paidAmount + paymentAmount;
        const newRemainingAmount = receivable.amount - newPaidAmount;
        
        return {
          ...receivable,
          paidAmount: newPaidAmount,
          remainingAmount: Math.max(0, newRemainingAmount),
          lastPaymentDate: payment.paymentDate,
          status: newRemainingAmount <= 0 ? 'paid' : receivable.status
        };
      }
      return receivable;
    }));

    // إعادة تعيين النموذج
    setPayment({
      receivableId: '',
      amount: '',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethod: 'cash',
      reference: '',
      notes: ''
    });
    
    setShowPaymentModal(false);
    
    // حساب المبلغ المتبقي الجديد
    const newRemainingAmount = selectedReceivable.remainingAmount - paymentAmount;
    const isFullyPaid = newRemainingAmount <= 0;
    
    alert(`✅ تم تسجيل الدفعة بنجاح!\n\n` +
          `💰 مبلغ الدفعة: ${paymentAmount.toLocaleString()} ريال\n` +
          `👤 العميل: ${selectedReceivable.customerName}\n` +
          `📄 رقم الفاتورة: ${selectedReceivable.invoiceNumber}\n` +
          `💳 طريقة الدفع: ${payment.paymentMethod === 'cash' ? 'نقد' : 
                              payment.paymentMethod === 'bank' ? 'تحويل بنكي' :
                              payment.paymentMethod === 'check' ? 'شيك' :
                              payment.paymentMethod === 'card' ? 'بطاقة ائتمان' : 'أخرى'}\n` +
          `📅 تاريخ الدفعة: ${new Date(payment.paymentDate).toLocaleDateString('ar-SA')}\n\n` +
          `${isFullyPaid ? 
            '🎉 تم سداد الذمة بالكامل!' : 
            `⏳ المبلغ المتبقي: ${newRemainingAmount.toLocaleString()} ريال`}`);
  };

  // وظائف المتابعة
  const handleAddFollowUp = (receivableId, followUpData) => {
    setReceivables(prev => prev.map(receivable => {
      if (receivable.id === receivableId) {
        return {
          ...receivable,
          followUps: [...(receivable.followUps || []), {
            date: new Date().toISOString().split('T')[0],
            ...followUpData
          }]
        };
      }
      return receivable;
    }));
  };

  // وظائف الطباعة والتصدير
  const handlePrint = () => {
    setShowPrintModal(true);
  };

  const executePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير الذمم المدينة</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .report-title { font-size: 18px; color: #666; margin-bottom: 5px; }
          .date-range { font-size: 14px; color: #888; }
          .section { margin: 30px 0; page-break-inside: avoid; }
          .section-title { font-size: 16px; font-weight: bold; background: #f5f5f5; padding: 10px; margin-bottom: 15px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .customer-name { text-align: right; }
          .amount { text-align: left; font-weight: bold; }
          .overdue { color: #e74c3c; }
          .current { color: #27ae60; }
          .paid { color: #3498db; }
          .total-row { background: #f8f9fa; font-weight: bold; border-top: 2px solid #333; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } .page-break { page-break-before: always; } }
        </style>
      </head>
      <body>
        ${printContent}
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
    setShowPrintModal(false);
  };

  const generatePrintContent = () => {
    return `
      <div class="header">
        <div class="company-name">شركة شراء السياحية</div>
        <div class="report-title">تقرير الذمم المدينة</div>
        <div class="date-range">
          للفترة من ${new Date(selectedPeriod.startDate).toLocaleDateString('ar-SA')} 
          إلى ${new Date(selectedPeriod.endDate).toLocaleDateString('ar-SA')}
        </div>
      </div>
      
      ${printOptions.includeDetails ? generateReceivablesTable() : ''}
      ${printOptions.includeAgeing ? generateAgeingAnalysis() : ''}
      
      <div class="footer">
        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
        <p>بواسطة: ${currentUser?.name || 'النظام'}</p>
      </div>
    `;
  };

  const generateReceivablesTable = () => {
    return `
      <div class="section">
        <div class="section-title">تفاصيل الذمم المدينة</div>
        <table>
          <thead>
            <tr>
              <th>العميل</th>
              <th>رقم الفاتورة</th>
              <th>تاريخ الفاتورة</th>
              <th>تاريخ الاستحقاق</th>
              <th>المبلغ الإجمالي</th>
              <th>المبلغ المدفوع</th>
              <th>المبلغ المتبقي</th>
              <th>العمر (يوم)</th>
              <th>الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${filteredReceivables.map(receivable => {
              const age = calculateAge(receivable.dueDate);
              return `
                <tr>
                  <td class="customer-name">${receivable.customerName}</td>
                  <td>${receivable.invoiceNumber}</td>
                  <td>${new Date(receivable.invoiceDate).toLocaleDateString('ar-SA')}</td>
                  <td>${new Date(receivable.dueDate).toLocaleDateString('ar-SA')}</td>
                  <td class="amount">${receivable.amount.toLocaleString()}</td>
                  <td class="amount">${receivable.paidAmount.toLocaleString()}</td>
                  <td class="amount ${receivable.status}">${receivable.remainingAmount.toLocaleString()}</td>
                  <td>${age}</td>
                  <td class="${receivable.status}">
                    ${receivable.status === 'paid' ? 'مدفوع' : 
                      receivable.status === 'overdue' ? 'متأخر' : 'جاري'}
                  </td>
                </tr>
              `;
            }).join('')}
            <tr class="total-row">
              <td colspan="4"><strong>الإجمالي</strong></td>
              <td class="amount"><strong>${statistics.totalAmount.toLocaleString()}</strong></td>
              <td class="amount"><strong>${statistics.totalPaid.toLocaleString()}</strong></td>
              <td class="amount"><strong>${statistics.totalRemaining.toLocaleString()}</strong></td>
              <td colspan="2"></td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const generateAgeingAnalysis = () => {
    return `
      <div class="section">
        <div class="section-title">تحليل أعمار الذمم</div>
        <table>
          <thead>
            <tr>
              <th>الفئة العمرية</th>
              <th>المبلغ</th>
              <th>النسبة %</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>جاري (غير مستحق)</td>
              <td class="amount current">${ageingAnalysis.current.toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis.current / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>1-30 يوم</td>
              <td class="amount">${ageingAnalysis['1-30'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['1-30'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>31-60 يوم</td>
              <td class="amount">${ageingAnalysis['31-60'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['31-60'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>61-90 يوم</td>
              <td class="amount">${ageingAnalysis['61-90'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['61-90'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr>
              <td>أكثر من 90 يوم</td>
              <td class="amount overdue">${ageingAnalysis['90+'].toLocaleString()}</td>
              <td>${statistics.totalRemaining > 0 ? ((ageingAnalysis['90+'] / statistics.totalRemaining) * 100).toFixed(1) : 0}%</td>
            </tr>
            <tr class="total-row">
              <td><strong>الإجمالي</strong></td>
              <td class="amount"><strong>${statistics.totalRemaining.toLocaleString()}</strong></td>
              <td><strong>100%</strong></td>
            </tr>
          </tbody>
        </table>
      </div>
    `;
  };

  const handleExport = () => {
    setShowExportModal(true);
  };

  const executeExport = () => {
    switch (exportFormat) {
      case 'csv':
        exportToCSV();
        break;
      case 'excel':
        exportToExcel();
        break;
      case 'pdf':
        exportToPDF();
        break;
      default:
        exportToCSV();
    }
    setShowExportModal(false);
  };

  const exportToCSV = () => {
    let csvContent = 'العميل,رقم الفاتورة,تاريخ الفاتورة,تاريخ الاستحقاق,المبلغ الإجمالي,المبلغ المدفوع,المبلغ المتبقي,العمر,الحالة,الوصف\n';
    
    filteredReceivables.forEach(receivable => {
      const age = calculateAge(receivable.dueDate);
      const status = receivable.status === 'paid' ? 'مدفوع' : 
                    receivable.status === 'overdue' ? 'متأخر' : 'جاري';
      
      csvContent += `"${receivable.customerName}","${receivable.invoiceNumber}","${receivable.invoiceDate}","${receivable.dueDate}",${receivable.amount},${receivable.paidAmount},${receivable.remainingAmount},${age},"${status}","${receivable.description}"\n`;
    });

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `الذمم-المدينة-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    alert('سيتم تطوير تصدير Excel قريباً');
  };

  const exportToPDF = () => {
    alert('سيتم تطوير تصدير PDF قريباً');
  };

  return (
    <div className="accounts-receivable-advanced">
      <div className="receivables-header">
        <div className="header-content">
          <h2>💰 الذمم المدينة المتقدمة</h2>
          <p>إدارة شاملة للذمم المدينة والمتابعة والتحصيل</p>
        </div>
        
        <div className="header-controls">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddModal(true)}
          >
            ➕ إضافة ذمة
          </button>
          <button 
            className="btn btn-success"
            onClick={() => setShowPaymentModal(true)}
          >
            💳 تسجيل دفعة
          </button>
          <button 
            className="btn btn-info"
            onClick={handlePrint}
          >
            🖨️ طباعة
          </button>
          <button 
            className="btn btn-warning"
            onClick={handleExport}
          >
            📊 تصدير
          </button>
          <button 
            className="btn btn-secondary"
            onClick={() => setShowFilterModal(true)}
          >
            🔍 تصفية متقدمة
          </button>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="receivables-stats">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalInvoices}</div>
            <div className="stat-label">عدد الفواتير</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalAmount.toLocaleString()}</div>
            <div className="stat-label">إجمالي المبلغ</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalPaid.toLocaleString()}</div>
            <div className="stat-label">المبلغ المحصل</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.totalRemaining.toLocaleString()}</div>
            <div className="stat-label">المبلغ المتبقي</div>
          </div>
        </div>
        
        <div className="stat-card overdue">
          <div className="stat-icon">⚠️</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.overdueAmount.toLocaleString()}</div>
            <div className="stat-label">المتأخر</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">📈</div>
          <div className="stat-info">
            <div className="stat-value">{statistics.collectionRate.toFixed(1)}%</div>
            <div className="stat-label">معدل التحصيل</div>
          </div>
        </div>
      </div>

      {/* أدوات التحكم */}
      <div className="receivables-controls">
        <div className="controls-row">
          <div className="search-box">
            <input
              type="text"
              placeholder="البحث في العملاء أو الفواتير..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="date-range">
            <label>الفترة:</label>
            <input
              type="date"
              value={selectedPeriod.startDate}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, startDate: e.target.value }))}
              className="date-input"
            />
            <span>إلى</span>
            <input
              type="date"
              value={selectedPeriod.endDate}
              onChange={(e) => setSelectedPeriod(prev => ({ ...prev, endDate: e.target.value }))}
              className="date-input"
            />
          </div>
          
          <div className="customer-filter">
            <select
              value={selectedCustomer}
              onChange={(e) => setSelectedCustomer(e.target.value)}
              className="form-control"
            >
              <option value="">جميع العملاء</option>
              {customers?.map(customer => (
                <option key={customer.id} value={customer.id}>{customer.name}</option>
              ))}
            </select>
          </div>
          
          <div className="view-modes">
            <button
              className={`view-mode-btn ${viewMode === 'summary' ? 'active' : ''}`}
              onClick={() => setViewMode('summary')}
            >
              📊 ملخص
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'detailed' ? 'active' : ''}`}
              onClick={() => setViewMode('detailed')}
            >
              📋 تفصيلي
            </button>
            <button
              className={`view-mode-btn ${viewMode === 'ageing' ? 'active' : ''}`}
              onClick={() => setViewMode('ageing')}
            >
              📅 تحليل الأعمار
            </button>
          </div>
        </div>
      </div>

      {/* عرض البيانات */}
      <div className="receivables-content">
        {viewMode === 'summary' && (
          <div className="summary-view">
            <div className="table-container">
              <table className="receivables-table">
                <thead>
                  <tr>
                    <th onClick={() => handleSort('customerName')}>
                      العميل {sortConfig.key === 'customerName' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('invoiceNumber')}>
                      رقم الفاتورة {sortConfig.key === 'invoiceNumber' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('dueDate')}>
                      تاريخ الاستحقاق {sortConfig.key === 'dueDate' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('amount')}>
                      المبلغ الإجمالي {sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('remainingAmount')}>
                      المبلغ المتبقي {sortConfig.key === 'remainingAmount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th onClick={() => handleSort('age')}>
                      العمر (يوم) {sortConfig.key === 'age' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                    </th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredReceivables.map(receivable => {
                    const age = calculateAge(receivable.dueDate);
                    return (
                      <tr key={receivable.id} className={receivable.status}>
                        <td className="customer-name">{receivable.customerName}</td>
                        <td>{receivable.invoiceNumber}</td>
                        <td>{new Date(receivable.dueDate).toLocaleDateString('ar-SA')}</td>
                        <td className="amount">{receivable.amount.toLocaleString()}</td>
                        <td className={`amount ${receivable.status}`}>
                          {receivable.remainingAmount.toLocaleString()}
                        </td>
                        <td className={age > 0 ? 'overdue' : 'current'}>{age}</td>
                        <td>
                          <span className={`status-badge ${receivable.status}`}>
                            {receivable.status === 'paid' ? 'مدفوع' : 
                             receivable.status === 'overdue' ? 'متأخر' : 'جاري'}
                          </span>
                        </td>
                        <td className="actions">
                          <button
                            className="action-btn view-btn"
                            onClick={() => {
                              setSelectedReceivable(receivable);
                              setShowDetailsModal(true);
                            }}
                            title="عرض التفاصيل"
                          >
                            👁️
                          </button>
                          {receivable.remainingAmount > 0 && (
                            <>
                              <button
                                className="action-btn payment-btn"
                                onClick={() => {
                                  setPayment(prev => ({ ...prev, receivableId: receivable.id.toString() }));
                                  setShowPaymentModal(true);
                                }}
                                title="تسجيل دفعة"
                              >
                                💳
                              </button>
                              <button
                                className="action-btn follow-btn"
                                onClick={() => {
                                  setSelectedReceivable(receivable);
                                  setShowFollowUpModal(true);
                                }}
                                title="إضافة متابعة"
                              >
                                📞
                              </button>
                            </>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot>
                  <tr className="total-row">
                    <td colSpan="3"><strong>الإجمالي</strong></td>
                    <td className="amount"><strong>{statistics.totalAmount.toLocaleString()}</strong></td>
                    <td className="amount"><strong>{statistics.totalRemaining.toLocaleString()}</strong></td>
                    <td colSpan="3"></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}

        {viewMode === 'detailed' && (
          <div className="detailed-view">
            {filteredReceivables.map(receivable => {
              const age = calculateAge(receivable.dueDate);
              return (
                <div key={receivable.id} className={`receivable-card ${receivable.status}`}>
                  <div className="card-header">
                    <div className="customer-info">
                      <h3>{receivable.customerName}</h3>
                      <span className="invoice-number">{receivable.invoiceNumber}</span>
                    </div>
                    <div className="status-info">
                      <span className={`status-badge ${receivable.status}`}>
                        {receivable.status === 'paid' ? 'مدفوع' : 
                         receivable.status === 'overdue' ? 'متأخر' : 'جاري'}
                      </span>
                      <span className={`age-badge ${age > 0 ? 'overdue' : 'current'}`}>
                        {age} يوم
                      </span>
                    </div>
                  </div>
                  
                  <div className="card-body">
                    <div className="receivable-details">
                      <div className="detail-item">
                        <label>تاريخ الفاتورة:</label>
                        <span>{new Date(receivable.invoiceDate).toLocaleDateString('ar-SA')}</span>
                      </div>
                      <div className="detail-item">
                        <label>تاريخ الاستحقاق:</label>
                        <span>{new Date(receivable.dueDate).toLocaleDateString('ar-SA')}</span>
                      </div>
                      <div className="detail-item">
                        <label>الوصف:</label>
                        <span>{receivable.description}</span>
                      </div>
                      <div className="detail-item">
                        <label>شروط الدفع:</label>
                        <span>{receivable.terms} يوم</span>
                      </div>
                    </div>
                    
                    <div className="amount-details">
                      <div className="amount-item">
                        <label>المبلغ الإجمالي:</label>
                        <span className="amount">{receivable.amount.toLocaleString()}</span>
                      </div>
                      <div className="amount-item">
                        <label>المبلغ المدفوع:</label>
                        <span className="amount paid">{receivable.paidAmount.toLocaleString()}</span>
                      </div>
                      <div className="amount-item">
                        <label>المبلغ المتبقي:</label>
                        <span className={`amount ${receivable.status}`}>
                          {receivable.remainingAmount.toLocaleString()}
                        </span>
                      </div>
                      {receivable.lastPaymentDate && (
                        <div className="amount-item">
                          <label>آخر دفعة:</label>
                          <span>{new Date(receivable.lastPaymentDate).toLocaleDateString('ar-SA')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="card-footer">
                    <div className="follow-ups">
                      {receivable.followUps && receivable.followUps.length > 0 && (
                        <div className="follow-up-summary">
                          <span>آخر متابعة: {new Date(receivable.followUps[receivable.followUps.length - 1].date).toLocaleDateString('ar-SA')}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="card-actions">
                      <button
                        className="btn btn-sm btn-info"
                        onClick={() => {
                          setSelectedReceivable(receivable);
                          setShowDetailsModal(true);
                        }}
                      >
                        👁️ التفاصيل
                      </button>
                      {receivable.remainingAmount > 0 && (
                        <>
                          <button
                            className="btn btn-sm btn-success"
                            onClick={() => {
                              setPayment(prev => ({ ...prev, receivableId: receivable.id.toString() }));
                              setShowPaymentModal(true);
                            }}
                          >
                            💳 دفعة
                          </button>
                          <button
                            className="btn btn-sm btn-warning"
                            onClick={() => {
                              setSelectedReceivable(receivable);
                              setShowFollowUpModal(true);
                            }}
                          >
                            📞 متابعة
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {viewMode === 'ageing' && (
          <div className="ageing-view">
            <div className="ageing-summary">
              <h3>تحليل أعمار الذمم المدينة</h3>
              <div className="ageing-chart">
                <div className="ageing-bars">
                  {Object.entries(ageingAnalysis).map(([ageGroup, amount]) => {
                    const percentage = statistics.totalRemaining > 0 ? (amount / statistics.totalRemaining) * 100 : 0;
                    return (
                      <div key={ageGroup} className="ageing-bar">
                        <div className="bar-header">
                          <span className="age-label">
                            {ageGroup === 'current' ? 'جاري (غير مستحق)' :
                             ageGroup === '1-30' ? '1-30 يوم' :
                             ageGroup === '31-60' ? '31-60 يوم' :
                             ageGroup === '61-90' ? '61-90 يوم' :
                             'أكثر من 90 يوم'}
                          </span>
                          <span className="amount">{amount.toLocaleString()}</span>
                        </div>
                        <div className="bar-container">
                          <div 
                            className={`bar-fill ${ageGroup === 'current' ? 'current' : 
                                                  ageGroup === '90+' ? 'critical' : 'warning'}`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <div className="bar-footer">
                          <span className="percentage">{percentage.toFixed(1)}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            
            <div className="ageing-table">
              <table className="receivables-table">
                <thead>
                  <tr>
                    <th>الفئة العمرية</th>
                    <th>عدد الفواتير</th>
                    <th>المبلغ</th>
                    <th>النسبة %</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(ageingAnalysis).map(([ageGroup, amount]) => {
                    const count = filteredReceivables.filter(r => {
                      const age = calculateAge(r.dueDate);
                      return getAgeGroup(age) === ageGroup && r.remainingAmount > 0;
                    }).length;
                    const percentage = statistics.totalRemaining > 0 ? (amount / statistics.totalRemaining) * 100 : 0;
                    
                    return (
                      <tr key={ageGroup} className={ageGroup === 'current' ? 'current' : 
                                                   ageGroup === '90+' ? 'critical' : 'warning'}>
                        <td>
                          {ageGroup === 'current' ? 'جاري (غير مستحق)' :
                           ageGroup === '1-30' ? '1-30 يوم' :
                           ageGroup === '31-60' ? '31-60 يوم' :
                           ageGroup === '61-90' ? '61-90 يوم' :
                           'أكثر من 90 يوم'}
                        </td>
                        <td>{count}</td>
                        <td className="amount">{amount.toLocaleString()}</td>
                        <td>{percentage.toFixed(1)}%</td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot>
                  <tr className="total-row">
                    <td><strong>الإجمالي</strong></td>
                    <td><strong>{filteredReceivables.filter(r => r.remainingAmount > 0).length}</strong></td>
                    <td className="amount"><strong>{statistics.totalRemaining.toLocaleString()}</strong></td>
                    <td><strong>100%</strong></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* النماذج المنبثقة */}
      
      {/* نموذج إضافة ذمة مدينة */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="add-modal">
            <div className="modal-header">
              <h3>➕ إضافة ذمة مدينة جديدة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowAddModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>العميل أو الوكيل *</label>
                  <CustomerAgentSelector
                    value={newReceivable.customerName || ''}
                    onChange={(e) => {
                      setNewReceivable(prev => ({
                        ...prev,
                        customerName: e.target.value,
                        customerId: e.target.value // سيتم تحديثه في onSelect
                      }));
                    }}
                    onSelect={(selectedOption) => {
                      setNewReceivable(prev => ({
                        ...prev,
                        customerId: selectedOption.id,
                        customerName: selectedOption.name
                      }));
                    }}
                    placeholder="اكتب اسم العميل أو الوكيل أو اختر من القائمة..."
                  />
                </div>

                <div className="form-group">
                  <label>رقم الفاتورة</label>
                  <input
                    type="text"
                    value={newReceivable.invoiceNumber}
                    onChange={(e) => setNewReceivable(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                    className="form-control"
                    placeholder="INV-2024-001"
                  />
                </div>

                <div className="form-group">
                  <label>المبلغ *</label>
                  <input
                    type="number"
                    value={newReceivable.amount}
                    onChange={(e) => setNewReceivable(prev => ({ ...prev, amount: e.target.value }))}
                    className="form-control"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>تاريخ الاستحقاق *</label>
                  <input
                    type="date"
                    value={newReceivable.dueDate}
                    onChange={(e) => setNewReceivable(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="form-control"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>شروط الدفع (يوم)</label>
                  <select
                    value={newReceivable.terms}
                    onChange={(e) => setNewReceivable(prev => ({ ...prev, terms: e.target.value }))}
                    className="form-control"
                  >
                    <option value="0">فوري</option>
                    <option value="15">15 يوم</option>
                    <option value="30">30 يوم</option>
                    <option value="45">45 يوم</option>
                    <option value="60">60 يوم</option>
                    <option value="90">90 يوم</option>
                  </select>
                </div>

                <div className="form-group full-width">
                  <label>الوصف</label>
                  <textarea
                    value={newReceivable.description}
                    onChange={(e) => setNewReceivable(prev => ({ ...prev, description: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="وصف الخدمة أو المنتج..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowAddModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleAddReceivable}
              >
                ➕ إضافة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تسجيل دفعة */}
      {showPaymentModal && (
        <div className="modal-overlay">
          <div className="payment-modal">
            <div className="modal-header">
              <h3>💳 تسجيل دفعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPaymentModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="form-grid">
                <div className="form-group">
                  <label>الذمة المدينة *</label>
                  <select
                    value={payment.receivableId}
                    onChange={(e) => {
                      setPayment(prev => ({ ...prev, receivableId: e.target.value, amount: '' }));
                    }}
                    className="form-control"
                    required
                  >
                    <option value="">اختر الذمة</option>
                    {filteredReceivables.filter(r => r.remainingAmount > 0).map(receivable => (
                      <option key={receivable.id} value={receivable.id}>
                        {receivable.customerName} - {receivable.invoiceNumber} - المتبقي: {receivable.remainingAmount.toLocaleString()} ريال
                      </option>
                    ))}
                  </select>
                </div>

                {payment.receivableId && (
                  <div className="form-group full-width">
                    <label>معلومات الذمة المحددة</label>
                    <div className="selected-receivable-info">
                      {(() => {
                        const selected = filteredReceivables.find(r => r.id === parseInt(payment.receivableId));
                        return selected ? (
                          <div className="receivable-summary">
                            <div className="info-item">
                              <span className="label">العميل:</span>
                              <span className="value">{selected.customerName}</span>
                            </div>
                            <div className="info-item">
                              <span className="label">رقم الفاتورة:</span>
                              <span className="value">{selected.invoiceNumber}</span>
                            </div>
                            <div className="info-item">
                              <span className="label">المبلغ الإجمالي:</span>
                              <span className="value">{selected.amount.toLocaleString()} ريال</span>
                            </div>
                            <div className="info-item">
                              <span className="label">المبلغ المدفوع:</span>
                              <span className="value paid">{selected.paidAmount.toLocaleString()} ريال</span>
                            </div>
                            <div className="info-item">
                              <span className="label">المبلغ المتبقي:</span>
                              <span className="value remaining">{selected.remainingAmount.toLocaleString()} ريال</span>
                            </div>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  </div>
                )}

                <div className="form-group">
                  <label>مبلغ الدفعة *</label>
                  <div className="amount-input-container">
                    <input
                      type="number"
                      value={payment.amount}
                      onChange={(e) => setPayment(prev => ({ ...prev, amount: e.target.value }))}
                      className="form-control"
                      placeholder="0.00"
                      min="0"
                      max={payment.receivableId ? 
                        filteredReceivables.find(r => r.id === parseInt(payment.receivableId))?.remainingAmount || 0 
                        : undefined}
                      step="0.01"
                      required
                    />
                    {payment.receivableId && (
                      <button
                        type="button"
                        className="btn btn-outline btn-sm full-payment-btn"
                        onClick={() => {
                          const selected = filteredReceivables.find(r => r.id === parseInt(payment.receivableId));
                          if (selected) {
                            setPayment(prev => ({ ...prev, amount: selected.remainingAmount.toString() }));
                          }
                        }}
                        title="دفع المبلغ المتبقي بالكامل"
                      >
                        💯 دفع كامل
                      </button>
                    )}
                  </div>
                  {payment.receivableId && payment.amount && (
                    <div className="payment-validation">
                      {(() => {
                        const selected = filteredReceivables.find(r => r.id === parseInt(payment.receivableId));
                        const amount = parseFloat(payment.amount);
                        if (selected && amount > selected.remainingAmount) {
                          return (
                            <div className="validation-error">
                              ⚠️ المبلغ يتجاوز المبلغ المتبقي ({selected.remainingAmount.toLocaleString()} ريال)
                            </div>
                          );
                        } else if (selected && amount > 0) {
                          const remaining = selected.remainingAmount - amount;
                          return (
                            <div className="validation-success">
                              ✅ سيصبح المبلغ المتبقي: {remaining.toLocaleString()} ريال
                              {remaining === 0 && <span className="full-payment-indicator"> - دفع كامل 🎉</span>}
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label>تاريخ الدفعة *</label>
                  <input
                    type="date"
                    value={payment.paymentDate}
                    onChange={(e) => setPayment(prev => ({ ...prev, paymentDate: e.target.value }))}
                    className="form-control"
                    required
                  />
                </div>

                <div className="form-group">
                  <label>طريقة الدفع</label>
                  <select
                    value={payment.paymentMethod}
                    onChange={(e) => setPayment(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="form-control"
                  >
                    <option value="cash">نقد</option>
                    <option value="bank">تحويل بنكي</option>
                    <option value="check">شيك</option>
                    <option value="card">بطاقة ائتمان</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>المرجع</label>
                  <input
                    type="text"
                    value={payment.reference}
                    onChange={(e) => setPayment(prev => ({ ...prev, reference: e.target.value }))}
                    className="form-control"
                    placeholder="رقم الشيك أو التحويل..."
                  />
                </div>

                <div className="form-group full-width">
                  <label>ملاحظات</label>
                  <textarea
                    value={payment.notes}
                    onChange={(e) => setPayment(prev => ({ ...prev, notes: e.target.value }))}
                    className="form-control"
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPaymentModal(false)}
              >
                إلغاء
              </button>
              <button 
                className={`btn btn-success ${
                  !payment.receivableId || 
                  !payment.amount || 
                  !payment.paymentDate ||
                  parseFloat(payment.amount) <= 0 ||
                  (payment.receivableId && parseFloat(payment.amount) > 
                    (filteredReceivables.find(r => r.id === parseInt(payment.receivableId))?.remainingAmount || 0))
                  ? 'disabled' : ''
                }`}
                onClick={handlePayment}
                disabled={
                  !payment.receivableId || 
                  !payment.amount || 
                  !payment.paymentDate ||
                  parseFloat(payment.amount) <= 0 ||
                  (payment.receivableId && parseFloat(payment.amount) > 
                    (filteredReceivables.find(r => r.id === parseInt(payment.receivableId))?.remainingAmount || 0))
                }
              >
                💳 تسجيل الدفعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج الطباعة */}
      {showPrintModal && (
        <div className="modal-overlay">
          <div className="print-modal">
            <div className="modal-header">
              <h3>🖨️ خيارات الطباعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowPrintModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="print-options">
                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeDetails}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeDetails: e.target.checked }))}
                    />
                    تضمين تفاصيل الذمم
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includeAgeing}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includeAgeing: e.target.checked }))}
                    />
                    تضمين تحليل الأعمار
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.includePayments}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, includePayments: e.target.checked }))}
                    />
                    تضمين تاريخ الدفعات
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.showZeroBalances}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, showZeroBalances: e.target.checked }))}
                    />
                    إظهار الأرصدة المدفوعة
                  </label>
                </div>

                <div className="option-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={printOptions.groupByCustomer}
                      onChange={(e) => setPrintOptions(prev => ({ ...prev, groupByCustomer: e.target.checked }))}
                    />
                    التجميع حسب العميل
                  </label>
                </div>

                <div className="option-group">
                  <label>حجم الصفحة:</label>
                  <select
                    value={printOptions.pageSize}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, pageSize: e.target.value }))}
                    className="form-control"
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                  </select>
                </div>

                <div className="option-group">
                  <label>اتجاه الصفحة:</label>
                  <select
                    value={printOptions.orientation}
                    onChange={(e) => setPrintOptions(prev => ({ ...prev, orientation: e.target.value }))}
                    className="form-control"
                  >
                    <option value="portrait">عمودي</option>
                    <option value="landscape">أفقي</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={executePrint}
              >
                🖨️ طباعة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصدير */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="export-modal">
            <div className="modal-header">
              <h3>📊 تصدير البيانات</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="export-options">
                <div className="format-selection">
                  <h4>اختر صيغة التصدير:</h4>
                  <div className="format-options">
                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="csv"
                        checked={exportFormat === 'csv'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📄</span>
                        <div>
                          <div className="format-name">CSV</div>
                          <div className="format-desc">ملف نصي مفصول بفواصل</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="excel"
                        checked={exportFormat === 'excel'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📊</span>
                        <div>
                          <div className="format-name">Excel</div>
                          <div className="format-desc">جدول بيانات Excel</div>
                        </div>
                      </div>
                    </label>

                    <label className="format-option">
                      <input
                        type="radio"
                        name="exportFormat"
                        value="pdf"
                        checked={exportFormat === 'pdf'}
                        onChange={(e) => setExportFormat(e.target.value)}
                      />
                      <div className="format-info">
                        <span className="format-icon">📋</span>
                        <div>
                          <div className="format-name">PDF</div>
                          <div className="format-desc">مستند PDF</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="export-summary">
                  <h4>ملخص التصدير:</h4>
                  <div className="summary-items">
                    <div className="summary-item">
                      <span className="label">عدد الذمم:</span>
                      <span className="value">{filteredReceivables.length}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">إجمالي المبلغ:</span>
                      <span className="value">{statistics.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">المبلغ المتبقي:</span>
                      <span className="value">{statistics.totalRemaining.toLocaleString()}</span>
                    </div>
                    <div className="summary-item">
                      <span className="label">المبلغ المتأخر:</span>
                      <span className="value overdue">{statistics.overdueAmount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-success"
                onClick={executeExport}
              >
                📥 تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج التصفية المتقدمة */}
      {showFilterModal && (
        <div className="modal-overlay">
          <div className="filter-modal">
            <div className="modal-header">
              <h3>🔍 تصفية متقدمة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowFilterModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="filter-form">
                <div className="filter-group">
                  <label>حالة الذمة:</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="current">جاري</option>
                    <option value="overdue">متأخر</option>
                    <option value="paid">مدفوع</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>الفئة العمرية:</label>
                  <select
                    value={filters.ageGroup}
                    onChange={(e) => setFilters(prev => ({ ...prev, ageGroup: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">جميع الأعمار</option>
                    <option value="current">جاري (غير مستحق)</option>
                    <option value="1-30">1-30 يوم</option>
                    <option value="31-60">31-60 يوم</option>
                    <option value="61-90">61-90 يوم</option>
                    <option value="90+">أكثر من 90 يوم</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>نطاق المبلغ:</label>
                  <div className="range-inputs">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.amountRange.min}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        amountRange: { ...prev.amountRange, min: e.target.value }
                      }))}
                      className="form-control"
                    />
                    <span>إلى</span>
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.amountRange.max}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        amountRange: { ...prev.amountRange, max: e.target.value }
                      }))}
                      className="form-control"
                    />
                  </div>
                </div>

                <div className="filter-group">
                  <label>وجود دفعات:</label>
                  <select
                    value={filters.hasPayments}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasPayments: e.target.value }))}
                    className="form-control"
                  >
                    <option value="all">الكل</option>
                    <option value="yes">يوجد دفعات</option>
                    <option value="no">لا يوجد دفعات</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={filters.overdue}
                      onChange={(e) => setFilters(prev => ({ ...prev, overdue: e.target.checked }))}
                    />
                    المتأخرة فقط
                  </label>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  setFilters({
                    status: 'all',
                    ageGroup: 'all',
                    amountRange: { min: '', max: '' },
                    customerType: 'all',
                    overdue: false,
                    hasPayments: 'all'
                  });
                }}
              >
                🔄 إعادة تعيين
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => setShowFilterModal(false)}
              >
                ✅ تطبيق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تفاصيل الذمة */}
      {showDetailsModal && selectedReceivable && (
        <div className="modal-overlay">
          <div className="details-modal">
            <div className="modal-header">
              <h3>👁️ تفاصيل الذمة المدينة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowDetailsModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="receivable-details">
                <div className="details-header">
                  <div className="detail-item">
                    <label>العميل:</label>
                    <span>{selectedReceivable.customerName}</span>
                  </div>
                  <div className="detail-item">
                    <label>رقم الفاتورة:</label>
                    <span>{selectedReceivable.invoiceNumber}</span>
                  </div>
                  <div className="detail-item">
                    <label>تاريخ الفاتورة:</label>
                    <span>{new Date(selectedReceivable.invoiceDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>تاريخ الاستحقاق:</label>
                    <span>{new Date(selectedReceivable.dueDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="detail-item">
                    <label>شروط الدفع:</label>
                    <span>{selectedReceivable.terms} يوم</span>
                  </div>
                  <div className="detail-item">
                    <label>العمر:</label>
                    <span className={calculateAge(selectedReceivable.dueDate) > 0 ? 'overdue' : 'current'}>
                      {calculateAge(selectedReceivable.dueDate)} يوم
                    </span>
                  </div>
                </div>

                <div className="amount-summary">
                  <h4>ملخص المبالغ:</h4>
                  <div className="amount-grid">
                    <div className="amount-item">
                      <label>المبلغ الإجمالي:</label>
                      <span className="amount">{selectedReceivable.amount.toLocaleString()}</span>
                    </div>
                    <div className="amount-item">
                      <label>المبلغ المدفوع:</label>
                      <span className="amount paid">{selectedReceivable.paidAmount.toLocaleString()}</span>
                    </div>
                    <div className="amount-item">
                      <label>المبلغ المتبقي:</label>
                      <span className={`amount ${selectedReceivable.status}`}>
                        {selectedReceivable.remainingAmount.toLocaleString()}
                      </span>
                    </div>
                    <div className="amount-item">
                      <label>نسبة التحصيل:</label>
                      <span className="amount">
                        {selectedReceivable.amount > 0 ? 
                          ((selectedReceivable.paidAmount / selectedReceivable.amount) * 100).toFixed(1) : 0}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="description-section">
                  <h4>الوصف:</h4>
                  <p>{selectedReceivable.description}</p>
                </div>

                {selectedReceivable.followUps && selectedReceivable.followUps.length > 0 && (
                  <div className="follow-ups-section">
                    <h4>سجل المتابعة:</h4>
                    <div className="follow-ups-list">
                      {selectedReceivable.followUps.map((followUp, index) => (
                        <div key={index} className="follow-up-item">
                          <div className="follow-up-header">
                            <span className="follow-up-date">
                              {new Date(followUp.date).toLocaleDateString('ar-SA')}
                            </span>
                            <span className={`follow-up-type ${followUp.type}`}>
                              {followUp.type === 'call' ? '📞 مكالمة' :
                               followUp.type === 'email' ? '📧 بريد إلكتروني' :
                               followUp.type === 'visit' ? '🏢 زيارة' : '📝 أخرى'}
                            </span>
                          </div>
                          <div className="follow-up-notes">
                            {followUp.notes}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDetailsModal(false)}
              >
                إغلاق
              </button>
              {selectedReceivable.remainingAmount > 0 && (
                <>
                  <button 
                    className="btn btn-success"
                    onClick={() => {
                      setPayment(prev => ({ ...prev, receivableId: selectedReceivable.id.toString() }));
                      setShowDetailsModal(false);
                      setShowPaymentModal(true);
                    }}
                  >
                    💳 تسجيل دفعة
                  </button>
                  <button 
                    className="btn btn-warning"
                    onClick={() => {
                      setShowDetailsModal(false);
                      setShowFollowUpModal(true);
                    }}
                  >
                    📞 إضافة متابعة
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* نموذج إضافة متابعة */}
      {showFollowUpModal && selectedReceivable && (
        <div className="modal-overlay">
          <div className="follow-up-modal">
            <div className="modal-header">
              <h3>📞 إضافة متابعة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowFollowUpModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-content">
              <div className="follow-up-form">
                <div className="customer-info">
                  <h4>{selectedReceivable.customerName}</h4>
                  <p>فاتورة: {selectedReceivable.invoiceNumber} - المبلغ المتبقي: {selectedReceivable.remainingAmount.toLocaleString()}</p>
                </div>

                <div className="form-group">
                  <label>نوع المتابعة:</label>
                  <select className="form-control" id="followUpType">
                    <option value="call">📞 مكالمة هاتفية</option>
                    <option value="email">📧 بريد إلكتروني</option>
                    <option value="visit">🏢 زيارة شخصية</option>
                    <option value="sms">📱 رسالة نصية</option>
                    <option value="letter">📄 خطاب رسمي</option>
                    <option value="other">📝 أخرى</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>ملاحظات المتابعة:</label>
                  <textarea
                    className="form-control"
                    rows="4"
                    placeholder="اكتب تفاصيل المتابعة..."
                    id="followUpNotes"
                  />
                </div>

                <div className="form-group">
                  <label>تاريخ المتابعة التالية:</label>
                  <input
                    type="date"
                    className="form-control"
                    id="nextFollowUpDate"
                  />
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowFollowUpModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={() => {
                  const type = document.getElementById('followUpType').value;
                  const notes = document.getElementById('followUpNotes').value;
                  const nextDate = document.getElementById('nextFollowUpDate').value;
                  
                  if (notes.trim()) {
                    handleAddFollowUp(selectedReceivable.id, { type, notes, nextDate });
                    setShowFollowUpModal(false);
                    alert('تم إضافة المتابعة بنجاح');
                  } else {
                    alert('يرجى إدخال ملاحظات المتابعة');
                  }
                }}
              >
                📞 إضافة المتابعة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountsReceivableAdvanced;