"""
مخططات المستخدمين
User Schemas
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr

class UserBase(BaseModel):
    """المخطط الأساسي للمستخدم"""
    username: str
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    """مخطط إنشاء مستخدم"""
    password: str

class UserUpdate(BaseModel):
    """مخطط تحديث مستخدم"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None

class UserResponse(UserBase):
    """مخطط استجابة المستخدم"""
    id: int
    is_verified: bool
    is_superuser: bool
    last_login: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True