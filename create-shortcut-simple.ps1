# سكربت إنشاء أيقونة سطح المكتب المبسط
# Simple Desktop Shortcut Creator

$PROJECT_PATH = "c:\Users\<USER>\Desktop\sharaubtravelsoft"
$DESKTOP_PATH = [Environment]::GetFolderPath("Desktop")
$SHORTCUT_PATH = "$DESKTOP_PATH\Sharau Travel System.lnk"
$BATCH_PATH = "$PROJECT_PATH\start-app.bat"

Write-Host "🔄 إنشاء أيقونة سطح المكتب..." -ForegroundColor Yellow

# حذف الاختصار القديم إذا كان موجوداً
if (Test-Path $SHORTCUT_PATH) {
    Remove-Item $SHORTCUT_PATH -Force
}

try {
    $WshShell = New-Object -ComObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($SHORTCUT_PATH)
    $Shortcut.TargetPath = $BATCH_PATH
    $Shortcut.WorkingDirectory = $PROJECT_PATH
    $Shortcut.Description = "Sharau Travel & Tourism System"
    $Shortcut.Save()
    
    Write-Host "✅ تم إنشاء الأيقونة بنجاح!" -ForegroundColor Green
    Write-Host "📍 الموقع: $SHORTCUT_PATH" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ فشل في إنشاء الأيقونة: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 لتشغيل النظام:" -ForegroundColor Cyan
Write-Host "   • انقر نقراً مزدوجاً على أيقونة 'Sharau Travel System'" -ForegroundColor White
Write-Host "   • أو شغل الملف: start-app.bat" -ForegroundColor White
Write-Host ""

Read-Host "اضغط Enter للخروج"