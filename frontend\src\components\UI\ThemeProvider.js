import React, { createContext, useContext, useEffect, useState } from 'react';
import './ThemeSelector.css';

// 🎨 سياق الثيم
const ThemeContext = createContext();

// 🌈 الثيمات المتاحة
const THEMES = {
  light: {
    id: 'light',
    name: 'الوضع النهاري',
    icon: '☀️',
    description: 'تصميم مشرق ونظيف',
    colors: {
      primary: '#3b82f6',
      secondary: '#10b981',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
    }
  },
  dark: {
    id: 'dark',
    name: 'الوضع الليلي',
    icon: '🌙',
    description: 'تصميم مظلم مريح للعين',
    colors: {
      primary: '#6366f1',
      secondary: '#34d399',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
    }
  },
  ocean: {
    id: 'ocean',
    name: 'ثيم المحيط',
    icon: '🌊',
    description: 'ألوان زرقاء هادئة',
    colors: {
      primary: '#0ea5e9',
      secondary: '#06b6d4',
      background: '#f0f9ff',
      surface: '#e0f2fe',
      text: '#0c4a6e',
    }
  },
  sunset: {
    id: 'sunset',
    name: 'ثيم الغروب',
    icon: '🌅',
    description: 'ألوان دافئة وجذابة',
    colors: {
      primary: '#f59e0b',
      secondary: '#ef4444',
      background: '#fffbeb',
      surface: '#fef3c7',
      text: '#92400e',
    }
  },
  forest: {
    id: 'forest',
    name: 'ثيم الغابة',
    icon: '🌲',
    description: 'ألوان خضراء طبيعية',
    colors: {
      primary: '#059669',
      secondary: '#65a30d',
      background: '#f0fdf4',
      surface: '#dcfce7',
      text: '#14532d',
    }
  },
  royal: {
    id: 'royal',
    name: 'ثيم ملكي',
    icon: '👑',
    description: 'ألوان أرجوانية فاخرة',
    colors: {
      primary: '#7c3aed',
      secondary: '#a855f7',
      background: '#faf5ff',
      surface: '#f3e8ff',
      text: '#581c87',
    }
  }
};

// 🎯 مزود الثيم
export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [isAutoMode, setIsAutoMode] = useState(false);
  const [customSettings, setCustomSettings] = useState({
    fontSize: 'medium',
    borderRadius: 'medium',
    animations: true,
    reducedMotion: false,
  });

  // 📱 تحديد الوضع التلقائي حسب وقت النظام
  useEffect(() => {
    if (isAutoMode) {
      const hour = new Date().getHours();
      const shouldBeDark = hour < 6 || hour > 18;
      setCurrentTheme(shouldBeDark ? 'dark' : 'light');
    }
  }, [isAutoMode]);

  // 💾 حفظ الإعدادات في localStorage
  useEffect(() => {
    localStorage.setItem('theme', currentTheme);
    localStorage.setItem('isAutoMode', JSON.stringify(isAutoMode));
    localStorage.setItem('customSettings', JSON.stringify(customSettings));
  }, [currentTheme, isAutoMode, customSettings]);

  // 📖 تحميل الإعدادات من localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    const savedAutoMode = localStorage.getItem('isAutoMode');
    const savedSettings = localStorage.getItem('customSettings');

    if (savedTheme && THEMES[savedTheme]) {
      setCurrentTheme(savedTheme);
    }

    if (savedAutoMode) {
      setIsAutoMode(JSON.parse(savedAutoMode));
    }

    if (savedSettings) {
      setCustomSettings(JSON.parse(savedSettings));
    }
  }, []);

  // 🎨 تطبيق الثيم على الصفحة
  useEffect(() => {
    const root = document.documentElement;
    const theme = THEMES[currentTheme];

    // تطبيق الثيم الأساسي
    root.setAttribute('data-theme', currentTheme);
    
    // تطبيق الألوان المخصصة
    if (theme) {
      Object.entries(theme.colors).forEach(([key, value]) => {
        root.style.setProperty(`--theme-${key}`, value);
      });
    }

    // تطبيق الإعدادات المخصصة
    root.style.setProperty('--custom-font-size', getFontSizeValue(customSettings.fontSize));
    root.style.setProperty('--custom-border-radius', getBorderRadiusValue(customSettings.borderRadius));
    
    if (customSettings.reducedMotion) {
      root.style.setProperty('--transition-fast', '0ms');
      root.style.setProperty('--transition-normal', '0ms');
      root.style.setProperty('--transition-slow', '0ms');
    }

    // إضافة/إزالة فئة CSS للحركات
    if (customSettings.animations) {
      root.classList.add('animations-enabled');
    } else {
      root.classList.remove('animations-enabled');
    }

  }, [currentTheme, customSettings]);

  // 🔧 دوال مساعدة
  const getFontSizeValue = (size) => {
    const sizes = {
      small: '14px',
      medium: '16px',
      large: '18px',
      xlarge: '20px'
    };
    return sizes[size] || sizes.medium;
  };

  const getBorderRadiusValue = (radius) => {
    const radii = {
      none: '0px',
      small: '4px',
      medium: '8px',
      large: '12px',
      xlarge: '16px'
    };
    return radii[radius] || radii.medium;
  };

  // 🎯 دوال التحكم في الثيم
  const changeTheme = (themeId) => {
    if (THEMES[themeId]) {
      setCurrentTheme(themeId);
      setIsAutoMode(false);
    }
  };

  const toggleAutoMode = () => {
    setIsAutoMode(!isAutoMode);
  };

  const updateCustomSettings = (newSettings) => {
    setCustomSettings(prev => ({ ...prev, ...newSettings }));
  };

  const resetToDefaults = () => {
    setCurrentTheme('light');
    setIsAutoMode(false);
    setCustomSettings({
      fontSize: 'medium',
      borderRadius: 'medium',
      animations: true,
      reducedMotion: false,
    });
  };

  // 📊 معلومات الثيم الحالي
  const themeInfo = {
    current: THEMES[currentTheme],
    available: THEMES,
    isAutoMode,
    customSettings,
    actions: {
      changeTheme,
      toggleAutoMode,
      updateCustomSettings,
      resetToDefaults,
    }
  };

  return (
    <ThemeContext.Provider value={themeInfo}>
      {children}
    </ThemeContext.Provider>
  );
};

// 🎣 هوك لاستخدام الثيم
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// 🎨 مكون اختيار الثيم
export const ThemeSelector = ({ className = '' }) => {
  const { current, available, isAutoMode, actions } = useTheme();

  return (
    <div className={`theme-selector ${className}`}>
      <div className="theme-selector-header">
        <h3>🎨 اختيار الثيم</h3>
        <div className="auto-mode-toggle">
          <label>
            <input
              type="checkbox"
              checked={isAutoMode}
              onChange={actions.toggleAutoMode}
            />
            <span>الوضع التلقائي</span>
          </label>
        </div>
      </div>

      <div className="theme-grid">
        {Object.values(available).map((theme) => (
          <button
            key={theme.id}
            className={`theme-option ${current.id === theme.id ? 'active' : ''}`}
            onClick={() => actions.changeTheme(theme.id)}
            disabled={isAutoMode}
          >
            <div className="theme-icon">{theme.icon}</div>
            <div className="theme-name">{theme.name}</div>
            <div className="theme-description">{theme.description}</div>
            <div className="theme-colors">
              {Object.values(theme.colors).slice(0, 3).map((color, index) => (
                <div
                  key={index}
                  className="color-dot"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ThemeProvider;
