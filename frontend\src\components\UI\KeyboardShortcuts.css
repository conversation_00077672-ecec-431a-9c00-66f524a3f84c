/* ⌨️ أنماط اختصارات لوحة المفاتيح */

/* 📚 نافذة مساعدة الاختصارات */
.shortcuts-help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.shortcuts-help-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: zoomIn 0.3s ease-out;
}

/* 🎯 رأس النافذة */
.shortcuts-help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

.shortcuts-help-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.shortcuts-help-close {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
  border-radius: var(--radius-full);
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.shortcuts-help-close:hover {
  background: var(--danger-500);
  color: white;
  transform: scale(1.1);
}

/* 📄 محتوى النافذة */
.shortcuts-help-content {
  padding: var(--space-6);
  max-height: 60vh;
  overflow-y: auto;
  display: grid;
  gap: var(--space-6);
}

/* 🏷️ فئة الاختصارات */
.shortcuts-category {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  transition: all var(--transition-fast);
}

.shortcuts-category:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.shortcuts-category-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--glass-border);
}

.shortcuts-category-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.shortcuts-category-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-800);
}

/* 📋 قائمة الاختصارات */
.shortcuts-list {
  display: grid;
  gap: var(--space-3);
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.shortcut-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-4px);
}

/* ⌨️ مفاتيح الاختصار */
.shortcut-keys {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.shortcut-key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  padding: 0 var(--space-2);
  background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--neutral-700);
  text-transform: uppercase;
  box-shadow: 
    0 1px 0 rgba(255, 255, 255, 0.8) inset,
    0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-fast);
}

.shortcut-key:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 1px 0 rgba(255, 255, 255, 0.8) inset,
    0 2px 6px rgba(0, 0, 0, 0.15);
}

.shortcut-plus {
  font-size: 0.75rem;
  color: var(--neutral-500);
  font-weight: 600;
  margin: 0 var(--space-1);
}

/* 📝 وصف الاختصار */
.shortcut-description {
  font-size: 0.875rem;
  color: var(--neutral-600);
  font-weight: 500;
  text-align: left;
}

/* 🔗 تذييل النافذة */
.shortcuts-help-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--glass-border);
  background: rgba(255, 255, 255, 0.03);
  text-align: center;
}

.shortcuts-help-footer p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--neutral-500);
}

.shortcuts-help-footer kbd {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 var(--space-1);
  background: var(--neutral-200);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.625rem;
  font-weight: 600;
  color: var(--neutral-700);
  margin: 0 2px;
}

/* 🎯 عرض الاختصار */
.shortcut-display {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.shortcut-display .shortcut-key {
  min-width: 24px;
  height: 24px;
  font-size: 0.625rem;
}

.shortcut-display .shortcut-plus {
  font-size: 0.625rem;
  margin: 0 2px;
}

/* 🔘 زر مع اختصار */
.button-shortcut {
  margin-left: var(--space-2);
  opacity: 0.7;
}

.button-shortcut .shortcut-key {
  min-width: 20px;
  height: 20px;
  font-size: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: currentColor;
}

/* 📊 إحصائيات الاختصارات */
.shortcuts-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.shortcuts-stats-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  min-width: 120px;
}

.shortcuts-stats-label {
  font-size: 0.75rem;
  color: var(--neutral-500);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.shortcuts-stats-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--neutral-800);
}

.shortcuts-stats-status {
  font-size: 0.875rem;
  font-weight: 600;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  text-align: center;
}

.shortcuts-stats-status.enabled {
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-600);
}

.shortcuts-stats-status.disabled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-600);
}

.shortcuts-stats-shortcut {
  margin-top: var(--space-1);
}

/* 🌙 أنماط الوضع المظلم */
[data-theme="dark"] .shortcuts-category-name,
.dark-mode .shortcuts-category-name {
  color: var(--neutral-200);
}

[data-theme="dark"] .shortcut-description,
.dark-mode .shortcut-description {
  color: var(--neutral-400);
}

[data-theme="dark"] .shortcut-key,
.dark-mode .shortcut-key {
  background: linear-gradient(145deg, var(--neutral-700) 0%, var(--neutral-800) 100%);
  border-color: var(--neutral-600);
  color: var(--neutral-200);
}

[data-theme="dark"] .shortcuts-stats-value,
.dark-mode .shortcuts-stats-value {
  color: var(--neutral-200);
}

[data-theme="dark"] .shortcuts-help-footer kbd,
.dark-mode .shortcuts-help-footer kbd {
  background: var(--neutral-700);
  border-color: var(--neutral-600);
  color: var(--neutral-200);
}

/* 📱 التصميم المتجاوب */
@media (max-width: 768px) {
  .shortcuts-help-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .shortcuts-help-content {
    padding: var(--space-4);
    gap: var(--space-4);
  }
  
  .shortcuts-category {
    padding: var(--space-4);
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .shortcuts-stats {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .shortcuts-stats-item {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .shortcuts-help-header {
    padding: var(--space-4);
  }
  
  .shortcuts-help-header h2 {
    font-size: 1.25rem;
  }
  
  .shortcut-keys {
    flex-wrap: wrap;
  }
  
  .shortcut-key {
    min-width: 24px;
    height: 24px;
    font-size: 0.625rem;
  }
}

/* 🎬 حركات خاصة */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🚫 تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  .shortcuts-help-overlay,
  .shortcuts-help-modal,
  .shortcuts-category,
  .shortcut-item,
  .shortcut-key {
    animation: none;
    transition: none;
  }
  
  .shortcuts-category:hover,
  .shortcut-item:hover,
  .shortcut-key:hover {
    transform: none;
  }
}

/* 🎯 تحسينات إمكانية الوصول */
.shortcuts-help-modal:focus-within {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.shortcut-key:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 🔤 تحسينات الخطوط */
.shortcut-key,
.shortcuts-help-footer kbd {
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
}
