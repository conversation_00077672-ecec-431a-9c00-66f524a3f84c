# 📚 دليل المطور - نظام إدارة السفر

## 🎯 نظرة عامة

نظام إدارة السفر هو تطبيق ويب شامل مبني بـ React لإدارة جميع جوانب وكالة السفر، من الحجوزات إلى إدارة العملاء والتقارير المالية.

## 🏗️ البنية التقنية

### التقنيات المستخدمة

- **Frontend**: React 18.2.0
- **Styling**: CSS3 مع متغيرات CSS مخصصة
- **State Management**: Context API + Custom Hooks
- **Routing**: React Router v6
- **Testing**: Jest + React Testing Library
- **Build Tool**: Create React App
- **Performance**: Custom optimization utilities

### هيكل المشروع

```
frontend/
├── public/                 # الملفات العامة
├── src/
│   ├── components/        # المكونات القابلة لإعادة الاستخدام
│   │   ├── UI/           # مكونات واجهة المستخدم
│   │   ├── Bookings/     # مكونات الحجوزات
│   │   ├── Analytics/    # مكونات التحليلات
│   │   └── Dashboard/    # مكونات لوحة التحكم
│   ├── pages/            # صفحات التطبيق
│   ├── utils/            # الأدوات المساعدة
│   ├── store/            # إدارة الحالة
│   ├── styles/           # الأنماط العامة
│   └── tests/            # الاختبارات
├── docs/                 # الوثائق
└── scripts/              # سكريبتات مساعدة
```

## 🚀 البدء السريع

### المتطلبات

- Node.js 16+ 
- npm 8+ أو yarn 1.22+
- Git

### التثبيت

```bash
# استنساخ المشروع
git clone [repository-url]
cd frontend

# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm start

# فتح المتصفح على http://localhost:3000
```

### الأوامر المتاحة

```bash
# تطوير
npm start              # تشغيل خادم التطوير
npm run build          # بناء للإنتاج
npm run build:analyze  # تحليل حجم الحزمة

# اختبارات
npm test               # تشغيل الاختبارات
npm run test:coverage  # تشغيل مع تقرير التغطية
npm run test:watch     # مراقبة التغييرات

# جودة الكود
npm run lint           # فحص الكود
npm run lint:fix       # إصلاح مشاكل الكود
npm run format         # تنسيق الكود

# أدوات مساعدة
npm run analyze        # تحليل الأداء
npm run docs:serve     # تشغيل خادم الوثائق
```

## 🎨 نظام التصميم

### متغيرات CSS

```css
:root {
  /* الألوان الأساسية */
  --primary-500: #3b82f6;
  --secondary-500: #10b981;
  --warning-500: #f59e0b;
  --danger-500: #ef4444;
  
  /* الألوان المحايدة */
  --neutral-50: #f9fafb;
  --neutral-900: #111827;
  
  /* التدرجات */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-aurora: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  
  /* الزجاج المصقول */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px);
  
  /* المسافات */
  --space-1: 0.25rem;
  --space-12: 3rem;
  
  /* الانتقالات */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
}
```

### مكونات واجهة المستخدم

#### الأزرار

```jsx
import { Button } from '../components/UI/Button';

// استخدام أساسي
<Button variant="primary" size="md">
  حفظ
</Button>

// مع أيقونة
<Button variant="secondary" icon="📁">
  فتح ملف
</Button>

// حالة التحميل
<Button loading={true}>
  جاري الحفظ...
</Button>
```

#### الإشعارات

```jsx
import { useNotifications } from '../components/UI/EnhancedNotifications';

function MyComponent() {
  const { success, error, warning, info } = useNotifications();
  
  const handleSave = () => {
    success('تم الحفظ بنجاح!', {
      duration: 3000,
      actions: [
        {
          label: 'عرض',
          onClick: () => console.log('عرض')
        }
      ]
    });
  };
}
```

#### اختصارات لوحة المفاتيح

```jsx
import { useShortcuts } from '../components/UI/KeyboardShortcuts';

function MyComponent() {
  const { registerShortcut, unregisterShortcut } = useShortcuts();
  
  useEffect(() => {
    registerShortcut('ctrl+s', 'save', 'حفظ الملف', 'general');
    
    return () => {
      unregisterShortcut('ctrl+s');
    };
  }, []);
}
```

## 📊 إدارة الحالة

### استخدام المتجر المحسن

```jsx
import { useOptimizedStore, useBookings } from '../store/optimizedStore';

function BookingsComponent() {
  const { bookings, loading, actions } = useBookings();
  
  const handleAddBooking = (booking) => {
    actions.addBooking(booking);
  };
  
  return (
    <div>
      {loading ? (
        <div>جاري التحميل...</div>
      ) : (
        bookings.map(booking => (
          <div key={booking.id}>{booking.customerName}</div>
        ))
      )}
    </div>
  );
}
```

### إنشاء هوك مخصص

```jsx
import { useState, useEffect } from 'react';
import { useOptimizedStore } from '../store/optimizedStore';

export const useBookingForm = (initialData = {}) => {
  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState({});
  const { actions } = useOptimizedStore();
  
  const validate = () => {
    const newErrors = {};
    
    if (!formData.customerName) {
      newErrors.customerName = 'اسم العميل مطلوب';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async () => {
    if (validate()) {
      await actions.addBooking(formData);
      setFormData(initialData);
    }
  };
  
  return {
    formData,
    setFormData,
    errors,
    handleSubmit,
    isValid: Object.keys(errors).length === 0
  };
};
```

## ⚡ تحسين الأداء

### استخدام نظام التحسين

```jsx
import { useMemoryOptimization, useOptimizedMemo } from '../utils/memoryOptimizer';
import { OptimizedImage } from '../utils/memoryOptimizer';

function MyComponent({ data }) {
  const { addCleanup } = useMemoryOptimization();
  
  // تحسين العمليات الحسابية المكلفة
  const processedData = useOptimizedMemo(() => {
    return data.map(item => ({
      ...item,
      processed: heavyCalculation(item)
    }));
  }, [data], { maxAge: 60000 }); // تخزين لمدة دقيقة
  
  // استخدام الصور المحسنة
  return (
    <div>
      <OptimizedImage 
        src="/path/to/image.jpg"
        alt="وصف الصورة"
        placeholder={<div>جاري التحميل...</div>}
      />
    </div>
  );
}
```

### مراقبة الأداء

```jsx
import { performanceManager } from '../utils/performanceManager';

// تهيئة مراقب الأداء
performanceManager.init();

// الحصول على مقاييس الأداء
const metrics = performanceManager.getMetrics();
console.log('مقاييس الأداء:', metrics);

// تشغيل تشخيص شامل
performanceManager.runDiagnostics().then(report => {
  console.log('تقرير التشخيص:', report);
});
```

## 🧪 الاختبارات

### كتابة اختبار مكون

```jsx
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import MyComponent from '../MyComponent';

describe('MyComponent', () => {
  test('يجب عرض النص الصحيح', () => {
    render(<MyComponent text="مرحبا" />);
    expect(screen.getByText('مرحبا')).toBeInTheDocument();
  });
  
  test('يجب استدعاء الدالة عند النقر', () => {
    const mockClick = jest.fn();
    render(<MyComponent onClick={mockClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockClick).toHaveBeenCalledTimes(1);
  });
});
```

### اختبار هوك مخصص

```jsx
import { renderHook, act } from '@testing-library/react';
import { useBookingForm } from '../hooks/useBookingForm';

describe('useBookingForm', () => {
  test('يجب تحديث البيانات بشكل صحيح', () => {
    const { result } = renderHook(() => useBookingForm());
    
    act(() => {
      result.current.setFormData({ customerName: 'أحمد' });
    });
    
    expect(result.current.formData.customerName).toBe('أحمد');
  });
});
```

### تشغيل الاختبارات

```bash
# جميع الاختبارات
npm test

# اختبارات محددة
npm test -- --testNamePattern="MyComponent"

# مع تقرير التغطية
npm run test:coverage

# مراقبة التغييرات
npm run test:watch
```

## 🔧 أدوات التطوير

### إعدادات VS Code الموصى بها

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "files.associations": {
    "*.css": "css"
  }
}
```

### ملحقات VS Code المفيدة

- ES7+ React/Redux/React-Native snippets
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens
- Prettier - Code formatter
- ESLint

## 🐛 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### خطأ في تحميل المكونات

```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

#### مشاكل في الأنماط

```bash
# التأكد من تحميل ملفات CSS
import './Component.css';

# استخدام CSS Modules إذا لزم الأمر
import styles from './Component.module.css';
```

#### مشاكل في الاختبارات

```bash
# تنظيف ذاكرة التخزين المؤقت
npm test -- --clearCache

# تحديث اللقطات
npm test -- --updateSnapshot
```

## 📈 مراقبة الأداء

### مقاييس مهمة

- **First Contentful Paint (FCP)**: < 1.5s
- **Largest Contentful Paint (LCP)**: < 2.5s
- **Cumulative Layout Shift (CLS)**: < 0.1
- **First Input Delay (FID)**: < 100ms

### أدوات المراقبة

```jsx
// استخدام Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

## 🚀 النشر

### بناء للإنتاج

```bash
# بناء التطبيق
npm run build

# تحليل حجم الحزمة
npm run build:analyze

# اختبار البناء محلياً
npx serve -s build
```

### متغيرات البيئة

```bash
# .env.production
REACT_APP_API_URL=https://api.example.com
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=production
```

## 📚 موارد إضافية

- [React Documentation](https://reactjs.org/docs)
- [Testing Library](https://testing-library.com/docs/react-testing-library/intro)
- [Web Vitals](https://web.dev/vitals/)
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/)

## 🤝 المساهمة

### إرشادات المساهمة

1. إنشاء فرع جديد للميزة
2. كتابة اختبارات للكود الجديد
3. التأكد من اجتياز جميع الاختبارات
4. إنشاء Pull Request مع وصف واضح

### معايير الكود

- استخدام أسماء متغيرات وصفية
- كتابة تعليقات باللغة العربية
- اتباع نمط الكود الموحد
- كتابة اختبارات شاملة

---

📝 **ملاحظة**: هذا الدليل يتم تحديثه باستمرار. للحصول على أحدث المعلومات، راجع الوثائق في مجلد `/docs`.
