import React, { useState } from 'react';
import './VisaSelector.css';

// 🔍 مكون اختيار التأشيرة من المخزون
const VisaSelector = ({ 
  availableVisas = [], 
  selectedVisa, 
  onVisaSelect, 
  searchTerm = '', 
  onSearchChange 
}) => {
  const [showVisaList, setShowVisaList] = useState(false);
  const [hoveredVisa, setHoveredVisa] = useState(null);

  // 🔍 تصفية التأشيرات المتاحة
  const filteredVisas = availableVisas.filter(visa => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      visa.issueNumber.toLowerCase().includes(searchLower) ||
      visa.registryNumber.toLowerCase().includes(searchLower) ||
      visa.nationality.toLowerCase().includes(searchLower) ||
      visa.sponsorName.toLowerCase().includes(searchLower) ||
      visa.profession.toLowerCase().includes(searchLower)
    );
  });

  // 🎯 اختيار التأشيرة
  const handleVisaSelect = (visa) => {
    onVisaSelect(visa);
    setShowVisaList(false);
    onSearchChange('');
  };

  // 🎨 الحصول على أيقونة نوع التأشيرة
  const getVisaTypeIcon = (type) => {
    const icons = {
      work: '💼',
      visit: '👥',
      business: '🏢',
      family: '👨‍👩‍👧‍👦'
    };
    return icons[type] || '📋';
  };

  // 🎨 الحصول على نص نوع التأشيرة
  const getVisaTypeText = (type) => {
    const types = {
      work: 'عمل',
      visit: 'زيارة',
      business: 'تجارية',
      family: 'عائلية'
    };
    return types[type] || type;
  };

  // 📅 التحقق من انتهاء صلاحية التأشيرة
  const isVisaExpired = (expiryDate) => {
    return new Date(expiryDate) < new Date();
  };

  // 📅 حساب الأيام المتبقية
  const getDaysRemaining = (expiryDate) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <div className="visa-selector">
      <div className="visa-selector-header">
        <label>🔍 البحث عن تأشيرة متاحة:</label>
        <div className="visa-search-container">
          <input
            type="text"
            className="visa-search-input"
            placeholder="البحث برقم الصادر، رقم السجل، الجنسية، أو اسم الكفيل..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={() => setShowVisaList(true)}
          />
          <button
            type="button"
            className="visa-search-toggle"
            onClick={() => setShowVisaList(!showVisaList)}
          >
            {showVisaList ? '🔼' : '🔽'}
          </button>
        </div>
      </div>

      {/* 📋 قائمة التأشيرات المتاحة */}
      {showVisaList && (
        <div className="visa-list-container">
          <div className="visa-list-header">
            <h4>📋 التأشيرات المتاحة ({filteredVisas.length})</h4>
            {filteredVisas.length === 0 && searchTerm && (
              <p className="no-results">لا توجد تأشيرات تطابق البحث</p>
            )}
          </div>

          <div className="visa-list">
            {filteredVisas.map(visa => {
              const daysRemaining = getDaysRemaining(visa.expiryDate);
              const isExpired = isVisaExpired(visa.expiryDate);
              const isExpiringSoon = daysRemaining <= 30 && daysRemaining > 0;

              return (
                <div
                  key={visa.id}
                  className={`visa-item ${selectedVisa?.id === visa.id ? 'selected' : ''} ${isExpired ? 'expired' : ''}`}
                  onClick={() => !isExpired && handleVisaSelect(visa)}
                  onMouseEnter={() => setHoveredVisa(visa)}
                  onMouseLeave={() => setHoveredVisa(null)}
                >
                  <div className="visa-item-header">
                    <div className="visa-numbers">
                      <span className="visa-issue-number">
                        📋 {visa.issueNumber}
                      </span>
                      <span className="visa-registry-number">
                        📝 {visa.registryNumber}
                      </span>
                    </div>
                    <div className="visa-type-badge">
                      <span className="visa-type-icon">
                        {getVisaTypeIcon(visa.visaType)}
                      </span>
                      <span className="visa-type-text">
                        {getVisaTypeText(visa.visaType)}
                      </span>
                    </div>
                  </div>

                  <div className="visa-item-details">
                    <div className="visa-detail-row">
                      <span className="detail-label">الجنسية:</span>
                      <span className="detail-value">{visa.nationality}</span>
                    </div>
                    <div className="visa-detail-row">
                      <span className="detail-label">المهنة:</span>
                      <span className="detail-value">{visa.profession}</span>
                    </div>
                    <div className="visa-detail-row">
                      <span className="detail-label">الكفيل:</span>
                      <span className="detail-value">{visa.sponsorName}</span>
                    </div>
                  </div>

                  <div className="visa-item-footer">
                    <div className="visa-expiry">
                      {isExpired ? (
                        <span className="expiry-status expired">
                          ❌ منتهية الصلاحية
                        </span>
                      ) : isExpiringSoon ? (
                        <span className="expiry-status expiring">
                          ⚠️ تنتهي خلال {daysRemaining} يوم
                        </span>
                      ) : (
                        <span className="expiry-status valid">
                          ✅ صالحة لـ {daysRemaining} يوم
                        </span>
                      )}
                    </div>
                    <div className="visa-price">
                      <span className="price-label">السعر:</span>
                      <span className="price-value">{visa.sellingPrice} ر.س</span>
                    </div>
                  </div>

                  {/* 🎯 مؤشر الاختيار */}
                  {selectedVisa?.id === visa.id && (
                    <div className="selection-indicator">
                      ✅ مختارة
                    </div>
                  )}

                  {/* 🔍 تفاصيل إضافية عند التمرير */}
                  {hoveredVisa?.id === visa.id && (
                    <div className="visa-hover-details">
                      <div className="hover-detail">
                        <strong>تاريخ الإصدار:</strong> {new Date(visa.issueDate).toLocaleDateString('ar-SA')}
                      </div>
                      <div className="hover-detail">
                        <strong>تاريخ الانتهاء:</strong> {new Date(visa.expiryDate).toLocaleDateString('ar-SA')}
                      </div>
                      <div className="hover-detail">
                        <strong>مدة الإقامة:</strong> {visa.duration} يوم
                      </div>
                      <div className="hover-detail">
                        <strong>عدد الدخول:</strong> {visa.entries === 'single' ? 'دخول واحد' : 'متعدد'}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* 📊 إحصائيات سريعة */}
          <div className="visa-list-stats">
            <div className="stat-item">
              <span className="stat-icon">📋</span>
              <span className="stat-text">
                {availableVisas.length} تأشيرة متاحة
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-icon">💼</span>
              <span className="stat-text">
                {availableVisas.filter(v => v.visaType === 'work').length} عمل
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-icon">👥</span>
              <span className="stat-text">
                {availableVisas.filter(v => v.visaType === 'visit').length} زيارة
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-icon">⚠️</span>
              <span className="stat-text">
                {availableVisas.filter(v => getDaysRemaining(v.expiryDate) <= 30).length} تنتهي قريباً
              </span>
            </div>
          </div>

          <div className="visa-list-actions">
            <button
              type="button"
              className="btn btn-secondary btn-sm"
              onClick={() => setShowVisaList(false)}
            >
              إغلاق القائمة
            </button>
            {selectedVisa && (
              <button
                type="button"
                className="btn btn-outline btn-sm"
                onClick={() => handleVisaSelect(null)}
              >
                إلغاء الاختيار
              </button>
            )}
          </div>
        </div>
      )}

      {/* 📋 التأشيرة المختارة */}
      {selectedVisa && !showVisaList && (
        <div className="selected-visa-preview">
          <div className="preview-header">
            <span className="preview-title">✅ التأشيرة المختارة:</span>
            <button
              type="button"
              className="preview-edit-btn"
              onClick={() => setShowVisaList(true)}
              title="تغيير التأشيرة"
            >
              ✏️
            </button>
          </div>
          <div className="preview-content">
            <div className="preview-numbers">
              <span className="preview-issue">📋 {selectedVisa.issueNumber}</span>
              <span className="preview-registry">📝 {selectedVisa.registryNumber}</span>
            </div>
            <div className="preview-details">
              <span className="preview-type">
                {getVisaTypeIcon(selectedVisa.visaType)} {getVisaTypeText(selectedVisa.visaType)}
              </span>
              <span className="preview-nationality">{selectedVisa.nationality}</span>
              <span className="preview-price">{selectedVisa.sellingPrice} ر.س</span>
            </div>
          </div>
        </div>
      )}

      {/* 💡 نصائح مفيدة */}
      {!selectedVisa && availableVisas.length === 0 && (
        <div className="visa-selector-empty">
          <div className="empty-icon">📋</div>
          <h4>لا توجد تأشيرات متاحة</h4>
          <p>يمكنك إضافة تأشيرات جديدة من صفحة مخزون التأشيرات</p>
          <button
            type="button"
            className="btn btn-primary btn-sm"
            onClick={() => window.open('/inventory/visas', '_blank')}
          >
            🔗 فتح مخزون التأشيرات
          </button>
        </div>
      )}

      {/* 💡 نصائح للبحث */}
      {searchTerm && filteredVisas.length === 0 && availableVisas.length > 0 && (
        <div className="search-tips">
          <h5>💡 نصائح للبحث:</h5>
          <ul>
            <li>جرب البحث برقم الصادر (مثل: V2024001)</li>
            <li>جرب البحث برقم السجل (مثل: R2024001)</li>
            <li>جرب البحث بالجنسية (مثل: بنغلاديش)</li>
            <li>جرب البحث باسم الكفيل</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default VisaSelector;
