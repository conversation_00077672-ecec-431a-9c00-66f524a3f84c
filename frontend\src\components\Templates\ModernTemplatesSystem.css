/* Modern Templates System - نظام القوالب المتطور والمذهل */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

/* ===== MAIN CONTAINER ===== */
.modern-templates-system {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

.modern-templates-system::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* ===== LOADING STATE ===== */
.templates-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.templates-loading h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.templates-loading p {
  font-size: 16px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* ===== HEADER ===== */
.templates-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.title-section h1 {
  font-size: 36px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section p {
  font-size: 18px;
  color: #666;
  margin: 0;
  font-weight: 500;
}

.create-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 15px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.create-btn:hover::before {
  left: 100%;
}

.create-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
}

/* ===== STATISTICS ===== */
.templates-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

/* ===== CONTROLS ===== */
.templates-controls {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-box {
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e0e6ed;
  border-radius: 15px;
  font-size: 16px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  outline: none;
}

.search-box input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-section select {
  padding: 12px 15px;
  border: 2px solid #e0e6ed;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.filter-section select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.view-mode {
  display: flex;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 4px;
  gap: 2px;
}

.view-mode button {
  padding: 10px 15px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  color: #666;
}

.view-mode button.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* ===== CATEGORIES TABS ===== */
.categories-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  overflow-x: auto;
  padding: 10px 0;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 2px solid transparent;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-weight: 600;
  color: #666;
  position: relative;
  overflow: hidden;
}

.category-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--category-color, #667eea), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-tab:hover::before,
.category-tab.active::before {
  opacity: 0.1;
}

.category-tab.active {
  background: white;
  border-color: var(--category-color, #667eea);
  color: var(--category-color, #667eea);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.category-icon {
  font-size: 20px;
  position: relative;
  z-index: 1;
}

.category-name {
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.category-count {
  background: var(--category-color, #667eea);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
  position: relative;
  z-index: 1;
}

/* ===== TEMPLATES CONTAINER ===== */
.templates-container {
  margin-bottom: 30px;
}

.templates-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
}

.templates-container.list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* ===== TEMPLATE CARD ===== */
.template-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.template-card:hover::before {
  transform: scaleX(1);
}

.template-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.template-thumbnail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;
}

.thumbnail-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.template-type {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 6px 12px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.inactive-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 700;
  transform: rotate(15deg);
}

.template-header {
  margin-bottom: 15px;
}

.template-name {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.template-category {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.template-description {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.tag {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  padding: 4px 10px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #dee2e6;
}

.tag.more {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 12px;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  font-size: 14px;
}

.template-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.preview-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.use-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.download-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* ===== NO TEMPLATES STATE ===== */
.no-templates {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.no-templates-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-templates h3 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.no-templates p {
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
}

.create-first-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 15px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.create-first-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

/* ===== MODALS ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-content {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: modalSlideIn 0.4s ease-out;
  max-height: 90vh;
  overflow-y: auto;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.create-modal {
  width: 90vw;
  max-width: 800px;
}

.preview-modal {
  width: 95vw;
  max-width: 1200px;
}

.modal-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.modal-body {
  padding: 30px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #e0e6ed;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  outline: none;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-footer {
  background: #f8f9fa;
  padding: 20px 30px;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn,
.use-template-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.cancel-btn:hover,
.save-btn:hover,
.use-template-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* ===== TEMPLATE PREVIEW ===== */
.preview-content {
  max-height: 70vh;
  overflow-y: auto;
  border: 2px solid #e0e6ed;
  border-radius: 15px;
  padding: 20px;
  background: #f8f9fa;
}

.template-preview {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-family: 'Cairo', Arial, sans-serif;
  direction: rtl;
  text-align: right;
}

/* ===== VOUCHER TEMPLATE STYLES ===== */
.voucher-template {
  max-width: 210mm;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border: 3px solid #2563eb;
  border-radius: 15px;
  overflow: hidden;
  font-family: 'Cairo', Arial, sans-serif;
  direction: rtl;
}

.voucher-template.payment-voucher {
  border-color: #dc2626;
}

.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30px;
  border-bottom: 3px solid #2563eb;
}

.payment-voucher .voucher-header {
  border-bottom-color: #dc2626;
}

.company-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  font-size: 48px;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.company-info h1 {
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
  margin: 0 0 5px 0;
}

.company-details {
  color: #475569;
  font-size: 14px;
  margin: 2px 0;
}

.voucher-type-section {
  text-align: center;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.voucher-type-section.payment {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
}

.voucher-type {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 10px;
}

.voucher-number {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 15px;
  border-radius: 8px;
  display: inline-block;
}

.voucher-body {
  padding: 30px;
}

.voucher-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.info-section {
  background: #f8fafc;
  padding: 20px;
  border-radius: 10px;
  border-right: 4px solid #2563eb;
}

.payment-voucher .info-section {
  border-right-color: #dc2626;
}

.info-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 15px;
}

.payment-voucher .info-section h3 {
  color: #dc2626;
}

.info-section p {
  margin: 8px 0;
  font-size: 14px;
}

.amount-section {
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  margin: 25px 0;
  border: 2px solid #f59e0b;
}

.amount-section.payment {
  background: linear-gradient(135deg, #fee2e2, #f87171);
  border-color: #dc2626;
}

.amount-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 15px;
}

.amount-section.payment h3 {
  color: #7f1d1d;
}

.amount-value {
  font-size: 28px;
  font-weight: 700;
  color: #78350f;
  margin-bottom: 10px;
  font-family: 'Courier New', monospace;
}

.amount-section.payment .amount-value {
  color: #7f1d1d;
}

.amount-words {
  font-size: 14px;
  color: #a16207;
  font-weight: 500;
  font-style: italic;
  background: rgba(255, 255, 255, 0.5);
  padding: 8px 15px;
  border-radius: 8px;
  display: inline-block;
}

.amount-section.payment .amount-words {
  color: #991b1b;
}

.description-section {
  background: #f1f5f9;
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
  border-right: 4px solid #10b981;
}

.description-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
  margin-bottom: 10px;
}

.voucher-footer {
  padding: 20px 30px;
  border-top: 2px solid #e5e7eb;
}

.signatures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.signature-box {
  text-align: center;
  padding: 15px;
  border: 2px dashed #9ca3af;
  border-radius: 10px;
  min-height: 80px;
}

.signature-box p {
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.signature-line {
  border-bottom: 2px solid #9ca3af;
  margin: 15px 0 10px 0;
}

.signature-box small {
  font-size: 12px;
  color: #6b7280;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .templates-container.grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .modern-templates-system {
    padding: 15px;
  }
  
  .templates-header {
    padding: 20px;
  }
  
  .title-section h1 {
    font-size: 28px;
  }
  
  .templates-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .templates-controls {
    flex-direction: column;
    gap: 20px;
  }
  
  .categories-tabs {
    flex-wrap: wrap;
  }
  
  .templates-container.grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .voucher-info {
    grid-template-columns: 1fr;
  }
  
  .signatures {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .template-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95vw;
    margin: 10px;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-card {
  animation: fadeInUp 0.6s ease-out;
}

.template-card:nth-child(even) {
  animation-delay: 0.1s;
}

.template-card:nth-child(3n) {
  animation-delay: 0.2s;
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}