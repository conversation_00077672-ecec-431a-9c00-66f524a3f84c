import React from 'react';
import VisaInventory from '../../components/Inventory/VisaInventory';
import EnhancedCustomers from '../../components/Customers/EnhancedCustomers';
import { useNotifications, EnhancedNotificationsProvider } from '../../components/UI/EnhancedNotifications';

// 🧪 صفحة اختبار ربط التأشيرات مع العملاء
const TestVisaCustomerLinkContent = () => {
  const { success, info } = useNotifications();
  const [activeTab, setActiveTab] = React.useState('inventory');

  React.useEffect(() => {
    info('مرحباً بك في صفحة اختبار ربط التأشيرات مع العملاء! 🎉', {
      title: 'صفحة الاختبار',
      duration: 5000
    });
  }, [info]);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    success(`تم التبديل إلى ${tab === 'inventory' ? 'مخزون التأشيرات' : 'إدارة العملاء'}`, {
      duration: 2000
    });
  };

  return (
    <div style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* 🎯 رأس الصفحة */}
      <div style={{
        padding: '2rem',
        textAlign: 'center',
        color: 'white'
      }}>
        <h1 style={{ 
          fontSize: '2.5rem', 
          margin: '0 0 1rem 0',
          textShadow: '0 2px 4px rgba(0,0,0,0.3)'
        }}>
          🔗 اختبار ربط التأشيرات مع العملاء
        </h1>
        <p style={{ 
          fontSize: '1.25rem', 
          margin: 0,
          opacity: 0.9
        }}>
          اختبر إضافة التأشيرات للمخزون وربطها بالعملاء تلقائياً
        </p>
      </div>

      {/* 📑 تبويبات التنقل */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '1rem',
        padding: '0 2rem 2rem 2rem'
      }}>
        <button
          onClick={() => handleTabChange('inventory')}
          style={{
            padding: '1rem 2rem',
            fontSize: '1.125rem',
            border: 'none',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            backgroundColor: activeTab === 'inventory' ? 'white' : 'rgba(255,255,255,0.2)',
            color: activeTab === 'inventory' ? '#667eea' : 'white',
            fontWeight: activeTab === 'inventory' ? 'bold' : 'normal',
            boxShadow: activeTab === 'inventory' ? '0 4px 12px rgba(0,0,0,0.2)' : 'none',
            transform: activeTab === 'inventory' ? 'translateY(-2px)' : 'none'
          }}
        >
          📋 مخزون التأشيرات
        </button>
        
        <button
          onClick={() => handleTabChange('customers')}
          style={{
            padding: '1rem 2rem',
            fontSize: '1.125rem',
            border: 'none',
            borderRadius: '12px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            backgroundColor: activeTab === 'customers' ? 'white' : 'rgba(255,255,255,0.2)',
            color: activeTab === 'customers' ? '#667eea' : 'white',
            fontWeight: activeTab === 'customers' ? 'bold' : 'normal',
            boxShadow: activeTab === 'customers' ? '0 4px 12px rgba(0,0,0,0.2)' : 'none',
            transform: activeTab === 'customers' ? 'translateY(-2px)' : 'none'
          }}
        >
          👥 إدارة العملاء
        </button>
      </div>

      {/* 📋 محتوى التبويبات */}
      <div style={{
        backgroundColor: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        margin: '0 2rem 2rem 2rem',
        borderRadius: '20px',
        border: '1px solid rgba(255,255,255,0.2)',
        overflow: 'hidden',
        boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
      }}>
        {activeTab === 'inventory' && (
          <div>
            <div style={{
              padding: '2rem',
              borderBottom: '1px solid rgba(255,255,255,0.1)',
              backgroundColor: 'rgba(255,255,255,0.05)'
            }}>
              <h2 style={{ 
                margin: '0 0 1rem 0', 
                color: 'white',
                fontSize: '1.5rem'
              }}>
                📋 مخزون التأشيرات
              </h2>
              <p style={{ 
                margin: 0, 
                color: 'rgba(255,255,255,0.8)',
                fontSize: '1rem'
              }}>
                أضف تأشيرات جديدة للمخزون. ستظهر تلقائياً في نافذة العملاء عند إضافة عميل جديد.
              </p>
            </div>
            <VisaInventory />
          </div>
        )}

        {activeTab === 'customers' && (
          <div>
            <div style={{
              padding: '2rem',
              borderBottom: '1px solid rgba(255,255,255,0.1)',
              backgroundColor: 'rgba(255,255,255,0.05)'
            }}>
              <h2 style={{ 
                margin: '0 0 1rem 0', 
                color: 'white',
                fontSize: '1.5rem'
              }}>
                👥 إدارة العملاء
              </h2>
              <p style={{ 
                margin: 0, 
                color: 'rgba(255,255,255,0.8)',
                fontSize: '1rem'
              }}>
                أضف عملاء جدد واربطهم بالتأشيرات المتاحة. يمكنك البحث بأرقام التأشيرات أيضاً.
              </p>
            </div>
            <EnhancedCustomers />
          </div>
        )}
      </div>

      {/* 💡 نصائح الاستخدام */}
      <div style={{
        margin: '0 2rem 2rem 2rem',
        padding: '2rem',
        backgroundColor: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        borderRadius: '20px',
        border: '1px solid rgba(255,255,255,0.2)',
        color: 'white'
      }}>
        <h3 style={{ 
          margin: '0 0 1rem 0',
          fontSize: '1.25rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          💡 كيفية الاستخدام:
        </h3>
        <ol style={{ 
          margin: 0,
          paddingRight: '1.5rem',
          lineHeight: '1.8'
        }}>
          <li>
            <strong>إضافة تأشيرة:</strong> اذهب لتبويب "مخزون التأشيرات" وانقر "إضافة تأشيرة جديدة"
          </li>
          <li>
            <strong>ملء البيانات:</strong> أدخل رقم الصادر ورقم السجل وباقي التفاصيل
          </li>
          <li>
            <strong>إضافة عميل:</strong> اذهب لتبويب "إدارة العملاء" وانقر "إضافة عميل جديد"
          </li>
          <li>
            <strong>ربط التأشيرة:</strong> في قسم "ربط التأشيرة" ابحث عن التأشيرة المطلوبة واختارها
          </li>
          <li>
            <strong>البحث السريع:</strong> يمكنك البحث في العملاء برقم الصادر أو رقم السجل
          </li>
        </ol>
      </div>

      {/* ⌨️ اختصارات لوحة المفاتيح */}
      <div style={{
        margin: '0 2rem 2rem 2rem',
        padding: '2rem',
        backgroundColor: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        borderRadius: '20px',
        border: '1px solid rgba(255,255,255,0.2)',
        color: 'white'
      }}>
        <h3 style={{ 
          margin: '0 0 1rem 0',
          fontSize: '1.25rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          ⌨️ اختصارات لوحة المفاتيح:
        </h3>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          margin: 0
        }}>
          <div>
            <code style={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              fontWeight: 'bold'
            }}>
              Ctrl + Shift + V
            </code>
            <span style={{ marginRight: '0.5rem' }}>إضافة تأشيرة جديدة</span>
          </div>
          <div>
            <code style={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              fontWeight: 'bold'
            }}>
              Ctrl + Shift + C
            </code>
            <span style={{ marginRight: '0.5rem' }}>إضافة عميل جديد</span>
          </div>
          <div>
            <code style={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              fontWeight: 'bold'
            }}>
              Ctrl + Shift + F
            </code>
            <span style={{ marginRight: '0.5rem' }}>البحث السريع</span>
          </div>
          <div>
            <code style={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              padding: '0.25rem 0.5rem',
              borderRadius: '4px',
              fontWeight: 'bold'
            }}>
              Ctrl + /
            </code>
            <span style={{ marginRight: '0.5rem' }}>عرض جميع الاختصارات</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const TestVisaCustomerLink = () => {
  return (
    <EnhancedNotificationsProvider>
      <TestVisaCustomerLinkContent />
    </EnhancedNotificationsProvider>
  );
};

export default TestVisaCustomerLink;
