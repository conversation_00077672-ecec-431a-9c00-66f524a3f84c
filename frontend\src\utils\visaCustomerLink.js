// 🔗 نظام ربط التأشيرات مع العملاء

// 📋 فئة إدارة ربط التأشيرات
export class VisaCustomerLinkManager {
  constructor() {
    this.links = new Map(); // ربط التأشيرات بالعملاء
    this.visaInventory = new Map(); // مخزون التأشيرات
    this.customers = new Map(); // قاعدة بيانات العملاء
    this.observers = []; // مراقبي التغييرات
    
    this.init();
  }

  // 🚀 تهيئة النظام
  init() {
    // الاستماع لأحداث النظام
    this.setupEventListeners();
    
    // تحميل البيانات المحفوظة
    this.loadSavedData();
    
    console.log('🔗 تم تهيئة نظام ربط التأشيرات بالعملاء');
  }

  // 🎧 إعداد مستمعي الأحداث
  setupEventListeners() {
    // الاستماع لإضافة تأشيرة جديدة
    document.addEventListener('visaAdded', (event) => {
      this.handleVisaAdded(event.detail.visa);
    });

    // الاستماع لتحديث حالة التأشيرة
    document.addEventListener('visaStatusUpdate', (event) => {
      this.handleVisaStatusUpdate(event.detail);
    });

    // الاستماع لإضافة عميل جديد
    document.addEventListener('customerAdded', (event) => {
      this.handleCustomerAdded(event.detail.customer);
    });

    // الاستماع لحذف عميل
    document.addEventListener('customerDeleted', (event) => {
      this.handleCustomerDeleted(event.detail.customerId);
    });
  }

  // 📋 إضافة تأشيرة للمخزون
  handleVisaAdded(visa) {
    this.visaInventory.set(visa.id, {
      ...visa,
      status: 'available',
      linkedCustomerId: null,
      addedAt: new Date().toISOString()
    });

    this.notifyObservers('visaAdded', visa);
    this.saveData();
    
    console.log(`📋 تم إضافة التأشيرة ${visa.issueNumber} للمخزون`);
  }

  // 🔄 تحديث حالة التأشيرة
  handleVisaStatusUpdate({ visaId, newStatus, customerId, customerName }) {
    const visa = this.visaInventory.get(visaId);
    if (!visa) {
      console.warn(`⚠️ التأشيرة ${visaId} غير موجودة في المخزون`);
      return;
    }

    // تحديث حالة التأشيرة
    visa.status = newStatus;
    visa.linkedCustomerId = customerId;
    visa.updatedAt = new Date().toISOString();

    if (customerId && customerName) {
      // إنشاء رابط بين التأشيرة والعميل
      this.createLink(visaId, customerId);
      
      console.log(`🔗 تم ربط التأشيرة ${visa.issueNumber} بالعميل ${customerName}`);
    } else if (newStatus === 'available') {
      // إزالة الرابط إذا أصبحت التأشيرة متاحة
      this.removeLink(visaId);
      
      console.log(`🔓 تم إلغاء ربط التأشيرة ${visa.issueNumber}`);
    }

    this.notifyObservers('visaStatusUpdated', { visa, customerId, customerName });
    this.saveData();
  }

  // 👤 إضافة عميل جديد
  handleCustomerAdded(customer) {
    this.customers.set(customer.id, {
      ...customer,
      addedAt: new Date().toISOString()
    });

    // إذا كان العميل مرتبط بتأشيرة، تحديث حالة التأشيرة
    if (customer.assignedVisa) {
      this.handleVisaStatusUpdate({
        visaId: customer.assignedVisa.id,
        newStatus: 'reserved',
        customerId: customer.id,
        customerName: customer.name
      });
    }

    this.notifyObservers('customerAdded', customer);
    this.saveData();
    
    console.log(`👤 تم إضافة العميل ${customer.name}`);
  }

  // 🗑️ حذف عميل
  handleCustomerDeleted(customerId) {
    const customer = this.customers.get(customerId);
    if (!customer) return;

    // البحث عن التأشيرات المرتبطة بهذا العميل
    const linkedVisas = this.getVisasByCustomer(customerId);
    
    // إلغاء ربط جميع التأشيرات
    linkedVisas.forEach(visa => {
      this.handleVisaStatusUpdate({
        visaId: visa.id,
        newStatus: 'available',
        customerId: null,
        customerName: null
      });
    });

    this.customers.delete(customerId);
    this.notifyObservers('customerDeleted', { customerId, linkedVisas });
    this.saveData();
    
    console.log(`🗑️ تم حذف العميل ${customer.name} وإلغاء ربط ${linkedVisas.length} تأشيرة`);
  }

  // 🔗 إنشاء رابط بين تأشيرة وعميل
  createLink(visaId, customerId) {
    const linkId = `${visaId}-${customerId}`;
    
    this.links.set(linkId, {
      visaId,
      customerId,
      createdAt: new Date().toISOString(),
      status: 'active'
    });

    return linkId;
  }

  // 🔓 إزالة رابط
  removeLink(visaId) {
    // البحث عن الرابط المرتبط بهذه التأشيرة
    for (const [linkId, link] of this.links.entries()) {
      if (link.visaId === visaId) {
        this.links.delete(linkId);
        break;
      }
    }
  }

  // 🔍 البحث عن التأشيرات المتاحة
  getAvailableVisas() {
    return Array.from(this.visaInventory.values())
      .filter(visa => visa.status === 'available')
      .sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt));
  }

  // 🔍 البحث عن التأشيرات حسب العميل
  getVisasByCustomer(customerId) {
    return Array.from(this.visaInventory.values())
      .filter(visa => visa.linkedCustomerId === customerId);
  }

  // 🔍 البحث عن العميل حسب التأشيرة
  getCustomerByVisa(visaId) {
    const visa = this.visaInventory.get(visaId);
    if (!visa || !visa.linkedCustomerId) return null;
    
    return this.customers.get(visa.linkedCustomerId);
  }

  // 🔍 البحث في التأشيرات
  searchVisas(searchTerm) {
    const term = searchTerm.toLowerCase();
    
    return Array.from(this.visaInventory.values())
      .filter(visa => 
        visa.issueNumber.toLowerCase().includes(term) ||
        visa.registryNumber.toLowerCase().includes(term) ||
        visa.nationality.toLowerCase().includes(term) ||
        visa.sponsorName.toLowerCase().includes(term) ||
        visa.profession.toLowerCase().includes(term)
      );
  }

  // 🔍 البحث في العملاء
  searchCustomers(searchTerm) {
    const term = searchTerm.toLowerCase();
    
    return Array.from(this.customers.values())
      .filter(customer => 
        customer.name.toLowerCase().includes(term) ||
        customer.phone.includes(term) ||
        customer.email?.toLowerCase().includes(term) ||
        customer.idNumber?.includes(term) ||
        customer.passportNumber?.toLowerCase().includes(term) ||
        customer.visaIssueNumber?.toLowerCase().includes(term) ||
        customer.visaRegistryNumber?.toLowerCase().includes(term)
      );
  }

  // 📊 إحصائيات النظام
  getStatistics() {
    const totalVisas = this.visaInventory.size;
    const availableVisas = this.getAvailableVisas().length;
    const reservedVisas = Array.from(this.visaInventory.values())
      .filter(visa => visa.status === 'reserved').length;
    const usedVisas = Array.from(this.visaInventory.values())
      .filter(visa => visa.status === 'used').length;
    
    const totalCustomers = this.customers.size;
    const customersWithVisas = Array.from(this.customers.values())
      .filter(customer => customer.assignedVisa).length;
    
    const totalLinks = this.links.size;
    
    return {
      visas: {
        total: totalVisas,
        available: availableVisas,
        reserved: reservedVisas,
        used: usedVisas
      },
      customers: {
        total: totalCustomers,
        withVisas: customersWithVisas,
        withoutVisas: totalCustomers - customersWithVisas
      },
      links: {
        total: totalLinks,
        active: Array.from(this.links.values())
          .filter(link => link.status === 'active').length
      }
    };
  }

  // 🔍 التحقق من صحة الرابط
  validateLink(visaId, customerId) {
    const visa = this.visaInventory.get(visaId);
    const customer = this.customers.get(customerId);
    
    if (!visa) {
      return { valid: false, error: 'التأشيرة غير موجودة' };
    }
    
    if (!customer) {
      return { valid: false, error: 'العميل غير موجود' };
    }
    
    if (visa.status !== 'available' && visa.linkedCustomerId !== customerId) {
      return { valid: false, error: 'التأشيرة غير متاحة' };
    }
    
    // التحقق من انتهاء صلاحية التأشيرة
    if (new Date(visa.expiryDate) < new Date()) {
      return { valid: false, error: 'التأشيرة منتهية الصلاحية' };
    }
    
    return { valid: true };
  }

  // 🔄 مزامنة البيانات
  syncData() {
    // إزالة الروابط المعطلة
    for (const [linkId, link] of this.links.entries()) {
      const visa = this.visaInventory.get(link.visaId);
      const customer = this.customers.get(link.customerId);
      
      if (!visa || !customer || visa.linkedCustomerId !== link.customerId) {
        this.links.delete(linkId);
      }
    }
    
    // تحديث حالة التأشيرات المنتهية الصلاحية
    for (const visa of this.visaInventory.values()) {
      if (new Date(visa.expiryDate) < new Date() && visa.status !== 'expired') {
        visa.status = 'expired';
        if (visa.linkedCustomerId) {
          this.removeLink(visa.id);
          visa.linkedCustomerId = null;
        }
      }
    }
    
    this.saveData();
    this.notifyObservers('dataSynced', this.getStatistics());
    
    console.log('🔄 تم مزامنة البيانات');
  }

  // 📊 تسجيل مراقب
  subscribe(callback) {
    this.observers.push(callback);
  }

  // 📊 إلغاء تسجيل مراقب
  unsubscribe(callback) {
    this.observers = this.observers.filter(obs => obs !== callback);
  }

  // 📊 إشعار المراقبين
  notifyObservers(event, data) {
    this.observers.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('خطأ في إشعار المراقب:', error);
      }
    });
  }

  // 💾 حفظ البيانات
  saveData() {
    try {
      const data = {
        links: Array.from(this.links.entries()),
        visaInventory: Array.from(this.visaInventory.entries()),
        customers: Array.from(this.customers.entries()),
        lastSaved: new Date().toISOString()
      };
      
      localStorage.setItem('visaCustomerLinks', JSON.stringify(data));
    } catch (error) {
      console.error('فشل في حفظ البيانات:', error);
    }
  }

  // 📖 تحميل البيانات المحفوظة
  loadSavedData() {
    try {
      const savedData = localStorage.getItem('visaCustomerLinks');
      if (!savedData) return;
      
      const data = JSON.parse(savedData);
      
      this.links = new Map(data.links || []);
      this.visaInventory = new Map(data.visaInventory || []);
      this.customers = new Map(data.customers || []);
      
      console.log('📖 تم تحميل البيانات المحفوظة');
      
      // مزامنة البيانات بعد التحميل
      setTimeout(() => this.syncData(), 1000);
      
    } catch (error) {
      console.error('فشل في تحميل البيانات المحفوظة:', error);
    }
  }

  // 🧹 تنظيف البيانات
  cleanup() {
    // إزالة التأشيرات المنتهية الصلاحية القديمة
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    for (const [visaId, visa] of this.visaInventory.entries()) {
      if (visa.status === 'expired' && new Date(visa.expiryDate) < oneMonthAgo) {
        this.visaInventory.delete(visaId);
        this.removeLink(visaId);
      }
    }
    
    this.saveData();
    console.log('🧹 تم تنظيف البيانات القديمة');
  }

  // 📤 تصدير البيانات
  exportData() {
    const data = {
      statistics: this.getStatistics(),
      visas: Array.from(this.visaInventory.values()),
      customers: Array.from(this.customers.values()),
      links: Array.from(this.links.values()),
      exportedAt: new Date().toISOString()
    };
    
    return data;
  }
}

// 🌍 إنشاء مثيل عام
export const visaCustomerLinkManager = new VisaCustomerLinkManager();

// 🎯 دوال مساعدة
export const linkVisa = (visaId, customerId) => {
  return visaCustomerLinkManager.createLink(visaId, customerId);
};

export const unlinkVisa = (visaId) => {
  return visaCustomerLinkManager.removeLink(visaId);
};

export const getAvailableVisas = () => {
  return visaCustomerLinkManager.getAvailableVisas();
};

export const searchVisas = (searchTerm) => {
  return visaCustomerLinkManager.searchVisas(searchTerm);
};

export const getVisaCustomerStats = () => {
  return visaCustomerLinkManager.getStatistics();
};

// 🔄 مزامنة دورية
setInterval(() => {
  visaCustomerLinkManager.syncData();
}, 300000); // كل 5 دقائق

// 🧹 تنظيف دوري
setInterval(() => {
  visaCustomerLinkManager.cleanup();
}, 86400000); // كل 24 ساعة

export default visaCustomerLinkManager;
