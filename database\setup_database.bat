@echo off
chcp 65001 > nul
echo =====================================================
echo       إعداد قاعدة بيانات نظام شراء السياحة
echo       Sharau Travel System Database Setup
echo =====================================================
echo.

echo جاري إعداد قاعدة البيانات...
echo Setting up database...
echo.

REM تحقق من وجود MySQL
mysql --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: MySQL غير مثبت أو غير موجود في PATH
    echo Error: MySQL is not installed or not in PATH
    pause
    exit /b 1
)

echo تم العثور على MySQL
echo MySQL found
echo.

REM طلب معلومات الاتصال
set /p MYSQL_HOST="أدخل عنوان الخادم (افتراضي: localhost): "
if "%MYSQL_HOST%"=="" set MYSQL_HOST=localhost

set /p MYSQL_PORT="أدخل رقم المنفذ (افتراضي: 3306): "
if "%MYSQL_PORT%"=="" set MYSQL_PORT=3306

set /p MYSQL_USER="أدخل اسم المستخدم (افتراضي: root): "
if "%MYSQL_USER%"=="" set MYSQL_USER=root

echo.
echo جاري الاتصال بقاعدة البيانات...
echo Connecting to database...

REM تنفيذ ملف SQL
mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u %MYSQL_USER% -p < complete_database.sql

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إنشاء قاعدة البيانات بنجاح!
    echo ✅ Database created successfully!
    echo.
    echo معلومات الاتصال:
    echo Connection details:
    echo - الخادم/Host: %MYSQL_HOST%
    echo - المنفذ/Port: %MYSQL_PORT%
    echo - قاعدة البيانات/Database: travel_accounting
    echo - المستخدم/User: travel_user
    echo - كلمة المرور/Password: travel_password_2024
    echo.
    echo المستخدم الافتراضي للنظام:
    echo Default system user:
    echo - اسم المستخدم/Username: admin
    echo - كلمة المرور/Password: admin123
    echo.
) else (
    echo.
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo ❌ Failed to create database
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul