/* منتقي نوع الفيزا */
.visa-type-selector {
  width: 100%;
  direction: rtl;
  font-family: 'Cairo', Arial, sans-serif;
}

.visa-select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: white;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.visa-select:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.visa-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.visa-select:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  border-color: #dee2e6;
}

.visa-select option {
  padding: 10px;
  font-size: 14px;
  color: #2c3e50;
}

.visa-select option:disabled {
  color: #95a5a6;
  font-style: italic;
}

/* معلومات الفيزا المختارة */
.visa-info {
  margin-top: 12px;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.visa-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.visa-details {
  flex: 1;
}

.visa-name {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.visa-description {
  font-size: 0.9rem;
  color: #7f8c8d;
  line-height: 1.4;
}

/* تحسينات للوضع المظلم */
.dark-mode .visa-select {
  background: #34495e;
  color: #ecf0f1;
  border-color: #4a5568;
}

.dark-mode .visa-select:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.dark-mode .visa-select:focus {
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.dark-mode .visa-select option {
  background: #34495e;
  color: #ecf0f1;
}

.dark-mode .visa-info {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-color: #4a5568;
}

.dark-mode .visa-description {
  color: #bdc3c7;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .visa-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .visa-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .visa-name {
    font-size: 1rem;
  }
  
  .visa-description {
    font-size: 0.85rem;
  }
}

/* تأثيرات إضافية */
.visa-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.visa-icon:hover {
  transform: scale(1.05);
}

/* ألوان مخصصة لكل نوع فيزا */
.visa-info[data-visa-type="work"] {
  border-color: #27ae60;
  background: linear-gradient(135deg, rgba(39, 174, 96, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="work_temporary"] {
  border-color: #f39c12;
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="family_visit"] {
  border-color: #8e44ad;
  background: linear-gradient(135deg, rgba(142, 68, 173, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="tourist"] {
  border-color: #3498db;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="business"] {
  border-color: #e67e22;
  background: linear-gradient(135deg, rgba(230, 126, 34, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="study"] {
  border-color: #2980b9;
  background: linear-gradient(135deg, rgba(41, 128, 185, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="transit"] {
  border-color: #16a085;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="medical"] {
  border-color: #e74c3c;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="diplomatic"] {
  border-color: #9b59b6;
  background: linear-gradient(135deg, rgba(155, 89, 182, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="official"] {
  border-color: #34495e;
  background: linear-gradient(135deg, rgba(52, 73, 94, 0.05) 0%, #ffffff 100%);
}

.visa-info[data-visa-type="other"] {
  border-color: #7f8c8d;
  background: linear-gradient(135deg, rgba(127, 140, 141, 0.05) 0%, #ffffff 100%);
}
