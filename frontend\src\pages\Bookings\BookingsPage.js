import React, { useState, useEffect } from 'react';
import { EnhancedNotificationsProvider, useNotifications } from '../../components/UI/EnhancedNotifications';

// استيراد صفحات الحجوزات المختلفة
import FlightBookingPage from './FlightBookingPage';
import UmrahBookingPage from './UmrahBookingPage';
import HajjBookingPage from './HajjBookingPage';
import PassportBookingPage from './PassportBookingPage';
import BusBookingPage from './BusBookingPage';
import CarBookingPage from './CarBookingPage';
import DocumentAuthenticationPage from './DocumentAuthenticationPage';

const BookingsPageContent = () => {
  const [activeTab, setActiveTab] = useState('flights');
  const { clearAllNotifications } = useNotifications();
  // const [loading, setLoading] = useState(false); // غير مستخدم حالياً

  // مسح الإشعارات عند تغيير التبويب
  useEffect(() => {
    clearAllNotifications();
  }, [activeTab, clearAllNotifications]);

  // تعريف التبويبات
  const tabs = [
    { id: 'flights', name: 'حجز طيران', icon: '✈️', color: '#3498db' },
    { id: 'umrah', name: 'حجز عمرة', icon: '🕋', color: '#27ae60' },
    { id: 'hajj', name: 'حجز حج', icon: '🏛️', color: '#8e44ad' },
    { id: 'passports', name: 'حجز جوازات', icon: '📘', color: '#e74c3c' },
    { id: 'buses', name: 'حجز باصات', icon: '🚌', color: '#f39c12' },
    { id: 'cars', name: 'حجز سيارات', icon: '🚗', color: '#1abc9c' },
    { id: 'documents', name: 'تعميد وثائق', icon: '📋', color: '#9b59b6' }
  ];

  // دالة عرض المحتوى حسب التبويب النشط
  const renderTabContent = () => {
    switch (activeTab) {
      case 'flights':
        return <FlightBookingPage />;
      case 'umrah':
        return <UmrahBookingPage />;
      case 'hajj':
        return <HajjBookingPage />;
      case 'passports':
        return <PassportBookingPage />;
      case 'buses':
        return <BusBookingPage />;
      case 'cars':
        return <CarBookingPage />;
      case 'documents':
        return <DocumentAuthenticationPage />;
      default:
        return <FlightBookingPage />;
    }
  };

  return (
    <div style={{ fontFamily: 'Cairo, Arial, sans-serif' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '15px'
      }}>
        <div>
          <h1 style={{ margin: 0, color: '#2c3e50', fontSize: '28px' }}>🏢 إدارة الحجوزات</h1>
          <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>نظام شامل لإدارة جميع أنواع الحجوزات والخدمات</p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        padding: '20px',
        marginBottom: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '10px',
          justifyContent: 'center'
        }}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                background: activeTab === tab.id 
                  ? `linear-gradient(135deg, ${tab.color} 0%, ${tab.color}dd 100%)`
                  : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                color: activeTab === tab.id ? 'white' : '#495057',
                border: activeTab === tab.id ? `2px solid ${tab.color}` : '2px solid #dee2e6',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'all 0.3s ease',
                minWidth: '140px',
                justifyContent: 'center',
                boxShadow: activeTab === tab.id 
                  ? `0 4px 15px ${tab.color}40`
                  : '0 2px 5px rgba(0,0,0,0.1)'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== tab.id) {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab.id) {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                }
              }}
            >
              <span style={{ fontSize: '18px' }}>{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        padding: '25px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        minHeight: '600px'
      }}>
        {renderTabContent()}
      </div>
    </div>
  );
};

const BookingsPage = () => {
  return (
    <EnhancedNotificationsProvider>
      <BookingsPageContent />
    </EnhancedNotificationsProvider>
  );
};

export default BookingsPage;