/* منتقي الوكيل */
.agent-selector {
  width: 100%;
  direction: rtl;
  font-family: 'Cairo', Arial, sans-serif;
}

.agent-select-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-select {
  flex: 1;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background: white;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.agent-select:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.agent-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.agent-select:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  border-color: #dee2e6;
}

.agent-select option {
  padding: 10px;
  font-size: 14px;
  color: #2c3e50;
}

.agent-details-toggle {
  width: 40px;
  height: 40px;
  border: 2px solid #3498db;
  border-radius: 50%;
  background: white;
  color: #3498db;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  outline: none;
}

.agent-details-toggle:hover {
  background: #3498db;
  color: white;
  transform: scale(1.05);
}

/* تفاصيل الوكيل */
.agent-details {
  margin-top: 15px;
  padding: 20px;
  border: 2px solid #3498db;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ecf0f1;
}

.agent-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.agent-code {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-family: 'Courier New', monospace;
}

.agent-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  min-width: 80px;
}

/* شبكة التفاصيل */
.agent-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(52, 152, 219, 0.1);
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: rgba(52, 152, 219, 0.1);
  transform: translateY(-2px);
}

.detail-icon {
  font-size: 1.2rem;
  width: 30px;
  text-align: center;
}

.detail-content {
  flex: 1;
}

.detail-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-bottom: 2px;
  font-weight: 600;
}

.detail-value {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 500;
}

/* ملاحظات الوكيل */
.agent-notes {
  margin-top: 15px;
  padding: 15px;
  background: rgba(243, 156, 18, 0.05);
  border: 1px solid rgba(243, 156, 18, 0.2);
  border-radius: 8px;
}

.notes-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #f39c12;
  margin-bottom: 8px;
}

.notes-content {
  font-size: 0.9rem;
  color: #2c3e50;
  line-height: 1.5;
}

/* رسالة عدم وجود وكلاء */
.no-agents-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: rgba(231, 76, 60, 0.05);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: 8px;
  color: #e74c3c;
  font-weight: 600;
  margin-top: 10px;
}

.no-agents-icon {
  font-size: 1.2rem;
}

/* تحسينات للوضع المظلم */
.dark-mode .agent-select {
  background: #34495e;
  color: #ecf0f1;
  border-color: #4a5568;
}

.dark-mode .agent-select:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.dark-mode .agent-select option {
  background: #34495e;
  color: #ecf0f1;
}

.dark-mode .agent-details-toggle {
  background: #34495e;
  border-color: #3498db;
  color: #3498db;
}

.dark-mode .agent-details-toggle:hover {
  background: #3498db;
  color: white;
}

.dark-mode .agent-details {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-color: #3498db;
}

.dark-mode .agent-header {
  border-bottom-color: #4a5568;
}

.dark-mode .agent-name {
  color: #ecf0f1;
}

.dark-mode .agent-code {
  color: #bdc3c7;
}

.dark-mode .detail-item {
  background: rgba(52, 152, 219, 0.1);
  border-color: rgba(52, 152, 219, 0.2);
}

.dark-mode .detail-item:hover {
  background: rgba(52, 152, 219, 0.2);
}

.dark-mode .detail-label {
  color: #bdc3c7;
}

.dark-mode .detail-value {
  color: #ecf0f1;
}

.dark-mode .agent-notes {
  background: rgba(243, 156, 18, 0.1);
  border-color: rgba(243, 156, 18, 0.3);
}

.dark-mode .notes-content {
  color: #ecf0f1;
}

.dark-mode .no-agents-message {
  background: rgba(231, 76, 60, 0.1);
  border-color: rgba(231, 76, 60, 0.3);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .agent-header {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .agent-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .agent-details-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .detail-item {
    padding: 10px;
  }
  
  .agent-select-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .agent-details-toggle {
    align-self: center;
    margin-top: 10px;
  }
}
